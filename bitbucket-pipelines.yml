definitions:
  services:
    docker:
      memory: 15360
  caches:
    sbt: /opt/atlassian/pipelines/agent/build/.sbt
    ivy2: /opt/atlassian/pipelines/agent/build/.ivy2
  steps:
    - step: &publish-s3-deploy-jar-plugin
        name: publish s3-deploy-jar plugin
        caches:
          - sbt
          - ivy2
        script:
          - cd scala/sbt-s3-deploy-jar && sbt publish
    - step: &commons
        name: test commons
        caches:
          - sbt
          - ivy2
        script:
          - cd spark && sbt commonsEmr6_4_02_12/test
    - step: &comcast
        size: 2x
        max-time: 20            
        name: deploy comcast
        script:
          - export SBT_OPTS="-Xmx7000m"
          - cd spark && sbt comcast/test comcast/deployJar
        caches:
          - sbt
          - ivy2
    - step: &comcast-early-fiber
        name: deploy comcast-early-fiber
        script:
          - cd spark && sbt comcast-early-fiber/test comcast-early-fiber/deployJar
        caches:
          - sbt
          - ivy2
    - step: &high-confidence-svt
        name: deploy high-confidence-svt
        script:
          - cd spark && sbt high-confidence-svt/test high-confidence-svt/deployJar
        caches:
          - sbt
          - ivy2
    - step: &b2bbroadband
        size: 2x
        max-time: 20 
        name: deploy b2bbroadband
        script:
          - cd spark && sbt b2bbroadband/deployJar
        caches:
          - sbt
          - ivy2
    - step: &broadband
        name: deploy broadband
        script:
          - cd spark && sbt broadbandEmr5_17_02_11/test broadbandEmr5_17_02_11/deployJar
        caches:
          - sbt
          - ivy2
    - step: &broadband-customer-base
        name: deploy broadband-customer-base
        script:
          - cd spark && sbt broadband-customer-baseEmr5_17_02_11/test broadband-customer-baseEmr5_17_02_11/deployJar
        caches:
          - sbt
          - ivy2
    - step: &broadband-market-share
        size: 2x
        name: deploy broadband-market-share
        script:
          - cd spark && sbt broadband-market-share/test broadband-market-share/deployJar
        caches:
          - sbt
          - ivy2
    - step: &csp-forecasting
        name: deploy csp-forecasting
        script:
          - cd spark && sbt csp-forecasting/test csp-forecasting/deployJar
        caches:
          - sbt
          - ivy2
    - step: &csv-converter
        name: deploy csv-converter
        script:
          - cd spark && sbt csv-converter/test csv-converter/deployJar
        caches:
          - sbt
          - ivy2
    - step: &georesolver
        name: deploy georesolver
        script:
          - cd spark && sbt georesolver/test georesolver/deployJar
        caches:
          - sbt
          - ivy2
    - step: &device-master-table
        max-time: 20
        name: deploy device-master-table
        script:
          - cd spark && sbt device-master-table/test device-master-table/deployJar
        caches:
          - sbt
          - ivy2
    - step: &udp-device-switching
        size: 4x
        name: deploy udp-device-switching
        size: 4x
        script:
          - cd spark && sbt udp-device-switching/test udp-device-switching/deployJar
        caches:
          - sbt
          - ivy2
    - step: &jsonline-processor
        name: deploy jsonline-processor
        script:
          - cd spark && sbt jsonline-processor/test jsonline-processor/deployJar
        caches:
          - sbt
          - ivy2
    - step: &legacy-sharetracker-datasourcing
        name: deploy legacy-sharetracker-datasourcing
        script:
          - cd spark && sbt legacy-sharetracker-datasourcing/test legacy-sharetracker-datasourcing/deployJar
        caches:
          - sbt
          - ivy2
    - step: &liveramp
        name: deploy liveramp
        script:
          - cd spark && sbt liveramp/test liveramp/deployJar
        caches:
          - sbt
          - ivy2
    - step: &data-tests
        name: deploy data-tests
        script:
          - cd spark && sbt data-tests/test data-tests/deployJar
        caches:
          - sbt
          - ivy2
    - step: &mvno
        size: 4x
        name: deploy mvno
        size: 4x
        script:
          - cd spark && sbt mvno/test mvno/deployJar
        caches:
          - sbt
          - ivy2
    - step: &cellular-fixed-wireless
        name: deploy cellular-fixed-wireless
        script:
          - cd spark && sbt cellular-fixed-wireless/test cellular-fixed-wireless/deployJar
        caches:
          - sbt
          - ivy2
    - step: &custom-regions
        name: deploy custom-regions
        script:
          - cd spark && sbt custom-regions/test custom-regions/deployJar
        caches:
          - sbt
          - ivy2
    - step: &non-pii-lima
        name: deploy non-pii-lima
        script:
          - cd spark && sbt non-pii-lima/test non-pii-lima/deployJar
        caches:
          - sbt
          - ivy2
    - step: &oracle-zipcode-scoring
        size: 2x
        max-time: 20
        name: deploy oracle-zipcode-scoring
        script:
          - export SBT_OPTS="-Xmx7000m"
          - cd spark && sbt oracle-zip-scoring/test oracle-zip-scoring/deployJar
        caches:
          - sbt
          - ivy2
    - step: &sharetracker-b2b-bbca-udp
        name: deploy sharetracker-b2b-bbca-udp
        script:
          - cd spark && sbt sharetracker-b2b-bbca-udp/deployJar
        caches:
          - sbt
          - ivy2
    - step: &sharetracker-tablet
        name: deploy sharetracker-tablet
        script:
          - cd spark && sbt sharetracker-tablet/test sharetracker-tablet/deployJar
        caches:
          - sbt
          - ivy2
    - step: &tapad
        name: deploy tapad
        script:
          - cd spark && sbt tapad/test tapad/deployJar
        caches:
          - sbt
          - ivy2
    - step: &unified-data-pipeline
        size: 2x
        max-time: 20
        name: deploy unified-data-pipeline
        script:
          - export SBT_OPTS="-Xmx7000m"
          - cd spark && sbt unified-data-pipeline/test unified-data-pipeline/deployJar
        caches:
          - sbt
          - ivy2
    - step: &upload-bootstrap-actions-custom-steps-hive-pig
        name: Upload Bootstrap Action, Custom Step, Hive and Pig scripts to S3
        script:
          # Set the destination folder
          - export S3_DESTINATION_PREFIX=${S3_DESTINATION_PREFIX}/${BITBUCKET_BRANCH}
          # Copy only the changed files
          - aws s3 sync bootstrap_actions $S3_DESTINATION_PREFIX/bootstrap_actions
          - aws s3 sync custom_steps $S3_DESTINATION_PREFIX/custom_steps
          - aws s3 sync hive $S3_DESTINATION_PREFIX/hive
          - aws s3 sync pig $S3_DESTINATION_PREFIX/pig
    - step: &data-transfer
        name: deploy data-transfer
        script:
          - cd spark && sbt data-transfer/test data-transfer/deployJar
        caches:
          - sbt
          - ivy2
    - step: &wireless-market-share
        size: 2x
        max-time: 20
        name: deploy wireless-market-share
        script:
          - cd spark && sbt wireless-market-share/test wireless-market-share/deployJar
        caches:
          - sbt
          - ivy2
    - step: &first-party-ingestion
        name: deploy first-party-ingestion
        script:
          - cd spark && sbt first-party-ingestion/test first-party-ingestion/deployJar
        caches:
          - sbt
          - ivy2
    - step: &pyspark
        name: deploy pyspark
        script:
          - . /venv/bin/activate
          - cd pyspark
          - pip3 install -r requirements.txt
          - pip3 install -e .
          - ./build.py -t -l -s -v -b ${BITBUCKET_BRANCH} -3 ${S3_DESTINATION_PREFIX}
image:
  name: 282403293636.dkr.ecr.us-east-1.amazonaws.com/bitbucket-pipelines-emr-jobs:latest
  aws:
    access-key: $AWS_ACCESS_KEY_ID
    secret-key: $AWS_SECRET_ACCESS_KEY
pipelines:
  default:
    - parallel:
      - step: *publish-s3-deploy-jar-plugin
      - step: *cellular-fixed-wireless
      - step: *commons
      - step: *comcast
      - step: *comcast-early-fiber
      - step: *high-confidence-svt
      - step: *custom-regions
      - step: *b2bbroadband
      - step: *broadband
      - step: *broadband-customer-base
      - step: *broadband-market-share
      - step: *csp-forecasting
      - step: *csv-converter
      - step: *georesolver
      - step: *device-master-table
      - step: *udp-device-switching
      - step: *jsonline-processor
      - step: *legacy-sharetracker-datasourcing
      - step: *liveramp
      - step: *data-tests
      - step: *mvno
      - step: *non-pii-lima
      - step: *oracle-zipcode-scoring
      - step: *sharetracker-b2b-bbca-udp
      - step: *sharetracker-tablet
      - step: *tapad
      - step: *unified-data-pipeline
      - step: *upload-bootstrap-actions-custom-steps-hive-pig
      - step: *data-transfer
      - step: *first-party-ingestion
      - step: *wireless-market-share
      - step: *pyspark
  branches:
    master:
      - parallel:
        - step: *publish-s3-deploy-jar-plugin
        - step: *cellular-fixed-wireless
        - step: *commons
        - step: *comcast
        - step: *comcast-early-fiber
        - step: *high-confidence-svt
        - step: *custom-regions
        - step: *b2bbroadband
        - step: *broadband
        - step: *broadband-customer-base
        - step: *broadband-market-share
        - step: *csp-forecasting
        - step: *csv-converter
        - step: *georesolver
        - step: *device-master-table
        - step: *udp-device-switching
        - step: *jsonline-processor
        - step: *legacy-sharetracker-datasourcing
        - step: *liveramp
        - step: *data-tests
        - step: *mvno
        - step: *non-pii-lima
        - step: *oracle-zipcode-scoring
        - step: *sharetracker-b2b-bbca-udp
        - step: *sharetracker-tablet
        - step: *tapad
        - step: *unified-data-pipeline
        - step: *upload-bootstrap-actions-custom-steps-hive-pig
        - step: *data-transfer
        - step: *first-party-ingestion
        - step: *wireless-market-share
        - step: *pyspark
        - step:
            name: Deploy ScalaDocs
            script:
              - cd spark && sbt unidoc && sbt apiEmr5_17_02_11/unidoc && sbt deployUnidoc && sbt apiEmr5_17_02_11/deployUnidoc
            caches:
              - sbt
              - ivy2
