# EMR-Jobs Repo
With _relay-data-jobs_ being our _library code_ repo, this repo will be the short-term location for our job code,
which uses that library. Relay-data-jobs should contain wrapper/framework code, and in emr-jobs we store
pig, hive, and spark code

##### Scala Code
When this repo is pushed, scala code get's built and copied to S3. The S3 bucket can be found in the _BitBucket settings >  Pipelines > Environment Variables_

##### PySpark Code
When this repo is pushed, pyspark code get's tested, linted, zipped up and copied to S3. The S3 bucket can be found in the _BitBucket settings >  Pipelines > Environment Variables_

##### PySpark Repo setup
1) Setup a [virtualenv](https://docs.python.org/3/library/venv.html) or a [direnv](https://stackabuse.com/managing-python-environments-with-direnv-and-pyenv/) so you have a [good python enviroment](https://xkcd.com/1987/) only for this project.
2) `pip3 install -r requirements.txt`
3) `pip3 install -e .`
4) `./build.py -l -t`

##### BitBucket Pipelines Yaml
When editing the BitBucket Pipelines file it's easy to make mistakes since
 it requires strict tab alignment. [This](https://bitbucket-pipelines.atlassian.io/validator) link can help.

Dockerfile for pipeline builder is located [here](https://bitbucket.org/comniscient/bitbucket-pipelines-builder/src/master/bitbucket-pipelines-emr-jobs/).
