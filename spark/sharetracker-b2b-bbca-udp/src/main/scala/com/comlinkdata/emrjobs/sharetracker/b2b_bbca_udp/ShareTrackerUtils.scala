package com.comlinkdata.emrjobs.sharetracker.b2b_bbca_udp

import com.comlinkdata.largescale.commons.LocalDateRange

import java.time.LocalDate

object ShareTrackerUtils {
  val ONE_MONTH_LAG_QUARTER = "lag"
  val FULL_QUARTER = "quarter"
  val MOD_FULL_QUARTER = "modified_quarter"

  private def getFullQuarter(d: LocalDate): (LocalDateRange, String) = {
    val quarter = ((d.getMonthValue + 8) % 12) / 3 + 1
    val startDate = d.minusMonths(3).withMonth((quarter - 1) * 3 + 1).withDayOfMonth(1)
    val endDate = startDate.plusMonths(3).minusDays(1)
    (LocalDateRange.of(startDate, endDate), f"${endDate.getYear}%04dQ$quarter")
  }

  def getPeriod(d: LocalDate, runType: String): (LocalDateRange, String) = runType match {
    case ONE_MONTH_LAG_QUARTER =>
      val (range, quarter) = getFullQuarter(d.plusMonths(1))
      (LocalDateRange.of(range.startDate.minusMonths(1), range.endDate.plusDays(1).minusMonths(1).minusDays(1)), quarter)
    case FULL_QUARTER =>
      getFullQuarter(d)
    case MOD_FULL_QUARTER =>
      if (d.getDayOfMonth >= 15) {
        val dt = d.plusMonths(1).withDayOfMonth(1)
        getFullQuarter(dt)
      }
      else {
        val dt = d
        getFullQuarter(dt)
      }
  }
}
