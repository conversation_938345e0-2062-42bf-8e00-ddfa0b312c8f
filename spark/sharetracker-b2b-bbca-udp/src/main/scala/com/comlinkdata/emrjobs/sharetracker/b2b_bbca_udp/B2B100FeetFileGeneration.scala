package com.comlinkdata.emrjobs.sharetracker.b2b_bbca_udp

import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.udp._
import com.typesafe.scalalogging.LazyLogging
import java.net.URI
import java.time.LocalDate
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import scala.math.Pi

case class B2B100FeetFileGenerationConfig(
  datasources: Seq[ComlinkdataDatasource],
  b2bLatLongPath: URI,
  udpAggPath: URI,
  tempPath: String,
  resultPath: URI,
  date: Option[LocalDate],
  runType: String
)

object B2B100FeetFileGenerationJob extends SparkJob(B2B100FeetFileGenerationRunner)

object B2B100FeetFileGenerationRunner extends SparkJobRunner[B2B100FeetFileGenerationConfig] with LazyLogging with SparkConstants {

  import com.comlinkdata.emrjobs.sharetracker.b2b_bbca_udp.B2B100FeetFileGenerationSchema._

  private val PI_180 = Pi / 180

  private def truncate3(source: Column): Column = (source * 1000).cast(IntegerType) / 1000.0

  private def truncate6(source: Column): Column = (source * 1000000).cast(IntegerType) / 1000000.0

  private def aggregateUdp(udp: DataFrame)(implicit spark: SparkSession): Dataset[Udp3Digit] = {
    import spark.implicits._
    udp
      .groupBy(
        'isp,
        truncate6('latitude).as("udp_latitude"),
        truncate6('longitude).as("udp_longitude"),
        truncate3('latitude).as("lat_3"),
        truncate3('longitude).as("lng_3"))
      .agg(count('*).as("tot_device_cnt"))
      .as[Udp3Digit]
  }

  private def prepareB2B(b2b: DataFrame)(implicit spark: SparkSession): Dataset[B2B3Digit] = {
    import spark.implicits._
    b2b
      .select(
        'lat.as("b2b_lat"),
        'lng.as("b2b_lng"),
        truncate3('lat).as("lat_3"),
        truncate3('lng).as("lng_3"))
      .distinct
      .as[B2B3Digit]
  }

  private def matchUdpToB2B(b2b3digit: Dataset[B2B3Digit])(udp3digit: Dataset[Udp3Digit])(implicit spark: SparkSession): Dataset[B2BJoin] = {
    import spark.implicits._
    val calculateDistance = acos {
      sin('udp_latitude * PI_180) * sin('b2b_lat * PI_180) +
        cos('udp_latitude * PI_180) * cos('b2b_lat * PI_180) * cos(('udp_longitude - 'b2b_lng) * PI_180)
    } * (1 / PI_180) * 60 * 1.1515 * 1609.344
    udp3digit
      .join(b2b3digit, Seq("lat_3", "lng_3"))
      .filter(calculateDistance < 30.48)
      .as[B2BJoin]
  }

  private def aggregateB2B(b2b: Dataset[B2BJoin])(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    b2b
      .groupBy('isp, 'b2b_lat, 'b2b_lng)
      .agg(sum('tot_device_cnt).as("tot_device_cnt"))
      .select(
        'b2b_lat.as("LAT"),
        'b2b_lng.as("LONG"),
        'isp.as("ISP_NAME"),
        'tot_device_cnt.as("DEVICE_CNT"))
  }

  override def runJob(config: B2B100FeetFileGenerationConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    val (range, quarter) = ShareTrackerUtils.getPeriod(config.date.getOrElse(LocalDate.now), config.runType)
    val b2bSourceUri = Utils.joinPaths(config.b2bLatLongPath, s"yyyyqq=$quarter")
    logger.info(s"Reading B2B Lat/Long data from $b2bSourceUri")
    val b2b3digit = spark.read
      .schema(Utils.schema[B2BInput])
      .option("header", "true")
      .csv(b2bSourceUri.toString)
      .withColumnRenamed("long", "lng")
      .transform(prepareB2B)
    config.datasources.foreach { ds =>
      val tsl = TimeSeriesLocation.ofDatePartitions(config.udpAggPath).withPartition(ds).build
      range.foreach { date =>
        if (tsl.exists(date)) {
          logger.info(s"Processing UDP date $date for datasource $ds")
          spark.read.option(ReadOpts.basePath, tsl.source.toString).parquet(tsl.partition(date))
            .transform(aggregateUdp)
            .transform(matchUdpToB2B(b2b3digit))
            .write.mode(SaveMode.Overwrite).parquet(s"${config.tempPath}/ds=$ds/date=$date/")
        } else logger.info(s"Skipping non-existent UDP date $date for datasource $ds")
      }
    }
    logger.info(s"Aggregating daily output")
    spark.read.parquet(config.tempPath).as[B2BJoin]
      .transform(aggregateB2B)
      .coalesce(1)
      .write
      .option("header", "true")
      .option("delimiter", "|")
      .csv(s"${config.resultPath.toString}/runType=${config.runType}/q=$quarter/")
  }
}
