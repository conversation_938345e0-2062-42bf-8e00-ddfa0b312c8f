package com.comlinkdata.emrjobs.sharetracker.b2b_bbca_udp

import com.comlinkdata.commons.testing.CldBaseSpec
import com.comlinkdata.emrjobs.sharetracker.b2b_bbca_udp.ShareTrackerUtils.{FULL_QUARTER, ONE_MONTH_LAG_QUARTER, MOD_FULL_QUARTER, getPeriod}
import com.comlinkdata.largescale.commons.LocalDateRange

import java.sql.Date
import java.time.LocalDate
import scala.language.implicitConversions

class ShareTrackerUtilsSpec extends CldBaseSpec {
  implicit def stringToDate(string: String): Date = Date.valueOf(string)

  implicit def stringToLocalDate(string: String): LocalDate = stringToDate(string).toLocalDate

  describe("getQuarter") {
    it("for FULL_QUARTER, based on execution date, always gives previous full quarter. If execution date = any date in Q2 - will give startDate and endDate from Q1") {
      getPeriod("2020-01-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-01-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-01-31", FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-02-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-02-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-02-29", FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-03-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-03-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-03-31", FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")

      getPeriod("2020-04-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-04-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-04-30", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-05-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-05-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-05-30", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-06-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-06-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-06-30", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")

      getPeriod("2020-07-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-07-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-07-30", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-08-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-08-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-08-30", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-09-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-09-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-09-30", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")

      getPeriod("2020-10-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-10-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-10-30", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-11-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-11-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-11-30", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-12-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-12-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-12-30", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")

      getPeriod("2021-01-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-01-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-01-31", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-02-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-02-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-02-29", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-03-01", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-03-15", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-03-31", FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
    }

    it("for ONE_MONTH_LAG_QUARTER, based on execution date, always gives previous full quarter with one month lag (i.e Q1=Dec-Feb, Q2=Mar-May, Q3=Jun-Aug, Q4=Sep-Nov). " +
      "Execution date can be last month of quarter or month after that - need to return same startDate and endDate in both of these cases") {
      getPeriod("2020-12-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-09-01", "2020-11-30"), "2020Q4")
      getPeriod("2020-12-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-09-01", "2020-11-30"), "2020Q4")
      getPeriod("2020-12-31", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-09-01", "2020-11-30"), "2020Q4")
      getPeriod("2021-01-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-09-01", "2020-11-30"), "2020Q4")
      getPeriod("2021-01-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-09-01", "2020-11-30"), "2020Q4")
      getPeriod("2021-01-31", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-09-01", "2020-11-30"), "2020Q4")
      getPeriod("2021-02-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-09-01", "2020-11-30"), "2020Q4")
      getPeriod("2021-02-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-09-01", "2020-11-30"), "2020Q4")
      getPeriod("2021-02-28", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-09-01", "2020-11-30"), "2020Q4")

      getPeriod("2021-03-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-12-01", "2021-02-28"), "2021Q1")
      getPeriod("2021-03-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-12-01", "2021-02-28"), "2021Q1")
      getPeriod("2021-03-31", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-12-01", "2021-02-28"), "2021Q1")
      getPeriod("2021-04-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-12-01", "2021-02-28"), "2021Q1")
      getPeriod("2021-04-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-12-01", "2021-02-28"), "2021Q1")
      getPeriod("2021-04-30", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-12-01", "2021-02-28"), "2021Q1")
      getPeriod("2021-05-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-12-01", "2021-02-28"), "2021Q1")
      getPeriod("2021-05-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-12-01", "2021-02-28"), "2021Q1")
      getPeriod("2021-05-30", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2020-12-01", "2021-02-28"), "2021Q1")

      getPeriod("2021-06-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-03-01", "2021-05-31"), "2021Q2")
      getPeriod("2021-06-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-03-01", "2021-05-31"), "2021Q2")
      getPeriod("2021-06-20", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-03-01", "2021-05-31"), "2021Q2")
      getPeriod("2021-07-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-03-01", "2021-05-31"), "2021Q2")
      getPeriod("2021-07-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-03-01", "2021-05-31"), "2021Q2")
      getPeriod("2021-07-20", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-03-01", "2021-05-31"), "2021Q2")
      getPeriod("2021-08-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-03-01", "2021-05-31"), "2021Q2")
      getPeriod("2021-08-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-03-01", "2021-05-31"), "2021Q2")
      getPeriod("2021-08-20", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-03-01", "2021-05-31"), "2021Q2")

      getPeriod("2021-09-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-06-01", "2021-08-31"), "2021Q3")
      getPeriod("2021-09-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-06-01", "2021-08-31"), "2021Q3")
      getPeriod("2021-09-20", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-06-01", "2021-08-31"), "2021Q3")
      getPeriod("2021-10-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-06-01", "2021-08-31"), "2021Q3")
      getPeriod("2021-10-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-06-01", "2021-08-31"), "2021Q3")
      getPeriod("2021-10-20", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-06-01", "2021-08-31"), "2021Q3")
      getPeriod("2021-11-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-06-01", "2021-08-31"), "2021Q3")
      getPeriod("2021-11-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-06-01", "2021-08-31"), "2021Q3")
      getPeriod("2021-11-20", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-06-01", "2021-08-31"), "2021Q3")

      getPeriod("2021-12-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-09-01", "2021-11-30"), "2021Q4")
      getPeriod("2021-12-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-09-01", "2021-11-30"), "2021Q4")
      getPeriod("2021-12-31", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-09-01", "2021-11-30"), "2021Q4")
      getPeriod("2022-01-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-09-01", "2021-11-30"), "2021Q4")
      getPeriod("2022-01-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-09-01", "2021-11-30"), "2021Q4")
      getPeriod("2022-01-31", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-09-01", "2021-11-30"), "2021Q4")
      getPeriod("2022-02-01", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-09-01", "2021-11-30"), "2021Q4")
      getPeriod("2022-02-15", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-09-01", "2021-11-30"), "2021Q4")
      getPeriod("2022-02-28", ONE_MONTH_LAG_QUARTER) shouldBe(LocalDateRange.of("2021-09-01", "2021-11-30"), "2021Q4")
    }

    it("for MOD_FULL_QUARTER, based on execution date, always gives previous full quarter with no lag except for last 15 days of quarter. " +
      "If execution date = any date in Q2 (except for last 15 days of quarter) - will give startDate and endDate for Q1. " +
      "If execution date = any date in last 15 days of quarter, will give startDate and endDate for same current quarter. " +
      "Execution date can be last 15 days of quarter or month after that - need to return same startDate and endDate in both of these cases") {
      getPeriod("2019-12-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2019-12-25", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2019-12-31", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-01-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-01-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-01-31", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-02-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-02-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-02-29", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-03-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")
      getPeriod("2020-03-14", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2019-10-01", "2019-12-31"), "2019Q4")

      getPeriod("2020-03-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-03-31", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-04-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-04-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-04-30", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-05-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-05-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-05-30", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-06-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")
      getPeriod("2020-06-14", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-01-01", "2020-03-31"), "2020Q1")

      getPeriod("2020-06-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-06-30", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-07-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-07-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-07-30", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-08-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-08-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-08-30", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-09-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")
      getPeriod("2020-09-14", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-04-01", "2020-06-30"), "2020Q2")

      getPeriod("2020-09-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-09-30", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-10-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-10-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-10-30", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-11-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-11-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-11-30", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-12-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")
      getPeriod("2020-12-14", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-07-01", "2020-09-30"), "2020Q3")

      getPeriod("2020-12-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2020-12-30", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-01-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-01-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-01-31", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-02-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-02-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-02-29", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-03-01", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")
      getPeriod("2021-03-14", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2020-10-01", "2020-12-31"), "2020Q4")

      getPeriod("2021-03-15", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2021-01-01", "2021-03-31"), "2021Q1")
      getPeriod("2021-03-31", MOD_FULL_QUARTER) shouldBe(LocalDateRange.of("2021-01-01", "2021-03-31"), "2021Q1")

    }

  }
}

