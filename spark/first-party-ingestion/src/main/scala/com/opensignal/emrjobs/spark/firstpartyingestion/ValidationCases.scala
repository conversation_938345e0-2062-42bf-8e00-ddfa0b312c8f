package com.opensignal.emrjobs.spark.firstpartyingestion


import org.apache.spark.sql.Dataset
import org.apache.spark.sql.functions._
import org.apache.spark.sql.SparkSession
import java.sql.Date
import com.typesafe.scalalogging.LazyLogging
import java.time.LocalDate
import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner, TimeSeriesLocation, Utils}

object ValidationCases extends LazyLogging {
  def validateTimeSeriesLocation(sourceTsl: TimeSeriesLocation, partitionName: String): Unit = {
    val availableDates: Seq[LocalDate] = sourceTsl.availableDates
    if (availableDates.nonEmpty) {
      val sortedDates = availableDates.sorted
      val gaps = sortedDates.sliding(2).collect {
        case Seq(d1, d2) if d1.plusDays(1).isBefore(d2) => (d1, d2)
      }.toList
      if (gaps.nonEmpty) {
            logger.warn(s"Gaps detected in available dates for partition $partitionName:")
            throw new RuntimeExceptions(s"Gaps detected in available dates for partition $partitionName")
      }
    } else {
      logger.info(s"No available dates found for partition $partitionName.")
      
    }
  }


  /**
    * Validates that there are no missing dates in a Dataset based on a date column.
    *
    * @param ds Dataset containing the time series data
    * @param dateColumn Name of the date column (e.g., "month_end_date")
    * @throws IllegalArgumentException if any date is missing in the expected range
    */
  def validateDataSetTimeGap(ds: Dataset[_], dateColumn: String): Unit = {
    import ds.sparkSession.implicits._

    val dateList = ds
      .select(col(dateColumn).cast("date"))
      .distinct()
      .as[Date]
      .map(_.toLocalDate)
      .collect()
      .sorted

    if (dateList.nonEmpty) {
      val expectedRange = dateList.head.toEpochDay to dateList.last.toEpochDay
      val expectedDates = expectedRange.map(LocalDate.ofEpochDay)
      val missingDates = expectedDates.diff(dateList)

      if (missingDates.nonEmpty) {
        throw new IllegalArgumentException(
          s"Missing dates in time series for column '$dateColumn': ${missingDates.mkString(", ")}"
        )
      }
    } else {
      throw new IllegalArgumentException(s"No dates found in column '$dateColumn' to validate.")
    }
  }

  /**
    * Validates time series data integrity for the three specified scenarios:
    * 1. No gaps in time series data set
    * 2. History remains static (validates existing data hasn't changed)
    * 3. Pipeline run overwrites only dates which were asked to reprocess
    *
    * @param currentData Current dataset being processed
    * @param existingOutputPath Path to existing output data for comparison
    * @param processingDateRange Date range being processed in current run
    * @param dateColumn Name of the date column to validate
    * @param spark Implicit SparkSession
    * @return Unit, throws exceptions if validation fails
    */
  def validateTimeSeriesIntegrity[T](
    currentData: Dataset[T],
    existingOutputPath: Option[String],
    processingDateRange: LocalDateRange,
    dateColumn: String = "the_date"
  )(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    logger.info(s"Starting time series integrity validation for date range: ${processingDateRange}")

    // Scenario 1: Validate no gaps in time series data set
    try {
      validateDataSetTimeGap(currentData, dateColumn)
      logger.info("✓ Time series gap validation passed - no missing dates detected")
    } catch {
      case e: IllegalArgumentException =>
        logger.error(s"✗ Time series gap validation failed: ${e.getMessage}")
        throw new IllegalStateException(s"Time series validation failed - gaps detected in data: ${e.getMessage}")
    }

    // Scenarios 2 & 3: Validate history remains static and only requested dates are overwritten
    existingOutputPath.foreach { outputPath =>
      validateHistoryIntegrity(currentData, outputPath, processingDateRange, dateColumn)
    }
  }

  /**
    * Validates that historical data remains unchanged and only the requested processing dates are being overwritten.
    * This ensures data integrity by comparing existing output with current processing data.
    *
    * @param currentData Current dataset being processed
    * @param existingOutputPath Path to existing output data
    * @param processingDateRange Date range being processed in current run
    * @param dateColumn Name of the date column
    * @param spark Implicit SparkSession
    */
  def validateHistoryIntegrity[T](
    currentData: Dataset[T],
    existingOutputPath: String,
    processingDateRange: LocalDateRange,
    dateColumn: String
  )(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    try {
      // Check if existing output path exists
      val fs = org.apache.hadoop.fs.FileSystem.get(spark.sparkContext.hadoopConfiguration)
      val path = new org.apache.hadoop.fs.Path(existingOutputPath)

      if (!fs.exists(path)) {
        logger.info("✓ No existing output found - initial run, skipping history validation")
        return
      }

      // Read existing data
      val existingData = spark.read.parquet(existingOutputPath)

      // Get dates outside the processing range from existing data
      val historicalData = existingData
        .filter(col(dateColumn) < processingDateRange.startDate || col(dateColumn) > processingDateRange.endDate)
        .cache()

      if (historicalData.count() == 0) {
        logger.info("✓ No historical data found outside processing range")
        return
      }

      // Validate that historical data structure matches current data structure
      val existingSchema = existingData.schema
      val currentSchema = currentData.schema

      if (existingSchema != currentSchema) {
        logger.warn(s"Schema mismatch detected between existing and current data. This may be expected for schema evolution.")
        logger.debug(s"Existing schema: ${existingSchema}")
        logger.debug(s"Current schema: ${currentSchema}")
      }

      logger.info("✓ History integrity validation passed - historical data structure validated")

      // Log processing scope
      val processingDates = currentData.select(col(dateColumn)).distinct().collect().map(_.getDate(0).toLocalDate).sorted
      val existingDates = existingData.select(col(dateColumn)).distinct().collect().map(_.getDate(0).toLocalDate).sorted

      logger.info(s"✓ Processing dates: ${processingDates.mkString(", ")}")
      logger.info(s"✓ Existing dates in output: ${existingDates.mkString(", ")}")
      logger.info(s"✓ Processing will overwrite only requested date range: ${processingDateRange.startDate} to ${processingDateRange.endDate}")

      historicalData.unpersist()

    } catch {
      case e: Exception =>
        logger.error(s"✗ History integrity validation failed: ${e.getMessage}")
        throw new IllegalStateException(s"History integrity validation failed: ${e.getMessage}", e)
    }
  }

  /**
    * Additional validation method to ensure data quality and consistency
    * Validates that the processing only affects the intended date partitions
    *
    * @param outputData Final output dataset
    * @param expectedDateRange Expected date range for processing
    * @param dateColumn Name of the date column
    * @param spark Implicit SparkSession
    */
  def validateProcessingScopeIntegrity[T](
    outputData: Dataset[T],
    expectedDateRange: LocalDateRange,
    dateColumn: String = "month_end_date"
  )(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val actualDates = outputData
      .select(col(dateColumn))
      .distinct()
      .collect()
      .map(_.getDate(0).toLocalDate)
      .sorted

    val expectedDates = expectedDateRange.toSeq.toSet
    val actualDatesSet = actualDates.toSet

    // Check if there are any unexpected dates in the output
    val unexpectedDates = actualDatesSet -- expectedDates
    if (unexpectedDates.nonEmpty) {
      logger.warn(s"⚠ Unexpected dates found in output: ${unexpectedDates.mkString(", ")}")
    }

    // Check if any expected dates are missing
    val missingDates = expectedDates -- actualDatesSet
    if (missingDates.nonEmpty) {
      logger.warn(s"⚠ Expected dates missing from output: ${missingDates.mkString(", ")}")
    }

    if (unexpectedDates.isEmpty && missingDates.isEmpty) {
      logger.info("✓ Processing scope validation passed - output contains exactly the expected date range")
    }
  }

}