package com.opensignal.emrjobs.spark

import com.comlinkdata.largescale.commons.{HashingUtils, TimeSeriesLocation}
import com.opensignal.largescale.schema.first_party_ingestion.ingested_raw.{IngestedConnects, IngestedDisconnects}
import com.typesafe.scalalogging.LazyLogging
import java.sql.Date
import org.apache.spark.sql.expressions.{UserDefinedFunction, Window}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Column, Dataset, Row, SparkSession}


package object firstpartyingestion {

  trait Adapter[O] extends LazyLogging {
    def readPartitionName: String

    def writePartitionName: String

    def read(tsl: TimeSeriesLocation, uploadDate: Date, hashingSalt: String)(implicit spark: SparkSession): Dataset[O]
  }

  private val colMapUdf: UserDefinedFunction = udf { (r: Row, names: Seq[String]) =>
    r.schema.map(_.name)
      .flatMap(name => names.find(_.toUpperCase == name.toUpperCase).map(_ -> r.getAs[String](name))).toMap
  }

  private def parseDate(dateCol: Column): Column =
    coalesce(Seq("yyyy-MM-dd", "dd-MM-yyyy", "ddMMMyyyy", "M/d/yyyy", "M/d/yy").map(to_date(dateCol, _)): _*)

  object Comcast {
    object ConnectAdapter extends Adapter[IngestedConnects] {
      override def readPartitionName: String = "upload_date"

      override def writePartitionName: String = "month_end_date"

      override def read(tsl: TimeSeriesLocation, uploadDate: Date, hashingSalt: String)(implicit spark: SparkSession): Dataset[IngestedConnects] = {
        import spark.implicits._
        val mapCols = Seq("DIVISION", "REGION", "DMA_NAME", "ZIP", "DWELL_TYPE_GROUP", "PRODUCT_MIX",
          "DISCONNECT_TYPE", "HSD_TIER_NAME")
        val deduplicationWindow = Window
          .partitionBy('CUSTOMER_ACCOUNT_ID, parseDate('MONTH_END_DT))
          .orderBy(upper('MOVERS_FLAG)==="YES")
        spark
          .read.option("header", true).option("delimiter", "\t")
          .csv(tsl.partition(uploadDate.toLocalDate))
          .withColumn("rank", row_number.over(deduplicationWindow))
          .filter('rank === 1)
          .select(
            when('CENSUS_BLOCK === "" || 'CENSUS_BLOCK === "?", lit(null)).otherwise('CENSUS_BLOCK).as("census_block_id"),
            HashingUtils.sha2HashWithSalt('CUSTOMER_ACCOUNT_ID, hashingSalt).cast("String").as("customer_id"),
            parseDate('MONTH_END_DT).as("month_end_date"),
            lit(uploadDate).as("upload_date"),
            parseDate('HSD_CONNECT_DT).as("connect_date"),
            (upper('MOVERS_FLAG)==="YES").as("transfer_flag"),
            lit(1009).as("winning_sp_id"),
            colMapUdf(struct('*), array(mapCols.map(lit(_)): _*)).as("other"))
          .filter('customer_id.isNotNull
            && 'customer_id =!= ""
            && 'month_end_date < 'upload_date
            && dayofmonth('month_end_date) === 21)
          .as[IngestedConnects]
      }
    }

    object DisconnectAdapter extends Adapter[IngestedDisconnects] {
      override def readPartitionName: String = "upload_date"

      override def writePartitionName: String = "month_end_date"

      override def read(tsl: TimeSeriesLocation, uploadDate: Date, hashingSalt: String)(implicit spark: SparkSession): Dataset[IngestedDisconnects] = {
        import spark.implicits._
        val mapCols = Seq("DIVISION", "REGION", "DMA_NAME", "ZIP", "DWELL_TYPE_GROUP", "PRODUCT_MIX",
          "DISCONNECT_TYPE", "HSD_TIER_NAME")
        val deduplicationWindow = Window
          .partitionBy('CUSTOMER_ACCOUNT_ID, parseDate('MONTH_END_DT))
          .orderBy(upper('MOVERS_FLAG)==="YES")
        spark
          .read.option("header", true).option("delimiter", "\t")
          .csv(tsl.partition(uploadDate.toLocalDate))
          .withColumn("rank", row_number.over(deduplicationWindow))
          .filter('rank === 1)
          .select(
            when('CENSUS_BLOCK === "" || 'CENSUS_BLOCK === "?", lit(null)).otherwise('CENSUS_BLOCK).as("census_block_id"),
            HashingUtils.sha2HashWithSalt('CUSTOMER_ACCOUNT_ID, hashingSalt).cast("String").as("customer_id"),
            parseDate('MONTH_END_DT).as("month_end_date"),
            lit(uploadDate).as("upload_date"),
            parseDate('HSD_CONNECT_DT).as("connect_date"),
            parseDate('HSD_DISCONNECT_DT).as("disconnect_date"),
            (upper('MOVERS_FLAG)==="YES").as("transfer_flag"),
            lit(1009).as("losing_sp_id"),
            colMapUdf(struct('*), array(mapCols.map(lit(_)): _*)).as("other"))
          .filter('customer_id.isNotNull
            && 'customer_id =!= ""
            && 'month_end_date < 'upload_date
            && dayofmonth('month_end_date) === 21)
          .as[IngestedDisconnects]
      }
    }
  }

  object Charter {
    object ConnectAdapter extends Adapter[IngestedConnects] {
      override def readPartitionName: String = "upload_date"

      override def writePartitionName: String = "month_end_date"

      override def read(tsl: TimeSeriesLocation, uploadDate: Date, hashingSalt: String)(implicit spark: SparkSession): Dataset[IngestedConnects] = {
        import spark.implicits._
        val mapCols = Seq("REGION", "MA", "DMA")
        spark
          .read.option("header", true).option("delimiter", "|")
          .csv(tsl.partition(uploadDate.toLocalDate))
          .select(
            when('CENSUS_BLOCK_ID.isin("", "?"), lit(null)).otherwise('CENSUS_BLOCK_ID).as("census_block_id"),
            HashingUtils.sha2HashWithSalt('CUSTOMER_ID, hashingSalt).cast("String").as("customer_id"),
            parseDate('MONTH_END_DATE).as("month_end_date"),
            lit(uploadDate).as("upload_date"),
            ('TRANSFER_FLAG === "Y").as("transfer_flag"),
            parseDate('CONNECT_DATE).as("connect_date"),
            lit(6104).as("winning_sp_id"),
            colMapUdf(struct('*), array(mapCols.map(lit(_)): _*)).as("other"))
          .filter('customer_id.isNotNull
            && 'customer_id =!= ""
            && 'month_end_date <= 'upload_date
            && dayofmonth('month_end_date) === 28)
          .as[IngestedConnects]
      }
    }

    object DisconnectAdapter extends Adapter[IngestedDisconnects] {
      override def readPartitionName: String = "upload_date"

      override def writePartitionName: String = "month_end_date"

      override def read(tsl: TimeSeriesLocation, uploadDate: Date, hashingSalt: String)(implicit spark: SparkSession): Dataset[IngestedDisconnects] = {
        import spark.implicits._
        val mapCols = Seq("DMA_NAME")
        val raw = spark
          .read.option("header", true).option("delimiter", "|")
          .csv(tsl.partition(uploadDate.toLocalDate))
        val cols = raw.columns.map(_.toUpperCase)
        val result = if (cols.contains("DISCONNECT_DATE")) raw else raw.withColumn("DISCONNECT_DATE", lit(null: Date))
        result
          .select(
            when('CENSUS_BLOCK_ID.isin("", "?"), lit(null)).otherwise('CENSUS_BLOCK_ID).as("census_block_id"),
            HashingUtils.sha2HashWithSalt('CUSTOMER_ID, hashingSalt).cast("String").as("customer_id"),
            parseDate('MONTH_END_DATE).as("month_end_date"),
            lit(uploadDate).as("upload_date"),
            ('TRANSFER_FLAG === "Y").as("transfer_flag"),
            parseDate('CONNECT_DATE).as("connect_date"),
            parseDate('DISCONNECT_DATE).as("disconnect_date"),
            lit(6104).as("losing_sp_id"),
            colMapUdf(struct('*), array(mapCols.map(lit(_)): _*)).as("other"))
          .filter('customer_id.isNotNull
            && 'customer_id =!= ""
            && 'month_end_date <= 'upload_date
            && dayofmonth('month_end_date) === 28)
          .as[IngestedDisconnects]
      }
    }
  }

  def connectAdapters: Map[String, Adapter[IngestedConnects]] = Map(
    "comcast" -> Comcast.ConnectAdapter,
    "charter" -> Charter.ConnectAdapter
  )

  def disconnectAdapters: Map[String, Adapter[IngestedDisconnects]] = Map(
    "comcast" -> Comcast.DisconnectAdapter,
    "charter" -> Charter.DisconnectAdapter
  )
}
