package com.opensignal.emrjobs.spark.firstpartyingestion.tier2.AdjustmentFactors

import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner}
import com.opensignal.emrjobs.spark.firstpartyingestion.FirstPartyIngestionUtils
import com.opensignal.emrjobs.spark.firstpartyingestion.FirstPartyIngestionUtils.validateDateRange
import com.opensignal.emrjobs.spark.firstpartyingestion.ValidationCases.{validateDataSetTimeGap, validateProcessingScopeIntegrity}
import com.opensignal.largescale.schema.first_party_ingestion.tier2.OptimizationModeling.NonMoverWinsPerLossOptimizationOutput
import com.opensignal.largescale.schema.first_party_ingestion.tier2.OptimizationPreparation.NonMoverWinsPerLossOptimizationInput
import com.opensignal.largescale.schema.first_party_ingestion.tier2.UpdateAdjustmentFactors.FirstPartyAdjustmentFactors
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.broadcast
import org.apache.spark.sql.{Dataset, SaveMode, SparkSession}

import java.net.URI
import java.time.LocalDate


case class UpdateAdjustmentFactorsJobConfig(
  nonMoverWinsPerLossOptimizationInputPath: URI,
  nonMoverWinsPerLossOptimizationOutputPath: URI,
  adjustmentFactorsPath: URI,
  dateRangeOpt: Option[LocalDateRange],
  lookBackMonths: Option[Int],
  endOfMonthDay: Option[Int],
  processingDate: Option[LocalDate],
  cordAttacherUpperBound: Double,
  cordAttacherLowerBound: Double,
  numOutputPartitions: Int
)


object UpdateAdjustmentFactorsJobRunner extends SparkJobRunner[UpdateAdjustmentFactorsJobConfig] with LazyLogging {
  override def runJob(config: UpdateAdjustmentFactorsJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    // Configure for dynamic overwrite
    spark.conf.set("spark.sql.sources.partitionOverwriteMode", "dynamic")

    // Processing Date Range
    val dateRange = FirstPartyIngestionUtils.getProcessingDateRange(config.dateRangeOpt, config.lookBackMonths, config.endOfMonthDay)
    validateDateRange(dateRange)
    val revision = config.processingDate.getOrElse(LocalDate.now())
    logger.info(s"Running BBCV Aggregated Churn Preparation for Dates in range: (${dateRange.startDate}, ${dateRange.endDate})")

    // Load the Non-Movers Wins-Per-Loss Optimization Input
    val nonMoverWinsPerLossAgg = NonMoverWinsPerLossOptimizationInput.read(config.nonMoverWinsPerLossOptimizationInputPath.toString, dateRange, revision)

    // Load the Optimization Modeling Output with appropriate filtering
    val optimizationOutput = NonMoverWinsPerLossOptimizationOutput.read(config.nonMoverWinsPerLossOptimizationOutputPath.toString, dateRange, revision)
      .filter($"relax_step" === 0.0 && $"provider_wins" >= 0.0)

    // Calculate the New Adjustment Factors
    val adjustmentFactors = calculateAdjustmentFactors(
      nonMoverWinsPerLossAgg,
      optimizationOutput,
      config.cordAttacherLowerBound,
      config.cordAttacherUpperBound
    )

    // Apply validation before storing data to S3
    logger.info("Applying validation before storing adjustment factors to S3...")

    // Validate no gaps in time series data set
    validateDataSetTimeGap(adjustmentFactors, "month_end_date")
    logger.info("✓ Time series gap validation passed - no missing dates detected")

    // Validate processing scope - ensure only intended dates are being overwritten
    validateProcessingScopeIntegrity(adjustmentFactors, dateRange, "month_end_date")
    logger.info("✓ Processing scope validation passed - output contains expected date range")

    // Output to S3
    adjustmentFactors
      .repartition(config.numOutputPartitions, $"month_end_date", $"processing_date")
      .write
      .mode(SaveMode.Overwrite)
      .partitionBy("month_end_date", "processing_date")
      .parquet(config.adjustmentFactorsPath.toString)
  }


  /**
    * Join the Non-Mover Wins-Per-Loss Aggregate (optimization input) with the Optimization Modeling output in order
    * to calculate the final multipliers to be used
    * @param optInputs NonMoverWinsPerLossOptimizationInput table containing the non-mover WPL aggregations
    * @param optOutputs NonMoverWinsPerLossOptimizationOutput table containing the optimization modeling results
    * @param cordAttacherLowerBound lower bound for cord attacher ratio to be kept
    * @param cordAttacherUpperBound upper bound for the cord attacher ratio to be kept
    * @param spark
    * @return
    */
  def calculateAdjustmentFactors(
    optInputs: Dataset[NonMoverWinsPerLossOptimizationInput],
    optOutputs: Dataset[NonMoverWinsPerLossOptimizationOutput],
    cordAttacherLowerBound: Double,
    cordAttacherUpperBound: Double
  )(implicit spark: SparkSession): Dataset[FirstPartyAdjustmentFactors] = {
    import spark.implicits._

    optInputs
      .joinWith(
        broadcast(optOutputs),
        optInputs("dma_name") === optOutputs("dma_name") && optInputs("month_end_date") === optOutputs("month_end_date"))
      .map {
        case (nonMoverWinsPerLoss: NonMoverWinsPerLossOptimizationInput, optimizationOutput: NonMoverWinsPerLossOptimizationOutput) => {

          // Preliminary Computations
          val winVolumeMultiplierOptimization = optimizationOutput.final_wins / nonMoverWinsPerLoss.raw_wins
          val lossVolumeMultiplierOptimization = optimizationOutput.final_losses / nonMoverWinsPerLoss.raw_losses

          // Conditional Calculations
          val (winVolumeMultiplier, cordAttacherPct) = {
            val pct = 1 - ((nonMoverWinsPerLoss.raw_wins * lossVolumeMultiplierOptimization) / optimizationOutput.provider_wins)
            if (pct >= cordAttacherLowerBound && pct <= cordAttacherUpperBound) (lossVolumeMultiplierOptimization, pct)
            else (winVolumeMultiplierOptimization, optimizationOutput.cord_attacher_pct)
          }

          // Construct Object with Desired Output Schema
          FirstPartyAdjustmentFactors(
            month_end_date = nonMoverWinsPerLoss.month_end_date,
            dma_name = nonMoverWinsPerLoss.dma_name,
            win_volume_multiplier = winVolumeMultiplier,
            loss_volume_multiplier = lossVolumeMultiplierOptimization,
            cord_attacher_pct = cordAttacherPct,
            cord_cutter_pct = optimizationOutput.cord_cutter_pct,
            processing_date = nonMoverWinsPerLoss.processing_date
          )
        }
      }
  }

}

object UpdateAdjustmentFactorsJob extends SparkJob(UpdateAdjustmentFactorsJobRunner)
