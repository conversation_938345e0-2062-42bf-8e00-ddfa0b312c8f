package com.opensignal.emrjobs.spark.firstpartyingestion.tier3.PrePlatformAggregate

import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner}
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierAggRollup
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierAggRollup.implicits.CarrierAggRollupOps
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.GuardRail
import com.opensignal.emrjobs.spark.firstpartyingestion.FirstPartyIngestionUtils
import com.opensignal.emrjobs.spark.firstpartyingestion.FirstPartyIngestionUtils.{PreparedBbcvOutputTypes, getFmeDateFromChurnDate, validateDateRange}
import com.opensignal.emrjobs.spark.firstpartyingestion.tier1.BbcvDataPreparation.lookup.CensusBlockToMasterBlockLookup25
import com.opensignal.emrjobs.spark.firstpartyingestion.tier1.SpatialInputPreparation.lookup.SpatialInputLookupModel.MasterBlockLookup
import com.opensignal.emrjobs.spark.firstpartyingestion.tier3.PrePlatformAggregate.Adapters.AggregationCustomRulesAdapter
import com.opensignal.emrjobs.spark.firstpartyingestion.tier3.PrePlatformAggregate.IntermediaryModels.IntermediaryAggregationModels._
import com.opensignal.largescale.commons.algo.guard_rails.implicits.{GrApplyImplicitOps, GrTransformImplicitOps}
import com.opensignal.largescale.schema.first_party_ingestion.tier1.BbcvDataPreparation.{PreparedBbcvOutputLosses, PreparedBbcvOutputWins}
import com.opensignal.largescale.schema.first_party_ingestion.tier1.SpatialInputPreparation._
import com.opensignal.largescale.schema.first_party_ingestion.tier2.OptimizationModeling.NonMoverWinsPerLossOptimizationOutput
import com.opensignal.largescale.schema.first_party_ingestion.tier2.OptimizationPreparation.NonMoverWinsPerLossOptimizationInput
import com.opensignal.largescale.schema.first_party_ingestion.tier2.SpatialAllocation.SpatialAllocationOutput
import com.opensignal.largescale.schema.first_party_ingestion.tier2.UpdateAdjustmentFactors.FirstPartyAdjustmentFactors
import com.opensignal.largescale.schema.first_party_ingestion.tier3.PrePlatformAggregate.BroadbandMovementResAggregate.DefaultBroadbandMovementResAggregate
import com.opensignal.largescale.commons.algo.guard_rails.GrApplyOps
import com.opensignal.largescale.schema.lookup_tables.broadband25.GuardRailExt
import com.opensignal.largescale.schema.lookup_tables.broadband25.guard_rails.AggColNames
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.{avg, broadcast, coalesce, col, countDistinct, lit, sum, udf, when}
import org.apache.spark.sql.{DataFrame, Dataset, Row, SaveMode, SparkSession}

import java.net.URI
import java.sql.Date
import java.time.LocalDate
import scala.reflect.ClassTag
import scala.reflect.runtime.universe.TypeTag


case class ComcastPrePlatformAggregateJobConfig(
  bbcvProviderWinsPath: URI,
  bbcvProviderLossesPath: URI,
  bbcvNonProviderWinsPath: URI,
  bbcvNonProviderLossesPath: URI,
  spatialInputConnectsPath: URI,
  spatialInputDisconnectsPath: URI,
  spatialOutputConnectsPath: Option[URI],
  spatialOutputDisconnectsPath: Option[URI],
  adjustmentFactorsPath: URI,
  aggCarrierRollupLookupPath: URI,
  connectsAggregatePath: URI,
  disconnectsAggregatePath: URI,
  broadbandMovementResAggregatePath: URI,
  guardRailLookupPath: URI,
  dmaGuardRailLookupPath: Option[URI],
  masterBlockLookupPath25: URI,
  provider: String,
  providerSpList: Array[Int],
  dateRangeOpt: Option[LocalDateRange],
  lookBackMonths: Option[Int],
  endOfMonthDay: Option[Int],
  processingDate: Option[LocalDate],
  numOutputPartitions: Int,
)


object ComcastPrePlatformAggregateJobRunner extends SparkJobRunner[ComcastPrePlatformAggregateJobConfig] with LazyLogging {
  override def runJob(config: ComcastPrePlatformAggregateJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    // Configure for dynamic overwrite
    spark.conf.set("spark.sql.sources.partitionOverwriteMode", "dynamic")
    spark.sparkContext.setCheckpointDir("hdfs:/tmp-spark-checkpoint")

    // Processing Date Range
    val dateRange = FirstPartyIngestionUtils.getProcessingDateRange(config.dateRangeOpt, config.lookBackMonths, config.endOfMonthDay)
    val revision = config.processingDate.getOrElse(LocalDate.now())
    logger.info(s"Running Pre-Platform Aggregate Calculation for Dates in range: (${dateRange.startDate}, ${dateRange.endDate})")
    validateDateRange(dateRange)
    // Get the Provider-Specific Custom Aggregation Rules
    val adapter = AggregationCustomRulesAdapter.forProvider(config.provider)
    logger.info(s"Loaded Custom Logic for Provider: ${adapter.provider}")

    // Load the Prepared BBCV Subsets
    logger.info(s"Loading Prepared BBCV Subsets for Provider Wins/Losses & Non-Provider Wins/Losses using revision=${revision}")
    val bbcvProviderWins = PreparedBbcvOutputWins.read(config.bbcvProviderWinsPath.toString, dateRange, revision)
    val bbcvProviderLosses = PreparedBbcvOutputLosses.read(config.bbcvProviderLossesPath.toString, dateRange, revision)
    val bbcvNonProviderWins = PreparedBbcvOutputWins.read(config.bbcvNonProviderWinsPath.toString, dateRange, revision)
    val bbcvNonProviderLosses = PreparedBbcvOutputLosses.read(config.bbcvNonProviderLossesPath.toString, dateRange, revision)

    bbcvProviderWins.where($"loser_sp_platform" === 6746).groupBy($"month_end_date").agg(sum($"multiplier")).show(20)
    bbcvProviderWins.where($"loser_sp_platform" === 6746).show(20)
    print(bbcvProviderWins.where($"loser_sp_platform" === 6746).groupBy($"month_end_date").agg(countDistinct("ifa")).show(20))

    // Load the Spatial Input Tables
    logger.info(s"Loading Spatial Input tables for Connects & Disconnects using revision=${revision}")
    val spatialInputConnects = SpatialInputConnect.read(config.spatialInputConnectsPath.toString, dateRange, revision)
    val spatialInputDisconnects = SpatialInputDisconnect.read(config.spatialInputDisconnectsPath.toString, dateRange, revision)

    // Load Spatial Allocation Output
    logger.info(s"Loading Spatial Allocation Weights for Connects & Disconnects using revision=${revision} if provided")
    val spatialOutputIsDefined = config.spatialOutputConnectsPath.isDefined && config.spatialOutputDisconnectsPath.isDefined
    val (
      spatialOutputConnects: Option[Dataset[SpatialAllocationOutput]],
      spatialOutputDisconnects: Option[Dataset[SpatialAllocationOutput]]
      ) = if (spatialOutputIsDefined) { (
      Some(SpatialAllocationOutput.read(config.spatialOutputConnectsPath.get.toString, dateRange, revision)),
      Some(SpatialAllocationOutput.read(config.spatialOutputDisconnectsPath.get.toString, dateRange, revision))
    )
    } else { (None, None) }

    // Load Adjustment Factors
    logger.info(s"Loading Adjustment Factors using revision=${revision}")
    val adjustmentFactors = FirstPartyAdjustmentFactors.read(config.adjustmentFactorsPath.toString, dateRange, revision).persist


    // Calculate Spatial Weights
    logger.info("Mapping Spatial Allocation Weights to Spatial Input")
    val spatialConnectsWeights = applySpatialAllocationWeightsToSpatialInput(spatialInputConnects, spatialOutputConnects)
    val spatialDisconnectsWeights = applySpatialAllocationWeightsToSpatialInput(spatialInputDisconnects, spatialOutputDisconnects)

    // Apply Spatial Weights to BBCV Data
    logger.info("Mapping Spatial Allocation Weights to BBCV Subsets")
    val weightedProviderWins = applySpatialWeightsToBbcvWin(bbcvProviderWins, spatialConnectsWeights)
    val weightedNonProviderWins = applySpatialWeightsToBbcvWin(bbcvNonProviderWins, None)
    val weightedProviderLosses = applySpatialWeightsToBbcvLoss(bbcvProviderLosses, spatialDisconnectsWeights)
    val weightedNonProviderLosses = applySpatialWeightsToBbcvLoss(bbcvNonProviderLosses, None)

    weightedProviderWins.where($"win".isNotNull && $"win.loser_sp_platform" === 6746).show(20)
    weightedProviderWins.where($"win".isNotNull && $"win.loser_sp_platform" === 6746).groupBy($"win.month_end_date").agg(sum($"win.multiplier")).show(20)



    // Apply Adjustment Factors to BBCV Data
    logger.info("Applying Adjustment Factors to Weighted BBCV Subsets")
    val adjustedProviderWins = applyAdjustmentFactorsToBbcvSubset(weightedProviderWins, adjustmentFactors, PreparedBbcvOutputTypes.ProviderWins)
    val adjustedNonProviderWins = applyAdjustmentFactorsToBbcvSubset(weightedNonProviderWins, adjustmentFactors, PreparedBbcvOutputTypes.NonProviderWins)
    val adjustedProviderLosses = applyAdjustmentFactorsToBbcvSubset(weightedProviderLosses, adjustmentFactors, PreparedBbcvOutputTypes.ProviderLosses)
    val adjustedNonProviderLosses = applyAdjustmentFactorsToBbcvSubset(weightedNonProviderLosses, adjustmentFactors, PreparedBbcvOutputTypes.NonProviderLosses)

    adjustedProviderWins.where($"loser_sp_platform" === 6746).show(20)
    adjustedProviderWins.where($"loser_sp_platform" === 6746).groupBy($"month_end_date").agg(sum($"adjusted_wins")).show(20)

    // Calculate the Broadband Movement Res Aggregate
    val broadbandMovementResAggregate = adapter
      .calculateBroadbandMovementResAggregate(adjustedProviderWins, PreparedBbcvOutputTypes.ProviderWins)
      .union(adapter.calculateBroadbandMovementResAggregate(adjustedNonProviderWins, PreparedBbcvOutputTypes.NonProviderWins))
      .union(adapter.calculateBroadbandMovementResAggregate(adjustedProviderLosses, PreparedBbcvOutputTypes.ProviderLosses))
      .union(adapter.calculateBroadbandMovementResAggregate(adjustedNonProviderLosses, PreparedBbcvOutputTypes.NonProviderLosses))

    // Load the Carrier Roll-Up Lookup Table
    val carrierAggRollup: Dataset[CarrierAggRollup] = CarrierAggRollup.read(URI create config.aggCarrierRollupLookupPath.toString)

    implicit val masterBlockLookup25: CensusBlockToMasterBlockLookup25 = CensusBlockToMasterBlockLookup25(config.masterBlockLookupPath25, config.provider, config.providerSpList)
    val dmaGuardRailTargetsLookup = spark.read.option("header", "true").csv(config.dmaGuardRailLookupPath.get.toString)
    val dmaList = dmaGuardRailTargetsLookup.select("dma").as[String].collect().toList

    // Apply the Agg Carrier Roll-Up Lookup with Post-Rollup Self-Churn Removal
    val withAggCarrierRollup = broadbandMovementResAggregate // todo - 2020 rollups ?
      .transform(carrierAggRollup.combineCarriers($"primary_sp_group", $"primary_census_block_id"))
      .transform(carrierAggRollup.combineCarriers($"secondary_sp_group", $"secondary_census_block_id"))
      .where($"primary_sp_group" =!= $"secondary_sp_group")
      .transform(adapter.applyCustomProviderRules)
      .transform(masterBlockLookup25.appendFieldsDF)
      .withColumn("dma", when($"dma".isInCollection(dmaList), $"dma").otherwise(lit("other")))


    val guardRailTargetsLookup: Dataset[GuardRail] = GuardRail.read(config.guardRailLookupPath)

    implicit val aggColNames: AggColNames = AggColNames(
      date = "the_date",
      winner = "primary_sp_group",
      loser = "secondary_sp_group",
      winner_geoid = "",
      loser_geoid = "",
      wins = "adjusted_wins",
      losses = "adjusted_losses"
    )


    val guardRailsAtt = GuardRailExt.createAtt(guardRailTargetsLookup, config.endOfMonthDay, dateRange, dmaGuardRailTargetsLookup)
    val guardRails = GuardRailExt.createOther(guardRailTargetsLookup, config.endOfMonthDay, dateRange)

    guardRailsAtt.where($"primary_sp_group" === 6746).sort($"max_date".desc).show(50)
    guardRails.where($"primary_sp_group" === 6713).sort($"max_date".desc).show(50)
    guardRails.where($"primary_sp_group" === 6730).sort($"max_date".desc).show(50)
    val grTransformAtt = withAggCarrierRollup.transform(guardRailsAtt.mkGrTransform(0, "", ""))
    val grTransformOther = withAggCarrierRollup.transform(guardRails.mkGrTransform(0, "", ""))
    val grTransform = grTransformAtt.unionByName(grTransformOther)
    grTransform.where($"primary_sp_group" === 6746).sort($"partition_date".desc).show(50)
    grTransform.where($"primary_sp_group" === 6713).sort($"partition_date".desc).show(50)
    grTransform.where($"primary_sp_group" === 6730).sort($"partition_date".desc).show(50)
    val finalAggregate = withAggCarrierRollup.transform(ds => grTransform.applyGr(ds)(withAggCarrierRollup.encoder))


    // Calculate Connects Aggregate
    logger.info("Calculating the Connects Aggregate")
    val connectsAggregate = applyAdjustmentFactorsToSpatialInput(spatialInputConnects, adjustmentFactors)
      .transform(adapter.calculateConnectsAggregate)


    // Calculate Disconnects Aggregate
    logger.info("Calculating the Disconnects Aggregate")
    val disconnectsAggregate = applyAdjustmentFactorsToSpatialInput(spatialInputDisconnects, adjustmentFactors)
      .transform(adapter.calculateDisconnectsAggregate)


    // Save the Aggregations to S3
    adapter.writeConnectsAggregateToS3(connectsAggregate, Date.valueOf(revision), config.connectsAggregatePath, config.numOutputPartitions)
    adapter.writeDisconnectsAggregateToS3(disconnectsAggregate, Date.valueOf(revision), config.disconnectsAggregatePath, config.numOutputPartitions)
    adapter.writeBroadbandMovementAggregateToS3(finalAggregate, Date.valueOf(revision), config.broadbandMovementResAggregatePath, config.numOutputPartitions)
  }

  /**
    * Left-Joins Spatial Input (connects or disconnects) to adjustment factors, and replaces the inclusion_flag field with
    * a new transfer_ind column
    * @param spatialInput The spatial input (connects or disconnects) to be joined
    * @param adjustmentFactors the adjustment factors to left join to the spatial input
    * @param spark
    * @tparam T
    * @return
    */
  def applyAdjustmentFactorsToSpatialInput[T <: SpatialInputCommons : ClassTag : TypeTag](spatialInput: Dataset[T], adjustmentFactors: Dataset[FirstPartyAdjustmentFactors])(implicit spark: SparkSession): Dataset[Row] = {
    import spark.implicits._
    spatialInput
      .withColumn("transfer_ind", when($"inclusion_flag", 1).otherwise(0))
      .join(adjustmentFactors, Seq("month_end_date", "dma_name"), "left")
      .drop("inclusion_flag")
  }


  /**
    * Inner join the spatial input dataset (connects or disconnects) to the spatial allocation output in order to extract
    * the weights and the "other" fields
    * @param spatialInput The spatial input (connects or disconnects) to be joined
    * @param spatialOutput the spatial allocation output containing the weights to be joined
    * @param spark
    * @tparam T
    * @return
    */
  def applySpatialAllocationWeightsToSpatialInput[T <: SpatialInputCommons : ClassTag : TypeTag](spatialInput: Dataset[T], spatialOutput: Option[Dataset[SpatialAllocationOutput]])(implicit spark: SparkSession): Option[Dataset[SpatialWeights]] = {
    import spark.implicits._
    if (spatialOutput.isDefined) {
      Some(spatialInput
        .join(spatialOutput.get, Seq("month_end_date", "customer_id"))
        .select($"ifa", $"month_end_date", $"weight", $"other")
        .as[SpatialWeights])
    }
    else None
  }


  /**
    * Map PreparedBbcvOutputWin to SpatialWeights (if applicable), leaving a placeholder loss field for union with
    * the losses results downstream
    * @param bbcvWin PreparedBbcvOutputWin (provider win or non provider win)
    * @param spatialWeights the determined spatial weights to be mapped (or none if non-provider win)
    * @param spark
    * @return
    */
  def applySpatialWeightsToBbcvWin(bbcvWin: Dataset[PreparedBbcvOutputWins], spatialWeights: Option[Dataset[SpatialWeights]])(implicit spark: SparkSession): Dataset[BbcvWithSpatialWeight] = {
    import spark.implicits._

    spatialWeights match {
      case None => bbcvWin.map { win => BbcvWithSpatialWeight(win=Some(win), loss=None, weight=None) }
      case Some(weights) => bbcvWin
        .joinWith(weights, bbcvWin("month_end_date")===weights("month_end_date") && bbcvWin("ifa")===weights("ifa"))
        .map {
          case (win: PreparedBbcvOutputWins, weight: SpatialWeights) =>
            BbcvWithSpatialWeight(win=Some(win), loss=None, weight=Some(weight))
        }
    }
  }


  /**
    * Map PreparedBbcvOutputLoss to SpatialWeights (if applicable), leaving a placeholder win field for union with
    * the wins results downstream
    * @param bbcvLoss PreparedBbcvOutputLoss (provider loss or non provider loss)
    * @param spatialWeights the determined spatial weights to be mapped (or none if non-provider loss)
    * @param spark
    * @return
    */
  def applySpatialWeightsToBbcvLoss(bbcvLoss: Dataset[PreparedBbcvOutputLosses], spatialWeights: Option[Dataset[SpatialWeights]])(implicit spark: SparkSession): Dataset[BbcvWithSpatialWeight] = {
    import spark.implicits._

    spatialWeights match {
      case None => bbcvLoss.map { loss => BbcvWithSpatialWeight(win=None, loss=Some(loss), weight=None) }
      case Some(weights) => bbcvLoss
        .joinWith(weights, bbcvLoss("month_end_date")===weights("month_end_date") && bbcvLoss("ifa")===weights("ifa"))
        .map {
          case (loss: PreparedBbcvOutputLosses, weight: SpatialWeights) =>
            BbcvWithSpatialWeight(win=None, loss=Some(loss), weight=Some(weight))
        }
    }
  }


  /**
    * Join the PreparedBbcvOutput with Spatial Weights dataset to the Adjustment Factors using different conditions based
    * on which subset type the source of the PreparedBbcvOutput uses.
    * @param weightedBbcv The PreparedBbcvOutput subset (provider wins/losses or non provider wins/losses) after joining with
    *                     the spatial weights
    * @param adjustmentFactors The adjustment factors to be applied to the weightedBbcv table
    * @param subsetType enum to indicate which of the four subset types is the source of the data
    * @param spark
    * @return
    */
  def applyAdjustmentFactorsToBbcvSubset(weightedBbcv: Dataset[BbcvWithSpatialWeight], adjustmentFactors: Dataset[FirstPartyAdjustmentFactors], subsetType: PreparedBbcvOutputTypes.Value)(implicit spark: SparkSession): Dataset[AdjustedBbcv] = {
    import spark.implicits._

    // Join on the appropriate DMA field depending on BBCV subset
    val dmaJoinCondition = subsetType match {
      case PreparedBbcvOutputTypes.ProviderWins | PreparedBbcvOutputTypes.NonProviderWins =>
        weightedBbcv("win.month_end_date") === adjustmentFactors("month_end_date") &&
          weightedBbcv("win.winning_dma_name") === adjustmentFactors("dma_name")
      case PreparedBbcvOutputTypes.ProviderLosses | PreparedBbcvOutputTypes.NonProviderLosses =>
        weightedBbcv("loss.month_end_date") === adjustmentFactors("month_end_date") &&
          weightedBbcv("loss.losing_dma_name") === adjustmentFactors("dma_name")
    }

    // Perform the Join
    weightedBbcv
      .joinWith(broadcast(adjustmentFactors), dmaJoinCondition)
      .map {
        case (weighted: BbcvWithSpatialWeight, factors: FirstPartyAdjustmentFactors) =>
          val winOrLoss: Double = subsetType match{
            case PreparedBbcvOutputTypes.ProviderWins | PreparedBbcvOutputTypes.NonProviderWins =>
              if (weighted.weight.isDefined)
              {
                weighted.weight.get.weight * (1.0 - factors.cord_attacher_pct)}
              else factors.win_volume_multiplier * weighted.win.get.multiplier
            case PreparedBbcvOutputTypes.ProviderLosses | PreparedBbcvOutputTypes.NonProviderLosses =>
              if (weighted.weight.isDefined) weighted.weight.get.weight * (1.0 - factors.cord_cutter_pct)
              else factors.loss_volume_multiplier * weighted.loss.get.multiplier
            case _ => 0.0
          }
          val (adjustedWin, adjustedLoss) = subsetType match {
            case PreparedBbcvOutputTypes.ProviderWins | PreparedBbcvOutputTypes.NonProviderWins => (winOrLoss, 0.0)
            case PreparedBbcvOutputTypes.ProviderLosses | PreparedBbcvOutputTypes.NonProviderLosses => (0.0, winOrLoss)
          }
          val (rawWin, rawLoss) = (
            adjustedWin / factors.win_volume_multiplier,
            adjustedLoss / factors.loss_volume_multiplier
          )
          AdjustedBbcv(
            month_end_date = factors.month_end_date,
            loser_sp_platform = weighted.win.getOrElse(weighted.loss.get).loser_sp_platform,
            winner_sp_platform = weighted.win.getOrElse(weighted.loss.get).winner_sp_platform,
            losing_census_block_id = weighted.win.getOrElse(weighted.loss.get).losing_census_blockid,
            winning_census_block_id = weighted.win.getOrElse(weighted.loss.get).winning_census_blockid,
            mover_ind = weighted.win.getOrElse(weighted.loss.get).is_mover,
            other = if (weighted.weight.isDefined) weighted.weight.get.other else Map[String,String](),
            win_volume_multiplier = factors.win_volume_multiplier,
            loss_volume_multiplier = factors.loss_volume_multiplier,
            raw_wins = rawWin,
            raw_losses = rawLoss,
            adjusted_wins = adjustedWin,
            adjusted_losses = adjustedLoss
          )
      }
  }

}

object ComcastPrePlatformAggregateJob extends SparkJob(ComcastPrePlatformAggregateJobRunner)
