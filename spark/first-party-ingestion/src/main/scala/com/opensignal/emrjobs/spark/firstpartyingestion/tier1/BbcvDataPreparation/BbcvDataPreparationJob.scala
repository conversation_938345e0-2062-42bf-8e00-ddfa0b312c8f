package com.opensignal.emrjobs.spark.firstpartyingestion.tier1.BbcvDataPreparation

import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner}
import com.comlinkdata.largescale.schema.broadband_market_share.BroadbandPlatformAggregateWithId
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.GuardRail
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.GuardRail.implicits.GuardRailOps
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.GuardRailTransform.implicits.GuardRailTransformOps
import com.opensignal.emrjobs.spark.firstpartyingestion.FirstPartyIngestionUtils
import com.opensignal.emrjobs.spark.firstpartyingestion.FirstPartyIngestionUtils.{PreparedBbcvOutputTypes, getFmeDateFromChurnDate, validateDateRange}
import com.opensignal.emrjobs.spark.firstpartyingestion.ValidationCases.{validateDataSetTimeGap, validateTimeSeriesIntegrity, validateProcessingScopeIntegrity}
import com.opensignal.emrjobs.spark.firstpartyingestion.tier1.BbcvDataPreparation.IntermediaryModels.{WrappedPlatformAggregate25WithMasterBlock, WrappedPlatformAggregateWithMasterBlock}
import com.opensignal.emrjobs.spark.firstpartyingestion.tier1.BbcvDataPreparation.lookup.{CensusBlockToMasterBlockLookup, CensusBlockToMasterBlockLookup25, ServiceTerritoryLookup, ServiceTerritoryLookup25}
import com.opensignal.largescale.schema.first_party_ingestion.tier1.BbcvDataPreparation.{GeoIdFields, PreparedBbcvOutputCommons, PreparedBbcvOutputLosses, PreparedBbcvOutputWins, SpGroupFields}
import com.opensignal.largescale.schema.first_party_ingestion.tier3.PrePlatformAggregate.SyndicatedPlatformAggregate25
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, Dataset, Encoder, SaveMode, SparkSession}
import org.apache.commons.codec.digest.MurmurHash3

import java.net.URI
import java.time.LocalDate


object BbcvDataPreparationJobRunner extends SparkJobRunner[BbcvDataPreparationJobConfig] with LazyLogging {
  override def runJob(config: BbcvDataPreparationJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    // Configure for dynamic overwrite
    val rand = new scala.util.Random
    spark.sparkContext.setCheckpointDir(s"hdfs:/spark-checkpoints/comcast-2-0/bbcv/${rand.nextLong().abs}")
    spark.conf.set("spark.sql.sources.partitionOverwriteMode", "dynamic")

    // Processing Date Range )
    val forceRun = config.forceRun.getOrElse(false)
    val generalDateRange = FirstPartyIngestionUtils.getProcessingDateRange(config.dateRangeOpt, config.lookBackMonths, config.endOfMonthDay)
    val processingStartDate = generalDateRange.startDate.minusMonths(1).plusDays(1) // if FME=2023-01-21, start from 2022-12-22
    val guardRailStartDate = processingStartDate.minusDays(config.guardRailLookBack)
    val guardRailDateRange = LocalDateRange.of(guardRailStartDate, generalDateRange.endDate)
    val processingDate = config.processingDate.getOrElse(LocalDate.now())
    logger.info(s"Running BBCV Aggregated Churn Preparation for Dates in range: (${guardRailDateRange.startDate}, ${guardRailDateRange.endDate})")
    validateDateRange(generalDateRange)

    // Load Lookups
    implicit val masterBlockLookup25: CensusBlockToMasterBlockLookup25 = CensusBlockToMasterBlockLookup25(config.masterBlockLookupPath25, config.provider, config.providerSpList)
    implicit val svtLookup: ServiceTerritoryLookup = ServiceTerritoryLookup(config.serviceTerritoryLookupPath25, config.provider)

    val guardRailTargetsLookup: Dataset[GuardRail] = GuardRail.read(config.guardRailLookupPath)


    // Load Aggregate Source
    logger.info("Reading Broadband Platform Aggregate With ID")
    val platformAggregateUnion = config.datasources
      .map(dsConfig => {
        val ds = dsConfig.ds
        val dsEnd = dsConfig.endDate.getOrElse(ds.lastDate.getOrElse(LocalDate.now()))
        val minDate = if(ds.firstDate.isAfter(guardRailStartDate)) ds.firstDate else guardRailStartDate
        val maxDate = if(dsEnd.isBefore(guardRailDateRange.endDate)) dsEnd else guardRailDateRange.endDate
        if (minDate.isAfter(maxDate)) {
          spark.emptyDataset[BroadbandPlatformAggregateWithId]
        } else {
          BroadbandPlatformAggregateWithId.readExisting25(dsConfig.path, LocalDateRange(minDate, maxDate))
            .repartition($"the_date", $"element_id")
        }
      })
      .reduceLeft( (a,b) => {
        val keysToExclude = a.select($"the_date", $"element_id").distinct() // we only want each element_id represented once on a given the_date
        a.unionByName(b.join(keysToExclude, Seq("the_date", "element_id"), "left_anti").as[BroadbandPlatformAggregateWithId]).distinct()
      } )
      .repartition($"the_date")
      .cache
    logger.info(s"Read ${platformAggregateUnion.count()} total records from all Datasources")
    val platformAggregate = platformAggregateUnion.transform(preliminaryDeduplication(config.endOfMonthDay.getOrElse(21))).repartition($"the_date").cache
    logger.info(s"Remaining records after deduplication: ${platformAggregate.count()}")
    platformAggregateUnion.unpersist()

    // Input Data Validation -- Throw if Gap Days detected in Unioned Syndicated Inputs or if date exists outside range
    if (isBadDatesPresent(guardRailDateRange, platformAggregate) && !forceRun) {
      throw new IllegalStateException(s"Invalid Dates in range (${guardRailDateRange.startDate}, ${guardRailDateRange.endDate})")
    }

    if (guardRailTargetsLookup.select(max($"max_date")).first().getDate(0).toLocalDate.isBefore(generalDateRange.endDate)) {
      throw new IllegalStateException(s"Guard Rail Lookup does not contain data for the full range of dates to be processed")
    }

    // Read in Syndicated 2.5 Platform Agg
    logger.info(s"Loading Syndicated 2.5 Platform Agg on range ${guardRailDateRange} from ${config.syndicatedPlatformAggPath}")
    val syndicatedPlatformAgg = if (config.syndicatedPlatformAggPath.isDefined) SyndicatedPlatformAggregate25.read(config.syndicatedPlatformAggPath.get, guardRailDateRange, ds = Seq("eskimi", "gamoshi"), countryISO = "USA") else spark.emptyDataset[SyndicatedPlatformAggregate25]

    syndicatedPlatformAgg.groupBy($"the_date").agg(countDistinct($"ifa").as("ifa_count")).show(20)

    import spark.implicits._
    // Prefiltering to Appropriate Footprint & Service Territory
    val inTerritoryAgg = platformAggregate
      .transform(preliminarySpFiltering)
      .transform(inFootprintFiltering)
      .transform(primarySpInTerritory)
      .transform(secondarySpInTerritory)
      .cache
      .checkpoint

    val inTerritoryAggAtt = syndicatedPlatformAgg
      .where(($"primary_sp_group" === 6746 || $"secondary_sp_group" === 6746) && ($"primary_sp_group" =!= 7383 && $"secondary_sp_group" =!= 7383))
      .transform(preliminarySpFiltering)
      .transform(inFootprintFiltering)
      .transform(primarySpInTerritory)
      .transform(secondarySpInTerritory)
      .cache
      .checkpoint

    // Calculate the Guard Rail modifiers to apply
    val guardRailModifiers = inTerritoryAgg.transform(guardRailTargetsLookup.createGuardRail(config.guardRailLookBack))

    // Universal Transformations (filters before parsing into the 4 outputs)
    val aggregateWithGr = inTerritoryAgg
      .filter($"the_date" >= processingStartDate)
      .transform(guardRailModifiers.applyGuardRail)

    val aggregateBeforeParse = aggregateWithGr
      .transform(WrappedPlatformAggregateWithMasterBlock.wrapPlatformAggregate)
      .transform(masterBlockLookup25.withMasterBlockPreAgg)
      .repartition($"platform_aggregate.the_date")
      .persist
    platformAggregate.unpersist()



    // Parse the 4 output tables
    val providerWins = aggregateBeforeParse.transform(prepareOutput[PreparedBbcvOutputWins](config.providerSpList, config.endOfMonthDay, processingDate, PreparedBbcvOutputTypes.ProviderWins))
    val providerLosses = aggregateBeforeParse.transform(prepareOutput[PreparedBbcvOutputLosses](config.providerSpList, config.endOfMonthDay, processingDate, PreparedBbcvOutputTypes.ProviderLosses))
    val nonProviderWins = aggregateBeforeParse.transform(prepareOutput[PreparedBbcvOutputWins](config.providerSpList, config.endOfMonthDay, processingDate, PreparedBbcvOutputTypes.NonProviderWins))
    val nonProviderLosses = aggregateBeforeParse.transform(prepareOutput[PreparedBbcvOutputLosses](config.providerSpList, config.endOfMonthDay, processingDate, PreparedBbcvOutputTypes.NonProviderLosses))

    val allWins = providerWins.unionByName(nonProviderWins)
    val allLosses = providerLosses.unionByName(nonProviderLosses)

    val attAggregateBeforeParse = inTerritoryAggAtt
      .filter($"the_date" >= processingStartDate)
      .transform(applyAttGuardRail(allWins, allLosses, guardRailTargetsLookup, config.endOfMonthDay))
      .transform(WrappedPlatformAggregate25WithMasterBlock.wrapPlatformAggregate)
      .transform(masterBlockLookup25.withMasterBlockAgg)
      .repartition($"platform_aggregate.the_date")
      .persist


    println("AT&T Agg")
    attAggregateBeforeParse.groupBy($"platform_aggregate.the_date").agg(sum("platform_aggregate.adjusted_wins"), sum("platform_aggregate.adjusted_losses")).show(20)
    println("Other Agg")
    aggregateBeforeParse.groupBy($"platform_aggregate.the_date").agg(sum("platform_aggregate.adjusted_wins"), sum("platform_aggregate.adjusted_losses")).show(20)

    val attProviderWins = attAggregateBeforeParse.transform(prepareAttFwaOutput[PreparedBbcvOutputWins](config.providerSpList, config.endOfMonthDay, processingDate, PreparedBbcvOutputTypes.ProviderWins))
    val attProviderLosses = attAggregateBeforeParse.transform(prepareAttFwaOutput[PreparedBbcvOutputLosses](config.providerSpList, config.endOfMonthDay, processingDate, PreparedBbcvOutputTypes.ProviderLosses))
    val attNonProviderWins = attAggregateBeforeParse.transform(prepareAttFwaOutput[PreparedBbcvOutputWins](config.providerSpList, config.endOfMonthDay, processingDate, PreparedBbcvOutputTypes.NonProviderWins))
    val attNonProviderLosses = attAggregateBeforeParse.transform(prepareAttFwaOutput[PreparedBbcvOutputLosses](config.providerSpList, config.endOfMonthDay, processingDate, PreparedBbcvOutputTypes.NonProviderLosses))

    val providerWinsUnion = attProviderWins.unionByName(providerWins)
    val providerLossesUnion = attProviderLosses.unionByName(providerLosses)
    val nonProviderWinsUnion = attNonProviderWins.unionByName(nonProviderWins)
    val nonProviderLossesUnion = attNonProviderLosses.unionByName(nonProviderLosses)

    // Apply validation for the three scenarios if applicable
    logger.info("Applying time series integrity validation...")

    // Validate each output dataset
    validateTimeSeriesIntegrity(
      providerWinsUnion,
      Some(config.outputConfig.providerWinsPath.toString),
      generalDateRange,
      "month_end_date"
    )

    validateTimeSeriesIntegrity(
      providerLossesUnion,
      Some(config.outputConfig.providerLossesPath.toString),
      generalDateRange,
      "month_end_date"
    )

    validateTimeSeriesIntegrity(
      nonProviderWinsUnion,
      Some(config.outputConfig.nonProviderWinsPath.toString),
      generalDateRange,
      "month_end_date"
    )

    validateTimeSeriesIntegrity(
      nonProviderLossesUnion,
      Some(config.outputConfig.nonProviderLossesPath.toString),
      generalDateRange,
      "month_end_date"
    )

    logger.info("✓ All time series integrity validations passed")

    // Additional validation: Ensure processing scope is correct
    validateProcessingScopeIntegrity(providerWinsUnion, generalDateRange, "month_end_date")
    validateProcessingScopeIntegrity(providerLossesUnion, generalDateRange, "month_end_date")
    validateProcessingScopeIntegrity(nonProviderWinsUnion, generalDateRange, "month_end_date")
    validateProcessingScopeIntegrity(nonProviderLossesUnion, generalDateRange, "month_end_date")

    logger.info("✓ All processing scope validations passed")

    // Output to S3
    writeOutputToS3(providerWinsUnion, config.outputConfig.providerWinsPath, config.outputConfig.numFilesPerPartition)
    writeOutputToS3(providerLossesUnion, config.outputConfig.providerLossesPath, config.outputConfig.numFilesPerPartition)
    writeOutputToS3(nonProviderWinsUnion, config.outputConfig.nonProviderWinsPath, config.outputConfig.numFilesPerPartition)
    writeOutputToS3(nonProviderLossesUnion, config.outputConfig.nonProviderLossesPath, config.outputConfig.numFilesPerPartition)
  }

  /**
    * Boolean check to ensure there are no gap dates in the data or dates outside the desired ldr by ensuring the
    * length of the expected date range matches the count of distinct dates in the input table, and that the
    * distinct count of dates outside the LDR is 0
    * @param ldr local date range to use for the check
    * @param data data to be validated
    * @param dateColumnName optional string of the column name for the date column to validate. Default is "the_date"
    * @param spark
    * @return
    */
  def isBadDatesPresent(ldr: LocalDateRange, data: Dataset[BroadbandPlatformAggregateWithId], dateColumnName: String="the_date"): Boolean = {
    val gapDays = ldr.length != data
      .filter(col(dateColumnName)>=ldr.startDate && col(dateColumnName)<=ldr.endDate)
      .select(col(dateColumnName))
      .distinct
      .count
    val outOfRange = data
      .filter(col(dateColumnName)<ldr.startDate || col(dateColumnName)>ldr.endDate)
      .select(col(dateColumnName))
      .distinct
      .count != 0
    gapDays || outOfRange
  }



  /**
    * Validates that historical data remains unchanged and only the requested processing dates are being overwritten.
    * This ensures data integrity by comparing existing output with current processing data.
    *
    * @param currentData Current dataset being processed
    * @param existingOutputPath Path to existing output data
    * @param processingDateRange Date range being processed in current run
    * @param dateColumn Name of the date column
    * @param spark Implicit SparkSession
    */
  def validateHistoryIntegrity[T](
    currentData: Dataset[T],
    existingOutputPath: String,
    processingDateRange: LocalDateRange,
    dateColumn: String
  )(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    try {
      // Check if existing output path exists
      val fs = org.apache.hadoop.fs.FileSystem.get(spark.sparkContext.hadoopConfiguration)
      val path = new org.apache.hadoop.fs.Path(existingOutputPath)

      if (!fs.exists(path)) {
        logger.info("✓ No existing output found - initial run, skipping history validation")
        return
      }

      // Read existing data
      val existingData = spark.read.parquet(existingOutputPath)

      // Get dates outside the processing range from existing data
      val historicalData = existingData
        .filter(col(dateColumn) < processingDateRange.startDate || col(dateColumn) > processingDateRange.endDate)
        .cache()

      if (historicalData.count() == 0) {
        logger.info("✓ No historical data found outside processing range")
        return
      }

      // Validate that historical data structure matches current data structure
      val existingSchema = existingData.schema
      val currentSchema = currentData.schema

      if (existingSchema != currentSchema) {
        logger.warn(s"Schema mismatch detected between existing and current data. This may be expected for schema evolution.")
        logger.debug(s"Existing schema: ${existingSchema}")
        logger.debug(s"Current schema: ${currentSchema}")
      }

      logger.info("✓ History integrity validation passed - historical data structure validated")

      // Log processing scope
      val processingDates = currentData.select(col(dateColumn)).distinct().collect().map(_.getDate(0).toLocalDate).sorted
      val existingDates = existingData.select(col(dateColumn)).distinct().collect().map(_.getDate(0).toLocalDate).sorted

      logger.info(s"✓ Processing dates: ${processingDates.mkString(", ")}")
      logger.info(s"✓ Existing dates in output: ${existingDates.mkString(", ")}")
      logger.info(s"✓ Processing will overwrite only requested date range: ${processingDateRange.startDate} to ${processingDateRange.endDate}")

      historicalData.unpersist()

    } catch {
      case e: Exception =>
        logger.error(s"✗ History integrity validation failed: ${e.getMessage}")
        throw new IllegalStateException(s"History integrity validation failed: ${e.getMessage}", e)
    }
  }

  /**
    * Additional validation method to ensure data quality and consistency
    * Validates that the processing only affects the intended date partitions
    *
    * @param outputData Final output dataset
    * @param expectedDateRange Expected date range for processing
    * @param dateColumn Name of the date column
    * @param spark Implicit SparkSession
    */
  def validateProcessingScopeIntegrity[T](
    outputData: Dataset[T],
    expectedDateRange: LocalDateRange,
    dateColumn: String = "month_end_date"
  )(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val actualDates = outputData
      .select(col(dateColumn))
      .distinct()
      .collect()
      .map(_.getDate(0).toLocalDate)
      .sorted

    val expectedDates = expectedDateRange.toSeq.toSet
    val actualDatesSet = actualDates.toSet

    // Check if there are any unexpected dates in the output
    val unexpectedDates = actualDatesSet -- expectedDates
    if (unexpectedDates.nonEmpty) {
      logger.warn(s"⚠ Unexpected dates found in output: ${unexpectedDates.mkString(", ")}")
    }

    // Check if any expected dates are missing
    val missingDates = expectedDates -- actualDatesSet
    if (missingDates.nonEmpty) {
      logger.warn(s"⚠ Expected dates missing from output: ${missingDates.mkString(", ")}")
    }

    if (unexpectedDates.isEmpty && missingDates.isEmpty) {
      logger.info("✓ Processing scope validation passed - output contains exactly the expected date range")
    }
  }


  /**
    * Perform initial decuplication on the datasource union to prevent double-counting Element IDs, and to prevent
    * downstream deduplication from violating guard rails
    * @param endOfMonthDay fiscal month cutoff day to determine which FME a date should be grouped into
    * @param data the union results to be deduplicated
    * @param spark
    * @return
    */
  def preliminaryDeduplication(endOfMonthDay: Int)(data: Dataset[BroadbandPlatformAggregateWithId])(implicit spark: SparkSession): Dataset[BroadbandPlatformAggregateWithId] = {
    import spark.implicits._

    // Identify the fiscal month associated with a the_date partition
    val dataWithFme = data
      .withColumn("month_end_date",
        when(dayofmonth($"the_date") > endOfMonthDay, date_add(last_day($"the_date"), endOfMonthDay))
          .otherwise(to_date(concat_ws("-", year($"the_date"), month($"the_date"), lit(endOfMonthDay)), "yyyy-M-d")))
      .cache()

    // Identify duplicates -- more than 1 win & 1 loss in a given fme (cnt > 2) or multiple dates in a fme (cnt distinct > 1)
    val dupeStats = dataWithFme
      .groupBy($"month_end_date", $"element_id")
      .agg(
        count($"the_date").as("the_date_count"),
        countDistinct($"the_date").as("distinct_the_date_count")
      )
      .filter($"the_date_count" > 2 || $"distinct_the_date_count" > 1)

    // Remove duplicates entirely
    dataWithFme
      .join(broadcast(dupeStats), Seq("month_end_date", "element_id"), "left_anti")
      .drop($"month_end_date")
      .as[BroadbandPlatformAggregateWithId]
  }


  def applyAttGuardRail(allWins: Dataset[PreparedBbcvOutputWins], allLosses: Dataset[PreparedBbcvOutputLosses], targets: Dataset[GuardRail], endOfMonthDay: Option[Int])(agg: Dataset[SyndicatedPlatformAggregate25])(implicit spark: SparkSession): Dataset[SyndicatedPlatformAggregate25] = {
    import spark.implicits._

    val fmeUDF = udf((date: LocalDate) => getFmeDateFromChurnDate(date, endOfMonthDay))

    val attWithFme = agg.withColumn("fme", fmeUDF($"the_date"))

    val nationalAtt = attWithFme
      .where($"primary_sp_group" === 6746)
      .groupBy("fme").agg(sum($"adjusted_wins").as("att_wins"), sum($"adjusted_losses").as("att_losses"))

    val nationalWL = allWins.groupBy("month_end_date").agg(countDistinct($"ifa").as("national_wins"))
      .join(allLosses.groupBy($"month_end_date").agg(countDistinct($"ifa").as("national_losses")), Seq("month_end_date"), "inner")

    val attTargets = targets.where($"sp_id" === 6746)
      .withColumn("fme", fmeUDF($"min_date"))
      .withColumn("target", ($"lower_bound" + $"upper_bound") / 2.0)
      .withColumn("win_target", when($"w_or_l" === "w", $"target").otherwise(null))
      .withColumn("loss_target", when($"w_or_l" === "l", $"target").otherwise(null))
      .groupBy($"fme").agg(sum($"win_target").as("win_target"), sum($"loss_target").as("loss_target"))

    val multiplier = nationalAtt.as("a")
      .join(nationalWL.as("b"), $"a.fme" === $"b.month_end_date", "inner")
      .join(attTargets.as("c"), $"a.fme" === $"c.fme", "inner")
      .withColumn("win_multiplier", ($"win_target" * $"national_wins") / ($"att_wins" - ($"att_wins" * $"win_target")))
      .withColumn("loss_multiplier", ($"loss_target" * $"national_losses") / ($"att_losses" - ($"att_losses" * $"loss_target")))
      .select($"a.fme".as("the_date"), $"win_multiplier", $"loss_multiplier")

    val adjResult = attWithFme.join(multiplier.as("b"), $"fme" === $"b.the_date", "left")
      .withColumn("adjusted_wins", when($"primary_sp_group" === 6746, $"adjusted_wins" * $"win_multiplier").otherwise($"adjusted_wins" * $"loss_multiplier"))
      .withColumn("adjusted_losses", when($"primary_sp_group" === 6746, $"adjusted_losses" * $"loss_multiplier").otherwise($"adjusted_losses" * $"win_multiplier"))

    val result = adjResult
    .drop($"win_multiplier")
    .drop($"loss_multiplier")
    .drop($"b.the_date")
    .drop($"fme")
    .select(SyndicatedPlatformAggregate25.cols: _*)
    .as[SyndicatedPlatformAggregate25]

    result
  }

  /**
    * Performs initial pre-filtering on the primary and secondary sp_groups to remove undesired values.
    * Filter out rows where primary=secondary (self-churn)
    * Filter out rows where primary=0
    * Filter out rows where secondary=0
    *
    * @param input dataset to be filtered on the primary & secondary sp groups
    * @return
    */
  def preliminarySpFiltering[T <: SpGroupFields : Encoder](input: Dataset[T])(implicit spark: SparkSession): Dataset[T] = {
    import spark.implicits._
    input
      .filter((row: T) =>
        row.primary_sp_group != row.secondary_sp_group &&
          row.primary_sp_group != 0 &&
          row.secondary_sp_group != 0
      )
  }


  /**
    * Performs initial pre-filtering on the primary and secondary sp_groups to remove undesired values.
    * Filter out rows where primary=secondary (self-churn)
    * Filter out rows where primary=0
    * Filter out rows where secondary=0
    *
    * @param input dataset to be filtered on the primary & secondary sp groups
    * @return
    */
  def preliminarySpFiltering25(input: Dataset[SyndicatedPlatformAggregate25]): Dataset[SyndicatedPlatformAggregate25] = {
    input
      .filter(row =>
        row.primary_sp_group != row.secondary_sp_group &&
          row.primary_sp_group != 0 &&
          row.secondary_sp_group != 0
      )
  }

  /**
    * Filter the input dataset based on the result of calling CensusBlockToMasterBlockLookup.isCensusBlockInFootprint for
    * the row's census_blockid field
    *
    * @param input             dataset to be filtered
    * @param masterBlockLookup implicit CensusBlockToMasterBlockLookup to use for performing the lookup call
    * @return
    */
  def inFootprintFiltering[T <: GeoIdFields: Encoder](input: Dataset[T])(implicit masterBlockLookup25: CensusBlockToMasterBlockLookup25, spark: SparkSession): Dataset[T] = {
    masterBlockLookup25.filterCensusBlocksInFootprint(input)
  }


  /**
    * Perform additional filtering on the primary SP to ensure that the primary SP is in-territory. Defined as:
    * primary_sp_group is NOT in the service territory sp lookup
    * OR the primary_sp_group-census_block pair is present in the service territory sp lookup
    * @param input dataset to be filtered
    * @param svtLookup implicit service territory lookup object for performing the in-territory queries
    * @return
    */
  def primarySpInTerritory[T <: SpGroupFields : Encoder](input: Dataset[T])(implicit svtLookup: ServiceTerritoryLookup, spark: SparkSession): Dataset[T] = {
    val geoCol = input.limit(1).collect().head.primaryGeoIdField
    svtLookup.filterInTerritory(input, "primary_sp_group", geoCol)
  }


  /**
    * Perform additional filtering on the secondary SP to ensure that the secondary SP is in-territory. Defined as:
    * secondary_sp_group is NOT in the service territory sp lookup
    * OR the secondary_sp_group-secondary_census_block pair is present in the service territory sp lookup
    * @param input dataset to be filtered
    * @param svtLookup implicit service territory lookup object for performing the in-territory queries
    * @return
    */
  def secondarySpInTerritory[T <: SpGroupFields : Encoder](input: Dataset[T])(implicit svtLookup: ServiceTerritoryLookup, spark: SparkSession): Dataset[T] = {
    import spark.implicits._
    val geoCol = input.limit(1).collect().head.secondaryGeoIdField
    svtLookup.filterInTerritory(input, "secondary_sp_group", geoCol)
  }

  /**
    * Filter input into one of Provider Wins/Losses or Non-Provider Wins/Losses based on SP filtering rules dependend on
    * which subset is being filtered
    * @param providerSpList List of valid SPs for the provider (e.g. Array(1009) for Comcast
    * @param outputType enum to distinguish between provider win/loss and non-provider win/loss
    * @param input table to be filtered according to the corresponding rules
    * @return
    */
  def parsePreparedBbcvSubset(providerSpList: Array[Int], outputType: PreparedBbcvOutputTypes.Value)(input: Dataset[WrappedPlatformAggregateWithMasterBlock]): Dataset[WrappedPlatformAggregateWithMasterBlock] = {
    input
      .filter { row =>
        outputType match {
          case PreparedBbcvOutputTypes.ProviderWins =>
            providerSpList.contains(row.platform_aggregate.primary_sp_group) && row.platform_aggregate.adjusted_wins > 0.00
          case PreparedBbcvOutputTypes.ProviderLosses =>
            providerSpList.contains(row.platform_aggregate.primary_sp_group) && row.platform_aggregate.adjusted_losses > 0.00
          case PreparedBbcvOutputTypes.NonProviderWins =>
            !providerSpList.contains(row.platform_aggregate.primary_sp_group) && row.platform_aggregate.adjusted_wins > 0.00
          case PreparedBbcvOutputTypes.NonProviderLosses =>
            !providerSpList.contains(row.platform_aggregate.primary_sp_group) && row.platform_aggregate.adjusted_losses > 0.00
        }
      }
  }

  /**
    * Filter input into one of Provider Wins/Losses or Non-Provider Wins/Losses based on SP filtering rules dependend on
    * which subset is being filtered
    *
    * @param providerSpList List of valid SPs for the provider (e.g. Array(1009) for Comcast
    * @param outputType     enum to distinguish between provider win/loss and non-provider win/loss
    * @param input          table to be filtered according to the corresponding rules
    * @return
    */
  def parsePreparedBbcv25Subset(providerSpList: Array[Int], outputType: PreparedBbcvOutputTypes.Value)(input: Dataset[WrappedPlatformAggregate25WithMasterBlock]): Dataset[WrappedPlatformAggregate25WithMasterBlock] = {
    input
      .filter { row =>
        outputType match {
          case PreparedBbcvOutputTypes.ProviderWins =>
            providerSpList.contains(row.platform_aggregate.primary_sp_group) && row.platform_aggregate.adjusted_wins > 0.00
          case PreparedBbcvOutputTypes.ProviderLosses =>
            providerSpList.contains(row.platform_aggregate.primary_sp_group) && row.platform_aggregate.adjusted_losses > 0.00
          case PreparedBbcvOutputTypes.NonProviderWins =>
            !providerSpList.contains(row.platform_aggregate.primary_sp_group) && row.platform_aggregate.adjusted_wins > 0.00
          case PreparedBbcvOutputTypes.NonProviderLosses =>
            !providerSpList.contains(row.platform_aggregate.primary_sp_group) && row.platform_aggregate.adjusted_losses > 0.00
        }
      }
  }

  /**
    * Run Deduplication on the final output table by removing all records which have more than one churn_date for the
    * same IFA in a single month_end_date
    * @param input source table to be deduplicated
    * @param spark
    * @return
    */
  def takeDuplicatesOut[T <: PreparedBbcvOutputCommons : Encoder](input: Dataset[T])(implicit spark: SparkSession): Dataset[T] = {
    import spark.implicits._

    // All IFA, month_end_date pairs which need to be removed
    val duplicates = input
      .groupBy($"ifa", $"month_end_date")
      .agg(count($"churn_date") as "churn_date_count")
      .filter($"churn_date_count" > 1 )
      .persist
    println("Duplicates:")
    println(duplicates.count())

    // remove the duplicates via left-anti join
    input
      .join(broadcast(duplicates), Seq("ifa", "month_end_date"), "left_anti")
      .as[T]
  }

  /**
    * Wrapper method to perform the necessary parsing and output unwrapping for the specific PreparedBbcvOutput type
    * @param providerSpList List of valid SPs for the current provider being parsed for (e.g. Array(1009) for Comcast)
    * @param endOfMonthDay day indicating the end of the month to be used for mapping churn date to fiscal month end date
    * @param outputType enum indicating which of the four output types to be parsed
    * @param input dataset to be parsed into the desired output table
    * @param spark
    * @return
    */
  def prepareOutput[T <: PreparedBbcvOutputCommons : Encoder](
    providerSpList: Array[Int],
    endOfMonthDay: Option[Int],
    processingDate: LocalDate,
    outputType: PreparedBbcvOutputTypes.Value
  )(input: Dataset[WrappedPlatformAggregateWithMasterBlock])(implicit spark: SparkSession): Dataset[T] = {

    println("Starting for Non AT&T FWA")

    input
      .transform(parsePreparedBbcvSubset(providerSpList, outputType))
      .transform(WrappedPlatformAggregateWithMasterBlock.unwrapToPreparedBbcvOutput[T](outputType, endOfMonthDay, processingDate))
      .transform(takeDuplicatesOut[T])
  }

  /**
    * Wrapper method to perform the necessary parsing and output unwrapping for the specific PreparedBbcvOutput type
    *
    * @param providerSpList List of valid SPs for the current provider being parsed for (e.g. Array(1009) for Comcast)
    * @param endOfMonthDay  day indicating the end of the month to be used for mapping churn date to fiscal month end date
    * @param outputType     enum indicating which of the four output types to be parsed
    * @param input          dataset to be parsed into the desired output table
    * @param spark
    * @return
    */
  def prepareAttFwaOutput[T <: PreparedBbcvOutputCommons : Encoder](
    providerSpList: Array[Int],
    endOfMonthDay: Option[Int],
    processingDate: LocalDate,
    outputType: PreparedBbcvOutputTypes.Value
  )(input: Dataset[WrappedPlatformAggregate25WithMasterBlock])(implicit spark: SparkSession): Dataset[T] = {
    input
      .transform(parsePreparedBbcv25Subset(providerSpList, outputType))
      .transform(WrappedPlatformAggregate25WithMasterBlock.unwrapToPreparedBbcvOutput[T](outputType, endOfMonthDay, processingDate))
  }


  /**
    * Wrapper method for writing the PreparedBbcvOutput table to S3 with appropriate partitioning
    * @param data dataset to be written to s3
    * @param destination S3 destination URI to write the data
    */
  def writeOutputToS3[T <: PreparedBbcvOutputCommons : Encoder](data: Dataset[T], destination: URI, numParts: Option[Int])(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    data
      .repartition(numParts.getOrElse(1), $"month_end_date", $"processing_date")
      .write
      .mode(SaveMode.Overwrite)
      .partitionBy("month_end_date", "processing_date")
      .parquet(destination.toString)
  }

}

object BbcvDataPreparationJob extends SparkJob(BbcvDataPreparationJobRunner)
