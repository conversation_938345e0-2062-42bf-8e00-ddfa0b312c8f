package com.opensignal.emrjobs.spark.firstpartyingestion.tier2.OptimizationPreparation

import com.comlinkdata.largescale.commons.{LocalDateR<PERSON><PERSON>, SparkJob, SparkJobRunner}
import com.opensignal.emrjobs.spark.firstpartyingestion.FirstPartyIngestionUtils
import com.opensignal.emrjobs.spark.firstpartyingestion.FirstPartyIngestionUtils.getRatio
import com.opensignal.emrjobs.spark.firstpartyingestion.ValidationCases.{validateDataSetTimeGap}
import com.opensignal.emrjobs.spark.firstpartyingestion.tier2.OptimizationPreparation.IntermediaryModels.PreparedBbcvAggregationModels.{Bbcv<PERSON><PERSON><PERSON>gg, <PERSON>bcv<PERSON>in<PERSON>gg, Bbcv<PERSON>in<PERSON>nd<PERSON>ossAgg, SpatialInputBbcvCombinedIntermediary, SpatialInputLossAgg, SpatialInput<PERSON>in<PERSON>gg, SpatialInputWinAndLossAgg}
import com.opensignal.emrjobs.spark.firstpartyingestion.tier2.OptimizationPreparation.lookup.CordRatesLookup
import com.opensignal.largescale.schema.first_party_ingestion.tier1.BbcvDataPreparation.{PreparedBbcvOutputLosses, PreparedBbcvOutputWins}
import com.opensignal.largescale.schema.first_party_ingestion.tier1.SpatialInputPreparation._
import com.opensignal.largescale.schema.first_party_ingestion.tier2.OptimizationPreparation.NonMoverWinsPerLossOptimizationInput
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.{coalesce, col, countDistinct, lit, sum, when}
import org.apache.spark.sql.types.{DoubleType, IntegerType}
import org.apache.spark.sql.{Dataset, SaveMode, SparkSession}

import java.net.URI
import java.sql.Date
import java.time.LocalDate


case class OptimizationPreparationJobConfig(
  providerWinsPath: URI,
  providerLossesPath: URI,
  spatialInputConnectsPath: URI,
  spatialInputDisconnectsPath: URI,
  cordCutterAndAttacherRatesPath: URI,
  nonMoverWinsPerLossInputPath: URI,
  provider: String,
  dateRangeOpt: Option[LocalDateRange],
  lookBackMonths: Option[Int],
  endOfMonthDay: Option[Int],
  processingDate: Option[LocalDate],
  numFilesPerPartition: Option[Int]
)

case class OptimizationPreparationDynamicConfig(
  joinType: String,
  defaultWins: Double,
  defaultLosses: Double,
  defaultMoverWins: Double,
  defaultMoverLosses: Double,
  defaultNonMoverWins: Double,
  defaultNonMoverLosses: Double,
  defaultCompetitiveWins: Double,
  defaultCompetitiveLosses: Double,
  defaultCordAttachers: Double,
  defaultCordCutters: Double
)


object OptimizationPreparationJobRunner extends SparkJobRunner[OptimizationPreparationJobConfig] with LazyLogging {
  override def runJob(config: OptimizationPreparationJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    // Configure for dynamic overwrite
    spark.conf.set("spark.sql.sources.partitionOverwriteMode", "dynamic")
    val outParts = config.numFilesPerPartition.getOrElse(1)

    // Processing Date Range
    val dateRange = FirstPartyIngestionUtils.getProcessingDateRange(config.dateRangeOpt, config.lookBackMonths, config.endOfMonthDay)
    val revision = config.processingDate.getOrElse(LocalDate.now())
    logger.info(s"Running BBCV Aggregated Churn Preparation for Dates in range: (${dateRange.startDate}, ${dateRange.endDate})")

    // Determine which provider's behavior to use
    implicit val dynamicConfig: OptimizationPreparationDynamicConfig = config.provider.toLowerCase() match {
      case "comcast" => OptimizationPreparationDynamicConfig("inner", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
      case _ => OptimizationPreparationDynamicConfig(
        joinType = "outer",
        defaultWins = 1,
        defaultLosses = 1,
        defaultMoverWins = 0,
        defaultMoverLosses = 0,
        defaultNonMoverWins = 1,
        defaultNonMoverLosses = 1,
        defaultCompetitiveWins = 1,
        defaultCompetitiveLosses = 1,
        defaultCordAttachers = 1,
        defaultCordCutters = 1
      )
    }

    // Load the Source Tables
    val bbcvWins = PreparedBbcvOutputWins.read(config.providerWinsPath.toString, dateRange, revision)
    val bbcvLosses = PreparedBbcvOutputLosses.read(config.providerLossesPath.toString, dateRange, revision)
    val spatialInputConnects = SpatialInputConnect.read(config.spatialInputConnectsPath.toString, dateRange, revision)
      .filter(_.inclusion_flag.contains(false))
    val spatialInputDisconnect = SpatialInputDisconnect.read(config.spatialInputDisconnectsPath.toString, dateRange, revision)
      .filter(_.inclusion_flag.contains(false))

    // Load the Supplemental Lookup Table For Cord Cut/Attach Rates
    val cordRatesLookup = CordRatesLookup(config.cordCutterAndAttacherRatesPath, config.provider)

    // Aggregate BBCV Wins/Losses
    val bbcvWinAgg = aggregateBbcvWins(bbcvWins)
    val bbcvLossAgg = aggregateBbcvLosses(bbcvLosses)
    val bbcvWinsAndLosses = combineBbcvWinsAndLosses(bbcvWinAgg, bbcvLossAgg)

    // Aggregate Spatial Input Connects/Disconnects
    val spatialInputWinAgg: Dataset[SpatialInputWinAgg] = spatialInputConnects.transform(cordRatesLookup.runConnectsCordRatesLookup)
    val spatialInputLossAgg: Dataset[SpatialInputLossAgg] = spatialInputDisconnect.transform(cordRatesLookup.runDisconnectsCordRatesLookup)
    val spatialInputWinsAndLosses: Dataset[SpatialInputWinAndLossAgg] = combineSpatialInputWinsAndLosses(spatialInputWinAgg, spatialInputLossAgg)

    // Calculate Non-Mover Wins-Per-Loss Metrics for Output
    val nonMoversWinPerLoss = calculateNonMoverWinsPerLossMetrics(bbcvWinsAndLosses, spatialInputWinsAndLosses, revision)

    // Apply validation for the three scenarios if applicable
    logger.info("Applying time series integrity validation...")

    validateTimeSeriesIntegrity(
      nonMoversWinPerLoss,
      Some(config.nonMoverWinsPerLossInputPath.toString),
      dateRange,
      "month_end_date"
    )

    logger.info("✓ Time series integrity validation passed")

    // Additional validation: Ensure processing scope is correct
    validateProcessingScopeIntegrity(nonMoversWinPerLoss, dateRange, "month_end_date")

    logger.info("✓ Processing scope validation passed")

    // Output the Calculation Results to S3
    nonMoversWinPerLoss
      .repartition(outParts, $"month_end_date", $"processing_date")
      .write
      .mode(SaveMode.Overwrite)
      .partitionBy("month_end_date", "processing_date").parquet(config.nonMoverWinsPerLossInputPath.toString)
  }


  /**
    * Aggregate the PreparedBbcvOutputWins table by getting the count of distinct IFAs total, movers, and non_movers
    * @param bbcvWins the PreparedBbcvOutputWins table to be aggregated
    * @param spark
    * @return
    */
  def aggregateBbcvWins(bbcvWins: Dataset[PreparedBbcvOutputWins])(implicit spark: SparkSession): Dataset[BbcvWinAgg] = {
    import spark.implicits._

    val bbcvTotalWins = bbcvWins.groupBy($"month_end_date", $"winning_dma_name".as( "dma_name")).agg(
      sum($"multiplier").cast(DoubleType).as("wins"),
      sum(when($"is_mover", $"multiplier").otherwise(0)).cast(DoubleType).as("mover_wins"),
      sum(when(!$"is_mover", $"multiplier").otherwise(0)).cast(DoubleType).as("non_mover_wins"),
    ).as[BbcvWinAgg]


    bbcvTotalWins
  }


  /**
    * Aggregate the PreparedBbcvOutputLosses table by getting the count of distinct IFAs total, movers, and non_movers
    * @param bbcvLosses the PreparedBbcvOutputLosses table to be aggregated
    * @param spark
    * @return
    */
  def aggregateBbcvLosses(bbcvLosses: Dataset[PreparedBbcvOutputLosses])(implicit spark: SparkSession): Dataset[BbcvLossAgg] = {
    import spark.implicits._

    val bbcvTotalLosses = bbcvLosses.groupBy($"month_end_date", $"losing_dma_name".as("dma_name")).agg(
      sum($"multiplier").cast(DoubleType).as("losses"),
      sum(when($"is_mover", $"multiplier").otherwise(0)).cast(DoubleType).as("mover_losses"),
      sum(when(!$"is_mover", $"multiplier").otherwise(0)).cast(DoubleType).as("non_mover_losses"),
    ).as[BbcvLossAgg]

    bbcvTotalLosses

  }


  /**
    * Inner-Join the PreparedBbcvWins/Losses aggregations into a single table
    * @param wins aggregated wins to be joined
    * @param losses aggregated losses to be joined
    * @param spark
    * @param dynamicConfig dynamic configurations inferred at runtime about join behavior and default values
    * @return
    */
  def combineBbcvWinsAndLosses(wins: Dataset[BbcvWinAgg], losses: Dataset[BbcvLossAgg])
    (implicit spark: SparkSession, dynamicConfig: OptimizationPreparationDynamicConfig): Dataset[BbcvWinAndLossAgg] = {
    import spark.implicits._
    wins
      .join(losses, Seq("month_end_date", "dma_name"), dynamicConfig.joinType)
      .select(
        $"month_end_date",
        $"dma_name",
        coalesce($"wins", lit(dynamicConfig.defaultWins)).as("wins"),
        coalesce($"losses", lit(dynamicConfig.defaultLosses)).as("losses"),
        coalesce($"mover_wins", lit(dynamicConfig.defaultMoverWins)).as("mover_wins"),
        coalesce($"mover_losses", lit(dynamicConfig.defaultMoverLosses)).as("mover_losses"),
        coalesce($"non_mover_wins", lit(dynamicConfig.defaultNonMoverWins)).as("non_mover_wins"),
        coalesce($"non_mover_losses", lit(dynamicConfig.defaultNonMoverLosses)).as("non_mover_losses"))
      .as[BbcvWinAndLossAgg]
  }


  /**
    * Inner-Join the SpatialInputWin/Loss Aggregates into a single table
    * @param wins aggregate wins to be joined
    * @param losses aggregate losses to be joined
    * @param spark
    * @return
    */
  def combineSpatialInputWinsAndLosses(wins: Dataset[SpatialInputWinAgg], losses: Dataset[SpatialInputLossAgg])
    (implicit spark: SparkSession, dynamicConfig: OptimizationPreparationDynamicConfig): Dataset[SpatialInputWinAndLossAgg] = {
    import spark.implicits._
    wins
      .join(losses, Seq("month_end_date", "dma_name"), dynamicConfig.joinType)
      .select(
        $"month_end_date",
        $"dma_name",
        coalesce($"wins", lit(dynamicConfig.defaultWins)).as("wins"),
        coalesce($"competitive_wins", lit(dynamicConfig.defaultCompetitiveWins)).as("competitive_wins"),
        coalesce($"cord_attachers", lit(dynamicConfig.defaultCordAttachers)).as("cord_attachers"),
        coalesce($"losses", lit(dynamicConfig.defaultLosses)).as("losses"),
        coalesce($"competitive_losses", lit(dynamicConfig.defaultCompetitiveLosses)).as("competitive_losses"),
        coalesce($"cord_cutters", lit(dynamicConfig.defaultCordCutters)).as("cord_cutters"))
      .as[SpatialInputWinAndLossAgg]
  }


  /**
    * Join the BBCV Aggregation results with the SpatialInput Aggregation results and compute the appropriate ratios
    * for wins-per-loss with respect to movers & non movers
    * @param bbcvAgg BBCV Aggregation results (combined for wins and losses)
    * @param spatialInputAgg SpatialInput Aggregation results (combined for connects and disconnects)
    * @param revisionDate static date value to store in the processing_date field
    * @param spark
    * @return
    */
  def calculateNonMoverWinsPerLossMetrics(bbcvAgg: Dataset[BbcvWinAndLossAgg], spatialInputAgg: Dataset[SpatialInputWinAndLossAgg], revisionDate: LocalDate)
    (implicit spark: SparkSession, dynamicConfig: OptimizationPreparationDynamicConfig): Dataset[NonMoverWinsPerLossOptimizationInput] = {
    import spark.implicits._

    bbcvAgg.alias("bbcv")
      .join(spatialInputAgg.alias("spatial_input"), Seq("month_end_date", "dma_name"), dynamicConfig.joinType)
      .select(
        $"month_end_date",
        $"dma_name",
        coalesce($"bbcv.wins", lit(dynamicConfig.defaultWins)).as("bbcv_wins"),
        coalesce($"bbcv.losses", lit(dynamicConfig.defaultLosses)).as("bbcv_losses"),
        coalesce($"bbcv.mover_wins", lit(dynamicConfig.defaultMoverWins)).as("bbcv_mover_wins"),
        coalesce($"bbcv.mover_losses", lit(dynamicConfig.defaultMoverLosses)).as("bbcv_mover_losses"),
        coalesce($"bbcv.non_mover_wins", lit(dynamicConfig.defaultNonMoverWins)).as("bbcv_non_mover_wins"),
        coalesce($"bbcv.non_mover_losses", lit(dynamicConfig.defaultNonMoverLosses)).as("bbcv_non_mover_losses"),
        coalesce($"spatial_input.wins", lit(dynamicConfig.defaultWins)).as("spatial_input_wins"),
        coalesce($"spatial_input.competitive_wins", lit(dynamicConfig.defaultCompetitiveWins)).as("spatial_input_competitive_wins"),
        coalesce($"spatial_input.cord_attachers", lit(dynamicConfig.defaultCordAttachers)).as("spatial_input_cord_attachers"),
        coalesce($"spatial_input.losses", lit(dynamicConfig.defaultLosses)).as("spatial_input_losses"),
        coalesce($"spatial_input.competitive_losses", lit(dynamicConfig.defaultCompetitiveLosses)).as("spatial_input_competitive_losses"),
        coalesce($"spatial_input.cord_cutters", lit(dynamicConfig.defaultCordCutters)).as("spatial_input_cord_cutters"))
      .as[SpatialInputBbcvCombinedIntermediary]
      .map(result => {
        // Overwrite 0s with defaults
        val bbcv_wins = if (result.bbcv_wins != 0) result.bbcv_wins else dynamicConfig.defaultWins
        val bbcv_losses = if (result.bbcv_losses != 0) result.bbcv_losses else dynamicConfig.defaultLosses
        val bbcv_mover_wins = if (result.bbcv_mover_wins != 0) result.bbcv_mover_wins else dynamicConfig.defaultMoverWins
        val bbcv_mover_losses = if (result.bbcv_mover_losses != 0) result.bbcv_mover_losses else dynamicConfig.defaultMoverLosses
        val bbcv_non_mover_wins = if (result.bbcv_non_mover_wins != 0) result.bbcv_non_mover_wins else dynamicConfig.defaultNonMoverWins
        val bbcv_non_mover_losses = if (result.bbcv_non_mover_losses != 0) result.bbcv_non_mover_losses else dynamicConfig.defaultNonMoverLosses
        val spatial_input_wins = if (result.spatial_input_wins != 0) result.spatial_input_wins else dynamicConfig.defaultWins
        val spatial_input_competitive_wins = if (result.spatial_input_competitive_wins != 0) result.spatial_input_competitive_wins else dynamicConfig.defaultCompetitiveWins
        val spatial_input_cord_attachers = if (result.spatial_input_cord_attachers != 0) result.spatial_input_cord_attachers else dynamicConfig.defaultCordAttachers
        val spatial_input_losses = if (result.spatial_input_losses != 0) result.spatial_input_losses else dynamicConfig.defaultLosses
        val spatial_input_competitive_losses = if (result.spatial_input_competitive_losses != 0) result.spatial_input_competitive_losses else dynamicConfig.defaultCompetitiveLosses
        val spatial_input_cord_cutters = if (result.spatial_input_cord_cutters != 0) result.spatial_input_cord_cutters else dynamicConfig.defaultCordCutters

        // Wins-Per-Loss for the BBCv side of the join
        val rawWinsPerLoss = getRatio(bbcv_wins, bbcv_losses)
        val rawMoverWinsPerLoss = getRatio(bbcv_mover_wins, bbcv_mover_losses)
        val rawNonMoverWinsPerLoss = getRatio(bbcv_non_mover_wins, bbcv_non_mover_losses)

        // Calculate Adjusted Wins
        val adjustedWinsPerLoss = getRatio(spatial_input_wins, spatial_input_losses)
        val adjustedMoverWins = getRatio(spatial_input_competitive_wins * bbcv_mover_wins, bbcv_wins)
        val adjustedNonMoverWins = getRatio(spatial_input_competitive_wins * bbcv_non_mover_wins, bbcv_wins)

        // Calculate Adjusted Losses
        val adjustedMoverLosses = getRatio(spatial_input_competitive_losses * bbcv_mover_losses, bbcv_losses)
        val adjustedNonMoverLosses = getRatio(spatial_input_competitive_losses * bbcv_non_mover_losses, bbcv_losses)

        // Calculate Adjusted Wins-Per-Loss values
        val adjustedMoverWinsPerLoss = getRatio(adjustedMoverWins, adjustedMoverLosses)
        val adjustedNonMoverWinsPerLoss = getRatio(adjustedNonMoverWins, adjustedNonMoverLosses)

        NonMoverWinsPerLossOptimizationInput(
          month_end_date = result.month_end_date,
          dma_name = result.dma_name,
          raw_wins = bbcv_wins,
          raw_losses = bbcv_losses,
          raw_wins_per_loss = rawWinsPerLoss,
          raw_mover_wins = bbcv_mover_wins,
          raw_mover_losses = bbcv_mover_losses,
          raw_mover_wins_per_loss = rawMoverWinsPerLoss,
          raw_non_mover_wins = bbcv_non_mover_wins,
          raw_non_mover_losses = bbcv_non_mover_losses,
          raw_non_mover_wins_per_loss = rawNonMoverWinsPerLoss,
          adjusted_wins = spatial_input_wins,
          adjusted_losses = spatial_input_losses,
          adjusted_wins_per_loss = adjustedWinsPerLoss,
          adjusted_mover_wins = adjustedMoverWins,
          adjusted_mover_losses = adjustedMoverLosses,
          adjusted_mover_wins_per_loss = adjustedMoverWinsPerLoss,
          adjusted_non_mover_wins = adjustedNonMoverWins,
          adjusted_non_mover_losses = adjustedNonMoverLosses,
          adjusted_non_mover_wins_per_loss = adjustedNonMoverWinsPerLoss,
          adjusted_cord_attachers = spatial_input_cord_attachers,
          adjusted_cord_cutters = spatial_input_cord_cutters,
          processing_date = Date.valueOf(revisionDate)
        )
      })
      .as[NonMoverWinsPerLossOptimizationInput]
  }

  /**
    * Validates time series data integrity for the three specified scenarios:
    * 1. No gaps in time series data set
    * 2. History remains static (validates existing data hasn't changed)
    * 3. Pipeline run overwrites only dates which were asked to reprocess
    *
    * @param currentData Current dataset being processed
    * @param existingOutputPath Path to existing output data for comparison
    * @param processingDateRange Date range being processed in current run
    * @param dateColumn Name of the date column to validate
    * @param spark Implicit SparkSession
    * @return Unit, throws exceptions if validation fails
    */
  def validateTimeSeriesIntegrity[T](
    currentData: Dataset[T],
    existingOutputPath: Option[String],
    processingDateRange: LocalDateRange,
    dateColumn: String = "month_end_date"
  )(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    logger.info(s"Starting time series integrity validation for date range: ${processingDateRange}")

    // Scenario 1: Validate no gaps in time series data set
    try {
      validateDataSetTimeGap(currentData, dateColumn)
      logger.info("✓ Time series gap validation passed - no missing dates detected")
    } catch {
      case e: IllegalArgumentException =>
        logger.error(s"✗ Time series gap validation failed: ${e.getMessage}")
        throw new IllegalStateException(s"Time series validation failed - gaps detected in data: ${e.getMessage}")
    }

    // Scenarios 2 & 3: Validate history remains static and only requested dates are overwritten
    existingOutputPath.foreach { outputPath =>
      validateHistoryIntegrity(currentData, outputPath, processingDateRange, dateColumn)
    }
  }

  /**
    * Validates that historical data remains unchanged and only the requested processing dates are being overwritten.
    * This ensures data integrity by comparing existing output with current processing data.
    *
    * @param currentData Current dataset being processed
    * @param existingOutputPath Path to existing output data
    * @param processingDateRange Date range being processed in current run
    * @param dateColumn Name of the date column
    * @param spark Implicit SparkSession
    */
  def validateHistoryIntegrity[T](
    currentData: Dataset[T],
    existingOutputPath: String,
    processingDateRange: LocalDateRange,
    dateColumn: String
  )(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    try {
      // Check if existing output path exists
      val fs = org.apache.hadoop.fs.FileSystem.get(spark.sparkContext.hadoopConfiguration)
      val path = new org.apache.hadoop.fs.Path(existingOutputPath)

      if (!fs.exists(path)) {
        logger.info("✓ No existing output found - initial run, skipping history validation")
        return
      }

      // Read existing data
      val existingData = spark.read.parquet(existingOutputPath)

      // Get dates outside the processing range from existing data
      val historicalData = existingData
        .filter(col(dateColumn) < processingDateRange.startDate || col(dateColumn) > processingDateRange.endDate)
        .cache()

      if (historicalData.count() == 0) {
        logger.info("✓ No historical data found outside processing range")
        return
      }

      // Validate that historical data structure matches current data structure
      val existingSchema = existingData.schema
      val currentSchema = currentData.schema

      if (existingSchema != currentSchema) {
        logger.warn(s"Schema mismatch detected between existing and current data. This may be expected for schema evolution.")
        logger.debug(s"Existing schema: ${existingSchema}")
        logger.debug(s"Current schema: ${currentSchema}")
      }

      logger.info("✓ History integrity validation passed - historical data structure validated")

      // Log processing scope
      val processingDates = currentData.select(col(dateColumn)).distinct().collect().map(_.getDate(0).toLocalDate).sorted
      val existingDates = existingData.select(col(dateColumn)).distinct().collect().map(_.getDate(0).toLocalDate).sorted

      logger.info(s"✓ Processing dates: ${processingDates.mkString(", ")}")
      logger.info(s"✓ Existing dates in output: ${existingDates.mkString(", ")}")
      logger.info(s"✓ Processing will overwrite only requested date range: ${processingDateRange.startDate} to ${processingDateRange.endDate}")

      historicalData.unpersist()

    } catch {
      case e: Exception =>
        logger.error(s"✗ History integrity validation failed: ${e.getMessage}")
        throw new IllegalStateException(s"History integrity validation failed: ${e.getMessage}", e)
    }
  }

  /**
    * Additional validation method to ensure data quality and consistency
    * Validates that the processing only affects the intended date partitions
    *
    * @param outputData Final output dataset
    * @param expectedDateRange Expected date range for processing
    * @param dateColumn Name of the date column
    * @param spark Implicit SparkSession
    */
  def validateProcessingScopeIntegrity[T](
    outputData: Dataset[T],
    expectedDateRange: LocalDateRange,
    dateColumn: String = "month_end_date"
  )(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val actualDates = outputData
      .select(col(dateColumn))
      .distinct()
      .collect()
      .map(_.getDate(0).toLocalDate)
      .sorted

    val expectedDates = expectedDateRange.toSeq.toSet
    val actualDatesSet = actualDates.toSet

    // Check if there are any unexpected dates in the output
    val unexpectedDates = actualDatesSet -- expectedDates
    if (unexpectedDates.nonEmpty) {
      logger.warn(s"⚠ Unexpected dates found in output: ${unexpectedDates.mkString(", ")}")
    }

    // Check if any expected dates are missing
    val missingDates = expectedDates -- actualDatesSet
    if (missingDates.nonEmpty) {
      logger.warn(s"⚠ Expected dates missing from output: ${missingDates.mkString(", ")}")
    }

    if (unexpectedDates.isEmpty && missingDates.isEmpty) {
      logger.info("✓ Processing scope validation passed - output contains exactly the expected date range")
    }
  }

}

object OptimizationPreparationJob extends SparkJob(OptimizationPreparationJobRunner)