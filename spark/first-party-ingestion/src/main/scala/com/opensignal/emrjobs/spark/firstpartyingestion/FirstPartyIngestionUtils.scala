package com.opensignal.emrjobs.spark.firstpartyingestion

import com.comlinkdata.largescale.commons.LocalDateRange
import com.typesafe.scalalogging.LazyLogging

import java.time.LocalDate
import java.time.temporal.TemporalAdjusters
import scala.annotation.tailrec


object FirstPartyIngestionUtils extends LazyLogging {

  object PreparedBbcvOutputTypes extends Enumeration {
    type PreparedBbcvOutputTypes = Value
    val ProviderWins, ProviderLosses, NonProviderWins, NonProviderLosses = Value
  }

  /**
    * Get the Current Fiscal Month End Date (day of the month corresponding to the most recent monthly partition expected)
    * If the optional asOf is provided, use that as the max date to consider
    *
    * @param asOf the maximum date to consider as "current" -- defaults to the current date if not provided
    * @param endOfMonthDay optional int to represent the specific day of the month to use as partition. If none is provided,
    *                      use the value 0 (which maps to using the end-of-month date)
    * @return the breatest month end partition
    */
  @tailrec def getCurrentFiscalMonthEndDate(asOf: LocalDate = LocalDate.now, endOfMonthDay: Option[Int] = None): LocalDate = {
    // Create the LocalDate representing the month end date for the month of asOf
    val day = endOfMonthDay.getOrElse(0)
    val monthEnd = {
      if (day >= 1) LocalDate.of(asOf.getYear, asOf.getMonthValue, day)
      else asOf.`with`(TemporalAdjusters.lastDayOfMonth())
    }

    // If the expected end is after asOf, use call again with asOf the last day of the previous month instead
    if (asOf.isBefore(monthEnd))
      getCurrentFiscalMonthEndDate(
        asOf = asOf.minusMonths(1).`with`(TemporalAdjusters.lastDayOfMonth()),
        endOfMonthDay = endOfMonthDay
      )
    else monthEnd
  }


  /**
    * Given a Churn date, return the next FiscalMonthEndDate to occur after that date
    * @param churnDate date to map to its corresponding FME date
    * @param endOfMonthDay day of month to act as the cutoff for a fiscal month
    * @return
    */
  def getFmeDateFromChurnDate(churnDate: LocalDate = LocalDate.now, endOfMonthDay: Option[Int] = None): LocalDate = {
    val cutoffDay = endOfMonthDay.getOrElse(churnDate.lengthOfMonth)
    if (churnDate.getDayOfMonth <= cutoffDay) LocalDate.of(churnDate.getYear, churnDate.getMonthValue, cutoffDay)
    else {
      val nextMonth = churnDate.plusMonths(1)
      LocalDate.of(nextMonth.getYear, nextMonth.getMonthValue, cutoffDay)
    }
  }

  /**
    * Validates that the two provided LocalDate values represent the same calendar-day. If they don't throw an Illegal
    * State Exception identifying that the dates mismatch
    * @param expected LocalDate object containing the expected date value
    * @param available LocalDate object containing the available date value
    */
  def validateMatchingDatesOrThrow(expected: LocalDate, available: LocalDate): Unit = {
    if (!expected.equals(available)) {
      throw new IllegalStateException(s"Date Value Mismatch Between Expected End Date (${expected}) and available Input End Date (${available})")
    }
  }

  /**
    * Given an end date and the number of lookback months to include, create a LocalDateRange object
    * or throw an IllegalArgumentException if there are no lookBackMonths provided
    * @param end intended end date of the LocalDateRange
    * @param lookBackMonths inclusive number of months to include in the date range
    * @param endOfMonthDay if a fixed end date is not provided, use this value to mark the end of month day to use in date
    *                      range generation
    * @return
    */
  def createLocalDateRangeOrThrow(end: LocalDate, lookBackMonths: Option[Int] = None, endOfMonthDay: Option[Int] = None): LocalDateRange = {
    val start = end.minusMonths(lookBackMonths.getOrElse({
      throw new IllegalArgumentException("loook_back_months is required config when no date_range_opt is provided")
    })-1) // 1 less than provided value to ensure range encompasses expected # of months
    if (endOfMonthDay.isDefined)
      LocalDateRange(getCurrentFiscalMonthEndDate(start, endOfMonthDay), getCurrentFiscalMonthEndDate(end, endOfMonthDay))
    else
      LocalDateRange(getCurrentFiscalMonthEndDate(start), getCurrentFiscalMonthEndDate(end))
  }

  /**
    * Create a LocalDateRange object for the expected processing date range
    * @param dateRangeOpt optional LocalDateRange. If provided, return as-is, otherwise calculate the range
    * @param lookBackMonths optional number of months to consider for range calculation. required when no dateRangeOpt
    * @param endOfMonthDay optional integer representing the day of the month to be used as month-end partitions
    * @return
    */
  def getProcessingDateRange(
    dateRangeOpt: Option[LocalDateRange],
    lookBackMonths: Option[Int],
    endOfMonthDay: Option[Int] = None
  ): LocalDateRange = {

    // If dateRangeOpt is provided, override everything, and just return as-is
    dateRangeOpt.getOrElse {
      val expectedEndDate = getCurrentFiscalMonthEndDate(endOfMonthDay=endOfMonthDay)
      createLocalDateRangeOrThrow(expectedEndDate, lookBackMonths, endOfMonthDay)
    }
  }

  /**
    * Given a localDateRange, extract just the month end date for each
    * @param dateRange date range to be filtered to only desired partition dates
    * @param endOfMonthDay optional int to represent the day of month for partition, if none use end of calendar month
    * @return
    */
  def getMonthlyPartitionDateList(dateRange: LocalDateRange, endOfMonthDay: Option[Int] = None): Seq[LocalDate] = {
    endOfMonthDay match {
      case Some(day: Int) => dateRange.toSeq.filter(d => d.getDayOfMonth == day)
      case None => dateRange.toSeq.filter(d => d.getDayOfMonth == d.`with`(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth)
    }
  }

  /**
    * Given a census block ID, return the substring for the county if the given census block is long enough, otherwise
    * return None
    * @param censusBlock census block ID to derive the county for
    * @return
    */
  def getCountyIfValid(censusBlock: String): Option[String] = {
    if (censusBlock.length >= 5) Some(censusBlock.substring(0, 5))
    else None
  }

  /**
    * Given a census block ID, return the substring for the census tract if the given census block is long enough,
    * otherwise return None
    * @param censusBlock census block ID to derive the census tract for
    * @return
    */
  def getTractIfValid(censusBlock: String): Option[String] = {
    if (censusBlock.length >= 11) Some(censusBlock.substring(0, 11))
    else None
  }

  /**
    * method for computing the ratio (numerator / denominator) with a default of 0.0 when the denominator is <= 0
    * @param numerator numerator for division (could be Int or Double)
    * @param denominator denominator for division (could be Int or Double
    * @return
    */
  def getRatio(numerator: Double, denominator: Double): Double = if (denominator > 0) numerator / denominator else 0.0
  def getRatio(numerator: Double, denominator: Int): Double = if (denominator > 0) numerator / denominator else 0.0
  def getRatio(numerator: Int, denominator: Int): Double = getRatio(numerator.toDouble, denominator)
  def getRatio(numerator: Int, denominator: Double): Double = getRatio(numerator.toDouble, denominator)

  def validateDateRange(dateRange: LocalDateRange): Unit = {
    if (dateRange.startDate.isBefore(LocalDate.of(2024,1,21))) {
      throw new IllegalArgumentException("Cannot process frozen data before 2024-01-21")
    }
  }
}
