package com.opensignal.emrjobs.spark.firstpartyingestion.tier2.AdjustmentFactors

import com.comlinkdata.largescale.commons.LocalDateRange
import com.opensignal.emrjobs.spark.firstpartyingestion.ValidationCases._
import org.apache.spark.sql.{Dataset, SparkSession}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

import java.time.LocalDate

class UpdateAdjustmentFactorsJobValidationSpec extends AnyFlatSpec with Matchers {

  implicit val spark: SparkSession = SparkSession.builder()
    .appName("UpdateAdjustmentFactorsJobValidationSpec")
    .master("local[*]")
    .config("spark.sql.adaptive.enabled", "false")
    .getOrCreate()

  import spark.implicits._

  "validateDataSetTimeGap" should "pass when no gaps exist in adjustment factors time series data" in {
    // Create test data with consecutive monthly dates
    val testData = Seq(
      ("2024-01-31", "DMA1", 1.2, 0.8, 0.15, 0.25, "2024-02-01"),
      ("2024-02-29", "DMA1", 1.1, 0.9, 0.18, 0.22, "2024-02-01"),
      ("2024-03-31", "DMA1", 1.3, 0.7, 0.20, 0.28, "2024-02-01")
    ).toDF("month_end_date", "dma_name", "win_volume_multiplier", "loss_volume_multiplier", 
           "cord_attacher_pct", "cord_cutter_pct", "processing_date")
      .withColumn("month_end_date", $"month_end_date".cast("date"))
      .withColumn("processing_date", $"processing_date".cast("date"))

    // Should not throw exception
    noException should be thrownBy {
      validateDataSetTimeGap(testData, "month_end_date")
    }
  }

  "validateDataSetTimeGap" should "fail when gaps exist in adjustment factors time series data" in {
    // Create test data with missing month (gap)
    val testData = Seq(
      ("2024-01-31", "DMA1", 1.2, 0.8, 0.15, 0.25, "2024-02-01"),
      ("2024-03-31", "DMA1", 1.3, 0.7, 0.20, 0.28, "2024-02-01") // Missing February
    ).toDF("month_end_date", "dma_name", "win_volume_multiplier", "loss_volume_multiplier", 
           "cord_attacher_pct", "cord_cutter_pct", "processing_date")
      .withColumn("month_end_date", $"month_end_date".cast("date"))
      .withColumn("processing_date", $"processing_date".cast("date"))

    // Should throw IllegalArgumentException due to gap
    an[IllegalArgumentException] should be thrownBy {
      validateDataSetTimeGap(testData, "month_end_date")
    }
  }

  "validateProcessingScopeIntegrity" should "pass when adjustment factors contain exactly expected dates" in {
    val testData = Seq(
      ("2024-01-31", "DMA1", 1.2, 0.8, 0.15, 0.25, "2024-02-01"),
      ("2024-02-29", "DMA1", 1.1, 0.9, 0.18, 0.22, "2024-02-01"),
      ("2024-03-31", "DMA1", 1.3, 0.7, 0.20, 0.28, "2024-02-01")
    ).toDF("month_end_date", "dma_name", "win_volume_multiplier", "loss_volume_multiplier", 
           "cord_attacher_pct", "cord_cutter_pct", "processing_date")
      .withColumn("month_end_date", $"month_end_date".cast("date"))
      .withColumn("processing_date", $"processing_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 31), LocalDate.of(2024, 3, 31))

    // Should not throw exception and log success
    noException should be thrownBy {
      validateProcessingScopeIntegrity(testData, dateRange, "month_end_date")
    }
  }

  "validateProcessingScopeIntegrity" should "warn when unexpected dates are present in adjustment factors" in {
    val testData = Seq(
      ("2024-01-31", "DMA1", 1.2, 0.8, 0.15, 0.25, "2024-02-01"),
      ("2024-02-29", "DMA1", 1.1, 0.9, 0.18, 0.22, "2024-02-01"),
      ("2024-03-31", "DMA1", 1.3, 0.7, 0.20, 0.28, "2024-02-01"),
      ("2024-04-30", "DMA1", 1.4, 0.6, 0.22, 0.30, "2024-02-01") // Unexpected date
    ).toDF("month_end_date", "dma_name", "win_volume_multiplier", "loss_volume_multiplier", 
           "cord_attacher_pct", "cord_cutter_pct", "processing_date")
      .withColumn("month_end_date", $"month_end_date".cast("date"))
      .withColumn("processing_date", $"processing_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 31), LocalDate.of(2024, 3, 31))

    // Should not throw exception but will log warning
    noException should be thrownBy {
      validateProcessingScopeIntegrity(testData, dateRange, "month_end_date")
    }
  }

  "validateProcessingScopeIntegrity" should "warn when expected dates are missing from adjustment factors" in {
    val testData = Seq(
      ("2024-01-31", "DMA1", 1.2, 0.8, 0.15, 0.25, "2024-02-01"),
      ("2024-02-29", "DMA1", 1.1, 0.9, 0.18, 0.22, "2024-02-01")
      // Missing March 2024
    ).toDF("month_end_date", "dma_name", "win_volume_multiplier", "loss_volume_multiplier", 
           "cord_attacher_pct", "cord_cutter_pct", "processing_date")
      .withColumn("month_end_date", $"month_end_date".cast("date"))
      .withColumn("processing_date", $"processing_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 31), LocalDate.of(2024, 3, 31))

    // Should not throw exception but will log warning
    noException should be thrownBy {
      validateProcessingScopeIntegrity(testData, dateRange, "month_end_date")
    }
  }

  "UpdateAdjustmentFactorsJob validation" should "handle multiple DMAs correctly" in {
    val testData = Seq(
      ("2024-01-31", "DMA1", 1.2, 0.8, 0.15, 0.25, "2024-02-01"),
      ("2024-01-31", "DMA2", 1.1, 0.9, 0.18, 0.22, "2024-02-01"),
      ("2024-02-29", "DMA1", 1.3, 0.7, 0.20, 0.28, "2024-02-01"),
      ("2024-02-29", "DMA2", 1.4, 0.6, 0.22, 0.30, "2024-02-01")
    ).toDF("month_end_date", "dma_name", "win_volume_multiplier", "loss_volume_multiplier", 
           "cord_attacher_pct", "cord_cutter_pct", "processing_date")
      .withColumn("month_end_date", $"month_end_date".cast("date"))
      .withColumn("processing_date", $"processing_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 31), LocalDate.of(2024, 2, 29))

    // Should pass validation with multiple DMAs
    noException should be thrownBy {
      validateDataSetTimeGap(testData, "month_end_date")
      validateProcessingScopeIntegrity(testData, dateRange, "month_end_date")
    }
  }

  override def afterAll(): Unit = {
    spark.stop()
    super.afterAll()
  }
}
