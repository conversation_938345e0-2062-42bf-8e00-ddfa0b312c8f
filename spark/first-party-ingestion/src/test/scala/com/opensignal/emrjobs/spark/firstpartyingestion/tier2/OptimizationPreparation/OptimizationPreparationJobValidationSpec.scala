package com.opensignal.emrjobs.spark.firstpartyingestion.tier2.OptimizationPreparation

import com.comlinkdata.largescale.commons.LocalDateRange
import com.opensignal.emrjobs.spark.firstpartyingestion.tier2.OptimizationPreparation.OptimizationPreparationJobRunner._
import org.apache.spark.sql.{Dataset, SparkSession}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

import java.time.LocalDate

class OptimizationPreparationJobValidationSpec extends AnyFlatSpec with Matchers {

  implicit val spark: SparkSession = SparkSession.builder()
    .appName("OptimizationPreparationJobValidationSpec")
    .master("local[*]")
    .config("spark.sql.adaptive.enabled", "false")
    .getOrCreate()

  import spark.implicits._

  "validateTimeSeriesIntegrity" should "pass when no gaps exist in time series data" in {
    // Create test data with consecutive dates
    val testData = Seq(
      ("2024-01-21", "DMA1", 10.0, 5.0),
      ("2024-01-22", "DMA1", 12.0, 6.0),
      ("2024-01-23", "DMA1", 8.0, 4.0)
    ).toDF("month_end_date", "dma_name", "raw_wins", "raw_losses")
      .withColumn("month_end_date", $"month_end_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 21), LocalDate.of(2024, 1, 23))

    // Should not throw exception
    noException should be thrownBy {
      validateTimeSeriesIntegrity(testData, None, dateRange, "month_end_date")
    }
  }

  "validateTimeSeriesIntegrity" should "fail when gaps exist in time series data" in {
    // Create test data with missing date (gap)
    val testData = Seq(
      ("2024-01-21", "DMA1", 10.0, 5.0),
      ("2024-01-23", "DMA1", 8.0, 4.0) // Missing 2024-01-22
    ).toDF("month_end_date", "dma_name", "raw_wins", "raw_losses")
      .withColumn("month_end_date", $"month_end_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 21), LocalDate.of(2024, 1, 23))

    // Should throw IllegalStateException due to gap
    an[IllegalStateException] should be thrownBy {
      validateTimeSeriesIntegrity(testData, None, dateRange, "month_end_date")
    }
  }

  "validateProcessingScopeIntegrity" should "pass when output contains exactly expected dates" in {
    val testData = Seq(
      ("2024-01-21", "DMA1", 10.0, 5.0),
      ("2024-01-22", "DMA1", 12.0, 6.0),
      ("2024-01-23", "DMA1", 8.0, 4.0)
    ).toDF("month_end_date", "dma_name", "raw_wins", "raw_losses")
      .withColumn("month_end_date", $"month_end_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 21), LocalDate.of(2024, 1, 23))

    // Should not throw exception and log success
    noException should be thrownBy {
      validateProcessingScopeIntegrity(testData, dateRange, "month_end_date")
    }
  }

  "validateProcessingScopeIntegrity" should "warn when unexpected dates are present" in {
    val testData = Seq(
      ("2024-01-21", "DMA1", 10.0, 5.0),
      ("2024-01-22", "DMA1", 12.0, 6.0),
      ("2024-01-23", "DMA1", 8.0, 4.0),
      ("2024-01-24", "DMA1", 9.0, 3.0) // Unexpected date
    ).toDF("month_end_date", "dma_name", "raw_wins", "raw_losses")
      .withColumn("month_end_date", $"month_end_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 21), LocalDate.of(2024, 1, 23))

    // Should not throw exception but will log warning
    noException should be thrownBy {
      validateProcessingScopeIntegrity(testData, dateRange, "month_end_date")
    }
  }

  "validateProcessingScopeIntegrity" should "warn when expected dates are missing" in {
    val testData = Seq(
      ("2024-01-21", "DMA1", 10.0, 5.0),
      ("2024-01-22", "DMA1", 12.0, 6.0)
      // Missing 2024-01-23
    ).toDF("month_end_date", "dma_name", "raw_wins", "raw_losses")
      .withColumn("month_end_date", $"month_end_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 21), LocalDate.of(2024, 1, 23))

    // Should not throw exception but will log warning
    noException should be thrownBy {
      validateProcessingScopeIntegrity(testData, dateRange, "month_end_date")
    }
  }

  override def afterAll(): Unit = {
    spark.stop()
    super.afterAll()
  }
}
