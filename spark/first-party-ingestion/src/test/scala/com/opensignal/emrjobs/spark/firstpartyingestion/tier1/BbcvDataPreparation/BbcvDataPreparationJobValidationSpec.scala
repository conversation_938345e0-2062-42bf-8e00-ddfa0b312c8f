package com.opensignal.emrjobs.spark.firstpartyingestion.tier1.BbcvDataPreparation

import com.comlinkdata.largescale.commons.LocalDateRange
import com.opensignal.emrjobs.spark.firstpartyingestion.tier1.BbcvDataPreparation.BbcvDataPreparationJobRunner._
import org.apache.spark.sql.{Dataset, SparkSession}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

import java.time.LocalDate

class BbcvDataPreparationJobValidationSpec extends AnyFlatSpec with Matchers {

  implicit val spark: SparkSession = SparkSession.builder()
    .appName("BbcvDataPreparationJobValidationSpec")
    .master("local[*]")
    .config("spark.sql.adaptive.enabled", "false")
    .getOrCreate()

  import spark.implicits._

  "validateTimeSeriesIntegrity" should "pass when no gaps exist in time series data" in {
    // Create test data with consecutive dates
    val testData = Seq(
      ("2024-01-21", "data1"),
      ("2024-01-22", "data2"),
      ("2024-01-23", "data3")
    ).toDF("the_date", "value")
      .withColumn("the_date", $"the_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 21), LocalDate.of(2024, 1, 23))

    // Should not throw exception
    noException should be thrownBy {
      validateTimeSeriesIntegrity(testData, None, dateRange, "the_date")
    }
  }

  "validateTimeSeriesIntegrity" should "fail when gaps exist in time series data" in {
    // Create test data with missing date (gap)
    val testData = Seq(
      ("2024-01-21", "data1"),
      ("2024-01-23", "data3") // Missing 2024-01-22
    ).toDF("the_date", "value")
      .withColumn("the_date", $"the_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 21), LocalDate.of(2024, 1, 23))

    // Should throw IllegalStateException due to gap
    an[IllegalStateException] should be thrownBy {
      validateTimeSeriesIntegrity(testData, None, dateRange, "the_date")
    }
  }

  "validateProcessingScopeIntegrity" should "pass when output contains exactly expected dates" in {
    val testData = Seq(
      ("2024-01-21", "data1"),
      ("2024-01-22", "data2"),
      ("2024-01-23", "data3")
    ).toDF("month_end_date", "value")
      .withColumn("month_end_date", $"month_end_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 21), LocalDate.of(2024, 1, 23))

    // Should not throw exception and log success
    noException should be thrownBy {
      validateProcessingScopeIntegrity(testData, dateRange, "month_end_date")
    }
  }

  "validateProcessingScopeIntegrity" should "warn when unexpected dates are present" in {
    val testData = Seq(
      ("2024-01-21", "data1"),
      ("2024-01-22", "data2"),
      ("2024-01-23", "data3"),
      ("2024-01-24", "data4") // Unexpected date
    ).toDF("month_end_date", "value")
      .withColumn("month_end_date", $"month_end_date".cast("date"))

    val dateRange = LocalDateRange(LocalDate.of(2024, 1, 21), LocalDate.of(2024, 1, 23))

    // Should not throw exception but will log warning
    noException should be thrownBy {
      validateProcessingScopeIntegrity(testData, dateRange, "month_end_date")
    }
  }

  override def afterAll(): Unit = {
    spark.stop()
    super.afterAll()
  }
}
