package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs

import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON>, Spark<PERSON><PERSON><PERSON><PERSON><PERSON>, TimeSeriesLocation}
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierAggRollup
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierAggRollup.implicits._
import com.typesafe.scalalogging.LazyLogging
import java.net.URI
import java.time.LocalDate
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SaveMode, SparkSession}

case class SubscribersConfig(
  fixedWirelessSubsPath: String,
  popstatsPath: URI,
  fixedWirelessSpidsPath: URI,
  hhBlockGroupPath: String,
  penetrationPath: URI,
  householdsPath: URI,
  aggCarrierRollupsPath: String,
  spatialAllocationPath: URI,
  satelliteMultiplier: Double,
  carrierMultipliers: Map[String, Double],
  resultPath: URI,
  date: LocalDate
)

object Subscribers<PERSON>ob extends SparkJob(SubscribersRunner)

object SubscribersRunner extends SparkJobRunner[SubscribersConfig] with LazyLogging {
  override def runJob(config: SubscribersConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    val fixed_wireless_subs_tutela = spark.read.parquet(config.fixedWirelessSubsPath)
      .select(
        'carrier,
        'block,
        'num_networks,
        when(length('block) === 14, concat(lit("0"), 'block.substr(1, 11))).otherwise('block.substr(1, 12)).as("geoid"))
    val popstatsTsl = TimeSeriesLocation.ofDatePartitions(config.popstatsPath).build
    val geoidCensusPopstatsLookup = getGeoidCensusPopStatsLookup(popstatsTsl.partition(popstatsTsl.latestInputPartition))
    val spidsTsl = TimeSeriesLocation.ofDatePartitions(config.fixedWirelessSpidsPath).build
    val spidsPath = spidsTsl.partition(spidsTsl.latestInputPartition)
    val fixed_wireless_spids = spark.read.parquet(spidsPath)
      .withColumnRenamed("tutela_carrier", "carrier")
    val householdBlockGroups = getHouseholdBlockGroups(config.hhBlockGroupPath)
    val penetrationPath = TimeSeriesLocation.ofDatePartitions(config.penetrationPath).build.partition(config.date)
    val bb_penetration = spark.read.parquet(penetrationPath)
      .select(
        'block,
        'block.substr(1, 12).as("serv_terr_bgid"),
        'block_bb_total.cast("double").as("block_bb_total"))
      .filter(coalesce('block_bb_total, lit(0.0)) > 0.0)
    val householdsPath = TimeSeriesLocation.ofDatePartitions(config.householdsPath).build.partition(config.date)
    logger.info(s"Reading households input from $householdsPath")
    val households = spark.read.parquet(householdsPath)
      .filter('sp_platform.isin("6130", "6140", "6710"))
      .select(
        'census_block_id.substr(1, 5).as("county"),
        'census_block_id.substr(1, 2).as("state"),
        'sp_platform.cast("integer").as("sp_platform"),
        'temp_household_id)
    val rollup = CarrierAggRollup.read(URI.create(config.aggCarrierRollupsPath))
    val saPath = TimeSeriesLocation.ofDatePartitions(config.spatialAllocationPath).build.partition(config.date)
    val spatial_allocation = spark.read.parquet(saPath)
      .select(
        'sp_platform.cast("integer").as("sp_platform"),
        'census_block_id.as("limit_to_census_blockids"),
        'destination_census_block_id.as("census_block_id"),
        'weight_allocated.as("subscribers"))
    val resultPath = TimeSeriesLocation.ofDatePartitions(config.resultPath).build.validOutputPartitionOrThrow(config.date)
    val multipliers = config.carrierMultipliers.toList match {
      case (id, value) :: tail =>
        tail
          .foldLeft(when('sp_platform === id.toInt, value)) {
            case (col, (id, value)) => col.when('sp_platform === id.toInt, value)
          }.otherwise(lit(1.0))
      case _ => lit(1.0)
    }
    val fwa = fixed_wireless_subs_tutela
      .join(geoidCensusPopstatsLookup, Seq("geoid"), "left")
      .join(fixed_wireless_spids, Seq("carrier"), "left")
      .filter(!coalesce('num_networks, lit("")).isin("0", ""))
      .select(
        'carrier,
        'sp_id.as("sp_platform"),
        when('serv_terr_bgid.isNotNull, concat('serv_terr_bgid, 'block.substr(13, 3)))
          .when(length('block) === 14, concat(lit("0"), 'block))
          .otherwise('block).as("census_block_id"),
        'num_networks)
      .groupBy('carrier, 'sp_platform, 'census_block_id)
      .agg(sum('num_networks.cast("double") * multipliers).as("subscribers"))
      .select(
        'census_block_id.as("census_blockid"),
        'sp_platform,
        'subscribers)
    val bbms_final_output = spatial_allocation
      .transform(rollup.combineCarriers('sp_platform, 'limit_to_census_blockids))
      .select(
        'census_block_id.as("census_blockid"),
        'sp_platform,
        'subscribers)
    val satellite_bg = getSatellite(householdBlockGroups, geoidCensusPopstatsLookup)
    val satellite_census_block = bb_penetration
      .join(satellite_bg, Seq("serv_terr_bgid"), "left")
      .select(
        'block.as("census_block_id"),
        'block.substr(1, 5).as("county"),
        'block.substr(1, 2).as("state"),
        ('block_bb_total * 'sat_pct_5y_2019 * config.satelliteMultiplier).as("satellite_subs"))
    val sat_share_county = households
      .groupBy('county, 'sp_platform)
      .agg(countDistinct('temp_household_id).as("cnt"))
      .select(
        'county,
        'sp_platform.as("county_sp_platform"),
        ('cnt * 1.0 / sum('cnt).over(Window.partitionBy('county))).as("county_share"))
    val sat_share_state = households
      .groupBy('state, 'sp_platform)
      .agg(countDistinct('temp_household_id).as("cnt"))
      .select(
        'state,
        'sp_platform.as("state_sp_platform"),
        ('cnt * 1.0 / sum('cnt).over(Window.partitionBy('state))).as("state_share"))
    val satellite_final = satellite_census_block
      .join(sat_share_county, "county")
      .join(sat_share_state, "state")
      .filter('county_sp_platform === 'state_sp_platform)
      .select(
        'census_block_id.as("census_blockid"),
        coalesce('county_sp_platform, 'state_sp_platform).as("sp_platform"),
        ('satellite_subs * coalesce('county_share, 'state_share, lit(0.0))).as("subscribers"))
      .filter('sp_platform.isin(6130, 6140, 6710) && 'subscribers > 0.0)
    val result = bbms_final_output.union(fwa).union(satellite_final)
      .groupBy('census_blockid.substr(1, 15).as("census_blockid"), 'sp_platform)
      .agg(sum('subscribers).as("subscribers"))
    logger.info(s"Writing results to $resultPath")
    result.repartition(25).write.mode(SaveMode.ErrorIfExists).parquet(resultPath)
  }
}
