CREATE TABLE carvonia_pii.charter_bbmv_udp_output_for_redshift_2023q2
WITH (
    external_location = 's3://d000-pii-xray-comlinkdata-com/prod/private/broadband-market-share/comcast_bbmv_udp_output_for_redshift_2023q2/v=1.0.0/',
    partitioned_by = ARRAY['month_end_date']
) AS
-- s3://d000-pii-carvonia-comlinkdata-com/prod/reference-tables/bbmv-censusblock-division-region-dma-all-quarters/month_end_date=2023-06-28/
WITH first_party_footprint AS (
    -- block: string, cbg: string, hsd_hp: long, hsd_subs: long
    SELECT censusblock AS block, SUBSTR(censusblock, 1, 12) AS cbg, hsd_hp, hsd_subs
    FROM carvonia_pii.charter_bbmv_subs
    WHERE month_end_date = DATE('2023-06-28')
)
, satellite_cb AS (
    -- s3://c400-athena-dev-comlinkdata-com/paulina_sandbox/BBpen_model_2023/census_block_0912_v2/quarter=2023q2/
    WITH penetration AS (
        -- block: string, cbg: string, block_total: double, block_bb_total: double
        SELECT block, SUBSTR(block, 1, 12) AS cbg, block_total, block_bb_total
        FROM acs_bb.bbpen_model_2023_block_0912_v2
        WHERE quarter = '2023q2'
    )
    , penetration_block AS (
        SELECT
            block,
            block_total,
            CASE WHEN block_total = 0.0 THEN 0.0 ELSE block_bb_total / block_total END AS block_bb_penetration
        FROM penetration
    )
    , penetration_cbg AS (
        SELECT
            cbg,
            CASE WHEN SUM(block_total) = 0.0 THEN 0.0 ELSE SUM(block_bb_total) / SUM(block_total) END AS cbg_bb_penetration
        FROM penetration
        GROUP BY cbg
    )
    , penetration_adjusted_first_party_footprint AS (
        -- block: string, cbg: string, hsd_hp: long, hsd_subs: double, bb_hsd_hp: double
        SELECT
            block,
            cbg,
            CASE WHEN hsd_subs > hsd_hp THEN hsd_subs ELSE hsd_hp END AS hsd_hp,
            CAST((CASE WHEN hsd_subs > hsd_hp THEN hsd_subs ELSE hsd_hp END) AS DOUBLE) *
                (CASE
                    WHEN hsd_subs > hsd_hp THEN 1.0
                    WHEN block_total = 0.0 THEN cbg_bb_penetration
                    ELSE block_bb_penetration
                END)
            AS bb_hsd_hp,
            CAST(hsd_subs AS DOUBLE) AS hsd_subs
        FROM first_party_footprint
        LEFT JOIN penetration_block USING (block)
        LEFT JOIN penetration_cbg USING (cbg)
    )
    , penetration_adjusted_first_party_footprint_less_first_party_subs AS (
        -- block: string, cbg: string, block_total: long, block_bb_total: double, block_bb_total_first_party: double
        SELECT
            block,
            cbg,
            hsd_hp AS block_total,
            CASE WHEN hsd_subs > bb_hsd_hp THEN hsd_subs ELSE bb_hsd_hp END AS block_bb_total_first_party,
            CASE WHEN hsd_subs > bb_hsd_hp THEN 0.0 ELSE bb_hsd_hp - hsd_subs END AS block_bb_total
        FROM penetration_adjusted_first_party_footprint
    )
    -- s3://c400-athena-dev-comlinkdata-com/census/acs_hh_hu/block group/2020_onward/2020_geo/acs_bb/hh_hu_cbg_acs5_2020_onward/
    , satellite_prep AS (
        -- geoid: string, B28002_004: int, B28002_006: int, B28002_010: int
        SELECT
            geoid AS serv_terr_bgid,
            CASE WHEN uniqueid = 'B28002_004' THEN estimate ELSE 0 END AS B28002_004,
            CASE WHEN uniqueid = 'B28002_006' THEN estimate ELSE 0 END AS B28002_006,
            CASE WHEN uniqueid = 'B28002_010' THEN estimate ELSE 0 END AS B28002_010
        FROM acs_bb.hh_hu_cbg_acs5_2020_onward
        WHERE year = '2021'
        AND acs = '5_year'
    )
    , satellite AS (
        -- cbg: string, sat_pct: double
        SELECT
            serv_terr_bgid AS cbg,
            CASE
                WHEN SUM(B28002_004) - SUM(B28002_006) = 0 THEN 0.0
                ELSE (CAST(SUM(B28002_010) AS DOUBLE) / CAST(SUM(B28002_004) - SUM(B28002_006) AS DOUBLE))
            END AS sat_pct
        FROM satellite_prep
        GROUP BY serv_terr_bgid
    )
    -- block: string, cbg: string, block_total: long, block_bb_total: double, sat_subs: double
    SELECT
        block,
        cbg,
        SUBSTR(block, 1, 5) AS county,
        SUBSTR(block, 1, 2) AS state,
        block_total,
        block_bb_total,
        sat_pct * block_bb_total_first_party AS sat_subs
    FROM penetration_adjusted_first_party_footprint_less_first_party_subs
    LEFT JOIN satellite USING (cbg)
)
, fwa_by_carrier AS (
    -- s3://d000-comlinkdata-com/prod/private/cellular-fixed-wireless/quarterly-fixed-wireless-subs-output-cb2020/year=2023/month=06/day=15/
    WITH fwa_source AS (
        -- carrier: string, num_networks: long, block: string
        SELECT carrier, num_networks, SUBSTR(block, 1, 15) AS block
        FROM c300_production_aux_cellularfixedwireless.quarterly_fixed_wireless_subs_output_cb2020
        WHERE year||'-'||month||'-'||day = '2023-06-15'
    )
    , fwa_multiplier_denominator AS (
        -- carrier: string, observed_fwa_subs: double
        SELECT carrier, SUM(CAST(num_networks AS DOUBLE)) AS observed_fwa_subs
        FROM fwa_source
        GROUP BY carrier
    )
    -- s3://d000-comlinkdata-com/prod/private/broadband-market-share/industry-reported-numbers/v=1.0.2/date=2022-07-01/
    , fwa_multiplier_numerator AS (
        -- carrier: string, reported_fwa_subs: double
        SELECT 'Verizon' AS carrier, CAST(verizon_fwa AS DOUBLE) as reported_fwa_subs
        FROM acs_bb.industry_reported_numbers
        WHERE verizon_fwa <> ''
        AND quarter_end_date = '7/1/23'
        UNION ALL
        SELECT 'T-Mobile' AS carrier, CAST(tmobile_fwa AS DOUBLE) as reported_fwa_subs
        FROM acs_bb.industry_reported_numbers
        WHERE tmobile_fwa <> ''
        AND quarter_end_date = '7/1/23'
        UNION ALL
        SELECT 'US Cellular' AS carrier, CAST(uscc_fwa AS DOUBLE) as reported_fwa_subs
        FROM acs_bb.industry_reported_numbers
        WHERE uscc_fwa <> ''
        AND quarter_end_date = '7/1/23'
    )
    , fwa_multipliers AS (
        -- carrier: string, fwa_multiplier: double
        SELECT carrier, reported_fwa_subs * 1000.0 / observed_fwa_subs AS fwa_multiplier
        FROM fwa_multiplier_denominator
        JOIN fwa_multiplier_numerator USING (carrier)
    )
    -- s3://c400-athena-dev-comlinkdata-com/paulina_sandbox/fixed_wireless_spids/
    -- block: string, sp_platform: int, fwa_subs: double
    SELECT block, sp_id AS sp_platform, SUM(num_networks * COALESCE(fwa_multiplier, 1.0)) AS fwa_subs
    FROM fwa_source
    LEFT JOIN fwa_multipliers USING (carrier)
    JOIN acs_bb.fixed_wireless_spids sp ON carrier = tutela_carrier
    WHERE COALESCE(num_networks, 0) <> 0
    GROUP BY block, sp_id
)
, satellite_final AS (
    -- s3://d000-comlinkdata-com/prod/private/broadband-customer-base/i-final-customer-base-aggregate/date=2022-04-01/
    WITH bbcv_aggregate AS (
        -- county: string, state: string, sp_platform: string, customer_base: double
        SELECT
            SUBSTR(census_block, 1, 5) AS county,
            SUBSTR(census_block, 1, 2) AS state,
            sp_platform,
            CAST(customer_base AS DOUBLE) AS customer_base
        FROM broadband_customer_base_prod.step_i_final_customer_base_aggregate
        WHERE date = DATE('2022-04-01')
        AND sp_platform IN ('6130', '6140', '6710')
    )
    , sat_share_county AS (
        -- county: string, county_sp_platform: string, county_share: double
        SELECT
            county,
            sp_platform AS county_sp_platform,
            sp_cld_subs_count / total_cld_subs_count AS county_share
        FROM (
            SELECT county, sp_platform, SUM(customer_base) AS sp_cld_subs_count
            FROM bbcv_aggregate
            GROUP BY county, sp_platform
        ) JOIN (
            SELECT county, SUM(customer_base) AS total_cld_subs_count
            FROM bbcv_aggregate
            GROUP BY county
        ) USING (county)
    )
    , sat_share_state AS (
        -- state: string, state_sp_platform: string, state_share: double
        SELECT
            state,
            sp_platform AS state_sp_platform,
            sp_cld_subs_count / total_cld_subs_count AS state_share
        FROM (
            SELECT state, sp_platform, SUM(customer_base) AS sp_cld_subs_count
            FROM bbcv_aggregate
            GROUP BY state, sp_platform
        ) JOIN (
            SELECT state, SUM(customer_base) AS total_cld_subs_count
            FROM bbcv_aggregate
            GROUP BY state
        ) USING (state)
    )
    -- block: string, sp_platform: string, subscribers: double
    SELECT
        block,
        COALESCE(county_sp_platform, state_sp_platform) AS sp_platform,
        sat_subs * COALESCE(county_share, state_share) AS subscribers
    FROM satellite_cb
    LEFT JOIN sat_share_county USING (county)
    LEFT JOIN sat_share_state USING (state)
    WHERE sat_subs * COALESCE(county_share, state_share) > 0.0
)
, spatial_allocation AS (
    -- s3://d000-comlinkdata-com/prod/private/lookup-tables/broadband/agg-carrier-rollups/v=1.8.003/
    WITH county_sp_rollups AS (
        -- sp_platform: int, county_sp_platform: int, county: string
        SELECT DISTINCT
            child_sp_platform AS sp_platform,
            parent_sp_platform AS county_sp_platform,
            SUBSTR(limit_to_census_blockids, 1, 5) AS county
        FROM broadband.agg_carrier_rollups
        WHERE limit_to_census_blockids <> '-1'
    )
    , national_sp_rollups AS (
        -- sp_platform: int, national_sp_platform: int
        SELECT DISTINCT
            child_sp_platform AS sp_platform,
            parent_sp_platform AS national_sp_platform
        FROM broadband.agg_carrier_rollups
        WHERE limit_to_census_blockids = '-1'
    )
    -- s3://d000-pii-carvonia-comlinkdata-com/prod/private/broadband-market-share/spatial-allocation/v=1.0.0/date=2023-07-01/
    , spatial_allocation_source AS (
        -- block: string, county: string, sp_platform: int, weight_allocated: double
        SELECT
            destination_census_block_id AS block,
            SUBSTR(destination_census_block_id, 1, 5) AS county,
            CAST(sp_platform AS INTEGER) AS sp_platform,
            weight_allocated
        FROM carvonia_pii.spatial_allocation_prod_flat
        WHERE date = DATE('2023-07-01')
    )
    -- block: string, sp_platform: int, subscribers: double
    SELECT
        block,
        COALESCE(county_sp_platform, national_sp_platform, sp_platform) AS sp_platform,
        CAST(SUM(weight_allocated) AS INTEGER) AS subscribers
    FROM spatial_allocation_source
    LEFT JOIN county_sp_rollups USING (county, sp_platform)
    LEFT JOIN national_sp_rollups USING (sp_platform)
    GROUP BY block, COALESCE(county_sp_platform, national_sp_platform, sp_platform)
)
, redshift AS (
    SELECT block AS census_blockid, sp_platform, subscribers
    FROM spatial_allocation
    UNION ALL
    SELECT block AS census_blockid, sp_platform, CAST(fwa_subs AS INTEGER) AS subscribers
    FROM fwa_by_carrier
    JOIN first_party_footprint USING (block)
    UNION ALL
    SELECT block AS census_blockid, CAST(sp_platform AS INTEGER) AS sp_platform, subscribers
    FROM satellite_final
    UNION ALL
    SELECT block AS census_blockid, 6104 AS sp_platform, hsd_subs AS subscribers
    FROM first_party_footprint
    WHERE LENGTH(block) = 15
)
SELECT COUNT(*) FROM redshift
