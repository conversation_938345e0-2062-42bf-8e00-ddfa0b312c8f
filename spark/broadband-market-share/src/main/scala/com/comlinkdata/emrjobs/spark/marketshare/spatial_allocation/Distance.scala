package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation

import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.Distance.{TDistance, DistanceF, TLatitude, TRACT_STRING_LENGTH, TLongitude}
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.DistanceMatrix.{Dest, Src}
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.Allocation
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.types._

class Distance(
  distanceF: DistanceF,
  otherSubmarketPenalty: Double,
  otherTractPenalty: Double
) extends Serializable {

  def apply(src: Allocation, dst: Dest): Double = compute(
    src.latitude, src.longitude, dst.latitude, dst.longitude, src.submarket, dst.submarket, src.census_block_id, dst.csb
  )

  def apply(src: Src, dst: Dest): Double = compute(
    src.latitude, src.longitude, dst.latitude, dst.longitude, src.submarket, dst.submarket, src.csb, dst.csb
  )

  def compute(
    srcLatitude: TLatitude,
    srcLongitude: TLongitude,
    dstLatitude: TLatitude,
    dstLongitude: TLongitude,
    srcSubmarket: Submarket,
    dstSubmarket: Submarket,
    srcBlock: CensusBlock,
    dstBlock: CensusBlock
  ): TDistance = {
    val distanceRaw = distanceF(srcLatitude, srcLongitude, dstLatitude, dstLongitude)

    val submarketPenalty = if (srcSubmarket == dstSubmarket) 1 else otherSubmarketPenalty

    val srcTract = srcBlock.substring(0, TRACT_STRING_LENGTH)
    val dstTract = dstBlock.substring(0, TRACT_STRING_LENGTH)
    val tractPenalty = if (srcTract == dstTract) 1 else otherTractPenalty

    distanceRaw * submarketPenalty * tractPenalty
  }
}

object Distance {
  type TLatitude = Double
  type TLongitude = Double
  type TDistance = Double
  type DistanceF = (TLatitude, TLongitude, TLatitude, TLongitude) => TDistance

  val TRACT_STRING_LENGTH = 11


  def apply(distanceF: DistanceF, otherSubmarketPenalty: Double, otherTractPenalty: Double): Distance = {
    new Distance(distanceF, otherSubmarketPenalty, otherTractPenalty)
  }

  /**
    * Computes a simple euclidian distances between two points in the same row in a dataset.
    *
    * @param la1 fst. latitude
    * @param lo1 fst. longitude
    * @param la2 snd. latitude
    * @param lo2 snd. longitude
    * @return Euclidian distance between two points
    */
  def euclid(la1: Double, lo1: Double, la2: Double, lo2: Double): Double = {
    math.sqrt((la2 - la1) * (la2 - la1) + (lo2 - lo1) * (lo2 - lo1))
  }

  /**
    * Computes haversine/great-circle distances between two points.
    * implemented based on: https://www.movable-type.co.uk/scripts/latlong.html
    *
    * @param la1 fst. latitude
    * @param lo1 fst. longitude
    * @param la2 snd. latitude
    * @param lo2 snd. longitude
    * @return Harvesine/great-circle distance between two points
    */
  def haversine(la1: Double, lo1: Double, la2: Double, lo2: Double): Double = {
    val R = 6371e3 // earth radius in metres
    val psi1 = la1 * Math.PI / 180 // φ, λ in radians
    val psi2 = la2 * Math.PI / 180
    val deltaPsi = (la2 - la1) * Math.PI / 180
    val deltaLambda = (lo2 - lo1) * Math.PI / 180

    val a = Math.sin(deltaPsi / 2) * Math.sin(deltaPsi / 2) +
      Math.cos(psi1) * Math.cos(psi2) *
        Math.sin(deltaLambda / 2) * Math.sin(deltaLambda / 2)

    val c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    R * c // in metres
  }
}
