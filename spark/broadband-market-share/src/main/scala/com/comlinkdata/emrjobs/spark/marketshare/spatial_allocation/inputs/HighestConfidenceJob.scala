package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs

import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs.Schema._
import com.comlinkdata.largescale.commons.RichDate._
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.typesafe.scalalogging.LazyLogging
import java.lang.{Integer => JInt}
import java.net.URI
import java.time.LocalDate
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import org.apache.spark.sql.{SaveMode, SparkSession}


case class SpDma(sp: Int, dmas: List[String])

case class HighestConfidenceConfig(
  highConfidencePath: URI,
  carrierLookupPath: String,
  serviceTerritoriesPath: String,
  displayRulesPath: String,
  homeCarrierPath: URI,
  firstPartyFootprintPath: Option[String],
  // always includes 6130, 6140, 6710 as satellite SPIDs
  // first parties will also have their own SPID, and might add more client-dependent exclusions
  spExclusionList: List[Int],
  // some parties will have exclusions defined against both SPID and DMA
  spDmaInclusionList: List[SpDma],
  resultPath: URI,
  date: LocalDate
)

object Schema {
  case class TimeSeries(
    partition_date: String,
    ip: Ip,
    census_block_id: String,
    sp_platform: JInt,
    is_svt: Boolean)

  object TimeSeries extends Utils.reflection.ColumnNames[TimeSeries]

  case class Activity(sp_platform: JInt, raw_date: String)

  object Activity extends Utils.reflection.ColumnNames[Activity]

  case class Result(temp_household_id: Ip, census_block_id: String, sp_platform: Int)
}

object HighestConfidenceJob extends SparkJob(HighestConfidenceRunner)

object HighestConfidenceRunner extends SparkJobRunner[HighestConfidenceConfig] with LazyLogging {
  /**
    * Given a time series and a date range, will fill in the missing dates with an empty entry
    *
    * @param range  list of dates to have in the time series
    * @param series original time series
    * @return time series with gaps filled
    */
  def fillTimeSeries(range: Seq[String])(series: Seq[TimeSeries]): Seq[TimeSeries] =
    range.sorted.map(date => series.find(_.partition_date == date).getOrElse(TimeSeries(date, null, null, null, false)))

  /**
    * Given a time series, will find consistent SPIDs in it (consistent SPID appears three or more times sequentially),
    * and will clear (i.e., replace with an empty entry) all non-consistent entries
    *
    * @param series original time series
    * @return time series with inconsistencies cleared
    */
  def removeInconsistencies(series: Seq[TimeSeries]): Seq[TimeSeries] = {
    // walk through the series with a sliding scale
    val consistentSPIDs = series.map(_.sp_platform).sliding(3)
      .collect { case Seq(a, b, c) if Set(a, b, c).size == 1 && a != null => a }.toSet
    series.map { e =>
      if (consistentSPIDs.contains(e.sp_platform)) e // only retain consistent values
      else TimeSeries(e.partition_date, null, null, null, false)
    }
  }

  /**
    * Given a time series, will find 1- and 2-long empty sequences, enclosed between two consistent entries of the same
    * value, and will replace the empty entries with the enclosing consistent values
    *
    * @param series original time series
    * @return time series with nulls filled
    */
  def fillNulls(series: Seq[TimeSeries]): Seq[TimeSeries] = {
    case class Streak(begin: Int, end: Int, value: JInt)
    val nullStreaksToReplace = series.zipWithIndex.foldLeft(List.empty[Streak]) {
      case (Nil, (entry, _)) =>
        List(Streak(0, 0, entry.sp_platform))
      case (head :: tail, (entry, i)) if head.value == entry.sp_platform =>
        head.copy(end = i) :: tail
      case (list, (entry, i)) =>
        Streak(i, i, entry.sp_platform) :: list
    }.sliding(3).collect {
      case List(Streak(b1, e1, v1), Streak(b2, e2, null), Streak(b3, e3, v3))
        if e1 - b1 > 1 && e2 - b2 < 2 && e3 - b3 > 1 && v1 == v3 && v1 != null =>
        (b2 to e2).map(_ -> series(b1))
    }.flatten.toMap
    series.zipWithIndex.foldLeft(Seq.empty[TimeSeries]) {
      case (seq, (ts, i)) =>
        seq :+ nullStreaksToReplace.get(i).map(_.copy(partition_date = ts.partition_date)).getOrElse(ts)
    }
  }

  /**
    * Given a time series, will check if it needs to have activity checked outside the max date range of the series.
    * Activity needs to be checked if last two entries in the time series are empty. If activity has been detected,
    * the SPID that caused the activity will be searched in the time series, and if found, a matching entry will be
    * added to the end of the series
    *
    * @param activity optional activity, if detected
    * @param series   original time series
    * @return time series with activity record optionally added
    */
  def checkActivity(activity: Seq[Activity])(series: Seq[TimeSeries]): Seq[TimeSeries] =
    if (series.map(_.sp_platform).takeRight(2) != Seq(null, null))
      series
    else {
      val relevantSPIDs = series.map(_.sp_platform).filter(_ != null).toSet
      val latestRelevantActivity = activity.filter(a => relevantSPIDs.contains(a.sp_platform)) match {
        case Seq() => None
        case activityEntries => Some(activityEntries.maxBy(_.raw_date))
      }
      series ++ latestRelevantActivity.toSeq.flatMap { activity =>
        val lastSeenInTimeSeries = series.find(_.sp_platform == activity.sp_platform)
        lastSeenInTimeSeries.map(_.copy(partition_date = activity.raw_date)).toSeq
      }
    }

  /**
    * Checks if the series is of good enough quality to keep, i.e., if it has at least two non-empty contiguous entries.
    *
    * @param series Series to check
    * @return whether to keep it or not
    */
  def isGoodQuality(series: Seq[TimeSeries]): Boolean =
    series.sliding(2).exists {
      case Seq(ts1, ts2) =>
        ts1.sp_platform == ts2.sp_platform && ts1.sp_platform != null
      case _ =>
        false
    }

  def findBestBlock(series: Seq[TimeSeries]): TimeSeries = {
    assert(series.count(_.census_block_id != null) > 2,
      "Incorrect time series passed, should have more than 2 non-null entries")
    val mostFrequentBlock = series
      .filter(_.census_block_id != null)
      .groupBy(_.census_block_id).mapValues(_.size)
      .maxBy(_._2)._1
    val latestBlock = series
      .filter(_.census_block_id != null)
      .maxBy(_.partition_date).census_block_id
    val weights = series
      .groupBy(_.census_block_id)
      .map { case (block, entries) =>
        val weight = (if (mostFrequentBlock == block) 1 else 0) +
          (if (latestBlock == block) 2 else 0) +
          (if (entries.exists(_.is_svt)) 4 else 0)
        block -> weight
      }
    val bestBlock = weights.maxBy(_._2)._1
    series.find(_.census_block_id == bestBlock).orNull
  }

  override def runJob(config: HighestConfidenceConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    val isoDate = concat(lpad($"year", 4, "0"), lit("-"), lpad($"month", 2, "0"), lit("-"), lpad($"day", 2, "0"))
    val carrierInput = spark.read.parquet(config.carrierLookupPath)
      .withColumn("rank", row_number.over(Window.partitionBy($"mw_carrier").orderBy($"min_date".desc)))
      .filter($"rank" === 1)
      .select($"mw_carrier" as "carrier", $"sp_platform".cast(IntegerType) as "sp_platform")
    val serviceTerritoryInput = spark.read.parquet(config.serviceTerritoriesPath)
      .join(spark.read.option("header", true).csv(config.displayRulesPath),
        $"sp_id".cast(IntegerType) === $"sp_dim_id".cast(IntegerType))
      .select($"geoid20".substr(1, 15) as "census_block_id", $"parent_id", $"parent_id" as "sp_platform")
    val hcTsl = TimeSeriesLocation.ofYmdDatePartitions(config.highConfidencePath).build
    val hcMinDate = config.date.minusMonths(3)
    val hcMaxDate = config.date.minusDays(1)
    logger.info(s"Searching High Confidence partitions in range $hcMinDate to $hcMaxDate")
    val hcPartitions = hcTsl.partitionsExistingBetween(LocalDateRange.of(hcMinDate, hcMaxDate)).toList
    val hcDates = hcPartitions.map(_._1.toString)
    val hcLatestDate = hcPartitions.map(_._1).max
    logger.info(s"Reading High Confidence dates $hcDates")
    val highConfidenceInput = spark.read
      .option("basePath", config.highConfidencePath.toString)
      .parquet(hcPartitions.map(_._2): _*)
      .withColumn("census_block_id", lpad($"census_block_id".substr(1, 15), 15, "0"))
      .withColumn("partition_date", isoDate)
      .join(carrierInput, "carrier")
      .join(serviceTerritoryInput, Seq("census_block_id", "sp_platform"), "left")
      .withColumn("is_svt", $"parent_id".isNotNull)
      .groupBy($"ifa")
      .agg(collect_list(struct(TimeSeries.cols: _*)) as "series")
      .as[(Ifa, Seq[TimeSeries])]
    val resultTsl = TimeSeriesLocation.ofDatePartitions(config.resultPath).build
    val resultPath = resultTsl.partition(config.date)
    val homeCarrierInputRange = LocalDateRange.of(hcLatestDate, hcLatestDate.plusWeeks(1))
    logger.info(s"Reading Home Carrier data in range $homeCarrierInputRange")
    val homeCarrierPartitions = TimeSeriesLocation.ofDatePartitions(config.homeCarrierPath).build
      .inputPartitionsFor(homeCarrierInputRange)
    val homeCarrierInput = spark.read
      .option("basePath", config.homeCarrierPath.toString)
      .parquet(homeCarrierPartitions: _*)
      .withColumnRenamed("date", "raw_date")
      .join(carrierInput, "carrier")
      .filter($"sp_platform".isNotNull)
      .groupBy($"ifa")
      .agg(collect_list(struct(Activity.cols: _*)) as "activity")
      .as[(Ifa, Seq[Activity])]
    val highestConfidenceResult = highConfidenceInput
      .join(homeCarrierInput, Seq("ifa"), "left")
      .as[(Ifa, Seq[TimeSeries], Seq[Activity])]
      .flatMap { case (ifa, series, activity) =>
        val calculation = (fillTimeSeries(hcDates) _)
          .andThen(removeInconsistencies)
          .andThen(fillNulls)
          .andThen(checkActivity(Option(activity).toSeq.flatten))
        val result = calculation(series)
        if (isGoodQuality(result)) Some(ifa -> result) else None
      }
      .map(e => findBestBlock(e._2))
      .groupByKey(_.ip)
      .flatMapGroups { case (_, ipSeries) =>
        val series = ipSeries.toList
        val maxDate = series.map(_.partition_date).max
        series.find(_.partition_date == maxDate)
          .map(ts => Result(ts.ip, ts.census_block_id, ts.sp_platform))
      }
      .distinct
    val spFilteredResult = config.spExclusionList match {
      case spList@_ :: _ =>
        highestConfidenceResult.filter(!$"sp_platform".isin(spList: _*))
      case _ =>
        highestConfidenceResult
    }
    val spDmaFilteredResult = config.firstPartyFootprintPath.map {
      spark.read.parquet(_)
        .select($"censusblock" as "census_block_id", regexp_replace($"dma", " ", "_") as "dma").distinct
    } match {
      case Some(footprint) =>
        val spDmaFilter = config.spDmaInclusionList match {
          case head :: tail =>
            tail.foldLeft(when($"sp_platform" === head.sp, $"dma".isin(head.dmas: _*))) {
              case (whenExpr, SpDma(sp, dmas)) =>
                whenExpr.when($"sp_platform" === sp, $"dma".isin(dmas: _*))
            }.otherwise(lit(true))
          case _ =>
            lit(true)
        }
        spFilteredResult
          .join(footprint, "census_block_id")
          .filter(spDmaFilter)
          .drop("dma")
      case _ =>
        spFilteredResult
    }
    logger.info(s"Writing results to $resultPath")
    spDmaFilteredResult.repartition(25).write.mode(SaveMode.Overwrite).parquet(resultPath)
  }
}
