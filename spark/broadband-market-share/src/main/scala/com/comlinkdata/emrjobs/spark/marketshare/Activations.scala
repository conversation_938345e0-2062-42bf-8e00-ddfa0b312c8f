package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband.lookup.ConsolidatedCarrier.implicits.CarrierLookupOps
import com.comlinkdata.largescale.schema.broadband_market_share.BroadbandCellularNoDate
import com.comlinkdata.largescale.schema.cellular_only.CellularOnlyDevice
import com.comlinkdata.largescale.schema.udp.installbase.IPHighConfidence
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.{SparkSession, Dataset, Row}

/**
  * Broadband activations - devices which leave cellular-only/fixed-wireless world and join broadband world.
  *
  * @param spark spark session
  */
class Activations(implicit spark: SparkSession) {

  import spark.implicits._

  def runDay(
    hc: Dataset[IPHighConfidence],
    co: Dataset[CellularOnlyDevice],
    carrierLookup: Dataset[CarrierLookup]
  ): Dataset[BroadbandCellularNoDate] = {

    val winningDevices = hc.select(
      $"ifa",
      $"household_id",
      $"ip_min_date" as "churn_date",
      $"census_block_id" as "winning_census_block",
      $"carrier" as "winning_carrier"
    )

    val losingDevices = co.select(
      $"ifa",
      $"census_block_id" as "losing_census_block",
      $"carrier" as "losing_carrier"
    )

    // activations: devices which existed on CO_q(N-1) and exist on HC_q(N)
    winningDevices
      .join(losingDevices, Seq("ifa"))
      .select(
        $"*", lit("co2bb") as "churn_type",
        lit(0) as "losing_sp")
      .transform(carrierLookup.addSpId[Row](carrier = $"winning_carrier", date = $"churn_date"))
      .withColumnRenamed("sp_id", "winning_sp")
      .as[BroadbandCellularNoDate]
  }
}

object Activations {

  def apply()(implicit spark: SparkSession) = new Activations()
}