CREATE TABLE carvonia_pii.block_level_interim_bb_est_spatial_allocation_input_carvonia_all_quarters
WITH (
    external_location = 's3://d000-pii-carvonia-comlinkdata-com/prod/private/broadband-market-share/bb-penetration/v=1.0.0/',
    partitioned_by = (ARRAY['date'])
) AS
-- s3://d000-pii-carvonia-comlinkdata-com/prod/reference-tables/bbmv-censusblock-division-region-dma-all-quarters/month_end_date=2023-06-28/
WITH first_party_footprint AS (
    -- block: string, cbg: string, hsd_hp: long, hsd_subs: long
    SELECT censusblock AS block, SUBSTR(censusblock, 1, 12) AS cbg, hsd_hp, hsd_subs
    FROM carvonia_pii.charter_bbmv_subs
    WHERE month_end_date = DATE('2023-06-28')
)
, satellite_cb AS (
    -- s3://c400-athena-dev-comlinkdata-com/paulina_sandbox/BBpen_model_2023/census_block_0912_v2/quarter=2023q2/
    WITH penetration AS (
        -- block: string, cbg: string, block_total: double, block_bb_total: double
        SELECT block, SUBSTR(block, 1, 12) AS cbg, block_total, block_bb_total
        FROM acs_bb.bbpen_model_2023_block_0912_v2
        WHERE quarter = '2023q2'
    )
    , penetration_block AS (
        SELECT
            block,
            block_total,
            CASE WHEN block_total = 0.0 THEN 0.0 ELSE block_bb_total / block_total END AS block_bb_penetration
        FROM penetration
    )
    , penetration_cbg AS (
        SELECT
            cbg,
            CASE WHEN SUM(block_total) = 0.0 THEN 0.0 ELSE SUM(block_bb_total) / SUM(block_total) END AS cbg_bb_penetration
        FROM penetration
        GROUP BY cbg
    )
    , penetration_adjusted_first_party_footprint AS (
        -- block: string, cbg: string, hsd_hp: long, hsd_subs: double, bb_hsd_hp: double
        SELECT
            block,
            cbg,
            CASE WHEN hsd_subs > hsd_hp THEN hsd_subs ELSE hsd_hp END AS hsd_hp,
            CAST((CASE WHEN hsd_subs > hsd_hp THEN hsd_subs ELSE hsd_hp END) AS DOUBLE) *
                (CASE
                    WHEN hsd_subs > hsd_hp THEN 1.0
                    WHEN block_total = 0.0 THEN cbg_bb_penetration
                    ELSE block_bb_penetration
                END)
            AS bb_hsd_hp,
            CAST(hsd_subs AS DOUBLE) AS hsd_subs
        FROM first_party_footprint
        LEFT JOIN penetration_block USING (block)
        LEFT JOIN penetration_cbg USING (cbg)
    )
    , penetration_adjusted_first_party_footprint_less_first_party_subs AS (
        -- block: string, cbg: string, block_total: long, block_bb_total: double, block_bb_total_first_party: double
        SELECT
            block,
            cbg,
            hsd_hp AS block_total,
            CASE WHEN hsd_subs > bb_hsd_hp THEN hsd_subs ELSE bb_hsd_hp END AS block_bb_total_first_party,
            CASE WHEN hsd_subs > bb_hsd_hp THEN 0.0 ELSE bb_hsd_hp - hsd_subs END AS block_bb_total
        FROM penetration_adjusted_first_party_footprint
    )
    -- s3://c400-athena-dev-comlinkdata-com/census/acs_hh_hu/block group/2020_onward/2020_geo/acs_bb/hh_hu_cbg_acs5_2020_onward/
    , satellite_prep AS (
        -- geoid: string, B28002_004: int, B28002_006: int, B28002_010: int
        SELECT
            geoid AS serv_terr_bgid,
            CASE WHEN uniqueid = 'B28002_004' THEN estimate ELSE 0 END AS B28002_004,
            CASE WHEN uniqueid = 'B28002_006' THEN estimate ELSE 0 END AS B28002_006,
            CASE WHEN uniqueid = 'B28002_010' THEN estimate ELSE 0 END AS B28002_010
        FROM acs_bb.hh_hu_cbg_acs5_2020_onward
        WHERE year = '2021'
        AND acs = '5_year'
    )
    , satellite AS (
        -- cbg: string, sat_pct: double
        SELECT
            serv_terr_bgid AS cbg,
            CASE
                WHEN SUM(B28002_004) - SUM(B28002_006) = 0 THEN 0.0
                ELSE (CAST(SUM(B28002_010) AS DOUBLE) / CAST(SUM(B28002_004) - SUM(B28002_006) AS DOUBLE))
            END AS sat_pct
        FROM satellite_prep
        GROUP BY serv_terr_bgid
    )
    -- block: string, cbg: string, block_total: long, block_bb_total: double, sat_subs: double
    SELECT
        block,
        cbg,
        SUBSTR(block, 1, 5) AS county,
        SUBSTR(block, 1, 2) AS state,
        block_total,
        block_bb_total,
        sat_pct * block_bb_total_first_party AS sat_subs
    FROM penetration_adjusted_first_party_footprint_less_first_party_subs
    LEFT JOIN satellite USING (cbg)
)
, fwa_by_carrier AS (
    -- s3://d000-comlinkdata-com/prod/private/cellular-fixed-wireless/quarterly-fixed-wireless-subs-output-cb2020/year=2023/month=06/day=15/
    WITH fwa_source AS (
        -- carrier: string, num_networks: long, block: string
        SELECT carrier, num_networks, SUBSTR(block, 1, 15) AS block
        FROM c300_production_aux_cellularfixedwireless.quarterly_fixed_wireless_subs_output_cb2020
        WHERE year||'-'||month||'-'||day = '2023-06-15'
    )
    , fwa_multiplier_denominator AS (
        -- carrier: string, observed_fwa_subs: double
        SELECT carrier, SUM(CAST(num_networks AS DOUBLE)) AS observed_fwa_subs
        FROM fwa_source
        GROUP BY carrier
    )
    -- s3://d000-comlinkdata-com/prod/private/broadband-market-share/industry-reported-numbers/v=1.0.2/date=2022-07-01/
    , fwa_multiplier_numerator AS (
        -- carrier: string, reported_fwa_subs: double
        SELECT 'Verizon' AS carrier, CAST(verizon_fwa AS DOUBLE) as reported_fwa_subs
        FROM acs_bb.industry_reported_numbers
        WHERE verizon_fwa <> ''
        AND quarter_end_date = '7/1/23'
        UNION ALL
        SELECT 'T-Mobile' AS carrier, CAST(tmobile_fwa AS DOUBLE) as reported_fwa_subs
        FROM acs_bb.industry_reported_numbers
        WHERE tmobile_fwa <> ''
        AND quarter_end_date = '7/1/23'
        UNION ALL
        SELECT 'US Cellular' AS carrier, CAST(uscc_fwa AS DOUBLE) as reported_fwa_subs
        FROM acs_bb.industry_reported_numbers
        WHERE uscc_fwa <> ''
        AND quarter_end_date = '7/1/23'
    )
    , fwa_multipliers AS (
        -- carrier: string, fwa_multiplier: double
        SELECT carrier, reported_fwa_subs * 1000.0 / observed_fwa_subs AS fwa_multiplier
        FROM fwa_multiplier_denominator
        JOIN fwa_multiplier_numerator USING (carrier)
    )
    -- s3://c400-athena-dev-comlinkdata-com/paulina_sandbox/fixed_wireless_spids/
    -- block: string, sp_platform: int, fwa_subs: double
    SELECT block, sp_id AS sp_platform, SUM(num_networks * COALESCE(fwa_multiplier, 1.0)) AS fwa_subs
    FROM fwa_source
    LEFT JOIN fwa_multipliers USING (carrier)
    JOIN acs_bb.fixed_wireless_spids sp ON carrier = tutela_carrier
    WHERE COALESCE(num_networks, 0) <> 0
    GROUP BY block, sp_id
)
, fwa AS (
    -- block: string, fwa_subs: double
    SELECT block, SUM(fwa_subs) AS fwa_subs
    FROM fwa_by_carrier
    GROUP BY block
)
, sat_fwa_cb AS (
    -- block: string, cbg: string, block_total: long, block_bb_total: double, sat_subs: double, fwa_subs: double
    SELECT block, cbg, block_total, block_bb_total, sat_subs, COALESCE(fwa_subs, 0) AS fwa_subs
    FROM satellite_cb
    LEFT JOIN fwa USING (block)
)
, final AS (
    SELECT
        block,
        cbg AS block_group,
        block_total,
        CASE
            WHEN block_bb_total = 0.0 OR block_bb_total < sat_subs + fwa_subs THEN 0.0
            ELSE block_bb_total - sat_subs - fwa_subs
        END AS block_bb_total
    FROM sat_fwa_cb
)
SELECT
    cast(sum(block_bb_total) as integer) as block_bb_total, -- 12,958,802
    cast(sum(block_total) as integer) as block_total -- 51,987,519
FROM final
