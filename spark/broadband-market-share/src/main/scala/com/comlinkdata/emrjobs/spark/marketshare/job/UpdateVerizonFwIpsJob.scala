package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.{SparkJob, SparkJobRunner, UriDFTableRW, Utils}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp
import com.comlinkdata.largescale.schema.tutela.TutelaIpClassification
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SaveMode, SparkSession}
import org.apache.spark.sql.functions._

import java.net.URI
import java.time.LocalDate

/**
  * UpdateVerizonFwIpsJobConfig config
  *
  * @param tutelaIpClassificationLocation Tutela IP classification location
  * @param outputLocation                 output location
  */
case class UpdateVerizonFwIpsJobConfig(
  startDate: Option[LocalDate],
  tutelaIpClassificationLocation: URI,
  outputLocation: URI
)

object UpdateVerizonFwIpsJob extends SparkJob[UpdateVerizonFwIpsJobConfig](UpdateVerizonFwIpsJobRunner)

object UpdateVerizonFwIpsJobRunner extends SparkJobRunner[UpdateVerizonFwIpsJobConfig] with LazyLogging {

  val VERIZON_ISP: String = "Cellco Partnership DBA Verizon Wireless"
  val MAX_DAILY_DEVICES_PER_IP: Int = 500
  val VERIZON_SSIDS = Seq(
    "Verizon-LRV%",
    "Verizon-5G-Home%"
  )

  // SQL query can be found in: https://comniscient.atlassian.net/browse/BME-109
  def runJob(config: UpdateVerizonFwIpsJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val twoOctets = udf((ipPrefix: String) => {
      val ip0 :: ip1 :: _ = ipPrefix.split('.').toList
      s"$ip0.$ip1"
    })

    val tutelaIpClassificationRaw = config.startDate match {
      case Some(day) => TutelaIpClassification.readExistingMonth(config.tutelaIpClassificationLocation, day.getYearString, day.getMonthValueString)
      case None => TutelaIpClassification.readExistingLatestMonth(config.tutelaIpClassificationLocation)
    }

    val tutelaIpClassification = tutelaIpClassificationRaw
      .getOrElse(throw new RuntimeException("No Tutela IP classification data found"))
      .filter($"isp" === VERIZON_ISP)
      .withColumn("date", concat_ws("-", $"year", $"month", $"day"))
      .withColumn("ip_2_octets", twoOctets($"ipprefix"))
      .cache()

    val minDate= tutelaIpClassification
      .select(min($"date"))
      .as[String]
      .first()

    logger.info(s"Generating Verizon IP addresses lookup table for date: $minDate")

    val numberOfDays = tutelaIpClassification.select($"date").distinct().count()

    // Find out plausible Verizon IPs by filtering out "high traffic" IPs
    val plausibleVerizonIPs = tutelaIpClassification
      .filter($"mcc".isNotNull && $"mcc" =!= lit(0)) // all of NULLs in Athena show in Spark as 0
      .groupBy($"ip_2_octets")
      .agg(countDistinct($"id") as "device_id_count")
      .filter(($"device_id_count" / numberOfDays) < MAX_DAILY_DEVICES_PER_IP)

    // FWA counts are monthly unique SSID-BSSID combinations. This will have some double counting of routers as
    // there are more than 1 channel active per router.
    val ssidBssidCounts = tutelaIpClassification
      .filter(VERIZON_SSIDS.map(vs => $"ssid".like(vs)).reduce(_ || _))
      .select($"ip_2_octets", concat_ws("_", $"ssid", $"bssid") as "ssid_bssid")
      .groupBy($"ip_2_octets")
      .agg(countDistinct($"ssid_bssid") as "ssid_bssid_count")

    val result = ssidBssidCounts
      .join(broadcast(plausibleVerizonIPs), Seq("ip_2_octets"), "left_semi")
      .withColumn("date", to_date(lit(minDate)))
      .select(VerizonFwIp.cols: _*)
      .as[VerizonFwIp]

    UriDFTableRW(config.outputLocation)
      .writeDsPartitionBy(
        ds = result,
        partitionColumnStrings = Seq("date"),
        saveMode = SaveMode.Overwrite,
        configs = Seq(config),
        dynamicOverwrite = true,
        outputPartitions = Some(1)
      )
  }
}
