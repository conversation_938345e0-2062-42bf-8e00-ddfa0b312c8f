package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.emrjobs.spark.marketshare.Deactivations.FwaBudget
import com.comlinkdata.emrjobs.spark.marketshare.IfaHomeCarriers.FwIfas
import com.comlinkdata.emrjobs.spark.marketshare.implicits.ColumnOps
import com.comlinkdata.largescale.schema.FwaTrendsNoDate
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband.lookup.ConsolidatedCarrier.implicits.CarrierLookupOps
import com.comlinkdata.largescale.schema.broadband_market_share.BroadbandCellularNoDate
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.FixedWirelessSpid
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.FixedWirelessSpid.implicits.FixedWirelessSpidOps
import com.comlinkdata.largescale.schema.cellular_only.CellularOnlyDevice
import com.comlinkdata.largescale.schema.udp.installbase.IPHighConfidence
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.{SparkSession, Dataset, DataFrame}
import org.apache.spark.sql.functions._

/**
  * Computes "broadband deactivations" - devices which left broadband world and joined cellular-only or fixed-wireless
  * world.
  *
  * @param fwaFromBbProportion proportion of fixed-wireless devices compared to broadband (estimate)
  * @param spark               spark session
  */
class Deactivations(fwaFromBbProportion: Double)(implicit spark: SparkSession) {

  import spark.implicits._

  def runDay(
    highConfidences: Map[Int, Dataset[IPHighConfidence]],
    cellularOnlys: Map[Int, Dataset[CellularOnlyDevice]],
    carrierLookup: Dataset[CarrierLookup],
    spidLookup: Dataset[FixedWirelessSpid],
    excludeSpIds: Seq[Int],
    fwaTrends: Dataset[FwaTrendsNoDate],
    verizonFwIfasOpt: Option[Dataset[FwIfas]]
  ): Dataset[BroadbandCellularNoDate] = {
    val highConfidencesOrderedFromLatest = highConfidences.toSeq.sortBy(_._1).map(_._2)
    val cellularOnlysOrderedFromLatest = cellularOnlys.toSeq.sortBy(_._1).map(_._2)

    val deactivations = findRawDeactivations(
      highConfidencesOrderedFromLatest, cellularOnlysOrderedFromLatest, carrierLookup
    )

    // SP is assigned now as if a row was "potential" BB-FWA churn.
    // If the row isn't allocated to FWA, SP will be cleared to 0.
    val deactivationsWithSp = deactivations
      .transform(spidLookup.addTutelaSpId($"winning_carrier", excludeSpIds = excludeSpIds))
      .withColumnRenamed("sp_id", "winning_sp")
      .withColumn("tract", $"winning_census_block".tract)
      .cache()

    val fwaBudget = calculateFwaBudget(fwaTrends, deactivationsWithSp)
    val bb2fwa = allocateFwaChurn(deactivationsWithSp, verizonFwIfasOpt, fwaBudget)
      .withColumn("churn_type", lit("bb2fwa"))

    val bb2co = deactivationsWithSp
      .withColumn("winning_sp", lit(0))
      .join(bb2fwa.select($"ifa"), Seq("ifa"), "left_anti")
      .withColumn("churn_type", lit("bb2co"))

    bb2fwa
      .unionByName(bb2co)
      .select(BroadbandCellularNoDate.cols: _*)
      .as[BroadbandCellularNoDate]
  }

  /**
    * Allocate BB deactivations (for now considered as BB->CO) as BB->FWA based on FWA budget.
    * Deactivations which did not pass allocation are filtered out.
    *
    * @param deactivationsWithSp BB deactivations
    * @param verizonFwIfasOpt    Optional list of ifas on Verizon wireless
    * @param fwaBudget           FWA budget
    * @return BB->FWA deactivations
    */
  def allocateFwaChurn(
    deactivationsWithSp: DataFrame,
    verizonFwIfasOpt: Option[Dataset[FwIfas]],
    fwaBudget: Dataset[FwaBudget]
  ): DataFrame = {

    val verizonFwIfas = verizonFwIfasOpt
      .getOrElse(spark.emptyDataset[FwIfas])
      .groupBy($"ifa")
      .agg(countDistinct($"*") as "dates_on_vzn")

    val deactivationsWithFwaBudget = deactivationsWithSp
      .join(verizonFwIfas, Seq("ifa"), "left")
      .withColumn("dates_on_vzn", coalesce($"dates_on_vzn", lit(0)))
      .join(fwaBudget, Seq("winning_sp", "tract"), "left")

    val wTractSpByIfa = Window
      .partitionBy("tract", "winning_sp")
      .orderBy($"ifa", $"dates_on_vzn".desc)

    // for winning_sp = 0 fwa_budget = null
    // so the filter below will eliminate all not-real FWA
    // thus we don't need to clear winning SPs for unallocated rows (complete rows will be eliminated)
    deactivationsWithFwaBudget
      .withColumn("ifa_row_number", row_number() over wTractSpByIfa)
      .filter($"ifa_row_number" <= $"fwa_budget")
      .drop("ifa_row_number", "fwa_budget", "dates_on_vzn")
  }

  /**
    * Calculates fixed-wireless budget (target volume) based on FWA trends for some SPs per track.
    *
    */
  def calculateFwaBudget(
    fwaTrends: Dataset[FwaTrendsNoDate],
    deactivationsWithSp: DataFrame
  ): Dataset[FwaBudget] = {
    val volumeByTract = deactivationsWithSp
      .filter($"winning_sp" =!= 0) // only some SPs
      .groupBy($"winning_sp", $"tract")
      .count()

    val allBySpid = Window.partitionBy("sp_id")
      .rowsBetween(Window.unboundedPreceding, Window.unboundedFollowing)
    val prepFwaTrends = fwaTrends
      .filter($"sp_id" =!= 0 && $"growth" >= 0)
      .select($"sp_id" as "winning_sp", $"tract", $"growth", (count("*") over allBySpid) as "cnt")
      .filter($"cnt" >= 2)
      .drop("cnt")

    val wSp = Window.partitionBy($"winning_sp")
    val wSpByGrowth = Window
      .partitionBy($"winning_sp")
      .orderBy($"growth")

    // Here we create a per-tract and per-sp FWA "budget" (expected volume)
    //
    // Algorithm:
    // - "projected": is preliminary expectation of FWA volume per sp & tract, computed as
    //                deactivations volume per tract * percentile of FWA growth per SP and all tracts.
    //                Thus the volume is derived from observed volume in CO, which contains 50% of FWA and
    //                50% of no-service.
    //
    // - "fwa_budget": adjusts the "projected" volume to FWA, which is 50% of CO. That's why we must normalize
    //                the "projected" volume by a ratio of "50% of CO volume" / projected per all tracts, as:
    //
    //                    (deactivations_volume_per_sp * 0.5) / projected_volume_per_sp
    //
    // Constrains:
    //   1. fwa_budget must not be higher than observed deactivations volume
    //   2. fwa_budget must not be larger than the observed Tutela growth in that tract for this SP

    prepFwaTrends
      .join(volumeByTract, Seq("winning_sp", "tract"))
      .withColumn("projected", (percent_rank() over wSpByGrowth) * $"count")
      .withColumn("fwa_budget", round($"projected" * (sum($"count") over wSp) * fwaFromBbProportion / (sum($"projected") over wSp), 0))
      .withColumn("fwa_budget", when($"fwa_budget" > $"count", $"count").otherwise($"fwa_budget"))
      .withColumn("fwa_budget", when($"fwa_budget" > $"growth", $"growth").otherwise($"fwa_budget"))
      .select($"winning_sp", $"tract", $"fwa_budget")
      .as[FwaBudget]
  }

  def findRawDeactivations(
    highConfidencesOrderedFromLatest: Seq[Dataset[IPHighConfidence]],
    cellularOnlysOrderedFromLatest: Seq[Dataset[CellularOnlyDevice]],
    carrierLookup: Dataset[CarrierLookup],
  ): DataFrame = {
    // keep metadata from the latest high-confidence, but keep IFAs only which exist also in previous ones.
    val highConfidence = highConfidencesOrderedFromLatest.reduceLeft[Dataset[IPHighConfidence]] {
      case (hcLatest, hcPrev) =>
        hcLatest
          .join(hcPrev, Seq("ifa"), "leftsemi")
          .as[IPHighConfidence]
    }

    // keep metadata from the latest cellular-only, but keep IFAs only which exist also in previous ones,
    // all having cell_only_days_count >= 10.
    val cellularOnly = cellularOnlysOrderedFromLatest.reduceLeft[Dataset[CellularOnlyDevice]] {
      case (coLatest, coPrev) =>
        val coPrevFiltered = coPrev.filter($"cell_only_days_count" >= 10)
        coLatest
          .join(coPrevFiltered, Seq("ifa"), "leftsemi")
          .filter($"cell_only_days_count" >= 10)
          .as[CellularOnlyDevice]
    }

    val losingDevices = highConfidence.select(
      $"ifa",
      $"household_id",
      $"carrier" as "losing_carrier",
      $"census_block_id" as "losing_census_block",
      $"ip_max_date" as "churn_date"
    )

    val winningDevices = cellularOnly.select(
      $"ifa",
      $"census_block_id" as "winning_census_block",
      $"carrier" as "winning_carrier"
    )

    // deactivations: devices which existed on HC_q(N-1) and exist on CO_q(N)
    winningDevices
      .join(losingDevices, Seq("ifa"))
      .transform(carrierLookup.addSpId(carrier = $"losing_carrier", date = $"churn_date"))
      .withColumnRenamed("sp_id", "losing_sp")
  }
}

object Deactivations {

  case class FwaBudget(winning_sp: Int, tract: String, fwa_budget: Double)

  def apply(fwaFromBbProportion: Double)(implicit spark: SparkSession) = new Deactivations(fwaFromBbProportion)
}