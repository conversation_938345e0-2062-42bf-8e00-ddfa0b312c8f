package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.emrjobs.spark.marketshare.PlatformAggregate
import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJob<PERSON>unner, UriDFTableRW}
import com.comlinkdata.largescale.schema.broadband.lookup.{CarrierAggRollup, CarrierSuppression, CensusBlockNoSpLookup}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.{CensusBlock2010To2020Crosswalk, CensusBlockCrosswalk, FWAMultiplier, GuardRail, LegacyStateDimId}
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandAggregatedChurn, BroadbandAggregatedChurn25, ElementIDToSuppress}
import com.comlinkdata.largescale.schema.udp.installbase.{IfaWirelessCarrier, IfaWirelessCarrierYMD}
import com.comlinkdata.largescale.schema.udp.lookup.AllowedNetworkBrandPair
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.DCommonOcnLookup
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{Dataset, SaveMode, SparkSession}

import java.net.URI
import java.time.LocalDate

/**
  * Broadband Platform Aggregate job config
  *
  * @param startDate                                 optional start date
  * @param endDate                                   optional end date
  * @param censusBlockNoSpLookupLocation             geo-lookup location (no-SP)
  * @param censusBlockCrosswalkLocation              syndicated geo-lookup location
  * @param censusBlock2010To2020Location            Cross walk table to convert 2010 volume to 2020 volume
  * @param stateDimIdLookupLocation                  state-dim-id lookup location
  * @param aggCarrierRollupsLocation                 carrier rollup lookup table location
  * @param allowedNetworkBrandPairLocation           dimension table for joining to dCommonOcn
  * @param ifaWirelessCarrierLocation                dimension table for joining to wireless brands
  * @param dCommonOcnLocation                        dimension table for network brand sp
  * @param overlappingDays                           startDate = config.startDate - overlappingDays
  * @param platformPreAggregateRawLocation           output location of data aggregated at the ifa level
  * @param platformPreAggregateLocation              output location of data aggregated at the ifa level with fwa adjustments
  * @param platformAggregateLocation                 output location of data aggregated at census block
  * @param guardRailOutputLocation                   output location of helper table for guard rails
  * @param outputPartitions                          output number of partitions
  * @param ifasToSuppressLocation                    IFA suppression lookup table location
  * @param carrierSuppressLocation                   carrier suppression lookup table location
  * @param fwaMultipliers                            FWA multipliers
  * @param guardRailLocation                         Guard Rail location
  * @param guardRailLookback                         Moving average days for guard rails
  */
case class PlatformAggregateJobConfig(
  startDate: Option[LocalDate],
  endDate: Option[LocalDate],
  aggregatedChurnLocation: Option[URI],
  aggregatedChurn25Location: Option[URI],
  censusBlockNoSpLookupLocation: URI,
  censusBlockCrosswalkLocation: URI,
  censusBlockCrosswalk2020Location: URI,
  censusBlock2010To2020Location: URI,
  stateDimIdLookupLocation: URI,
  aggCarrierRollupsLocation: URI,
  allowedNetworkBrandPairLocation: URI,
  ifaWirelessCarrierLocation: URI,
  dCommonOcnLocation: URI,
  overlappingDays: Int,
  platformPreAggregateRawLocation: URI,
  platformPreAggregateLocation: URI,
  platformAggregateLocation: URI,
  guardRailOutputLocation: URI,
  outputPartitions: Int,
  ifasToSuppressLocation: Option[URI],
  carrierSuppressLocation: Option[URI],
  fwaMultipliers: Option[URI],
  guardRailLocation: Option[URI],
  guardRailLookback: Int
)

object PlatformAggregateJob extends SparkJob[PlatformAggregateJobConfig](PlatformAggregateJobRunner)

object PlatformAggregateJobRunner extends SparkJobRunner[PlatformAggregateJobConfig] with LazyLogging {

  def runJob(config: PlatformAggregateJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val tsl = (config.aggregatedChurnLocation, config.aggregatedChurn25Location) match {
      case (Some(uri), None) => BroadbandAggregatedChurn.tsl(uri)
      case (None, Some(uri)) => BroadbandAggregatedChurn25.tsl(uri)
    }

    val churnStartDate = getStartDate(config.startDate, config.overlappingDays, tsl.earliestDate)
    val guardRailStartDate = getStartDate(config.startDate, config.overlappingDays + config.guardRailLookback, tsl.earliestDate)

    val endDate = config.endDate.getOrElse(tsl.latestDate)
    val ldr = LocalDateRange(guardRailStartDate, endDate)

    logger.info(s"Processing date range: ${LocalDateRange(churnStartDate, endDate)}")
    logger.info(s"Adjusting date range for guard rail lookback: $ldr")


    val carrierRollupLookup = CarrierAggRollup.read(config.aggCarrierRollupsLocation).cache()
    val csbNoSpLookup = CensusBlockNoSpLookup.read(config.censusBlockNoSpLookupLocation).cache()
    val csbCrosswalkLookup = CensusBlockCrosswalk.read(config.censusBlockCrosswalkLocation).cache()
    val csbCrosswalk2020Lookup = CensusBlockCrosswalk.read2020(config.censusBlockCrosswalk2020Location).cache()
    val stateDimIdLookup = LegacyStateDimId.read(config.stateDimIdLookupLocation).cache()
    val allowedNetworkBrandPairLookup = AllowedNetworkBrandPair.read(config.allowedNetworkBrandPairLocation)
    val dCommonOcnLookup = DCommonOcnLookup.read(config.dCommonOcnLocation)
    val ifaWirelessCarrier = IfaWirelessCarrierYMD.read(config.ifaWirelessCarrierLocation).drop("year", "month", "day").as[IfaWirelessCarrier]
    val csb2010To2020Crosswalk = CensusBlock2010To2020Crosswalk.read(config.censusBlock2010To2020Location)

    val ifasToSuppress: Dataset[ElementIDToSuppress] = config.ifasToSuppressLocation match {
      case Some(path) => ElementIDToSuppress.read(path)
      case None => spark.emptyDataset[ElementIDToSuppress]
    }
    val carrierSuppression: Dataset[CarrierSuppression] = config.carrierSuppressLocation match {
      case Some(path) => CarrierSuppression.read(path)
      case None => spark.emptyDataset[CarrierSuppression]
    }
    val fwaMultipliers: Dataset[FWAMultiplier] = config.fwaMultipliers match {
      case Some(path) => FWAMultiplier.read(path)
      case None => spark.emptyDataset[FWAMultiplier]
    }
    val dsGuardRail: Dataset[GuardRail] = config.guardRailLocation match {
      case Some(path) => GuardRail.read(path)
      case None => spark.emptyDataset[GuardRail]
    }

    val churn: Option[Dataset[BroadbandAggregatedChurn]] = config.aggregatedChurnLocation
      .map(uri => BroadbandAggregatedChurn.readExisting(uri, ldr).as[BroadbandAggregatedChurn])

    val churn25: Option[Dataset[BroadbandAggregatedChurn25]] = config.aggregatedChurn25Location
      .map(uri => BroadbandAggregatedChurn25.readExisting(uri, ldr).as[BroadbandAggregatedChurn25])

    val platformAggTuple = PlatformAggregate()
      .generate(
        churn,
        churn25,
        csbNoSpLookup,
        csbCrosswalkLookup,
        csbCrosswalk2020Lookup,
        csb2010To2020Crosswalk,
        stateDimIdLookup,
        carrierRollupLookup,
        allowedNetworkBrandPairLookup,
        ifaWirelessCarrier,
        dCommonOcnLookup,
        ifasToSuppress,
        carrierSuppression,
        fwaMultipliers,
        dsGuardRail,
        config.guardRailLookback,
        churnStartDate
      )

    UriDFTableRW(config.platformAggregateLocation)
      .writeDsPartitionBy(
        ds = platformAggTuple.agg,
        partitionColumnStrings = Seq("the_date"),
        saveMode = SaveMode.Overwrite,
        configs = Seq(config),
        dynamicOverwrite = true,
        outputPartitions = Some(config.outputPartitions)
      )
    
    UriDFTableRW(config.platformPreAggregateLocation)
      .writeDsPartitionBy(
        ds = platformAggTuple.preAgg,
        partitionColumnStrings = Seq("the_date"),
        saveMode = SaveMode.Overwrite,
        configs = Seq(config),
        dynamicOverwrite = true,
        outputPartitions = Some(config.outputPartitions)
      )

    UriDFTableRW(config.platformPreAggregateRawLocation)
      .writeDsPartitionBy(
        ds = platformAggTuple.preAggRaw,
        partitionColumnStrings = Seq("the_date"),
        saveMode = SaveMode.Overwrite,
        configs = Seq(config),
        dynamicOverwrite = true,
        outputPartitions = Some(config.outputPartitions)
      )

    UriDFTableRW(config.guardRailOutputLocation)
      .writeDsPartitionBy(
        ds = platformAggTuple.guardRail,
        partitionColumnStrings = Seq("the_date"),
        saveMode = SaveMode.Overwrite,
        configs = Seq(config),
        dynamicOverwrite = true,
        outputPartitions = Some(config.outputPartitions)
      )
  }

  def getStartDate(startDate: Option[LocalDate], overlappingDays: Int, tslEarliestDate: LocalDate): LocalDate = {
    startDate match {
      case Some(day) if day.minusDays(overlappingDays).isBefore(tslEarliestDate) => tslEarliestDate
      case Some(day) => day.minusDays(overlappingDays)
      case None => tslEarliestDate
    }
  }

}
