package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema

import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.types._


/**
  * Target census block with meta-information
  *
  * @param county                 county
  * @param target_census_block_id target census block
  * @param target_submarket       target block submarket
  * @param target_latitude        target block latitude
  * @param target_longitude       target block longitude
  * @param capacity               target block capacity (remaining weight)
  */
case class Target(
  county: County,
  target_census_block_id: CensusBlock,
  target_submarket: Submarket,
  target_latitude: Double,
  target_longitude: Double,
  capacity: Double
)