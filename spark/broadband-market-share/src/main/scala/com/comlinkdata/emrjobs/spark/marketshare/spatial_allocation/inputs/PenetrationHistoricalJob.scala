package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs

import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs.PenetrationCommons.calculateSatelliteCensusBlocks
import com.comlinkdata.largescale.commons.{SparkJob, SparkJobRunner, TimeSeriesLocation}
import com.typesafe.scalalogging.LazyLogging
import java.net.URI
import java.time.LocalDate
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import org.apache.spark.sql.{SaveMode, SparkSession}

case class PenetrationHistoricalConfig(
  penetrationModelPath: String,
  householdBlockGroupsPath: String,
  firstPartyFootprintPath: Option[String],
  satelliteCensusBlockResultPath: URI,
  penetrationResultPath: URI,
  date: LocalDate
)

object PenetrationHistoricalJob extends SparkJob(PenetrationHistoricalRunner)

object PenetrationHistoricalRunner extends SparkJobRunner[PenetrationHistoricalConfig] with LazyLogging {
  override def runJob(config: PenetrationHistoricalConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    val penetrationResultPath = TimeSeriesLocation.ofDatePartitions(config.penetrationResultPath).build
      .partition(config.date)
    val satelliteCensusBlockResultPath = TimeSeriesLocation.ofDatePartitions(config.satelliteCensusBlockResultPath).build
      .partition(config.date)
    val firstPartyFootprint = config.firstPartyFootprintPath.map {
      spark.read.parquet(_).select(
        $"censusblock",
        $"hsd_hp".cast(IntegerType) as "hsd_hp",
        $"hsd_subs".cast(IntegerType) as "hsd_subs")
    }
    // block: string, cbg: string, block_total: double, block_bb_total: double, sat_subs: double
    val satelliteCensusBlocks = calculateSatelliteCensusBlocks(
      firstPartyFootprint,
      spark.read.parquet(config.penetrationModelPath),
      spark.read.parquet(config.householdBlockGroupsPath))
    logger.info(s"Writing satellite census block results into $satelliteCensusBlockResultPath")
    satelliteCensusBlocks.coalesce(10).write.mode(SaveMode.Overwrite).parquet(satelliteCensusBlockResultPath)
    // block: string, fwa_subs: double
    val penetration = satelliteCensusBlocks
      .select(
        $"block",
        $"cbg" as "block_group",
        $"block_total",
        when($"block_bb_total" === 0.0, lit(0.0))
          .when($"block_bb_total" - $"sat_subs" < 0.0, lit(0.0))
          .otherwise($"block_bb_total" - $"sat_subs") as "block_bb_total")
    logger.info(s"Writing penetration results into $penetrationResultPath")
    penetration.coalesce(10).write.mode(SaveMode.Overwrite).parquet(penetrationResultPath)
  }
}
