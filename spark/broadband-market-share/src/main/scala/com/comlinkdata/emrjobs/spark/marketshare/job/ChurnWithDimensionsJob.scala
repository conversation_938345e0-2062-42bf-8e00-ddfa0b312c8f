package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.emrjobs.spark.marketshare.ChurnWithDimensions
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.broadband.lookup.{PopStatsCensusBlockCentroidsTaggedNpa, CarrierLookup, CensusBlockNoSpLookup}
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandChurnWithDimension, BroadbandChurn, BroadbandDailyHome}
import com.comlinkdata.largescale.schema.udp.installbase.{IpToLocation, IPHighConfidence}
import com.typesafe.scalalogging.LazyLogging
import org.apache.sedona.sql.utils.SedonaSQLRegistrator
import org.apache.spark.sql.{SparkSession, Dataset, SaveMode}

import java.net.URI
import java.time.{Duration, LocalDate}

/**
  *
  * @param bbChurnLocation        Broadband churn
  * @param highConfidenceLocation IP High Confidence
  * @param ipLocsLocation         IP Locations
  * @param ifaLocsLocation        Ifa Locations
  * @param carrierLookupLocation  Carrier Lookup
  * @param polygonsLocation       Census Block Locations
  * @param outputLocation         Output
  * @param evaluationWindowSize   window size default to 69
  * @param startDate              start date
  * @param endDate                end date
  * @param repartition            optional output repartition
  */
case class ChurnWithDimensionsJobConfig(
  bbChurnLocation: URI,
  highConfidenceLocation: URI,
  ipLocsLocation: URI,
  udpDailyHomeLocation: URI,
  censusBlockNoSpLookupLocation: URI,
  popstatsLocation: URI,
  carrierLookupLocation: URI,
  carrierLookupFwLocation: URI,
  polygonsLocation: URI,
  outputLocation: URI,
  evaluationWindowSize: Int,
  startDate: LocalDate,
  endDate: Option[LocalDate],
  repartition: Option[Int]
)

object ChurnWithDimensionsJob extends SparkJob(ChurnWithDimensionsJobRunner) {

  /**
    * Register sedona before calling run.  This cannot happen in "configure spark" because it only
    * provides a SparkBuilder at that point
    */
  override def run(config: ChurnWithDimensionsJobConfig): Duration = {
    SedonaSQLRegistrator.registerAll(spark)
    super.run(config)
  }
}

object ChurnWithDimensionsJobRunner
  extends SparkJobRunner[ChurnWithDimensionsJobConfig]
    with SparkConstants
    with LazyLogging {

  override def runJob(config: ChurnWithDimensionsJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val tslChurnWithLocation = BroadbandChurnWithDimension.tsl(config.outputLocation)

    val dsCarrierLookup = CarrierLookup
      .read(config.carrierLookupLocation)
      .unionByName(CarrierLookup.read(config.carrierLookupFwLocation))
      .cache()

    val daysToRun: Seq[LocalDate] = (config.startDate to config.endDate.getOrElse(config.startDate)).toSeq
    for (endDate <- daysToRun) {
      val startDate = endDate.minusDays(config.evaluationWindowSize-1)
      val ldr = LocalDateRange(startDate, endDate)

      logger.info(s"Computing churn with dimension on $ldr")

      val dsHighConfidence: Dataset[IPHighConfidence] = IPHighConfidence.readExisting(config.highConfidenceLocation, ldr)

      val dsIpLocs: Dataset[IpToLocation] = IpToLocation.read(config.ipLocsLocation, ldr)
      val dsDailyHome: Dataset[BroadbandDailyHome] = BroadbandDailyHome.readExisting(config.udpDailyHomeLocation, ldr)

      val dsCBG: Dataset[CensusBlockNoSpLookup] = CensusBlockNoSpLookup.read(config.censusBlockNoSpLookupLocation)
      val dsCentroids: Dataset[PopStatsCensusBlockCentroidsTaggedNpa] = PopStatsCensusBlockCentroidsTaggedNpa.read(config.popstatsLocation)

      val dsChurn = BroadbandChurn.read(config.bbChurnLocation, endDate).as[BroadbandChurn]

      val data: Dataset[BroadbandChurnWithDimension] = dsChurn.transform(ChurnWithDimensions().run(
        endDate,
        config.polygonsLocation,
        dsCarrierLookup,
        dsHighConfidence,
        dsIpLocs,
        dsDailyHome,
        dsCBG,
        dsCentroids
      ))

      val dataRepartition = config.repartition match {
        case Some(partitions) => data.repartition(partitions)
        case None => data
      }

      UriDFTableRW
        .fromStr(tslChurnWithLocation.partition(endDate))
        .writeWithHistory(
          dataRepartition.drop("year", "month", "day").toDF(),
          SaveMode.ErrorIfExists,
          Seq(config))
    }
  }
}
