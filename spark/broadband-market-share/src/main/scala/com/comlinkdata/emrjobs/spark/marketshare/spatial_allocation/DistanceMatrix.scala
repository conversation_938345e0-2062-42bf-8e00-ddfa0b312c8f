package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation

import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.DistanceMatrix.{Dest, Src<PERSON>ensusBlock, DestRef}
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.constants.TOLERANCE
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.types._
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.{Target, Allocation}

import scala.collection.mutable

/**
  * A Non-thread-safe distance matrix of source blocks to target blocks.
  *
  * It is implemented as a map of source blocks to a "DestRef" (destination reference). A DestRef contains sorted array
  * of references (integer indexes) to the "dests" array. The sort order is by a closest distance to the source census
  * block.
  *
  * Memory complexity: O(|HH| * (4 + |Dest| * 4) + |Dest| * (8 + sizeof(Dest)))
  * where sizeof(Dest) = ~80 bytes
  *
  * Implementation note:
  * if we didn't use the extra reference level, the performance could be improved. But the memory would rise up.
  * Map would be {{{Map[SrcCensusBlock, (Int, Array[Dest])]}}}. It would store all Dest elements (only once), but each
  * key would have Array with pointers to all Dest elements (pointer is 8 bytes).
  * Complexity: O(|HH| * (4 + |Dest| * 8) + |Dest| * sizeof(Dest))
  *
  * Now we have {{{(Map[SrcCensusBlock, (Int, Array[Int])], Array[Dest])}}}. So the map stores direct Ints (not pointers)
  * and we have one extra {{{Array[Dest]}}}.
  * Complexity: O(|HH| * (4 + |Dest| * 4) + |Dest| * (8 + sizeof(Dest)))
  *
  * It might seem the latter is better, but from experience |HH| >> |Dest|.
  *
  * @param matrix distance matrix
  * @param dests  destination (target) blocks
  */
class DistanceMatrix(val matrix: Map[SrcCensusBlock, DestRef], dests: Array[Dest]) {

  /**
    * Get the closest destination block to a given source block.
    *
    * @param source source census block ID
    * @return closest destination (target) block
    */
  def get(source: SrcCensusBlock): Option[Dest] = {
    val destRefs = matrix.get(source)
    destRefs.flatMap(dr => {
      while (dr.startIndex < dr.destReference.length) {
        val dest = dests(dr.destReference(dr.startIndex))
        if (dest.capacity < TOLERANCE) {
          dr.startIndex += 1 //try next closest
        } else return Some(dest)
      }
      None
    })
  }

  /**
    * Get closest destination by source block.
    *
    * @param source source census block ID
    * @return closest destination (target) block
    */
  def apply(source: SrcCensusBlock): Dest = get(source).get

  /**
    * Determines if given source block can be allocated.
    *
    * A block can be allocated if there exist a destination block for given source block.
    *
    * @param source source census block ID
    * @return true if the block can be allocated, false otherwise
    */
  def hasTarget(source: SrcCensusBlock): Boolean = get(source).nonEmpty

  /**
    * Update a capacity of a closest destination block by a source block.
    *
    * @param source source census block ID
    * @param given  allocated weight which will be subtracted from a closest destination block capacity
    * @return true if a target was updated; false if the target doesn't exist (the source block cannot be allocated)
    */
  def update(source: SrcCensusBlock, given: Double): Boolean = {
    matrix.get(source).exists {
      dr =>
        if (dr.startIndex < dr.destReference.length) {
          val target = dests(dr.destReference(dr.startIndex)) // gets the closest destination block

          target.capacity -= given
          if (target.capacity < TOLERANCE) {
            // do not increment start index in other sources; get() will do it properly
            dr.startIndex += 1
          }
          true
        } else false
    }
  }
}

object DistanceMatrix {
  type SrcCensusBlock = CensusBlock
  type DstCensusBlock = CensusBlock
  type DstOrder = Int
  type Capacity = Double


  case class Src(csb: SrcCensusBlock, submarket: Submarket, latitude: Double, longitude: Double)

  implicit class SrcOps(a: Allocation) {
    def src: Src = Src(a.census_block_id, a.submarket, a.latitude, a.longitude)
  }

  case class Dest(csb: DstCensusBlock, submarket: Submarket, latitude: Double, longitude: Double, var capacity: Double)

  implicit class DestOps(t: Target) {
    def dest: Dest = Dest(t.target_census_block_id, t.target_submarket, t.target_latitude, t.target_longitude, t.capacity)
  }


  case class DestRef(var startIndex: Int, destReference: Array[Int])


  def apply(households: Seq[Allocation], targets: Seq[Target], distance: Distance): DistanceMatrix = {
    fromDest(households, targets.map(_.dest), distance)
  }

  def fromDest(households: Seq[Allocation], destinations: Seq[Dest], distance: Distance): DistanceMatrix = {

    // Get list of source blocks
    val sources = households.map(_.src).distinct
    val matrix = mutable.Map.empty[SrcCensusBlock, DestRef]

    val destArray = destinations.toArray
    val destsCount = destArray.length
    for (source <- sources) {
      val dr = DestRef(0, Array.fill(destsCount)(0))

      destArray
        .map(d => (distance(source, d), d))
        .zipWithIndex
        .sortBy { case ((dist, dest), _) => (dist, dest.capacity, dest.csb) }
        .zipWithIndex
        .foreach {
          case ((_, destIndex), refIndex) => dr.destReference(refIndex) = destIndex
        }

      matrix.put(source.csb, dr)
    }

    new DistanceMatrix(matrix.toMap, destArray)
  }
}
