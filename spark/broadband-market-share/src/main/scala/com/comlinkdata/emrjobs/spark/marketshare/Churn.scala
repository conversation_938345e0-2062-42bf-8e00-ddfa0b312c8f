package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.emrjobs.spark.marketshare.job.ArtificialChurn
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband.lookup.ConsolidatedCarrier.implicits.CarrierLookupOps
import com.comlinkdata.largescale.schema.broadband_market_share.{IfaHomeCarrierAggregated, BroadbandChurn, IfaHomeCarrier}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql._
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._

import java.sql.Date
import java.time.LocalDate

class Churn(implicit spark: SparkSession) extends LazyLogging {

  import spark.implicits._

  /**
    * Computes broadband churn for a single day with the following steps:
    * 1. get start and end date based off config.evaluationWindowSize, see getStartAndEndDates for more detail
    * 2. calculate spike start and end dates, usually evaluationWindowSize + 1 days
    * 3. Aggregate ifaHomeCarrier with spike dates
    * 4. combine new spike aggregate with input aggregate
    * 5. compute churn with spike data
    * 6. compute churn with input data
    * 7. Eliminate impossible churn
    * 8. write data out
    *
    * We need to recompute last "spikeEliminationWindowSize" days of ifa-home-carrier-aggregated to see all new
    * consolidated_ids which weren't reprocessed (Step 2 and 3)
    * We need to reprocess full 69 + 1 days of ifa-home-carrier-aggregated to get this right.
    * TODO: if we can't, we have to suppress artificial switches of reprocess date. how?
    * Example:
    * imagine if reprocess date = repro date - 1
    * so we need repro ifa-home-carrier-aggregated for date repro date - 1
    *
    * churn1 = compute churn for repro date - 1
    * churn2 = compute standard churn
    * all churns which exist both in churn1 at (repro date - 1) and churn2 at (repro date) should be removed from churn2
    *
    * @param artificialChurnLookup Static dataset of churn within carrier lookup table
    * @param carrierLookup         Carriers lookup
    */
  def runLoop(
    ldr: LocalDateRange,
    getIfaHomeCarrier: LocalDateRange => Dataset[IfaHomeCarrier],
    minimalPeriodNights: Int,
    minimalCarrierTenancy: Int,
    maximumDaysBetweenCarriers: Int,
    artificialChurnLookup: Dataset[ArtificialChurn],
    carrierLookup: Dataset[CarrierLookup]
  ): Dataset[BroadbandChurn] = {
    logger warn s"Running with effective start and end dates of $ldr"

    val ifaHomeCarrier = getIfaHomeCarrier(ldr)
    val ifaCarriersAgg = prepareIfaHomeCarrierAggregated(ifaHomeCarrier, carrierLookup)

    computeChurn(
      ldr,
      ifaCarriersAgg,
      artificialChurnLookup,
      minimalPeriodNights,
      minimalCarrierTenancy,
      maximumDaysBetweenCarriers
    )
  }

  /**
    * Applys a filter on df that removes days in holidays. Holidays are defined in HolidayCalculator
    *
    * @param d0 start date
    * @param d1 end date
    * @param df dataframe containing a date column
    * @return dataframe without holidays
    */
  def filterHolidays(d0: LocalDate, d1: LocalDate)(df: DataFrame): DataFrame = {
    /**
      * Boolean column that checks if dataframe's first date and last date are contained within a surrounding set of dates.
      *
      * @param dates holiday periods
      * @return true if $first_date and $last_date are inclusively contained within a holiday period
      */
    def makeLine(dates: (Date, Date)): Column =
      $"first_date" >= dates._1 && $"last_date" <= dates._2

    // transform immutable.IndexedSeq[(FirstDate: LocalDate, LastDate: LocalDate)] to Seq[(Date, Date)] that can be used in a dataframe
    val holidayConstants: Seq[(Date, Date)] = HolidayCalculator.holidays(d0, d1)
      .map { case (a, b) => Date.valueOf(a) -> Date.valueOf(b) }

    logger.info(s"Computing holiday filter for date range ${d0 -> d1}: holidays in period are: $holidayConstants.")
    if (holidayConstants.isEmpty) df
    else df.filter(not(holidayConstants.map(makeLine).reduce(_ || _)))
  }

  /**
    * Reads IFA-home-carrier and transforms it into IFA-home-carrier-aggregated
    *
    * @param ifaHomeCarrier  IFA-home-carrier
    * @param carrierLookup Carriers lookup
    * @return IfaHomeCarrierAggregated
    */
  def prepareIfaHomeCarrierAggregated(
    ifaHomeCarrier: Dataset[IfaHomeCarrier],
    carrierLookup: Dataset[CarrierLookup]
  ): Dataset[IfaHomeCarrierAggregated] = {
    ifaHomeCarrier
      .transform(calculateBroadbandConsolidateCarriers(carrierLookup))
      .as[IfaHomeCarrierAggregated]
  }

  def computeChurn(
    ldr: LocalDateRange,
    ifaHomeAggregated: Dataset[IfaHomeCarrierAggregated],
    artificialChurnLookup: Dataset[ArtificialChurn],
    minimalPeriodNights: Int,
    minimalCarrierTenancy: Int,
    maximumDaysBetweenCarriers: Int
  ): Dataset[BroadbandChurn] = {
    val broadbandGroups = ifaHomeAggregated.transform(computeChurnSegments).cache
    val aggregatedIntervals = broadbandGroups.transform(computeChurnAggNights(minimalPeriodNights, ldr)).cache
    val churnCandidates = aggregatedIntervals.transform(computeChurnCandidates(artificialChurnLookup))

    // Apply business rules to find the churn rows.
    val timingCheck: Column = $"loser_last_date" < $"winner_first_date"
    val minCarrierTenancy0 = datediff($"loser_last_date", $"loser_first_date") >= minimalCarrierTenancy
    val minCarrierTenancy1 = datediff($"winner_last_date", $"winner_first_date") >= minimalCarrierTenancy
    val maxDaysBetweenCarriers = datediff($"winner_first_date", $"loser_last_date") <= maximumDaysBetweenCarriers
    // on dropDate and below we expect artificial spikes when we just add carrier to carrier lookup
    val dropDate = ldr.startDate.minusDays(1).toDate

    logger info "Apply business rules to find the churn rows."
    churnCandidates
      .filter(Utils.and(
        timingCheck,
        minCarrierTenancy0,
        minCarrierTenancy1,
        maxDaysBetweenCarriers))
      .filter($"winner_first_date" > dropDate)
      .as[BroadbandChurn]
  }

  /**
    * is_min_date: to start counting at 1 instead of 0
    * diff_carrier: to identify a change in carrier
    * indicator: either the is_min_date or the diff_carrier column is equal to 1
    * cumulative_sum: running sum over the indicator
    *
    * what we want to do is label groups of like carriers into segments, in increasing order. e.g,:
    * ifa  | carrier | indicator | cumulative_sum
    * -----+---------+-----------+---------------
    * ifa1 |       A |         1 |             1
    * ifa1 |       A |         0 |             1
    * ifa1 |       A |         0 |             1
    * ifa1 |       B |         1 |             2
    * ifa1 |       B |         0 |             2
    * ifa1 |       A |         1 |             3
    * ifa1 |       A |         0 |             3
    * ifa1 |       B |         1 |             4
    * ifa1 |       A |         1 |             5
    *
    * so the algorithm to do this, is to carry a counter through this process,
    * and any time the previous entry is different (a new segment starts), add one to the segment counter,
    * otherwise carry it through.
    *
    * To get a counter, we just flag any segment change with a `1`, otherwise a `0`, and we can
    * sum these indicators as we move down the list, giving us the result we want above.
    *
    * carrier | cumulative_sum
    * --------+---------------
    * A       | 1
    * A       | 1
    * A       | 1
    * B       | 2
    * B       | 2
    * A       | 3
    * A       | 3
    * B       | 4
    * A       | 5
    *
    * @param ifaHomeAggregated Dataset of (ifa, sp_id, date)
    * @return ifaHomeAggregated with a new column of cumulative_sum outlined in the above algorithm
    */
  def computeChurnSegments(ifaHomeAggregated: Dataset[IfaHomeCarrierAggregated]): DataFrame = {
    val aggregationWindow = Window.partitionBy($"ifa").orderBy($"date")
    ifaHomeAggregated
      .withColumn("is_min_date", $"date" === min($"date").over(aggregationWindow))
      .withColumn("diff_carrier", $"sp_id" =!= lag($"sp_id", 1).over(aggregationWindow))
      .withColumn("indicator", when($"is_min_date" || $"diff_carrier", 1).otherwise(0))
      .withColumn("cumulative_sum", sum("indicator").over(aggregationWindow))
      .drop($"is_min_date")
      .drop($"diff_carrier")
      .drop($"indicator")
  }

  /**
    * Keep intervals with more than the minimal period nights while removing periods within holidays
    * TODO: Better "night" calculation done in UDP 2.0 InstallBase
    *
    * Example input:
    * ifa  | carrier | cumulutive_sum | date
    * -----+---------+----------------+-----------
    * ifa1 |       A |             1  | 2021-11-01
    * ifa1 |       A |             1  | 2021-11-02
    * ifa1 |       A |             1  | 2021-11-03
    * ifa1 |       B |             2  | 2021-11-04
    * ifa1 |       B |             2  | 2021-11-05
    * ifa1 |       A |             3  | 2021-11-06
    * ifa1 |       A |             3  | 2021-11-07
    * ifa1 |       B |             4  | 2021-11-08 * filtered out because of minimalPeriodNights
    * ifa1 |       A |             5  | 2021-11-09 * filtered out because of minimalPeriodNights
    *
    * nights:
    * ifa  | carrier | cumulutive_sum | first_date | last_date  | nights
    * -----+---------+----------------+------------+------------+---------
    * ifa1 |       A |             1  | 2021-11-01 | 2021-11-03 | 3
    * ifa1 |       B |             2  | 2021-11-04 | 2021-11-05 | 2
    * ifa1 |       A |             3  | 2021-11-06 | 2021-11-07 | 2
    *
    * output:
    * ifa  | carrier | first_date | last_date
    * -----+---------+------------+------------
    * ifa1 |       A | 2021-11-01 | 2021-11-07
    * ifa1 |       B | 2021-11-04 | 2021-11-05
    *
    * @param minimalPeriodNights Integer of how many consecutive nights an ifa must stay on a consolidated id
    * @param ldr                 LocalDateRange(StartDate, EndDate) window of data. Used to calculate holidays to exclude
    * @param df                  output of computeChurnSegments
    * @return new dataframe that has been aggregated down from a list of dates to a first and last date with counts
    */
  def computeChurnAggNights(minimalPeriodNights: Int, ldr: LocalDateRange)(df: DataFrame): DataFrame = {
    val aggregatedNights = df
      .groupBy($"ifa", $"sp_id", $"cumulative_sum")
      .agg(
        min($"date").as("first_date"),
        max($"date").as("last_date"),
        count($"date").as("night_count")
      )
      .filter($"night_count" >= minimalPeriodNights)
      .transform(filterHolidays(ldr.startDate, ldr.endDate))

    logger info "Aggregating consecutive intervals with the same carrier."
    // ZW THINKS GROUPBY LINE SHOULD BE AS FOLLOWS:
    //   https://comniscient.atlassian.net/wiki/spaces/RD/pages/831979661/Broadband+Algorithm+v1.5
    // .groupBy($"ifa", $"sp_id", $"cumulative_sum")
    // the cumulative_sum is a counter that separates an interval from adjacent intervals
    // currently, the groupby will combine nonadjacent intervals since we don't group by cumulative_sum
    // we need to add it in.
    aggregatedNights
      .groupBy($"ifa", $"sp_id")
      .agg(
        min($"first_date").as("first_date"),
        max($"last_date").as("last_date")
      )
  }

  /**
    * Create churn candidates by looking at the ifa's next carrier while removing artificial churn.
    *
    * Example input:
    * ifa1 | cons_id | first_date | last_date  |
    * -----+---------+------------+------------+
    * ifa1 |       1 | 2021-11-01 | 2021-11-07 |
    * ifa1 |       2 | 2021-11-08 | 2021-11-14 |
    *
    * before join:
    * ifa  | cons_id | loser_first_date | loser_last_date  | winner_cons_id | winner_first_date | winner_last_date |
    * -----+---------+------------------+------------------+----------------+------------------+------------------+
    * ifa1 |       1 | 2021-11-01       | 2021-11-07       | 2              | 2021-11-08        | 2021-11-14       |
    * ifa1 |       2 | 2021-11-08       | 2021-11-14       |                | null              | null             |
    *
    * We don't always reprocess home-carrier-aggregated(input) as required when the carrier lookup changes. In the case
    * where we add a record to carrier_lookup table example:
    * mw_carrier | sp_id | min_date
    * -----------+-------+------------
    * A          | 1     | 2016-01-01
    * A          | 2     | 2021-11-10      <-- this row was added
    * Creating artificialChurnLookup:
    * starting_id | ending_id | change_date
    * ------------+-----------+------------
    * 1           | 2         | 2021-11-10
    * So if we reprocess churn after the change_date (2021-11-10), we get this churn record of sp_id going
    * from 1->2, when they actually the same carrier. So we omit this record.
    * We also omit logical condition $"loser_last_date" < $"change_date" from the join
    *
    * @param artificialChurnLookup Static dataset of churn within carrier lookup table
    * @param df                    Dataframe output from computeChurnAggNights
    * @return Dataset of churn candidates that do not have any artificial churns
    */
  def computeChurnCandidates(artificialChurnLookup: Dataset[ArtificialChurn])(df: DataFrame): Dataset[BroadbandChurn] = {
    logger info "Creating a list of churnCandidates by looking for the following carrier in the window"
    val churnCandidatesWindow = Window
      .partitionBy($"ifa")
      .orderBy($"first_date")
    val crossingChangeDate = $"winner_first_date" >= $"change_date"
    val loserWinnerMatch = ($"loser_sp_id" === $"starting_id") && ($"winner_sp_id" === $"ending_id")
    val isArtificialChurn = loserWinnerMatch && crossingChangeDate

    df
      .select(
        $"ifa",
        $"sp_id".as("loser_sp_id"),
        $"first_date".as("loser_first_date"),
        $"last_date".as("loser_last_date"),
        lead($"sp_id", 1).over(churnCandidatesWindow).as("winner_sp_id"),
        lead($"first_date", 1).over(churnCandidatesWindow).as("winner_first_date"),
        lead($"last_date", 1).over(churnCandidatesWindow).as("winner_last_date"))
      .join(broadcast(artificialChurnLookup), isArtificialChurn, "left_anti")
      .as[BroadbandChurn]
  }

  /**
    * Similar to dailyHomeCarrierAgg, except using raw IfaHomeCarrier
    *
    * @param df              unconsolidated / raw IfaHomeCarrier
    * @param dsCarrierLookup Carrier Lookup dataset
    * @return DailyHomeCarrierAgg dataset for churn
    */
  def calculateBroadbandConsolidateCarriers(dsCarrierLookup: Dataset[CarrierLookup])(df: Dataset[IfaHomeCarrier]): Dataset[IfaHomeCarrierAggregated] = {
    df
      .withColumn("date", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))
      .transform(dsCarrierLookup.addIds($"carrier", $"date"))
      .filter($"sp_id".isNotNull)
      .groupBy($"ifa", $"date")
      .agg(
        max($"sp_id").as("sp_id"), // if a carrier is mapped to two or more, it should pick the max id, since bigger ids are usually more recent
        countDistinct($"sp_id").as("count"))
      .filter($"count" === 1)
      .drop("count")
      .as[IfaHomeCarrierAggregated]
  }
}

object Churn {

  def apply()(implicit sparkSession: SparkSession) = new Churn()
}