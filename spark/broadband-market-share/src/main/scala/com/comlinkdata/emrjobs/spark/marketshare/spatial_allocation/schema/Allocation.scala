package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema

import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.types._
import com.comlinkdata.largescale.schema.broadband_market_share.SpatialAllocation
import com.comlinkdata.largescale.schema.udp.Household

/**
  * Internal representation of the allocation.
  *
  * @param household_id household id
  * @param county county
  * @param census_block_id source (original) census block
  * @param target_census_block_id destination (re-allocated) census block
  * @param sp_platform sp_platform
  * @param submarket source (original) submarket
  * @param latitude source census block centroid latitude
  * @param longitude source census block centroid longitude
  * @param distance distance between source and target census blocks in metres
  * @param weight_factor weight factor (goal) of one household in the county
  * @param weight_allocated allocated weight of this household (<= weight_factor)
  * @param weight_allocated_cumulative cumulative allocated weight of this household
  */
case class Allocation(
  household_id: Household,
  county: County,
  census_block_id: CensusBlock,
  target_census_block_id: Option[CensusBlock],
  sp_platform: String,
  submarket: Submarket,
  latitude: Double,
  longitude: Double,
  distance: Double,
  weight_factor: Double,
  weight_allocated: Double,
  weight_allocated_cumulative: Double
) {
  def toOutput: SpatialAllocation = SpatialAllocation(
    household_id = household_id,
    county = county,
    census_block_id = census_block_id,
    destination_census_block_id = target_census_block_id.get,
    sp_platform = sp_platform,
    submarket = submarket,
    distance = distance,
    weight_factor = weight_factor,
    weight_allocated = weight_allocated,
    weight_allocated_cumulative = weight_allocated_cumulative
  )
}
