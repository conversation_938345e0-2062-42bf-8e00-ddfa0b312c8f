package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.emrjobs.spark.marketshare.AggregatedChurn
import com.comlinkdata.largescale.commons.io.dataset.findDaysToCompute
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandAggregatedChurn, BroadbandChurnWithDimension}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql._

import java.net.URI
import java.sql.Date
import java.time.LocalDate

/**
  * Config for AggregatedChurnJob.
  *
  * @param startDateOpt     optional start date (== churn_date)
  * @param endDateOpt       optional end date (== churn_date)
  * @param firstChurnYmdLag first churn_date==X partition ymd lag (first_ymd = churn_date + 14)
  * @param lastChurnYmdLag  last churn_date==X partition ymd lag (last_ymd = churn_date + 40)
  * @param rawDataFirstDate the very first day
  * @param bbChurnLocation  BB churn
  * @param outputLocation   output location
  * @param repartition      optional repartition
  */
case class AggregatedChurnConfig(
  startDateOpt: Option[LocalDate],
  endDateOpt: Option[LocalDate],
  firstChurnYmdLag: Int,
  lastChurnYmdLag: Int,
  rawDataFirstDate: LocalDate,
  bbChurnLocation: URI,
  outputLocation: URI,
  repartition: Option[Int]
)

object AggregatedChurnJob extends SparkJob[AggregatedChurnConfig](AggregatedChurnJobRunner)

object AggregatedChurnJobRunner extends SparkJobRunner[AggregatedChurnConfig] with LazyLogging {

  def runJob(config: AggregatedChurnConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    implicit val tsOut: TimeSeriesLocation = BroadbandAggregatedChurn.tsl(config.outputLocation)
    val daysToCompute = findDaysToCompute(
      minOutputDate = Some(config.rawDataFirstDate),
      startDate = config.startDateOpt,
      endDate = config.endDateOpt,
      overwrite = true // agg churn is always restating
    )

    logger.info(s"Computing churn days: $daysToCompute")
    if (daysToCompute.isEmpty) {
      logger.warn("Nothing to compute, exiting")
      return
    }

    val aggChurn = AggregatedChurn()

    val runsCount = daysToCompute.size
    for ((day, i) <- daysToCompute.zipWithIndex) {
      // date range as subject to restatement of given day
      val churnLdr = LocalDateRange(day.plusDays(config.firstChurnYmdLag), day.plusDays(config.lastChurnYmdLag))

      logger.info(s"Executing run ${i + 1}/$runsCount, churn_date=$day and churn days=$churnLdr (ignore non-existing)")

      val bbChurn = BroadbandChurnWithDimension
        .readExisting(config.bbChurnLocation, churnLdr)
        .filter($"churn_date" === Date.valueOf(day))

      val result: Dataset[BroadbandAggregatedChurn] = aggChurn.run(bbChurn)

      val dayPartition = URI create tsOut.partition(day)
      if (result.isEmpty) {
        logger.warn(s"Result for churn_date==$day is empty, deleting the day partition!")
        val cldUtils = CldFileUtils.newBuilder.forUri(config.outputLocation).build
        cldUtils.delete(dayPartition, recursive = true)
      } else {
        UriDFTableRW(config.outputLocation)
          .writeDsPartitionBy(
            ds = result,
            partitionColumnStrings = Seq("year", "month", "day"),
            saveMode = SaveMode.Overwrite,
            configs = Seq(config),
            dynamicOverwrite = true,
            outputPartitions = config.repartition
          )
      }
    }
  }
}