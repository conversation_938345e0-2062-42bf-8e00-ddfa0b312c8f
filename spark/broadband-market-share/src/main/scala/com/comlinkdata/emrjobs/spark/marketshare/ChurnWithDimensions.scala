package com.comlinkdata.emrjobs.spark.marketshare

import org.apache.spark.sql.expressions.Window
import com.comlinkdata.emrjobs.spark.georesolver.GeometryResolver
import com.comlinkdata.emrjobs.spark.marketshare.ChurnWithDimensions.DISCOUNT_RATE
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.customFunctions
import com.comlinkdata.largescale.commons.customFunctions.addDateColFromYMD

import java.net.URI
import com.comlinkdata.largescale.schema.udp.installbase.{IpToLocation, IPHighConfidence}
import com.typesafe.scalalogging.LazyLogging
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandChurnWithDimension, BroadbandChurn, BroadbandDailyHome}
import org.apache.spark.sql.types.IntegerType
import com.comlinkdata.largescale.schema.broadband.lookup.{PopStatsCensusBlockCentroidsTaggedNpa, CarrierLookup, CensusBlockNoSpLookup}
import org.apache.spark.sql.{SparkSession, Dataset, Row, DataFrame}
import org.apache.spark.sql.functions._

import java.time.{LocalTime, LocalDate}

/**
  * ChurnWithDimension
  *
  * Partitions by window_tail_date
  *
  * @param spark spark session
  */
class ChurnWithDimensions(implicit spark: SparkSession) extends LazyLogging {

  import spark.implicits._

  /**
    * Runs churn with dimension for one day
    *
    * @param endDate          window_tail_date
    * @param polygonsPath     census block polygons
    * @param dsCarrierLookup  carrier lookup
    * @param dsHighConfidence High Confidence dataset
    * @param dsIpLocs         Ip to Locations dataset
    * @param dsDailyHome      BB 1.8 daily homes (alternative to Ifa to Locations)
    * @param dsCBG            census block lookup table (to get census block id)
    * @param dsCentroid       centroid lookup table (to get lat/long)
    * @param churn            raw broadband churn output
    * @return pivoted churn table with additional columns
    */
  def run(
    endDate: LocalDate,
    polygonsPath: URI,
    dsCarrierLookup: Dataset[CarrierLookup],
    dsHighConfidence: Dataset[IPHighConfidence],
    dsIpLocs: Dataset[IpToLocation],
    dsDailyHome: Dataset[BroadbandDailyHome],
    dsCBG: Dataset[CensusBlockNoSpLookup],
    dsCentroid: Dataset[PopStatsCensusBlockCentroidsTaggedNpa]
  )(churn: Dataset[BroadbandChurn]): Dataset[BroadbandChurnWithDimension] = {
    logger.info(s"window_tail_date: $endDate at ${LocalTime.now()}")
    churn
      .transform(removeChurnWithMultipleEvents)
      .transform(addColBBorFWA)
      .transform(removeFWAChurnBeforeDate)
      .withColumn("window_tail_date", lit(endDate.toDate))
      .transform(unpivotByWinnerOrLoser)
      .transform(withCarrier(dsCarrierLookup))
      .transform(withIfaLocation(dsDailyHome, dsCBG, dsCentroid))
      .transform(withIpLocation(dsHighConfidence, dsIpLocs))
      .transform(pickBetweenIfaAndIpLocs)
      .transform(withHouseholdId(dsHighConfidence))
      .transform(withCensusBlock(polygonsPath))
      .transform(customFunctions.addYmdCols(col("window_tail_date")))
      .as[BroadbandChurnWithDimension]
  }

  /**
    * Remove churn records that have multiple events on the same churn_date. This is to avoid a cartesian product join later on
    *
    * @param churn Raw broadband churn output
    * @return Filtered BroadbandChurn with single events per ifa
    */
  def removeChurnWithMultipleEvents(churn: Dataset[BroadbandChurn]): Dataset[BroadbandChurn] = {
    val toRemove = churn
      .groupBy($"ifa", $"winner_first_date")
      .agg(count(lit(1)) as "c")
      .filter($"c" > 1)

    churn
      .join(toRemove, Seq("ifa"), "leftanti")
      .as[BroadbandChurn]
  }

  /**
    * Add Fixed Wireless Access (FWA) or Broadband (BB) if one leg of the churn has a fwa sp_id
    *
    * @param churn Broadband churn
    * @return BroadbandChurn Schema with FWA or BB column
    */
  def addColBBorFWA(churn: Dataset[BroadbandChurn]): DataFrame = {
    churn
      .withColumn("churn_record_type",
        when($"loser_sp_id" === FWA_TMO_SP_ID, "tmo")
          .when($"loser_sp_id" === FWA_VZW_SP_ID, "vzw")
          .when($"winner_sp_id" === FWA_TMO_SP_ID, "tmo")
          .when($"winner_sp_id" === FWA_VZW_SP_ID, "vzw")
          .otherwise("bb"))
  }

  /**
    * Remove FWA churn before 2021-01-01
    *
    * @param churn
    * @return
    */
  def removeFWAChurnBeforeDate(churn: DataFrame): DataFrame = {
    churn
      .withColumn("keep",
        when($"churn_record_type" === "bb", true)
          .when($"churn_record_type" === "vzw" and $"winner_first_date" >= FWA_VZW_START_DATE, true)
          .when($"churn_record_type" === "tmo" and $"winner_first_date" >= FWA_TMO_START_DATE, true)
          .otherwise(false))
      .filter($"keep")
  }

  /**
    * Transforms winner and loser columns into a unpivoted table while adding a new column to categorize winner and loser.
    * Easier to do a single join rather than two joins on both winner and loser
    *
    * @param churn Filtered broadband churn
    * @return Skinny table of churn with winner or loser column
    */
  def unpivotByWinnerOrLoser(churn: DataFrame): DataFrame = {
    val losers = churn
      .select(
        $"ifa",
        $"loser_first_date" as "first_date",
        $"loser_last_date" as "last_date",
        $"loser_sp_id" as "sp_id",
        $"winner_first_date" as "churn_date",
        $"window_tail_date",
        $"churn_record_type",
        lit(LOSER) as "w_or_l")
    val winners = churn
      .select(
        $"ifa",
        $"winner_first_date" as "first_date",
        $"winner_last_date" as "last_date",
        $"winner_sp_id" as "sp_id",
        $"winner_first_date" as "churn_date",
        $"window_tail_date",
        $"churn_record_type",
        lit(WINNER) as "w_or_l")
    losers.unionByName(winners)
  }

  /**
    * Takes one churn record and finds all potential carriers. Expect record count to go up with this step
    *
    * @param carrierLookup Carrier Lookup
    * @param df            unpivoted churn table
    * @return unpivoted churn table carrier and sp_platform
    */
  def withCarrier(carrierLookup: Dataset[CarrierLookup])(df: DataFrame): DataFrame = {
    df.alias("churn")
      .join(
        broadcast(carrierLookup).alias("carrier"),
        $"churn.sp_id" === $"carrier.sp" && $"churn.first_date" >= $"carrier.min_date",
        "inner")
      .select(
        $"churn.*",
        $"carrier.mw_carrier" as "carrier",
        $"carrier.sp_platform".cast(IntegerType)
      )
  }

  /**
    * Attaches a household id to unpivoted churn table from IP High Confidence table within the past 30 days
    *
    * @param dsHighConfidence High Confidence dataset that goes back {evaluationWindowSize} days
    * @param df               unpivoted churn table
    * @return unpivoted churn table with household_id
    */
  def withHouseholdId(dsHighConfidence: Dataset[IPHighConfidence])(df: DataFrame): DataFrame = {
    val dfHighConfidence = dsHighConfidence
      .withColumn("date", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))

    val window = Window.partitionBy("churn.ifa", "churn.w_or_l").orderBy($"high_confidence.date".asc)

    val round1 = df
      .alias("churn")
      .join(dfHighConfidence.alias("high_confidence"),
        $"churn.ifa" === $"high_confidence.ifa"
          and $"churn.ip" === $"high_confidence.ip"
          and $"high_confidence.date" >= $"churn.first_date",
        "left"
      )
      .withColumn("rank", row_number().over(window))
      .filter($"rank" === 1)
      .select($"churn.*", $"high_confidence.household_id")

    val partner = round1.alias("churn")
      .join(
        round1.alias("dim"),
        $"churn.ifa" === $"dim.ifa"
          and $"churn.churn_date" === $"dim.churn_date"
          and $"churn.w_or_l" =!= $"dim.w_or_l")
      .filter($"dim.household_id".isNotNull)
      .select(
        $"churn.ifa" as "ifa",
        $"churn.churn_date" as "churn_date",
        $"churn.w_or_l" as "w_or_l",
        $"dim.household_id" as "new_household_id")

    val round2 = round1
      .join(partner, Seq("ifa", "churn_date", "w_or_l"), "left")
      .withColumn("household_id", coalesce($"household_id", $"new_household_id", $"ifa"))

    round2
  }

  /**
    * Attaches IP, lat, and lng to unpivoted churn table:
    *   1. join to IP High Confidence by Carrier. The Carrier part of the join should reduce the 1 to many join, but not
    *      all. We are expecting many IPs for one ifa.
    *      2. Join to dsLocs (IpToLoc) via ip Using dsLocs, IpTolocation dataset, get the best lat and lng coordinates
    *      based off dates and ip from unpivoted churn
    *      3. reduce back down to 1 ifa -> 1 (ip, lat, lng) based off best "disc rate count"
    *
    * @param dsHighConfidence High confidence
    * @param dsLocs           Locations dataset that goes back {evaluationWindowSize} days
    * @param df               unpivoted churn table with ifa and carrier
    * @return unpivoted churn table with ip, lat, and lng coordinates
    */
  def withIpLocation(dsHighConfidence: Dataset[IPHighConfidence], dsLocs: Dataset[IpToLocation])(df: DataFrame): DataFrame = {
    val dfHighConfidence = dsHighConfidence
      .withColumn("date", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))

    val window = Window
      .partitionBy("churn.ifa", "churn.w_or_l")
      .orderBy($"high_confidence.date".desc)

    val ip = df.alias("churn")
      .join(dfHighConfidence.alias("high_confidence"),
        $"churn.ifa" === $"high_confidence.ifa"
          and $"churn.carrier" === $"high_confidence.carrier"
          and $"high_confidence.date".between($"churn.first_date", $"churn.last_date"),
        "left")
      .withColumn("ip_rank", row_number().over(window))
      .select($"churn.*", $"high_confidence.ip", $"ip_rank")

    val dfLocs = dsLocs.transform(addDateColFromYMD($"year", $"month", $"day", "loc_date"))

    val joined = ip.alias("churn")
      .filter($"sp_platform" =!= FWA_VZW_SP_ID && $"sp_platform" =!= FWA_TMO_SP_ID)
      .join(dfLocs.alias("locs"), Seq("ip"), "inner")
      .filter($"locs.loc_date".between($"churn.first_date", $"churn.last_date"))

    val bestLoc = df
      .transform(attachLocationDS(joined))
      .select(
        $"ifa",
        $"w_or_l",
        $"lat" as "ip_loc_lat",
        $"lng" as "ip_loc_lng",
        $"disc_count" as "ip_loc_disc_count",
        $"hours_seen" as "ip_loc_hours_seen",
        lit("ip") as "ip_loc_location_source"
      )
    val locWindow = Window.partitionBy("ifa", "w_or_l").orderBy($"ip_loc_disc_count".desc, $"ip_rank")
    ip
      .join(bestLoc, Seq("ifa", "w_or_l"), "left")
      .withColumn("rank", row_number().over(locWindow))
      .filter($"rank" === 1)
      .drop("rank", "ip_rank")
  }

  /**
    * Using BB 1.7 daily homes and lookup tables, get the best lat and lng coordinates based off dates and ip from
    * unpivoted churn table.
    *
    * @param dsDailyHome BB 1.7 daily homes
    * @param dsCBG       BB 1.7 cblockgroup_primary_cblock_with_fzip
    * @param dsCentroid  BB 1.7 popstats_census_block_centroids_tagged_npa
    * @param df          unpivoted churn table with ifa
    * @return
    */
  def withIfaLocation(
    dsDailyHome: Dataset[BroadbandDailyHome],
    dsCBG: Dataset[CensusBlockNoSpLookup],
    dsCentroid: Dataset[PopStatsCensusBlockCentroidsTaggedNpa],
  )(df: DataFrame): DataFrame = {
    val attachCensusBlockGeoId = df.as("churn")
      .join(dsDailyHome.as("daily_home"), Seq("ifa"), "inner")
      .filter($"daily_home.date".between($"churn.first_date", $"churn.last_date"))

    val groupedCensusBlockGroup = attachCensusBlockGeoId
      .groupBy(
        $"ifa",
        $"churn.w_or_l",
        $"daily_home.geoid".as("block_group"))
      .agg(approx_count_distinct($"daily_home.date").as("distinct_dates"))

    val windowBestCBlockGroup = Window
      .partitionBy($"ifa", $"w_or_l")
      .orderBy($"distinct_dates".desc)

    val rankedCensusBlockGroup = groupedCensusBlockGroup
      .withColumn("rank", row_number().over(windowBestCBlockGroup))
      .filter($"rank" === 1)

    val bestLoc = rankedCensusBlockGroup.as("cbg")
      .join(dsCBG.as("cb"), Seq("block_group"))
      .join(dsCentroid.as("centroid"), Seq("census_blockid"))
      .select(
        $"ifa",
        $"w_or_l",
        $"cb.census_blockid" as "daily_home_census_block_id",
        $"centroid.latitude" as "ifa_loc_lat",
        $"centroid.longitude" as "ifa_loc_lng",
        lit(0) as "ifa_loc_disc_count",
        lit(Array.emptyIntArray) as "ifa_loc_hours_seen",
        lit("daily_home") as "ifa_loc_location_source",
        $"distinct_dates" as "ifa_loc_daily_home_distinct_dates"
      )

    df.join(bestLoc, Seq("ifa", "w_or_l"), "left")
  }

  /**
    * Joins a dataset (IpToLocation or IfaToLocation) that has been joined on the appropriate columns to attach the
    * the following columns:
    *   - Hours seen at location - Appends, flattens, and distincts the hours seen list. This array value should be
    *     a combination of numbers from 0 to 23
    *   - lat,lng - get the best lat and lng coordinates based off dates
    *   - disc_count - metric used to pick the best lat and lng coordinate. The older the date, the less rank it holds
    *
    * @param locs DataFrame that is joined between DF and a IpToLocation or IfaToLocation dataset
    * @param df   unpivoted churn table with ifa to join back to
    * @return unpivoted churn table with hours_seen, (lat, lng) coordinate, and disc_count
    */
  def attachLocationDS(locs: DataFrame)(df: DataFrame): DataFrame = {
    val exploded = locs
      .withColumn("datediff", datediff($"churn.window_tail_date", $"locs.loc_date"))
      .withColumn("rate", pow(lit(DISCOUNT_RATE), $"datediff"))
      .select(
        $"churn.ifa" as "ifa",
        $"churn.w_or_l" as "w_or_l",
        $"locs.hours_seen" as "hours_seen",
        round($"locs.point.lat", 3) as "lat",
        round($"locs.point.lng", 3) as "lng",
        $"locs.point_obs" * $"rate" as "disc_count"
      )

    val locWindow = Window.partitionBy("ifa", "w_or_l").orderBy($"disc_count".desc)
    val aggregated = exploded
      .groupBy($"ifa", $"w_or_l", $"lat", $"lng")
      .agg(
        sum($"disc_count") as "disc_count",
        array_distinct(flatten(collect_list($"hours_seen"))) as "hours_seen")
      .withColumn("rank", row_number().over(locWindow))
      .filter($"rank" === 1)
      .drop("rank")

    aggregated
  }

  def pickBetweenIfaAndIpLocs(df: DataFrame): DataFrame = {
    if (df.columns.contains("ifa_loc_lat") && df.columns.contains("ip_loc_lat")) {
      df
        .withColumn("switch",
          when($"ifa_loc_disc_count".isNull, "ip") // case (ifa = null, ip = some or null)
            .when($"ip_loc_disc_count".isNull, "ifa") // case (ifa = some or null, ip = null)
            .when($"sp_platform".isin(FWA_TMO_SP_ID, FWA_VZW_SP_ID), "ifa")
            .otherwise("ip"))
        .withColumn("lat", when($"switch" === "ifa", $"ifa_loc_lat").otherwise($"ip_loc_lat"))
        .withColumn("lng", when($"switch" === "ifa", $"ifa_loc_lng").otherwise($"ip_loc_lng"))
        .withColumn("disc_count", when($"switch" === "ifa", $"ifa_loc_disc_count").otherwise($"ip_loc_disc_count"))
        .withColumn("hours_seen", when($"switch" === "ifa", $"ifa_loc_hours_seen").otherwise($"ip_loc_hours_seen"))
        .withColumn("location_source", when($"switch" === "ifa", $"ifa_loc_location_source").otherwise($"ip_loc_location_source"))
    } else if (df.columns.contains("ifa_loc_lat")) {
      df
        .withColumn("lat", $"ifa_loc_lat")
        .withColumn("lng", $"ifa_loc_lng")
        .withColumn("disc_count", $"ifa_loc_disc_count")
        .withColumn("hours_seen", $"ifa_loc_hours_seen")
        .withColumn("location_source", $"ifa_loc_location_source")

    } else {
      df
        .withColumn("lat", $"ip_loc_lat")
        .withColumn("lng", $"ip_loc_lng")
        .withColumn("disc_count", $"ip_loc_disc_count")
        .withColumn("hours_seen", $"ip_loc_hours_seen")
        .withColumn("location_source", $"ip_loc_location_source")
    }
  }

  /**
    * Given lat and lng, get the census_block_id using georesolver
    *
    * @param uri polygon json for census blocks. See Georesolver package for more input options
    * @param df  unpivoted churn table with lat and lng
    * @return unpivoted churn table with census_block_id
    */
  def withCensusBlock(uri: URI)(df: DataFrame): DataFrame = {
    // this is redundant, but easier to debug down the road
    val df2 = df.filter($"lat".isNotNull and $"lng".isNotNull)

    val cbr = GeometryResolver[Row](uri)
    cbr.resolveAsFlatDf(
      df2,
      $"lat",
      $"lng",
      "census_block_id"
    )
  }
}

object ChurnWithDimensions {
  final val DISCOUNT_RATE = 0.99
  final val HOUSEHOLD_DATE_DIFF = 30

  def apply()(implicit spark: SparkSession) = new ChurnWithDimensions()
}