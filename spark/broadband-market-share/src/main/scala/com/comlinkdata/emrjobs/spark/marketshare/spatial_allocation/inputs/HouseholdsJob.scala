package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs

import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON>, Spark<PERSON>ob<PERSON><PERSON><PERSON>, TimeSeriesLocation}
import com.typesafe.scalalogging.LazyLogging
import java.net.URI
import java.time.LocalDate
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.row_number
import org.apache.spark.sql.{SaveMode, SparkSession}

case class HouseholdsConfig(
  udpInstalledBasePath: String,
  carrierLookupPath: String,
  resultPath: URI,
  date: LocalDate
)

object HouseholdsJob extends SparkJob(HouseholdsRunner)

object HouseholdsRunner extends SparkJobRunner[HouseholdsConfig] with LazyLogging {
  override def runJob(config: HouseholdsConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    val resultTsl = TimeSeriesLocation.ofDatePartitions(config.resultPath).build
    val resultPath = resultTsl.validOutputPartitionOrThrow(config.date)
    val udpInstalledBase = spark.read.parquet(config.udpInstalledBasePath)
      .select('ip, 'census_block_id, 'carrier)
    logger.info(s"Reading Carrier Lookup from ${config.carrierLookupPath}")
    val carrierLookup = spark.read.parquet(config.carrierLookupPath)
      .withColumn("rank", row_number.over(Window.partitionBy('mw_carrier).orderBy('min_date.desc)))
      .filter('rank === 1)
      .select('mw_carrier.as("carrier"), 'sp_platform)
    val householdInput = udpInstalledBase
      .join(carrierLookup, Seq("carrier"), "left")
      .select('ip.as("temp_household_id"), 'census_block_id, 'sp_platform)
      .distinct
    logger.info(s"Writing results to $resultPath")
    householdInput.repartition(25).write.mode(SaveMode.ErrorIfExists).parquet(resultPath)
  }
}
