package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema

import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.common_columns._
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.types._
import com.comlinkdata.largescale.schema.broadband_market_share.{CensusBlockSubmarket, HouseholdEstimate}
import com.comlinkdata.largescale.schema.udp.Household
import org.apache.spark.sql.{SparkSession, Dataset}

case class UdpHousehold(
  household_id: Household,
  county: County,
  census_block_id: CensusBlock,
  sp_platform: String,
  submarket: Submarket,
  latitude: Double,
  longitude: Double
) {
  def toAllocation: Allocation = Allocation(
    household_id = household_id,
    county = county,
    census_block_id = census_block_id,
    target_census_block_id = None,
    sp_platform = sp_platform,
    submarket = submarket,
    latitude = latitude,
    longitude = longitude,
    distance = 0,
    weight_factor = 0,
    weight_allocated = 0,
    weight_allocated_cumulative = 0
  )
}

object UdpHousehold {

  /**
    * Converts raw UDP households to allocation-ready households.
    *
    * Dataset is deduplicated by household_id
    *
    * @return UDP households
    */
  def addSubmarket(
    submarkets: Dataset[CensusBlockSubmarket]
  )(households: Dataset[HouseholdEstimate])(implicit spark: SparkSession): Dataset[UdpHousehold] = {

    import spark.implicits._
    households
      .join(submarkets, Seq(colCensusBlockIdName, colCountyName))
      .select(Seq(
        colSubmarket,
        colLatitude,
        colLongitude) ++ HouseholdEstimate.cols: _*
      ).dropDuplicates(colHouseholdIdName)
      .as[UdpHousehold]
  }
}

