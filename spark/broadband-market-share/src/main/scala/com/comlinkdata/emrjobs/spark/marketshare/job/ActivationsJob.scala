package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.emrjobs.spark.marketshare.Activations
import com.comlinkdata.largescale.commons.io.dataset.overwrite
import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, LocalDateRange}
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband_market_share.BroadbandCellular
import com.comlinkdata.largescale.schema.cellular_only.CellularOnlyDevice
import com.comlinkdata.largescale.schema.udp.installbase.IPHighConfidence
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.SparkSession

import java.net.URI
import java.time.LocalDate

/**
  * Broadband activations job configuration.
  *
  * Requirements:
  * - cellularOnlyLookbackDays > 0
  *
  * Standard business rules:
  * - cellularOnlyLookbackDays = 28
  *
  * @param startDate                start date of processing (optional). If not provided, latest high-confidence date
  *                                 will be taken
  * @param endDate                  processing end date (optional, inclusive). If not provided, only one day will
  *                                 be computed.
  * @param carrierLookupLocation    carrier lookup table location
  * @param highConfidenceLocation   high-confidence location
  * @param cellularOnlyLookbackDays cellular-only lookback days. cellularOnlyDate = startDate - cellularOnlyLookbackDays
  * @param cellularOnlyLocation     cellular-only location
  * @param outputLocation           broadband activations (output) location
  * @param outputPartitions         output number of partitions
  */
case class ActivationsJobConfig(
  startDate: Option[LocalDate],
  endDate: Option[LocalDate],
  carrierLookupLocation: URI,
  carrierLookupFwLocation: URI,
  highConfidenceLocation: URI,
  cellularOnlyLookbackDays: Int,
  cellularOnlyLocation: URI,
  outputLocation: URI,
  outputPartitions: Int
)

object ActivationsJob extends SparkJob[ActivationsJobConfig](ActivationsJobRunner)

object ActivationsJobRunner extends SparkJobRunner[ActivationsJobConfig] with LazyLogging {

  def runJob(config: ActivationsJobConfig)(implicit spark: SparkSession): Unit = {
    require(config.cellularOnlyLookbackDays > 0, "cellularOnlyLookbackDays has to be > 0")

    val hcTsl = IPHighConfidence.tsl(config.highConfidenceLocation)
    val startDate = config.startDate.getOrElse(hcTsl.latestDate)
    val ldr = LocalDateRange(startDate, config.endDate.getOrElse(startDate))
    logger.info(s"Computing Activations for: $ldr")

    val carrierLookup = CarrierLookup
      .read(config.carrierLookupLocation)
      .unionByName(CarrierLookup.read(config.carrierLookupFwLocation))
    val bba = Activations()

    // will ignore non-existing days
    for (processingDate <- ldr if hcTsl.exists(processingDate)) {
      logger.info(s"Processing day: $processingDate")

      val hc = IPHighConfidence.read(config.highConfidenceLocation, processingDate)
      val coDate = processingDate.minusDays(config.cellularOnlyLookbackDays)
      val co = CellularOnlyDevice.read(config.cellularOnlyLocation, coDate)

      val result = bba.runDay(hc, co, carrierLookup)
      overwrite(result, processingDate, config.outputPartitions, BroadbandCellular.tsl(config.outputLocation))
      spark.catalog.clearCache()
    }
  }
}