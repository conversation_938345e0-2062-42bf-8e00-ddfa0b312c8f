package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.largescale.commons.LocalDateRange.LdrSeq
import com.comlinkdata.largescale.commons.RedshiftUtils.{RedshiftConfig, redshiftRead, redshiftWriteDateRange}
import com.comlinkdata.largescale.commons.RichDate.localDateOrdering
import com.comlinkdata.largescale.commons.io.dataset.{findCommonDates, getStartDateWithOverlapping}
import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{Column, DataFrame, Dataset, SparkSession}
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandPlatformAggregate, BroadbandPlatformAggregateWithDs}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.apache.spark.sql.functions.{broadcast, lit, substring, sum, when}
import org.apache.spark.sql.types.IntegerType

import java.net.URI
import java.sql.Date
import java.time.LocalDate

case class BroadbandRedshiftUploadDatasource(
  ds: ComlinkdataDatasource,
  location: URI,
  startDate: LocalDate,
  endDate: Option[LocalDate]
)

/**
  * BroadbandRedshiftUpload job configuration
  *
  * @param startDate                 optional start date
  * @param endDate                   optional end date
  * @param datasources               datasources to upload
  * @param frozenBb17Location        frozen bb 1.7/1.8 data. Can be null
  * @param frozenBb17DateRange       frozen bb 1.7/1.8 date range
  * @param frozenBb20Location        frozen bb 1.7/1.8 data. Can be null
  * @param frozenBb20DateRange       frozen bb 1.7/1.8 date range
  * @param overlappingDays           days before start date to reprocess due to data restating
  * @param redshiftPipelineName      redshift output without oofp filter
  * @param redshiftOofpName          redshift output with oofp filter
  * @param redshiftConfig            redshift configuration
  */
case class RedshiftUploadJobConfig(
  startDate: Option[LocalDate],
  endDate: Option[LocalDate],
  datasources: Array[BroadbandRedshiftUploadDatasource],
  frozenBb17Location: Option[URI],
  frozenBb17DateRange: Option[LocalDateRange],
  frozenBb20Location: Option[URI],
  frozenBb20DateRange: Option[LocalDateRange],
  overlappingDays: Int,
  redshiftPipelineName: String,
  redshiftOofpName: Option[String],
  redshiftConfig: RedshiftConfig
) {
  @transient
  implicit lazy val rc: RedshiftConfig = redshiftConfig
}

object RedshiftUploadJob extends SparkJob[RedshiftUploadJobConfig](RedshiftUploadJobRunner)

object RedshiftUploadJobRunner extends SparkJobRunner[RedshiftUploadJobConfig] with LazyLogging {

  def runJob(config: RedshiftUploadJobConfig)(implicit spark: SparkSession): Unit = {
    import config._

    val (earliestDate, latestDate) = getBoundaryDates(
      config.datasources,
      config.frozenBb17Location,
      config.frozenBb20Location
    )

    val startDate = getStartDateWithOverlapping(config.startDate, config.overlappingDays, earliestDate)
    val endDate = config.endDate.getOrElse(latestDate)

    val ldr = LocalDateRange(startDate, endDate)

    logger.info(s"Processing date range: $ldr")
    val platformAgg = unionPlatformAggregates(
      config.frozenBb17DateRange,
      config.frozenBb17Location,
      config.frozenBb20DateRange,
      config.frozenBb20Location,
      config.datasources
    )(ldr)

    logger.info("Uploading " + platformAgg.count() + " rows")

    redshiftWriteDateRange(platformAgg, config.redshiftPipelineName, Some(ldr))

    config.redshiftOofpName match {
      case Some(tableName) =>
        logger.info("Running oofp filter")
        //todo: AL - This is hardcoded with the intent to remove with 2020 cb project
        val f_syndicated_st_high_confidence = redshiftRead("broadband.f_syndicated_st_high_confidence_2020cb")
        val d_selection_stats_ps = redshiftRead("broadband.d_selection_stats_2020cb")
        val d_sp_id_display_rules_with_st = redshiftRead("broadband.d_sp_id_display_rules")
        val df = platformAgg.transform(oofp_filter(f_syndicated_st_high_confidence, d_selection_stats_ps, d_sp_id_display_rules_with_st))
        redshiftWriteDateRange(df, tableName, Some(ldr))
      case None =>
        logger.info(s"No oofp filtering")
    }
  }

  /**
    * Get the earliest and latest dates given all tsl inputs
    *
    * @param datasources               Map of datasources to their first date they start
    * @param frozenBb20Location        URI of Frozen Platform Aggregate 2.0 (schema without DS)
    * @param frozenBb17Location        URI of Frozen Platform Aggregate 1.7 (very old schema)
    * @param spark                     spark implicit
    * @return A tuple of the earliest and latest possible day of processing
    */
  def getBoundaryDates(
    datasources: Array[BroadbandRedshiftUploadDatasource],
    frozenBb20Location: Option[URI],
    frozenBb17Location: Option[URI]
  )(implicit spark: SparkSession): (LocalDate, LocalDate) = {
    val tslDs = datasources
      .map(_.location)
      .map(BroadbandPlatformAggregate.tsl)

    val tslFrozen = Seq(
      frozenBb20Location,
      frozenBb17Location
    )
      .flatten
      .map(BroadbandPlatformAggregate.tsl)

    val earliestDate = tslDs.union(tslFrozen)
      .map(_.earliestDate)
      .min

    //todo: what happens when quadrant is 1 day behind? min (read all partitions) vs max (read existing partitions)
    val latestDate = tslDs
      .map(_.latestDate)
      .min
    (earliestDate, latestDate)
  }

  def unionPlatformAggregates(
    frozenBb17DateRange: Option[LocalDateRange],
    frozenBb17Location: Option[URI],
    frozenBb20DateRange: Option[LocalDateRange],
    frozenBb20Location: Option[URI],
    datasources: Array[BroadbandRedshiftUploadDatasource]
  )(ldr: LocalDateRange)(implicit spark: SparkSession): Dataset[BroadbandPlatformAggregateWithDs] = {
    import spark.implicits._
    val (frozen17Ldr, frozen17) = getFrozen(ldr, frozenBb17DateRange, frozenBb17Location, BroadbandPlatformAggregateWithDs.readBb17)
    val (frozen20Ldr, frozen20) = getFrozen(ldr, frozenBb20DateRange, frozenBb20Location, BroadbandPlatformAggregateWithDs.read)
    val platformAggDs = datasources
      .map { ds =>
        val dsTsl = BroadbandPlatformAggregateWithDs.tsl(ds.location)
        val dsLdr = findCommonDates(ldr, dsTsl)
          .filterNot(_.isBefore(ds.startDate))
          .filterNot(_.isAfter(ds.endDate.getOrElse(LocalDate.of(9999, 1, 1))))
          .diff(frozen17Ldr)
          .diff(frozen20Ldr)
          .ldrOption
        dsLdr
          .map(BroadbandPlatformAggregate.read(ds.location, _))
          .getOrElse(spark.emptyDataset[BroadbandPlatformAggregate])
          .withColumn("ds", lit(ds.ds.toString))
          .as[BroadbandPlatformAggregateWithDs]
      }
      .reduce(_ unionByName _)
    platformAggDs
      .unionByName(frozen17)
      .unionByName(frozen20)
  }

  /**
    * Helper function to read frozen data with option handling
    * @param ldr Run dates
    * @param frozenLdr Hardcoded dates for the frozen dataset
    * @param location  Location of the frozen data
    * @param readFunction BroadbandPlatformAggregate.read or readBB17 function
    * @return Tuple of days to exclude from ldr and frozen data
    */
  def getFrozen(
    ldr: LocalDateRange,
    frozenLdr: Option[LocalDateRange],
    location: Option[URI],
    readFunction: (URI, LocalDateRange) => Dataset[BroadbandPlatformAggregateWithDs]
  )(implicit spark: SparkSession): (Seq[LocalDate], Dataset[BroadbandPlatformAggregateWithDs]) = {
    import spark.implicits._
    // find the dates that are overlapped
    val intersection: Seq[LocalDate] = frozenLdr match {
      case Some(fLdr) => fLdr.intersect(ldr)
      case None => Seq.empty[LocalDate]
    }

    // map back to local date range for reading function
    val fLdr = intersection.ldrOption

    // Get data with frozen ldr (fLdr)
    val data: Dataset[BroadbandPlatformAggregateWithDs] = fLdr
      .zip(location)
      .headOption
      .map({
        case (ldr, bbXLocation) =>
          logger.info(s"Reading frozen $bbXLocation on $ldr")
          readFunction(bbXLocation, ldr)
      }).getOrElse(spark.emptyDataset[BroadbandPlatformAggregateWithDs])

    // Get sequence of days for diff
    val computedDates: Seq[LocalDate] = fLdr.map(_.toSeq).getOrElse(Seq.empty[LocalDate])

    // return final output
    (computedDates, data)
  }

  def oofp_filter(
    f_syndicated_st_high_confidence: DataFrame,
    d_selection_stats_ps: DataFrame,
    d_sp_id_display_rules_with_st: DataFrame,
  )(df: Dataset[BroadbandPlatformAggregateWithDs])(implicit spark: SparkSession): Dataset[BroadbandPlatformAggregateWithDs] = {
    import spark.implicits._
    val f_syndicated_st_high_confidence_oofp_cb = f_syndicated_st_high_confidence
      .filter($"sp_id" === 6642)
      .select($"census_blockid" as "oofp_cb")
      .distinct()
      .cache()

    def tract(c: Column): Column = substring(c, 1, 11)

    val hc_tract = f_syndicated_st_high_confidence.as("s")
      .join(d_selection_stats_ps.as("p"), $"p.serv_terr_blockid" === $"s.census_blockid", "left")
      .join(broadcast(d_sp_id_display_rules_with_st).as("d"), $"d.sp_dim_id" === $"s.sp_id", "left")
      .groupBy($"d.parent_id" as "parent_id", tract($"s.census_blockid") as "tract")
      .agg(sum($"p.hu") as "hu")
      .filter($"hu" > 0)
      .cache()

    def oofp_column(sp_group: Column, census_block: Column)(df: DataFrame): DataFrame = {
      df.as("b")
        .join(broadcast(d_sp_id_display_rules_with_st).as("d"), $"d.sp_dim_id" === sp_group, "left")
        .join(hc_tract.as("t"), $"t.parent_id" === sp_group and $"t.tract" === tract(census_block), "left")
        .select(
          $"b.*",
          when($"d.is_service_territory_sp" === false, lit(null).cast(IntegerType))
            .when($"t.tract".isNull, lit(1))
            .otherwise(0) as "oofp_" + sp_group.toString()
        )
    }

    val oofp = df.toDF()
      .transform(oofp_column($"primary_sp_group", $"census_blockid"))
      .transform(oofp_column($"secondary_sp_group", $"secondary_census_blockid"))

    def res_case(
      isp_type: Column,
      sp_dim_id: Column,
      oofp_cb: Column,
      oofp_flag: Column,
      the_date: Column,
    ): Column =
      when(
        isp_type === "Municipal"
          and oofp_flag.cast(IntegerType) === 1
          and the_date >= Date.valueOf("2023-04-01"), 1)
      .when(
        isp_type.isin(Seq("ILEC", "MSO", "Rural Co-op"): _*)
          and !sp_dim_id.isin(1107, 4376)
          and oofp_flag.cast(IntegerType) === 1
          and the_date >= Date.valueOf("2023-04-01")
          and oofp_cb.isNull, 1)
      .otherwise(0)

    val res = oofp.as("a")
      .join(broadcast(d_sp_id_display_rules_with_st).as("w"), $"w.sp_dim_id" === $"a.primary_sp_group", "left")
      .join(f_syndicated_st_high_confidence_oofp_cb.as("w_oofp_cb"), $"a.census_blockid" === $"w_oofp_cb.oofp_cb", "left")
      .join(broadcast(d_sp_id_display_rules_with_st).as("l"), $"l.sp_dim_id" === $"a.secondary_sp_group", "left")
      .join(f_syndicated_st_high_confidence_oofp_cb.as("l_oofp_cb"), $"a.secondary_census_blockid" === $"l_oofp_cb.oofp_cb", "left")
      .select(
        $"a.*",
        res_case(
          $"w.isp_type",
          $"w.sp_dim_id",
          $"w_oofp_cb.oofp_cb",
          $"a.oofp_primary_sp_group",
          $"a.the_date"
        ) as "w_oofp",
        res_case(
          $"l.isp_type",
          $"l.sp_dim_id",
          $"l_oofp_cb.oofp_cb",
          $"a.oofp_secondary_sp_group",
          $"a.the_date"
        ) as "l_oofp"
      )

    res
      .filter($"w_oofp" + $"l_oofp" === 0)
      .select(BroadbandPlatformAggregateWithDs.cols: _*)
      .as[BroadbandPlatformAggregateWithDs]
  }
}
