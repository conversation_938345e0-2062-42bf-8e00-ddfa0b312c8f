package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation

import org.apache.spark.sql.{SparkSession, Column, Dataset}
import org.apache.spark.sql.functions._
import com.comlinkdata.largescale.schema.broadband_market_share.{CensusBlockSubmarket, BroadbandPenetrationEstimates}
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.AllocationOwnBlocks._
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.common_columns._
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.types.{CensusBlock, County}
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.{Target, Allocation, UdpHousehold}

/**
  * Own-block allocation:
  *
  * 1. Find weight factor of one UDP household (computed as BB households / UDP households).
  * The "weight" is like a multiplicative factor. If there are 10 census HH and 5 UDP HH, each UDP HH should be
  * counted x2. This factor does not need to be an integer, however, and in fact will almost never be unless
  * census HH/UDP HH is evenly divisible. But, in order to make sure that the final county carrier shares are
  * the same as the starting county carrier shares, each UDP household needs to be counted with the same weight.
  *
  * 2. Identify all UDP households that can give 100% of their weight to their own census block.
  * Any census block where total BB census HH >= sum(UDP HH * weight) meets this criteria
  *
  * 3. Update remaining weights (or capacity) of all census blocks.
  *
  * 4. Store unallocated UDP households, census block capacities and own-block allocated households.
  */
class AllocationOwnBlocks(implicit spark: SparkSession) {

  import spark.implicits._

  def run(
    bbPenetration: Dataset[BroadbandPenetrationEstimates],
    householdEstimates: Dataset[UdpHousehold],
    submarkets: Dataset[CensusBlockSubmarket]
  ): (Dataset[Allocation], Dataset[Allocation], Dataset[Target]) = {
    val weightFactors = computeWeightFactors(bbPenetration, householdEstimates)
    val initialAllocation = householdEstimates.transform(toEmptyAllocation(weightFactors))
    allocateOwnBlocks(bbPenetration, submarkets, initialAllocation)
  }

  def computeWeightFactors(
    bbPenetrationEstimates: Dataset[BroadbandPenetrationEstimates],
    udpHouseholds: Dataset[UdpHousehold]
  ): Dataset[WeightFactor] = {

    val territoryHouseholdsTotal = bbPenetrationEstimates
      .groupBy(colCounty)
      .agg(sum(colHouseholds) as "bb_households")

    val udpHouseholdsTotal = udpHouseholds
      .groupBy(colCounty)
      .agg(countDistinct(colHouseholdId) as "udp_households")

    // we must assign the same weight to all households, otherwise market shares might change
    territoryHouseholdsTotal
      .join(broadcast(udpHouseholdsTotal), Seq(colCountyName))
      .select(colCounty, $"bb_households" / $"udp_households" as colWeightFactorName)
      .as[WeightFactor]
  }

  def allocateOwnBlocks(
    bbPenetrationEstimates: Dataset[BroadbandPenetrationEstimates],
    submarkets: Dataset[CensusBlockSubmarket],
    initialAllocation: Dataset[Allocation]
  ): (Dataset[Allocation], Dataset[Allocation], Dataset[Target]) = {

    val udpHouseholdsPerCensusBlock = initialAllocation.transform(countHouseholdsPerCensusBlock)

    val fullyAllocated = udpHouseholdsPerCensusBlock
      .join(bbPenetrationEstimates, Seq(colCensusBlockIdName))
      .filter(colHouseholds >= ($"households2" * colWeightFactor))
      .drop("households2", colSubmarketName, colWeightFactorName)
      .cache()

    val isAllocated = colHouseholds.isNotNull
    val allocationResult = initialAllocation
      .join(fullyAllocated, Seq(colCensusBlockIdName, colCountyName), "left")
      .withColumn(colWeightAllocatedName, when(isAllocated, colWeightFactor).otherwise(ZERO))
      .withColumn(colWeightAllocatedCumulativeName, when(isAllocated, colWeightFactor).otherwise(ZERO))
      .withColumn(colTargetCensusBlockIdName, when(isAllocated, colCensusBlockId).otherwise(EMPTY_CENSUS_BLOCK))
      .drop(colHouseholdsName)
      .as[Allocation]
      .cache()

    val allocatedWeights = allocationResult.transform(allocatedWeightPerCensusBlock)
    val targets = bbPenetrationEstimates
      .join(submarkets, Seq(colCensusBlockIdName, colCountyName))
      .join(allocatedWeights, Seq(colCensusBlockIdName), "left")
      .select(
        colCounty,
        colCensusBlockId as colTargetCensusBlockIdName,
        colSubmarket as colTargetSubmarketName,
        colLatitude as colTargetLatitudeName,
        colLongitude as colTargetLongitudeName,
        when(colWeightAllocated.isNotNull, colHouseholds - colWeightAllocated)
          .otherwise(colHouseholds) as colCapacityName
      ).as[Target]

    val allocated = allocationResult.filter(r => r.weight_allocated == r.weight_factor)
    val unallocated = allocationResult.filter(r => r.weight_allocated == 0)

    (allocated, unallocated, targets)
  }

  def toEmptyAllocation(weightPerHousehold: Dataset[WeightFactor])(ds: Dataset[UdpHousehold]): Dataset[Allocation] = {
    ds.map(_.toAllocation)
      .drop(colWeightFactorName)
      .join(broadcast(weightPerHousehold), Seq(colCountyName))
      .as[Allocation]
  }

  def allocatedWeightPerCensusBlock(ds: Dataset[Allocation]): Dataset[WeightAllocated] = {
    ds
      .filter(colTargetCensusBlockId.isNotNull)
      .groupBy(colTargetCensusBlockId as colCensusBlockIdName)
      .agg(sum(colWeightAllocated) as colWeightAllocatedName)
      .as[WeightAllocated]
  }

  def countHouseholdsPerCensusBlock(ds: Dataset[Allocation]): Dataset[CensusBlockHouseholds2] = {
    ds.groupBy(colCensusBlockId, colWeightFactor)
      .agg(countDistinct(colHouseholdId) as "households2")
      .as[CensusBlockHouseholds2]
  }
}

object AllocationOwnBlocks {
  val EMPTY_CENSUS_BLOCK: Column = typedLit(Option.empty[CensusBlock])
  val ZERO: Column = typedLit(0.0)

  case class CensusBlockHouseholds2(
    census_block_id: CensusBlock,
    households2: Double,
    weight_factor: Double
  )

  case class WeightFactor(
    county: County,
    weight_factor: Double
  )

  case class WeightAllocated(
    census_block_id: CensusBlock,
    weight_allocated: Double
  )
}