package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.emrjobs.spark.marketshare.job.AggregatedChurnJobRunner.customFunctions
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandChurnWithDimension, BroadbandAggregatedChurn}
import com.comlinkdata.largescale.udp.LocationUtils.distanceBetweenCoordsUDF
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.row_number
import org.apache.spark.sql.{SparkSession, Column, Dataset, DataFrame}

/**
  * AggregatedChurn
  *
  * - takes the latest churn (with the longest window_tail_date)
  * - pivots winner/loser
  *
  * @param spark spark session
  */
class AggregatedChurn(implicit spark: SparkSession) {

  import spark.implicits._

  /**
    * Runs one day of aggregated churn
    *
    * @param bbChurn   churn which includes possibly multiple partition dates eligible for restatement
    * @return aggregated churn for given churn date
    */
  def run(bbChurn: Dataset[BroadbandChurnWithDimension]): Dataset[BroadbandAggregatedChurn] = {
    bbChurn
      .transform(consolidateBroadbandChurn)
      .transform(broadbandChurnPivot)
      .as[BroadbandAggregatedChurn]
  }

  def consolidateBroadbandChurn(ds: Dataset[BroadbandChurnWithDimension]): DataFrame = {
    val window = Window.partitionBy($"ifa", $"churn_date", $"w_or_l").orderBy($"window_tail_date".desc)
    ds
      .withColumn("rank", row_number.over(window))
      .filter($"rank" === 1)
      .select(
        $"ifa",
        $"churn_date",
        $"household_id",
        $"sp_platform",
        $"census_block_id",
        $"lat",
        $"lng"
      )
  }

  /**
    * Pivot churn with dimension data
    *
    * @param df pivoted churn table
    * @return unpivoted churn table with additional columns
    */
  def broadbandChurnPivot(df: DataFrame): Dataset[BroadbandAggregatedChurn] = {

    val colsToPivot = Seq(
      $"churn_date",
      $"household_id",
      $"sp_platform",
      $"census_block_id",
      $"lat",
      $"lng"
    )
    val key = Seq($"ifa", $"churn_date")
    val winnerCols: Seq[Column] = colsToPivot.map(c => c.as(s"${WINNER}_${c.toString()}")) ++ key
    val loserCols: Seq[Column] = colsToPivot.map(c => c.as(s"${LOSER}_${c.toString()}")) ++ key

    val removeSparseData = df.filter(colsToPivot.map(_.isNotNull).reduce(_ and _))
    val winners = removeSparseData.filter($"w_or_l" === WINNER).select(winnerCols: _*)
    val losers = removeSparseData.filter($"w_or_l" === LOSER).select(loserCols: _*)

    winners
      .join(losers, Seq("ifa", "churn_date"))
      .withColumn("household_id", $"winner_household_id")
      .transform(customFunctions.addYmdCols($"churn_date"))
      .withColumn("distance", distanceBetweenCoordsUDF($"winner_lat", $"winner_lng", $"loser_lat", $"loser_lng"))
      .select(BroadbandAggregatedChurn.cols: _*)
      .as[BroadbandAggregatedChurn]
  }
}

object AggregatedChurn {

  def apply()(implicit spark: SparkSession): AggregatedChurn = new AggregatedChurn()
}