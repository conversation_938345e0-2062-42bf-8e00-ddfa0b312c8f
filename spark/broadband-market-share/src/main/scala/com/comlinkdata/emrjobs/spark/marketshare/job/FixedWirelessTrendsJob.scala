package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.emrjobs.spark.marketshare.FixedWirelessTrends
import com.comlinkdata.largescale.commons.io.dataset.overwrite
import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, LocalDateRange}
import com.comlinkdata.largescale.schema.FwaTrends
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.FixedWirelessSubsOutput
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.FixedWirelessSpid
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.SparkSession

import java.net.URI
import java.time.LocalDate

/**
  * Fixed-wireless growth configuration.
  *
  * @param startDate                 start date of processing (optional). If not provided, latest fw-subscribers date
  *                                  will be taken
  * @param endDate                   processing end date (optional, inclusive). If not provided, only one day will
  *                                  be computed.
  * @param fwSubscribersLookbackDays fw-subscribers lookback days (usually 90)
  * @param fwSubscribersLocation     fw-subscribers location
  * @param spidLookupLocation        fixed-wireless SPID lookup table location
  * @param outputLocation            fixed-wireless trends output location
  * @param outputPartitions          output number of partitions
  */
case class FixedWirelessTrendsJobConfig(
  startDate: Option[LocalDate],
  endDate: Option[LocalDate],
  fwSubscribersLookbackDays: Int,
  fwSubscribersLocation: URI,
  spidLookupLocation: URI,
  outputLocation: URI,
  outputPartitions: Int
)

object FixedWirelessTrendsJob extends SparkJob[FixedWirelessTrendsJobConfig](FixedWirelessTrendsJobRunner)

object FixedWirelessTrendsJobRunner extends SparkJobRunner[FixedWirelessTrendsJobConfig] with LazyLogging {

  def runJob(config: FixedWirelessTrendsJobConfig)(implicit spark: SparkSession): Unit = {
    val fwTsl = FixedWirelessSubsOutput.tsl(config.fwSubscribersLocation)
    val startDate = config.startDate.getOrElse(fwTsl.latestDate)
    val ldr = LocalDateRange(startDate, config.endDate.getOrElse(startDate))
    logger.info(s"Computing fixed-wireless growth for $ldr")

    val fwSpidLookup = FixedWirelessSpid.read(config.spidLookupLocation)
    val fwaTrends = FixedWirelessTrends()

    for (processingDate <- ldr if fwTsl.exists(processingDate)) {
      logger.info(s"Processing day: $processingDate")

      val fwSubsStartDate = processingDate minusDays config.fwSubscribersLookbackDays
      val fwSubsStart = FixedWirelessSubsOutput.read(config.fwSubscribersLocation, fwSubsStartDate)
      val fwSubsEnd = FixedWirelessSubsOutput.read(config.fwSubscribersLocation, processingDate)

      val result = fwaTrends.runDay(fwSpidLookup, fwSubsStart, fwSubsEnd)
      overwrite(result, processingDate, config.outputPartitions, FwaTrends.tsl(config.outputLocation))
    }
  }
}
