package com.comlinkdata.emrjobs.spark

import org.apache.spark.sql.Column
import org.apache.spark.sql.functions.substring

import java.time.LocalDate

package object marketshare {
  final val WINNER = "winner"
  final val LOSER = "loser"

  final val VERIZON_FIXED_WIRELESS = "Verizon Fixed Wireless"
  final val TMOBILE_FIXED_WIRELESS = "T-Mobile Home Internet"

  final val FWA_TMO_SP_ID = 6713
  final val FWA_VZW_SP_ID = 6715
  final val FWA_SP_IDS = Seq(FWA_TMO_SP_ID, FWA_VZW_SP_ID, 6730)

  final val FWA_TMO_START_DATE = LocalDate.of(2021, 1, 1)
  final val FWA_VZW_START_DATE = LocalDate.of(2021, 1, 1)

  final val FWA_TMO_DEVICE_TYPES = Seq(
    "connected device",
    "connected tv",
    "desktop",
    "external tv device (connected device)",
    "game console (set-top box)",
    "games console",
    "personal computer",
    "set top box",
    "smart tv (connected tv)",
    "tablet",
    "tv",
    "2", // Gamoshi: Personal Computer
    "3", // Gamoshi: Connected TV
    "5", // Gamoshi: Tablet
    "6", // Gamoshi: Connected Device
    "7", // Gamoshi: Set Top Box
  )

  object implicits {

    implicit class ColumnOps(c: Column) {

      def tract: Column = substring(c, 1, 11)
    }
  }
}
