package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.emrjobs.spark.marketshare.{FWA_TMO_DEVICE_TYPES, IfaIpCarrierFwa}
import com.comlinkdata.largescale.commons.RichDate.localDateOrdering
import com.comlinkdata.largescale.commons.io.dataset.findDaysToCompute
import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner, TimeSeriesLocation, UriDFTableRW}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIpWithDateRange.implicits.VerizonFwIpWithDateRangeOps
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandIfaIpCarrier, IfaIpCarrierNoDate}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.{VerizonFwIp, VerizonFwIpWithDateRange}
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.tier2.{IfaSet, UdpIfaIpAgg}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Dataset, SaveMode, SparkSession}

import java.net.URI
import java.time.LocalDate
import scala.util.Try

/**
  * IFA-IP-Carrier-FWA job configuration.
  *
  * @param startDate            starting date
  * @param endDate              end date (optional)
  * @param datasources          list of input data sources
  * @param verizonFwIpLocation  Verizon fixed-wireless IPs lookup table location
  * @param ifaIpCarrierLocation IFA-IP-carrier 1.8 location
  * @param ifaIpAggLocation     UDP IFA-IP aggregate location
  * @param ifaSetLocation       IFA Set location
  * @param outputLocation       IFA-home carrier (output) location
  * @param outputPartitions     output number of partitions
  */
case class IfaIpCarrierFwaJobConfig(
  startDate: Option[LocalDate],
  endDate: Option[LocalDate],
  datasources: Seq[ComlinkdataDatasource],
  baseIfaDatasource: ComlinkdataDatasource,
  verizonFwIpLocation: URI,
  ifaIpCarrierLocation: URI,
  ifaIpAggLocation: URI,
  ifaSetLocation: URI,
  outputLocation: URI,
  outputPartitions: Option[Int]
)

object IfaIpCarrierFwaJob extends SparkJob[IfaIpCarrierFwaJobConfig](IfaIpCarrierFwaJobRunner)

object IfaIpCarrierFwaJobRunner extends SparkJobRunner[IfaIpCarrierFwaJobConfig] with LazyLogging {

  def runJob(config: IfaIpCarrierFwaJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    implicit val tslOut: TimeSeriesLocation = BroadbandIfaIpCarrier.tsl(config.outputLocation)

    val runner = IfaIpCarrierFwa()
    for (ds <- config.datasources) {
      val tslInIfaIpCarrier = BroadbandIfaIpCarrier.tsl(config.ifaIpCarrierLocation, ds)
      val tslInIfaIpAgg = UdpIfaIpAgg.tsl(config.ifaIpAggLocation, Seq(ds))
      val tslOut: TimeSeriesLocation = BroadbandIfaIpCarrier.tsl(config.outputLocation, ds)
      val daysToRun = findDaysToCompute(
        minOutputDate = None,
        startDate = config.startDate,
        endDate = config.endDate,
        overwrite = false,
        tslInIfaIpCarrier +: tslInIfaIpAgg: _*
      )(tslOut)

      if (daysToRun.isEmpty) {
        logger.info("Nothing to do, exiting")
        return
      }
      logger.info(s"Processing $ds, Computing days: $daysToRun")

      // cannot filter TMO carrier here, we need IFA-IP agg because of maxmind
      // Later use base ifa set for matches first, then fill with matches from matching ifa sets
      val ifaSetFilteredBase = IfaSet
        .readLatest(config.ifaSetLocation, config.baseIfaDatasource)._2
        .filter(lower($"device_type") =!= "smart phone") // filter out bulk
        .filter(size($"days") > 0)
        .filter(array_min($"days") <= daysToRun.max) // older or equal to max processing day
        .filter(lower($"device_type").isin(FWA_TMO_DEVICE_TYPES: _*))
        .select($"ifa")
        .distinct()
        .as[Ifa]
        .cache()

      // Uses base ifa set as a baseline for ifas, backfills with ds specific ifa set if needed
      val ifaSetFilteredDS = ds match {
        case config.baseIfaDatasource => spark.emptyDataset[Ifa]
        case thisDs => Try(IfaSet
          .readLatest(config.ifaSetLocation, thisDs)._2
          .filter(lower($"device_type") =!= "smart phone") // filter out bulk
          .filter(
            if(thisDs == ComlinkdataDatasource.gamoshi) lower($"device_type") =!= "4"
            else lower($"device_type") === lower($"device_type")
          ) // filter device type = 4 for just gamoshi
          .filter(size($"days") > 0)
          .filter(array_min($"days") <= daysToRun.max) // older or equal to max processing day
          .filter(lower($"device_type").isin(FWA_TMO_DEVICE_TYPES: _*))
          .select($"ifa")
          .distinct()
          .as[Ifa]
          .cache()).getOrElse(spark.emptyDataset[Ifa])
        case _ => spark.emptyDataset[Ifa]
      }

      // really small ~ around 60 records every month and slim columns
      val verizonIpBlocksRange: Dataset[VerizonFwIpWithDateRange] = VerizonFwIpWithDateRange
        .read(config.verizonFwIpLocation)
        .cache()


      for (day <- daysToRun) {
        logger.info(s"Running daily ifa home carrier for date: $day")

        val ifaIpCarrier = BroadbandIfaIpCarrier.read(config.ifaIpCarrierLocation, day, Seq(ds))
        val ifaIpAgg = UdpIfaIpAgg.read(config.ifaIpAggLocation, date = day, Seq(ds))

        val verizonIpBlocks = verizonIpBlocksRange.getDay(day)
        val ifaIpCarrierFwa = runner.runDay(
          ifaIpCarrier.filter($"suspiciousIp" === false),
          ifaIpAgg,
          verizonIpBlocks,
          ifaSetFilteredBase,
          ifaSetFilteredDS
        )

        val dest = tslOut.validOutputPartitionOrThrow(day)
        logger.info(f"Writing to $dest")

        ifaIpCarrierFwa
          .drop("ds", "year", "month", "day")
          .repartition(config.outputPartitions.getOrElse(10))
          .write
          .mode(SaveMode.ErrorIfExists)
          .parquet(dest)

        logger.info(s"Wrote ${ifaIpCarrierFwa.count()} records for ds $ds")
      }
    }
  }
}
