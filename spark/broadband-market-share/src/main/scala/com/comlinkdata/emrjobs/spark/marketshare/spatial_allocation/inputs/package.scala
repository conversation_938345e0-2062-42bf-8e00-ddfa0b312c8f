package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation

import java.time.LocalDate
import org.apache.spark.sql.functions.{coalesce, lit, sum, when}
import org.apache.spark.sql.types.DoubleType
import org.apache.spark.sql.{DataFrame, SparkSession}

package object inputs {
  def getFirstDate(d: LocalDate): LocalDate =
    d.withMonth(d.getMonthValue - (d.getMonthValue - 1) % 3).withDayOfMonth(1)

  def getHouseholdBlockGroups(hhBlockGroupPath: String)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    spark.read.option("header", "true")
      .csv(hhBlockGroupPath)
      .filter('year === "2019")
      .select('geoid, 'uniqueid, 'estimate)
  }

  def getGeoidCensusPopStatsLookup(geoidCensusPopstatsLookupPath: String)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    spark.read.option("header", "true").csv(geoidCensusPopstatsLookupPath)
      .select(
        'acs_2017_bgid.as("geoid"),
        'serv_terr_bgid)
  }

  def getSatellite(householdBlockGroups: DataFrame, geoidCensusPopStatsLookup: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    householdBlockGroups
      .join(geoidCensusPopStatsLookup, Seq("geoid"), "left")
      .select(
        coalesce('serv_terr_bgid, 'geoid).as("serv_terr_bgid"),
        when('uniqueid === "B28002_004", 'estimate).otherwise(lit(0)).as("B28002_004"),
        when('uniqueid === "B28002_006", 'estimate).otherwise(lit(0)).as("B28002_006"),
        when('uniqueid === "B28002_010", 'estimate).otherwise(lit(0)).as("B28002_010"))
      .groupBy('serv_terr_bgid)
      .agg(
        (sum('B28002_004) - sum('B28002_006)).as("bb_hh_5y_2019"),
        sum('B28002_010).as("sat_hh_5y_2019"))
      .select(
        'serv_terr_bgid,
        when('bb_hh_5y_2019 === 0, lit(0.0)).otherwise('sat_hh_5y_2019 * 1.0 / 'bb_hh_5y_2019).as("sat_pct_5y_2019"))
  }

  def getSatellite(householdBlockGroups: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    // geoid: string, B28002_004: int, B28002_006: int, B28002_010: int
    val satellitePrep = householdBlockGroups
      .filter($"year" === "2021" && $"acs" === "5_year")
      .select(
        $"geoid" as "cbg",
        when($"uniqueid" === "B28002_004", $"estimate").otherwise(lit(0)) as "B28002_004",
        when($"uniqueid" === "B28002_006", $"estimate").otherwise(lit(0)) as "B28002_006",
        when($"uniqueid" === "B28002_010", $"estimate").otherwise(lit(0)) as "B28002_010")
    // cbg: string, sat_pct_5y_2019: double
    satellitePrep
      .groupBy($"cbg")
      .agg(
        sum($"B28002_004") - sum($"B28002_006") as "bb_hh",
        sum($"B28002_010").cast(DoubleType) as "sat_hh")
      .select(
        $"cbg",
        when($"bb_hh" === 0, lit(0.0)).otherwise($"sat_hh" / $"bb_hh") as "sat_pct")
  }
  object PenetrationCommons {
    def calculateSatelliteCensusBlocks(
      firstPartyFootprint: Option[DataFrame],
      penetrationModel: DataFrame,
      householdBlockGroups: DataFrame)(implicit spark: SparkSession) = {
      import spark.implicits._
      // cbg: string, sat_pct: double
      val satellite = getSatellite(householdBlockGroups)
      val penetrationExpr = when($"block_total" === 0.0, lit(0.0)).otherwise($"block_bb_total" / $"block_total")
      // block: string, block_total: double, block_bb_total: double, block_penetration: double
      val blockPenetration = penetrationModel
        .select(
          $"block",
          $"block_total",
          $"block_bb_total",
          penetrationExpr as "block_penetration")
      firstPartyFootprint match {
        case Some(footprint) =>
          // cbg: string, cbg_penetration: double
          val cbgpPenetration = penetrationModel
            .groupBy($"block".substr(1, 12) as "cbg")
            .agg(sum($"block_total") as "block_total", sum($"block_bb_total") as "block_bb_total")
            .select($"cbg", penetrationExpr.as("cbg_penetration"))
          // block: string, cbg: string, hsd_hp: long, hsd_subs: double, bb_hsd_hp: double
          val penetrationAdjustedFootprint = footprint
            .select(
              $"censusblock" as "block",
              $"censusblock".substr(1, 12) as "cbg",
              $"hsd_hp".cast(DoubleType) as "hsd_hp",
              $"hsd_subs".cast(DoubleType) as "hsd_subs")
            .join(blockPenetration, Seq("block"), "left")
            .join(cbgpPenetration, Seq("cbg"), "left")
            .withColumn("hsd_hp_subs", when($"hsd_subs" > $"hsd_hp", $"hsd_subs").otherwise($"hsd_hp"))
            .select(
              $"block",
              $"cbg",
              $"hsd_hp_subs" as "hsd_hp",
              $"hsd_subs",
              $"hsd_hp_subs" *
                when($"hsd_subs" > $"hsd_hp", lit(1.0))
                  .when($"block_total" === 0.0, $"cbg_penetration")
                  .otherwise($"block_penetration") as "bb_hsd_hp")
          // block: string, cbg: string, block_total: long, block_bb_total: double, block_bb_total_fp: double
          val penetrationAdjustedFootprintLessFirstPartySubs = penetrationAdjustedFootprint
            .select(
              $"block",
              $"cbg",
              $"hsd_hp".cast(DoubleType) as "block_total",
              when($"hsd_subs" > $"bb_hsd_hp", $"hsd_subs").otherwise($"bb_hsd_hp") as "block_bb_total_fp",
              when($"hsd_subs" > $"bb_hsd_hp", lit(0.0)).otherwise($"bb_hsd_hp" - $"hsd_subs") as "block_bb_total")
          // block: string, cbg: string, county: string, state: string, block_total: double, block_bb_total: double, sat_subs: double
          penetrationAdjustedFootprintLessFirstPartySubs
            .join(satellite, Seq("cbg"), "left")
            .select(
              $"block",
              $"cbg",
              $"block".substr(1, 5) as "county",
              $"block".substr(1, 2) as "state",
              $"block_total",
              $"block_bb_total",
              $"sat_pct" * $"block_bb_total_fp" as "sat_subs")
        case _ =>
          // block: string, cbg: string, county: string, state: string, block_total: double, block_bb_total: double, sat_subs: double
          blockPenetration
            .withColumn("cbg", $"block".substr(1, 12))
            .join(satellite, Seq("cbg"), "left")
            .select(
              $"block",
              $"cbg",
              $"block".substr(1, 5) as "county",
              $"block".substr(1, 2) as "state",
              $"block_total",
              $"block_bb_total",
              $"sat_pct" * $"block_bb_total" as "sat_subs")
      }
    }
  }
}
