package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs

import com.comlinkdata.largescale.commons.RedshiftUtils.RedshiftConfig
import com.comlinkdata.largescale.commons._
import com.typesafe.scalalogging.LazyLogging

import java.net.URI
import java.time.LocalDate
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions.lit

case class SubscribersRedshiftUploadConfig(
  date: Option[LocalDate],
  subscribersLocation: URI,
  redshiftTable: String,
  redshiftConfig: RedshiftConfig
) {
  @transient
  implicit val rc: RedshiftConfig = redshiftConfig
}

object SubscribersRedshiftUploadJob extends SparkJob(SubscribersRedshiftUploadRunner)

object SubscribersRedshiftUploadRunner extends SparkJobRunner[SubscribersRedshiftUploadConfig] with LazyLogging {

  override def runJob(config: SubscribersRedshiftUploadConfig)(implicit spark: SparkSession): Unit = {
    import config._

    val inputTsl = TimeSeriesLocation.ofDatePartitions(config.subscribersLocation).build
    val inputDate = config.date.getOrElse(inputTsl.latestInputPartition)

    val ldr = LocalDateRange(inputDate, inputDate.plusMonths(3).minusDays(1))

    val result = spark.read.parquet(inputTsl.partition(inputDate))
      .withColumn("the_date", lit(inputDate))

    logger.info("Writing data to Redshift")
    RedshiftUtils.redshiftWriteDateRange(result, redshiftTable, Some(ldr))
  }
}
