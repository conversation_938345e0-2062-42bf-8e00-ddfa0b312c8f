package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs

import com.comlinkdata.largescale.commons.RedshiftUtils.RedshiftConfig
import com.comlinkdata.largescale.commons.{LocalDateRange, RedshiftUtils, SparkJob, SparkJobRunner}
import com.typesafe.scalalogging.LazyLogging
import java.net.URI
import java.time.LocalDate
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._

case class PenetrationRedshiftConfig(
  penetrationCountsLocation: URI,
  redshiftTable: String,
  redshiftConfig: RedshiftConfig,
  period: String,
  replaceHistory: Boolean
) {
  @transient
  implicit val rc: RedshiftConfig = redshiftConfig
}

object PenetrationRedshiftJob extends SparkJob(PenetrationRedshiftRunner)

object PenetrationRedshiftRunner extends SparkJobRunner[PenetrationRedshiftConfig] with LazyLogging {
  private val periodPattern = """([0-9]{4})q([1-4])""".r

  override def runJob(config: PenetrationRedshiftConfig)(implicit spark: SparkSession): Unit = {
    import config.rc

    val (year, quarter) = config.period match {
      case periodPattern(y, q) =>
        (y.toInt, q.toInt)
      case _ =>
        scala.sys.error("Period should be in the format {YYYY}q{N}, where YYYY is 4-digit year, and N is quarter (1-4)")
    }
    val quarterStartDate = LocalDate.of(year, quarter * 3 - 2, 1).plusMonths(3)
    logger.info(s"Running for year $year quarter $quarter with start date $quarterStartDate")

    import spark.implicits._

    val penetrationCounts = spark.read
      .parquet(config.penetrationCountsLocation.toString)
      .withColumn("block_group", $"block".substr(1, 12))
      .groupBy($"block_group")
      .agg(
        sum($"block_total") as "total_housing_units",
        sum($"block_bb_total") as "broadband_housing_units")
      .withColumn("record_year", lit(year.toString))
      .withColumn("quarter_end_date", lit(quarterStartDate))
    logger.info("Writing data to Redshift")
    RedshiftUtils.redshiftWriteDateRange(penetrationCounts, config.redshiftTable, dateColumnName = "quarter_end_date",
      ldrOpt = if (config.replaceHistory) Some(LocalDateRange.of(quarterStartDate.minusMonths(3), quarterStartDate)) else None)
  }
}
