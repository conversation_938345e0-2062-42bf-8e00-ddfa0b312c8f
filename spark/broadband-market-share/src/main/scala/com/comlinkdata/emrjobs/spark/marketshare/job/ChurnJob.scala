package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.emrjobs.spark.marketshare.Churn
import java.sql.Date
import java.time.LocalDate
import com.comlinkdata.largescale.commons.parsing.JsonUtils
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.commons.io.dataset.findDaysToCompute
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandChurn, IfaHomeCarrier}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql._
import org.apache.spark.sql.types.ShortType

import java.net.URI

case class ChurnConfig(
  evaluationWindowSize: Int,
  minimalPeriodNights: Int,
  minimalCarrierTenancy: Int,
  maximumDaysBetweenCarriers: Int,
  rawDataFirstDate: LocalDate,
  carrierLookupLocation: URI,
  carrierLookupFwLocation: URI,
  ifaHomeCarrierLocation: URI,
  outputLocation: URI,
  startDateOpt: Option[LocalDate],
  endDateOpt: Option[LocalDate]
)

/**
  * Artificial churn schema based off carrier lookup to identify artificial churns
  * There are 2 possible types of artificial churn:
  *   1. (should be fixed) Incorrectly recomputed ifa-home-carrier-aggregated (can be potentially removed if we calculate ifaCarrierAgg on the fly)
  *      2. Having two consolidated ids for the same mw_carrier in carrier lookup table, and should be preserved
  *
  * @param starting_id original sp_id
  * @param ending_id   new sp_id
  * @param change_date date that original switched to new id
  */
case class ArtificialChurn(
  starting_id: Int,
  ending_id: Int,
  change_date: Date
)

object ChurnJob extends SparkJob(ChurnRunner)

object ChurnRunner extends SparkJobRunner[ChurnConfig] with LazyLogging {

  /**
    * Runs churn for a given set of days and exclude days that already have an output. Days are calculated by:
    * 1. If both a start date and end date are specified, run the processing for each day within that range where
    * churn destination in s3 doesn't already contain data.
    * 2. If a start or end date is specified without the other, throw an exception.
    * 3. If neither are provided, run from the most recent churn day to last day in s3IfaHomeAggregatedCarrierLocation
    *
    * ** NOTE: this is not efficient, it runs the existing algorithm in a loop, which means there is a massive
    * amount of potential overlap of data, and it reads it independently each time.
    */
  def runJob(config: ChurnConfig)(implicit spark: SparkSession): Unit = {
    val tslIfaHomeCarrier = IfaHomeCarrier.tsl(config.ifaHomeCarrierLocation)

    implicit val tsOut: TimeSeriesLocation = BroadbandChurn.tsl(config.outputLocation)
    val daysToCompute = findDaysToCompute(
      minOutputDate = Some(config.rawDataFirstDate),
      startDate = config.startDateOpt,
      endDate = config.endDateOpt,
      overwrite = false,
      tslIfaHomeCarrier
    )

    logger.info(s"Computing days: $daysToCompute")

    val carrierLookup = CarrierLookup
      .read(config.carrierLookupLocation)
      .unionByName(CarrierLookup.read(config.carrierLookupFwLocation))

    val artificialChurnLookup = findArtificialChurns(carrierLookup).cache()

    val configsToRun = daysToCompute.map {
      day =>
        config.copy(startDateOpt = Some(day.minusDays(config.evaluationWindowSize - 1)), endDateOpt = Some(day))
    }

    val bbChurnAlgo = Churn()
    val configCount = configsToRun.length
    for ((config, i) <- configsToRun.zipWithIndex) {
      logger.info(s"Executing run ${i + 1}/$configCount with configuration ${JsonUtils.toJson(config, pretty = true)}")
      val (_, duration) = Utils timer {
        val ldr = LocalDateRange(config.startDateOpt.get, config.endDateOpt.get)
        val result = bbChurnAlgo.runLoop(
          ldr = ldr,
          getIfaHomeCarrier = IfaHomeCarrier.read(config.ifaHomeCarrierLocation, _),
          minimalPeriodNights = config.minimalPeriodNights,
          minimalCarrierTenancy = config.minimalCarrierTenancy,
          maximumDaysBetweenCarriers = config.maximumDaysBetweenCarriers,
          artificialChurnLookup = artificialChurnLookup,
          carrierLookup = carrierLookup
        )

        val writeLocation = tsOut.partition(config.endDateOpt.get)
        logger warn s"Writing output to $writeLocation."

        UriDFTableRW
          .fromStr(writeLocation)
          .writeWithHistory(
            result.repartition(1).toDF(),
            SaveMode.Overwrite,
            Seq(config))
      }
      logger.info(s"Completed run ${i + 1}/$configCount with elapsed time $duration.")
    }
  }

  /**
    * Finds artificial churns from carrier lookup table.
    *
    * By considering only sp_id for detecting churns, we have for the same mw_carrier (no real churn) two
    * sp_ids (appears to be a churn). So the fix is to create “anti-churn” table which has a list of the
    * sp_id pairs which actually represent the same carrier, thus should not be considered as churn
    *
    * mw_carrier | sp_id | min_date
    * -----------+-------+------------
    * A          | 1     | 2016-01-01
    * A          | 2     | 2021-11-10      <-- this row was added
    * Creating ArtificialChurnLookup:
    * starting_id | ending_id | change_date
    * ------------+-----------+------------
    * 1           | 2         | 2021-11-10
    *
    * @param carrierLookup carrier lookup table
    * @param spark         spark session
    * @return artificial churns
    */
  def findArtificialChurns(carrierLookup: Dataset[CarrierLookup])(implicit spark: SparkSession): Dataset[ArtificialChurn] = {
    import spark.implicits._
    val cteWindow = Window.partitionBy($"mw_carrier").orderBy($"min_date".asc)
    carrierLookup
      .withColumn("min_date", to_date($"min_date"))
      .withColumn("ending_id", lead($"sp", 1).over(cteWindow))
      .withColumn("change_date", lead($"min_date", 1).over(cteWindow))
      .filter($"ending_id".isNotNull) // replicate inner join
      .select(
        $"sp" cast ShortType as "starting_id",
        $"ending_id" cast ShortType,
        $"change_date")
      .dropDuplicates("starting_id", "ending_id")
      .as[ArtificialChurn]
  }
}