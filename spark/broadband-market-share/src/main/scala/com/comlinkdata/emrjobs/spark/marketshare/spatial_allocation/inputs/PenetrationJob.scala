package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs

import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs.PenetrationCommons.calculateSatelliteCensusBlocks
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.{SparkJob, SparkJobRunner, TimeSeriesLocation}
import com.typesafe.scalalogging.LazyLogging
import java.net.URI
import java.time.LocalDate
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{DoubleType, IntegerType}
import org.apache.spark.sql.{DataFrame, SaveMode, SparkSession}

case class PenetrationConfig(
  penetrationModelPath: String,
  householdBlockGroupsPath: String,
  industryReportedNumbers: String,
  fixedWirelessSubsPath: URI,
  fixedWirelessSpidsPath: String,
  firstPartyFootprintPath: Option[String],
  satelliteCensusBlockResultPath: URI,
  fwaByCarrierResultPath: URI,
  penetrationResultPath: URI,
  date: LocalDate
)

object PenetrationJob extends SparkJob(PenetrationRunner)

object PenetrationRunner extends SparkJobRunner[PenetrationConfig] with LazyLogging {
  private def calculateFwaByCarrier(
    fwaSource: DataFrame,
    industryReportedNumbers: DataFrame,
    fwaSPIDs: DataFrame)(implicit spark: SparkSession) = {
    import spark.implicits._
    // carrier: string, observed_fwa_subs: double
    val fwaDenominator = fwaSource
      .groupBy($"carrier")
      .agg(sum($"num_networks".cast(DoubleType)) as "observed_fwa_subs")
    val fwaNumeratorInput = industryReportedNumbers
      .select($"verizon_fwa".cast(DoubleType), $"tmobile_fwa".cast(DoubleType), $"uscc_fwa".cast(DoubleType), $"att_fwa".cast(DoubleType))
      .as[(Option[Double], Option[Double], Option[Double], Option[Double])].collect.head
    // reported_fwa_subs: double, carrier: string
    val fwaNumerator = Seq(
      (fwaNumeratorInput._1, "Verizon"),
      (fwaNumeratorInput._2, "T-Mobile"),
      (fwaNumeratorInput._3, "US Cellular"),
      (fwaNumeratorInput._4, "AT&T")
    ).toDF("reported_fwa_subs", "carrier")
    // carrier: string, fwa_multiplier: double
    val fwaMultipliers = fwaDenominator
      .join(fwaNumerator, "carrier")
      .select(
        $"carrier",
        $"reported_fwa_subs" * 1000.0 / $"observed_fwa_subs" as "fwa_multiplier")
    val fixedWirelessSPIDs = fwaSPIDs
      .withColumnRenamed("tutela_carrier", "carrier")
    // block: string, sp_platform: int, fwa_subs: double
    fwaSource
      .join(fwaMultipliers, Seq("carrier"), "left")
      .join(fixedWirelessSPIDs, "carrier")
      .filter(coalesce($"num_networks", lit(0)) =!= 0)
      .groupBy($"sp_id" as "sp_platform", $"block".substr(1, 15) as "block")
      .agg(sum($"num_networks" * coalesce($"fwa_multiplier", lit(1.0))) as "fwa_subs")
  }

  override def runJob(config: PenetrationConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    val penetrationResultPath = TimeSeriesLocation.ofDatePartitions(config.penetrationResultPath).build
      .partition(config.date)
    val satelliteCensusBlockResultPath = TimeSeriesLocation.ofDatePartitions(config.satelliteCensusBlockResultPath).build
      .partition(config.date)
    val fwaByCarrierResultPath = TimeSeriesLocation.ofDatePartitions(config.fwaByCarrierResultPath).build
      .partition(config.date)
    val firstPartyFootprint = config.firstPartyFootprintPath.map {
      spark.read.parquet(_).select(
        $"censusblock",
        $"hsd_hp".cast(IntegerType) as "hsd_hp",
        $"hsd_subs".cast(IntegerType) as "hsd_subs")
    }
    // block: string, cbg: string, block_total: double, block_bb_total: double, sat_subs: double
    val satelliteCensusBlocks = calculateSatelliteCensusBlocks(
      firstPartyFootprint,
      spark.read.parquet(config.penetrationModelPath),
      spark.read.parquet(config.householdBlockGroupsPath))
    logger.info(s"Writing satellite census block results into $satelliteCensusBlockResultPath")
    satelliteCensusBlocks.coalesce(10).write.mode(SaveMode.Overwrite).parquet(satelliteCensusBlockResultPath)
    // block: string, sp_platform: int, fwa_subs: double
    val fwaByCarrier = calculateFwaByCarrier(
      spark.read.parquet(config.fixedWirelessSubsPath.toString),
      spark.read.option("header", "true").csv(config.industryReportedNumbers)
        .filter(to_date($"quarter_end_date", "M/d/yy") === config.date.toDate),
      spark.read.parquet(config.fixedWirelessSpidsPath))
    logger.info(s"Writing FWA by carrier results into $fwaByCarrierResultPath")
    fwaByCarrier.coalesce(10).write.mode(SaveMode.Overwrite).parquet(fwaByCarrierResultPath)
    // block: string, fwa_subs: double
    val fwa = fwaByCarrier
      .groupBy($"block")
      .agg(sum($"fwa_subs") as "fwa_subs")
    val penetration = satelliteCensusBlocks
      .join(fwa, Seq("block"), "left")
      .withColumn("fwa_subs", coalesce($"fwa_subs", lit(0.0)))
      .select(
        $"block",
        $"cbg" as "block_group",
        $"block_total",
        when($"block_bb_total" === 0.0, lit(0.0))
          .when($"block_bb_total" - $"sat_subs" - $"fwa_subs" < 0.0, lit(0.0))
          .otherwise($"block_bb_total" - $"sat_subs" - $"fwa_subs") as "block_bb_total")
    logger.info(s"Writing penetration results into $penetrationResultPath")
    penetration.coalesce(10).write.mode(SaveMode.Overwrite).parquet(penetrationResultPath)
  }
}
