package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.emrjobs.spark.marketshare.implicits.ColumnOps
import com.comlinkdata.largescale.schema.FwaTrendsNoDate
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.FixedWirelessSubsOutput
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.FixedWirelessSpid
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.FixedWirelessSpid.implicits.FixedWirelessSpidOps
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SparkSession, Dataset, DataFrame}

/**
  * Fixed-wireless trends between two date periods.
  *
  * @param spark spark session
  */
class FixedWirelessTrends()(implicit spark: SparkSession) {

  import spark.implicits._

  /**
    * Computes fixed-wireless growth by comparing two time periods of FW subscribers.
    *
    * @param spidLookup FW SPID lookup table
    * @param fwaQ1      FW subscribers on Q1
    * @param fwaQ2      FW subscribers on Q2
    * @return FW growth
    */
  def runDay(
    spidLookup: Dataset[FixedWirelessSpid],
    fwaQ1: Dataset[FixedWirelessSubsOutput],
    fwaQ2: Dataset[FixedWirelessSubsOutput]
  ): Dataset[FwaTrendsNoDate] = {
    val subsQ1 = countSubscribers(spidLookup, fwaQ1, "q1_")
    val subsQ2 = countSubscribers(spidLookup, fwaQ2, "q2_")

    subsQ1
      .join(subsQ2, Seq("tract", "sp_id"), "fullouter")
      .select(
        $"sp_id",
        $"tract",
        coalesce($"q2_subs" - $"q1_subs", lit(0)) as "growth")
      .as[FwaTrendsNoDate]
  }

  def countSubscribers(
    spidLookup: Dataset[FixedWirelessSpid],
    fw: Dataset[FixedWirelessSubsOutput],
    prefix: String
  ): DataFrame = {
    fw.transform(spidLookup.addTutelaSpId($"carrier"))
      .groupBy($"block".tract as "tract", $"sp_id")
      .agg(sum($"num_networks") as s"${prefix}subs")
  }
}

object FixedWirelessTrends {

  def apply()(implicit spark: SparkSession): FixedWirelessTrends = new FixedWirelessTrends()
}
