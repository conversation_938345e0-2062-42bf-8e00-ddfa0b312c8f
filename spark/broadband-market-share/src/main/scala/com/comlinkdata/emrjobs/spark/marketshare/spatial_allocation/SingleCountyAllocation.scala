package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation

import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.constants.TOLERANCE
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.types.County
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.{Target, Allocation}

import scala.collection.mutable

/**
  * Single-county spatial allocation
  *
  * 1. Create mutable distance matrix
  * map of source census blocks to sorted list of destination census blocks
  *
  * 2. For each house
  *   - find closest target
  *   - try to allocate the house there
  *     - if allocation was successful (also partially), update target capacity
  *     - if house was fully allocated, continue with next house
  *     - otherwise continue with the same house with increased allocated weight
  */
class SingleCountyAllocation(county: String, distance: Distance) {

  def run(households: Seq[Allocation], targets: Seq[Target]): Seq[Allocation] = {
    val matrix = DistanceMatrix(households, targets, distance)

    // sort to eliminate non-determinism in the result
    val housesSorted = households.sortBy(r => new String(r.household_id))

    val result = mutable.MutableList.empty[Allocation]
    for (house <- housesSorted) {
      var houseWeight = house.weight_allocated_cumulative

      while ((house.weight_factor - houseWeight) > TOLERANCE && matrix.hasTarget(house.census_block_id)) {
        val target = matrix(house.census_block_id)
        val given = math.min(target.capacity, house.weight_factor - houseWeight)

        houseWeight += given
        matrix.update(house.census_block_id, given)

        result += house.copy(
          target_census_block_id = Some(target.csb),
          distance = distance(house, target),
          weight_allocated = given,
          weight_allocated_cumulative = houseWeight
        )
      }
    }
    result
  }
}

object SingleCountyAllocation {

  def apply(county: County, distance: Distance): SingleCountyAllocation = new SingleCountyAllocation(county, distance)
}
