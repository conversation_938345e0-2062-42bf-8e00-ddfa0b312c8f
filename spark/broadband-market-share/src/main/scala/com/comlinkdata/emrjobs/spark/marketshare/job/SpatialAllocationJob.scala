package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.constants.TOLERANCE
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.UdpHousehold
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.common_columns.{colCounty, colWeightAllocated, colHouseholdId, colCountyName, colWeightFactor}
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.{MultiCountyAllocation, AllocationOwnBlocks, Distance}
import com.comlinkdata.largescale.commons.io.dataset.overwrite
import com.comlinkdata.largescale.commons.{Spark<PERSON>ob<PERSON><PERSON><PERSON>, SparkJob}
import com.comlinkdata.largescale.schema.broadband_market_share.{CensusBlockSubmarket, HouseholdEstimate, BroadbandPenetrationEstimates, SpatialAllocation}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SparkSession, Dataset}

import java.net.URI
import java.time.LocalDate

/**
  * Configuration of SpatialAllocationJob.
  *
  * @param processingDate            optional processing date
  * @param householdsLocation        location of household estimates
  * @param bbPenetrationLocation     location of industry-model (penetration) BB households
  * @param submarketsLocation        location of submarkets lookup table
  * @param censusBlockExclude        census block prefixes which will not be considered. Empty sequence disables filtering
  * @param censusBlockInclude        census block prefixes which will be considered. Empty sequence disables filtering
  * @param excludeSpFilters          SP (carriers) to exclude
  * @param otherSubmarketPenalty     penalty (a multiplier) to census blocks distance if the blocks are in different submarkets
  * @param otherTractPenalty         penalty (a multiplier) to census blocks distance if the blocks are in different tracts
  * @param spatialAllocationLocation output location
  * @param outputPartitions          number of output partitions. For full USA a good number is 12
  */
case class SpatialAllocationJobConfig(
  processingDate: Option[LocalDate],
  householdsLocation: URI,
  bbPenetrationLocation: URI,
  submarketsLocation: URI,
  censusBlockExclude: Seq[String],
  censusBlockInclude: Seq[String],
  excludeSpFilters: Seq[Int],
  otherSubmarketPenalty: Double,
  otherTractPenalty: Double,
  spatialAllocationLocation: URI,
  outputPartitions: Int
)

object SpatialAllocationJob extends SparkJob[SpatialAllocationJobConfig](SpatialAllocationJobRunner)

object SpatialAllocationJobRunner extends SparkJobRunner[SpatialAllocationJobConfig] with LazyLogging {

  def runJob(config: SpatialAllocationJobConfig)(implicit spark: SparkSession): Unit = {
    val ts = HouseholdEstimate.tsl(config.householdsLocation)
    val processingDate = ts.latestDate(beforeOrEqual = config.processingDate)

    logger.info(s"Processing date: $processingDate")

    val submarkets = CensusBlockSubmarket.read(config.submarketsLocation, processingDate).cache()
    val bbPenetration = BroadbandPenetrationEstimates.read(
      config.bbPenetrationLocation, processingDate, config.censusBlockExclude, config.censusBlockInclude
    ).cache()
    val householdEstimates = HouseholdEstimate.read(
      config.householdsLocation, processingDate, config.censusBlockExclude, config.censusBlockInclude, config.excludeSpFilters.map(_.toString)
    )

    // Remove unknown blocks
    // Also make sure that udpHouseholds is distinct on household.
    //   - it's an extra safeguard though - technically that line should be unnecessary, but there are some points that
    //     are really close to block boundaries that the geocoding script assigns to both blocks
    val households = householdEstimates.transform(UdpHousehold.addSubmarket(submarkets)).cache()

    val allocation = new AllocationOwnBlocks()
    val (allocatedOwnBlocks, unallocated, targets) = allocation.run(bbPenetration, households, submarkets)

    import spark.implicits._

    val distance = Distance(Distance.haversine, config.otherSubmarketPenalty, config.otherTractPenalty)
    val allocatedNonOwnBlocks = MultiCountyAllocation(distance).run(unallocated, targets).cache()
    val result = allocatedOwnBlocks
      .unionByName(allocatedNonOwnBlocks)
      .map(_.toOutput)
      .cache()

    checkWSumCriteria(result)
    overwrite(result, processingDate, config.outputPartitions, SpatialAllocation.tsl(config.spatialAllocationLocation))
  }

  /**
    * Checks wsum criteria of the result
    *
    * wsum criteria:
    * sum of weight_allocated of all households in a county must be equal to weight factor of that county.
    *
    * @param result the allocation result
    * @param spark  spark session
    * @return true if the allocation is correct; false otherwise
    */
  private def checkWSumCriteria(result: Dataset[SpatialAllocation])(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val toleranceInverted = 1 / TOLERANCE
    val wsum = result
      .groupBy(colHouseholdId, colCounty, colWeightFactor)
      .agg((floor(lit(toleranceInverted) * sum(colWeightAllocated)) / toleranceInverted) as "wsum")
      .groupBy($"wsum", colCounty, colWeightFactor)
      .count()

    val goodWsums = wsum.groupBy(colCounty).count().filter($"count" === 1).drop("count")
    val badWsums = wsum.join(goodWsums, Seq(colCountyName), "left_anti").cache()

    if (!badWsums.isEmpty) {
      println("ERROR: BAD WSUM CRITERIA FOUND")
      badWsums.sort($"wsum".desc).show()

      println("wsums per county:")
      badWsums.groupBy(colCounty).count().show()
    }
  }
}