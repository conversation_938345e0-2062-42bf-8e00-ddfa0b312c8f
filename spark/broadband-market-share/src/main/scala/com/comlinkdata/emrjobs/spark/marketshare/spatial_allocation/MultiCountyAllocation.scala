package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation

import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.{Allocation, Target}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SparkSession, Dataset}

/**
  * Multi-county allocation job
  *
  * 1. Co-group unallocated households and targets by county
  * 2. Run single-county allocation for each county
  *
  * @param spark spark session
  */
class MultiCountyAllocation(distance: Distance)(implicit spark: SparkSession) extends LazyLogging {

  import spark.implicits._

  def run(
    households: Dataset[Allocation],
    targets: Dataset[Target],
  ): Dataset[Allocation] = {
    val householdsValid = households.cache()
    val targetsValid = targets.cache()

    logger.info(s"Unallocated households ${householdsValid.count()}")
    logger.info(s"Available targets: ${targetsValid.count()}")

    val householdsPerCounty = householdsValid.groupByKey(_.county)
    val targetsPerCounty = targets.groupByKey(_.county)
    val localDistance = distance

    householdsPerCounty.cogroup(targetsPerCounty) {
      case (county, houses, targets) =>
        val housesMaterialized = houses.toSeq
        val targetsMaterialized = targets.toSeq

        SingleCountyAllocation(county, localDistance).run(housesMaterialized, targetsMaterialized)
    }
  }
}
object MultiCountyAllocation {

  def apply(distance: Distance)(implicit spark: SparkSession): MultiCountyAllocation = {
    new MultiCountyAllocation(distance)
  }
}