package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.emrjobs.spark.marketshare.IfaHomeCarriers.{findTMOFwIfas, findVerizonFwIfas}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp
import com.comlinkdata.largescale.schema.broadband_market_share.IfaHomeCarrierNoDate
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp.twoOctetsUDF
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.tier2.UdpIfaIpAgg
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{Dataset, SparkSession}
import org.apache.spark.sql.functions._

import java.sql.Date

class IfaHomeCarriers(implicit spark: SparkSession) extends LazyLogging {

  import spark.implicits._


  /**
    * Computes IFA-home-carriers including Verizon Fixed-wireless.
    *
    * @param ifaHomeCarrierAgg IFA home-carrier from BB 1.8
    * @param ifaIpAgg          IFA-IP aggregate
    * @param verizonIpBlocks   verizon reserved IP blocks
    * @param ifaSetFiltered    List of IFAs that are on specific TMO device types
    * @return IFA-home-carriers
    */
  def runDay(
    ifaHomeCarrier: Dataset[IfaHomeCarrierNoDate],
    ifaIpAgg: Dataset[UdpIfaIpAgg],
    verizonIpBlocks: Dataset[VerizonFwIp],
    ifaSetFiltered: Dataset[Ifa]
  ): Dataset[IfaHomeCarrierNoDate] = {

    val verizonFwIfas = findVerizonFwIfas(ifaIpAgg, verizonIpBlocks)
    val tmobileFwIfas = findTMOFwIfas(ifaIpAgg, ifaSetFiltered)

    ifaHomeCarrier.as("hc")
      .join(verizonFwIfas.as("vzw"), Seq("ifa"), "full")
      .join(tmobileFwIfas.as("tmo"), Seq("ifa"), "full")
      .withColumn("final_carrier", coalesce($"tmo.carrier", $"vzw.carrier", $"hc.carrier"))
      .select($"ifa", $"final_carrier" as "carrier")
      .as[IfaHomeCarrierNoDate]
  }
}

object IfaHomeCarriers {

  case class FwIfas(ifa: Ifa, carrier: String, local_date: Date)
  /**
    * Finds IFAs which assigned IP address corresponds to a Verizon fixed-wireless reserved IP block.
    * Note: processing only in one day!
    *
    * @param ifaipAgg        IFA-IP aggregate day
    * @param verizonIpBlocks Verizon reserved IP blocks
    * @return IFAs + dates on Verizon reserved IP blocks
    */
  def findVerizonFwIfas(
    ifaipAgg: Dataset[UdpIfaIpAgg],
    verizonIpBlocks: Dataset[VerizonFwIp]
  )(implicit spark: SparkSession): Dataset[FwIfas] = {
    import spark.implicits._

    val verizonIps = verizonIpBlocks.select($"ip_2_octets")

    ifaipAgg
      .filter($"carrier".rlike("(?i)verizon"))
      .filter(length($"ip") === 4)
      .join(verizonIps, $"ip_2_octets" === twoOctetsUDF($"ip"))
      .select(
        $"ifa",
        lit(VERIZON_FIXED_WIRELESS) as "carrier",
        $"local_date")
      .dropDuplicates()
      .as[FwIfas]
  }

  /**
    * Finds IFAS that belong to T mobile fixed wireless by filtering on hosted specific devices
    * @param ifaipAgg       IFA-IP aggregate day
    * @param ifaSetFiltered List of IFAs that belong on specific TMO devices
    * @return IFAs on TMO fixed wireless
    */
  def findTMOFwIfas(
    ifaipAgg: Dataset[UdpIfaIpAgg],
    ifaSetFiltered: Dataset[Ifa]
  )(implicit spark: SparkSession): Dataset[FwIfas] = {
    import spark.implicits._

    ifaipAgg
      .filter($"carrier".rlike("(?i)t-mobile usa"))
      .join(ifaSetFiltered.select($"ifa"), Seq("ifa"), "leftsemi")
      .select(
        $"ifa",
        lit(TMOBILE_FIXED_WIRELESS) as "carrier",
        $"local_date"
      )
      .distinct()
      .as[FwIfas]
  }

  def apply()(implicit spark: SparkSession) = new IfaHomeCarriers()
}