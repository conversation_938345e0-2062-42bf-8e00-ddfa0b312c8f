package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs

import com.comlinkdata.largescale.commons.{Spark<PERSON>ob, Spark<PERSON>ob<PERSON>unner, TimeSeriesLocation}
import com.typesafe.scalalogging.LazyLogging
import java.net.URI
import java.time.LocalDate
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{DoubleType, IntegerType}
import org.apache.spark.sql.{SaveMode, SparkSession}

case class SubscriberAggregateConfig(
  satelliteCensusBlockPath: URI,
  fwaByCarrierPath: URI,
  bbcvAggregatePath: String,
  aggCarrierRollupsPath: String,
  spatialAllocationPath: URI,
  firstPartyFootprintPath: Option[String],
  firstPartySPID: Option[Int],
  subscriberAggregateResultPath: URI,
  date: LocalDate
)

object SubscriberAggregateJob extends SparkJob(SubscriberAggregateRunner)

object SubscriberAggregateRunner extends SparkJobRunner[SubscriberAggregateConfig] with LazyLogging {
  override def runJob(config: SubscriberAggregateConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    val subscriberAggregateResultPath = TimeSeriesLocation.ofDatePartitions(config.subscriberAggregateResultPath).build
      .validOutputPartitionOrThrow(config.date)
    val fpFootprintPath = config.firstPartyFootprintPath
    val satelliteCensusBlocks = spark.read.parquet(
      TimeSeriesLocation.ofDatePartitions(config.satelliteCensusBlockPath).build
        .validInputPartitionOrThrow(config.date): _*)
    val fwaTsl = TimeSeriesLocation.ofDatePartitions(config.fwaByCarrierPath).build
    val fwa =
      if (fwaTsl.exists(config.date)) Some(spark.read.parquet(fwaTsl.partition(config.date)))
      else None
    val spatialAllocation = spark.read.parquet(TimeSeriesLocation.ofDatePartitions(config.spatialAllocationPath).build
        .validInputPartitionOrThrow(config.date): _*)
      .select(
        $"destination_census_block_id" as "block",
        $"destination_census_block_id".substr(1, 5) as "county",
        $"sp_platform",
        $"weight_allocated")
    val aggCarrierRollups = spark.read.parquet(config.aggCarrierRollupsPath)
    val countyRollups = aggCarrierRollups
      .filter($"limit_to_census_blockids" =!= "-1")
      .select(
        $"child_sp_platform" as "sp_platform",
        $"parent_sp_platform" as "county_sp_platform",
        $"limit_to_census_blockids".substr(1, 5) as "county")
      .distinct
    val nationalRollups = aggCarrierRollups
      .filter($"limit_to_census_blockids" === "-1")
      .select(
        $"child_sp_platform" as "sp_platform",
        $"parent_sp_platform" as "national_sp_platform")
      .distinct
    val spatialAllocationSubscribers = spatialAllocation
      .join(countyRollups, Seq("county", "sp_platform"), "left")
      .join(nationalRollups, Seq("sp_platform"), "left")
      .groupBy(
        $"block" as "census_blockid",
        coalesce($"county_sp_platform", $"national_sp_platform", $"sp_platform") as "sp_platform",
        lit("SA") as "source")
      .agg(sum($"weight_allocated") as "subscribers")
    val bbcvAggregate = spark.read.parquet(config.bbcvAggregatePath)
      .select(
        $"census_block".substr(1, 5) as "county",
        $"census_block".substr(1, 2) as "state",
        $"sp_platform",
        $"customer_base" cast DoubleType as "customer_base")
      .filter($"sp_platform".isin("6130", "6140", "6710"))
    val countyBySP = bbcvAggregate
      .groupBy($"county", $"sp_platform")
      .agg(sum($"customer_base") as "sp_cld_subs_count")
    val countyTotal = bbcvAggregate
      .groupBy($"county")
      .agg(sum($"customer_base") as "total_cld_subs_count")
    val countyShare = countyBySP
      .join(countyTotal, Seq("county"))
      .select(
        $"county",
        $"sp_platform" as "county_sp_platform",
        $"sp_cld_subs_count" / $"total_cld_subs_count" as "county_share")
    val stateBySP = bbcvAggregate
      .groupBy($"state", $"sp_platform")
      .agg(sum($"customer_base") as "sp_cld_subs_count")
    val stateTotal = bbcvAggregate
      .groupBy($"state")
      .agg(sum($"customer_base") as "total_cld_subs_count")
    val stateShare = stateBySP
      .join(stateTotal, Seq("state"))
      .select(
        $"state",
        $"sp_platform" as "state_sp_platform",
        $"sp_cld_subs_count" / $"total_cld_subs_count" as "state_share")
    val satelliteSubscribers = satelliteCensusBlocks
      .join(countyShare, Seq("county"), "left")
      .join(stateShare, Seq("state"), "left")
      .withColumn("subscribers", $"sat_subs" * coalesce($"county_share", $"state_share"))
      .filter($"subscribers" > 0.0)
      .select(
        $"block" as "census_blockid",
        coalesce($"county_sp_platform", $"state_sp_platform") as "sp_platform",
        $"subscribers",
        lit("Sat") as "source")
    val fpSPID = config.firstPartySPID
    val firstPartyFootprint = config.firstPartyFootprintPath
      .map(spark.read.parquet(_).select($"censusblock" as "block", $"hsd_subs"))
    val result = (firstPartyFootprint, fpSPID, fwa) match {
      case (Some(footprint), Some(spid), Some(fwaByCarrier)) =>
        val fwaSubscribers = fwaByCarrier
          .join(footprint, Seq("block"))
          .select(
            $"block" as "census_blockid",
            $"sp_platform",
            $"fwa_subs" as "subscribers",
            lit("FWA") as "source")
        val firstPartySubscribers = footprint
          .filter(length($"block") === 15)
          .select(
            $"block" as "census_blockid",
            typedLit(spid) as "sp_platform",
            $"hsd_subs" as "subscribers",
            lit("FP") as "source")
        spatialAllocationSubscribers unionByName
          fwaSubscribers unionByName
          satelliteSubscribers unionByName
          firstPartySubscribers
      case (Some(footprint), Some(spid), None) =>
        val firstPartySubscribers = footprint
          .filter(length($"block") === 15)
          .select(
            $"block" as "census_blockid",
            typedLit(spid) as "sp_platform",
            $"hsd_subs" as "subscribers",
            lit("FP") as "source")
        spatialAllocationSubscribers unionByName
          satelliteSubscribers unionByName
          firstPartySubscribers
      case (None, None, Some(fwaByCarrier)) =>
        val fwaSubscribers = fwaByCarrier
          .select(
            $"block" as "census_blockid",
            $"sp_platform" as "sp_platform",
            $"fwa_subs" as "subscribers",
            lit("FWA") as "source")
        spatialAllocationSubscribers unionByName
          fwaSubscribers unionByName
          satelliteSubscribers
      case (None, None, None) =>
        spatialAllocationSubscribers unionByName
          satelliteSubscribers
      case _ =>
        scala.sys.error(s"Invalid combination of $fpFootprintPath and $fpSPID - either both should be provided, or neither")
    }
    logger.info(s"Writing results to $subscriberAggregateResultPath")
    result.distinct
      .withColumn("sp_platform", $"sp_platform" cast IntegerType)
      .withColumn("subscribers", $"subscribers" cast DoubleType)
      .repartition(25).write.mode(SaveMode.ErrorIfExists).parquet(subscriberAggregateResultPath)
  }
}
