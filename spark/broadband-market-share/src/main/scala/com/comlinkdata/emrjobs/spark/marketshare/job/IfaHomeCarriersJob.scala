package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.emrjobs.spark.marketshare.{FWA_TMO_DEVICE_TYPES, IfaHomeCarriers}
import com.comlinkdata.largescale.commons.RichDate.localDateOrdering
import com.comlinkdata.largescale.commons.io.dataset.findDaysToCompute
import com.comlinkdata.largescale.commons.{<PERSON>rk<PERSON>ob, SparkJobRunner, TimeSeriesLocation, UriDFTableRW}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIpWithDateRange.implicits.VerizonFwIpWithDateRangeOps
import com.comlinkdata.largescale.schema.broadband_market_share.{IfaHomeCarrier, IfaHomeCarrierAggregated, IfaHomeCarrierNoDate}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.{VerizonFwIp, VerizonFwIpWithDateRange}
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.tier2.{IfaSet, UdpIfaIpAgg}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Dataset, SaveMode, SparkSession}

import java.net.URI
import java.sql.Date
import java.time.LocalDate

/**
  * IFA-home carriers job configuration.
  *
  * @param startDate                 starting date
  * @param endDate                   end date (optional)
  * @param datasources               list of input data sources
  * @param verizonFwIpLocation       Verizon fixed-wireless IPs lookup table location
  * @param ifaHomeCarrierAggLocation IFA home-carrier agg location
  * @param ifaIpAggLocation          UDP IFA-IP aggregate location
  * @param ifaSetLocation            IFA Set location
  * @param carrierLookupLocation     carrier-lookup location
  * @param outputLocation            IFA-home carrier (output) location
  * @param outputPartitions          output number of partitions
  */
case class IfaHomeCarriersJobConfig(
  startDate: Option[LocalDate],
  endDate: Option[LocalDate],
  datasources: Seq[ComlinkdataDatasource],
  ifaSetDatasource: ComlinkdataDatasource,
  verizonFwIpLocation: URI,
  ifaHomeCarrierLocation: URI,
  ifaIpAggLocation: URI,
  ifaSetLocation: URI,
  outputLocation: URI,
  outputPartitions: Option[Int]
)

object IfaHomeCarriersJob extends SparkJob[IfaHomeCarriersJobConfig](IfaHomeCarriersJobRunner)

object IfaHomeCarriersJobRunner extends SparkJobRunner[IfaHomeCarriersJobConfig] with LazyLogging {

  def runJob(config: IfaHomeCarriersJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    implicit val tslOut: TimeSeriesLocation = IfaHomeCarrier.tsl(config.outputLocation)
    val hc = IfaHomeCarriers()

    val daysToRun = findDaysToCompute(
      minOutputDate = None,
      startDate = config.startDate,
      endDate = config.endDate,
      overwrite = false,
      TimeSeriesLocation.ofDatePartitions(config.ifaHomeCarrierLocation).build +:
        UdpIfaIpAgg.tsl(config.ifaIpAggLocation, config.datasources): _*
    )

    if (daysToRun.isEmpty) {
      logger.info("Nothing to do, exiting")
      return
    }
    logger.info(s"Computing days: $daysToRun")

    // cannot filter TMO carrier here, we need IFA-IP agg because of maxmind
    val ifaSetFiltered = IfaSet
      .readLatest(config.ifaSetLocation, config.ifaSetDatasource)._2
      .filter(lower($"device_type") =!= "smart phone") // filter out bulk
      .filter(size($"days") > 0)
      .filter(array_min($"days") <= daysToRun.max) // older or equal to max processing day
      .filter(lower($"device_type").isin(FWA_TMO_DEVICE_TYPES: _*))
      .select($"ifa")
      .distinct()
      .as[Ifa]
      .cache()

    // really small ~ around 60 records every month and slim columns
    val verizonIpBlocksRange: Dataset[VerizonFwIpWithDateRange] = VerizonFwIpWithDateRange
      .read(config.verizonFwIpLocation)
      .cache()

    for (day <- daysToRun) {
      logger.info(s"Running daily ifa home carrier for date: $day")

      val ifaHomeCarrier = IfaHomeCarrierNoDate.readBB17(config.ifaHomeCarrierLocation, day)
      val ifaIpAgg = UdpIfaIpAgg.read(config.ifaIpAggLocation, date = day, config.datasources)

      val verizonIpBlocks = verizonIpBlocksRange.getDay(day)
      val homeCarrier = hc.runDay(
        ifaHomeCarrier,
        ifaIpAgg,
        verizonIpBlocks,
        ifaSetFiltered
      )

      UriDFTableRW
        .fromStr(tslOut.partition(day))
        .writeWithHistory(
          config.outputPartitions.map(homeCarrier.repartition).getOrElse(homeCarrier),
          SaveMode.Overwrite,
          Seq(config))
    }
  }
}