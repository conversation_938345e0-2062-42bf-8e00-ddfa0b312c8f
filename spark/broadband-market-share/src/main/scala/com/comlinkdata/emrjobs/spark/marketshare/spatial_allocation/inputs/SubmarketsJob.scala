package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs

import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs.SubmarketsSchema.{BBMS, Submarket}
import com.comlinkdata.largescale.commons.{SparkJob, SparkJobRunner, TimeSeriesLocation}
import com.typesafe.scalalogging.LazyLogging
import java.net.URI
import java.time.LocalDate
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.StringType
import org.apache.spark.sql.{SaveMode, SparkSession}

case class SubmarketsConfig(
  serviceTerritoryPath: String,
  demographicsPath: String,
  displayRulesPath: String,
  masterBlockPath: String,
  resultPath: URI,
  date: LocalDate
)

object SubmarketsSchema {
  case class BBMS(census_blockid: String, sp_id: String, isp_type: String, technology: String) {
    def toSubmarket: String = s"$sp_id $isp_type $technology;"
  }

  case class Submarket(census_blockid: String, submarket: String)
}

object SubmarketsJob extends SparkJob(SubmarketsRunner)

object SubmarketsRunner extends SparkJobRunner[SubmarketsConfig] with LazyLogging {
  override def runJob(config: SubmarketsConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    val serviceTerritories = spark.read.parquet(config.serviceTerritoryPath)
      .select('census_blockid, 'serv_terr_id, 'sp_id, 'census_blockid.substr(1, 5).as("county"))
    val demographics = spark.read.parquet(config.demographicsPath)
      .select('serv_terr_blockid.as("census_blockid"), 'hh)
    val displayRules = spark.read.option("header", "true").csv(config.displayRulesPath)
      .select('sp_dim_id.as("sp_id"), 'isp_type)
      .distinct
    val masterBlock = spark.read.parquet(config.masterBlockPath)
      .select('serv_terr_blockid.as("census_blockid"), 'latitude, 'longitude)
    val resultTsl = TimeSeriesLocation.ofDatePartitions(config.resultPath).build
    val resultPath = resultTsl.validOutputPartitionOrThrow(config.date)
    val serviceTerritoriesWithISP = serviceTerritories
      .join(displayRules, Seq("sp_id"), "left")
      .select('census_blockid, 'serv_terr_id, 'sp_id, 'isp_type, 'county)
    val counties_Non_ILEC_MSO = serviceTerritoriesWithISP
      .join(demographics, "census_blockid")
      .filter(!'isp_type.isin("MSO", "ILEC"))
      .groupBy('county, 'sp_id, 'isp_type)
      .agg(sum('hh).as("county_sp_hh"))
      .withColumn("rank", row_number.over(Window.partitionBy('county).orderBy('county_sp_hh.desc)))
      .filter('rank <= 5)
      .select('county, 'sp_id, 'isp_type)
      .distinct
    val counties_ILEC_MSO = serviceTerritoriesWithISP
      .filter('isp_type.isin("MSO", "ILEC"))
      .select('county, 'sp_id, 'isp_type)
      .distinct
    val counties = counties_Non_ILEC_MSO
      .union(counties_ILEC_MSO)
    val submarketTerritories = serviceTerritories
      .filter('serv_terr_id.between(0, 5))
      .join(counties, Seq("county", "sp_id"))
      .select(
        'census_blockid,
        coalesce('sp_id.cast(StringType), lit("")).as("sp_id"),
        coalesce('isp_type, lit("")).as("isp_type"),
        when('serv_terr_id === 4, "Fiber").when('serv_terr_id === 5, "MSO").otherwise("Other DSL").as("technology"))
      .distinct
      .as[BBMS]
      .groupByKey(_.census_blockid)
      .mapGroups { case (census_blockid, rows) =>
        Submarket(census_blockid, rows.toList.distinct.map(_.toSubmarket).sorted.mkString)
      }
    val submarkets = masterBlock
      .join(submarketTerritories, Seq("census_blockid"), "left")
      .select(
        'census_blockid,
        coalesce('submarket, lit("None")).as("submarket"),
        'latitude,
        'longitude)
    logger.info(s"Writing results to $resultPath")
    submarkets.repartition(25).write.mode(SaveMode.ErrorIfExists).parquet(resultPath)
  }
}
