package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.emrjobs.spark.marketshare.{Deactivations, IfaHomeCarriers}
import com.comlinkdata.largescale.commons.io.dataset.overwrite
import com.comlinkdata.largescale.commons.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, LocalDateRange}
import com.comlinkdata.largescale.schema.{FwaTrendsNoDate, FwaTrends}
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband_market_share.BroadbandCellular
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.FixedWirelessSpid
import com.comlinkdata.largescale.schema.cellular_only.CellularOnlyDevice
import com.comlinkdata.largescale.schema.udp.installbase.IPHighConfidence
import com.comlinkdata.largescale.schema.udp.tier2.UdpIfaIpAgg
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.SparkSession

import java.net.URI
import java.time.LocalDate
import scala.util.Try

/**
  * BB deactivations job configuration.
  *
  * Requirements:
  * - any(highConfidenceLookbackDays) > 0
  * - length(highConfidenceLookbackDays) >= 1
  * - cellularOnlyLookbackDays > 0
  *
  * Standard rules:
  * - highConfidenceLookbackDays = [28, 21]
  *
  * @param startDate                  start date of processing (optional). If not provided, latest cellular-only date
  *                                   will be taken
  * @param endDate                    processing end date (inclusive)
  * @param datasources                list of input data sources
  * @param carrierLookupLocation      carrier lookup table location
  * @param spidLookupLocation         fixed-wireless SPID lookup table location
  * @param highConfidenceLocation     High-confidence location
  * @param highConfidenceLookbackDays High-confidence lookback days.
  *                                   For each i, highConfidenceDate(i) = processingDate - highConfidenceLookbackDays(i)
  * @param cellularOnlyLocation       cellular-only location
  * @param cellularOnlyLookbackDays   cellular-only lookup days.
  *                                   cellularOnlyDate = processingDate - cellularOnlyLookbackDays
  * @param fwaTrendsLocation          fixed-wireless trends location
  * @param fwaFromBbProportion        estimated proportion of fixed-wireless coming from broadband
  * @param outputLocation             BB deactivations (output) location
  * @param outputPartitions           output number of partitions
  */
case class DeactivationsJobConfig(
  startDate: Option[LocalDate],
  endDate: Option[LocalDate],
  datasources: Seq[ComlinkdataDatasource],
  carrierLookupLocation: URI,
  carrierLookupFwLocation: URI,
  spidLookupLocation: URI,
  excludeSpIds: Seq[Int],
  highConfidenceLocation: URI,
  highConfidenceLookbackDays: Seq[Int],
  cellularOnlyLocation: URI,
  cellularOnlyLookbackDays: Int,
  fwaTrendsLocation: URI,
  fwaFromBbProportion: Double,
  verizonIps: Option[VerizonFwIpConfig],
  outputLocation: URI,
  outputPartitions: Int
)

case class VerizonFwIpConfig(
  verizonFwIpLocation: URI,
  ifaIpAggLocation: URI
)

object DeactivationsJob extends SparkJob[DeactivationsJobConfig](DeactivationsJobRunner)

object DeactivationsJobRunner extends SparkJobRunner[DeactivationsJobConfig] with LazyLogging {

  def runJob(config: DeactivationsJobConfig)(implicit spark: SparkSession): Unit = {
    require(!config.highConfidenceLookbackDays.exists(_ <= 0), "All high-confidence lookback days have to be > 0")
    require(config.highConfidenceLookbackDays.nonEmpty, "At least one high-confidence lookback days has to be defined")
    require(config.cellularOnlyLookbackDays > 0, "cellularOnlyLookbackDays has to be > 0")

    val coTsl = CellularOnlyDevice.tsl(config.cellularOnlyLocation)
    val startDate = config.startDate.getOrElse(coTsl.latestDate)
    val ldr = LocalDateRange(startDate, config.endDate.getOrElse(startDate))
    logger.info(s"Computing BroadbandActivations for: $ldr")

    val carrierLookup = CarrierLookup
      .read(config.carrierLookupLocation)
      .unionByName(CarrierLookup.read(config.carrierLookupFwLocation))

    val spidLookup = FixedWirelessSpid.read(config.spidLookupLocation)
    val bbda = Deactivations(config.fwaFromBbProportion)

    // will ignore non-existing cellular-only dates (= output deactivation dates)
    for (processingDate <- ldr if coTsl.exists(processingDate)) {
      logger.info(s"Processing day: $processingDate")

      val highConfidences = config
        .highConfidenceLookbackDays
        .map(hcLookbackDays => {
          val hcDate = processingDate minusDays hcLookbackDays
          hcLookbackDays -> IPHighConfidence.read(config.highConfidenceLocation, hcDate)
        }).toMap

      val cellularOnlyPrevDate = processingDate minusDays config.cellularOnlyLookbackDays
      val cellularOnlys = Map(
        0 -> CellularOnlyDevice.read(config.cellularOnlyLocation, processingDate),
        config.cellularOnlyLookbackDays -> CellularOnlyDevice.read(config.cellularOnlyLocation, cellularOnlyPrevDate)
      )

      import spark.implicits._
      val fwaTrends = Try(FwaTrends.readNoDate(config.fwaTrendsLocation, processingDate))
        .getOrElse(spark.emptyDataset[FwaTrendsNoDate])

      val verizonFwIfasOpt = config.verizonIps.map {
        c =>
          val ifaIpAgg = UdpIfaIpAgg.read(c.ifaIpAggLocation, processingDate, config.datasources)
          val verizonIpBlocks = VerizonFwIp.read(c.verizonFwIpLocation)
          IfaHomeCarriers.findVerizonFwIfas(ifaIpAgg, verizonIpBlocks)
      }

      val result = bbda.runDay(
        highConfidences,
        cellularOnlys,
        carrierLookup,
        spidLookup,
        excludeSpIds = config.excludeSpIds,
        fwaTrends = fwaTrends,
        verizonFwIfasOpt = verizonFwIfasOpt
      )
      overwrite(result, processingDate, config.outputPartitions, BroadbandCellular.tsl(config.outputLocation))
      spark.catalog.clearCache()
    }
  }
}