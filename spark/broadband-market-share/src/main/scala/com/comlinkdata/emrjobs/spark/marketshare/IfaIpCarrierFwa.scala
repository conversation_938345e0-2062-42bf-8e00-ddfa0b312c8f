package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.emrjobs.spark.marketshare.IfaIpCarrierFwa.{findTmoFw, findVerizonFw}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp
import com.comlinkdata.largescale.schema.broadband_market_share.BroadbandIfaIpCarrier
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp.twoOctetsUDF
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.comlinkdata.largescale.schema.udp.tier2.UdpIfaIpAgg
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.{Dataset, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.StringType

import java.sql.Date

class IfaIpCarrierFwa(implicit spark: SparkSession) extends LazyLogging {

  import spark.implicits._

  /**
    * Computes IFA-home-carriers including Verizon Fixed-wireless.
    *
    * @param ifaIpCarrier    ifa-ip-carrier from BB 1.8
    * @param ifaIpAgg        ifa-ip aggregate
    * @param verizonIpBlocks verizon reserved IP blocks
    * @param tmoIfas         List of IFAs that are on specific TMO device types
    * @return IFA-home-carriers
    */
  def runDay(
    ifaIpCarrier: Dataset[BroadbandIfaIpCarrier],
    ifaIpAgg: Dataset[UdpIfaIpAgg],
    verizonIpBlocks: Dataset[VerizonFwIp],
    tmoIfasBase: Dataset[Ifa],
    tmoIfasDS: Dataset[Ifa]
  ):
  Dataset[BroadbandIfaIpCarrier] = {

    val verizonFwIfaIpCarriers = findVerizonFw(ifaIpAgg, verizonIpBlocks)
    val tmobileFwIfaIpCarriers = findTmoFw(ifaIpAgg, tmoIfasBase, tmoIfasDS)

    ifaIpCarrier.as("hc")
      .join(verizonFwIfaIpCarriers.as("vzw"), Seq("ifa"), "full")
      .join(tmobileFwIfaIpCarriers.as("tmo"), Seq("ifa"), "full")
      .select($"ifa",
        coalesce($"tmo.ip", $"vzw.ip", $"hc.ip") as "ip",
        coalesce($"tmo.carrier", $"vzw.carrier", $"hc.carrier") as "carrier",
        $"ds",
        $"suspiciousIp",
        $"year",
        $"month",
        $"day")
      .select(BroadbandIfaIpCarrier.cols:_*)
      .as[BroadbandIfaIpCarrier]
  }
}

object IfaIpCarrierFwa extends LazyLogging {

  case class FwIfaIpCarrier(ifa: Ifa, ip: Ip, carrier: String, local_date: Date)

  /**
    * Finds IFAs which assigned IP address corresponds to a Verizon fixed-wireless reserved IP block.
    * Note: processing only in one day!
    *
    * @param ifaipAgg        IFA-IP aggregate day
    * @param verizonIpBlocks Verizon reserved IP blocks
    * @return IFAs + dates on Verizon reserved IP blocks
    */
  def findVerizonFw(
    ifaipAgg: Dataset[UdpIfaIpAgg],
    verizonIpBlocks: Dataset[VerizonFwIp]
  )(implicit spark: SparkSession): Dataset[FwIfaIpCarrier] = {
    import spark.implicits._

    val verizonIps = verizonIpBlocks.select($"ip_2_octets")

    ifaipAgg
      .filter($"carrier".rlike("(?i)verizon"))
      .filter(length($"ip") === 4)
      .join(verizonIps, $"ip_2_octets" === twoOctetsUDF($"ip"), "leftsemi")
      .select(
        $"ifa",
        $"ip",
        lit(VERIZON_FIXED_WIRELESS) as "carrier",
        $"local_date")
      .dropDuplicates("ifa", "carrier", "local_date")
      .as[FwIfaIpCarrier]
  }

  /**
    * Finds IFAS that belong to T mobile fixed wireless by filtering on hosted specific devices
    * @param ifaipAgg    IFA-IP aggregate day
    * @param tmoIfasMw07 Mobilewalla List of IFAs that belong on specific TMO devices
    * @param tmoIfasDS   Other DS (Gamoshi or Quadrant) List of IFAs that belong on specific TMO Devices
    * @return IFAs on TMO fixed wireless
    */
  def findTmoFw(
    ifaipAgg: Dataset[UdpIfaIpAgg],
    tmoIfasBase: Dataset[Ifa],
    tmoIfasDS: Dataset[Ifa]
  )(implicit spark: SparkSession): Dataset[FwIfaIpCarrier] = {
    import spark.implicits._

    def joinIfaToIfaIpAgg(df: Dataset[UdpIfaIpAgg], ifaDs: Dataset[Ifa]): Dataset[FwIfaIpCarrier] = {
      df
        .filter($"carrier".rlike("(?i)t-mobile usa"))
        .join(ifaDs.select($"ifa"), Seq("ifa"), "leftsemi")
        .select(
          $"ifa",
          $"ip",
          lit(TMOBILE_FIXED_WIRELESS) as "carrier",
          $"local_date")
        .dropDuplicates("ifa", "carrier", "local_date")
        .as[FwIfaIpCarrier]
    }

    val dsIfaIpAgg =
      if(tmoIfasDS.isEmpty)
        spark.emptyDataset[FwIfaIpCarrier]
      else
        ifaipAgg.transform(joinIfaToIfaIpAgg(_, tmoIfasDS))

    val unmatchedIfaIpAgg =
      if(dsIfaIpAgg.isEmpty)
        ifaipAgg
      else
        ifaipAgg.join(dsIfaIpAgg.select($"ifa"), Seq("ifa"), "leftanti").as[UdpIfaIpAgg]

    val outputIfaIpAgg =
      unmatchedIfaIpAgg.transform(joinIfaToIfaIpAgg(_, tmoIfasBase))

    if(dsIfaIpAgg.isEmpty)
      outputIfaIpAgg
    else
      dsIfaIpAgg.union(outputIfaIpAgg)
  }

  def apply()(implicit spark: SparkSession) = new IfaIpCarrierFwa()
}