package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.largescale.commons.RichDate.localDateOrdering
import com.comlinkdata.largescale.commons.Utils.hexStringToBinUdf
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandIfaIpCarrier, BroadbandIfaIpCarrierWithHour}
import com.comlinkdata.largescale.schema.udp.Ip
import com.comlinkdata.largescale.udp.{ComlinkdataConnectionType, ComlinkdataDatasource}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql._
import org.apache.spark.sql.functions._

import java.net.URI
import java.sql.Timestamp
import java.time.LocalDate
import scala.util.Try

case class IfaIpCarrierJobConfig(
  udpMappingLocation: URI,
  datasources: List[ComlinkdataDatasource],
  suspiciousIpCarrierExclustionLocation: URI,
  outputLocation: URI,
  discardLocation: URI,
  numOutputFiles: Int,
  dateRangeOpt: Option[LocalDateRange],
  dsExcludeCarrierList: Option[Seq[String]],
)

//todo: make a reader class for this
case class BroadbandUdpMapping(
  ifa: String,
  ip: Ip,
  carrier: Option[String],
  datetime: Timestamp,
  local_datetime: Option[Timestamp],
  locationtype: String,
  raw_connectiontype: Option[String],
  connectiontype_2020: Option[String],
  connectiontype: Option[String],
  mm_connectiontype: Option[String],
  latitude: Float,
  longitude: Float
)

object BroadbandUdpMapping extends Utils.reflection.ColumnNames[BroadbandUdpMapping]

/**
 * https://comniscient.atlassian.net/wiki/spaces/RD/pages/643728109/Broadband+Algorithm+v1.1
 */
object IfaIpCarrierJob extends SparkJob[IfaIpCarrierJobConfig](IfaIpCarrierJobRunner) {

  override def configureSpark(builder: SparkSession.Builder): SparkSession.Builder =
    super.configureSpark(builder)
      .config("spark.sql.sources.partitionOverwriteMode", "dynamic")
      .config("spark.driver.maxResultSize", "2g")
      .config("fs.s3a.connection.maximum", 200) // https://stackoverflow.com/questions/56259853/why-aws-is-rejecting-my-connections-when-i-am-using-wholetextfiles-with-pyspar
}

/**
 * Get each devices wifi carriers on every date for only nights and weekends
 *
 * This outputs all IFAs with the carrier they were on, for each date.
 * format output is $ifa, $date, $carrier
 */
object IfaIpCarrierJobRunner extends SparkJobRunner[IfaIpCarrierJobConfig] with LazyLogging {
  val wifi: String = ComlinkdataConnectionType.WIFI.stringValue
  val weekDay = dayofweek(to_date(concat(col("year"),lit("-"),col("month"),lit("-"),col("day"))))
  val saturday = 7
  val sunday = 1
  val revealEndDateForBroadband = LocalDate.of(2021,2,1).minusDays(1)

  def tsInUdpMap(config: Config, ds: ComlinkdataDatasource)(implicit spark: SparkSession): TimeSeriesLocation = {
    TimeSeriesLocation
      .ofDatePartitions(config.udpMappingLocation)
      .withPartition(ds)
      .withFirstDate(ds.firstDate)
      .withLastDate(if (ds == ComlinkdataDatasource.reveal) Some(revealEndDateForBroadband) else ds.lastDate)
      .build
  }

  private def tsIn(config: Config)(implicit spark: SparkSession): TimeSeriesLocation =
    TimeSeriesLocation
      .ofDatePartitions(config.outputLocation)
      .build

  private def tsOut(config: Config, ds: String)(implicit spark: SparkSession): TimeSeriesLocation =
    TimeSeriesLocation
      .ofYmdDatePartitions(config.outputLocation)
      .withPartition(f"ds=$ds")
      .build

  private def tsOutDiscard(config: Config)(implicit spark: SparkSession): TimeSeriesLocation =
    TimeSeriesLocation
      .ofDatePartitions(config.discardLocation)
      .build
      
  def findDateRangeForDs(config: Config, ds: ComlinkdataDatasource)(implicit spark: SparkSession): LocalDateRange = {
    config.dateRangeOpt getOrElse {
      LocalDateRange.of(
        startDateInclusive = tsIn(config).latestInputPartition.plusDays(1),
        endDateInclusive = tsInUdpMap(config, ds).latestInputPartition
      )
    }
  }

  def runJob(config: IfaIpCarrierJobConfig)(implicit spark: SparkSession): Unit = {
    val dateRangeToProcess = config.dateRangeOpt.getOrElse {
      val startDate = tsIn(config).latestInputPartition.plusDays(1)

      val tss = config.datasources.map(tsInUdpMap(config, _))
      val endDates = tss.map(_.latestInputPartition)
      val (minEndDate, maxEndDate) = (endDates.min, endDates.max)

      // Find the latest common endDate of all data sources
      val endDate = LocalDateRange.of(minEndDate, maxEndDate).toSeq
        .sorted(Ordering[LocalDate].reverse) // from the latest
        .find(day => tss.forall(ts => Try(ts.validInputPartitionsOrThrow(LocalDateRange.of(startDate, day))).toOption.nonEmpty))
        .getOrElse(throw new IllegalStateException("there is no common date available among data sources"))

      LocalDateRange.of(startDate, endDate)
    }

    tsIn(config).validOutputPartitionsOrThrow(dateRangeToProcess)

    logger.info(s"Processing the following datasource-days: $dateRangeToProcess.")
    val runWithConfig = runDay(config) _

    dateRangeToProcess.foreach(runWithConfig)
  }

  def runDay(config: Config)(day: LocalDate)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val dsList = config.datasources

    logger.info(s"Processing ds=$dsList, day=$day.")

    val ifaHomeCarrierData = try loadUdpMappingAndIpMatch(config, dsList, day) catch {
      case ex: Exception =>
        logger.error(s"Ignoring day=$day; not all data sources have requested input partitions", ex)

        return
    }

    val broadbandCarriers = filterIfaHomeCarrierData(ifaHomeCarrierData)

    for (ds <- config.datasources) {
      val dest = tsOut(config, ds.toString).validOutputPartitionOrThrow(day)

      logger.info(s"Writing $ds results to $dest.")
      broadbandCarriers
        .filter($"ds" === ds.toString)
        .drop("ds", "year", "month", "day")
        .repartition(config.numOutputFiles)
        .write
        .mode(SaveMode.ErrorIfExists)
        .parquet(dest)

      logger.info(s"Wrote ${broadbandCarriers.count()} records for ds $ds")
    }
  }

  def quadrantSuspiciousIp(dsExcludeCarrierList: Seq[String])(df: Dataset[BroadbandUdpMapping])(implicit spark: SparkSession) = {
    import spark.implicits._
    df
      .filter(upper($"connectiontype") === "WIFI")
      .filter(length($"ip") === 4)
      .filter(!$"carrier".isin(dsExcludeCarrierList:_*))
      .groupBy($"ip")
      .agg(countDistinct($"ifa") as "distinct_ifa")
      .filter($"distinct_ifa" >= 5)
  }

  def loadUdpMappingAndIpMatch(
    config: IfaIpCarrierJobConfig,
    ds: List[ComlinkdataDatasource],
    day: LocalDate
  )(implicit spark: SparkSession): Dataset[Row] = {
    import spark.implicits._

    val excludeCarrierList: Seq[String] = config.dsExcludeCarrierList match {
      case Some(dsExcludes) => dsExcludes
      case None => Seq("")
    }
    logger.info(s"Excluding ${excludeCarrierList.length} carriers from ip filtering")

    val results: Seq[Dataset[Row]] = ds.map { ds: ComlinkdataDatasource =>
      logger.info(s"udp map -  $ds, $day")
      val df = loadUdpMappingData(config, ds, day)
      if (ds == ComlinkdataDatasource.quadrant) {
        val suspiciousIp = df.transform(quadrantSuspiciousIp(excludeCarrierList))
        val outDf = df.join(suspiciousIp, Seq("ip"), "leftouter")
                      .withColumn("suspiciousIp", when($"distinct_ifa".isNotNull, true).otherwise(false))
                      .withColumn("ds", lit(ds.toString))
                      .drop("distinct_ifa")
        outDf
      } else {
        df
          .withColumn("suspiciousIp", lit(false))
          .withColumn("ds", lit(ds.toString))
      }
    }

    val good = results.reduce(_ unionByName _)
    good
  }

  def loadUdpMappingData(config: IfaIpCarrierJobConfig, ds: ComlinkdataDatasource, day: LocalDate)(implicit spark: SparkSession): Dataset[BroadbandUdpMapping] = {
    import spark.implicits._

    logger.info(s"Processing Udp Mapping data for ds=$ds, day=$day.")
    val parts = tsInUdpMap(config, ds).validInputPartitionsOrThrow(LocalDateRange.of(day))
    logger.info(s"reading udp mapping input from partition $parts.")
    if (parts.isEmpty) spark.emptyDataset[BroadbandUdpMapping]
    else spark
      .read
      .option(ReadOpts.basePath, config.udpMappingLocation.toString)
      .parquet(parts: _*)
      .select(BroadbandUdpMapping.cols: _*)
      .as[BroadbandUdpMapping]
      .repartition(1024)
  }

  def filterIfaHomeCarrierData(ifaHomeCarrierData: Dataset[Row])(implicit spark: SparkSession): Dataset[BroadbandIfaIpCarrier] = {
    import spark.implicits._

    logger.info(s"Filtering ifa home carrier data.")

    ifaHomeCarrierData
      .transform(filterConnType(_, "WIFI"))
      .transform(transformUdpMapSlimNoIpToIfaHomeCarrier(_))
      .transform(filterResidentialBroadbandHours(_))
      .select(BroadbandIfaIpCarrier.cols: _*)
      .distinct
      .as[BroadbandIfaIpCarrier]
  }

  def filterConnType(ds: Dataset[Row], connTypeHold: String)(implicit spark: SparkSession): Dataset[Row] = {
    import spark.implicits._

    logger.info(s"Filtering connectiontype.")

    ds.toDF
      .filter(coalesce(upper($"connectiontype"), lit(connTypeHold)) === connTypeHold)
  }

  def transformUdpMapSlimNoIpToIfaHomeCarrier(ds: Dataset[Row])(implicit spark: SparkSession): Dataset[BroadbandIfaIpCarrierWithHour] = {
    import spark.implicits._

    logger.info(s"Adding date and hour from local_datetime")

    ds
      .withColumn("hour", hour('local_datetime))
      .withColumn("year", year(to_date('local_datetime)).cast("string"))
      .withColumn("month", lpad(month(to_date('local_datetime)), 2, "0"))
      .withColumn("day", lpad(dayofmonth(to_date('local_datetime)), 2, "0"))
      .withColumn("ifa", hexStringToBinUdf($"ifa"))
      .select(BroadbandIfaIpCarrierWithHour.cols: _*)
      .as[BroadbandIfaIpCarrierWithHour]
  }

  def filterResidentialBroadbandHours(ds: Dataset[BroadbandIfaIpCarrierWithHour])(implicit spark: SparkSession): Dataset[BroadbandIfaIpCarrierWithHour] = {
    import spark.implicits._

    logger.info(s"Filtering nights and weekends.")

    ds.toDF
      .filter(weekDay.isin(Seq(saturday, sunday):_*) || !col("hour").between(6, 19))
      .as[BroadbandIfaIpCarrierWithHour]
  }
}
