package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.emrjobs.spark.marketshare.PlatformAggregate._
import com.comlinkdata.largescale.schema.broadband.lookup.{CarrierAggRollup, CarrierSuppression, CensusBlockNoSpLookup}
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierAggRollup.implicits.CarrierAggRollupOps
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierSuppression.implicits.SinkSuppressionOps
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandAggregatedChurn, BroadbandAggregatedChurn25, BroadbandPlatformAggregate, BroadbandPlatformAggregateWithId, ElementIDToSuppress}
import com.comlinkdata.largescale.schema.broadband_market_share.ElementIDToSuppress.implicits.ElementIDToSuppressOps
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.{CensusBlock2010To2020Crosswalk, CensusBlockCrosswalk, FWAMultiplier, GuardRail, GuardRailTransform, LegacyStateDimId}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.CensusBlock2010To2020Crosswalk.implicits.CrosswalkOps
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.FWAMultiplier.implicits.MultiplierOps
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.GuardRail.implicits.GuardRailOps
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.GuardRailTransform.implicits.GuardRailTransformOps
import com.comlinkdata.largescale.schema.udp.installbase.IfaWirelessCarrier
import com.comlinkdata.largescale.schema.udp.lookup.AllowedNetworkBrandPair
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.DCommonOcnLookup
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.DoubleType
import org.apache.spark.sql.{Column, DataFrame, Dataset, Encoder, SparkSession}

import java.time.LocalDate

class PlatformAggregate(implicit spark: SparkSession) extends LazyLogging {

  import spark.implicits._

  def generate(
    churn20: Option[Dataset[BroadbandAggregatedChurn]],
    churn25: Option[Dataset[BroadbandAggregatedChurn25]],
    csbNoSpLookup: Dataset[CensusBlockNoSpLookup],
    csbCrosswalkLookup: Dataset[CensusBlockCrosswalk],
    csbCrosswalk2020Lookup: Dataset[CensusBlockCrosswalk],
    csb2010To2020Crosswalk: Dataset[CensusBlock2010To2020Crosswalk],
    stateDimIdLookup: Dataset[LegacyStateDimId],
    carrierRollupLookup: Dataset[CarrierAggRollup],
    allowedNetworkBrandPairLookup: Dataset[AllowedNetworkBrandPair],
    ifaWirelessCarrier: Dataset[IfaWirelessCarrier],
    dCommonOcnLookup: Dataset[DCommonOcnLookup],
    ifasToSuppress: Dataset[ElementIDToSuppress],
    carrierSuppression: Dataset[CarrierSuppression],
    fwaMultipliers: Dataset[FWAMultiplier],
    dsGuardRail: Dataset[GuardRail],
    guardRailLookBack: Int,
    churnStartDate: LocalDate
  ): PlatformAggregateOutput = {

    val churn: Dataset[BroadbandAggregatedChurn25] = churn25.getOrElse(churn20.get.transform(addNetworkBrandColumns(ifaWirelessCarrier)))

    val churnMovers = churn
      .transform(ifasToSuppress.suppressElements("household_id", $"churn_date"))
      .transform(ifasToSuppress.suppressElements("ifa", $"churn_date"))
      .transform(movers)
      .withColumn("element_id", coalesce($"household_id", $"ifa"))
      .transform(addWirelessSPColumns(allowedNetworkBrandPairLookup, dCommonOcnLookup))
      .cache()

    val preparePlatformPreAgg = prepareAgg(
      churnMovers,
      csbNoSpLookup,
      csbCrosswalkLookup,
      stateDimIdLookup,
      carrierRollupLookup,
      allowedNetworkBrandPairLookup,
      dCommonOcnLookup
    ) _

    val losingSide = preparePlatformPreAgg(
      "loser_sp_platform", "winner_sp_platform", "loser_census_block_id", "winner_census_block_id", "adjusted_losses", "adjusted_wins"
    ).cache()

    val winningSide = preparePlatformPreAgg(
      "winner_sp_platform", "loser_sp_platform", "winner_census_block_id", "loser_census_block_id", "adjusted_wins", "adjusted_losses"
    ).cache()

    val preAggRaw = losingSide
      .unionByName(winningSide)
      .transform(carrierSuppression.suppress("primary_sp_group", "census_blockid", "the_date"))
      .transform(carrierSuppression.suppress("secondary_sp_group", "secondary_census_blockid", "the_date"))
      .as[BroadbandPlatformAggregateWithId]

    val preAggRawWithMultipliers = preAggRaw
      .transform(fwaMultipliers.multiply(
        $"primary_sp_group",
        $"secondary_sp_group",
        $"the_date",
        $"adjusted_wins",
        $"adjusted_losses"))
      .as[BroadbandPlatformAggregateWithId]
      .cache()

    val dfGuardRail = preAggRawWithMultipliers.transform(dsGuardRail.createGuardRail(guardRailLookBack))

    val preAgg = preAggRawWithMultipliers
      .filter($"the_date" >= churnStartDate) // do not restate dates needed for guard rail lookback
      .transform(dfGuardRail.applyGuardRail)

    val agg = preAgg
      .transform(aggregate)
      .transform(csb2010To2020Crosswalk.crosswalk(
        "census_blockid",
        "secondary_census_blockid",
        "adjusted_wins",
        "adjusted_losses"))
      .transform(addGeoColumns(spark.emptyDataset[CensusBlockNoSpLookup], csbCrosswalk2020Lookup, stateDimIdLookup, $"census_blockid"))
      .as[BroadbandPlatformAggregate]

    PlatformAggregateOutput(agg, preAgg, preAggRaw, dfGuardRail)
  }

  /**
    * Prepares platform pre aggregate
    *
    * @param churn                         all churn dataset
    * @param csbNoSpLookup                 geo-information based on census block (no-SP)
    * @param stateDimIdLookup              legacy state-dim-id lookup
    * @param csbCrosswalkLookup            geo-information based on census block (syndicated)
    * @param carrierAggRollup              agg carrier rollup
    * @param allowedNetworkBrandPairLookup network brand lookup for d_common_ocn
    * @param dCommonOcnLookup              wireless sp group dimension
    * @param primarySp                     primary (source) SP
    * @param secondarySp                   secondary (dest) SP
    * @param primaryLocation               primary (source) location
    * @param secondaryLocation             secondary (dest) location
    * @param aggCount                      which column to aggregate
    * @param aggZero                       which column set to 0
    * @return platform aggregate
    */
  def prepareAgg[T](
    churn: Dataset[T],
    csbNoSpLookup: Dataset[CensusBlockNoSpLookup],
    csbCrosswalkLookup: Dataset[CensusBlockCrosswalk],
    stateDimIdLookup: Dataset[LegacyStateDimId],
    carrierAggRollup: Dataset[CarrierAggRollup],
    allowedNetworkBrandPairLookup: Dataset[AllowedNetworkBrandPair],
    dCommonOcnLookup: Dataset[DCommonOcnLookup]
  )(primarySp: String,
    secondarySp: String,
    primaryLocation: String,
    secondaryLocation: String,
    aggCount: String,
    aggZero: String
  ): Dataset[BroadbandPlatformAggregateWithId] = {
    churn
      .transform(addGeoColumns(csbNoSpLookup, csbCrosswalkLookup, stateDimIdLookup, col(primaryLocation)))
      .withColumnRenamed(primarySp, "primary_sp_group")
      .withColumnRenamed(secondarySp, "secondary_sp_group")
      .withColumnRenamed(primaryLocation, "census_blockid")
      .withColumnRenamed(secondaryLocation, "secondary_census_blockid")
      .withColumn("mover_ind", $"census_blockid" =!= $"secondary_census_blockid")
      .transform(carrierAggRollup.combineCarriers($"primary_sp_group", $"census_blockid"))
      .transform(carrierAggRollup.combineCarriers($"secondary_sp_group", $"secondary_census_blockid"))
      .withColumn("event_type", lit(EVENT_TYPE_BB_CHURN))
      .transform(eliminateArtificialIntraSpChurn)
      .withColumn(aggCount, lit(1))
      .withColumn(aggZero, lit(0))
      .withColumnRenamed("churn_date", "the_date")
      .as[BroadbandPlatformAggregateWithId]
      .transform(removeDuplicates(List($"distance", $"mover_ind")))
      .transform(preAggregate(aggCount, aggZero))
  }

  /**
    * Set losing census block id to winner census blockid so that winner == loser if under a certain distance threshold
    * @param churn                  Churn dataset. Either all churn or agg churn
    * @param shortMoveDist          Distance threshold. Default to 10,000
    * @return                       Dataset with an updated loser_census_block_id
    */
  def distanceRule[T : Encoder](shortMoveDist: Int = 10000)(churn: Dataset[T]): Dataset[T] = {
    churn
      .withColumn("loser_census_block_id",
        when($"distance" <= shortMoveDist, $"winner_census_block_id")
          .otherwise($"loser_census_block_id"))
      .as[T]
  }

  /**
    * Ensure only element_id the_date are unique to avoid duplicates.
    *
    * @param allChurn all churn dataset
    * @return deduped allChurn
    */
  def removeDuplicates(highPriorityCols: List[Column])(allChurn: Dataset[BroadbandPlatformAggregateWithId]): Dataset[BroadbandPlatformAggregateWithId] = {
    val orderByCols: List[Column] =  highPriorityCols ++ BroadbandPlatformAggregateWithId
      .colNames
      .diff(Seq("element_id", "the_date", "mover_ind"))
      .diff(highPriorityCols)
      .map(col)

    val w = Window
      .partitionBy($"element_id", $"the_date")
      .orderBy(orderByCols: _*)

    allChurn
      .withColumn("rank", row_number().over(w))
      .filter($"rank" === 1)
      .as[BroadbandPlatformAggregateWithId]
  }

  /**
    * Add network and brand columns
    *
    * @param ifaWirelessCarrier            Install base lookup table to map ifa -> network and brand
    * @param churn                         churn dataset
    * @return
    */
  def addNetworkBrandColumns(
    ifaWirelessCarrier: Dataset[IfaWirelessCarrier],
  )(churn: Dataset[BroadbandAggregatedChurn]): Dataset[BroadbandAggregatedChurn25] = {
    val w = Window
      .partitionBy($"ifa", $"churn_date")
      .orderBy($"hasNetwork".desc, $"hasBrand".desc, $"count".desc, $"network", $"brand") //network is final tiebreaker

    val bestNetwork = churn
      .join(ifaWirelessCarrier, Seq("ifa"), "left")
      .groupBy($"ifa", $"churn_date", $"network", $"brand")
      .agg(count(lit(1)) as "count")
      .withColumn("hasNetwork", when(coalesce($"network", lit("")) === "", 0).otherwise(1))
      .withColumn("hasBrand", when(coalesce($"brand", lit("")) === "", 0).otherwise(1))
      .withColumn("rank", row_number().over(w))
      .filter($"rank" === 1)
      .select($"ifa", $"churn_date", $"network", $"brand")

    churn
      .join(bestNetwork, Seq("ifa", "churn_date"), "inner")
      .select(
        churn("*"),
        bestNetwork("network"),
        bestNetwork("brand"))
      .select(BroadbandAggregatedChurn25.cols:_*)
      .as[BroadbandAggregatedChurn25]
  }

  /**
    * Add wireless_sp_group from lookup tables
    * @param ifaWirelessCarrier            Lookup table for ifa -> raw network + brand
    * @param allowedNetworkBrandPairLookup Lookup table for network + brand to common_name
    * @param dCommonOcnLookup              common_name lookup to wireless_sp_group
    * @param churn                         churn dataset
    * @return dataframe with wireless_sp_group column
    */
  def addWirelessSPColumns[T](
    allowedNetworkBrandPairLookup: Dataset[AllowedNetworkBrandPair],
    dCommonOcnLookup: Dataset[DCommonOcnLookup]
  )(churn: Dataset[T]): DataFrame = {
    val networkBrandLookup = allowedNetworkBrandPairLookup
      .join(dCommonOcnLookup, Seq("common_name"), "left")
      .select(
        $"network",
        coalesce($"brand", lit("")) as "_brand",
        $"ocn_common_dim_id" as "wireless_sp_group",
        $"common_name"
      )

    churn
      .withColumn("_brand", coalesce($"brand", lit("")))
      .join(broadcast(networkBrandLookup), Seq("network", "_brand"), "left")
      .select(
        churn("*"),
        networkBrandLookup("common_name"),
        networkBrandLookup("wireless_sp_group"))
      .withColumn("brand", coalesce($"common_name", $"brand"))
      .drop("common_name")
  }

  /**
    * pre Aggregate (count adjusted wins/loses) per main columns
    *
    * @param aggCountColumnName which column to count
    * @param aggZeroColumnName  which column set to 0
    * @param df                 augmented all-churn (with geographic columns)
    * @return broadband aggregate for the platform
    */
  def preAggregate(
    aggCountColumnName: String,
    aggZeroColumnName: String
  )(df: Dataset[BroadbandPlatformAggregateWithId]): Dataset[BroadbandPlatformAggregateWithId] = {

    val groupByCols: List[Column] = BroadbandPlatformAggregateWithId.colNames
      .diff(Seq(
        aggZeroColumnName,
        aggCountColumnName))
      .map(col)

    df
      .withColumn("rank", row_number().over(Window.partitionBy(groupByCols: _*).orderBy(aggCountColumnName)))
      .filter($"rank" === 1)
      .withColumn(aggCountColumnName, lit(1))
      .withColumn(aggZeroColumnName, lit(0))
      .select(BroadbandPlatformAggregateWithId.cols: _*)
      .as[BroadbandPlatformAggregateWithId]
  }

  /**
    * Aggregate wins and losses across all columns
    * @param df Pre Agg - FWA multiplier and guard rails
    * @return aggregated data
    */
  def aggregate(df: Dataset[BroadbandPlatformAggregateWithId]): Dataset[BroadbandPlatformAggregate] = {
    val groupByCols: List[Column] = BroadbandPlatformAggregate.colNames
      .diff(Seq("adjusted_wins", "adjusted_losses"))
      .map(col) ++ Seq($"w_or_l")

    df
      .withColumn("w_or_l", when($"adjusted_wins" > $"adjusted_losses", lit("w")).otherwise(lit("l")))
      .groupBy(groupByCols: _*)
      .agg(
        sum("adjusted_wins").cast(DoubleType) as "adjusted_wins",
        sum("adjusted_losses").cast(DoubleType) as "adjusted_losses")
      .select(BroadbandPlatformAggregate.cols: _*)
      .as[BroadbandPlatformAggregate]
  }


  /**
    * A set of rules to filter out invalid churn and movers.
    * @param churn aggregated churn dataset
    * @return Filtered down aggregated churn data
    */
  def movers(churn: Dataset[BroadbandAggregatedChurn25])(implicit spark: SparkSession): DataFrame = {
    churn
      .transform(distanceRule(10000))
      .withColumn("event_type", lit(EVENT_TYPE_BB_CHURN))
      .transform(eliminateIntraSpChurnFromBbChurn)
      .withColumn("loser_census_block_id", substring($"loser_census_block_id", 1, 15))
      .withColumn("winner_census_block_id", substring($"winner_census_block_id", 1, 15))
      .filter($"loser_sp_platform" =!= $"winner_sp_platform")
      .select(churn.columns.map(col):_*)
  }

  /**
    * Eliminate intra-SP movements (where a service provider does not change).
    *
    * @param ds broadband churn
    * @return filtered churn
    */
  def eliminateIntraSpChurnFromBbChurn(ds: DataFrame): DataFrame = {
    ds.filter($"event_type" =!= EVENT_TYPE_BB_CHURN || $"loser_sp_platform" =!= $"winner_sp_platform")
  }

  /**
    * Eliminate intra-SP movements (where a service provider does not change).
    *
    * @param ds platform agg
    * @return filtered agg
    */
  def eliminateArtificialIntraSpChurn(ds: DataFrame): DataFrame = {
    ds.filter($"event_type" === EVENT_TYPE_TRANSFERS || $"primary_sp_group" =!= $"secondary_sp_group")
  }
}

object PlatformAggregate {
  val EVENT_TYPE_BB_CHURN = "bb_churn"
  val EVENT_TYPE_TRANSFERS = "transfers"

  def apply()(implicit spark: SparkSession) = new PlatformAggregate()

  case class PlatformAggregateOutput(
    agg: Dataset[BroadbandPlatformAggregate],
    preAgg: Dataset[BroadbandPlatformAggregateWithId],
    preAggRaw: Dataset[BroadbandPlatformAggregateWithId],
    guardRail: Dataset[GuardRailTransform]
  )

  /**
    * Add geographic columns from syndicated geo-lookup table.
    *
    * @param csbNoSpLookup      geo-information based on census block (no-SP based)
    * @param csbCrosswalkLookup geo-information based on census block (syndicated)
    * @param stateDimIdLookup   legacy state-dim-id lookup
    * @param censusBlockColumn  census block column in dataset
    * @param ds                 dataset
    * @return dataframe with geographic columns
    */
  def addGeoColumns[T](
    csbNoSpLookup: Dataset[CensusBlockNoSpLookup],
    csbCrosswalkLookup: Dataset[CensusBlockCrosswalk],
    stateDimIdLookup: Dataset[LegacyStateDimId],
    censusBlockColumn: Column
  )(ds: Dataset[T])(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    val geoCols = Seq("state", "zip_cd", "cbsa", "bta", "cma", "dma")
    val dsNoGeoCols = ds.columns.filterNot(c => geoCols.contains(c)).map(col)
    val coalescedCols = geoCols.map(c => coalesce(col(c + "_new"), col(c)).as(c))

    val csbNoSpLookupSelection = csbNoSpLookup.select(
      $"block_group",
      $"zip" as "zip_cd",
      $"state_dim_id" as "state",
      $"bta",
      $"cbsa",
      $"dma",
      $"cma")

    val dsWithNoSpLookup = ds.select(dsNoGeoCols:_*).as("a")
      .join(
        broadcast(csbNoSpLookupSelection.as("geo")),
        $"geo.block_group" === substring(censusBlockColumn, 1, 12),
        "left")

    val csbCrosswalkLookupRequired = csbCrosswalkLookup
      .select(
        $"serv_terr_blockid",
        $"zip5" as "zip_cd_new",
        $"cbsa_fips_full" as "cbsa_new",
        $"state_fips",
        $"bta" as "bta_new",
        $"cma" as "cma_new",
        $"dma" as "dma_new")
      .distinct().cache()

    val stateDimIdLookupRequired = stateDimIdLookup
      .select($"state_fips", $"state_dim_id" as "state_new")
      .distinct().cache()

    dsWithNoSpLookup.as("ds")
      .join(broadcast(csbCrosswalkLookupRequired), censusBlockColumn === $"serv_terr_blockid", "left")
      .join(broadcast(stateDimIdLookupRequired), Seq("state_fips"), "left")
      .select(coalescedCols ++ dsNoGeoCols:_*)
  }
}
