package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation

import org.apache.spark.sql.Column
import org.apache.spark.sql.functions.col

package object schema {

  object types {
    type CensusBlock = String
    type County = String
    type Submarket = String
  }

  object common_columns {
    val colHouseholdIdName: String = "household_id"
    val colHouseholdId: Column = col(colHouseholdIdName)

    val colCountyName: String = "county"
    val colCounty: Column = col(colCountyName)

    val colCensusBlockIdName: String = "census_block_id"
    val colCensusBlockId: Column = col(colCensusBlockIdName)

    val colTargetCensusBlockIdName: String = "target_census_block_id"
    val colTargetCensusBlockId: Column = col(colTargetCensusBlockIdName)

    val colTargetSubmarketName: String = "target_submarket"
    val colTargetSubmarket: Column = col(colTargetSubmarketName)

    val colSubmarketName: String = "submarket"
    val colSubmarket: Column = col(colSubmarketName)

    val colLatitude: Column = col("latitude")
    val colLongitude: Column = col("longitude")

    val colTargetLatitudeName: String = "target_latitude"
    val colTargetLatitude: Column = col(colTargetLatitudeName)

    val colTargetLongitudeName: String = "target_longitude"
    val colTargetLongitude: Column = col(colTargetLongitudeName)

    val colWeightFactorName: String = "weight_factor"
    val colWeightFactor: Column = col(colWeightFactorName)

    val colWeightAllocatedName: String = "weight_allocated"
    val colWeightAllocated: Column = col(colWeightAllocatedName)

    val colWeightAllocatedCumulativeName: String = "weight_allocated_cumulative"
    val colWeightAllocatedCumulative: Column = col(colWeightAllocatedCumulativeName)

    val colCapacityName: String = "capacity"
    val colCapacity: Column = col(colCapacityName)

    val colHouseholdsName: String = "households"
    val colHouseholds: Column = col(colHouseholdsName)
  }
}
