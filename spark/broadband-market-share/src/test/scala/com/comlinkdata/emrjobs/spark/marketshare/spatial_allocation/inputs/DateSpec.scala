package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs

import com.comlinkdata.commons.testing.CldBaseSpec
import java.time.LocalDate

class DateSpec extends CldBaseSpec {
  describe("date calculations") {
    it("correctly computes quarter's first date") {
      getFirstDate(LocalDate.parse("2022-01-01")) shouldBe LocalDate.parse("2022-01-01")
      getFirstDate(LocalDate.parse("2022-01-31")) shouldBe LocalDate.parse("2022-01-01")
      getFirstDate(LocalDate.parse("2022-02-01")) shouldBe LocalDate.parse("2022-01-01")
      getFirstDate(LocalDate.parse("2022-02-28")) shouldBe LocalDate.parse("2022-01-01")
      getFirstDate(LocalDate.parse("2022-03-01")) shouldBe LocalDate.parse("2022-01-01")
      getFirstDate(LocalDate.parse("2022-03-31")) shouldBe LocalDate.parse("2022-01-01")
      getFirstDate(LocalDate.parse("2022-04-01")) shouldBe LocalDate.parse("2022-04-01")
      getFirstDate(LocalDate.parse("2022-04-30")) shouldBe LocalDate.parse("2022-04-01")
      getFirstDate(LocalDate.parse("2022-05-01")) shouldBe LocalDate.parse("2022-04-01")
      getFirstDate(LocalDate.parse("2022-05-31")) shouldBe LocalDate.parse("2022-04-01")
      getFirstDate(LocalDate.parse("2022-06-01")) shouldBe LocalDate.parse("2022-04-01")
      getFirstDate(LocalDate.parse("2022-06-30")) shouldBe LocalDate.parse("2022-04-01")
      getFirstDate(LocalDate.parse("2022-07-01")) shouldBe LocalDate.parse("2022-07-01")
      getFirstDate(LocalDate.parse("2022-07-31")) shouldBe LocalDate.parse("2022-07-01")
      getFirstDate(LocalDate.parse("2022-08-01")) shouldBe LocalDate.parse("2022-07-01")
      getFirstDate(LocalDate.parse("2022-08-31")) shouldBe LocalDate.parse("2022-07-01")
      getFirstDate(LocalDate.parse("2022-09-01")) shouldBe LocalDate.parse("2022-07-01")
      getFirstDate(LocalDate.parse("2022-09-30")) shouldBe LocalDate.parse("2022-07-01")
      getFirstDate(LocalDate.parse("2022-10-01")) shouldBe LocalDate.parse("2022-10-01")
      getFirstDate(LocalDate.parse("2022-10-31")) shouldBe LocalDate.parse("2022-10-01")
      getFirstDate(LocalDate.parse("2022-11-01")) shouldBe LocalDate.parse("2022-10-01")
      getFirstDate(LocalDate.parse("2022-11-30")) shouldBe LocalDate.parse("2022-10-01")
      getFirstDate(LocalDate.parse("2022-12-01")) shouldBe LocalDate.parse("2022-10-01")
      getFirstDate(LocalDate.parse("2022-12-31")) shouldBe LocalDate.parse("2022-10-01")
    }
  }
}
