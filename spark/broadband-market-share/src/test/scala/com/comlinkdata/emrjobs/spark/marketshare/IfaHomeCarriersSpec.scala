package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.Linkage.Fields
import com.comlinkdata.commons.testing.generators.broadband.{genIfaHomeCarrier, genVerizonFwIpBlock}
import com.comlinkdata.commons.testing.generators.common.{genIfa, generate}
import com.comlinkdata.commons.testing.generators.udp.genUdpIfaIpAgg
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband_market_share.IfaHomeCarrierNoDate
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.tier2.UdpIfaIpAgg
import com.typesafe.scalalogging.LazyLogging

class IfaHomeCarriersSpec extends CldSparkBaseSpec with LazyLogging {

  import spark.implicits._


  describe("Verizon Fixed Wireless") {
    val carrierLookupData = Seq(
      CarrierLookup("Carrier A", "A", "1", null, "1", "1", "2016-11-01"),
    )

    it("Verizon FW IFAs are added in home-carrier") {
      val ifaIpAggVerizonFw = generate(2, genUdpIfaIpAgg()).map(_.copy(
        connection_type = "cellular", carrier = "Verizon Fixed Wireless", ip = Array(74, 75, 1, 2)
      ))
      val verizonIpBlocks = generate(genVerizonFwIpBlock()).map(_.copy(ip_2_octets = "74.75"))

      // IFA-IP aggregate for broadband
      val ifaIpAggBroadband = generate(2, genUdpIfaIpAgg())

      val ifaHomeCarrierAggData = Fields.oneToOne("ifa").link(
        ifaIpAggBroadband, generate(2, genIfaHomeCarrier()))

      val ifaIpAgg = ifaIpAggVerizonFw ++ ifaIpAggBroadband

      val hc = IfaHomeCarriers()
      val result = hc.runDay(
        ifaHomeCarrierAggData.toDS(),
        ifaIpAgg.toDS(),
        verizonIpBlocks.toDS(),
        Seq.empty[Ifa].toDF("ifa").as[Ifa]        
      )

      // Verizon FW carrier will be copied from IFA-IP agg
      assert(result.filter(_.carrier == "Verizon Fixed Wireless").count() === 2)
    }

    it("Verizon FW IFAs are overwritten in home-carrier") {
      val ifaIpAgg = generate(2, genUdpIfaIpAgg()).map(_.copy(
        connection_type = "cellular", carrier = "Verizon Fixed Wireless", ip = Array(74, 75, 1, 2)
      ))
      val verizonIpBlocks = generate(genVerizonFwIpBlock()).map(_.copy(ip_2_octets = "74.75"))
      // This ip sequence will be overwritten with verizon FW carrier
      val ifaHomeCarrierAggData = Fields.oneToOne("ifa").link(
        ifaIpAgg, generate(2, genIfaHomeCarrier()))

      val hc = IfaHomeCarriers()
      val result = hc.runDay(
        ifaHomeCarrierAggData.toDS(),
        ifaIpAgg.toDS(),
        verizonIpBlocks.toDS(),
        Seq.empty[Ifa].toDF("ifa").as[Ifa]
      )

      // Verizon FW carrier will overwrite carrier of IP sequence
      assert(result.filter(_.carrier == "Verizon Fixed Wireless").count() === 2)
    }

    it("computes non cellular connection type") {
      val ifaIpAgg = generate(2, genUdpIfaIpAgg()).map(_.copy(
        connection_type = "wifi", carrier = "Verizon Fixed Wireless", ip = Array(74, 75, 1, 2)
      ))
      val verizonIpBlocks = generate(genVerizonFwIpBlock()).map(_.copy(ip_2_octets = "74.75"))
      // This ip sequence will be overwritten with verizon FW carrier
      val ifaHomeCarrierAggData = Fields.oneToOne("ifa").link(
        ifaIpAgg, generate(2, genIfaHomeCarrier()))

      val hc = IfaHomeCarriers()
      val result = hc.runDay(
        ifaHomeCarrierAggData.toDS(),
        ifaIpAgg.toDS(),
        verizonIpBlocks.toDS(),
        Seq.empty[Ifa].toDF("ifa").as[Ifa]
      )

      // Verizon FW carrier will overwrite carrier of IP sequence
      assert(result.filter(_.carrier == "Verizon Fixed Wireless").count() === 2)
    }
  }

  describe("IFA-Home carrier") {
    val carrierLookupData = Seq(
      CarrierLookup("Carrier A", "A", "1", null, "1", "1", "2016-11-01"),
    )

    it("computes home carrier without verizon FW") {
      val verizonIpBlocks = Seq.empty[VerizonFwIp]

      val ifaIpAgg = generate(2, genUdpIfaIpAgg())
      val ifaHomeCarrierAggData = Fields.oneToOne("ifa").link(
        ifaIpAgg, generate(2, genIfaHomeCarrier()))

      val hc = IfaHomeCarriers()
      val result = hc.runDay(
        ifaHomeCarrierAggData.toDS(),
        ifaIpAgg.toDS(),
        verizonIpBlocks.toDS(),
        Seq.empty[Ifa].toDF("ifa").as[Ifa]
      )

      assert(result.count() === 2)
    }
  }

  describe("Fixed Wireless") {
    val carrierLookupData = Seq(
      CarrierLookup("Carrier A", "A", "1", null, "1", "1", "2016-11-01"),
    )

    val verizonIpBlocks = generate(genVerizonFwIpBlock()).map(_.copy(ip_2_octets = "74.75"))

    it("no fwa picks no fwa") {
      val ifaIpAgg = spark.emptyDataset[UdpIfaIpAgg]
      val ifaHomeCarrierAggData = generate(1, genIfaHomeCarrier()).map(_.copy(carrier = "Carrier A"))

      val hc = IfaHomeCarriers()
      val result = hc.runDay(
        ifaHomeCarrierAggData.toDS(),
        ifaIpAgg,
        verizonIpBlocks.toDS(),
        Seq.empty[Ifa].toDF("ifa").as[Ifa]
      )
      val expected = result.select("carrier").collect().head.getAs[String](0)
      assert(result.count() === 1)
      assert(expected == "Carrier A")
    }

    it("tmo picks tmo") {
      val ifaIpAgg = generate(genUdpIfaIpAgg())
        .map(_.copy(connection_type = "cellular", carrier = "T-Mobile USA", ip = Array(74, 75, 1, 2)))
      val ifaHomeCarrierAggData = spark.emptyDataset[IfaHomeCarrierNoDate]
      val ifaSetFiltered = ifaIpAgg.toDS().select("ifa").distinct().as[Ifa]

      val hc = IfaHomeCarriers()
      val result = hc.runDay(
        ifaHomeCarrierAggData,
        ifaIpAgg.toDS(),
        verizonIpBlocks.toDS(),
        ifaSetFiltered
      )
      val expected = result.select("carrier").collect().head.getAs[String](0)
      assert(result.count() === 1)
      assert(expected == TMOBILE_FIXED_WIRELESS)
    }

    it("vzw picks vzw") {
      val ifaIpAgg = generate(genUdpIfaIpAgg())
        .map(_.copy(connection_type = "cellular", carrier = "Verizon", ip = Array(74, 75, 1, 2)))
      val ifaHomeCarrierAggData = spark.emptyDataset[IfaHomeCarrierNoDate]
      val ifaSetFiltered = ifaIpAgg.toDS().select("ifa").distinct().as[Ifa]

      val hc = IfaHomeCarriers()
      val result = hc.runDay(
        ifaHomeCarrierAggData,
        ifaIpAgg.toDS(),
        verizonIpBlocks.toDS(),
        ifaSetFiltered
      )

      val expected = result.select("carrier").collect().head.getAs[String](0)
      assert(result.count() === 1)
      assert(expected == VERIZON_FIXED_WIRELESS)
    }

    it("no fwa + vzw picks vzw") {
      val ifaIpAgg = generate(genUdpIfaIpAgg())
        .map(_.copy(connection_type = "cellular", carrier = "Verizon", ip = Array(74, 75, 1, 2)))
      val ifaHomeCarrierAggData = Fields
        .oneToOne("ifa")
        .link(ifaIpAgg, generate(1, genIfaHomeCarrier()).map(_.copy(carrier = "Carrier A")))
      val ifaSetFiltered = ifaIpAgg.toDS().select("ifa").distinct().as[Ifa]


      val hc = IfaHomeCarriers()
      val result = hc.runDay(
        ifaHomeCarrierAggData.toDS(),
        ifaIpAgg.toDS(),
        verizonIpBlocks.toDS(),
        ifaSetFiltered
      )
      val expected = result.select("carrier").collect().head.getAs[String](0)
      assert(result.count() === 1)
      assert(expected == VERIZON_FIXED_WIRELESS)
    }

    it("no fwa + tmo picks tmo") {
      val ifaIpAgg = generate(genUdpIfaIpAgg())
        .map(_.copy(connection_type = "cellular", carrier = "T-Mobile USA", ip = Array(74, 75, 1, 2)))
      val ifaHomeCarrierAggData = Fields
        .oneToOne("ifa")
        .link(ifaIpAgg, generate(1, genIfaHomeCarrier()).map(_.copy(carrier = "Carrier A")))
      val ifaSetFiltered = ifaIpAgg.toDS().select("ifa").distinct().as[Ifa]

      val hc = IfaHomeCarriers()
      val result = hc.runDay(
        ifaHomeCarrierAggData.toDS(),
        ifaIpAgg.toDS(),
        verizonIpBlocks.toDS(),
        ifaSetFiltered
      )
      val expected = result.select("carrier").collect().head.getAs[String](0)
      assert(result.count() === 1)
      assert(expected == TMOBILE_FIXED_WIRELESS)
    }


    it("no fwa + tmo + vzw picks tmo") {
      val ifa = generate(genIfa).head
      val ifaIpAgg = Seq("T-Mobile USA", "Verizon").flatMap { carrier =>
        generate(genUdpIfaIpAgg())
          .map(_.copy(ifa = ifa, connection_type = "cellular", carrier = carrier, ip = Array(74, 75, 1, 2)))
      }

      val ifaHomeCarrierAggData = Fields
        .oneToOne("ifa")
        .link(ifaIpAgg, generate(1, genIfaHomeCarrier()).map(_.copy(carrier = "Carrier A")))

      val ifaSetFiltered = ifaIpAgg.toDS().select("ifa").distinct().as[Ifa]

      val hc = IfaHomeCarriers()
      val result = hc.runDay(
        ifaHomeCarrierAggData.toDS(),
        ifaIpAgg.toDS(),
        verizonIpBlocks.toDS(),
        ifaSetFiltered
      )
      val expected = result.select("carrier").collect().head.getAs[String](0)
      assert(result.count() === 1)
      assert(expected == TMOBILE_FIXED_WIRELESS)
    }
  }
}