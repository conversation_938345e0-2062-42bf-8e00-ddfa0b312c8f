package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.commons.testing.generators.common.{genIfa, genIpV4, generate}
import com.comlinkdata.commons.testing.{CldSparkBaseSpec, CldSpecGenerators}
import com.comlinkdata.emrjobs.spark.marketshare.job.IfaIpCarrierJobRunner.{filterResidentialBroadbandHours, quadrantSuspiciousIp, revealEndDateForBroadband, transformUdpMapSlimNoIpToIfaHomeCarrier, tsInUdpMap}
import com.comlinkdata.largescale.commons.{LocalDateRange, Utils}
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandIfaIpCarrier, BroadbandIfaIpCarrierWithHour, IfaIpCarrierNoDate}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.apache.spark.sql.functions.{col, date_format, dayofweek, lit}
import org.junit.rules.TemporaryFolder

import java.net.URI
import java.sql.{Date, Timestamp}
import java.time.{LocalDate, LocalDateTime}

class IfaIpCarrierJobSpec extends CldSparkBaseSpec {

  import spark.implicits._

  describe("full run"){
    val now = LocalDateTime.of(2023, 7, 15, 12, 0)
    val newTimestamp = Timestamp.valueOf(now)
    val startDate = now.toLocalDate

    val udpMapData = Seq(
      BroadbandUdpMapping(
        ifa = "1234", //: String,
        ip = Array(192.toByte, 168.toByte, 0.toByte, 1.toByte), //: Ip,
        carrier = Some("carrier"), //: Option[String],
        datetime = newTimestamp, //: Timestamp,
        local_datetime = Some(newTimestamp), //: Option[Timestamp],
        locationtype = "locType", //: String,
        raw_connectiontype = Some("WIFI"), //: Option[String],
        connectiontype_2020 = Some("WIFI"), //: Option[String],
        connectiontype = Some("WIFI"), //: Option[String],
        mm_connectiontype = Some("WIFI"), //: Option[String],
        latitude = 26, //: Float,
        longitude = -80, //: Float
      )
    )

    val suspiciousIpCarrierData = Seq(
      CarrierLookup(
        mw_carrier = "test",
        consolidated_carrier = "test",
        consolidated_id = "test",
        service_territory_name = Some("test"),
        sp = "test",
        sp_platform = "test",
        min_date = "2016-01-01",
      )
    )

    it("with parquet"){
      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI
      val config: IfaIpCarrierJobConfig = IfaIpCarrierJobConfig(
        udpMappingLocation = basePath.resolve("ifa_ip_carrier_udp_mapper"),
        datasources = List(ComlinkdataDatasource.mw07),
        suspiciousIpCarrierExclustionLocation = basePath.resolve("ifa_ip_carrier_suspicious_ip_carrier"),
        discardLocation = basePath.resolve("ifa_ip_carrier_discard"),
        outputLocation = basePath.resolve("ifa_ip_carrier_output_location"),
        numOutputFiles = 10,
        dateRangeOpt = Some(LocalDateRange.of(startDate, startDate)),
        dsExcludeCarrierList = Some(Seq("test"))
      )

      udpMapData.toDS()
        .withColumn("date", lit(startDate))
        .withColumn("ds", lit("mw07"))
        .write
        .partitionBy("ds", "date")
        .parquet(config.udpMappingLocation.toString)

      suspiciousIpCarrierData.toDS().write.parquet(config.suspiciousIpCarrierExclustionLocation.toString)

      IfaIpCarrierJobRunner.runJob(config)

      val good = BroadbandIfaIpCarrier.read(config.outputLocation, LocalDateRange(startDate, startDate), List(ComlinkdataDatasource.mw07))

      good.filter($"suspiciousIp" === false).count() shouldBe 1
    }

    it("with parquet multiple ds") {
      val udpMapDataQuadrant = Seq(
        BroadbandUdpMapping(
          ifa = "1234", //: String,
          ip = Array(192.toByte, 168.toByte, 0.toByte, 1.toByte), //: Ip,
          carrier = Some("carrier"), //: Option[String],
          datetime = newTimestamp, //: Timestamp,
          local_datetime = Some(newTimestamp), //: Option[Timestamp],
          locationtype = "locType", //: String,
          raw_connectiontype = Some("WIFI"), //: Option[String],
          connectiontype_2020 = Some("WIFI"), //: Option[String],
          connectiontype = Some("WIFI"), //: Option[String],
          mm_connectiontype = Some("WIFI"), //: Option[String],
          latitude = 26, //: Float,
          longitude = -80, //: Float
        ))
      val udpMapDataMw07 = Seq(BroadbandUdpMapping(
        ifa = "2345", //: String,
        ip = Array(192.toByte, 172.toByte, 0.toByte, 1.toByte), //: Ip,
        carrier = Some("carrier 2"), //: Option[String],
        datetime = newTimestamp, //: Timestamp,
        local_datetime = Some(newTimestamp), //: Option[Timestamp],
        locationtype = "locType", //: String,
        raw_connectiontype = Some("WIFI"), //: Option[String],
        connectiontype_2020 = Some("WIFI"), //: Option[String],
        connectiontype = Some("WIFI"), //: Option[String],
        mm_connectiontype = Some("WIFI"), //: Option[String],
        latitude = 26, //: Float,
        longitude = -80, //: Float
      )
      )

      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI
      val config: IfaIpCarrierJobConfig = IfaIpCarrierJobConfig(
        udpMappingLocation = basePath.resolve("ifa_ip_carrier_udp_mapper"),
        datasources = List(ComlinkdataDatasource.quadrant, ComlinkdataDatasource.mw07),
        suspiciousIpCarrierExclustionLocation = basePath.resolve("ifa_ip_carrier_suspicious_ip_carrier"),
        discardLocation = basePath.resolve("ifa_ip_carrier_discard"),
        outputLocation = basePath.resolve("ifa_ip_carrier_output_location"),
        numOutputFiles = 10,
        dateRangeOpt = Some(LocalDateRange.of(startDate, startDate)),
        dsExcludeCarrierList = Some(Seq("test"))
      )

      udpMapDataQuadrant.toDS()
        .withColumn("date", lit(startDate))
        .withColumn("ds", lit("quadrant"))
        .union(udpMapDataMw07.toDS()
          .withColumn("date", lit(startDate))
          .withColumn("ds", lit("mw07"))).show()

      udpMapDataQuadrant.toDS()
        .withColumn("date", lit(startDate))
        .withColumn("ds", lit("quadrant"))
        .union(udpMapDataMw07.toDS()
          .withColumn("date", lit(startDate))
          .withColumn("ds", lit("mw07")))
        .write
        .partitionBy("ds", "date")
        .parquet(config.udpMappingLocation.toString)

      suspiciousIpCarrierData.toDS().write.parquet(config.suspiciousIpCarrierExclustionLocation.toString)

      IfaIpCarrierJobRunner.runJob(config)

      val good = BroadbandIfaIpCarrier.read(config.outputLocation, LocalDateRange(startDate, startDate), List(ComlinkdataDatasource.quadrant, ComlinkdataDatasource.mw07))
      good.show()

      good.filter($"suspiciousIp" === false).count() shouldBe 2
    }

    it("with parquet and quadrant suspicious ip filtering") {
      val quadrantUdpMapData = Seq(
        // 6 unique ifa on 1 ip in 1 day (bad records)
        (LocalDateTime.of(2023, 7, 15, 12, 0), Array(192.toByte, 168.toByte, 0.toByte, 1.toByte), "0001"),
        (LocalDateTime.of(2023, 7, 15, 12, 0), Array(192.toByte, 168.toByte, 0.toByte, 1.toByte), "0002"),
        (LocalDateTime.of(2023, 7, 15, 12, 0), Array(192.toByte, 168.toByte, 0.toByte, 1.toByte), "0003"),
        (LocalDateTime.of(2023, 7, 15, 12, 0), Array(192.toByte, 168.toByte, 0.toByte, 1.toByte), "0004"),
        (LocalDateTime.of(2023, 7, 15, 12, 0), Array(192.toByte, 168.toByte, 0.toByte, 1.toByte), "0005"),
        (LocalDateTime.of(2023, 7, 15, 12, 0), Array(192.toByte, 168.toByte, 0.toByte, 1.toByte), "0006"),
        // 2 unique ifa on 1 ip in 1 day (good records)
        (LocalDateTime.of(2023, 7, 15, 12, 0), Array(10.toByte, 0.toByte, 0.toByte, 1.toByte), "0007"),
        (LocalDateTime.of(2023, 7, 15, 12, 0), Array(10.toByte, 0.toByte, 0.toByte, 1.toByte), "0008"),
      ).map { i =>
        BroadbandUdpMapping(
          ifa = i._3, //: String,
          ip = i._2, //: Ip,
          carrier = Some("carrier"), //: Option[String],
          datetime = Timestamp.valueOf(i._1), //: Timestamp,
          local_datetime = Some(Timestamp.valueOf(i._1)), //: Option[Timestamp],
          locationtype = "locType", //: String,
          raw_connectiontype = Some("WIFI"), //: Option[String],
          connectiontype_2020 = Some("WIFI"), //: Option[String],
          connectiontype = Some("WIFI"), //: Option[String],
          mm_connectiontype = Some("WIFI"), //: Option[String],
          latitude = 26, //: Float,
          longitude = -80, //: Float
        )
      }

      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI
      val config: IfaIpCarrierJobConfig = IfaIpCarrierJobConfig(
        udpMappingLocation = basePath.resolve("ifa_ip_carrier_udp_mapper_quadrant"),
        datasources = List(ComlinkdataDatasource.quadrant),
        suspiciousIpCarrierExclustionLocation = basePath.resolve("ifa_ip_carrier_suspicious_ip_carrier"),
        discardLocation = basePath.resolve("ifa_ip_carrier_discard_quadrant"),
        outputLocation = basePath.resolve("ifa_ip_carrier_output_location_quadrant"),
        numOutputFiles = 10,
        dateRangeOpt = Some(LocalDateRange.of(startDate, startDate)),
        dsExcludeCarrierList = Some(Seq("test"))
      )

      quadrantUdpMapData.toDS()
        .withColumn("date", lit(startDate))
        .withColumn("ds", lit("quadrant"))
        .write
        .partitionBy("ds", "date")
        .parquet(config.udpMappingLocation.toString)

      suspiciousIpCarrierData.toDS().write.parquet(config.suspiciousIpCarrierExclustionLocation.toString)

      IfaIpCarrierJobRunner.runJob(config)

      val good = BroadbandIfaIpCarrier.read(config.outputLocation, LocalDateRange(startDate, startDate), List(ComlinkdataDatasource.quadrant))

      good.filter($"suspiciousIp" === false).count() shouldBe 2
      good.filter($"suspiciousIp" === true).count() shouldBe 6
    }
  }

  describe("suspicious ip"){
    val now = LocalDateTime.now
    val newTimestamp = Timestamp.valueOf(now)
    val udpMappingSingle = BroadbandUdpMapping(
      ifa = "1234", //: String,
      ip = Array(192.toByte, 168.toByte, 0.toByte, 1.toByte), //: Ip,
      carrier = Some("carrier"), //: Option[String],
      datetime = newTimestamp, //: Timestamp,
      local_datetime = Some(newTimestamp), //: Option[Timestamp],
      locationtype = "locType", //: String,
      raw_connectiontype = Some("WIFI"), //: Option[String],
      connectiontype_2020 = Some("WIFI"), //: Option[String],
      connectiontype = Some("WIFI"), //: Option[String],
      mm_connectiontype = Some("WIFI"), //: Option[String],
      latitude = 26, //: Float,
      longitude = -80, //: Float
    )

    it("returns one good record"){
      val df = (1 to 5).map(i => udpMappingSingle.copy(ifa = i.toString)).toDS()
      val out = df.transform(quadrantSuspiciousIp(Seq("")))
      out.count() shouldBe 1
    }
    it("ignores ipv6"){
      val ipv6 = (1 to 16).map(_.toByte).toArray[Byte]
      val df = (1 to 5).map(i => udpMappingSingle.copy(ifa = i.toString, ip = ipv6)).toDS()
      val out = df.transform(quadrantSuspiciousIp(Seq("")))
      out.count() shouldBe 0
    }
    it("selects only wifi connection types"){
      val df = (1 to 5).map(i => udpMappingSingle.copy(ifa = i.toString, connectiontype = Some("WIFI"))).toDS()
      val out = df.transform(quadrantSuspiciousIp(Seq("")))
      out.count() shouldBe 1
    }
    it("counts only 5 or more distinct ifas"){
      val df = (1 to 4).map(i => udpMappingSingle.copy(ifa = i.toString, connectiontype = Some("WIFI"))).toDS()
      val out = df.transform(quadrantSuspiciousIp(Seq("")))
      out.count() shouldBe 0
    }
    it("excludes specific carriers"){
      val suspiciousCarrierExcludeList = Seq("test")
      val df = (1 to 5).map(i => udpMappingSingle.copy(ifa = i.toString, carrier = Some("test"))).toDS()
      //val out = df.transform(quadrantSuspiciousIp(suspiciousIpCarrierData.toDS()))
      val out = df.transform(quadrantSuspiciousIp(suspiciousCarrierExcludeList))
      out.count() shouldBe 0
    }
  }
  describe("etl udp map") {
    val ifaString0 = CldSpecGenerators.newIfaString()
    val ifaByte0 = Utils.uuidString2Binary(ifaString0)
    val ip = CldSpecGenerators.newIp
    val lat = 26 // EST
    val lng = -80 // EST
    val now = LocalDateTime.now
    val newTimestamp = Timestamp.valueOf(now)
    val newDate = Date.valueOf(LocalDate.now)
    val newHour = now.getHour - 5

    val udpMapSeq = Seq(
      BroadbandUdpMapping(
        ifa = ifaString0, //: String,
        ip = ip, //: Ip,
        carrier = Some("carrier"), //: Option[String],
        datetime = newTimestamp, //: Timestamp,
        local_datetime = Some(newTimestamp), //: Option[Timestamp],
        locationtype = "locType", //: String,
        raw_connectiontype = Some("WIFI"), //: Option[String],
        connectiontype_2020 = Some("WIFI"), //: Option[String],
        connectiontype = Some("WIFI"), //: Option[String],
        mm_connectiontype = Some("WIFI"), //: Option[String],
        latitude = lat, //: Float,
        longitude = lng, //: Float
      ),
      BroadbandUdpMapping(
        ifa = ifaString0, //: String,
        ip = ip, //: Ip,
        carrier = Some("carrier"), //: Option[String],
        datetime = newTimestamp, //: Timestamp,
        local_datetime = Some(newTimestamp), //: Option[Timestamp],
        locationtype = "locType", //: String,
        raw_connectiontype = Some("CELLULAR"), //: Option[String],
        connectiontype_2020 = Some("CELLULAR"), //: Option[String],
        connectiontype = Some("CELLULAR"), //: Option[String],
        mm_connectiontype = Some("CELLULAR"), //: Option[String],
        latitude = lat, //: Float,
        longitude = lng, //: Float
      )
    )

    val ifaHomeCarrierSeq = Seq(
      BroadbandIfaIpCarrier(
        ifaByte0, //      ifa: Ifa,
        ip, //      ip: ip
        "carrier", //      carrier: Option[String],
        Some(false),
        "mw05",
        "2023",
        "04",
        "02"
      ),
      BroadbandIfaIpCarrier(
        ifaByte0, //      ifa: Ifa,
        ip, //      ip: ip
        "carrier", //      carrier: Option[String],
        Some(false),
        "mw05",
        "2023",
        "04",
        "02"
      )
    )

    val ifaCarrierKeepSeq = Seq(
      (Date.valueOf("2021-05-24"), 1), // Monday
      (Date.valueOf("2021-05-25"), 2), // Tuesday
      (Date.valueOf("2021-05-26"), 5), // Wednesday
      (Date.valueOf("2021-05-27"), 20), // Thursday
      (Date.valueOf("2021-05-28"), 20), // Friday
      (Date.valueOf("2021-05-29"), 14), // Saturday
      (Date.valueOf("2021-05-29"), 14), // Saturday
      (Date.valueOf("2021-05-30"), 14), // Sunday
      (Date.valueOf("2021-05-30"), 14), // Sunday
    ).map { i =>
      BroadbandIfaIpCarrierWithHour(
        ifaByte0, //      ifa: Ifa,
        ip, //      ip: Ip
        Some("carrier"), //      carrier: Option[String],
        i._2,
        false,
        "mw05",
        i._1.toLocalDate.getYear.toString,
        f"${i._1.toLocalDate.getMonthValue}%02d",
        f"${i._1.toLocalDate.getDayOfMonth}%02d"
      )
    }

    val ifaCarrierFilterSeq = Seq(
      (Date.valueOf("2021-05-24"), 7), // Monday
      (Date.valueOf("2021-05-25"), 12), // Tuesday
      (Date.valueOf("2021-05-26"), 13), // Wednesday
      (Date.valueOf("2021-05-27"), 15), // Thursday
      (Date.valueOf("2021-05-28"), 18) // Friday
    ).map { i =>
      BroadbandIfaIpCarrierWithHour(
        ifaByte0, //      ifa: Ifa,
        ip, //      ip: Ip
        Some("carrier"), //      carrier: Option[String],
        i._2,
        false,
        "mw05",
        i._1.toLocalDate.getYear.toString,
        f"${i._1.toLocalDate.getMonthValue}%02d",
        f"${i._1.toLocalDate.getDayOfMonth}%02d"
      )
    }

    // it("can transform udp map to IfaHomeCarrier") {
    //   val udpMapDs = udpMapSeq.toDS
    //   val result = udpMapDs
    //     .transform(transformUdpMapSlimNoIpToIfaHomeCarrier)
    //     .drop('hour)
    //     .drop('date)
    //     .as[BroadbandIfaIpCarrier]
    //   val expected = ifaHomeCarrierSeq.toDS

    //   assertDataFrameEquals(expected.toDF(), result.toDF())
    // }
    it("can filter for resident broadband hours") {
      val ifaHomeCarrierWithHourData = (ifaCarrierKeepSeq ++ ifaCarrierFilterSeq).toDS
      val result = ifaHomeCarrierWithHourData.transform(filterResidentialBroadbandHours(_)).toDF()
      val expected = ifaCarrierKeepSeq.toDS.toDF

      assertDataFrameEquals(expected, result)
    }
  }

  ignore("sql function api") {

    val dateData = Seq(
      Date.valueOf("2020-01-01")
    )

    it("can detect changes to sql functions api") {
      val dateDs = dateData.toDS
      val dateFormatInt = dateDs.toDF
        .withColumn("date_int", date_format(col("value"), "u"))
        .select("date_int")
        .first
        .get(0)

      val dayOfWeekInt = dateDs.toDF
        .withColumn("date_int", dayofweek(col("value")))
        .select("date_int")
        .first
        .get(0)

      dayOfWeekInt != dateFormatInt
    }
  }

  describe("tsInUdpMap") {
    it("can pull correct partitions for reveal & non-reveal") {
      val c = IfaIpCarrierJobConfig(
        udpMappingLocation = URI create "s3a://d000-comlinkdata-com/prod/private/broadband/udp-mapping/v=1.7.005/",
        datasources = List(ComlinkdataDatasource.reveal, ComlinkdataDatasource.mw05),
        suspiciousIpCarrierExclustionLocation = URI create "s3://some-bucket/prefix/to/carriers",
        discardLocation = URI create "s3://some-bucket/prefix/to/folderDiscard",
        outputLocation = URI create "s3://some-bucket/prefix/to/folder",
        numOutputFiles = 10,
        dateRangeOpt = Some(LocalDateRange.of(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 2, 2))),
        dsExcludeCarrierList = Some(Seq("test"))
      )
      tsInUdpMap(c, ComlinkdataDatasource.reveal).lastDate shouldBe Some(revealEndDateForBroadband)
      tsInUdpMap(c, ComlinkdataDatasource.mw07).lastDate shouldBe ComlinkdataDatasource.mw07.lastDate
    }
  }
}