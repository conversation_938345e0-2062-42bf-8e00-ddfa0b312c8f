package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.common.{generate, genIfa}
import com.comlinkdata.emrjobs.spark.marketshare.job.ChurnRunner.findArtificialChurns
import com.comlinkdata.emrjobs.spark.marketshare.job.ArtificialChurn
import com.comlinkdata.largescale.commons.LocalDateRange
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband_market_share.{IfaHomeCarrierAggregated, BroadbandChurn, IfaHomeCarrier}
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.functions.{to_date, concat_ws}

import java.sql.Date
import java.time.LocalDate
import java.util.Base64

class ChurnSpec extends CldSparkBaseSpec {
  import spark.implicits._

  describe("churn test") {
    it("computes churn correctly") {
      // carriers:
      // mw_carrier = "abc", sp_id=1, min_date=2021-12-21   (holidays are from  2021-12-22)
      // mw_carrier = "abc", sp_id=2, min_date=2022-01-08   (holidays are until 2022-01-07)

      val ifaHomeCarrierAggregated = dataset[IfaHomeCarrierAggregated](
        """
          |ifa |sp_id|date      |
          |YQ==|1    |2021-12-21|
          |YQ==|2    |2022-01-08|"""
      )
      val artificialChurnTable = spark.emptyDataset[ArtificialChurn]

      val ldr = LocalDateRange.of(LocalDate.parse("2022-01-08"))
      val bbChurnAlgo = Churn()
      val result = bbChurnAlgo.computeChurn(
        ldr,
        ifaHomeCarrierAggregated,
        artificialChurnTable,
        minimalPeriodNights = 0,
        minimalCarrierTenancy = 0,
        maximumDaysBetweenCarriers = 18
      ).toDF()

      val expected = dataframe[BroadbandChurn](
        """
          |ifa |loser_sp_id|loser_first_date|loser_last_date|winner_sp_id|winner_first_date|winner_last_date|
          |YQ==|          1|      2021-12-21|     2021-12-21|           2|       2022-01-08|      2022-01-08|""")

      assertDataFrameDataEquals(expected, result)
    }

    it("complete loop from ifa-home-carrier") {
      val ifa = generate(genIfa).map(Base64.getEncoder.encodeToString).head
      val ifaHC = dataset[IfaHomeCarrier] {
        s"""
           |ifa |carrier|year|month|day|
           |$ifa|A      |2023|02   |01 |
           |$ifa|B      |2023|02   |14 |"""
      }
      val carrierLookup = dataset[CarrierLookup] {
        """
          |mw_carrier|consolidated_carrier|consolidated_id|service_territory_name|sp|sp_platform|min_date  |
          |A         |Ac                  |1              |As                    |11|11         |2016-01-01|
          |B         |Bc                  |2              |Bs                    |22|22         |2016-01-01|"""
      }
      val artificialChurnLookup = spark.emptyDataset[ArtificialChurn]

      val ldr = LocalDateRange(LocalDate.parse("2023-02-01"), LocalDate.parse("2023-02-14"))
      val bbChurnAlgo = Churn()

      val result = bbChurnAlgo.runLoop(
        ldr,
        ldr => ifaHC.filter(to_date(concat_ws("-", $"year", $"month", $"day")).between(Date.valueOf(ldr.startDate), Date.valueOf(ldr.endDate))),
        minimalPeriodNights = 1,
        minimalCarrierTenancy = 0,
        maximumDaysBetweenCarriers = 13,
        artificialChurnLookup,
        carrierLookup
      )

      val expected = dataset[BroadbandChurn] {
        s"""
           |ifa |loser_sp_id|loser_first_date|loser_last_date|winner_sp_id|winner_first_date|winner_last_date|
           |$ifa|11         |2023-02-01      |2023-02-01     |22          |2023-02-14       |2023-02-14      |"""
      }

      assertDataFrameDataEquals(expected.toDF(), result.toDF())
    }
  }

  describe("spikes test") {
    it("does not create spike on carrier change") {
      // carriers:
      // mw_carrier = "abc", sp_id=1, min_date=2021-12-21   (holidays are from  2021-12-22)
      // mw_carrier = "abc", sp_id=2, min_date=2022-01-08   (holidays are until 2022-01-07)

      val ifaHomeCarrierAggregated = dataset[IfaHomeCarrierAggregated](
        """
          |ifa |sp_id|date      |
          |YQ==|1    |2021-12-21|
          |YQ==|2    |2022-01-08|"""
      )
      val artificialChurnTable = dataset[ArtificialChurn](
        """
          |starting_id|ending_id|change_date|
          |          1|        2| 2022-01-08|""")

      val ldr = LocalDateRange.of(LocalDate.parse("2022-01-08"))
      val bbChurnAlgo = Churn()
      val result = bbChurnAlgo.computeChurn(
        ldr,
        ifaHomeCarrierAggregated,
        artificialChurnTable,
        minimalPeriodNights = 0,
        minimalCarrierTenancy = 0,
        maximumDaysBetweenCarriers = 18
      )

      assertTrue(result.collect().isEmpty)
    }

    it("does not create spike on carrier introduction") {
      // carriers:
      // mw_carrier = "abc", sp_id=1, min_date=2021-12-21
      // mw_carrier = "abc", sp_id=2, min_date=2021-12-21
      //
      // reprocess date: 2022-01-08

      val ifaHomeCarrierAggregated = dataset[IfaHomeCarrierAggregated](
        """
          |ifa |sp_id|date      |
          |YQ==|1    |2021-12-21|
          |YQ==|2    |2021-12-23|
          |YQ==|2    |2022-01-08|"""
      )
      val artificialChurnTable = spark.emptyDataset[ArtificialChurn]

      val ldr = LocalDateRange.of(LocalDate.parse("2022-01-08"))
      val bbChurnAlgo = Churn()
      val result = bbChurnAlgo.computeChurn(
        ldr,
        ifaHomeCarrierAggregated,
        artificialChurnTable,
        minimalPeriodNights = 0,
        minimalCarrierTenancy = 0,
        maximumDaysBetweenCarriers = 18
      )
      assertTrue(result.collect().isEmpty)
    }
  }

  describe("filter holiday"){
    val endDate = LocalDate.of(2022, 1, 1)
    val startDate = LocalDate.of(2021, 11, 1)

    it("thanksgivings"){
      val bbChurnAlgo = Churn()
      val df = Seq(
        (LocalDate.of(2021, 11, 10), LocalDate.of(2021, 11, 12)), // before thanksgiving
        (LocalDate.of(2021, 11, 13), LocalDate.of(2021, 11, 20)), // around the start of thanksgiving
        (LocalDate.of(2021, 11, 20), LocalDate.of(2021, 11, 21)), // fully contained within thanksgiving
        (LocalDate.of(2021, 11, 25), LocalDate.of(2021, 12, 10)), // around the end of thanksgiving
        (LocalDate.of(2021, 12, 11), LocalDate.of(2021, 12, 12))) // after thanksgiving
        .toDF("first_date", "last_date")
        .transform(bbChurnAlgo.filterHolidays(startDate, endDate))
      val results = df.collect()
      results.length shouldBe 4
    }

    it("new years"){
      val bbChurnAlgo = Churn()
      val df = Seq(
        (LocalDate.of(2021, 12, 10), LocalDate.of(2021, 12, 12)), // before new years
        (LocalDate.of(2021, 12, 13), LocalDate.of(2021, 12, 23)), // around the start of new years
        (LocalDate.of(2021, 12, 22), LocalDate.of(2022,  1,  7)), // fully contained within new years
        (LocalDate.of(2022,  1,  1), LocalDate.of(2022,  1, 10)), // around the end of new years
        (LocalDate.of(2022,  1, 11), LocalDate.of(2022,  1, 12))) // after new years
        .toDF("first_date", "last_date")
        .transform(bbChurnAlgo.filterHolidays(startDate, endDate))
      val results = df.collect()
      results.length shouldBe 4
    }
  }

  describe("Artificial Churn"){
    it("Zero switch"){
      val dsCarrierLookup: Dataset[CarrierLookup] = dataset[CarrierLookup] {
        s"""
           |mw_carrier            |consolidated_carrier  |consolidated_id|service_territory_name  |sp  |sp_platform|min_date  |
           |new_Carrier           |CenturyLink           |10002          |CenturyLink, Inc.       |858 |858        |2016-11-01|
         """
      }

      val result = dsCarrierLookup.transform(findArtificialChurns)
      val expected: Dataset[ArtificialChurn] = spark.emptyDataset[ArtificialChurn]

      assertDatasetEquals(expected, result)
    }

    it("One switch"){
      val dsCarrierLookup: Dataset[CarrierLookup] = dataset[CarrierLookup] {
        s"""
           |mw_carrier            |consolidated_carrier  |consolidated_id|service_territory_name  |sp  |sp_platform|min_date  |
           |Winters Broadband LLC |Winters Broadband LL  |10000          |x                       |6071|6574       |2016-11-01|
           |Winters Broadband LLC |Cal.net               |10001          |x                       |6072|6070       |2021-04-06|
           |new_Carrier           |CenturyLink           |10002          |CenturyLink, Inc.       |858 |858        |2016-11-01|
         """
      }

      val result = dsCarrierLookup.transform(findArtificialChurns)
      val expected: Dataset[ArtificialChurn] = dataset[ArtificialChurn] {
        s"""
           |starting_id|ending_id|change_date|
           |       6071|     6072| 2021-04-06|
         """
      }
      assertDatasetEquals(expected, result)

    }

    it("Multi switch"){
      val dsCarrierLookup: Dataset[CarrierLookup] = dataset[CarrierLookup] {
        s"""
           |mw_carrier            |consolidated_carrier  |consolidated_id|service_territory_name  |sp  |sp_platform|min_date  |
           |Winters Broadband LLC |Winters Broadband LL  |10000          |x                       |6071|6574       |2016-11-01|
           |Winters Broadband LLC |Cal.net               |10001          |x                       |6072|6070       |2021-04-06|
           |Winters Broadband LLC |I am new              |10003          |x                       |6073|6071       |2022-05-07|
           |new_Carrier           |CenturyLink           |10002          |CenturyLink, Inc.       |858 |858        |2016-11-01|
         """
      }

      val result = dsCarrierLookup.transform(findArtificialChurns).orderBy($"starting_id")
      val expected: Dataset[ArtificialChurn] = dataset[ArtificialChurn] {
        s"""
           |starting_id|ending_id|change_date|
           |       6071|     6072| 2021-04-06|
           |       6072|     6073| 2022-05-07|
         """
      }

      assertDatasetEquals(expected, result)
    }
  }
}
