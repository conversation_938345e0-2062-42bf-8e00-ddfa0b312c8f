package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.Linkage.Fields
import com.comlinkdata.commons.testing.generators.broadband._
import com.comlinkdata.commons.testing.generators.common.{censusBlocks, generate}
import com.comlinkdata.commons.testing.generators.wireless_market_share.genDCommonOcnLookup
import com.comlinkdata.emrjobs.spark.marketshare.job.PlatformAggregateJobRunner.getStartDate
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.schema.broadband.lookup.{CarrierAggRollup, CarrierSuppression}
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandAggregatedChurn, BroadbandPlatformAggregate, ElementIDToSuppress}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.{CensusBlock2010To2020Crosswalk, CensusBlockCrosswalk, FWAMultiplier, GuardRail, LegacyStateDimId}
import com.comlinkdata.largescale.schema.udp.installbase.IfaWirelessCarrierYMD
import com.comlinkdata.largescale.schema.udp.lookup.AllowedNetworkBrandPair
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.DCommonOcnLookup
import org.junit.rules.TemporaryFolder

import java.sql.Date
import java.time.LocalDate

class PlatformAggregateJobSpec extends CldSparkBaseSpec {

  import spark.implicits._

  describe("full run") {
    val startDate = LocalDate.of(2022, 4, 1)
    val endDate = LocalDate.of(2022, 7, 1)

    val churnDate = LocalDate.of(2022, 6, 12)

    val aggChurnData: Seq[BroadbandAggregatedChurn] = (startDate to endDate)
      .flatMap(day => generate(genBroadbandAggregatedChurn(day.toDate))).toSeq

    val censusBlockCrosswalkData: Seq[CensusBlockCrosswalk] = generate(genCensusBlockCrosswalk())

    val censusBlockNoSpData = generate(genCensusBlockNoSpLookup())

    val aggCarrierRollupData: Seq[CarrierAggRollup] = generate(genCarrierAggRollup())

    val stateDimIdData: Seq[LegacyStateDimId] = generate(genLegacyStateDimId())

    val ifaWirelessCarrierData: Seq[IfaWirelessCarrierYMD] = Fields("ifa" -> "ifa")
      .link(aggChurnData, generate(genIfaWirelessCarrier()))

    val allowedNetworkBrandPairData: Seq[AllowedNetworkBrandPair] = Fields("brand" -> "brand", "network" -> "network")
      .link(ifaWirelessCarrierData, generate(genAllowedNetworkBrandPair()))

    val dCommonOcnLookupData: Seq[DCommonOcnLookup] = Fields("common_name" -> "common_name")
      .link(allowedNetworkBrandPairData, generate(genDCommonOcnLookup()))

    val ifaToSuppressData: Seq[ElementIDToSuppress] = generate(genElementIDToSuppress())
    val carrierSuppression: Seq[CarrierSuppression] = generate(genCarrierSuppression())
    val fwaMultipliers = Seq(FWAMultiplier(0, Date.valueOf(startDate), None, 0, 0))

    val dsGuardRail = Seq(
      GuardRail(6713, "w", 0.12f, 0.18f, Date.valueOf("2023-05-01"), Some(Date.valueOf("9999-01-01"))),
      GuardRail(6713, "l", 0.03f, 0.08f, Date.valueOf("2023-05-01"), Some(Date.valueOf("9999-01-01"))),
      GuardRail(6730, "w", 0.04f, 0.08f, Date.valueOf("2023-05-01"), Some(Date.valueOf("9999-01-01"))),
      GuardRail(6730, "l", 0.005f, 0.02f, Date.valueOf("2023-05-01"), Some(Date.valueOf("9999-01-01"))),
    )

    val csb2020Crosswalk: Seq[CensusBlock2010To2020Crosswalk] = censusBlocks.map(geoid10 => CensusBlock2010To2020Crosswalk(geoid10, geoid10, 1.0, 1.0))

    it("with parquet") {
      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI

      val config: PlatformAggregateJobConfig = PlatformAggregateJobConfig(
        startDate = Some(endDate),
        endDate = Some(endDate),
        aggregatedChurnLocation = Some(basePath.resolve("plat_agg_aggchurnlocation")),
        aggregatedChurn25Location = None,
        censusBlockNoSpLookupLocation = basePath.resolve("plat_agg_censusBlockNoSpLookupLocation"),
        censusBlockCrosswalkLocation = basePath.resolve("plat_agg_censusBlockCrosswalkLocation"),
        censusBlockCrosswalk2020Location = basePath.resolve("plat_agg_censusBlockCrosswalkLocation2020"),
        censusBlock2010To2020Location = basePath.resolve("plat_agg_censusBlock2020CrosswalkLocation"),
        stateDimIdLookupLocation = basePath.resolve("plat_agg_stateDimIdLookupLocation"),
        aggCarrierRollupsLocation = basePath.resolve("plat_agg_aggCarrierRollupsLocation"),
        allowedNetworkBrandPairLocation = basePath.resolve("plat_agg_allowedNetworkBrandPairLocation"),
        ifaWirelessCarrierLocation = basePath.resolve("plat_agg_ifaWirelessCarrierLocation"),
        dCommonOcnLocation = basePath.resolve("plat_agg_dCommonOcnLocation"),
        overlappingDays = 5,
        platformPreAggregateRawLocation = basePath.resolve("plat_agg_platformPreAggregateRawLocation"),
        platformPreAggregateLocation = basePath.resolve("plat_agg_platformPreAggregateLocation"),
        platformAggregateLocation = basePath.resolve("plat_agg_platformAggregateLocation"),
        guardRailOutputLocation = basePath.resolve("plat_agg_guardRailOutputLocation"),
        outputPartitions = 1,
        ifasToSuppressLocation = Some(basePath.resolve("plat_agg_ifatosuppress")),
        carrierSuppressLocation = Some(basePath.resolve("plat_agg_carriersuppress")),
        fwaMultipliers = Some(basePath.resolve("plat_agg_fwa_multipliers")),
        guardRailLocation = Some(basePath.resolve("plat_agg_guard_rail")),
        guardRailLookback = 3
      )

      aggChurnData.toDS()
        .write
        .partitionBy("year", "month", "day")
        .parquet(config.aggregatedChurnLocation.get.toString)

      // simple parquet lookups:
      Seq(
        (censusBlockCrosswalkData.toDS(), config.censusBlockCrosswalkLocation.toString),
        (censusBlockNoSpData.toDS(), config.censusBlockNoSpLookupLocation.toString),
        (csb2020Crosswalk.toDS(), config.censusBlock2010To2020Location.toString),
        (ifaToSuppressData.toDS(), config.ifasToSuppressLocation.get.toString),
        (carrierSuppression.toDS(), config.carrierSuppressLocation.get.toString),
        (fwaMultipliers.toDS(), config.fwaMultipliers.get.toString),
        (dsGuardRail.toDS(), config.guardRailLocation.get.toString),
        (allowedNetworkBrandPairData.toDS(), config.allowedNetworkBrandPairLocation.toString)
      ).foreach{ case (ds, loc) => ds.write.parquet(loc)}

      // csv lookups
      Seq(
        (stateDimIdData.toDS(), config.stateDimIdLookupLocation.toString),
        (dCommonOcnLookupData.toDS(), config.dCommonOcnLocation.toString)
      ).foreach{ case (ds, loc) => ds.write.option("header", "true").csv(loc)}

      // data that requires transforms before writing out
      censusBlockCrosswalkData.toDS()
        .withColumn("census_blockid", $"serv_terr_blockid")
        .drop("serv_terr_blockid", "popstats_blockid", "acs_2017_blockid")
        .write.parquet(config.censusBlockCrosswalk2020Location.toString)

      aggCarrierRollupData.toDS()
        .withColumnRenamed("limit_to_census_blockid", "limit_to_census_blockids")
        .write.parquet(config.aggCarrierRollupsLocation.toString)

      ifaWirelessCarrierData.toDS()
        .write
        .partitionBy("year", "month", "day")
        .parquet(config.ifaWirelessCarrierLocation.toString)

      PlatformAggregateJobRunner.runJob(config)

      BroadbandPlatformAggregate.read(config.platformAggregateLocation, config.startDate.get).count()
      BroadbandPlatformAggregate.read(config.platformPreAggregateLocation, config.startDate.get).count()
    }
  }

  describe("get start date") {

    val tslEarliestDate = LocalDate.of(2018, 3, 31)
    val overlappingDays = 7
    it("with empty input date") {
      getStartDate(None, overlappingDays, tslEarliestDate) shouldBe tslEarliestDate
    }

    it("with input date much greater than tslEarliestDate") {
      val inputDate = tslEarliestDate.plusDays(100)
      getStartDate(Some(inputDate), overlappingDays, tslEarliestDate) shouldBe inputDate.minusDays(overlappingDays)
    }

    it("with input date before tslEarliestDate") {
      val inputDate = tslEarliestDate.minusDays(100)
      getStartDate(Some(inputDate), overlappingDays, tslEarliestDate) shouldBe tslEarliestDate
    }

    it("with input date after tslEarliestDate, but overlapping days push it before earliest date") {
      val inputDate = tslEarliestDate.minusDays(3)
      getStartDate(Some(inputDate), overlappingDays, tslEarliestDate) shouldBe tslEarliestDate
    }
  }
}