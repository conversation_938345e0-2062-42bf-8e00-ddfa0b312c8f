package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.commons.testing.DataHelperFunctions.data
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.FixedWirelessSpid
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup

object Lookups {

  val spidLookup: Seq[FixedWirelessSpid] = data[FixedWirelessSpid] {
    """|tutela_carrier|              common_name|sp_id|
       |           VZN| Verizon 5G Home Internet| 6715|
       |           TMO|   T-Mobile Home Internet| 6713|
       |          USCC|US Cellular Home Internet| 6714|
       |       Verizon| Verizon 5G Home Internet| 6715|
       |      T-Mobile|   T-Mobile Home Internet| 6713|
       |   US Cellular|US Cellular Home Internet| 6714|"""
  }

  val carrierLookup: Seq[CarrierLookup] = data[CarrierLookup] {
    """
      |mw_carrier|consolidated_carrier|consolidated_id|service_territory_name|sp|sp_platform|min_date  |
      |C         |C                   |1              |                      |1 |Hop        |1985-06-02|"""
  }
}
