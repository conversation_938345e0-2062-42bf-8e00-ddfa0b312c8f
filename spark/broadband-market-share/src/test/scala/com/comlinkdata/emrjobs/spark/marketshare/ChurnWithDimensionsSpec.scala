package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.broadband.genCensusBlockNoSpLookup
import com.comlinkdata.commons.testing.generators.common.generate
import com.comlinkdata.commons.testing.generators.udp.genIPHighConfidence
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.schema.broadband.lookup.PopStatsCensusBlockCentroidsTaggedNpa
import com.comlinkdata.largescale.schema.broadband_market_share.BroadbandDailyHome
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.comlinkdata.largescale.schema.udp.installbase.{IpToLocation, IPHighConfidence}
import com.comlinkdata.largescale.schema.udp.location.Point
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.types.FloatType

import java.sql.Date
import java.time.LocalDate

private case class cwdChurn(
  ifa: Ifa,
  ip: Ip,
  w_or_l: String,
  first_date: Date,
  last_date: Date,
  window_tail_date: Date
)

class ChurnWithDimensionsSpec extends CldSparkBaseSpec {

  import spark.implicits._

  private val ifas: Array[Ifa] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]
  private val ips: Array[Ip] = (32 to 62).map( x => Array(192.toByte, 168.toByte, 0.toByte, x.toByte)).toArray[Array[Byte]]
  private val hhids: Array[Array[Byte]] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]

  describe("household ids"){
    val hc: Seq[IPHighConfidence] = Seq(
      (ifas(0), ips(0), hhids(0), LocalDate.of(2022, 1, 29)),
      (ifas(0), ips(0), hhids(1), LocalDate.of(2022, 2, 5)),
      (ifas(0), ips(0), hhids(2), LocalDate.of(2022, 2, 12)),
      (ifas(0), ips(0), hhids(3), LocalDate.of(2022, 2, 19)),
      (ifas(0), ips(0), hhids(4), LocalDate.of(2022, 2, 26)),
      (ifas(0), ips(1), hhids(5), LocalDate.of(2022, 3, 5)),
      (ifas(0), ips(1), hhids(6), LocalDate.of(2022, 3, 12)),
      (ifas(0), ips(1), hhids(7), LocalDate.of(2022, 3, 19)),
    ).flatMap( i => generate(genIPHighConfidence()).map(_.copy(
      ifa = i._1,
      ip = i._2,
      household_id = i._3,
      obs_count = 1,
      night_obs_count = 1,
      carrier = "fake_carrier",
      ip_min_date = i._4.toDate,
      ip_max_date = i._4.toDate,
      case_method = 0,
      year = i._4.getYearString,
      month = i._4.getMonthValueString,
      day = i._4.getDayOfMonthString)))

    val cwdChurn = Seq(
      (ifas(0), ips(0), "loser",  Date.valueOf("2022-02-05"), Date.valueOf("2022-02-28"), Date.valueOf("2022-03-01")),
      (ifas(0), ips(1), "winner", Date.valueOf("2022-03-01"), Date.valueOf("2022-03-15"), Date.valueOf("2022-03-01")),
    )

    it("winner should be as consistent as possible") {

      val dsHighConfidence = hc.toDS()
      val dsChurn = cwdChurn.toDF("ifa", "ip", "w_or_l", "first_date", "last_date", "churn_date")

      val chWdim = ChurnWithDimensions()

      val expected_winner_hhid = hhids(5)
      val actual_winner_hhid = dsChurn
        .transform(chWdim.withHouseholdId(dsHighConfidence))
        .filter($"w_or_l" === "winner")
        .select("household_id")
        .collect()(0)
        .getAs[Array[Byte]](0)
      expected_winner_hhid shouldBe actual_winner_hhid
    }

    it("loser should be as consistent as possible"){
      val dsHighConfidence = hc.toDS()
      val dsChurn = cwdChurn.toDF("ifa", "ip", "w_or_l", "first_date", "last_date", "churn_date")

      val chWdim = ChurnWithDimensions()

      val expected_loser_hhid = hhids(1)
      val actual_loser_hhid = dsChurn
        .transform(chWdim.withHouseholdId(dsHighConfidence))
        .filter($"w_or_l" === "loser")
        .select("household_id")
        .collect()(0)
        .getAs[Array[Byte]](0)
      expected_loser_hhid shouldBe actual_loser_hhid
    }
  }

  describe("Attach Locations Fixed wireless"){
    val churnData = Seq(
      cwdChurn(ifas(0), ips(0), "winner", Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20")),
      cwdChurn(ifas(1), ips(1), "winner", Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20")),
      cwdChurn(ifas(2), ips(2), "winner", Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20")),
      cwdChurn(ifas(3), ips(3), "winner", Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20"))
    )

    val ifaLocsData = Seq(2, 3).map(i =>
      BroadbandDailyHome(
        ifa = ifas(i),
        location_rank = 1,
        geoid = i.toString,
        distinct_hours = 1,
        date = Date.valueOf("2022-10-20")
      )
    )

    val cbNoSpLookupData = Seq(0 , 1, 2, 3).flatMap(i => generate(genCensusBlockNoSpLookup()).map(_.copy(
      block_group = i.toString,
      census_blockid = i.toString
    )))

    val centroids = Seq(0 , 1, 2, 3).map(i => PopStatsCensusBlockCentroidsTaggedNpa(
      id = i.toString,
      data = i.toString,
      maptitude_lon = i.toString,
      maptitude_lat = i.toString,
      census_blockid = i.toString,
      latitude = i,
      longitude = i,
      zip = null,
      npa = null
    ))

    describe("TMO Churn") {
      it("with TMO Ifa Location") {
        val chWdim = ChurnWithDimensions()
        val data = churnData
          .toDF()
          .withColumn("sp_platform", lit(6713))
          .transform(chWdim.withIfaLocation(ifaLocsData.toDS(), cbNoSpLookupData.toDS(), centroids.toDS()))
          .transform(chWdim.pickBetweenIfaAndIpLocs)
          .filter($"lat".isNotNull)
          .select($"ifa", $"lat", $"lng")
          .orderBy($"ifa")

        val expected = Seq(2, 3)
          .map(i => (ifas(i), i, i))
          .toDF("ifa", "lat", "lng")
          .withColumn("lat", $"lat".cast(FloatType))
          .withColumn("lng", $"lng".cast(FloatType))

        assertDataFrameDataEquals(expected, data)
      }

      it("with VZW Ifa Location") {
        val chWdim = ChurnWithDimensions()
        val data = churnData
          .toDF()
          .withColumn("sp_platform", lit(6715))
          .transform(chWdim.withIfaLocation(spark.emptyDataset[BroadbandDailyHome], cbNoSpLookupData.toDS(), centroids.toDS()))
          .transform(chWdim.pickBetweenIfaAndIpLocs)
          .filter($"lat".isNotNull)

        data.count() shouldBe 0
      }
    }

    describe("VZW Churn") {
      it("with VZW Ifa Location") {
        val chWdim = ChurnWithDimensions()
        val data = churnData
          .toDF()
          .withColumn("sp_platform", lit(6715))
          .transform(chWdim.withIfaLocation(ifaLocsData.toDS(), cbNoSpLookupData.toDS(), centroids.toDS()))
          .transform(chWdim.pickBetweenIfaAndIpLocs)
          .filter($"lat".isNotNull)
          .select($"ifa", $"lat", $"lng")
          .orderBy($"ifa")

        val expected = Seq(2, 3)
          .map(i => (ifas(i), i, i))
          .toDF("ifa", "lat", "lng")
          .withColumn("lat", $"lat".cast(FloatType))
          .withColumn("lng", $"lng".cast(FloatType))

        assertDataFrameDataEquals(expected, data)
      }

      it("with TMO Ifa Location") {
        val chWdim = ChurnWithDimensions()
        val data = churnData
          .toDF()
          .withColumn("sp_platform", lit(6713))
          .transform(chWdim.withIfaLocation(spark.emptyDataset[BroadbandDailyHome], cbNoSpLookupData.toDS(), centroids.toDS()))
          .transform(chWdim.pickBetweenIfaAndIpLocs)
          .filter($"lat".isNotNull)

        data.count() shouldBe 0
      }
    }
  }

  describe("Attach Locations"){

    val churnData = Seq(
      cwdChurn(ifas(0), ips(0), "winner", Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20")),
      cwdChurn(ifas(1), ips(1), "winner", Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20")),
      cwdChurn(ifas(2), ips(2), "winner", Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20")),
      cwdChurn(ifas(3), ips(3), "winner", Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20"))
    )

    val ifaLocsData = Seq(2, 3).map(i =>
      BroadbandDailyHome(
        ifa = ifas(i),
        location_rank = 1,
        geoid = i.toString,
        distinct_hours = 1,
        date = Date.valueOf("2022-10-20")
      )
    )

    val cbNoSpLookupData = Seq(0, 1, 2, 3).flatMap(i => generate(genCensusBlockNoSpLookup()).map(_.copy(
      block_group = i.toString,
      census_blockid = i.toString
    )))

    val centroids = Seq(0, 1, 2, 3).map(i => PopStatsCensusBlockCentroidsTaggedNpa(
      id = i.toString,
      data = i.toString,
      maptitude_lon = i.toString,
      maptitude_lat = i.toString,
      census_blockid = i.toString,
      latitude = i,
      longitude = i,
      zip = null,
      npa = null
    ))

    val ipLocsData = Seq(1, 3).map( i =>
      IpToLocation(
        ip = ips(i),
        obs_count = 1,
        night_obs_count = 1,
        ifas = Seq(ifas(i)),
        ifa_count = 1,
        hours_seen = Seq(1),
        point = Point(i+10, i+10),
        point_obs = 100, //have this override ifaLocsData
        year = "2022",
        month = "10",
        day = "20"
      )
    )

    val hcData = churnData.flatMap( c =>
      generate(genIPHighConfidence()).map( hc =>
        hc.copy(ifa = c.ifa, ip = c.ip, carrier = "test_carrier")
    ))

    it("Only Ip Location Dataset"){
      val chWdim = ChurnWithDimensions()
      val data = churnData
        .toDF()
        .withColumn("carrier", lit("test_carrier"))
        .withColumn("sp_platform", lit(1000))
        .transform(chWdim.withIpLocation(hcData.toDS(), ipLocsData.toDS()))
        .transform(chWdim.pickBetweenIfaAndIpLocs)
        .filter($"lat".isNotNull)
        .select($"ifa", $"lat", $"lng")
        .orderBy($"ifa")

      val expected = Seq(1, 3)
        .map(i => (ifas(i), i+10, i+10))
        .toDF("ifa", "lat", "lng")
        .withColumn("lat", $"lat".cast(FloatType))
        .withColumn("lng", $"lng".cast(FloatType))

      assertDataFrameDataEquals(expected, data)
    }

    it("Broadband only Ifa and Ip Location Datasets"){
      val chWdim = ChurnWithDimensions()
      val data = Seq(
        (ifas(0), ips(0), "winner", 1000, Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20")), // no hookup
        (ifas(1), ips(1), "winner", 1000, Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20")), // should be ip
        (ifas(2), ips(2), "winner", 6715, Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20")), // should be ifa
        (ifas(3), ips(3), "winner", 1000, Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20"))  // should be ip
      ).toDF("ifa", "ip", "w_or_l", "sp_platform", "first_date", "last_date", "window_tail_date")
        .withColumn("carrier", lit("test_carrier"))
        .transform(chWdim.withIfaLocation(ifaLocsData.toDS(), cbNoSpLookupData.toDS(), centroids.toDS()))
        .transform(chWdim.withIpLocation(hcData.toDS(), ipLocsData.toDS()))
        .transform(chWdim.pickBetweenIfaAndIpLocs)
        .filter($"lat".isNotNull)
        .select($"ifa", $"lat", $"lng")
        .orderBy($"ifa")

      val expected = Seq(
        (ifas(1), 11.0f, 11.0f),
        (ifas(2), 2.0f, 2.0f),
        (ifas(3), 13.0f, 13.0f))
      .toDF("ifa", "lat", "lng")
      .withColumn("lat", $"lat".cast(FloatType))
      .withColumn("lng", $"lng".cast(FloatType))

      assertDataFrameDataEquals(expected, data)
    }

    it("Fixed wireless and Ip locations should return 0"){
      val chWdim = ChurnWithDimensions()
      val data = Seq(
        (ifas(2), ips(2), "winner", 6715, Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20"))
      ).toDF("ifa", "ip", "w_or_l", "sp_platform", "first_date", "last_date", "window_tail_date")
        .withColumn("carrier", lit("test_carrier"))
        .transform(chWdim.withIpLocation(hcData.toDS(), ipLocsData.toDS()))
        .transform(chWdim.pickBetweenIfaAndIpLocs)
        .filter($"lat".isNotNull)

      data.count() shouldBe 0
    }
  }

  describe("Attach Locations with multiple IP"){

    val hc: Seq[IPHighConfidence] = Seq(
      (ifas(1), ips(1), hhids(0), LocalDate.of(2022, 10, 29)),
      (ifas(2), ips(1), hhids(1), LocalDate.of(2022, 11, 5)),
      (ifas(2), ips(2), hhids(2), LocalDate.of(2022, 11, 12)),
      (ifas(2), ips(3), hhids(3), LocalDate.of(2022, 11, 19)),
      (ifas(2), ips(4), hhids(4), LocalDate.of(2022, 11, 26)),
    ).flatMap(i => generate(genIPHighConfidence()).map(_.copy(
      ifa = i._1,
      ip = i._2,
      household_id = i._3,
      obs_count = 1,
      night_obs_count = 1,
      carrier = "fake_carrier",
      ip_min_date = i._4.toDate,
      ip_max_date = i._4.toDate,
      case_method = 0,
      year = i._4.getYearString,
      month = i._4.getMonthValueString,
      day = i._4.getDayOfMonthString)))

    val ipLocsData = Seq(1, 2, 3, 4).map(i =>
      IpToLocation(
        ip = ips(i),
        obs_count = i,
        night_obs_count = 1,
        ifas = Seq(ifas(i)),
        ifa_count = 1,
        hours_seen = Seq(1),
        point = Point(i, i),
        point_obs = i,
        year = "2022",
        month = "10",
        day = "20"
      )
    )

    it("handles no Ip dataset"){
      val dsHighConfidence = hc.toDS()
      val dsIpLocs = ipLocsData.toDS()
      // ips don't matter here, just need a data structure
      val churnData = Seq(
        cwdChurn(ifas(0), ips(0), "winner", Date.valueOf("2022-10-01"), Date.valueOf("2022-10-20"), Date.valueOf("2022-10-20")),
      )
      val chWdim = ChurnWithDimensions()
      val data = churnData.toDF()
        .withColumn("carrier", lit("fake_carrier"))
        .withColumn("sp_platform", lit(6713))
        .drop("ip")
        .transform(chWdim.withIpLocation(dsHighConfidence, dsIpLocs))
        .transform(chWdim.pickBetweenIfaAndIpLocs)
        .select($"ifa", $"lat", $"lng")
        .orderBy($"ifa")

      val expected = Seq((ifas(0), null, null))
        .toDF("ifa", "lat", "lng")
        .withColumn("lat", $"lat".cast(FloatType))
        .withColumn("lng", $"lng".cast(FloatType))

      assertDataFrameDataEquals(expected, data)
    }

    it("handles one to one ip dataset"){
      val dsHighConfidence = hc.toDS()
      val dsIpLocs = ipLocsData.toDS()
      val churnData = Seq(
        cwdChurn(ifas(1), ips(0), "winner", Date.valueOf("2022-10-01"), Date.valueOf("2022-12-25"), Date.valueOf("2022-12-25")),
      )

      val chWdim = ChurnWithDimensions()
      val data = churnData.toDF()
        .withColumn("carrier", lit("fake_carrier"))
        .withColumn("sp_platform", lit(1000))
        .drop("ip")
        .transform(chWdim.withIpLocation(dsHighConfidence, dsIpLocs))
        .transform(chWdim.pickBetweenIfaAndIpLocs)
        .select($"ifa", $"lat", $"lng")
        .orderBy($"ifa")
      
      val expected = Seq(
        (ifas(1), 1.0f, 1.0f))
        .toDF("ifa", "lat", "lng")
        .withColumn("lat", $"lat".cast(FloatType))
        .withColumn("lng", $"lng".cast(FloatType))

      assertDataFrameDataEquals(expected, data)
    }

    it("handles one many ips and picks right lat lng with discount rate"){
      val dsHighConfidence = hc.toDS()
      val dsIpLocs = ipLocsData.toDS()
      // ips don't matter here, just need a data structure
      val churnData = Seq(
        cwdChurn(ifas(2), ips(0), "winner", Date.valueOf("2022-10-01"), Date.valueOf("2022-12-25"), Date.valueOf("2022-12-25")),
      )

      val chWdim = ChurnWithDimensions()
      val data = churnData.toDF()
        .withColumn("carrier", lit("fake_carrier"))
        .withColumn("sp_platform", lit(1000))
        .drop("ip")
        .transform(chWdim.withIpLocation(dsHighConfidence, dsIpLocs))
        .transform(chWdim.pickBetweenIfaAndIpLocs)
        //.select($"ifa", $"lat", $"lng")
        .orderBy($"ifa")

      val expected = Seq(
        (ifas(2), 4.0f, 4.0f))
        .toDF("ifa", "lat", "lng")
        .withColumn("lat", $"lat".cast(FloatType))
        .withColumn("lng", $"lng".cast(FloatType))

      assertDataFrameDataEquals(expected, data)
    }
  }

}

