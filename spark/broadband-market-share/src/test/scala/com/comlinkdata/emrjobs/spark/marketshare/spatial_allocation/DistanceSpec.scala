package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation

import com.comlinkdata.commons.testing.CldBaseSpec
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.DistanceMatrix.Dest

class DistanceSpec extends CldBaseSpec {
  val otherSubmarketPenalty = 1.25
  val otherTractPenalty = 1.25

  describe("distance computation") {

    it("euclidean distance") {
      val result = Distance.euclid(0, 0, 10, 10)
      val expected = math.sqrt(100 + 100)
      assert(expected === result)
    }

    it("without penalties") {
      val distance = Distance(Distance.euclid, otherSubmarketPenalty, otherTractPenalty)

      val allocation = schema.Allocation("H1".getBytes, "13075", "130759601001063", None, "0", "S", latitude = 0, longitude = 0, 0, 0, 0, 0)
      val dest = Dest("130759601001064", "S", latitude = 10, longitude = 0, 0)

      assert(10 === distance(allocation, dest))
    }

    it("with submarket penalty") {
      val distance = Distance(Distance.euclid, otherSubmarketPenalty, otherTractPenalty)

      val allocation = schema.Allocation("H1".getBytes, "13075", "130759601001063", None, "0", "S", latitude = 0, longitude = 0, 0, 0, 0, 0)
      val dest = Dest("130759601001064", "T", latitude = 10, longitude = 0, 0)

      assert(10 * otherSubmarketPenalty === distance(allocation, dest))
    }

    it("with tract penalty") {
      val distance = Distance(Distance.euclid, otherSubmarketPenalty, otherTractPenalty)

      val allocation = schema.Allocation("H1".getBytes, "13075", "130759601001063", None, "0", "S", latitude = 0, longitude = 0, 0, 0, 0, 0)
      val dest = Dest("130759601091064", "S", latitude = 10, longitude = 0, 0)

      assert(10 * otherTractPenalty === distance(allocation, dest))
    }

    it("with submarket & tract penalty") {
      val distance = Distance(Distance.euclid, otherSubmarketPenalty, otherTractPenalty)

      val allocation = schema.Allocation("H1".getBytes, "13075", "130759601001063", None, "0", "S", latitude = 0, longitude = 0, 0, 0, 0, 0)
      val dest = Dest("130759601091064", "T", latitude = 10, longitude = 0, 0)

      assert(10 * otherSubmarketPenalty * otherTractPenalty === distance(allocation, dest))
    }
  }
}
