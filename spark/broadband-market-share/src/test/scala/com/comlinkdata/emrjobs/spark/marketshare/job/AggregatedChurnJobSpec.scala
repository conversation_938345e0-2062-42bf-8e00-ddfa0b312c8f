package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.commons.LocalDateRange.toLocalDateRangeSeq
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.LocalDateRange
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandChurnWithDimension, BroadbandAggregatedChurn}
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.comlinkdata.largescale.udp.LocationUtils.distanceBetweenCoordsUDF
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.Dataset
import org.junit.rules.TemporaryFolder

import java.sql.Date
import java.time.LocalDate

class AggregatedChurnJobSpec extends CldSparkBaseSpec with LazyLogging {
  import spark.implicits._

  private val ifas: Array[Ifa] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]
  private val ips: Array[Ip] = (32 to 62).map( x => Array(192.toByte, 168.toByte, 0.toByte, x.toByte)).toArray[Array[Byte]]
  private val hhids: Array[Array[Byte]] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]


  describe("Full run") {

    val evaluationWindowSize = 69
    val churnDate = LocalDate.of(2022, 3, 24)

    val loser = LocalDateRange(LocalDate.of(2022, 3, 24), LocalDate.of(2022, 4, 30))
      .map( day => BroadbandChurnWithDimension(
        ifa = ifas(0),
        household_id = hhids(0),
        ip = ips(0),
        first_date = Date.valueOf("2022-02-19"),
        last_date = Date.valueOf("2022-03-23"),
        churn_date = Date.valueOf("2022-03-24"),
        sp_id = 1,
        carrier = "Carrier A",
        sp_platform = 1,
        lat = 1f,
        lng = 1f,
        hours_seen = Array.empty[Int],
        census_block_id = "1",
        w_or_l = "loser",
        window_tail_date = day.toDate,
        year = day.getYearString,
        month = day.getMonthValueString,
        day = day.getDayOfMonthString
      ))

    val winner = Seq(
      BroadbandChurnWithDimension(ifas(0), hhids(2), ips(1), Date.valueOf("2022-03-24"), Date.valueOf("2022-03-24"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.1f, 3.2f, Array.empty[Int], "2", "winner", Date.valueOf("2022-03-24"), "2022", "03", "24"),
      BroadbandChurnWithDimension(ifas(0), hhids(2), ips(1), Date.valueOf("2022-03-24"), Date.valueOf("2022-03-25"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.1f, 3.2f, Array.empty[Int], "2", "winner", Date.valueOf("2022-03-25"), "2022", "03", "25"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-03-26"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-03-26"), "2022", "03", "26"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-03-27"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-03-27"), "2022", "03", "27"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-03-28"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-03-28"), "2022", "03", "28"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-03-29"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-03-29"), "2022", "03", "29"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-03-30"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-03-30"), "2022", "03", "30"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-03-31"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-03-31"), "2022", "03", "31"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-01"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-04-01"), "2022", "04", "01"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-02"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-04-02"), "2022", "04", "02"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-03"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-04-03"), "2022", "04", "03"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-04"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-04-04"), "2022", "04", "04"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-05"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-04-05"), "2022", "04", "05"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-06"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-04-06"), "2022", "04", "06"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-07"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-04-07"), "2022", "04", "07"),
      BroadbandChurnWithDimension(ifas(0), hhids(3), ips(2), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-08"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.2f, 3.5f, Array.empty[Int], "3", "winner", Date.valueOf("2022-04-08"), "2022", "04", "08"),
      BroadbandChurnWithDimension(ifas(0), hhids(4), ips(3), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-09"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.3f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-09"), "2022", "04", "09"),
      BroadbandChurnWithDimension(ifas(0), hhids(4), ips(3), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-10"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.3f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-10"), "2022", "04", "10"),
      BroadbandChurnWithDimension(ifas(0), hhids(4), ips(3), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-11"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.3f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-11"), "2022", "04", "11"),
      BroadbandChurnWithDimension(ifas(0), hhids(4), ips(3), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-12"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.3f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-12"), "2022", "04", "12"),
      BroadbandChurnWithDimension(ifas(0), hhids(4), ips(3), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-13"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.3f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-13"), "2022", "04", "13"),
      BroadbandChurnWithDimension(ifas(0), hhids(4), ips(3), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-14"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.3f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-14"), "2022", "04", "14"),
      BroadbandChurnWithDimension(ifas(0), hhids(4), ips(3), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-15"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.3f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-15"), "2022", "04", "15"),
      BroadbandChurnWithDimension(ifas(0), hhids(5), ips(4), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-16"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.9f, 4.3f, Array.empty[Int], "5", "winner", Date.valueOf("2022-04-16"), "2022", "04", "16"),
      BroadbandChurnWithDimension(ifas(0), hhids(5), ips(4), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-17"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.9f, 4.3f, Array.empty[Int], "5", "winner", Date.valueOf("2022-04-17"), "2022", "04", "17"),
      BroadbandChurnWithDimension(ifas(0), hhids(5), ips(4), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-18"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.9f, 4.3f, Array.empty[Int], "5", "winner", Date.valueOf("2022-04-18"), "2022", "04", "18"),
      BroadbandChurnWithDimension(ifas(0), hhids(5), ips(4), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-19"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.9f, 4.3f, Array.empty[Int], "5", "winner", Date.valueOf("2022-04-19"), "2022", "04", "19"),
      BroadbandChurnWithDimension(ifas(0), hhids(5), ips(4), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-20"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.9f, 4.3f, Array.empty[Int], "5", "winner", Date.valueOf("2022-04-20"), "2022", "04", "20"),
      BroadbandChurnWithDimension(ifas(0), hhids(5), ips(4), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-21"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.9f, 4.3f, Array.empty[Int], "5", "winner", Date.valueOf("2022-04-21"), "2022", "04", "21"),
      BroadbandChurnWithDimension(ifas(0), hhids(5), ips(4), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-22"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.9f, 4.3f, Array.empty[Int], "5", "winner", Date.valueOf("2022-04-22"), "2022", "04", "22"),
      BroadbandChurnWithDimension(ifas(0), hhids(6), ips(5), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-23"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.4f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-23"), "2022", "04", "23"),
      BroadbandChurnWithDimension(ifas(0), hhids(6), ips(5), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-24"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.4f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-24"), "2022", "04", "24"),
      BroadbandChurnWithDimension(ifas(0), hhids(6), ips(5), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-25"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.4f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-25"), "2022", "04", "25"),
      BroadbandChurnWithDimension(ifas(0), hhids(6), ips(5), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-26"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.4f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-26"), "2022", "04", "26"),
      BroadbandChurnWithDimension(ifas(0), hhids(6), ips(5), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-27"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.4f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-27"), "2022", "04", "27"),
      BroadbandChurnWithDimension(ifas(0), hhids(6), ips(5), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-28"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.4f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-28"), "2022", "04", "28"),
      BroadbandChurnWithDimension(ifas(0), hhids(6), ips(5), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-29"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.4f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-29"), "2022", "04", "29"),
      BroadbandChurnWithDimension(ifas(0), hhids(7), ips(6), Date.valueOf("2022-03-24"), Date.valueOf("2022-04-30"), Date.valueOf("2022-03-24"), 10023, "Carrier B", 2, 2.4f, 4.2f, Array.empty[Int], "4", "winner", Date.valueOf("2022-04-30"), "2022", "04", "30")
    )
    val filler = (churnDate to churnDate.plusDays(evaluationWindowSize))
      .map( day => BroadbandChurnWithDimension(
        ifa = ifas(10),
        household_id = hhids(10),
        ip = ips(10),
        first_date = Date.valueOf("2022-02-19"),
        last_date = Date.valueOf("2022-03-23"),
        churn_date = Date.valueOf("2022-03-24"),
        sp_id = 1,
        carrier = "Carrier A",
        sp_platform = 1,
        lat = 1f,
        lng = 1f,
        hours_seen = Array.empty[Int],
        census_block_id = "1",
        w_or_l = "loser",
        window_tail_date = day.toDate,
        year = day.getYearString,
        month = day.getMonthValueString,
        day = day.getDayOfMonthString
      ))
    val bbChurnWithDimensionData = winner ++ loser ++ filler


    it("with parquet") {

      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI

      val config: AggregatedChurnConfig = AggregatedChurnConfig(
        startDateOpt = Option(churnDate),
        endDateOpt = Option(churnDate),
        firstChurnYmdLag = 14,
        lastChurnYmdLag = 69 - 14,
        rawDataFirstDate = LocalDate.of(2016, 1, 1),
        bbChurnLocation = basePath.resolve("bb_agg_churn_2_bb_churn_location"),
        outputLocation = basePath.resolve("bb_agg_churn_2_bb_output_location"),
        repartition = Option(1)
      )

      spark.createDataFrame(bbChurnWithDimensionData).write
        .partitionBy("year", "month", "day")
        .parquet(config.bbChurnLocation.toString)

      //spark.sparkContext.setLogLevel("INFO")
      AggregatedChurnJobRunner.runJob(config)

      val out: Dataset[BroadbandAggregatedChurn] = BroadbandAggregatedChurn.read(config.outputLocation, churnDate)

      out.count() shouldBe 1

      folder.delete()
    }

    it("handles incomplete churn dates"){
      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI

      val config: AggregatedChurnConfig = AggregatedChurnConfig(
        startDateOpt = Option(churnDate),
        endDateOpt = Option(churnDate.plusDays(10)),
        firstChurnYmdLag = 14,
        lastChurnYmdLag = 69 - 14,
        rawDataFirstDate = LocalDate.of(2016, 1, 1),
        bbChurnLocation = basePath.resolve("bb_agg_churn_2_bb_churn_location"),
        outputLocation = basePath.resolve("bb_agg_churn_2_bb_output_location"),
        repartition = Option(1)
      )
      spark.createDataFrame(bbChurnWithDimensionData).write
        .partitionBy("year", "month", "day")
        .parquet(config.bbChurnLocation.toString)

      //spark.sparkContext.setLogLevel("INFO")
      AggregatedChurnJobRunner.runJob(config)

    }
  }

  describe("Location UDF"){
    //todo: this does not belong here, clean me up
    it("handle nulls"){
      val df = spark.sql(
        """
          | select
          |   cast(null as Float) as winner_lat,
          |   cast(null as Float) as winner_lng,
          |   cast(null as Float) as loser_lat,
          |   cast(null as Float) as loser_lng
          |""".stripMargin)

      df
        .withColumn("distance", distanceBetweenCoordsUDF($"winner_lat", $"winner_lng", $"loser_lat", $"loser_lng"))
        .select("distance")
        .collect()
      //should not error out
    }
  }
}
