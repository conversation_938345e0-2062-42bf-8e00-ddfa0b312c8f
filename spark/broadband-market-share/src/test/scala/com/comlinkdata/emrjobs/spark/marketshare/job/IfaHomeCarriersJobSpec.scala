package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.common.{genIfa, genIpV4, generate}
import com.comlinkdata.commons.testing.generators.udp.{genIfaSet, genUdpIfaIpAgg}
import com.comlinkdata.emrjobs.spark.marketshare.IfaHomeCarriers
import com.comlinkdata.emrjobs.spark.marketshare.job.IfaHomeCarriersJobSpec.IfaHomeCarrierBB17
import com.comlinkdata.largescale.commons.{LocalDateRange, UriDFTableRW}
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband_market_share.{IfaHomeCarrier, IfaHomeCarrierNoDate}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp.twoOctets
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.tier2.UdpIfaIpAgg
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.comlinkdata.largescale.udp.ComlinkdataDatasource.mw05
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.lit
import org.scalacheck.Gen

import java.net.URI
import java.sql.Date
import java.time.LocalDate

class IfaHomeCarriersJobSpec extends CldSparkBaseSpec with LazyLogging {

  import spark.implicits._

  private val verizonFwIpTable = URI.create("table://ifaHomeCarriers/verizonFWIP")
  private val ifaHomeCarrierTable = URI.create("table://ifaHomeCarriers/AggLocation")
  private val ifaIpAggTable = URI.create("table://ifaHomeCarriers/ifaIpAggLocation")
  private val ifaSetTable = URI.create("table://ifaHomeCarriers/ifaSetLocation")
  private val carrierLookupTable = URI.create("table://ifaHomeCarriers/carrierLookupLocation")
  private val carrierLookupFwTable = URI.create("table://ifaHomeCarriers/carrierLookupFwLocation")
  private val outputTable = URI.create("table://ifaHomeCarriers/outputLocation")

  describe("Full run") {
    lazy val ifas = generate(30, genIfa)
    lazy val ips = generate(30, genIpV4)
    lazy val startDate = LocalDate.of(2021, 10, 1)
    lazy val endDate = LocalDate.of(2021, 10, 2)
    lazy val daysToRun = LocalDateRange(startDate, endDate)

    lazy val ifaHomeCarrierData = Seq(
      IfaHomeCarrierBB17(ifas(1), "Some Carrier",Date.valueOf(startDate)),
      IfaHomeCarrierBB17(ifas(2), "Some Carrier",Date.valueOf(startDate)),
      IfaHomeCarrierBB17(ifas(3), "Some Carrier",Date.valueOf(startDate)),
      IfaHomeCarrierBB17(ifas(4), "Some Carrier",Date.valueOf(endDate)),
    )

    lazy val verizonFwIpData: Seq[VerizonFwIp] = Seq(
      startDate.plusMonths(-2),
      startDate.plusMonths(-1),
      startDate)
    .map(_.toDate)
    .map(VerizonFwIp(
      ip_2_octets = twoOctets(ips(2)),
      ssid_bssid_count = 1L,
      _
    ))

    lazy val ifaIpAgg: Seq[UdpIfaIpAgg] = generate(1, genUdpIfaIpAgg()).map(_.copy(
      ifa = ifas(4),
      carrier = "Verizon FW",
      ip = ips(2)
    )) ++
      generate(1, genUdpIfaIpAgg()).map(_.copy(
        ifa = ifas(5),
        carrier = "T-Mobile USA",
        ip = ips(2)
      ))

    lazy val ifaSetLocationData = Seq("mw05", "mw03", "mw07").flatMap { ds =>
      generate(genIfaSet(
        ifas = Gen.oneOf(Seq(ifas(5))),
        dates = Gen.const(Date.valueOf(endDate)),
      )).map(_.copy(ds = ds))
    }

    lazy val carrierLookupData = Seq(
      CarrierLookup("Carrier A", "A", "1", null, "1", "1", "2016-11-01")
    )

    it("with parquet") {
      val config = IfaHomeCarriersJobConfig(
        startDate = Some(startDate),
        endDate = Some(endDate),
        verizonFwIpLocation = verizonFwIpTable,
        ifaHomeCarrierLocation = ifaHomeCarrierTable,
        ifaIpAggLocation = ifaIpAggTable,
        ifaSetLocation = ifaSetTable,
        datasources = Seq(mw05),
        ifaSetDatasource = ComlinkdataDatasource.mw07,
        outputLocation = outputTable,
        outputPartitions = None
      )

      UriDFTableRW(config.ifaHomeCarrierLocation).writeDsPartitionBy(ifaHomeCarrierData.toDS(), Seq("date"))
      UriDFTableRW(config.verizonFwIpLocation).writeDs(verizonFwIpData.toDS())

      val ifaIpAggData = daysToRun.map(day =>
        ifaIpAgg.toDS()
          .withColumn("year", lit(day.getYearString))
          .withColumn("month", lit(day.getMonthValueString))
          .withColumn("day", lit(day.getDayOfMonthString))
          .withColumn("ds", lit("mw05")))
        .reduce(_ unionByName _)

      val ifaIpAggCount = ifaIpAggData.count()

      UriDFTableRW(config.ifaIpAggLocation)
        .writeDsPartitionBy(ifaIpAggData.as[UdpIfaIpAgg], Seq("ds", "year", "month", "day"))

      UriDFTableRW(config.ifaSetLocation)
        .writeDsPartitionBy(ifaSetLocationData.toDS(), Seq("ds", "year", "month", "day"))

      //spark.sparkContext.setLogLevel("INFO")
      IfaHomeCarriersJobRunner.runJob(config)
      val out = IfaHomeCarrier.read(config.outputLocation, LocalDateRange(startDate, endDate))

      out.count() shouldBe 3 + ifaIpAggCount //daysToRun.length = verizon counts
      out.select($"year", $"month", $"day").distinct.count() shouldBe daysToRun.length
    }

    ignore("without parquet") {
      val process = IfaHomeCarriers()
      val dhc = process.runDay(
        ifaHomeCarrierData.toDS().as[IfaHomeCarrierNoDate],
        ifaIpAgg.toDS(),
        verizonFwIpData.toDS(),
        ifaSetLocationData.toDS().select("ifa").distinct().as[Ifa]
      )
      dhc.collect()
    }
  }
}

object IfaHomeCarriersJobSpec {

  case class IfaHomeCarrierBB17(
    ifa: Ifa,
    carrier: String,
    date: Date
  )
}
