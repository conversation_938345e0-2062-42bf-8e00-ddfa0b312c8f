package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.AllocationOwnBlocks.{WeightFactor, CensusBlockHouseholds2, WeightAllocated}
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.{Allocation, UdpHousehold, Target}
import com.comlinkdata.largescale.schema.broadband_market_share.{CensusBlockSubmarket, BroadbandPenetrationEstimates}
import java.nio.file.Files
import scala.util.Try

class AllocationOwnBlocksSpec extends CldSparkBaseSpec {
  import spark.implicits._

  override def beforeAll(): Unit = {
    super.beforeAll()
    val tmp = Files.createTempDirectory("spatial_allocation_checkpoints").toFile
    Try(tmp.delete())
    spark.sparkContext.setCheckpointDir(tmp.toString)
  }

  describe("helper functions") {
    it("properly computes weight per household") {
      val bbHouseholds = dataset[BroadbandPenetrationEstimates] {
        """
          |census_block_id|county|households
          |              1|     1|        17
          |              2|     1|        16
          |              3|     1|         1
          """
      }

      val udpHouseholds = dataset[UdpHousehold] {
        """
          |census_block_id|submarket|sp_platform|county|household_id|latitude|longitude
          |              1|        S|          0|     1|        SDE=|       0|        0
          |              1|        S|          0|     1|        SDI=|       0|        0
          |              2|        S|          0|     1|        SDM=|       0|        0
          |              3|        S|          0|     1|        SDQ=|       0|        0
          """
      }

      // weight = (17+16+1) / 4 = 34/4 = 8.5   ===> 1 UDP household counts for 8.5 households
      val expected = dataset[WeightFactor](
        """
          |county|weight_factor
          |     1|          8.5
          """
      )

      val result = new AllocationOwnBlocks().computeWeightFactors(bbHouseholds, udpHouseholds)
      assertDatasetEquals(expected, result)
    }

    it("computes allocated weight per destination census block") {
      val ds = dataset[Allocation](
        """
          |census_block_id|submarket|sp_platform|county|household_id|latitude|longitude|weight_factor|weight_allocated|weight_allocated_cumulative|target_census_block_id|distance
          |        census1|        S|          0| censu|        SDE=|       0|        0|          7.5|             5.0|                        5.0|               census2|    10.0
          |        census1|        S|          0| censu|        SDE=|       0|        0|          7.5|             2.5|                        7.5|               census3|   100.0
          |        census1|        S|          0| censu|        SDI=|       0|        0|          7.5|             5.0|                        5.0|               census1|     0.0
          |        census1|        S|          0| censu|        SDM=|       0|        0|          7.5|             2.5|                        2.5|                  null|     0.0
        """)

      val expected = dataset[WeightAllocated](
        """
          |census_block_id|weight_allocated
          |        census1|             5.0
          |        census2|             5.0
          |        census3|             2.5
          """
      )

      val result = new AllocationOwnBlocks().allocatedWeightPerCensusBlock(ds).sort($"census_block_id")
      assertDatasetEquals(expected, result)
    }

    it("counts households per census block and county") {
      val ds = dataset[Allocation](
        """
          |census_block_id|county|submarket|sp_platform|household_id|latitude|longitude|target_census_block_id|distance|weight_factor|weight_allocated|weight_allocated_cumulative
          |             C1|     C|        A|          0|        SDE=|       0|        0|                  null|       0|            5|               0|                          0
          |             C1|     C|        B|          0|        SDI=|       0|        0|                  null|       0|            5|               0|                          0
          |             C2|     C|        A|          0|        SDM=|       0|        0|                  null|       0|            5|               0|                          0
        """)

      val expected = dataset[CensusBlockHouseholds2](
        """
          |census_block_id|households2|weight_factor
          |             C1|          2|            5
          |             C2|          1|            5
          """
      )

      val result = new AllocationOwnBlocks().countHouseholdsPerCensusBlock(ds)
      assertDatasetEquals(expected, result)
    }
  }

  describe("spatial allocation") {

    it("allocates full own-blocks") {
      val bbHouseholds = dataset[BroadbandPenetrationEstimates] {
        """
          |census_block_id|county|households
          |              1|     1|        17
          |              2|     1|        16
          |              3|     1|         1
          """
      }

      val submarkets = dataset[CensusBlockSubmarket] {
        """
          |census_block_id|submarket|county|latitude|longitude
          |              1|        S|     1|       0|        0
          |              2|        S|     1|      10|        0
          |              3|        S|     1|     100|        0
          """
      }

      val udpHouseholds = dataset[UdpHousehold] {
        """
          |census_block_id|submarket|sp_platform|county|household_id|latitude|longitude
          |              1|        S|          0|     1|        SDE=|       0|        0
          |              1|        S|          0|     1|        SDI=|       0|        0
          |              2|        S|          0|     1|        SDM=|      10|        0
          |              3|        S|          0|     1|        SDQ=|     100|        0
          """
      }

      val initialAllocationResult = dataset[Allocation] {
        """
          |census_block_id|submarket|sp_platform|target_census_block_id|county|distance|household_id|weight_factor|weight_allocated|weight_allocated_cumulative|latitude|longitude
          |              1|        S|          0|                  null|     1|       0|        SDE=|          8.5|               0|                          0|       0|        0
          |              1|        S|          0|                  null|     1|       0|        SDI=|          8.5|               0|                          0|       0|        0
          |              2|        S|          0|                  null|     1|       0|        SDM=|          8.5|               0|                          0|      10|        0
          |              3|        S|          0|                  null|     1|       0|        SDQ=|          8.5|               0|                          0|     100|        0
          """
      }

      // weight = (17+16+1) / 4 = 34/4 = 8.5   ===> 1 UDP household counts for 8.5 households
      val weight = new AllocationOwnBlocks().computeWeightFactors(bbHouseholds, udpHouseholds)
      val weightValue = weight.head().weight_factor

      val (allocated, unallocated, newWeightRemaining) = new AllocationOwnBlocks().allocateOwnBlocks(
        bbHouseholds, submarkets, initialAllocationResult
      )

      // we have 2 unique households (SDE=,SDI=) in block "1", that is 2 * 8.5 = 17.0 = 17 => allocate
      // we have 1 unique household (SDM=) in block "2", that is 1 * 8.5 = 8.5        < 16 => allocate
      // we have 1 unique household (SDQ=) in block "3", that is 1 * 8.5 = 8.5        > 1  => don't allocate
      val expectedAllocationResult = dataset[Allocation] {
        s"""
           |census_block_id|submarket|sp_platform|target_census_block_id|county|distance|household_id|weight_factor|weight_allocated|weight_allocated_cumulative|latitude|longitude
           |              1|        S|          0|                     1|     1|       0|        SDE=| $weightValue|    $weightValue|               $weightValue|       0|        0
           |              1|        S|          0|                     1|     1|       0|        SDI=| $weightValue|    $weightValue|               $weightValue|       0|        0
           |              2|        S|          0|                     2|     1|       0|        SDM=| $weightValue|    $weightValue|               $weightValue|      10|        0
           |              3|        S|          0|                  null|     1|       0|        SDQ=| $weightValue|               0|                          0|     100|        0
          """
      }

      val expectedWeightRemaining = dataset[Target] {
        s"""
           |target_census_block_id|county|capacity               |target_submarket|target_latitude|target_longitude
           |                     1|     1|${17 - 2 * weightValue}|               S|              0|               0
           |                     2|     1|    ${16 - weightValue}|               S|             10|               0
           |                     3|     1|                      1|               S|            100|               0
          """
      }

      assertDataFrameDataEquals(expectedAllocationResult.toDF(), (allocated unionByName unallocated).toDF())
      assertDatasetEquals(expectedWeightRemaining, newWeightRemaining.sort($"target_census_block_id"))
    }
  }
}
