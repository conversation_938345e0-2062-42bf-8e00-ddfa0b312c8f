package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.commons.LocalDateRange
import com.comlinkdata.largescale.commons.LocalDateRange.toLocalDateRangeSeq
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandChurn, IfaHomeCarrier}
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.schema.udp.Ifa
import org.junit.rules.TemporaryFolder

import java.time.LocalDate

class ChurnJobSpec extends CldSparkBaseSpec {

  import spark.implicits._
  private val ifas: Array[Ifa] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]

  describe("full run"){
    val windowSize = 69 // must be bigger than configs minimalPeriodNights, minimalCarrierTenancy, and maximumDaysBetweenCarriers
    val startDate = LocalDate.of(2022, 2, 25)

    val carrierLookupData = Seq(
      CarrierLookup("Carrier A", "A", "1"	, null, "1", "1", "2016-11-01")

    )
    val carrierLookupFwData = Seq(
      CarrierLookup("Carrier B", "B", "2"	, null, "2", "2", "2016-11-01")
    )

    val switchDay = startDate.minusDays(windowSize/2)
    val dateRangeOnCarrierA = LocalDateRange(startDate.minusDays(windowSize), switchDay.minusDays(1))
    val dateRangeOnCarrierB = LocalDateRange(switchDay, startDate)

    val carrierSplit =
      toLocalDateRangeSeq(dateRangeOnCarrierA).map(d => ("Carrier A", 1, d)) ++
      toLocalDateRangeSeq(dateRangeOnCarrierB).map(d => ("Carrier B", 2, d))
    val ifaHomeCarrierData = carrierSplit.map( i => IfaHomeCarrier(
      ifa = ifas(0),
      carrier = i._1,
      year = i._3.getYearString,
      month = i._3.getMonthValueString,
      day = i._3.getDayOfMonthString
    ))

    it("with parquet"){
      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI

      val config: ChurnConfig = ChurnConfig(
        evaluationWindowSize = windowSize,
        minimalPeriodNights = 3,
        minimalCarrierTenancy = 14,
        maximumDaysBetweenCarriers = 14,
        rawDataFirstDate = LocalDate.of(2016, 11, 1),
        carrierLookupLocation = basePath.resolve("broadband_churn_2_carrier_lookup_location"),
        carrierLookupFwLocation = basePath.resolve("broadband_churn_2_carrier_lookup_fw_location"),
        ifaHomeCarrierLocation = basePath.resolve("broadband_churn_2_ifa_home_carrier_location"),
        outputLocation = basePath.resolve("broadband_churn_2_output_location"),
        startDateOpt = Some(startDate),
        endDateOpt = Some(startDate) // 1 day process
      )

      spark.createDataFrame(carrierLookupData).write.parquet(config.carrierLookupLocation.toString)
      spark.createDataFrame(carrierLookupFwData).write.parquet(config.carrierLookupFwLocation.toString)

      spark.createDataFrame(ifaHomeCarrierData)
        .write
        .partitionBy("year", "month", "day")
        .parquet(config.ifaHomeCarrierLocation.toString)

      ChurnRunner.runJob(config)

      val outTSL = BroadbandChurn.tsl(config.outputLocation)

      val result = spark.read
        .parquet(outTSL.partition(startDate))
        .as[BroadbandChurn]
        .toDF()
      folder.delete()

      val expected = Seq(
        BroadbandChurn(
          ifa = ifas(0),
          loser_sp_id = 1,
          loser_first_date = startDate.minusDays(windowSize-1).toDate,
          loser_last_date = switchDay.minusDays(1).toDate,
          winner_sp_id = 2,
          winner_first_date = switchDay.toDate,
          winner_last_date = startDate.toDate
        )).toDF()
      assertDataFrameDataEquals(expected, result)
    }
  }
}
