package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.marketshare.Lookups.spidLookup
import com.comlinkdata.largescale.schema.FwaTrendsNoDate
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.FixedWirelessSubsOutput

class FixedWirelessTrendsSpec extends CldSparkBaseSpec {

  import spark.implicits._

  describe("fixed wireless trends") {
    it("should work") {
      val fwaQ1 = dataset[FixedWirelessSubsOutput] {
        """
          |      date|      block|carrier|num_networks|
          |2022-01-01|12345678901|    TMO|          10|
          |2022-01-01|12345678901|      A|          10|
          |2022-01-02|12345678901|      B|          15|"""
      }
      val fwaQ2 = dataset[FixedWirelessSubsOutput] {
        """
          |      date|      block|carrier|num_networks|
          |2022-02-01|12345678901|    TMO|          5|
          |2022-02-01|12345678901|      A|          5|
          |2022-02-02|12345678901|      B|          7|"""
      }

      val fwaTrends = FixedWirelessTrends()
      val result = fwaTrends.runDay(spidLookup.toDS, fwaQ1, fwaQ2)

      val expected = dataset[FwaTrendsNoDate] {
        """
          |sp_id|      tract|growth|
          |    0|12345678901|   -13|
          | 6713|12345678901|    -5|"""
      }
      assertDatasetEquals(expected, result.sort($"sp_id"))
    }
  }
}
