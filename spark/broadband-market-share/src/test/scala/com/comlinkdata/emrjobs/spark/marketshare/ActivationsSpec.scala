package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.Linkage.Fields
import com.comlinkdata.commons.testing.generators.common.generate
import com.comlinkdata.commons.testing.generators.udp.{genIPHighConfidence, genCellularOnly}
import Lookups.carrierLookup
import com.comlinkdata.emrjobs.spark.marketshare.ActivationsSpec.BroadbandActivationStripped
import com.comlinkdata.largescale.commons.Utils

import java.sql.Date

class ActivationsSpec extends CldSparkBaseSpec {
  import spark.implicits._

  describe("broadband activations") {

    it("should find intersection of CO_(N-1) and HC_N") {
      val hc = generate(5, genIPHighConfidence())
        .map(_.copy(carrier = "C", ip_min_date = Date.valueOf("2022-08-19")))
      val co = Fields.oneToOne("ifa").link(hc, generate(5, genCellularOnly()))

      val bba = Activations()
      val result = bba
        .runDay(hc.toDS(), co.toDS(), carrierLookup.toDS)
        .select(BroadbandActivationStripped.cols: _*)
        .as[BroadbandActivationStripped]

      val expected = dataset[BroadbandActivationStripped] {
        """
          |churn_date|winning_carrier|winning_sp|churn_type|losing_sp|
          |2022-08-19|              C|         1|     co2bb|        0|
          |2022-08-19|              C|         1|     co2bb|        0|
          |2022-08-19|              C|         1|     co2bb|        0|
          |2022-08-19|              C|         1|     co2bb|        0|
          |2022-08-19|              C|         1|     co2bb|        0|"""
      }

      assertDatasetEquals(expected, result)
    }

  }
}
object ActivationsSpec {

  case class BroadbandActivationStripped(
    churn_date: Date,
    winning_carrier: String,
    winning_sp: Int,
    churn_type: String,
    losing_sp: Int
  )
  object BroadbandActivationStripped extends Utils.reflection.ColumnNames[BroadbandActivationStripped]
}
