package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.common.{genIfa, genIpV4, generate}
import com.comlinkdata.commons.testing.generators.udp.{genIfaSet, genUdpIfaIpAgg}
import com.comlinkdata.emrjobs.spark.marketshare.IfaIpCarrierFwa
import com.comlinkdata.largescale.commons.{LocalDateRange, UriDFTableRW}
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.schema.broadband_market_share.BroadbandIfaIpCarrier
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.tier2.UdpIfaIpAgg
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.{concat, lit, to_date}
import org.junit.rules.TemporaryFolder
import org.scalacheck.Gen

import java.sql.Date
import java.time.LocalDate

class IfaIpCarrierFwaJobSpec extends CldSparkBaseSpec with LazyLogging {

  import spark.implicits._

  describe("Full run") {
    lazy val ifas = generate(30, genIfa)
    lazy val ips = generate(30, genIpV4)
    lazy val startDate = LocalDate.of(2021, 10, 1)
    lazy val endDate = LocalDate.of(2021, 10, 2)
    lazy val daysToRun = LocalDateRange(startDate, endDate)

    lazy val ifaIpCarrierData = Seq(
      BroadbandIfaIpCarrier(ifas(1), ips(2), "Some Carrier", Some(false), "mw05", startDate.getYearString, startDate.getMonthValueString, startDate.getDayOfMonthString),
      BroadbandIfaIpCarrier(ifas(2), ips(2), "Some Carrier", Some(false), "mw05", startDate.getYearString, startDate.getMonthValueString, startDate.getDayOfMonthString),
      BroadbandIfaIpCarrier(ifas(3), ips(2), "Some Carrier", Some(false), "mw05", startDate.getYearString, startDate.getMonthValueString, startDate.getDayOfMonthString),
      BroadbandIfaIpCarrier(ifas(4), ips(2), "Some Carrier", Some(false), "mw05", endDate.getYearString, endDate.getMonthValueString, endDate.getDayOfMonthString),
    )

    lazy val verizonFwIpData = Seq(VerizonFwIp(ip_2_octets = ips(2)(0) + "." + ips(2)(1), ssid_bssid_count = 1L, startDate.toDate))
    lazy val ifaIpAgg: Seq[UdpIfaIpAgg] = generate(1, genUdpIfaIpAgg()).map(_.copy(
      ifa = ifas(4),
      carrier = "Verizon FW",
      ip = ips(2)
    )) ++
      generate(1, genUdpIfaIpAgg()).map(_.copy(
        ifa = ifas(5),
        carrier = "T-Mobile USA",
        ip = ips(2)
      ))

    lazy val ifaSetLocationData = Seq("mw05", "mw03", "mw07", "quadrant", "gamoshi").flatMap { ds =>
      generate(genIfaSet(
        ifas = Gen.oneOf(Seq(ifas(5))),
        dates = Gen.const(Date.valueOf(endDate)),
      )).map(_.copy(ds = ds))
    }

    it("with parquet") {
      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()
      val basePath = folder.toURI

      val config = IfaIpCarrierFwaJobConfig(
        startDate = Some(startDate),
        endDate = Some(endDate),
        verizonFwIpLocation = basePath.resolve("ifa_ip_carrier_fwa_verizonFwIpTable"),
        ifaIpCarrierLocation = basePath.resolve("ifa_ip_carrier_fwa_ifaHomeCarrierTable"),
        ifaIpAggLocation = basePath.resolve("ifa_ip_carrier_fwa_ifaIpAggTable"),
        ifaSetLocation = basePath.resolve("ifa_ip_carrier_fwa_ifaSetTable"),
        datasources = Seq(ComlinkdataDatasource.mw07),
        baseIfaDatasource = ComlinkdataDatasource.mw07,
        outputLocation = basePath.resolve("ifa_ip_carrier_fwa_out"),
        outputPartitions = None
      )

      UriDFTableRW(config.verizonFwIpLocation).writeDs(verizonFwIpData.toDS())

      config.datasources
        .map(ds => ifaIpCarrierData.toDS().withColumn("ds", lit(ds.toString)))
        .reduce(_ unionByName _)
        .write
        .partitionBy("ds", "year", "month", "day")
        .parquet(config.ifaIpCarrierLocation.toString)

      config.datasources
        .flatMap( ds => daysToRun.map( day=> (ds, day))) // cross product (ds, day)
        .map { x =>
          val ds = x._1
          val day = x._2
          ifaIpAgg.toDS()
            .withColumn("year", lit(day.getYearString))
            .withColumn("month", lit(day.getMonthValueString))
            .withColumn("day", lit(day.getDayOfMonthString))
            .withColumn("local_date", to_date(concat(lit(day.getYearString),
              lit("-"), lit(day.getMonthValueString),
              lit("-"), lit(day.getDayOfMonthString))))
            .withColumn("ds", lit(ds.toString))
        }.reduce(_ unionByName _)
        .write
        .partitionBy("ds", "year", "month", "day")
        .parquet(config.ifaIpAggLocation.toString)

      ifaSetLocationData.toDS()
        .write
        .partitionBy("ds", "year", "month", "day")
        .parquet(config.ifaSetLocation.toString)

      IfaIpCarrierFwaJobRunner.runJob(config)
      val out = BroadbandIfaIpCarrier.read(config.outputLocation, LocalDateRange(startDate, endDate), config.datasources)

      //out.count() shouldBe 3 + ifaIpAggCount //daysToRun.length = verizon counts
      out.select($"year", $"month", $"day").distinct.count() shouldBe daysToRun.length
    }

    it("with parquet different base ifa set ds") {
      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()
      val basePath = folder.toURI

      val config = IfaIpCarrierFwaJobConfig(
        startDate = Some(startDate),
        endDate = Some(endDate),
        verizonFwIpLocation = basePath.resolve("ifa_ip_carrier_fwa_verizonFwIpTable"),
        ifaIpCarrierLocation = basePath.resolve("ifa_ip_carrier_fwa_ifaHomeCarrierTable"),
        ifaIpAggLocation = basePath.resolve("ifa_ip_carrier_fwa_ifaIpAggTable"),
        ifaSetLocation = basePath.resolve("ifa_ip_carrier_fwa_ifaSetTable"),
        datasources = Seq(ComlinkdataDatasource.mw07),
        baseIfaDatasource = ComlinkdataDatasource.quadrant,
        outputLocation = basePath.resolve("ifa_ip_carrier_fwa_out"),
        outputPartitions = None
      )

      UriDFTableRW(config.verizonFwIpLocation).writeDs(verizonFwIpData.toDS())

      config.datasources
        .map(ds => ifaIpCarrierData.toDS().withColumn("ds", lit(ds.toString)))
        .reduce(_ unionByName _)
        .write
        .partitionBy("ds", "year", "month", "day")
        .parquet(config.ifaIpCarrierLocation.toString)

      config.datasources
        .flatMap(ds => daysToRun.map(day => (ds, day))) // cross product (ds, day)
        .map { x =>
          val ds = x._1
          val day = x._2
          ifaIpAgg.toDS()
            .withColumn("year", lit(day.getYearString))
            .withColumn("month", lit(day.getMonthValueString))
            .withColumn("day", lit(day.getDayOfMonthString))
            .withColumn("local_date", to_date(concat(lit(day.getYearString),
              lit("-"), lit(day.getMonthValueString),
              lit("-"), lit(day.getDayOfMonthString))))
            .withColumn("ds", lit(ds.toString))
        }.reduce(_ unionByName _)
        .write
        .partitionBy("ds", "year", "month", "day")
        .parquet(config.ifaIpAggLocation.toString)

      ifaSetLocationData.toDS()
        .write
        .partitionBy("ds", "year", "month", "day")
        .parquet(config.ifaSetLocation.toString)

      IfaIpCarrierFwaJobRunner.runJob(config)
      val out = BroadbandIfaIpCarrier.read(config.outputLocation, LocalDateRange(startDate, endDate), config.datasources)

      //out.count() shouldBe 3 + ifaIpAggCount //daysToRun.length = verizon counts
      out.select($"year", $"month", $"day").distinct.count() shouldBe daysToRun.length
    }

    ignore("without parquet") {
      val process = IfaIpCarrierFwa()
      val dhc = process.runDay(
        ifaIpCarrierData.toDS().as[BroadbandIfaIpCarrier],
        ifaIpAgg.toDS(),
        verizonFwIpData.toDS(),
        ifaSetLocationData.toDS().select($"ifa").distinct().as[Ifa],
        ifaSetLocationData.toDS().select($"ifa").distinct().as[Ifa]
      )
      dhc.collect()
    }
  }
}