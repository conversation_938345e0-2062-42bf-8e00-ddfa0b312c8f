package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.broadband.genCensusBlockNoSpLookup
import com.comlinkdata.commons.testing.generators.common.generate
import com.comlinkdata.commons.testing.generators.udp.genIPHighConfidence
import com.comlinkdata.emrjobs.spark.marketshare.ChurnWithDimensions
import com.comlinkdata.largescale.commons.LocalDateRange.toLocalDateRangeSeq
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.LocalDateRange
import com.comlinkdata.largescale.schema.broadband.lookup.{PopStatsCensusBlockCentroidsTaggedNpa, CarrierLookup, CensusBlockNoSpLookup}
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandChurnWithDimension, BroadbandChurn, BroadbandDailyHome}
import com.comlinkdata.largescale.schema.udp.{Ifa, Household, Ip}
import com.comlinkdata.largescale.schema.udp.installbase.{IpToLocation, IPHighConfidence, IfaToLocation}
import com.comlinkdata.largescale.schema.udp.location.Point
import com.typesafe.scalalogging.LazyLogging
import org.apache.sedona.sql.utils.SedonaSQLRegistrator
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{col, lit}
import org.junit.rules.TemporaryFolder
import org.scalatest.BeforeAndAfterAll

import java.time.{DayOfWeek, LocalDate}

class ChurnWithDimensionsJobSpec extends CldSparkBaseSpec with LazyLogging with BeforeAndAfterAll {
  import spark.implicits._

  private val ifas: Array[Ifa] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]
  private val ips: Array[Ip] = (32 to 62).map( x => Array(192.toByte, 168.toByte, 0.toByte, x.toByte)).toArray[Array[Byte]]
  private val hhids: Array[Household] = (32 to 62).map( x => Array(10.toByte, x.toByte)).toArray[Array[Byte]]

  override def beforeAll(): Unit = {
    super.beforeAll()
    SedonaSQLRegistrator.registerAll(spark)
  }

  describe("Full run") {
    val windowTailDate = LocalDate.of(2022, 2, 25)
    val windowSize = 69
    val daysToProcess = LocalDateRange(windowTailDate.minusDays(windowSize), windowTailDate)
    val switchDate = LocalDate.of(2022, 1, 5)
    val churnDate = switchDate.minusDays(14) //LocalDate.of(year, 12, 21)

    val carrierLookupData = Seq(
      CarrierLookup("Carrier A", "A", "1"	, null, "1", "1", "2016-11-01"),
      CarrierLookup("Carrier B", "B", "2"	, null, "2", "2", "2016-11-01"),
      CarrierLookup("Carrier C", "C", "3"	, null, "3", "3", "2016-11-01")
    )
    val carrierLookupFwData = Seq(
      CarrierLookup("T-Mobile Home Internet", "W", "88888"	, null, "6713", "6713", "2016-11-01"),
      CarrierLookup("Verizon W", "W", "99999"	, null, "6715", "6715", "2016-11-01")
    )

    val highConfidenceData: Seq[IPHighConfidence] = toLocalDateRangeSeq(daysToProcess)
      .filter(_.getDayOfWeek == DayOfWeek.SATURDAY)
      .zipWithIndex
      .flatMap { case (day, index) => Seq(0, 1, 2, 3).map(x => (x, day, index)) }
      .flatMap {
        case (ifa_index: Int, day: LocalDate, index: Int) => generate(genIPHighConfidence()).map(_.copy(
          ifa = ifas(ifa_index),
          ip = ips(ifa_index),
          household_id = hhids( ifa_index * (index % 2)), // change household id every 2 weeks
          carrier = if (day.isBefore(switchDate)) "Carrier A" else "Carrier B",
          year = day.getYearString,
          month = day.getMonthValueString,
          day = day.getDayOfMonthString
        ))
      }

    val ipLocsData = toLocalDateRangeSeq(daysToProcess)
      .flatMap(day => Seq((day, Point(1f, 1f), 10), (day, Point(2f, 2f), 5)))
      .map{ case (day, point, point_obs) =>
        IpToLocation(
          ip = ips(0),
          obs_count = 1,
          night_obs_count = 1,
          ifas = Seq(ifas(0)),
          ifa_count = 1,
          hours_seen = Seq(0, 1, 2, 3),
          point = point,
          point_obs = point_obs,
          year = day.getYearString,
          month = day.getMonthValueString,
          day = day.getDayOfMonthString
        )
      }

    val vzwIfaLocsData = toLocalDateRangeSeq(daysToProcess)
      .flatMap(day => Seq(1, 2, 3, 5).map(x => (x, day)))
      .flatMap{ case (i, day) => Seq((i, day, Point(1f, 1f), 10), (i, day, Point(2f, 2f), 5)) }
      .map { case (i, day, point, point_obs) =>
        IfaToLocation(
          ifa = ifas(i),
          obs_count = 1,
          night_obs_count = 1,
          ips = Seq(ips(i)),
          ip_count = 1,
          hours_seen = Seq(0, 1, 2, 3),
          point = point,
          point_obs = point_obs,
          year = day.getYearString,
          month = day.getMonthValueString,
          day = day.getDayOfMonthString
        )
      }

    val tmoIfaLocsData = toLocalDateRangeSeq(daysToProcess)
      .flatMap(day => Seq(7, 8).map(x => (x, day)))
      .flatMap { case (i, day) => Seq((i, day, Point(1f, 1f), 10), (i, day, Point(2f, 2f), 5)) }
      .map { case (i, day, point, point_obs) =>
        IfaToLocation(
          ifa = ifas(i),
          obs_count = 1,
          night_obs_count = 1,
          ips = Seq(ips(i)),
          ip_count = 1,
          hours_seen = Seq(0, 1, 2, 3),
          point = point,
          point_obs = point_obs,
          year = day.getYearString,
          month = day.getMonthValueString,
          day = day.getDayOfMonthString
        )
      }

    val dailyHomeData = toLocalDateRangeSeq(daysToProcess)
      .flatMap(day => Seq(1, 2, 3, 5, 7, 8).map(x => (x, day)))
      .map { case (i, day) =>
        BroadbandDailyHome(
          ifa = ifas(i),
          location_rank = 1,
          geoid = "123",
          distinct_hours = 1,
          date = day.toDate
        )
      }

    val censusBlockNoSpLookupData = generate(genCensusBlockNoSpLookup()).map(_.copy(
      block_group = "123",
      census_blockid = "456"
    ))

    val churnData = Seq(
      // wifi only test
      BroadbandChurn(ifas(0), 1, churnDate.toDate, switchDate.toDate, 2, switchDate.plusDays(1).toDate, windowTailDate.toDate),
      // ifa location test
      BroadbandChurn(ifas(1), 1, churnDate.toDate, switchDate.toDate, 2, switchDate.plusDays(1).toDate, windowTailDate.toDate),
      // verizon test
      BroadbandChurn(ifas(2), 6715, churnDate.toDate, switchDate.toDate, 2, switchDate.plusDays(1).toDate, windowTailDate.toDate),
      BroadbandChurn(ifas(3), 2, churnDate.toDate, switchDate.toDate, 6715, switchDate.plusDays(1).toDate, windowTailDate.toDate),
      // missing census block id
      BroadbandChurn(ifas(4), 1, churnDate.toDate, switchDate.toDate, 3, switchDate.plusDays(1).toDate, windowTailDate.toDate),
      // missing household_id
      BroadbandChurn(ifas(5), 1, churnDate.toDate, switchDate.toDate, 2, switchDate.plusDays(1).toDate, windowTailDate.toDate),
      // tmobile test
      BroadbandChurn(ifas(7), 6713, churnDate.toDate, switchDate.toDate, 2, switchDate.plusDays(1).toDate, windowTailDate.toDate),
      BroadbandChurn(ifas(8), 2, churnDate.toDate, switchDate.toDate, 6713, switchDate.plusDays(1).toDate, windowTailDate.toDate),
    )

    val polygonsPath = this.getClass.getResource("polygons").toURI

    it("with parquet") {

      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI

      val config: ChurnWithDimensionsJobConfig = ChurnWithDimensionsJobConfig(
        bbChurnLocation = basePath.resolve("bb_churn_with_dims_bb_churn_location"),
        highConfidenceLocation = basePath.resolve("bb_churn_with_dims_high_confidence_location"),
        ipLocsLocation = basePath.resolve("bb_churn_with_dims_ip_locs_location"),
        udpDailyHomeLocation = basePath.resolve("bb_churn_with_dims_udpDailyHomeLocation"),
        censusBlockNoSpLookupLocation = basePath.resolve("bb_churn_with_dims_censusBlockNoSpLookupLocation"),
        popstatsLocation = this.getClass.getResource("popstats/popstats.csv").toURI,
        carrierLookupLocation = basePath.resolve("bb_churn_with_dims_carrier_lookup_location"),
        carrierLookupFwLocation = basePath.resolve("bb_churn_with_dims_carrier_lookup_fw_location"),
        polygonsLocation = polygonsPath,
        outputLocation = basePath.resolve("bb_churn_with_dims_output_location"),
        evaluationWindowSize = windowSize,
        startDate = windowTailDate,
        endDate = Some(windowTailDate),
        repartition = None
      )

      spark.createDataFrame(carrierLookupData).write.parquet(config.carrierLookupLocation.toString)
      spark.createDataFrame(carrierLookupFwData).write.parquet(config.carrierLookupFwLocation.toString)

      spark.createDataFrame(highConfidenceData).write
        .partitionBy("year", "month", "day")
        .parquet(config.highConfidenceLocation.toString)

      spark.createDataFrame(ipLocsData).write
        .partitionBy("year", "month", "day")
        .parquet(config.ipLocsLocation.toString)

      spark.createDataFrame(dailyHomeData).write
        .partitionBy("date")
        .parquet(config.udpDailyHomeLocation.toString)

      spark.createDataFrame(censusBlockNoSpLookupData).write
        .parquet(config.censusBlockNoSpLookupLocation.toString)

      spark.createDataFrame(churnData)
        .withColumn("window_tail_date", lit(windowTailDate))
        .transform(customFunctions.addYmdCols(col("window_tail_date")))
        .write
        .partitionBy("year", "month", "day")
        .parquet(config.bbChurnLocation.toString)

      ChurnWithDimensionsJobRunner.runJob(config)
      val tslOut = BroadbandChurnWithDimension.tsl(config.outputLocation)

      val out: DataFrame = spark.read
        .option(ReadOpts.basePath, config.outputLocation.toString)
        .parquet(tslOut.partitions(windowTailDate, Some(windowTailDate).getOrElse(windowTailDate)):_*)

      //out.orderBy("ifa", "w_or_l").show
      // multiply by 2 for winner / loser. Minus 1 because missing census block on i=4
      val expectedCount = 2 * (churnData.length - 1)
      out.count() shouldBe expectedCount
      folder.delete()
    }

    ignore("without parquet"){

      val dsChurn = churnData.toDS()
      val data = dsChurn.transform(ChurnWithDimensions().run(
        endDate = windowTailDate,
        polygonsPath = polygonsPath,
        dsCarrierLookup = carrierLookupData.toDS(),
        dsHighConfidence = highConfidenceData.toDS(),
        dsIpLocs = ipLocsData.toDS(),
        spark.emptyDataset[BroadbandDailyHome],
        spark.emptyDataset[CensusBlockNoSpLookup],
        spark.emptyDataset[PopStatsCensusBlockCentroidsTaggedNpa]
      ))

      data.collect()
    }
  }
}
