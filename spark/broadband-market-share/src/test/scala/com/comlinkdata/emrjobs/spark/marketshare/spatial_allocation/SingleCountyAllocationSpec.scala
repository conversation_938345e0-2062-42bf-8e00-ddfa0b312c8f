package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.{Allocation, Target}

class SingleCountyAllocationSpec extends CldSparkBaseSpec {
  import spark.implicits._
  val otherSubmarketPenalty = 1.25
  val otherTractPenalty = 1.25

  describe("single-county spatial allocation") {

    it("should allocate blocks") {
      val unallocated = data[Allocation] {
        s"""
           |census_block_id|submarket|sp_platform|county|household_id|weight_factor|weight_allocated|weight_allocated_cumulative|target_census_block_id|distance|latitude|longitude
           |130759601001063|        S|          0| 13075|        SDE=|          7.5|               0|                          0|                  null|     0.0|       0|        0
           |130759601001063|        S|          0| 13075|        SDI=|          7.5|               0|                          0|                  null|     0.0|       0|        0
          """
      }

      val targets = data[Target](
        """
          |target_census_block_id|county|capacity|target_submarket|target_latitude|target_longitude
          |       130759601001063| 13075|       5|               S|              0|               0
          |       130759601001055| 13075|       5|               S|             10|               0
          |       130759601004014| 13075|       5|               S|            100|               0
          """
      )

      val expected = dataset[Allocation] {
        s"""
           |household_id|county|census_block_id|target_census_block_id|sp_platform|submarket|latitude|longitude|distance|weight_factor|weight_allocated|weight_allocated_cumulative|
           |        SDE=| 13075|130759601001063|       130759601001063|          0|        S|     0.0|      0.0|     0.0|          7.5|             5.0|                        5.0|
           |        SDE=| 13075|130759601001063|       130759601001055|          0|        S|     0.0|      0.0|    10.0|          7.5|             2.5|                        7.5|
           |        SDI=| 13075|130759601001063|       130759601001055|          0|        S|     0.0|      0.0|    10.0|          7.5|             2.5|                        2.5|
           |        SDI=| 13075|130759601001063|       130759601004014|          0|        S|     0.0|      0.0|   100.0|          7.5|             5.0|                        7.5|
          """
      }

      val distance = Distance(Distance.euclid, otherSubmarketPenalty, otherTractPenalty)
      val result = SingleCountyAllocation("13075", distance).run(unallocated, targets).toDS()

      assertDataFrameEquals(expected.toDF(), result.toDF())
    }
  }
}
