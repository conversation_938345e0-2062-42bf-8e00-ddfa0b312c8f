package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs

import com.comlinkdata.commons.testing.CldBaseSpec
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs.HighestConfidenceRunner._
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.inputs.Schema.{Activity, TimeSeries}
import java.lang.{Integer => JInt}
import scala.language.implicitConversions

class HighestConfidenceSpec extends CldBaseSpec {
  def intSeries(arg: JInt*): Seq[TimeSeries] =
    arg.zipWithIndex.map { case (spid, idx) => TimeSeries("%02d".format(idx), null, null, spid, false) }

  def series(arg: String*): Seq[TimeSeries] =
    arg.map(TimeSeries(_, null, null, null, false))

  def series(arg: (String, String, Boolean)): TimeSeries =
    TimeSeries(arg._1, null, arg._2, null, arg._3)

  describe("fill series") {
    it("should handle empty series") {
      fillTimeSeries(Seq("1"))(Seq.empty) shouldBe series("1")
      fillTimeSeries(Seq("1", "3"))(Seq.empty) shouldBe series("1", "3")
    }
    it("should fill in sequence") {
      fillTimeSeries(Seq("3", "1"))(Seq.empty) shouldBe series("1", "3")
    }
    it("should fill gaps in the beginning") {
      fillTimeSeries(Seq("1", "3", "5"))(series("5")) shouldBe series("1", "3", "5")
      fillTimeSeries(Seq("1", "3", "5"))(series("3", "5")) shouldBe series("1", "3", "5")
    }
    it("should fill gaps in the end") {
      fillTimeSeries(Seq("3", "1", "5"))(series("1")) shouldBe series("1", "3", "5")
      fillTimeSeries(Seq("1", "3", "5"))(series("1", "3")) shouldBe series("1", "3", "5")
    }
    it("should fill gaps in the middle") {
      fillTimeSeries(Seq("1", "3", "5"))(series("1", "5")) shouldBe series("1", "3", "5")
    }
    it("should ignore partitions not in range") {
      fillTimeSeries(Seq("1", "3", "5"))(series("1", "3", "5", "0")) shouldBe series("1", "3", "5")
      fillTimeSeries(Seq("1", "3", "5"))(series("1", "3", "5", "2")) shouldBe series("1", "3", "5")
      fillTimeSeries(Seq("1", "3", "5"))(series("1", "3", "5", "4")) shouldBe series("1", "3", "5")
      fillTimeSeries(Seq("1", "3", "5"))(series("1", "3", "5", "6")) shouldBe series("1", "3", "5")
    }
  }
  describe("remove inconsistencies") {
    it("should remove all SPIDs without consistent sequences") {
      removeInconsistencies(intSeries(1, null, 1, 1, 1, null, 2, 2, 1, 4, 4, 4, null)) shouldBe
        intSeries(1, null, 1, 1, 1, null, null, null, 1, 4, 4, 4, null)
    }
  }
  describe("fill nulls") {
    it("should fill single nulls between consistent streaks") {
      fillNulls(intSeries(1, 1, 1, null, 1, 1, 1)) shouldBe intSeries(1, 1, 1, 1, 1, 1, 1)
      fillNulls(intSeries(null, 1, 1, 1, null, 1, 1, 1)) shouldBe intSeries(null, 1, 1, 1, 1, 1, 1, 1)
      fillNulls(intSeries(1, 1, 1, null, 1, 1, 1, null)) shouldBe intSeries(1, 1, 1, 1, 1, 1, 1, null)
    }
    it("should fill double nulls between consistent streaks") {
      fillNulls(intSeries(1, 1, 1, null, null, 1, 1, 1)) shouldBe intSeries(1, 1, 1, 1, 1, 1, 1, 1)
      fillNulls(intSeries(null, 1, 1, 1, null, null, 1, 1, 1)) shouldBe intSeries(null, 1, 1, 1, 1, 1, 1, 1, 1)
      fillNulls(intSeries(1, 1, 1, null, null, 1, 1, 1, null)) shouldBe intSeries(1, 1, 1, 1, 1, 1, 1, 1, null)
    }
    it("should ignore nulls between different streaks") {
      fillNulls(intSeries(1, 1, 1, null, null, 2, 2, 2)) shouldBe intSeries(1, 1, 1, null, null, 2, 2, 2)
      fillNulls(intSeries(1, 1, 1, null, null, 2, 2, 2, 1, 1, 1)) shouldBe intSeries(1, 1, 1, null, null, 2, 2, 2, 1, 1, 1)
    }
    it("should ignore triple nulls") {
      fillNulls(intSeries(1, 1, 1, null, null, null, 1, 1, 1)) shouldBe intSeries(1, 1, 1, null, null, null, 1, 1, 1)
    }
  }
  describe("check activity") {
    it("should not check activity if two latest entries are not null") {
      checkActivity(Seq())(intSeries(1, 2, 3, 4)) shouldBe intSeries(1, 2, 3, 4)
    }
    it("should ignore irrelevant activity") {
      checkActivity(Seq(Activity(1, "1")))(intSeries(2, null, null)) shouldBe intSeries(2, null, null)
    }
    it("should return unchanged series if there's no activity") {
      checkActivity(Seq())(intSeries(1, null, null)) shouldBe intSeries(1, null, null)
    }
    it("should pick the latest relevant activity") {
      checkActivity(Seq(Activity(1, "08"), Activity(2, "07")))(intSeries(1, 1, 2, 1, 2, 2, null, null)) shouldBe
        intSeries(1, 1, 2, 1, 2, 2, null, null, 1)
    }
  }
  describe("check quality") {
    it("should fail any empty series") {
      isGoodQuality(intSeries()) shouldBe false
      isGoodQuality(intSeries(null)) shouldBe false
      isGoodQuality(intSeries(null, null)) shouldBe false
    }
    it("should fail any series with one non-empty entry") {
      isGoodQuality(intSeries(1)) shouldBe false
      isGoodQuality(intSeries(null, 1)) shouldBe false
      isGoodQuality(intSeries(1, null)) shouldBe false
      isGoodQuality(intSeries(null, 1, null)) shouldBe false
    }
    it("should fail any series with non-contiguous blocks") {
      isGoodQuality(intSeries(1, null, 1)) shouldBe false
      isGoodQuality(intSeries(1, 2, 1)) shouldBe false
    }
    it("should pass any series with at least two contiguous blocks") {
      isGoodQuality(intSeries(1, 1)) shouldBe true
      isGoodQuality(intSeries(null, 1, 1)) shouldBe true
      isGoodQuality(intSeries(1, 1, null)) shouldBe true
      isGoodQuality(intSeries(null, 1, 1, null)) shouldBe true
    }
  }
  describe("weight calculations") {
    it("prefers SVT blocks") {
      findBestBlock(Seq(
        ("2023-06-01", "CB1", true), ("2023-06-02", "CB1", true), ("2023-06-03", "CB2", false), ("2023-06-04", "CB2", false),
      ).map(series)) shouldBe TimeSeries("2023-06-01", null, "CB1", null, true)
      findBestBlock(Seq(
        ("2023-06-01", "CB1", true), ("2023-06-02", "CB2", false), ("2023-06-03", "CB2", false),
      ).map(series)) shouldBe TimeSeries("2023-06-01", null, "CB1", null, true)
    }
  }
}
