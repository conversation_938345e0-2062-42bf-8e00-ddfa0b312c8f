package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.marketshare.job.UpdateVerizonFwIpsJobRunner.VERIZON_ISP
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.Utils
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp
import com.comlinkdata.largescale.schema.tutela.TutelaIpClassification
import org.apache.spark.sql.functions.{concat, lit}

import java.time.LocalDate

class UpdateVerizonFwIpsJobSpec extends CldSparkBaseSpec {
  import spark.implicits._

  describe("update verizon FW IPs - full job") {

    it("works") {
      val tutelaIpClassification = dataset[TutelaIpClassification] {
        s"""
          |ipprefix   |id              |mcc|isp         |ssid               |bssid|year|month|day|
          |174.206.108|21891816824C5A22|311|$VERIZON_ISP|Verizon-LRValsothis|A    |2023|06   |01 |
          |174.240.118|12E01A94A381A222|311|$VERIZON_ISP|Verizon-LRValsothis|B    |2023|06   |01 |
          |174.192.143|F8DE6571F2798396|311|$VERIZON_ISP|Verizon-LRValsothis|C    |2023|06   |01 |
          |174.249.182|E3702238CC005BC9|311|$VERIZON_ISP|Verizon-LRValsothis|D    |2023|06   |01 |
          |174.192.143|819CA6F6F6D0E876|311|$VERIZON_ISP|Verizon-LRValsothis|E    |2023|06   |01 |"""
      }

      val tutelaIpClassificationLocation = randomPath()
      val tutelaJsonFileLocation = Utils.joinPaths(tutelaIpClassificationLocation, "2023", "06", "01", "24")

      tutelaIpClassification
        .drop("year", "month", "day")
        .write
        .json(tutelaJsonFileLocation.toString)

      val outputLocation = randomPath()
      UpdateVerizonFwIpsJobRunner.runJob(UpdateVerizonFwIpsJobConfig(
        None,
        tutelaIpClassificationLocation,
        outputLocation
      ))

      val result = VerizonFwIp.read(outputLocation).sort($"ip_2_octets")

      val expected = dataset[VerizonFwIp] {
        """
          |ip_2_octets|ssid_bssid_count|      date|
          |    174.192|               2|2023-06-01|
          |    174.206|               1|2023-06-01|
          |    174.240|               1|2023-06-01|
          |    174.249|               1|2023-06-01|"""
      }
      assertDatasetEquals(expected, result)
    }

    it("handles earlier months if provided"){
      // ymd isn't important here, and will be dropped
      val tutelaIpClassification = dataset[TutelaIpClassification] {
        s"""
           |ipprefix   |id              |mcc|isp         |ssid               |bssid|year|month|day|
           |174.206.108|21891816824C5A22|311|$VERIZON_ISP|Verizon-LRValsothis|A    |2023|06   |01 |
           |174.240.118|12E01A94A381A222|311|$VERIZON_ISP|Verizon-LRValsothis|B    |2023|06   |01 |
           |174.192.143|F8DE6571F2798396|311|$VERIZON_ISP|Verizon-LRValsothis|C    |2023|06   |01 |
           |174.249.182|E3702238CC005BC9|311|$VERIZON_ISP|Verizon-LRValsothis|D    |2023|06   |01 |
           |174.192.143|819CA6F6F6D0E876|311|$VERIZON_ISP|Verizon-LRValsothis|E    |2023|06   |01 |"""
      }

      val basePath = randomPath()
      val tutelaIpClassificationLocation = Utils.joinPaths(basePath, "tutella_ip_class")
      Seq(
        LocalDate.of(2023, 4, 1),
        LocalDate.of(2023, 5, 1),
        LocalDate.of(2023, 6, 1)
      ).foreach { day =>
        val jsonFileLocation = Utils.joinPaths(
          tutelaIpClassificationLocation,
          day.getYearString,
          day.getMonthValueString,
          day.getDayOfMonthString,
          day.getMonthValueString // hour
        )

        tutelaIpClassification
          .drop("year", "month", "day")
          .withColumn("month", lit(day.getMonthValue.formatted("%03d")))
          .withColumn("ipprefix", concat(lit("174."), $"month", lit("."), $"month"))
          .write
          .json(jsonFileLocation.toString)
      }

      val outputLocation = Utils.joinPaths(basePath, "verizon_fw_ip_output")
      UpdateVerizonFwIpsJobRunner.runJob(UpdateVerizonFwIpsJobConfig(
        Some(LocalDate.of(2023, 5, 1)),
        tutelaIpClassificationLocation,
        outputLocation
      ))

      val result = VerizonFwIp.read(outputLocation).sort($"ip_2_octets")

      val expected = dataset[VerizonFwIp] {
        """
          |ip_2_octets|ssid_bssid_count|      date|
          |    174.005|               5|2023-05-01|"""
      }
      assertDatasetEquals(expected, result)
    }
  }
}
