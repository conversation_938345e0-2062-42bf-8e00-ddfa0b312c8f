package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.Linkage.Fields
import com.comlinkdata.commons.testing.generators.common.generate
import com.comlinkdata.commons.testing.generators.udp.{genIPHighConfidence, genCellularOnly}
import Lookups.{carrierLookup, spidLookup}
import com.comlinkdata.emrjobs.spark.marketshare.DeactivationsSpec.BroadbandDeactivationsStripped
import com.comlinkdata.largescale.commons.Utils
import com.comlinkdata.largescale.schema.FwaTrendsNoDate

import java.sql.Date

class DeactivationsSpec extends CldSparkBaseSpec {

  import spark.implicits._

  describe("broadband to fixed-wireless") {
    it("should work") {
      val hc1 = generate(3, genIPHighConfidence())
        .map(_.copy(ip_max_date = Date.valueOf("2022-08-19"), carrier = "C"))
      val hc2 = generate(3, genIPHighConfidence())
        .map(_.copy(ip_max_date = Date.valueOf("2022-08-19"), carrier = "C"))

      val co1 = Fields.oneToOne("ifa")
        .link(hc1, generate(3, genCellularOnly()))
        .map(_.copy(census_block_id = "123456789012345", carrier = Some("TMO"), cell_only_days_count = 10))

      val co2 = Fields.oneToOne("ifa")
        .link(hc2, generate(3, genCellularOnly()))
        .map(_.copy(census_block_id = "234567890123456", carrier = Some("TMO"), cell_only_days_count = 10))

      val hc = (hc1 ++ hc2).toDS()
      val co = co1 ++ co2

      // FWA trends need at least 2 rows per SP, because of growth percentile.
      // Percentile is always 0 for the first row (a known bug).
      val fwaTrends = dataset[FwaTrendsNoDate] {
        """
          |sp_id|      tract|growth|
          |6713 |12345678901|    10|
          |6713 |23456789012|    20|"""
      }

      val bbd = Deactivations(0.5)
      val result = bbd
        .runDay(Map(0 -> hc), Map(0 -> co.toDS), carrierLookup.toDS(), spidLookup.toDS(), Seq.empty, fwaTrends, None)
        .select(BroadbandDeactivationsStripped.cols: _*)
        .sort($"winning_sp")
        .as[BroadbandDeactivationsStripped]

      val expected = dataset[BroadbandDeactivationsStripped] {
        """
          |churn_date|churn_type|losing_carrier|losing_sp|winning_census_block|winning_carrier|winning_sp|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|
          |2022-08-19|    bb2fwa|             C|        1|     234567890123456|            TMO|      6713|
          |2022-08-19|    bb2fwa|             C|        1|     234567890123456|            TMO|      6713|
          |2022-08-19|    bb2fwa|             C|        1|     234567890123456|            TMO|      6713|"""
      }
      assertDatasetEquals(expected, result)
    }
  }

  describe("broadband to cellular-only") {
    it("should work") {
      val hc = generate(5, genIPHighConfidence())
        .map(_.copy(ip_max_date = Date.valueOf("2022-08-19"), carrier = "C"))

      val co = Fields.oneToOne("ifa")
        .link(hc, generate(5, genCellularOnly()))
        .map(_.copy(census_block_id = "123456789012345", carrier = Some("TMO"), cell_only_days_count = 10))

      // FWA trends need at least 2 rows per SP, because of growth percentile.
      // Percentile is always 0 for the first row (a known bug).
      val fwaTrends = dataset[FwaTrendsNoDate] {
        """
          |sp_id|      tract|growth|
          |6713 |12345678901|    10|
          |6713 |23456789012|    20|"""
      }

      val bbd = Deactivations(0.5)
      val result = bbd
        .runDay(Map(0 -> hc.toDS), Map(0 -> co.toDS), carrierLookup.toDS(), spidLookup.toDS(), Seq.empty, fwaTrends, None)
        .select(BroadbandDeactivationsStripped.cols: _*)
        .sort($"winning_sp")
        .as[BroadbandDeactivationsStripped]

      val expected = dataset[BroadbandDeactivationsStripped] {
        """
          |churn_date|churn_type|losing_carrier|losing_sp|winning_census_block|winning_carrier|winning_sp|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|"""
      }
      assertDatasetEquals(expected, result)
    }

    it("works on empty fwa-trends") {
      val hc = generate(5, genIPHighConfidence())
        .map(_.copy(ip_max_date = Date.valueOf("2022-08-19"), carrier = "C"))

      val co = Fields.oneToOne("ifa")
        .link(hc, generate(5, genCellularOnly()))
        .map(_.copy(census_block_id = "123456789012345", carrier = Some("TMO"), cell_only_days_count = 10))

      val fwaTrends = spark.emptyDataset[FwaTrendsNoDate]

      val bbd = Deactivations(0.5)
      val result = bbd
        .runDay(Map(0 -> hc.toDS), Map(0 -> co.toDS), carrierLookup.toDS(), spidLookup.toDS(), Seq.empty, fwaTrends, None)
        .select(BroadbandDeactivationsStripped.cols: _*)
        .sort($"winning_sp")
        .as[BroadbandDeactivationsStripped]

      val expected = dataset[BroadbandDeactivationsStripped] {
        """
          |churn_date|churn_type|losing_carrier|losing_sp|winning_census_block|winning_carrier|winning_sp|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|
          |2022-08-19|     bb2co|             C|        1|     123456789012345|            TMO|         0|"""
      }
      assertDatasetEquals(expected, result)
    }
  }
}

object DeactivationsSpec {

  case class BroadbandDeactivationsStripped(
    churn_date: Date,
    churn_type: String,
    losing_carrier: String,
    losing_sp: Int,
    winning_census_block: String,
    winning_carrier: String,
    winning_sp: Int
  )

  object BroadbandDeactivationsStripped extends Utils.reflection.ColumnNames[BroadbandDeactivationsStripped]
}