package com.comlinkdata.emrjobs.spark.marketshare.job

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.broadband.genBroadbandPlatformAggregate
import com.comlinkdata.commons.testing.generators.common.generate
import com.comlinkdata.emrjobs.spark.marketshare.job.RedshiftUploadJobRunner.{getBoundaryDates, oofp_filter, unionPlatformAggregates}
import com.comlinkdata.largescale.commons.RedshiftUtils.RedshiftConfig
import com.comlinkdata.largescale.commons.RichDate.{localDateOrdering, toRichDate}
import com.comlinkdata.largescale.commons.LocalDateRange
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandPlatformAggregate, BroadbandPlatformAggregateWithDs}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.apache.spark.sql.{DataFrame, Dataset, Row}
import org.apache.spark.sql.functions.{lit, max, min}
import org.junit.rules.TemporaryFolder

import java.io.File
import java.net.URI
import java.sql.Date
import java.time.LocalDate

class RedshiftUploadJobSpec extends CldSparkBaseSpec {

  lazy val beforeAllTempFolder = new TemporaryFolder()
  lazy val folder: File = beforeAllTempFolder.newFolder()
  lazy val basePath: URI = folder.toURI

  val prodLdr: LocalDateRange = LocalDateRange(LocalDate.of(2023, 1, 3), LocalDate.of(2023, 1, 31))
  val f17Ldr: LocalDateRange = LocalDateRange(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 5))
  val f20Ldr: LocalDateRange = LocalDateRange(LocalDate.of(2023, 1, 6), LocalDate.of(2023, 1, 10))
  val earliestProdDate: LocalDate = f20Ldr.endDate.plusDays(1) // assuming f20Ldr.endDate > mw07.startDate
  lazy val datasources: Array[BroadbandRedshiftUploadDatasource] = Array(
    BroadbandRedshiftUploadDatasource(
      ds = ComlinkdataDatasource.mw07,
      location = basePath.resolve("bb20_rs_upload_platform_agg_partitioned_by_ds_mw07"),
      startDate = LocalDate.of(2023, 1, 3), // same as f20Ldr.startDate
      endDate = Some(LocalDate.of(2023, 1, 20))
    ),
    BroadbandRedshiftUploadDatasource(
      ds = ComlinkdataDatasource.quadrant,
      location = basePath.resolve("bb20_rs_upload_platform_agg_partitioned_by_ds_quadrant"),
      startDate = LocalDate.of(2023, 1, 15), // need to be after f20Ldr.endDate
      endDate = None
    ),
    BroadbandRedshiftUploadDatasource(
      ds = ComlinkdataDatasource.gamoshi,
      location = basePath.resolve("bb20_rs_upload_platform_agg_partitioned_by_ds_gamoshi"),
      startDate = LocalDate.of(2023, 1, 20),
      endDate = None
    )
  )

  lazy val config: RedshiftUploadJobConfig = RedshiftUploadJobConfig(
    startDate = None, //adjust these per run
    endDate = None, //adjust these per run
    datasources = datasources,
    frozenBb17Location = Some(basePath.resolve("bb20_rs_upload_frozen_bb_17")),
    frozenBb17DateRange = Some(f17Ldr),
    frozenBb20Location = Some(basePath.resolve("bb20_rs_upload_frozen_bb_20")),
    frozenBb20DateRange = Some(f20Ldr),
    overlappingDays = 30,
    redshiftPipelineName = "rs_upload_pipeline",
    redshiftOofpName = Some("rs_upload"),
    redshiftConfig = RedshiftConfig(
      rsTemporaryLocation = basePath.resolve("bb20_rs_upload_temp_location"),
      rsJdbcEndpoint = "test",
      rsUserName = "user",
      rsParameterStoreKey = "pass"
    )
  )

  override def beforeAll(): Unit = {
    super.beforeAll()
    import spark.implicits._

    beforeAllTempFolder.create()

    def genData(startDate: LocalDate, endDate: LocalDate): Seq[BroadbandPlatformAggregate] = (startDate to endDate)
      .flatMap(d => generate(genBroadbandPlatformAggregate(d.toDate)))
      .toSeq

    config.datasources
      .foreach { ds => 
        genData(prodLdr.startDate, prodLdr.endDate).toDS()
          .write
          .partitionBy("the_date")
          .parquet(ds.location.toString)
      }
      
    genData(f17Ldr.startDate, f17Ldr.endDate).toDS()
      .withColumn("ds", lit("bb17"))
      .write
      .partitionBy("the_date")
      .parquet(config.frozenBb17Location.get.toString)
    
    // in prod we have frozen 20 data start in the f17 range, so we will simulate that by have f17 as start date instead
    genData(f17Ldr.startDate, f20Ldr.endDate).toDS()
      .withColumn("ds", lit("bb20"))
      .write
      .partitionBy("the_date")
      .parquet(config.frozenBb20Location.get.toString)
  }

  override def afterAll(): Unit = {
    super.afterAll()
    folder.delete()
  }

  describe("Redshift Upload") {

    def getResultsTranform(ds: Dataset[BroadbandPlatformAggregateWithDs]): DataFrame = ds
      .groupBy("ds")
      .agg(min("the_date") as "min_date", max("the_date") as "max_date")
      .orderBy("ds")

    def getResults: LocalDateRange => Array[Row] =
      unionPlatformAggregates(
        config.frozenBb17DateRange,
        config.frozenBb17Location,
        config.frozenBb20DateRange,
        config.frozenBb20Location,
        config.datasources
      )(_).transform(getResultsTranform).collect()

    def getDatasource(ds: ComlinkdataDatasource): BroadbandRedshiftUploadDatasource = datasources.find(_.ds == ds).head
    
    it("gets earliest start date and max end date frozen"){
      val (earliestDate, latestDate) = getBoundaryDates(
        datasources = config.datasources,
        frozenBb20Location = config.frozenBb20Location,
        frozenBb17Location = config.frozenBb17Location
      )
      earliestDate shouldBe f17Ldr.startDate
      latestDate shouldBe prodLdr.endDate
    }

    it("gets earliest start date and max end date unfrozen") {
      val (earliestDate, latestDate) = getBoundaryDates(
        datasources = config.datasources,
        frozenBb20Location = None,
        frozenBb17Location = None
      )
      earliestDate shouldBe prodLdr.startDate
      latestDate shouldBe prodLdr.endDate
    }

    it("frozen 1.8 data"){
      val result = getResults(LocalDateRange(f17Ldr.startDate, f17Ldr.endDate))

      result.length shouldBe 1
      result(0).get(0) shouldBe "bb17"
      result(0).getDate(1).toLocalDate shouldBe f17Ldr.startDate
      result(0).getDate(2).toLocalDate shouldBe f17Ldr.endDate
    }
    it("frozen 1.8 + frozen 2.0 data"){
      val result = getResults(LocalDateRange(f17Ldr.startDate, f20Ldr.endDate))

      result.length shouldBe 2
      result(0).get(0) shouldBe "bb17"
      result(0).getDate(1).toLocalDate shouldBe f17Ldr.startDate
      result(0).getDate(2).toLocalDate shouldBe f17Ldr.endDate
      result(1).get(0) shouldBe "bb20"
      result(1).getDate(1).toLocalDate shouldBe f20Ldr.startDate
      result(1).getDate(2).toLocalDate shouldBe f20Ldr.endDate
    }
    it("frozen 2.0"){
      val result = getResults(LocalDateRange(f20Ldr.startDate, f20Ldr.endDate))

      result.length shouldBe 1
      result(0).get(0) shouldBe "bb20"
      result(0).getDate(1).toLocalDate shouldBe f20Ldr.startDate
      result(0).getDate(2).toLocalDate shouldBe f20Ldr.endDate
    }
    it("frozen 2.0 + prod mw07 only"){
      // datasources map assumption that mw07 startdate has the earliest start date
      // so get the earliest start date of all other datasources to find the "end date" where no other ds is present
      val endDate = datasources
        .filter(_.ds != ComlinkdataDatasource.mw07)
        .map(_.startDate)
        .min
        .plusDays(-1)

      val result = getResults(LocalDateRange(f20Ldr.startDate, endDate))

      result.length shouldBe 2
      result(0).get(0) shouldBe "bb20"
      result(0).getDate(1).toLocalDate shouldBe f20Ldr.startDate
      result(0).getDate(2).toLocalDate shouldBe f20Ldr.endDate
      result(1).get(0) shouldBe "mw07"
      result(1).getDate(1).toLocalDate shouldBe f20Ldr.endDate.plusDays(1) // assuming f20Ldr.endDate > mw07.startDate
      result(1).getDate(2).toLocalDate shouldBe endDate

    }
    it("frozen 2.0 + prod"){
      val result = getResults(LocalDateRange(f20Ldr.startDate, prodLdr.endDate))

      result.length shouldBe datasources.length + 1
      result(0).get(0) shouldBe "bb20"
      result(0).getDate(1).toLocalDate shouldBe f20Ldr.startDate
      result(0).getDate(2).toLocalDate shouldBe f20Ldr.endDate
      result(1).get(0) shouldBe "gamoshi"
      result(1).getDate(1).toLocalDate shouldBe getDatasource(ComlinkdataDatasource.gamoshi).startDate // assuming gamoshi start date > f20Ldr.endDate
      result(1).getDate(2).toLocalDate shouldBe prodLdr.endDate
      result(2).get(0) shouldBe "mw07"
      result(2).getDate(1).toLocalDate shouldBe f20Ldr.endDate.plusDays(1) // assuming f20Ldr.endDate > mw07.startDate
      result(2).getDate(2).toLocalDate shouldBe getDatasource(ComlinkdataDatasource.mw07).endDate.get
      result(3).get(0) shouldBe "quadrant"
      result(3).getDate(1).toLocalDate shouldBe getDatasource(ComlinkdataDatasource.quadrant).startDate // assuming f20Ldr.endDate > mw07.startDate
      result(3).getDate(2).toLocalDate shouldBe prodLdr.endDate
    }
    it("prod"){
      val result = getResults(LocalDateRange(earliestProdDate, prodLdr.endDate))

      result.length shouldBe datasources.length
      result(0).get(0) shouldBe "gamoshi"
      result(0).getDate(1).toLocalDate shouldBe getDatasource(ComlinkdataDatasource.gamoshi).startDate // assuming gamoshi start date > f20Ldr.endDate
      result(0).getDate(2).toLocalDate shouldBe prodLdr.endDate
      result(1).get(0) shouldBe "mw07"
      result(1).getDate(1).toLocalDate shouldBe earliestProdDate // assuming f20Ldr.endDate > mw07.startDate
      result(1).getDate(2).toLocalDate shouldBe getDatasource(ComlinkdataDatasource.mw07).endDate.get
      result(2).get(0) shouldBe "quadrant"
      result(2).getDate(1).toLocalDate shouldBe getDatasource(ComlinkdataDatasource.quadrant).startDate // assuming f20Ldr.endDate > mw07.startDate
      result(2).getDate(2).toLocalDate shouldBe prodLdr.endDate
    }
    it("unfrozen"){
      val result = unionPlatformAggregates(
        None,
        None,
        None,
        None,
        config.datasources
      )(prodLdr).transform(getResultsTranform).collect()

      result.length shouldBe datasources.length
      result(0).get(0) shouldBe "gamoshi"
      result(0).getDate(1).toLocalDate shouldBe getDatasource(ComlinkdataDatasource.gamoshi).startDate // assuming gamoshi start date > f20Ldr.endDate
      result(0).getDate(2).toLocalDate shouldBe prodLdr.endDate
      result(1).get(0) shouldBe "mw07"
      result(1).getDate(1).toLocalDate shouldBe getDatasource(ComlinkdataDatasource.mw07).startDate // assuming f20Ldr.endDate > mw07.startDate
      result(1).getDate(2).toLocalDate shouldBe getDatasource(ComlinkdataDatasource.mw07).endDate.get
      result(2).get(0) shouldBe "quadrant"
      result(2).getDate(1).toLocalDate shouldBe getDatasource(ComlinkdataDatasource.quadrant).startDate // assuming f20Ldr.endDate > mw07.startDate
      result(2).getDate(2).toLocalDate shouldBe prodLdr.endDate
    }

    it("unfrozen no data on ldr"){
      val result = unionPlatformAggregates(
        None,
        None,
        None,
        None,
        config.datasources
      )(LocalDateRange(f17Ldr.startDate, f17Ldr.startDate)).transform(getResultsTranform).collect()

      result.length shouldBe 0
    }
  }

  describe("oofp filtering") {
    it("handles smoke test") {
      import spark.implicits._
      val platformAgg: Dataset[BroadbandPlatformAggregateWithDs] = generate(genBroadbandPlatformAggregate(Date.valueOf("2023-04-01"))).toDS()
        .withColumn("ds", lit(ComlinkdataDatasource.gamoshi.toString))
        .as[BroadbandPlatformAggregateWithDs]
      val f_syndicated_st_high_confidence = Seq(
        ("0123456789012345", 1, 2, 3)
      ).toDF("census_blockid", "serv_terr_id", "ilec_max_serv_terr_id", "sp_id")
      val d_selection_stats_ps = Seq(
        ("0123456789012345", 1.0001)
      ).toDF("serv_terr_blockid", "hu")
      val d_sp_id_display_rules_with_st = Seq(
        (1, 2, "ILEC", false)
      ).toDF("sp_dim_id", "parent_id", "isp_type", "is_service_territory_sp")
      val df = platformAgg
        .transform(oofp_filter(f_syndicated_st_high_confidence, d_selection_stats_ps, d_sp_id_display_rules_with_st))
      df.count()
    }
  }
}
