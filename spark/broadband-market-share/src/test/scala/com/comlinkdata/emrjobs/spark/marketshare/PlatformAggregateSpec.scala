package com.comlinkdata.emrjobs.spark.marketshare

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.Linkage.Fields
import com.comlinkdata.commons.testing.generators.broadband._
import com.comlinkdata.commons.testing.generators.common.{censusBlocks, generate}
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.schema.broadband.lookup.{CarrierAggRollup, CarrierSuppression, CensusBlockNoSpLookup}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.FWAMultiplier.implicits.MultiplierOps
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.GuardRail.implicits.GuardRailOps
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.GuardRailTransform.implicits.GuardRailTransformOps
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.{CensusBlock2010To2020Crosswalk, CensusBlockCrosswalk, FWAMultiplier, GuardRail, LegacyStateDimId}
import com.comlinkdata.largescale.schema.broadband_market_share.{BroadbandAggregatedChurn, BroadbandPlatformAggregate, BroadbandPlatformAggregateWithId, ElementIDToSuppress}
import com.comlinkdata.largescale.schema.udp.installbase.IfaWirelessCarrier
import com.comlinkdata.largescale.schema.udp.lookup.AllowedNetworkBrandPair
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.DCommonOcnLookup
import org.apache.spark.sql.{DataFrame, Dataset}
import org.apache.spark.sql.functions.{coalesce, lit, sum, when}
import org.scalacheck.Gen

import java.sql.Date
import java.time.LocalDate
import javax.xml.bind.DatatypeConverter

class PlatformAggregateSpec extends CldSparkBaseSpec {

  import spark.implicits._

  describe("platform aggregate") {

    val allowedNetworkBrandPairLookupData = Seq(
      AllowedNetworkBrandPair("Verizon Wireless", "Probable Verizon MVNO", "Verizon Wireless"),
      AllowedNetworkBrandPair("Verizon Wireless", "Spectrum Wireless", "Spectrum Mobile"),
      AllowedNetworkBrandPair("Verizon Wireless", null, "Verizon Wireless"),
      AllowedNetworkBrandPair("US Cellular", null, "U.S. Cellular"),
    )

    val dCommonOcnLookupData = Seq(
      DCommonOcnLookup(6105, "Spectrum Mobile", "W", 6105, "", "SMV"),
      DCommonOcnLookup(1, "Verizon Wireless", "W", 1, "", "VZW"),
      DCommonOcnLookup(7, "U.S. Cellular", "W", 7, "", "USC"),
    )

    val fwaMultiData = Seq(
      FWAMultiplier(6713, Date.valueOf("2021-01-01"), None, 2, 2)
    )

    val cb2020xwalk = censusBlocks.map(geoid10 => CensusBlock2010To2020Crosswalk(geoid10, geoid10, 1.0, 1.0))

    val StartDate = Date.valueOf("2022-01-01")

    it("should be created without throwing") {
      val churn = generate(4, genBroadbandAggregatedChurn(Gen.oneOf(Seq(StartDate))))
        .map(c => c.copy(winner_census_block_id = c.loser_census_block_id)) // simulate non-movers

      val csbNoSpLookup = spark.emptyDataset[CensusBlockNoSpLookup]
      val csbCrosswalkLookup = Fields("winner_census_block_id" -> "serv_terr_blockid")
        .link(churn, generate(4, genCensusBlockCrosswalk()))

      val stateDimLookup = Fields("state_fips" -> "state_fips")
        .link(csbCrosswalkLookup, generate(4, genLegacyStateDimId()))
        .toDS()

      val carrierAggRollup = generate(4, genCarrierAggRollup()).toDS

      val allowedNetworkBrandPairLookup = allowedNetworkBrandPairLookupData.toDS()

      val dCommonOcnLookup = dCommonOcnLookupData.toDS()

      val churnDs = Some(churn.toDS)

      val platformAgg = PlatformAggregate()
        .generate(
          churnDs,
          None,
          csbNoSpLookup,
          csbCrosswalkLookup.toDS(),
          csbCrosswalkLookup.toDS(),
          cb2020xwalk.toDS(),
          stateDimLookup,
          carrierAggRollup,
          allowedNetworkBrandPairLookup,
          spark.emptyDataset[IfaWirelessCarrier],
          dCommonOcnLookup,
          spark.emptyDataset[ElementIDToSuppress],
          spark.emptyDataset[CarrierSuppression],
          fwaMultiData.toDS(),
          spark.emptyDataset[GuardRail],
          30,
          StartDate.toLocalDate
        )
      assert(platformAgg.agg.count() >= 2)
    }

    it("eliminates intra-sp movements in bb churn") {

      val churn = Seq(
        ("A", "1", "2", "bb_churn"),
        ("B", "1", "1", "bb_churn")
      ).toDF("ifa", "loser_sp_platform", "winner_sp_platform", "event_type")

      val result = PlatformAggregate().eliminateIntraSpChurnFromBbChurn(churn).toDF()

      val expected = Seq(
        ("A", "1", "2", "bb_churn")
      ).toDF("ifa", "loser_sp_platform", "winner_sp_platform", "event_type")
      result.show()
      expected.show()
      assertDataFrameDataEquals(expected, result)
    }

    // No longer valid as transfers is not allowed as an event_type
    ignore("intra-sp movements allowed in case of transfers") {

      val churn = dataset[BroadbandAggregatedChurn] {
        """
          |household_id|ifa |loser_sp_platform|loser_census_block_id|winner_census_block_id|winner_sp_platform|distance|churn_date|year|month|day|
          |        YQ==|YQ==|                1|                   10|                    20|                 1|10000.00|2022-01-08|2022|   02| 20|
          |        YQ==|YQ==|                1|                   10|                    20|                 1|10000.00|2022-01-08|2022|   02| 20|"""
      }

      val carrierAggRollup = dataset[CarrierAggRollup] {
        """
          |child_sp_platform|parent_sp_platform|limit_to_census_blockid|
          |3                |2                 |-1                     |
          |1                |2                 |-1                     |"""
      }

      val stateDimLookup = generate(genLegacyStateDimId()).map(_.copy(state_fips = "AL", state_dim_id = 0)).toDS()
      val csbCrosswalkLookup = (generate(genCensusBlockCrosswalk()).map(_.copy(serv_terr_blockid = "10")) ++
        generate(genCensusBlockCrosswalk()).map(_.copy(serv_terr_blockid = "20")))
        .map(_.copy(zip5 = "0", dma = 0, cma = 0, state_fips = "AL", bta = "0", cbsa_fips_full = "0"))
        .toDS()
      val allowedNetworkBrandPairLookup = allowedNetworkBrandPairLookupData.toDS()
      val dCommonOcnLookup = dCommonOcnLookupData.toDS()

      val result = PlatformAggregate().generate(
        Some(churn),
        None,
        spark.emptyDataset[CensusBlockNoSpLookup],
        csbCrosswalkLookup,
        csbCrosswalkLookup,
        cb2020xwalk.toDS(),
        stateDimLookup,
        carrierAggRollup,
        allowedNetworkBrandPairLookup,
        spark.emptyDataset[IfaWirelessCarrier],
        dCommonOcnLookup,
        spark.emptyDataset[ElementIDToSuppress],
        spark.emptyDataset[CarrierSuppression],
        fwaMultiData.toDS(),
        spark.emptyDataset[GuardRail],
        30,
        StartDate.toLocalDate
      )

      val expected = Seq(
        BroadbandPlatformAggregate(
          the_date = Date.valueOf("2022-01-08"),
          census_blockid = "20",
          zip_cd = "0",
          state = Some(0),
          bta = "0",
          cbsa = "0",
          dma = Some(0),
          cma = Some(0),
          secondary_census_blockid = "10",
          secondary_sp_group = 2,
          primary_sp_group = 2,
          mover_ind = true,
          adjusted_wins = 1,
          adjusted_losses = 0,
          network = Some("Verizon Wireless"),
          brand = Some("Verizon Wireless"),
          wireless_sp_group = Some(1)),
        BroadbandPlatformAggregate(
          the_date = Date.valueOf("2022-01-08"),
          census_blockid = "10",
          zip_cd = "0",
          state = Some(0),
          bta = "0",
          cbsa = "0",
          dma = Some(0),
          cma = Some(0),
          secondary_census_blockid = "20",
          secondary_sp_group = 2,
          primary_sp_group = 2,
          mover_ind = true,
          adjusted_wins = 0,
          adjusted_losses = 1,
          network = Some("Verizon Wireless"),
          brand = Some("Verizon Wireless"),
          wireless_sp_group = Some(1))
      ).toDS

      assertDatasetEquals(expected.orderBy($"census_blockid"), result.agg.orderBy($"census_blockid"))
    }

    it("intra-sp movements are eliminated in case of bb churn") {

      val churn = dataset[BroadbandAggregatedChurn] {
        """
          |household_id|ifa |loser_sp_platform|loser_census_block_id|winner_census_block_id|winner_sp_platform|distance|churn_date|year|month|day|
          |        YQ==|YQ==|                1|                   10|                    20|                 2|10000.00|2022-01-08|2022|   02| 20|
          |        YQ==|YQ==|                1|                   10|                    20|                 1|10000.00|2022-01-08|2022|   02| 20|"""
      }

      val carrierAggRollup = dataset[CarrierAggRollup] {
        """
          |child_sp_platform|parent_sp_platform|limit_to_census_blockid|
          |3                |2                 |-1                     |
          |1                |2                 |-1                     |"""
      }

      val stateDimLookup = generate(genLegacyStateDimId()).map(_.copy(state_fips = "AL", state_dim_id = 0)).toDS()
      val csbNoSpLookup = spark.emptyDataset[CensusBlockNoSpLookup]
      val csbCrosswalkLookup = (generate(genCensusBlockCrosswalk()).map(_.copy(serv_terr_blockid = "10")) ++
        generate(genCensusBlockCrosswalk()).map(_.copy(serv_terr_blockid = "20")))
        .map(_.copy(zip5 = "0", dma = 0, cma = 0, state_fips = "AL", bta = "0", cbsa_fips_full = "0"))
        .toDS()
      val allowedNetworkBrandPairLookup = allowedNetworkBrandPairLookupData.toDS()
      val dCommonOcnLookup = dCommonOcnLookupData.toDS()

      val result = PlatformAggregate().generate(
        Some(churn),
        None,
        csbNoSpLookup,
        csbCrosswalkLookup,
        csbCrosswalkLookup,
        cb2020xwalk.toDS(),
        stateDimLookup,
        carrierAggRollup,
        allowedNetworkBrandPairLookup,
        spark.emptyDataset[IfaWirelessCarrier],
        dCommonOcnLookup,
        spark.emptyDataset[ElementIDToSuppress],
        spark.emptyDataset[CarrierSuppression],
        fwaMultiData.toDS(),
        spark.emptyDataset[GuardRail],
        30,
        StartDate.toLocalDate)

      val expected = spark.emptyDataset[BroadbandPlatformAggregate]

      assertDatasetEquals(expected, result.agg)
    }

    it("handles carrier suppression after carrier agg rollup"){
      val churn = generate(4, genBroadbandAggregatedChurn(Gen.oneOf(Seq(StartDate))))
        .map(c => c.copy(winner_sp_platform = 6715))

      val csbNoSpLookup = spark.emptyDataset[CensusBlockNoSpLookup]
      val csbCrosswalkLookup = Fields("winner_census_block_id" -> "serv_terr_blockid")
        .link(churn, generate(4, genCensusBlockCrosswalk()))

      val stateDimLookup = Fields("state_fips" -> "state_fips")
        .link(csbCrosswalkLookup, generate(4, genLegacyStateDimId()))
        .toDS()

      val carrierAggRollup = Seq(CarrierAggRollup(
          child_sp_platform = 6715,
          parent_sp_platform = 6730,
          limit_to_census_blockid = "-1")).toDS()

      val carrierSuppression = Seq(CarrierSuppression(
        sp_id = 6730,
        limit_to_census_blockid = "-1",
        start_date = Date.valueOf("1900-01-01"),
        end_date = Some(Date.valueOf("9999-01-01")),
      )).toDS()

      val platformAgg = PlatformAggregate()
        .generate(
          Some(churn.toDS()),
          None,
          csbNoSpLookup,
          csbCrosswalkLookup.toDS(),
          csbCrosswalkLookup.toDS(),
          cb2020xwalk.toDS(),
          stateDimLookup,
          carrierAggRollup,
          allowedNetworkBrandPairLookupData.toDS(),
          spark.emptyDataset[IfaWirelessCarrier],
          dCommonOcnLookupData.toDS(),
          spark.emptyDataset[ElementIDToSuppress],
          carrierSuppression,
          fwaMultiData.toDS(),
          spark.emptyDataset[GuardRail],
          30,
          StartDate.toLocalDate
        )
      platformAgg.agg.count shouldBe 0
    }
  }

  describe("transform addWirelessSPColumns") {
    val pairs = Seq(
      AllowedNetworkBrandPair("Verizon Wireless", "Ting Mobile", "Ting on VZW"),
      AllowedNetworkBrandPair("Verizon Wireless", "Xfinity Mobile", "Xfinity Mobile"),
      AllowedNetworkBrandPair("Verizon Wireless", "Probable Verizon MVNO", "Verizon Wireless"),
      AllowedNetworkBrandPair("Verizon Wireless", null, "Verizon Wireless"),
      AllowedNetworkBrandPair("US Cellular", null, "U.S. Cellular"),
    )

    val dCommonOcnLookup = Seq(
      DCommonOcnLookup(1, "Verizon Wireless", "W", 1, "", "VZW"),
      DCommonOcnLookup(7, "U.S. Cellular", "W", 7, "", "USC"),
      DCommonOcnLookup(6052, "Xfinity Mobile", "W", 6052, "", "XMV"),
      DCommonOcnLookup(6503, "Ting on VZW", "W", 6503, "", "OTH"),
    )

    def genActual(network: String, brand: String): DataFrame = {
      val churn = generate(1, genBroadbandAggregatedChurn())
      val ifaWirelessCarrier = Fields("ifa" -> "ifa")
        .link(churn, generate(genIfaWirelessCarrier()))
        .map(_.copy(
          network = network,
          brand = brand))
        .toDS()
        .as[IfaWirelessCarrier]

      val actual = churn.toDS()
        .transform(PlatformAggregate().addNetworkBrandColumns(ifaWirelessCarrier))
        .transform(PlatformAggregate().addWirelessSPColumns(pairs.toDS(), dCommonOcnLookup.toDS()))
        .select($"network", $"brand", $"wireless_sp_group")
      actual
    }

    it("handles no match") {
      val actual = genActual("~", "~")
      val expected = Seq(
        ("~", "~", null)
      ).toDF("network", "brand", "wireless_sp_group")
      assertDataFrameDataEquals(expected, actual)
    }

    it("handles basic 1:1 mappings") {
      val actual = genActual("Verizon Wireless", "Xfinity Mobile")
      val expected = Seq(
        ("Verizon Wireless", "Xfinity Mobile", 6052)
      ).toDF("network", "brand", "wireless_sp_group")

      assertDataFrameDataEquals(expected, actual)
    }

    it("handles overwrite brand") {
      val actual = genActual("Verizon Wireless", "Ting Mobile")
      val expected = Seq(
        ("Verizon Wireless", "Ting on VZW", 6503)
      ).toDF("network", "brand", "wireless_sp_group")

      assertDataFrameDataEquals(expected, actual)
    }

    it("handles network without brand (brand is null)") {
      val actual = genActual("Verizon Wireless", null)
      val expected = Seq(
        ("Verizon Wireless", "Verizon Wireless", 1)
      ).toDF("network", "brand", "wireless_sp_group")

      assertDataFrameDataEquals(expected, actual)
    }

    it("handles Probable brand") {
      val actual = genActual("Verizon Wireless", "Probable Verizon MVNO")
      val expected = Seq(
        ("Verizon Wireless", "Verizon Wireless", 1)
      ).toDF("network", "brand", "wireless_sp_group")

      assertDataFrameDataEquals(expected, actual)
    }

    it("handles US Cellular -> U.S. Cellular") {
      val actual = genActual("US Cellular", null)
      val expected = Seq(
        ("US Cellular", "U.S. Cellular", 7)
      ).toDF("network", "brand", "wireless_sp_group")

      assertDataFrameDataEquals(expected, actual)
    }

    it("handles all churn with empty network field") {
      val actual = genActual(null, null)

      val expected = Seq(
        ("x", "x", 1) // ignore this, workaround for null values with good schema
      ).toDF("network", "brand", "wireless_sp_group")
        .withColumn("network", lit(null))
        .withColumn("brand", lit(null))
        .withColumn("wireless_sp_group", lit(null))

      assertDataFrameDataEquals(expected, actual)
    }

    it("handles all churn with one empty and one populated network field") {

      val actual = genActual("US Cellular", null)

      val expected = Seq(
        ("US Cellular", "U.S. Cellular", 7)
      ).toDF("network", "brand", "wireless_sp_group")

      assertDataFrameDataEquals(expected, actual)
    }

    it("handles all churn with one empty and two populated network fields") {
      val churn = generate(1, genBroadbandAggregatedChurn())
      val ifaWirelessCarrierBase = churn.toDS()
        .select("ifa")
        .withColumn("brand", lit(null))
        .cache()
      val ifaWirelessCarrier = Seq(
        lit("US Cellular"),
        lit("US Cellular"), // dupe check
        lit(null),
        lit("Verizon Wireless")
      )
        .map(ifaWirelessCarrierBase.withColumn("network", _).as[IfaWirelessCarrier])
        .reduce(_ unionByName _)

      val actual = churn.toDS()
        .transform(PlatformAggregate().addNetworkBrandColumns(ifaWirelessCarrier))
        .transform(PlatformAggregate().addWirelessSPColumns(pairs.toDS(), dCommonOcnLookup.toDS()))
        .select($"network", $"brand", $"wireless_sp_group")

      val expected = Seq(
        ("US Cellular", "U.S. Cellular", 7)
      ).toDF("network", "brand", "wireless_sp_group")

      assertDataFrameDataEquals(expected, actual)
    }
  }

  describe("handles preagg") {
    it("reduces correctly") {
      val item: BroadbandPlatformAggregateWithId = generate(genBroadbandPlatformAggregateWithId()).head
      val data: Dataset[BroadbandPlatformAggregateWithId] = List.fill(10)(item).toDS()
      val result = data.transform(PlatformAggregate().preAggregate("adjusted_wins", "adjusted_losses"))
      result.count() shouldBe 1
    }
    it("handles deduplication deterministically and correctly") {

      val allowedNetworkBrandPairLookup = Seq(AllowedNetworkBrandPair("Verizon Wireless", "Verizon Wireless", "x")).toDS()
      val dCommonOcnLookup = Seq(DCommonOcnLookup(
        ocn_common_dim_id = 1,
        common_name = "x",
        common_name_mode = "x",
        id6 = 1,
        changed = "x",
        sp_reporting_nm = "x",
      )).toDS()

      val ifaWirelessCarrier = Seq(
        IfaWirelessCarrier(DatatypeConverter.parseHexBinary("947a"), "Verizon Wireless", "Verizon Wireless"),
        IfaWirelessCarrier(DatatypeConverter.parseHexBinary("1b8e"), "Verizon Wireless", "Verizon Wireless")
      ).toDS()

      val churnMovers = Seq(
        BroadbandAggregatedChurn(
          ifa = DatatypeConverter.parseHexBinary("947a"),
          loser_sp_platform = 6140,
          loser_census_block_id = "160550001002029",
          winner_sp_platform = 6710,
          winner_census_block_id = "530330081001004",
          household_id = DatatypeConverter.parseHexBinary("D10D"),
          distance = 417661.1692783153,
          churn_date = Date.valueOf("2023-04-28"),
          year = "2023",
          month = "04",
          day = "28"
        ),
        BroadbandAggregatedChurn(
          ifa = DatatypeConverter.parseHexBinary("1b8e"),
          loser_sp_platform = 6140,
          loser_census_block_id = "160550001002029",
          winner_sp_platform = 6710,
          winner_census_block_id = "160550001002040",
          household_id = DatatypeConverter.parseHexBinary("D10D"),
          distance = 8496.398079049704,
          churn_date = Date.valueOf("2023-04-28"),
          year = "2023",
          month = "04",
          day = "28"
        ))
        .toDS()
        .transform(PlatformAggregate().addNetworkBrandColumns(ifaWirelessCarrier))
        .transform(PlatformAggregate().movers)
        .withColumn("element_id", coalesce($"household_id", $"ifa"))
        .transform(PlatformAggregate().addWirelessSPColumns(allowedNetworkBrandPairLookup, dCommonOcnLookup))

      val carrierRollupLookup = spark.emptyDataset[CarrierAggRollup]

      val csbNoSpLookup = Seq(
        CensusBlockNoSpLookup(
          block_group = "160550001002",
          census_blockid = "160550001002040",
          zip = "83858",
          region_dim_id = 0,
          state_dim_id = 13,
          bta = "425",
          cbsa = "17660",
          dma = 881,
          cma = 388),
        CensusBlockNoSpLookup(
          block_group = "530330081001",
          census_blockid = "530330081001004",
          zip = "98101",
          region_dim_id = 0,
          state_dim_id = 47,
          bta = "413",
          cbsa = "42660",
          dma = 819,
          cma = 20)
      ).toDS()

      val csbCrosswalkLookup = spark.emptyDataset[CensusBlockCrosswalk]
      val stateDimIdLookup = spark.emptyDataset[LegacyStateDimId]


      val preparePlatformPreAgg = PlatformAggregate().prepareAgg(
        churnMovers,
        csbNoSpLookup,
        csbCrosswalkLookup,
        stateDimIdLookup,
        carrierRollupLookup,
        allowedNetworkBrandPairLookup,
        dCommonOcnLookup
      ) _

      val losingSide = preparePlatformPreAgg(
        "loser_sp_platform", "winner_sp_platform", "loser_census_block_id", "winner_census_block_id", "adjusted_losses", "adjusted_wins"
      ).cache()
      val winningSide = preparePlatformPreAgg(
        "winner_sp_platform", "loser_sp_platform", "winner_census_block_id", "loser_census_block_id", "adjusted_wins", "adjusted_losses"
      ).cache()

      val df1 = losingSide.select($"census_blockid" as "cb1", $"secondary_census_blockid" as "cb2")
      val df2 = winningSide.select($"secondary_census_blockid" as "cb1", $"census_blockid" as "cb2")
      assertDataFrameDataEquals(df1, df2)
    }
  }

  describe("multipliers") {

    it("handles non fixed wireless") {
      val fwaMultiData = Seq(FWAMultiplier(3, Date.valueOf("2021-01-01"), Some(Date.valueOf("9999-01-01")), 3, 2)).toDS()
      val item: BroadbandPlatformAggregate = generate(genBroadbandPlatformAggregate()).head.copy(the_date = Date.valueOf("2023-01-01"))

      val items = Seq(
        item.copy(primary_sp_group = 1, secondary_sp_group = 2, adjusted_wins = 1, adjusted_losses = 0),
        item.copy(primary_sp_group = 2, secondary_sp_group = 1, adjusted_wins = 0, adjusted_losses = 1)
      )

      val out = items.toDS()
        .transform(fwaMultiData.multiply($"primary_sp_group", $"secondary_sp_group", $"the_date", $"adjusted_wins", $"adjusted_losses"))
        .select($"primary_sp_group",
          $"secondary_sp_group",
          $"adjusted_wins",
          $"adjusted_losses")
        .orderBy($"primary_sp_group", $"secondary_sp_group", $"adjusted_wins", $"adjusted_losses")

      val expected = Seq(
        (1, 2, 1, 0),
        (2, 1, 0, 1),
      ).toDF("primary_sp_group", "secondary_sp_group", "adjusted_wins", "adjusted_losses")

      assertDataFrameDataEquals(expected, out)
    }

    it("handles end date") {
      val fwaMultiData = Seq(FWAMultiplier(1, Date.valueOf("2021-01-01"), Some(Date.valueOf("2022-01-01")), 3, 2)).toDS()
      val item: BroadbandPlatformAggregate = generate(genBroadbandPlatformAggregate()).head.copy(the_date = Date.valueOf("2023-01-01"))
      val items = Seq(
        item.copy(primary_sp_group = 1, secondary_sp_group = 2, adjusted_wins = 1, adjusted_losses = 0),
        item.copy(primary_sp_group = 2, secondary_sp_group = 1, adjusted_wins = 0, adjusted_losses = 1)
      )
      val out = items.toDS()
        .transform(fwaMultiData.multiply($"primary_sp_group", $"secondary_sp_group", $"the_date", $"adjusted_wins", $"adjusted_losses"))
        .select($"primary_sp_group",
          $"secondary_sp_group",
          $"adjusted_wins",
          $"adjusted_losses")
        .orderBy($"primary_sp_group", $"secondary_sp_group", $"adjusted_wins", $"adjusted_losses")

      val expected = Seq(
        (1, 2, 1, 0),
        (2, 1, 0, 1)
      ).toDF("primary_sp_group", "secondary_sp_group", "adjusted_wins", "adjusted_losses")

      assertDataFrameDataEquals(expected, out)
    }

    it("handles all 4 cases ") {
      val fwaMultiData = Seq(FWAMultiplier(1, Date.valueOf("2021-01-01"), Some(Date.valueOf("9999-01-01")), 3, 2)).toDS()
      val item: BroadbandPlatformAggregate = generate(genBroadbandPlatformAggregate(Gen.oneOf(Seq(Date.valueOf("2022-01-01"))))).head
      val items = Seq(
        item.copy(primary_sp_group = 1, secondary_sp_group = 2, adjusted_wins = 1, adjusted_losses = 0), // scale wins to 2
        item.copy(primary_sp_group = 2, secondary_sp_group = 1, adjusted_wins = 0, adjusted_losses = 1), // scale losses to 2 (tmo loss)
        item.copy(primary_sp_group = 1, secondary_sp_group = 3, adjusted_wins = 0, adjusted_losses = 1), // scale losses to 3 (tmo win)
        item.copy(primary_sp_group = 3, secondary_sp_group = 1, adjusted_wins = 1, adjusted_losses = 0) // scale wins to 3
      )

      val out = items.toDS()
        .transform(fwaMultiData.multiply($"primary_sp_group", $"secondary_sp_group", $"the_date", $"adjusted_wins", $"adjusted_losses"))
        .select($"primary_sp_group",
          $"secondary_sp_group",
          $"adjusted_wins",
          $"adjusted_losses")
        .orderBy($"primary_sp_group", $"secondary_sp_group", $"adjusted_wins", $"adjusted_losses")

      val expected = Seq(
        (1, 2, 2, 0),
        (1, 3, 0, 3),
        (2, 1, 0, 2),
        (3, 1, 3, 0),
      ).toDF("primary_sp_group", "secondary_sp_group", "adjusted_wins", "adjusted_losses")

      assertDataFrameDataEquals(expected, out)
    }
  }

  describe("distance rule"){
    it("handles non movers by swapping"){
      val churn = generate(genBroadbandAggregatedChurn()).map(_.copy(
        loser_census_block_id = "loser",
        winner_census_block_id = "winner",
        distance = 500.0
      )).toDS()
      val df = churn.transform(PlatformAggregate().distanceRule(10000))
      df.select($"loser_census_block_id").collect()(0).getString(0) shouldBe "winner"
    }
    it("handles movers by not swapping"){
      val churn = generate(genBroadbandAggregatedChurn()).map(_.copy(
        loser_census_block_id = "loser",
        winner_census_block_id = "winner",
        distance = 10500.0
      )).toDS()
      val df = churn.transform(PlatformAggregate().distanceRule(10000))
      df.select($"loser_census_block_id").collect()(0).getString(0) shouldBe "loser"
    }
  }

  describe("guard rails"){
    it("can scale to cover very low volumes"){
      val guardRailLookBack = 30
      val churnStartDate = LocalDate.of(2023, 1, 1)
      val guardRailStartDate = churnStartDate.minusDays(guardRailLookBack)

      val fwa_churn = (guardRailStartDate to churnStartDate).flatMap { day =>
        generate(day.getDayOfMonth, genBroadbandPlatformAggregateWithId(dates = Gen.oneOf(Seq(Date.valueOf(day)))))
          .map(c => c.copy(primary_sp_group = 6730, secondary_sp_group = 1))
      }.toSeq.toDS()

      val bb_churn = (guardRailStartDate to churnStartDate).flatMap { day =>
        generate(99, genBroadbandPlatformAggregateWithId(dates = Gen.oneOf(Seq(Date.valueOf(day)))))
          .map(c => c.copy(primary_sp_group = 3, secondary_sp_group = 4))
      }.toSeq.toDS()

      val all_churn = fwa_churn
        .unionByName(bb_churn)

      val guardRail = GuardRail(
        sp_id = 6730,
        w_or_l = "w",
        lower_bound = .10f,
        upper_bound = .20f,
        min_date = Date.valueOf(churnStartDate.minusDays(1)),
        max_date = Some(Date.valueOf(churnStartDate.plusDays(10))))

      val bounds = all_churn
        .as[BroadbandPlatformAggregateWithId]
        .transform(Seq(guardRail).toDS().createGuardRail(guardRailLookBack))
        .cache()

      val preAgg = all_churn
        .filter($"the_date" >= churnStartDate) // do not restate dates needed for guard rail lookback
        .transform(bounds.applyGuardRail)
        .cache()

      preAgg
        .filter($"primary_sp_group" === 6730)
        .select($"adjusted_wins").collect()(0).get(0) shouldBe 11

      val result: Double = preAgg
        .groupBy()
        .agg(
          sum($"adjusted_wins") as "total",
          sum(when($"primary_sp_group" === 6730, $"adjusted_wins")) as "fwa_total")
        .select($"fwa_total" / $"total" as "win_share")
        .collect()(0)
        .getDouble(0)

      result.toFloat shouldBe >= (guardRail.lower_bound)
      
    }
  }
}
