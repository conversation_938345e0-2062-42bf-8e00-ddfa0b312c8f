package com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.DistanceMatrix.{Dest, DestRef}
import com.comlinkdata.emrjobs.spark.marketshare.spatial_allocation.schema.{Allocation, Target}


class DistanceMatrixSpec extends CldSparkBaseSpec {
  val otherSubmarketPenalty = 1.25
  val otherTractPenalty = 1.25

  describe("initialization") {
    it("no penalties") {
      val households = data[Allocation] {
        """
          |household_id|county|census_block_id|target_census_block_id|sp_platform|submarket|latitude|longitude|distance|weight_factor|weight_allocated|weight_allocated_cumulative
          |        SDE=| 13075|130759601001063|                  null|          0|        S|     0.0|      5.0|       0|          5.0|               0|                        3.5
          |        SDI=| 13075|130759601001063|                  null|          0|        S|     0.0|      5.0|       0|          5.0|               0|                        2.5
          |        SDM=| 13075|130759601001055|                  null|          0|        S|    10.0|      0.0|       0|          5.0|               0|                          0
          """.stripMargin
      }

      val targets = data[Target] {
        """
          |target_census_block_id|county|capacity|target_latitude|target_longitude|target_submarket
          |       130759601004014| 13075|     4.5|              5|               5|               S
          |       130759601003026| 13075|     4.5|             10|               0|               S
          """.stripMargin
      }

      val distance = Distance(Distance.euclid, otherSubmarketPenalty, otherTractPenalty)
      val matrix = DistanceMatrix(households, targets, distance)

      assert(matrix.matrix.keys === Set("130759601001055", "130759601001063"))
      val res1 = matrix.matrix("130759601001055")
      val res2 = matrix.matrix("130759601001063")

      assert(0 === res1.startIndex)
      assert(0 === res2.startIndex)
      assert(Array(1, 0) sameElements res1.destReference)
      assert(Array(0, 1) sameElements res2.destReference)
    }
  }

  describe("reading from destination matrix") {
    it("gets closest target") {
      val dests = Array(
        Dest("130759601004014", "S", 5.0, 5.0, 4.5),
        Dest("130759601003026", "S", 10.0, 0, 4.5)
      )

      val dr1 = DestRef(0, Array(1, 0))
      val dr2 = DestRef(0, Array(0, 1))
      val matrix = new DistanceMatrix(Map("130759601001055" -> dr1, "130759601001063" -> dr2), dests)

      assert("130759601004014" === matrix.get("130759601001063").get.csb)
      assert("130759601003026" === matrix.get("130759601001055").get.csb)
    }

    it("doesnt throw on non-existing target") {
      val distance = Distance(Distance.euclid, otherSubmarketPenalty, otherTractPenalty)
      val matrix = DistanceMatrix(Seq.empty, Seq.empty, distance)
      assert(None === matrix.get("non-existing"))
    }
  }

  describe("modifying") {
    it("out-of-capacity target causes incrementing of startIndex") {
      val dests = Array(
        Dest("130759601004014", "S", 5.0, 5.0, 4.5),
        Dest("130759601003026", "S", 10.0, 0, 4.5)
      )

      val dr1 = DestRef(0, Array(1, 0))
      val dr2 = DestRef(0, Array(0, 1))
      val matrix = new DistanceMatrix(Map("130759601001055" -> dr1, "130759601001063" -> dr2), dests)

      matrix.update("130759601001055", 4.5)

      assert(1 === matrix.matrix("130759601001055").startIndex)
      assert(0 === matrix.matrix("130759601001063").startIndex)
    }

    it("get removes out-of-capacity target and returns next") {
      val dest1 = Dest("130759601001063", "S", 3.0, 15.0, 0)
      val dest2 = Dest("130759601001055", "S", 5.0, 5.0, 0)
      val dest3 = Dest("130759601004014", "S", 10.0, 0, 4.5)

      val dr = DestRef(0, Array(0, 1, 2))
      val matrix = new DistanceMatrix(Map("130759601001063" -> dr), Array(dest1, dest2, dest3))

      assert(matrix.get("130759601001063") === Some(dest3))
      assert(2 === matrix.matrix("130759601001063").startIndex)
    }

    it("doesnt throw on non-existing key update") {
      val distance = Distance(Distance.euclid, otherSubmarketPenalty, otherTractPenalty)
      val matrix = DistanceMatrix(Seq.empty, Seq.empty, distance)
      assert(false === matrix.update("non-existing", 55))
    }

    it("has no target when only out-of-capacity target exists") {
      val dest1 = Dest("130759601001063", "S", 3.0, 15.0, 0)

      val dr = DestRef(0, Array(0))
      val matrix = new DistanceMatrix(Map("130759601001063" -> dr), Array(dest1))

      assert(matrix.hasTarget("130759601001063") === false)
    }
  }
}
