package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.ApplyOuiExclusionRules

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BlockXLoopCounts.MappedBlocks
import org.apache.spark.sql.functions.substring
import org.apache.spark.sql.{Dataset, SparkSession}


/**
  * mapped blocks coinciding with appropriate filters that map to the OUI table blocks
  * @param BLOCK_6 the block-6 of the associated bssid
  * @param VENDOR name of the vendor associated with the block 6
  * @param DESCRIPTION short description associated with the block_6/vendor (may be empty)
  */
case class OuiMappedBlocksForAnalysis(
  BLOCK_6: String,
  VENDOR: String,
  DESCRIPTION: String
)


object OuiMappedBlocksForAnalysis{

  /**
    * Extract the distinct Block_6's which are associated with the Verizon\\_ ssid pattern. Map those blocks to the
    * relevant information from the OUI Lookup Table.
    * @param mappingBssid the MappingBssid table to be processed
    * @param ouiLookupTable the Oui lookup table to be used for block-related analysis
    * @param spark
    * @return
    *
    * @todo -- currently designed with the expectation that only verizon c-band is to be handled by this method
    *       if approval for a DataOps maintained lookup table of SSID_Pattern/BLOCK_6 pairs for exclusion is maintained,
    *       then this step is not necessary. But it is outside the scope of engineering to perform this analysis ourselves
    *       each processing quarter
    */
  def getBlocksToAnalyze(mappingBssid: Dataset[MappedBlocks], ouiLookupTable: Dataset[OuiLookupTable])(implicit spark: SparkSession): Dataset[OuiMappedBlocksForAnalysis] = {
    import spark.implicits._
    mappingBssid
      .where($"SSID_PATTERN"==="Verizon_")
      .select(substring($"BLOCK_X",0,8).as("BLOCK_6"))
      .distinct()
      .join(ouiLookupTable, $"BLOCK_6"===$"BLOCK", "left")
      .select($"BLOCK_6", $"VENDOR", $"DESCRIPTION")
      .as[OuiMappedBlocksForAnalysis]
  }


  /**
    * Filter the given blocks to report which ones should be excluded going forward. We want the final outputs of the
    * pipeline to include those which either have a Null/blank description, or are associated with askey.
    * @param blocksForAnalysis
    * @param spark
    * @return
    */
  def getBlocksToExclude(blocksForAnalysis: Dataset[OuiMappedBlocksForAnalysis])(implicit spark: SparkSession): Dataset[OuiMappedBlocksForAnalysis] = {
    import spark.implicits._
    blocksForAnalysis
      .where(
        !(
          ($"VENDOR".isNull || $"VENDOR"==="")
          || ($"DESCRIPTION".isNull || $"DESCRIPTION"==="")
          || $"VENDOR".like("%askey%")
          || $"DESCRIPTION".like("%askey%")
        )
      )
  }
}
