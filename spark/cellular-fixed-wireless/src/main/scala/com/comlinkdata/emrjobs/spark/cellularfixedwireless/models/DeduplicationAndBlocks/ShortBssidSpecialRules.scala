package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks

import org.apache.spark.sql.{Dataset, SparkSession}

import java.net.URI


/**
  * ShortBssidSpecialRules -- Internal Reference Table used for determining non-default short bssid substring rules
  * @param modem_model model of the model onto which these rules should apply
  * @param pos the starting position of the BSSID where the short BSSID should be extracted from
  * @param len the length of the substring needed for the short bssid
  */
case class ShortBssidSpecialRules(
  modem_model: String,
  pos: Int,
  len: Int
)



object ShortBssidSpecialRules{

  /**
    * Load the special modem-specific rules for short bssid from the given S3 URI. CSV file expected
    * @param path S3 uri for the special rules lookup table. CSV file expected
    * @param spark
    */
  def loadRules(path: URI)(implicit spark: SparkSession): Dataset[ShortBssidSpecialRules] = {
    import spark.implicits._
    spark.read.option("header", "true")
      .csv(path.toString)
      .select(
        $"modem_model",
        $"pos".cast("Integer").as("pos"),
        $"len".cast("Integer").as("len")
      )
      .as[ShortBssidSpecialRules]
  }
}
