package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.FixedWirelessSubsOutput
import org.apache.spark.sql.{Dataset, SparkSession}
import org.apache.spark.sql.functions.{count, lit}

import java.time.LocalDate


/**
  * SubsOutput -- Output Dataset
  * @param block census block ID for the corresponding modem
  * @param date quarter end date corresponding to the modem observation
  * @param carrier the identified carrier of the modem
  * @param num_networks the aggregate number of networks per block per carrier
  */
case class SubsOutput(
  date: java.sql.Date,
  block: String,
  carrier: String,
  num_networks: java.lang.Long
)

object SubsOutput{
  /**
    * Used for preparing the desired output with aggregate subscriber information
    * @param blockJoinedBssid block Joined BSSID to be used for aggregating into the desired subs output
    * @param dateValue static value to be set in the date field for this processing quarter
    * @param spark
    */
  def createFromBlockJoined(blockJoinedBssid: Dataset[BlockJoinedBssid], dateValue: LocalDate)(implicit spark: SparkSession): Dataset[SubsOutput] = {
    import spark.implicits._
    blockJoinedBssid
      .groupBy($"block", $"BSSID_Carrier")
      .agg(count("*").as("num_networks"))
      .select(
        lit(java.sql.Date.valueOf(dateValue)).as("date"),
        $"block",
        $"BSSID_Carrier".as("carrier"),
        $"num_networks"
      )
      .as[SubsOutput]
  }

  /**
    * Converts object type into the same present in the commons package to allow for proper resource sharing
    * @param so SubsOutput dataset to be converted
    * @param spark
    */
  def convertToFixedWirelessSubsOutput(so: Dataset[SubsOutput])(implicit spark: SparkSession): Dataset[FixedWirelessSubsOutput] = {
    import spark.implicits._
    so.select(FixedWirelessSubsOutput.cols: _*).as[FixedWirelessSubsOutput]
  }
}

