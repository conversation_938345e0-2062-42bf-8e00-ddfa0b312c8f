package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks

import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.{ManufacturerLookup, ModemInfoLookup}
import org.apache.spark.sql.functions.{lower, regexp_replace, substring, when}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

import java.sql.Timestamp


/**
  * businessOutput - output dataset
  * @param serv_terr_blockid block for the identified record
  * @param BSSID_Carrier carrier for the identified record
  * @param latitude latitude for the identified record
  * @param longitude longitude for the identified record
  * @param classification business/residential/unknown/both classification for the identified record
  * @param short_bssid short bssid for the identified record
  * @param modem_vendor vendor for the identified record
  * @param modem_model model for the identified record
  * @param first_observed_date first observed date for the identified record
  */
case class BusinessOutput(
  serv_terr_blockid: String,
  BSSID_Carrier: String,
  latitude: Double,
  longitude: Double,
  classification: String,
  short_bssid: String,
  modem_vendor: String,
  modem_model: String,
  first_observed_date: Timestamp
)

object BusinessOutput{

  /**
    * Used for preparing the desired output with post-clustering wireless modem/business related data
    * @param blockJoinedBssid block joined BSSID to be used for adapting into the desired business output schema
    * @param spark
    * @return
    */
  def createFromBlockJoinedBssid(blockJoinedBssid: Dataset[BlockJoinedBssid], manufacturerInfo: Dataset[ManufacturerLookup],
    modemInfo: Dataset[ModemInfoLookup])(implicit spark: SparkSession): Dataset[BusinessOutput] = {
    import spark.implicits._

    val vznResidential = modemInfo.filter(
      $"Carrier" === "Verizon" && $"Classification" === "Residential"
    ).select("ModemIdentifier").rdd.map(r => r(0)).collect()

    val vznBusiness = modemInfo.filter(
      $"Carrier" === "Verizon" && $"Classification" === "Business"
    ).select("ModemIdentifier").rdd.map(r => r(0)).collect()

    blockJoinedBssid.join(manufacturerInfo, lower(substring(regexp_replace($"short_bssid", ":", ""), 0, 6)) === lower($"MacPrefix"), "left")
      .withColumn("classification",
        when($"BSSID_Carrier" === "AT&T" && $"Manufacturer" === "ASKEY COMPUTER CORP", "Business")
        .when($"BSSID_Carrier" === "AT&T" && $"Manufacturer" =!= "ASKEY COMPUTER CORP", "Residential")
        .when($"BSSID_Carrier" === "Verizon" && $"modem_model".isInCollection(vznResidential), "Residential")
        .when($"BSSID_Carrier" === "Verizon" && $"modem_model".isInCollection(vznBusiness), "Business")
          .when($"BSSID_Carrier" === "T-Mobile" && $"Manufacturer" === "Arcadyan Corporation", "Business")
          .when($"BSSID_Carrier" === "T-Mobile" && $"Manufacturer" === "Sagemcom Broadband SAS", "Residential & Business")
          .when($"BSSID_Carrier" === "T-Mobile" && $"Manufacturer" === "Nokia Solutions and Networks GmbH & Co. KG", "Residential & Business")
          .when($"BSSID_Carrier" === "T-Mobile" && $"Manufacturer" === "SERCOMM PHILIPPINES INC", "Residential & Business")
          .when($"BSSID_Carrier" === "T-Mobile" && $"Manufacturer" === "Sercomm Corporation.", "Residential & Business")
          .otherwise("Unknown")
      ).select(
        $"block".as("serv_terr_blockid"),
        $"BSSID_Carrier",
        $"centroid_lat".as("latitude"),
        $"centroid_lon".as("longitude"),
        $"classification",
        $"short_bssid",
        $"modem_vendor",
        $"modem_model",
        $"first_observed_date"
      )
      .as[BusinessOutput]
  }
}

