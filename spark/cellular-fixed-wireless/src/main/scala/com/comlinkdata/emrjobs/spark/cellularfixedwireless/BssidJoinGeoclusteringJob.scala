package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.CellularFixedWirelessUtils.{dynamicOverwrite, generateYearMonthDayPath, getLastWeekdayDate}
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.model.LuDeviceFilterMapping
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering._
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner}
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.{BssidExclusionsLookup, ManufacturerLookup, ModemInfoLookup}
import com.typesafe.scalalogging.LazyLogging
import org.apache.sedona.core.serde.SedonaKryoRegistrator
import org.apache.sedona.sql.utils.SedonaSQLRegistrator
import org.apache.spark.serializer.KryoSerializer
import org.apache.spark.sql.SparkSession

import java.net.URI
import java.time.{Duration, LocalDate}


/**
  * @param processEndDateInclusive inclusive end date to be processed
  * @param numDaysToProcess the size of the lookback window for processing
  * @param targetCarriers string list of carriers to be included in the processing
  * @param wifiDataLocation URI path to the wifi scans data for fixed wireless
  * @param fwFaBlockMappingLocation URI path to the output tables for PersistDataToFWFABlockMappingJob
  * @param luDeviceFilterMappingLocation URI path for the LU_DEVICE_FILTER_MAPPING lookup table (csv file expected)
  * @param onesourceScansLocation URI path for the onesource wifiscans
  * @param inclusionsPath URI path for the location to output the inclusion data
  * @param bssidExclusionsLocation URI path for the location of the bssid exclusions lookup table
  * @param outputLocation URI path (no partitions) for the desired output directory of the table
  */
case class BssidJoinGeoclusteringJobConfig(
  processEndDateInclusive: Option[LocalDate],
  numDaysToProcess: Option[Int],
  targetCarriers: List[String],
  fwFaBlockMappingLocation: URI,
  luDeviceFilterMappingLocation: URI,
  onesourceScansLocation: URI,
  inclusionsPath: URI,
  bssidExclusionsLocation: URI,
  manufacturerLookupLocation: URI,
  modemLookupLocation: URI,
  mappedBlocksLocation: URI,
  outputLocation: URI
)


object BssidJoinGeoclusteringJob extends SparkJob(BssidJoinGeoclusteringJobRunner){
  /**
    * Register sedona before calling run.  This cannot happen in "configure spark" because it only
    * provides a SparkBuilder at that point
    */
  override def run(config: BssidJoinGeoclusteringJobConfig): Duration = {
    SedonaSQLRegistrator.registerAll(spark)
    super.run(config)
  }

  override def configureSpark(builder: SparkSession.Builder): SparkSession.Builder = {
    builder
      .config("spark.kryo.registrator", classOf[SedonaKryoRegistrator].getName)
      .config("spark.serializer", classOf[KryoSerializer].getName)
  }
}



object BssidJoinGeoclusteringJobRunner extends SparkJobRunner[BssidJoinGeoclusteringJobConfig] with LazyLogging{

  def runJob(config: BssidJoinGeoclusteringJobConfig)(implicit spark: SparkSession) = {

    // 1 -- Dynamic Config Computation
    logger.info("Computing Dynamic Parameters from Config")
    val processEndDateInclusive = config.processEndDateInclusive.getOrElse(getLastWeekdayDate("SATURDAY"))
    val lookBackDays = config.numDaysToProcess.getOrElse(90)-1 // -1 because range is inclusive of end date
    val inputDateRange = LocalDateRange.of(processEndDateInclusive.minusDays(lookBackDays), processEndDateInclusive)

    // 2 -- Dynamic URI Path Computation
    val onesourceDataPaths = CellularFixedWirelessUtils.generateScanDataPaths(config.onesourceScansLocation, inputDateRange)
    val fwFaBlockMappingPath = generateYearMonthDayPath(config.fwFaBlockMappingLocation, processEndDateInclusive)
    val outputPath = generateYearMonthDayPath(config.outputLocation, processEndDateInclusive)
    val inclusionOutputPath = generateYearMonthDayPath(config.inclusionsPath, processEndDateInclusive)


    // 3 -- Prepare CldFileUtils for input Validation (done within the load/create methods)
    implicit val cldFileUtils: CldFileUtils = CldFileUtils.newBuilder
      .forUri(
        config.fwFaBlockMappingLocation,
        config.luDeviceFilterMappingLocation,
        config.outputLocation,
        config.onesourceScansLocation,
        config.inclusionsPath,
        config.bssidExclusionsLocation,
        config.manufacturerLookupLocation,
        config.modemLookupLocation,
        config.mappedBlocksLocation
      )(spark).build

    // 4 -- Read Input Data
    logger.info(s"Loading Onesource Wifi Scans from Path: ${config.onesourceScansLocation}")
    val onesourceScans = OnesourceData.loadData(onesourceDataPaths, inputDateRange).cache()
    logger.info(s"Loading Manufacturer Info from path ${config.manufacturerLookupLocation}")
    val manufacturerInfo = ManufacturerLookup.read(config.manufacturerLookupLocation)
    logger.info(s"Creating Mapping_BSSID table from sources:\n\t${fwFaBlockMappingPath}\n\t${config.luDeviceFilterMappingLocation}")
    val mappingBssid = MappingBssid.createFromDataPaths(URI create fwFaBlockMappingPath, config.luDeviceFilterMappingLocation, config.targetCarriers).cache()
    logger.info(s"Loading LuDeviceFilterMapping from path: ${config.luDeviceFilterMappingLocation}")

    logger.info(s"Loading Lookup: ${config.bssidExclusionsLocation}")
    val bssidExclusions = BssidExclusionsLookup.read(config.bssidExclusionsLocation).cache()
    logger.info(s"Loading Modem Info from path ${config.modemLookupLocation}")
    val modemInfo = ModemInfoLookup.read(config.modemLookupLocation)
    logger.info(s"Loading Mapped Blocks from Path: ${config.mappedBlocksLocation}")
    val carrierMappedBlocks = spark.read.option("header", "true").csv(config.mappedBlocksLocation.toString).cache()

    val vznCustomAdapter = new VznAdapter(modemInfo)

    // 5 -- Processing Scans Data
    logger.info(s"Processing Ingested Data Scans for Carriers: ${config.targetCarriers}")
    val ingestedDataScans = IngestedData.createFromScans(onesourceScans, mappingBssid, config.targetCarriers).cache()
    logger.info("Processing Ingested Data scans for Verizon")
    val vznIngestedDataScans = IngestedData.createIngestedInclusions(onesourceScans, vznCustomAdapter, bssidExclusions).cache()
    logger.info("Processing Ingested Data Scans for T-Mobile and US Cellular")
    val lookupScans = IngestedData.createFromLookup(onesourceScans, carrierMappedBlocks, config.luDeviceFilterMappingLocation).cache()

    val ingestedUnion = ingestedDataScans.union(vznIngestedDataScans).union(lookupScans).cache()

    val attCustomAdapter = new AttAdapter(manufacturerInfo, modemInfo)
    logger.info("Creating Inclusion Dataset for AT&T")
    val inclusionScans = CustomInclusionData.createInclusionDataset(onesourceScans, attCustomAdapter).cache()

    logger.info(s"Writing the custom inclusion dataset to S3 path: ${inclusionOutputPath}")
    dynamicOverwrite(inclusionScans, inclusionOutputPath)

    logger.info("Processing Clustered Data Scans")
    val clusteredDataScans = ClusteredData.createFromScans(ingestedUnion).cache()
    logger.info("Processing Agg Data Scans")
    val aggDataScans = AggData.createFromScans(clusteredDataScans).cache()
    logger.info("Processing Cluster Filtering Scans")
    val clusterFilteringScans = ClusterFiltering.createFromScans(aggDataScans).cache()
    logger.info("Processing Filtered Data Scans for Output")
    val filteredDataScans = FilteredData.createFromScans(aggDataScans, clusterFilteringScans).cache()

    // 6 -- Write Output (filteredDataScans)
    dynamicOverwrite(filteredDataScans, outputPath)
  }
}
