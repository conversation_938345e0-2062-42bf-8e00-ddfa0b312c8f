package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks

import com.comlinkdata.largescale.schema.cellular_fixed_wireless.FixedWirelessServiceTerritoryOutput
import org.apache.spark.sql.functions.when
import org.apache.spark.sql.{Dataset, SparkSession}


/**
  * ExtrapolatedBlocks -- Intermediary Dataset containing the modems extrapolated into every census block their signal
  * will reach into
  * @param serv_terr_blockid the ID of the census block (service territory) associated with the modem
  * @param BSSID_Carrier the carrier of the modem scanned
  * @param modem_frequency the signal frequency of the modem
  */
case class ExtrapolatedBlocks(
  serv_terr_blockid: String,
  BSSID_Carrier: String,
  modem_frequency: String
)


object ExtrapolatedBlocks {

  /**
    * Report every distinct block, carrier, and modem frequency from the input BlockJoinedBssid dataset with appropriate
    * schema renaming for outputs.
    * modem
    * @param blockJoined intermediary block-joined dataset containing the modems to be extrapolated
    * @param spark
    * @return
    */
  def getExtrapolatedBlocks(blockJoined: Dataset[BlockJoinedBssid])(implicit spark: SparkSession): Dataset[ExtrapolatedBlocks] = {
    import spark.implicits._
    blockJoined
      .select(
        $"block".as("serv_terr_blockid"),
        $"BSSID_Carrier",
        when($"BSSID_Carrier"==="Verizon" && $"modem_model"==="LV55", "mmWave")
          .when($"BSSID_Carrier"==="US Cellular" && $"modem_model"==="FG2000", "mmWave")
          .otherwise("Sub6")
          .as("modem_frequency")
      )
      .distinct()
      .as[ExtrapolatedBlocks]
  }


  /**
    * Converts object type into the same present in the commons package to allow for proper resource sharing
    * @param eb ExtrapolatedBlocks dataset to be converted
    * @param spark
    * @return
    */
  def convertToFixedWirelessServiceTerritoryOutput(eb: Dataset[ExtrapolatedBlocks])(implicit spark: SparkSession): Dataset[FixedWirelessServiceTerritoryOutput] = {
    import spark.implicits._
    eb.select(FixedWirelessServiceTerritoryOutput.cols: _*).as[FixedWirelessServiceTerritoryOutput]
  }
}
