package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.largescale.commons.{LocalDateRange, RedshiftUtils, SparkJob, SparkJobRunner}
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.FixedWirelessSubsOutput
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.SparkSession

import java.net.URI


case class CellularFixedWirelessRedshiftUploadJobConfig(
  uploadDateRange: Option[LocalDateRange],
  subsOutputLocation: URI,
  redshiftUploadTableName: String,
  redshiftJdbcEndpoint: String,
  redshiftUserName: String,
  redshiftParamStoreKey: String,
  redshiftTempLocation: URI,
)


object CellularFixedWirelessRedshiftUploadJob extends SparkJob(CellularFixedWirelessRedshiftUploadJobRunner)


object CellularFixedWirelessRedshiftUploadJobRunner extends SparkJobRunner[CellularFixedWirelessRedshiftUploadJobConfig] with LazyLogging {

  def runJob(config: CellularFixedWirelessRedshiftUploadJobConfig)(implicit spark: SparkSession) = {
    import spark.implicits._

    // Prepare the Redshift Config Object
    implicit val rsConfig: RedshiftUtils.RedshiftConfig = RedshiftUtils.RedshiftConfig(
      rsTemporaryLocation = config.redshiftTempLocation,
      rsJdbcEndpoint = config.redshiftJdbcEndpoint,
      rsUserName = config.redshiftUserName,
      rsParameterStoreKey = config.redshiftParamStoreKey
    )

    // Date Range to Upload to Redshift
    val uploadDateRange = config.uploadDateRange.getOrElse({
      val latestDate = FixedWirelessSubsOutput.tsl(config.subsOutputLocation).latestDate
      LocalDateRange(startDate = latestDate, endDate = latestDate)
    })

    // Load the Data
    val fixedWirelessSubs = FixedWirelessSubsOutput.readRange(config.subsOutputLocation, uploadDateRange).cache()

    // Upload to Redshift
    logger.info(s"Total Partitions in Upload to Redshift: ${fixedWirelessSubs.select($"date").distinct().count()}")
    RedshiftUtils.redshiftWriteDateRange(
      data = fixedWirelessSubs,
      tableName = config.redshiftUploadTableName,
      ldrOpt = Some(uploadDateRange),
      dateColumnName = "date"
    )
  }

}
