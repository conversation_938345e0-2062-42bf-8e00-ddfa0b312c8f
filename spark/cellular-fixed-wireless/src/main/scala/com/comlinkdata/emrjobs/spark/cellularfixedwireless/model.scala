package com.comlinkdata.emrjobs.spark.cellularfixedwireless

object model {
  case class BlockXLoopCounts(

  )

  case class BlockX(
    block_x: Array[Byte],
    total_block_x_bssid_cnt: Int
  )

  case class PatternMatchedBlockRecords(
    scan_bssid: Array[Byte],
    scan_ssid: Array[Byte]
  )

  case class BlockFilters(
    previous_block_x: Array[Byte]
  )

  case class FwFaBlockMapping(
    UUID: String,
    RUN_TIMESTAMP: java.sql.Timestamp,
    QUARTER: java.lang.Long,
    YEAR: java.lang.Long,
    BLOCK_X: String,
    SSID_PATTERN: String,
    LEFT_SSID: String,
    MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: java.lang.Long,
    TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: java.lang.Long,
    TOTAL_BLOCK_X_BSSID_CNT: java.lang.Long,
    MAX_LEFT_SSID_BSSID_CNT: java.lang.Long
  )

  case class FwFaBlockMappingOutput(
    UUID: String,
    RUN_TIMESTAMP: java.sql.Timestamp,
    BLOCK_X: String,
    SSID_PATTERN: String,
    LEFT_SSID: String,
    MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: java.lang.Long,
    TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: java.lang.Long,
    TOTAL_BLOCK_X_FIOS_CNT: java.lang.Long,
    TOTAL_BLOCK_X_IOT_CNT: java.lang.Long,
    TOTAL_BLOCK_X_SPECTRUM_CNT: java.lang.Long,
    TOTAL_BLOCK_X_BSSID_CNT: java.lang.Long,
    MAX_LEFT_SSID_BSSID_CNT: java.lang.Long
  )

  case class FwTmpBlock6(
    BLOCK_6: String
  )

  case class FwTmpBlockFilters(
    PREVIOUS_BLOCK_X: String
  )

  case class FwTmpBlockX(
    BLOCK_X: String,
    TOTAL_BLOCK_X_BSSID_CNT: java.lang.Long
  )

  case class FwTmpMappedBlocks(
    UUID: String,
    RUN_TIMESTAMP: java.sql.Timestamp,
    QUARTER: java.lang.Long,
    YEAR: java.lang.Long,
    BLOCK_X: String,
    SSID_PATTERN: String,
    LEFT_SSID: String,
    MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: java.lang.Long,
    TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: java.lang.Long,
    TOTAL_BLOCK_X_FIOS_CNT: java.lang.Long,
    TOTAL_BLOCK_X_IOT_CNT: java.lang.Long,
    TOTAL_BLOCK_X_SPECTRUM_CNT: java.lang.Long,
    TOTAL_BLOCK_X_BSSID_CNT: java.lang.Long,
    MAX_LEFT_SSID_BSSID_CNT: java.lang.Long
  )

  case class FwTmpQuarterPatternMatchedBlockRecords(
    SCAN_BSSID: String,
    SCAN_SSID: String
  )

  case class LuDeviceFilterMapping(
    CARRIER: String,
    TYPE: String,
    STATUS: String,
    LAUNCHED: String,
    MAX_TECHNOLOGY: String,
    SSID_PATTERN: String,
    VENDOR: String,
    MODEL: String,
    ENABLED: String
  )

  case class SsidFilters(
    SSID_FILTER: String,
    SSID_PATTERN: String
  )

  case class SsidFiltersLtrl(
    SSID_FILTER: String,
    SSID_PATTERN: String,
    PATTERN_LTRL: String
  )

  case class IntData(
    BLOCK_X: String,
    MAX_PATTERN_MATCH: String,
    SSID_PATTERN: String,
    TOTAL_BLOCK_X_BSSID_CNT: Long,
    MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: Long,
    TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: Long,
    TOTAL_BLOCK_X_FIOS_CNT: Long,
    TOTAL_BLOCK_X_IOT_CNT: Long,
    TOTAL_BLOCK_X_SPECTRUM_CNT: Long
  )
}