package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering.CustomInclusionData
import com.comlinkdata.emrjobs.spark.georesolver.GeometryResolver
import org.apache.spark.sql.{Dataset, SparkSession}
import org.apache.spark.sql.functions.substring


/**
  * BlockJoinedBssid -- intermediary dataset with modems mapped to encapsulating CBs
  * @param block associated census block containing the modem
  * @param BSSID_Carrier carrier identified for the modem
  * @param BSSID_Technology modem technology identified
  * @param modem_model model of the identified modem
  * @param modem_vendor vendor of the identified modem
  * @param short_BSSID bssid substring to identify modem
  * @param centroid_lat centroid latitude of all locations of the same modem
  * @param centroid_lon centroid longitude of all locations of the same modem
  * @param first_observed_date earliest date the modem was observed this quarter
  */
case class BlockJoinedBssid(
  block: String,
  BSSID_Carrier: String,
  BSSID_Technology: String,
  modem_model: String,
  modem_vendor: String,
  short_BSSID: String,
  centroid_lat: Double,
  centroid_lon: Double,
  first_observed_date: java.sql.Timestamp
)


object BlockJoinedBssid{

  /**
    * Join the deduplicated data to the census block geography data in order to map each distinct device to a particular
    * census block
    * @param dedupe results of the dedupe subquery, containing one record per device
    * @param resolver GeometryResolver object configured to resolve the DeduplicateBssid dataset to census block polygons
    * @param spark
    * @return
    */
  def getBlockJoined(dedupe: Dataset[DeduplicateBssid], resolver: GeometryResolver[DeduplicateBssid])(implicit spark: SparkSession): Dataset[BlockJoinedBssid] = {
    import spark.implicits._
    resolver
      .resolveAsFlatDf(dedupe, $"centroid_lat", $"centroid_lon", "block")
      .select(
        substring($"block",0,15).as("block"),
        $"BSSID_Carrier",
        $"BSSID_Technology",
        $"modem_model",
        $"modem_vendor",
        $"short_BSSID",
        $"centroid_lat",
        $"centroid_lon",
        $"first_observed_date"
      )
      .as[BlockJoinedBssid]
  }

  /***
    * Join the Inclusion data to the census block geography data in order to map each distinct device to a particular
    * census block
    *
    * @param inclusions - Inclusion dataset with lat and long information
    * @param resolver - GeoResolver to apply block based on the lat and long
    * @param spark - spark
    * @return Dataset with associated census blocks based on location data
    */
  def getBlockJoinedInclusions(inclusions: Dataset[CustomInclusionData], resolver: GeometryResolver[CustomInclusionData])(implicit spark: SparkSession): Dataset[BlockJoinedBssid] = {
    import spark.implicits._
    resolver
      .resolveAsFlatDf(inclusions, $"centroid_lat", $"centroid_lon", "block")
      .select(
        substring($"block", 0, 15).as("block"),
        $"BSSID_Carrier",
        $"BSSID_Technology",
        $"modem_model",
        $"modem_vendor",
        $"short_BSSID",
        $"centroid_lat",
        $"centroid_lon",
        $"first_observed_date"
      )
      .as[BlockJoinedBssid]
  }
}
