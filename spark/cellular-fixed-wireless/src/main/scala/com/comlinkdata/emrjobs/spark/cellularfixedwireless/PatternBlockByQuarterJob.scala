package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.model._
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.CellularFixedWirelessUtils._
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering.OnesourceData
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{Dataset, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.expressions.Window

import java.net.URI
import java.time.LocalDate


case class PatternBlockByQuarterJobConfig(
  processEndDateInclusive: Option[LocalDate],
  numDaysToProcess: Option[Int],
  luPropertiesBssidCountThresholdLarge: java.lang.Long,
  numFilesPerOutputPartition: Int,
  ssidFiltersLocation: URI,
  scanDataLocation: URI,
  outputLocation: URI
)


object PatternBlockByQuarterJob extends SparkJob(PatternBlockByQuarterJobRunner)


object PatternBlockByQuarterJobRunner extends SparkJobRunner[PatternBlockByQuarterJobConfig] with LazyLogging{

  def runJob(config: PatternBlockByQuarterJobConfig)(implicit spark: SparkSession) = {
    import spark.implicits._

    // Pull Optional Config Values
    val processEndDateInclusive = config.processEndDateInclusive.getOrElse(getLastWeekdayDate("SATURDAY"))
    val lookBackDays = config.numDaysToProcess.getOrElse(90)-1 // -1 because range is inclusive of end date
    val bssidCountThresholdLarge = config.luPropertiesBssidCountThresholdLarge

    // Compute Dynamic Configs
    val inputDateRange = LocalDateRange.of(processEndDateInclusive.minusDays(lookBackDays), processEndDateInclusive)
    val scanDataPaths = generateScanDataPaths(config.scanDataLocation, inputDateRange)
    val outputWithPartitions = CellularFixedWirelessUtils.generateYearMonthDayPath(config.outputLocation, processEndDateInclusive)
    logger.info(s"Processing Dates in Range: (${inputDateRange.startDate}, ${inputDateRange.endDate})")

    // Prepare Builder for S3 validation in relevant buckets
    implicit val cldFileUtils: CldFileUtils = CldFileUtils.newBuilder.forUri(config.ssidFiltersLocation, config.scanDataLocation)(spark).build

    // Create the SSID_FILTERS table
    logger.info(s"Loading SSID Filters Table into Memory from source: ${config.ssidFiltersLocation}")
    val ssidFiltersRaw = spark.read
      .option("header", "true")
      .csv(config.ssidFiltersLocation.toString)
      .as[LuDeviceFilterMapping]
    val ssidFilters = extractSsidFilters(ssidFiltersRaw).cache()

    // Load Scan Data into Memory
    logger.info(s"Loading Onesource Wifi Scans from Path: ${config.scanDataLocation}")
    val scanData = OnesourceData.loadData(scanDataPaths, inputDateRange)


    // Extracting Block6 Data
    val block6 = extractBlock6(scanData, ssidFilters, bssidCountThresholdLarge, inputDateRange)

    // Write the Results to S3
    logger.info(s"Writing BLOCK_6s data to destination: ${config.outputLocation}")
    dynamicOverwrite(block6, outputWithPartitions)
  }



  /**
    * Extracts the appropriate SSID Filters from the raw SSID Filter Mapping Raw Table
    * @param ssidFiltersRaw the raw data read into memory containing the SSID Filters to be extracted from
    * @param spark
    * @return DataFrame containing the fields SSID_FILTER as "[PATTERN]%" and SSID_PATTERN as "[PATTERN]"
    */
  def extractSsidFilters(ssidFiltersRaw: Dataset[LuDeviceFilterMapping])(implicit spark: SparkSession) = {
    import spark.implicits._
    ssidFiltersRaw
      .where(
        upper($"ENABLED") === "TRUE"
          && $"SSID_PATTERN".isNotNull
      )
      .select(
        concat($"SSID_PATTERN", lit("%")).as("SSID_FILTER"),
        $"SSID_PATTERN"
      )
      .distinct()
      .as[SsidFilters]
  }



  /**
    * Produce a dataframe with unique BLOCK_6s (first 8 characters of Scan_BSSID) which meet filter criteria, match
    * specified SSID patterns, and occur with a frequency >= a given threshold
    * @param scanData The raw data to extract the BLOCK_6s from
    * @param ssidFilters Table containing the patterns for which the Scan Data will be filtered
    * @param bssidCountThresholdLarge Frequency threshold for which the unique BLOCK_6s must occur to be considered
    * @param processDateRangeInclusive LocalDateRange indicating the start and end dates (inclusive) to filter scan timestamps
    * @param spark
    * @return DataFrame containing the BLOCK_6 values which passed all filters
    */
  def extractBlock6(scanData: Dataset[OnesourceData], ssidFilters: Dataset[SsidFilters], bssidCountThresholdLarge: java.lang.Long, processDateRangeInclusive: LocalDateRange)(implicit spark: SparkSession) = {
    import spark.implicits._

    val window = Window.partitionBy($"Scan_BSSID").orderBy($"QOS_Date".desc)
    val regexFilter = spark.sparkContext.broadcast(getRegexFromFilterTable(ssidFilters.toDF(), "SSID_FILTER"))
    val start = processDateRangeInclusive.startDate
    val end = processDateRangeInclusive.endDate
    val ymd = end.toString.split("-")

    scanData
      .withColumn("scan_date", to_date($"QOS_Date"))
      .where(
        $"scan_date" >= start
        && $"scan_date" <= end
        && $"Scan_BSSID".isNotNull
        && !$"Scan_BSSID".isin("-16384", "-32768", "00:00:00:00:00:00")
        && $"Location_Age" <= 600
        && $"WIFIScan_SSID".rlike(regexFilter.value)
      )
      .select(
        substring($"Scan_BSSID", 0, 8).as("BLOCK_6"),
        $"Scan_BSSID",
        row_number().over(window).as("ROW_NUM")
      )
      .where($"ROW_NUM" === 1)
      .groupBy("BLOCK_6")
      .agg( countDistinct($"Scan_BSSID").as("TOTAL_BLOCK_6_COUNT") )
      .where( $"TOTAL_BLOCK_6_COUNT" >= bssidCountThresholdLarge)
      .select($"BLOCK_6")
      .as[FwTmpBlock6]
  }

}
