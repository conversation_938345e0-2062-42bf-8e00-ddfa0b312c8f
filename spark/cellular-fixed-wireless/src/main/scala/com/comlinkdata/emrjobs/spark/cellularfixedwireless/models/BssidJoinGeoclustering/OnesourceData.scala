package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering

import com.comlinkdata.largescale.commons.LocalDateRange
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import org.apache.spark.sql.functions.{concat_ws, to_date}
import org.apache.spark.sql.{Dataset, SparkSession}

import java.net.URI

/***
  * Custom case class for OnesourceWifScans
  * @param Meta_CreatedDate - Created date
  * @param Location_Latitude - Latitude
  * @param Location_Longitude - Longitude
  * @param Location_HorizontalAccuracy - Location Horizontal Accuracy
  * @param Location_Age - Location age
  * @param QOS_Date - Qos Date
  * @param Scan_BSSID - BSSID
  * @param WIFIScan_Capabilities - Scan capabilities
  * @param WIFIScan_ChannelNumberIdentification - Channel Number IDentification
  * @param WIFIScan_ChannelWidth - Channel Width
  * @param WIFIScan_DeviceName _ Device Name
  * @param WIFIScan_Frequency - Frequency
  * @param WIFIScan_Manufacturer - Manufacturer
  * @param WIFIScan_ModelName - Model Name
  * @param WIFIScan_ModelNumber - Model Number
  * @param WIFIScan_OUI - OUI
  * @param WIFIScan_SSID - Scan SSID
  */
case class OnesourceData(
  Meta_CreatedDate: java.sql.Timestamp,
  Location_Latitude: Double,
  Location_Longitude: Double,
  Location_HorizontalAccuracy: Double,
  Location_Age: BigInt,
  QOS_Date: java.sql.Timestamp,
  Scan_BSSID: String,
  WIFIScan_Capabilities: String,
  WIFIScan_ChannelNumberIdentification: Array[BigInt],
  WIFIScan_ChannelWidth: BigInt,
  WIFIScan_DeviceName: String,
  WIFIScan_Frequency: BigInt,
  WIFIScan_Manufacturer: String,
  WIFIScan_ModelName: String,
  WIFIScan_ModelNumber: String,
  WIFIScan_OUI: String,
  WIFIScan_SSID: String
)


object OnesourceData {

  /**
    * Validates the give URI paths for the raw data, reads the data into DataSet[WifiData], and performs initial filters
    * on the data in order to reduce data size as much as possible before processing.
    * @param paths sequence containing the string representation of all partition paths for the data
    * @param spark - spark
    * @param fileUtils - Ensure paths are valid
    * @return Onesource Data from the given paths
    */
  def loadData(paths: Seq[String], processDateRange: LocalDateRange)(implicit spark: SparkSession, fileUtils: CldFileUtils): Dataset[OnesourceData] = {
    import spark.implicits._
    validatePathsOrThrow(paths)

    spark.read
      .parquet(paths: _*)
      .withColumn("scan_date", to_date($"Meta_CreatedDate"))
      .withColumn("WIFIScan_SSID", concat_ws("", $"WIFIScan_SSID"))
      .withColumnRenamed("WIFIScan_BSSID", "Scan_BSSID")
      .where(
        $"Location_Age" <= 600
          && $"Scan_BSSID".isNotNull
          && !$"Scan_BSSID".isin("-16384","-32768")
      )
      .as[OnesourceData]
  }


  /**
    * Validate that all input paths are readable, and throw an error if there's any bad paths provided
    * @param paths sequence of strings representing the S3 paths to validate
    * @param fileUtils
    */
  def validatePathsOrThrow(paths: Seq[String])(implicit fileUtils: CldFileUtils) = {
    paths.foreach(p => fileUtils.allFilesInDirectoryAreReadableOrThrow(URI create p))
  }
}
