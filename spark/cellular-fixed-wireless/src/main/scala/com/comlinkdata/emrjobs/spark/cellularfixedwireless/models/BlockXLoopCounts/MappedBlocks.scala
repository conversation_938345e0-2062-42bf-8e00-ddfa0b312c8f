package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BlockXLoopCounts

import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import org.apache.spark.sql.{Dataset, SparkSession}

import java.net.URI


/**
  * Schema for intermediary output table: mapped_blocks
  * @param UUID
  * @param RUN_TIMESTAMP
  * @param BLOCK_X
  * @param SSID_PATTERN
  * @param LEFT_SSID
  * @param MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT
  * @param TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT
  * @param TOTAL_BLOCK_X_FIOS_CNT
  * @param TOTAL_BLOCK_X_IOT_CNT
  * @param TOTAL_BLOCK_X_SPECTRUM_CNT
  * @param TOTAL_BLOCK_X_BSSID_CNT
  * @param MAX_LEFT_SSID_BSSID_CNT
  */
case class MappedBlocks(
  UUID: String,
  RUN_TIMESTAMP: java.sql.Timestamp,
  BLOCK_X: String,
  SSID_PATTERN: String,
  LEFT_SSID: String,
  MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: java.lang.Long,
  TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: java.lang.Long,
  TOTAL_BLOCK_X_FIOS_CNT: java.lang.Long,
  TOTAL_BLOCK_X_IOT_CNT: java.lang.Long,
  TOTAL_BLOCK_X_SPECTRUM_CNT: java.lang.Long,
  TOTAL_BLOCK_X_BSSID_CNT: java.lang.Long,
  MAX_LEFT_SSID_BSSID_CNT: java.lang.Long
)


object MappedBlocks {

  /**
    * Validate the given path is readable. If it is, read the appropriate schema for BlockXLoopCounts output (MappedBlocks)
    * and return as a dataset.
    * @param path URI path to the S3 location containing the output from BlockXLoopCounts for the current processiong period
    * @param spark
    * @param fileUtils
    * @return
    */
  def loadValidDataOrThrow(path: URI)(implicit spark: SparkSession, fileUtils: CldFileUtils): Dataset[MappedBlocks] = {
    import spark.implicits._
    fileUtils.allFilesInDirectoryAreReadableOrThrow(path)
    spark.read
      .parquet(path.toString)
      .select(
        $"UUID",
        $"RUN_TIMESTAMP",
        $"BLOCK_X",
        $"SSID_PATTERN",
        $"LEFT_SSID",
        $"MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT",
        $"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT",
        $"TOTAL_BLOCK_X_FIOS_CNT",
        $"TOTAL_BLOCK_X_IOT_CNT",
        $"TOTAL_BLOCK_X_SPECTRUM_CNT",
        $"TOTAL_BLOCK_X_BSSID_CNT",
        $"MAX_LEFT_SSID_BSSID_CNT"
      )
      .as[MappedBlocks]
  }
}
