package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.CellularFixedWirelessUtils.stClusterDbScanWithBinning
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.{collect_list, concat_ws, expr, hash, lit, pmod}
import org.apache.spark.sql.{Dataset, SparkSession}
import org.locationtech.jts.geom.Geometry


/**
  * ClusteredDataScans -- Intermediary table containing the results of running data through DBScan
  * @param Scan_BSSID BSSID associated with the device scanned
  * @param Scan_SSID SSID associated with the device scanned
  * @param BSSID_Carrier Carrier associated with the device scanned
  * @param BSSID_Technology Technology associated with the device scanned
  * @param modem_vendor vendor associated with the modem scanned
  * @param modem_model model associated with the model scanned
  * @param geogpoint geographical point constructed from the (lon,lat) of the scan location
  * @param cluster_num cluster number assigned to the record as a result of running DBScan to all matching BSSIDs with 2 points of location precision
  * @param Device_UID UID associated with the device scaned
  * @param Scan_Timestamp timestamp on the device of the device scanned
  * @param Location_Latitude latitude of the device
  * @param Location_Longitude longitude of the device
  */
case class ClusteredDataScans(
  Scan_BSSID: String,
  Scan_SSID: String,
  BSSID_Carrier: String,
  BSSID_Technology: String,
  modem_vendor: String,
  modem_model: String,
  geogpoint: Geometry,
  cluster_num: java.lang.Long,
  Device_UID: String,
  Scan_Timestamp: java.sql.Timestamp,
  Location_Latitude: Double,
  Location_Longitude: Double
)


object ClusteredData {

  /**
    * Using the preprocessed input dataset IngestedDataScans, assign each lat/long it's own geographical point (geogpoint),
    * and performs a binned DBScan operation on all points over a BSSID/SSID/latitude to 2 decimal places/longitude to 2
    * decimal places window (cluster all records of a specific device within a particular neighborhood)
    * @param ingestedDataScans the IngestedDataScans subquery results to be operated on
    * @param spark
    * @return
    */
  def createFromScans(ingestedDataScans: Dataset[IngestedDataScans])(implicit spark: SparkSession): Dataset[ClusteredDataScans] = {
    import spark.implicits._
    val eps = 200
    val minGeogs = 2

    val clusterUdf = stClusterDbScanWithBinning(eps, minGeogs)

    // Step 1: Prepare data with geogpoint, bucketed cluster key
    val withGeo = ingestedDataScans
      .withColumn(
        "geogpoint",
        expr("ST_POINT(CAST(ROUND(Location_Longitude, 4) AS DECIMAL(16,12)), CAST(ROUND(Location_Latitude, 4) AS DECIMAL(16,12)))")
      )
      .withColumn("bucket_id", pmod(hash($"Device_UID"), lit(10)))
      .withColumn("cluster_key", concat_ws("-", $"Scan_BSSID", $"Scan_SSID", $"cluster_lat", $"cluster_long", $"bucket_id"))
      .repartition($"cluster_key") // Avoid skew by distributing group keys

    // Step 2: Aggregate geogpoints per group (for clustering)
    val groupedPoints = withGeo
      .groupBy("Scan_BSSID", "Scan_SSID", "cluster_lat", "cluster_long", "bucket_id", "cluster_key")
      .agg(collect_list($"geogpoint").as("geogpoints"))

    // Step 3: Join back with original rows and apply the UDF row-wise
    val clustered = withGeo
      .join(groupedPoints, Seq("Scan_BSSID", "Scan_SSID", "cluster_lat", "cluster_long", "bucket_id", "cluster_key"))
      .withColumn("cluster_num", clusterUdf($"geogpoints", $"cluster_key", $"geogpoint"))

    // Step 4: Select final schema
    clustered.select(
      $"Scan_BSSID",
      $"Scan_SSID",
      $"BSSID_Carrier",
      $"BSSID_Technology",
      $"modem_vendor",
      $"modem_model",
      $"geogpoint",
      $"cluster_num",
      $"Device_UID",
      $"Scan_Timestamp",
      $"Location_Latitude",
      $"Location_Longitude"
    ).as[ClusteredDataScans]
  }
}
