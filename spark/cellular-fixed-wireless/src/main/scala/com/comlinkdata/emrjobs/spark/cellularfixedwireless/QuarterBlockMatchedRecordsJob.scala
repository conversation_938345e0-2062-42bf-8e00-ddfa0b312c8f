package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.model._
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.CellularFixedWirelessUtils._
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering.OnesourceData
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{Dataset, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.expressions.Window

import java.net.URI
import java.time.LocalDate


case class QuarterBlockMatchedRecordsJobConfig(
  processEndDateInclusive: Option[LocalDate],
  numDaysToProcess: Option[Int],
  numFilesPerOutputPartition: Int,
  block6Location: URI,
  scanDataLocation: URI,
  outputLocation: URI
)


object QuarterBlockMatchedRecordsJob extends SparkJob(QuarterBlockMatchedRecordsJobRunner)


object QuarterBlockMatchedRecordsJobRunner extends SparkJobRunner[QuarterBlockMatchedRecordsJobConfig] with LazyLogging {

  def runJob(config: QuarterBlockMatchedRecordsJobConfig)(implicit spark: SparkSession) = {
    import spark.implicits._

    // Extract Optional Config Values
    val processEndDateInclusive = config.processEndDateInclusive.getOrElse(getLastWeekdayDate("SATURDAY"))
    val lookBackDays = config.numDaysToProcess.getOrElse(90)-1 // -1 because range is inclusive of end date

    // Compute Dynamic Config Variables
    val inputDateRange = LocalDateRange.of(processEndDateInclusive.minusDays(lookBackDays), processEndDateInclusive)
    val scanDataPaths = generateScanDataPaths(config.scanDataLocation, inputDateRange)
    val block6WithPartitions = CellularFixedWirelessUtils.generateYearMonthDayPath(config.block6Location, processEndDateInclusive)
    val outputWithPartitions = CellularFixedWirelessUtils.generateYearMonthDayPath(config.outputLocation, processEndDateInclusive)

    // Prepare File Utils for S3 Validation
    implicit val cldFileUtils: CldFileUtils = CldFileUtils.newBuilder.forUri(config.scanDataLocation, config.block6Location)(spark).build

    // Extract the Block6 Table
    logger.info(s"Loading BLOCK_6 data into memory from source: ${block6WithPartitions}")
    val blockFilters = spark.read
      .parquet(block6WithPartitions)
      .withColumn("BLOCK_6", upper($"BLOCK_6"))
      .as[FwTmpBlock6]
      .cache()

    // Extract scan data table into memory
    logger.info(s"Loading Onesource Wifi Scans from Path: ${config.scanDataLocation}")
    val scanData = OnesourceData.loadData(scanDataPaths, inputDateRange)

    // Compute Query
    logger.info(s"Extracting pattern matched blocks for ${inputDateRange.startDate} through ${inputDateRange.endDate}")
    val patternMatchedBlockRecords = extractPatternMatchedBlockRecords(scanData, blockFilters, inputDateRange)

    // Write Results to S3
    logger.info(s"Writing pattern matched block records to destination: ${config.outputLocation}")
    dynamicOverwrite(patternMatchedBlockRecords, outputWithPartitions)
  }



  /**
    * Produce a dataframe with distinct BSSID and SSID for the processing year/quarter that meet the pattern matching
    * requirements
    * @param scanData The raw data to be filtered for the pattern-matching
    * @param blockFilters Table containing the filters with the SQL wildcards to filter scanData with
    * @param processDateRangeInclusive LocalDateRange indicating the start and end dates (inclusive) to filter scan timestamps
    * @param spark
    * @return DataFrame containing the filtered SSID and BSSID values which passed all filters
    */
  def extractPatternMatchedBlockRecords(scanData: Dataset[OnesourceData], blockFilters: Dataset[FwTmpBlock6], processDateRangeInclusive: LocalDateRange)(implicit spark: SparkSession) = {
    import spark.implicits._
    val window = Window.partitionBy($"Scan_BSSID").orderBy($"QOS_Date".desc, $"WIFIScan_SSID".desc, $"Location_Age")
    val start = processDateRangeInclusive.startDate
    val end = processDateRangeInclusive.endDate

    val blocks = spark.sparkContext.broadcast(blockFilters.select($"BLOCK_6").as[String].collect().toSet)

    scanData
      .repartition($"Scan_BSSID", $"WIFIScan_SSID")
      .withColumn("scan_date", to_date($"QOS_Date"))
      .where(
        $"scan_date" >= start
        && $"scan_date" <= end
        && $"Scan_BSSID".isNotNull
        && !$"Scan_BSSID".isin("-16384", "-32768", "00:00:00:00:00:00")
        && $"Location_Age" <= 600
      )
      .select(
        $"Scan_BSSID",
        $"WIFIScan_SSID",
        $"QOS_Date",
        row_number().over(window).as("ROW_NUM")
      )
      .where($"ROW_NUM" === 1)
      .withColumn("BLOCK_6", upper(substring($"Scan_BSSID",0,8)))
      .where($"BLOCK_6".isInCollection(blocks.value))
      .select(
        $"Scan_BSSID".as("SCAN_BSSID"),
        $"WIFIScan_SSID".as("SCAN_SSID")
      )
      .distinct()
      .as[FwTmpQuarterPatternMatchedBlockRecords]
  }

}
