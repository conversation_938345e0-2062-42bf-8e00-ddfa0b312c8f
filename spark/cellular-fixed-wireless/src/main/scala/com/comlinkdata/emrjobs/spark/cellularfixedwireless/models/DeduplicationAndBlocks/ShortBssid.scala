package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering.FilteredDataScans
import org.apache.spark.sql.functions.{expr, lit, when}
import org.apache.spark.sql.{Dataset, SparkSession}
import org.locationtech.jts.geom.Geometry


/**
  * ShortBssid -- Intermediary Dataset containing the appropriately extracted BSSID substring for each modem
  * @param BSSID_Carrier carrier identified for the modem
  * @param BSSID_Technology modem technology identified
  * @param modem_model model of the identified modem
  * @param modem_vendor vendor of the identified modem
  * @param short_BSSID bssid substring to identify modem
  * @param earliest_date earliest date the modem was observed this quarter
  * @param aggregate_centroid Centroid of the modem locations (within a 2-decimal place lat-long locality)
  */
case class ShortBssid(
  BSSID_Carrier: String,
  BSSID_Technology: String,
  modem_model: String,
  modem_vendor: String,
  short_BSSID: String,
  earliest_date: java.sql.Timestamp,
  aggregate_centroid: Geometry
)


object ShortBssid {
  /**
    * Select the relevant geoclustered data values and the correxponding BSSID substring relevant to the particular modems
    * being processed
    * @param geoclustered raw data produced from the BssidJoinGeoclustering job
    * @param rules lookup table for model-specific special rules when extracting the short_bssid
    * @param spark
    * @return
    */
  def getShortBssidFromData(geoclustered: Dataset[FilteredDataScans], rules: Dataset[ShortBssidSpecialRules])(implicit spark: SparkSession): Dataset[ShortBssid] = {
    import spark.implicits._
    geoclustered
      .join(rules, Seq("modem_model"), "left")
      .withColumn("pos", when($"pos".isNotNull, $"pos").otherwise(lit(0)))
      .withColumn("len", when($"len".isNotNull, $"len").otherwise(lit(16)))
      .select(
        $"BSSID_Carrier",
        $"BSSID_Technology",
        $"modem_model",
        $"modem_vendor",
        $"Scan_BSSID".substr($"pos", $"len").as("short_BSSID"),
        $"earliest_date",
        expr("ST_POINT(CAST(aggregate_centroid_longitude AS DECIMAL(16,12)),CAST(aggregate_centroid_latitude AS DECIMAL(16,12)))").as("aggregate_centroid")
      )
      .as[ShortBssid]
  }
}
