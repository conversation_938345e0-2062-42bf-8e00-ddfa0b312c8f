package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.model.LuDeviceFilterMapping
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.BssidExclusionsLookup
import org.apache.spark.sql.functions.{broadcast, coalesce, count, lit, lower, round, substring, upper}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

import java.net.URI


/**
  * IngestedDataScans -- Intermediary table for the initial ingestion + preprocessing of the wifi scans data
  * @param Device_UID the UID associated with the device scanned
  * @param Scan_Timestamp the device's current timestamp at time of scan
  * @param Scan_SSID SSID associated with the scanned device
  * @param Scan_BSSID BSSID associated with the scanned device
  * @param Location_Latitude latitude of the scanned device's location
  * @param Location_Longitude longitude of the scanned device's location
  * @param cluster_lat location_latitude rounded to 2 points of decimal precision for clustering
  * @param cluster_long location_longitude rounded to 2 points of decimal precision for clustering
  * @param Location_Age age of the device scanned
  * @param BSSID_Carrier carrier associated with the scanned device
  * @param BSSID_Technology technology associated with the scanned device
  * @param modem_vendor vendor associated with the scanned device
  * @param modem_model model associated with the scanned device
  */
case class IngestedDataScans(
  Device_UID: String,
  Scan_Timestamp: java.sql.Timestamp,
  Scan_SSID: String,
  Scan_BSSID: String,
  Location_Latitude: Double,
  Location_Longitude: Double,
  cluster_lat: String,
  cluster_long: String,
  Location_Age: java.lang.Long,
  BSSID_Carrier: String,
  BSSID_Technology: String,
  modem_vendor: String,
  modem_model: String
)


object IngestedData {
  def createFromLookup(onesourceScans: Dataset[OnesourceData], carrierMappedBlocks: DataFrame, luDeviceFilterMappingLocation: URI)(implicit spark: SparkSession): Dataset[IngestedDataScans] = {
    import spark.implicits._

    val targetCarriers = Seq("US Cellular", "T-Mobile")
    val filteredCarrierMappedBlocks = carrierMappedBlocks
      .where($"FWA_ISP".isInCollection(targetCarriers))

    val filterMapping = spark.read.option("header", "true")
      .csv(luDeviceFilterMappingLocation.toString)
      .as[LuDeviceFilterMapping]

    onesourceScans.join(filteredCarrierMappedBlocks, $"Scan_BSSID".startsWith($"mapped_OUI_blocks"), "inner")
      .join(broadcast(filterMapping.where(upper($"ENABLED") === "TRUE" && $"type" === "fixed")), $"WIFIScan_SSID".startsWith($"SSID_Pattern") && $"CARRIER" === $"FWA_ISP", "left")
      .select(
        lit(' ').as("Device_UID"),
        $"QOS_Date".as("Scan_Timestamp"),
        $"WIFIScan_SSID".as("Scan_SSID"),
        $"Scan_BSSID",
        $"Location_Latitude",
        $"Location_Longitude",
        (round($"Location_Latitude"/2,2) * 2).cast("String").as("cluster_lat"),
        (round($"Location_Longitude"/2,2) * 2).cast("String").as("cluster_long"),
        $"Location_Age".cast("Long"),
        $"FWA_ISP".as("BSSID_Carrier"),
        lit("5G").as("BSSID_Technology"),
        coalesce($"WIFIScan_Manufacturer", $"VENDOR").as("modem_vendor"),
        coalesce($"WIFIScan_ModelName", $"MODEL").as("modem_model")
    )
  .as[IngestedDataScans]
  }


  /**
    * Pre-process the raw wifi data by filtering to BSSID values present in mappingBssid for the appropriate carriers
    * defined for runtime processing. Initially filter to any record with the first 8 characters matching (aggressive
    * pre-filter) before joining to mappingBssid to join on BSSIDs which start with BLOCK_X
    *
    * @param wifi Dataset of the wifi scans data to be processed
    * @param mappingBssid dataset of the mapping BSSID data, which contains mapping of known SSIDs to the appropriate modems
    * @param targetCarriers list of carrier names on which to apply the filters
    * @param spark
    * @return
    */
  def createFromScans(wifi: Dataset[OnesourceData], mappingBssid: Dataset[MappingBssid], targetCarriers: List[String])(implicit spark: SparkSession): Dataset[IngestedDataScans] = {
    import spark.implicits._

    // Get the first 8 characters of each block_x for prefiltering
    val block8Filter = spark.sparkContext.broadcast(mappingBssid
      .select(substring($"BLOCK_X", 0, 8))
      .distinct()
      .as[String]
      .collect()
      .toSet
    )

    // perform the ingested data query
    // NOTE: substr(BLOCK_X, 0, block_length) may be redundant since block_length=LENGTH(BLOCK_X)
    //   If time allows test performance if joining on $"Scan_BSSID".startsWith("BLOCK_X") instead
    wifi
      .where(substring($"Scan_BSSID", 0, 8).isInCollection(block8Filter.value))
      .join(
        broadcast(mappingBssid.withColumn("block", $"BLOCK_X".substr(lit(0),$"block_length"))),
        $"Scan_BSSID".startsWith($"block"),
        "inner"
      )
      .select(
        lit(' ').as("Device_UID"),
        $"QOS_Date".as("Scan_Timestamp"),
        $"WIFIScan_SSID".as("Scan_SSID"),
        $"Scan_BSSID",
        $"Location_Latitude",
        $"Location_Longitude",
        (round($"Location_Latitude"/2,2) * 2).cast("String").as("cluster_lat"),
        (round($"Location_Longitude"/2,2) * 2).cast("String").as("cluster_long"),
        $"Location_Age".cast("Long"),
        $"CARRIER".as("BSSID_Carrier"),
        $"MAX_TECHNOLOGY".as("BSSID_Technology"),
        $"VENDOR".as("modem_vendor"),
        $"MODEL".as("modem_model")
      ).where( ($"CARRIER" === "Verizon" && $"modem_model" === "LV55") || ($"CARRIER" === "US Cellular" && $"modem_model" === "FG2000"))
      .as[IngestedDataScans]
  }

  /***
    * Use the inclusion based approach to create the dataset and process it into a schema usable by dbscan
    * @param rawScans - raw WifiScans for the processing period
    * @param onesourceScans - onesource scans for the processing period
    * @param adapter - carrier specific adapter to apply carrier-specific transformations
    * @param exclusions - bssidExclusions - Additional Exclusions to be applied to the output dataset
    * @param spark - spark
    * @return Dataset of IngestedDataScans with appropriate filtering
    */
  def createIngestedInclusions(onesourceScans: Dataset[OnesourceData],
    adapter: CarrierAdapter, exclusions: Dataset[BssidExclusionsLookup]) (implicit spark: SparkSession) : Dataset[IngestedDataScans] = {
    import spark.implicits._

    val filteredData = onesourceScans.filter(!lower($"WIFIScan_SSID").like("%spectrum%"))

    // Create the ouilookup from the Scans with the custom filters from the adapter
    val ouiLookup = onesourceScans
      .transform(adapter.applyPreFiltering)
      .groupBy(
        substring($"Scan_BSSID", 0, 11).as("OUI_long"),
        $"WIFIScan_Manufacturer",
        $"WIFIScan_ModelName",
        $"WIFIScan_ModelNumber"
      )
      .agg(count($"*").as("numrecords"))
      .filter($"numrecords" >= 5).as[OuiLookup]

    // Create the BSSID list from the filteredWifiScans and filter with other business rules
    val bssidLevel = filteredData
      .transform(adapter.applyAdditionalFilters)
      .join(ouiLookup, substring($"Scan_BSSID", 0, 11) === $"OUI_LONG", "leftsemi")
      .groupBy(
        $"Scan_BSSID",
      )
      .agg(count($"Scan_BSSID").as("num_UID")).as[BssidLevel]

    // Create the dataset and ensure it fits the schema needed for DBScan and apply exclusions after
    val result = bssidLevel.join(filteredData, Seq("Scan_BSSID"), "inner")
      .drop($"WIFIScan_Manufacturer")
      .drop($"WIFIScan_ModelName")
      .join(ouiLookup, substring($"Scan_BSSID", 0, 11) === $"OUI_LONG", "inner")
      .withColumn("BSSID_Carrier", lit("Verizon"))
      .select(
        lit(' ').as("Device_UID"),
        $"QOS_Date".as("Scan_Timestamp"),
        $"WIFIScan_SSID".as("Scan_SSID"),
        $"Scan_BSSID",
        $"Location_Latitude",
        $"Location_Longitude",
        (round($"Location_Latitude" / 2, 2) * 2).cast("String").as("cluster_lat"),
        (round($"Location_Longitude" / 2, 2) * 2).cast("String").as("cluster_long"),
        $"Location_Age".cast("Long"),
        $"BSSID_Carrier",
        lit("5G").as("BSSID_Technology"),
        $"WIFIScan_Manufacturer".as("modem_vendor"),
        $"WIFIScan_ModelName".as("modem_model"))
      .join(broadcast(exclusions), $"Scan_BSSID".startsWith($"short_BSSID"), "left_anti").as[IngestedDataScans]

    result
  }
}
