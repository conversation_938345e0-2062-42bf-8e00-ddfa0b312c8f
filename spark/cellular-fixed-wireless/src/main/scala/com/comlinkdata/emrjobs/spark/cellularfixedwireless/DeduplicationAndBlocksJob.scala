package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.emrjobs.spark.georesolver.GeometryResolver
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.CellularFixedWirelessUtils._
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering.{CustomInclusionData, FilteredDataScans}
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks._
import org.apache.sedona.core.serde.SedonaKryoRegistrator
import org.apache.sedona.sql.utils.SedonaSQLRegistrator
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.{SparkJob, SparkJobRunner}
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.{FiosLookup, ManufacturerLookup, ModemInfoLookup}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.serializer.KryoSerializer
import org.apache.spark.sql.{Dataset, SparkSession, functions}
import org.apache.spark.sql.functions.{ceil, lit, rand}
import org.apache.spark.sql.types.{DoubleType, IntegerType}

import java.net.URI
import java.time.{Duration, LocalDate}


/**
  * Job Configurations
  * @param processEndDateInclusive the end date (inclusive) of the 90 day sliding window being processed. Window size
  *                                unnecessary since it's only used for date partition selection in this step
  * @param geoclusteredLocation geoclustered_location -- URI path to the BssidJoinGeoclusteringJob output dataset
  * @param shortBssidSpecialRulesLocation short_bssid_special_rules_location -- URI path to a reference table specifying
  *                                       proper substring index & length for generating short_bssid based on modem model
  *                                       (any deviations apart from the default contained in lookup table)
  * @param inclusionsLocation URI path of data that did not go through DBScan
  * @param fiosLookupLocation URI path of fios blocks and associated fiber densities with limits
  * @param cbPolygonsLocation cb_polygons_locaton -- URI path to the census block polygon GeoJson files. Should contain the
  *                           degree-based coordinate system with lat-lon pairs
  * @param modemOutputLocation modem_output_location -- desired URI for the output path (no partitions) for modem output
  *                            information. Modem centroids spatial-joined to the degree-based polygons
  * @param serviceTerritoryOutputLocation service_territory_output_location -- desired URI for the output path (no
  *                                       partitions) of the service territory mapping. Centroids distance-joined to
  *                                       census blocks based on the modem's signal frequency
  */
case class DeduplicationAndBlocksJobConfig(
  processEndDateInclusive: Option[LocalDate],
  geoclusteredLocation: URI,
  shortBssidSpecialRulesLocation: URI,
  cbPolygonsLocation: URI,
  inclusionsLocation: URI,
  fiosLookupLocation: URI,
  manufacturerLookupLocation: URI,
  modemLookupLocation: URI,
  modemOutputLocation: URI,
  subsOutputLocation: URI,
  serviceTerritoryOutputLocation: URI,
  businessOutputLocation: URI
)


object DeduplicationAndBlocksJob extends SparkJob(DeduplicationAndBlocksJobRunner){
  /**
    * Register sedona before calling run.  This cannot happen in "configure spark" because it only
    * provides a SparkBuilder at that point
    */
  override def run(config: DeduplicationAndBlocksJobConfig): Duration = {
    SedonaSQLRegistrator.registerAll(spark)
    super.run(config)
  }

  override def configureSpark(builder: SparkSession.Builder): SparkSession.Builder = {
    builder
      .config("spark.kryo.registrator", classOf[SedonaKryoRegistrator].getName)
      .config("spark.serializer", classOf[KryoSerializer].getName)
  }
}



object DeduplicationAndBlocksJobRunner extends SparkJobRunner[DeduplicationAndBlocksJobConfig] with LazyLogging{

  def runJob(config: DeduplicationAndBlocksJobConfig)(implicit spark: SparkSession) = {
    import spark.implicits._

    // 1 -- Dynamic Config Computation
    logger.info("Computing Dynamic Parameters from Config")
    val processEndDateInclusive = config.processEndDateInclusive.getOrElse(getLastWeekdayDate("SATURDAY"))


    // 2 -- Dynamic Path Computation
    val geoclusteredPath = generateYearMonthDayPath(config.geoclusteredLocation, processEndDateInclusive)
    val inclusionsPath = generateYearMonthDayPath(config.inclusionsLocation, processEndDateInclusive)
    val modemOutputPath = generateYearMonthDayPath(config.modemOutputLocation, processEndDateInclusive)
    val subsOutputPath = generateYearMonthDayPath(config.subsOutputLocation, processEndDateInclusive)
    val serviceTerritoryOutputPath = generateYearMonthDayPath(config.serviceTerritoryOutputLocation, processEndDateInclusive)
    val businessOutputPath = generateYearMonthDayPath(config.businessOutputLocation, processEndDateInclusive)


    // 3 -- Input and output path validation
    logger.info("Validating All S3 Paths")
    implicit val cldFileUtils: CldFileUtils = CldFileUtils.newBuilder
      .forUri(
        config.geoclusteredLocation,
        config.shortBssidSpecialRulesLocation,
        config.cbPolygonsLocation,
        config.inclusionsLocation,
        config.fiosLookupLocation,
        config.manufacturerLookupLocation,
        config.modemLookupLocation,
        config.modemOutputLocation,
        config.subsOutputLocation,
        config.serviceTerritoryOutputLocation,
        config.businessOutputLocation
      )(spark).build
    cldFileUtils.allFilesInDirectoryAreReadableOrThrow(URI create geoclusteredPath)
    cldFileUtils.allFilesInDirectoryAreReadableOrThrow(config.shortBssidSpecialRulesLocation)
    cldFileUtils.allFilesInDirectoryAreReadableOrThrow(config.cbPolygonsLocation)
    cldFileUtils.allFilesInDirectoryAreReadableOrThrow(URI create inclusionsPath)
    cldFileUtils.allFilesInDirectoryAreReadableOrThrow(config.fiosLookupLocation)
    cldFileUtils.allFilesInDirectoryAreReadableOrThrow(config.manufacturerLookupLocation)
    cldFileUtils.allFilesInDirectoryAreReadableOrThrow(config.modemLookupLocation)

    // 4 -- read input data
    logger.info(s"Loading Geoclustered Data from Path: ${geoclusteredPath}")
    val geoclustered = spark.read.parquet(geoclusteredPath).as[FilteredDataScans]
    logger.info(s"Loading Inclusion Data from Path: ${inclusionsPath}")
    val inclusions = spark.read.parquet(inclusionsPath).as[CustomInclusionData]


    logger.info(s"Loading Short BSSID Special Rules from Path: ${config.shortBssidSpecialRulesLocation}")
    val shortBssidSpecialRules = ShortBssidSpecialRules.loadRules(config.shortBssidSpecialRulesLocation)
    logger.info(s"Configuring GeometryResolver to use polygons in path: ${config.cbPolygonsLocation}")
    val resolver = GeometryResolver[DeduplicateBssid](config.cbPolygonsLocation)
    logger.info(s"Configuring GeometryResolver for inclusions to use polygons in path: ${config.cbPolygonsLocation}")
    val inclusionResolver = GeometryResolver[CustomInclusionData](config.cbPolygonsLocation)
    logger.info(s"Loading FIOS block lookup table from Path: ${config.fiosLookupLocation}")
    val fiosLookup = FiosLookup.loadLookup(config.fiosLookupLocation).cache()
    logger.info(s"Loading Manufacturer Info from path ${config.manufacturerLookupLocation}")
    val manufacturerInfo = ManufacturerLookup.read(config.manufacturerLookupLocation)
    logger.info(s"Loading Modem Info from path ${config.modemLookupLocation}")
    val modemInfo = ModemInfoLookup.read(config.modemLookupLocation)


    // 5 -- processing step(s)
    logger.info("Extracting appropriate short_BSSIDs")
    val shortBssid = ShortBssid.getShortBssidFromData(geoclustered, shortBssidSpecialRules)
    logger.info("Clustering by short_BSSID")
    val cluster = ClusterBssid.getClustered(shortBssid)
    logger.info("Running Deduplication")
    val dedupe = DeduplicateBssid.getDeduplicate(cluster)
    logger.info("Resolving data to Census Block Polygons")
    var blockJoin = BlockJoinedBssid.getBlockJoined(dedupe, resolver).cache()

    logger.info("Creating block joined Inclusions")
    val blockJoinInclusions = BlockJoinedBssid.getBlockJoinedInclusions(inclusions, inclusionResolver).cache()

    var violation: Boolean = true

    var iteration = 0
    do {
      logger.info(s"Starting iteration number ${iteration} for Fios suppression")

      val totalByCarrier = blockJoin.groupBy($"BSSID_Carrier").count().withColumnRenamed("count", "carrier_total")

      val candidatesWithLookups = blockJoin.join(fiosLookup, Seq("BSSID_Carrier", "block"), "inner").select(
        $"block",
        $"BSSID_Carrier",
        $"BSSID_Technology",
        $"modem_model",
        $"modem_vendor",
        $"short_BSSID",
        $"centroid_lat",
        $"centroid_lon",
        $"first_observed_date",
        $"max_vz_observations".as("max_pct_allowed"),
        $"vz_fiber_density"
      ).as[DensityJoinedBssid]

      val blockTotalByCarrier = candidatesWithLookups.groupBy($"BSSID_Carrier", $"vz_fiber_density").count()

      val candidatesWithCarrier = candidatesWithLookups
        .join(totalByCarrier, Seq("BSSID_Carrier"), "inner")
        .select(
          $"block",
          $"BSSID_Carrier",
          $"BSSID_Technology",
          $"modem_model",
          $"modem_vendor",
          $"short_BSSID",
          $"centroid_lat",
          $"centroid_lon",
          $"first_observed_date",
          $"carrier_total",
          $"max_pct_allowed",
          $"vz_fiber_density"
        )

      val candidatesWithPcts = candidatesWithCarrier
        .join(blockTotalByCarrier, Seq("BSSID_Carrier", "vz_fiber_density"), "inner")
        .withColumn("current_pct", $"count".cast(DoubleType) / $"carrier_total".cast(DoubleType))
        .select(
          $"block",
          $"BSSID_Carrier",
          $"BSSID_Technology",
          $"modem_model",
          $"modem_vendor",
          $"short_BSSID",
          $"centroid_lat",
          $"centroid_lon",
          $"first_observed_date",
          $"carrier_total",
          $"count",
          $"current_pct",
          $"max_pct_allowed",
          $"vz_fiber_density"
        )

      val overMax = candidatesWithPcts
        .where($"current_pct" > $"max_pct_allowed")
        .withColumn("volume_to_remove", ceil(($"count" - $"max_pct_allowed" * $"carrier_total") / (lit(1) - $"max_pct_allowed")))
        .orderBy(rand()).cache()

      val countOverMax = overMax.count()
      violation = !(countOverMax == 0 || iteration == 10)

      val toRemove = overMax.groupBy($"vz_fiber_density", $"BSSID_Carrier").agg(functions.max($"volume_to_remove").cast(IntegerType).as("total_volume")).collect()

      val finalToRemove = toRemove.foldLeft(spark.emptyDataset[DensityJoinedBssid])(
        (sample: Dataset[DensityJoinedBssid], toRemove) => {
          sample.union(candidatesWithLookups.where($"BSSID_Carrier" === toRemove.getAs[String]("BSSID_Carrier") && $"vz_fiber_density" === toRemove.getAs[String]("vz_fiber_density")).limit(toRemove.getAs[Int]("total_volume")))
        })

      blockJoin = blockJoin.join(finalToRemove, Seq("block", "BSSID_Carrier", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", "first_observed_date"), "leftanti")
        .select(
          $"block",
          $"BSSID_Carrier",
          $"BSSID_Technology",
          $"modem_model",
          $"modem_vendor",
          $"short_BSSID",
          $"centroid_lat",
          $"centroid_lon",
          $"first_observed_date"
        ).as[BlockJoinedBssid]

      logger.info(s"Finished with iteration number ${iteration}")
      iteration += 1

    } while (violation)

    val blockJoinUnion = blockJoin.union(blockJoinInclusions)
    logger.info(s"Force-caching blockJoinUnion by logging count: ${blockJoinUnion.count()} records at this stage")
    val businessOutput = BusinessOutput.createFromBlockJoinedBssid(blockJoinUnion, manufacturerInfo, modemInfo)

    val filteredBlockJoinUnion = filterBusinessModels(blockJoinUnion, modemInfo)
    logger.info("Extracting Modem Output Table from Blockjoined data")
    val modemOutput = ModemOutput.createFromBlockJoinedBssid(filteredBlockJoinUnion)
    logger.info("Extracting Subs Output Table from Blockjoined data")
    val subsOutput = SubsOutput.createFromBlockJoined(filteredBlockJoinUnion, processEndDateInclusive)
    logger.info("Final Service Territory Allocation")
    val serviceTerritoryOutput = ExtrapolatedBlocks.getExtrapolatedBlocks(filteredBlockJoinUnion)



    // 6 -- write output
    dynamicOverwrite(ModemOutput.convertToFixedWirelessModemOutput(modemOutput), modemOutputPath)
    dynamicOverwrite(SubsOutput.convertToFixedWirelessSubsOutput(subsOutput), subsOutputPath)
    dynamicOverwrite(ExtrapolatedBlocks.convertToFixedWirelessServiceTerritoryOutput(serviceTerritoryOutput), serviceTerritoryOutputPath)
    dynamicOverwrite(businessOutput, businessOutputPath)
  }

  def filterBusinessModels(blockJoinedUnion: Dataset[BlockJoinedBssid], modemInfo: Dataset[ModemInfoLookup]) (implicit spark: SparkSession): Dataset[BlockJoinedBssid] = {
    import spark.implicits._

    val businessOnly = modemInfo.filter(
      $"Carrier" === "Verizon" && $"BzExclusive"
    ).select("ModemIdentifier").rdd.map(r => r(0)).collect()

    blockJoinedUnion.filter(
      !($"BSSID_Carrier" === "AT&T" && $"modem_vendor" === "ASKEY COMPUTER CORP")
      ).filter(!($"BSSID_Carrier" === "Verizon" && $"modem_model".isInCollection(businessOnly)))
  }
}
