package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering

import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.{ManufacturerLookup, ModemInfoLookup}
import org.apache.spark.sql.functions.{avg, broadcast, lit, lower, regexp_replace, substring, upper}
import org.apache.spark.sql.{Dataset, SparkSession, functions}

/***
  * CarrierAdapter - Adapter for model based approach for inclusions
  *
  * applyPreFiltering - Filters the oncesource data that creates the oui lookup
  *
  * applyAdditionalFilters - Apply additional filters to the scan data retrieved for a carrier
  *
  * processBssids - given the WifiData, bssidLevel, and ouiLookup extract the relevant data from the WifiData
  */
trait CarrierAdapter {
  /***
    * Pre filtering on the onesource data
    * @param data - 90 days worth of onesource data for the given period
    * @param spark - spark
    * @return filtered scans based on the business rules
    */
  def applyPreFiltering(data: Dataset[OnesourceData]) (implicit spark: SparkSession): Dataset[OnesourceData]

  /***
    * Additional Filtering on the wifiData
    * @param data - 90 days worth of wifiScans for the given period
    * @param spark - spark
    * @return filtered wifiScans for given period
    */
  def applyAdditionalFilters(data: Dataset[OnesourceData]) (implicit spark: SparkSession): Dataset[OnesourceData]

  /***
    * Create the dataset from the wifiData and other ouiLookup and bssidLevel data
    * @param bssidLevel - bssid dataset to join with wifiData
    * @param data - 90 days worth of wifiScans for the given period
    * @param ouiLookup - lookup table based on OUIs containing model information
    * @param spark - spark
    * @return the processed inclusion dataset for the given period
    */
  def processBssids(bssidLevel: Dataset[BssidLevel], data: Dataset[OnesourceData], ouiLookup: Dataset[OuiLookup]) (implicit spark: SparkSession) : Dataset[CustomInclusionData]
}

/***
  * AttAdapter - Custom carrier adapter for creating inclusions for AT&T
  *
  * applyPreFiltering - filters the ModelNumber to only include att air models as well as checking null bssids and location info
  *
  * applyAdditionalFilters - check ssids to make sure they contain att
  *
  * processBssids - get the correct records and put them in a format to be used later on
  */
class AttAdapter(manufacturerLookupTable: Dataset[ManufacturerLookup], modemInfoLookup: Dataset[ModemInfoLookup]) extends CarrierAdapter {

  private val manufacturerLookup = manufacturerLookupTable
  private val modemInfo = modemInfoLookup

  override def applyPreFiltering(data: Dataset[OnesourceData])(implicit spark: SparkSession): Dataset[OnesourceData] = {
    import spark.implicits._

    val modemList = modemInfo.filter(
      $"Carrier" === "AT&T"
    ).select($"ModemIdentifier").rdd.map(r => r(0)).collect()

    data.join(broadcast(manufacturerLookup), lower(substring(regexp_replace($"Scan_BSSID", ":", ""), 0, 6)) === lower($"MacPrefix"), "left")
      .filter( ($"Manufacturer" === "ASKEY COMPUTER CORP" && upper($"WIFIScan_SSID").like("ATT-WIFI%")
        || $"WIFIScan_ModelNumber".isInCollection(modemList))
        && $"Scan_BSSID".isNotNull
    ).as[OnesourceData]
  }

  override def applyAdditionalFilters(data: Dataset[OnesourceData])(implicit spark: SparkSession): Dataset[OnesourceData] = {
    import spark.implicits._

    data.filter(
      lower($"WIFIScan_SSID").like("%att%")

    ).as[OnesourceData]
  }

  override def processBssids(bssidLevel: Dataset[BssidLevel], data: Dataset[OnesourceData], ouiLookup: Dataset[OuiLookup])(implicit spark: SparkSession): Dataset[CustomInclusionData] = {
    import spark.implicits._

     bssidLevel.join(data, Seq("Scan_BSSID"), "left")
      .groupBy("Scan_BSSID").agg(functions.min("QOS_Date").as("first_observed_date"),
        avg($"Location_Latitude").as("centroid_lat"), avg($"Location_Longitude").as("centroid_lon"))
       .join(ouiLookup, substring($"Scan_BSSID", 0, 11) === $"OUI_LONG", "inner")
       .join(broadcast(manufacturerLookup), lower(substring(regexp_replace($"Scan_BSSID", ":", ""), 0, 6)) === lower($"MacPrefix"), "left")
      .withColumn("BSSID_Carrier", lit("AT&T"))
      .withColumn("BSSID_Technology", lit("5G"))
      .select($"BSSID_Carrier",
        $"BSSID_Technology",
        $"WIFIScan_ModelName".as("modem_model"),
        $"Manufacturer".as("modem_vendor"),
        $"Scan_BSSID".as("short_BSSID"),
        $"centroid_lat",
        $"centroid_lon",
        $"first_observed_date").as[CustomInclusionData]
  }
}

/**
  * Custom Carrier Adapter for Verizon
  *
  * applyPreFiltering - filters based on model names as well as nonNull Bssid and location info
  *
  * applyAdditionalFilters - no additional business rules were provided for vzn filtering
  *
  * processBssids - Not necessary for vzn given the fact that they need to be passed through dbscan
  */
class VznAdapter(modemInfoLookup: Dataset[ModemInfoLookup]) extends CarrierAdapter {

  private val modemInfo = modemInfoLookup

  override def applyPreFiltering(data: Dataset[OnesourceData])(implicit spark: SparkSession): Dataset[OnesourceData] = {
    import spark.implicits._

    val modemList = modemInfo.filter(
      $"Carrier" === "Verizon"
    ).select($"ModemIdentifier").rdd.map(r => r(0)).collect()

    data.filter(
      upper($"WIFIScan_ModelName").isInCollection(modemList)
        && $"Scan_BSSID".isNotNull &&
        (
          ($"Location_Age" <= 3600 && $"Location_HorizontalAccuracy".isNull)
            || $"Location_HorizontalAccuracy" <= 1000
          )
    ).as[OnesourceData]
  }

  override def applyAdditionalFilters(data: Dataset[OnesourceData])(implicit spark: SparkSession): Dataset[OnesourceData] = {
    data
  }

  override def processBssids(bssidLevel: Dataset[BssidLevel], data: Dataset[OnesourceData], ouiLookup: Dataset[OuiLookup])(implicit spark: SparkSession): Dataset[CustomInclusionData] = {
    val data: Dataset[CustomInclusionData] = null
    data
  }

}