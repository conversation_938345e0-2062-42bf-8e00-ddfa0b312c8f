package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks

import org.apache.sedona.core.formatMapper.GeoJsonReader
import org.apache.spark.sql.{Dataset, SparkSession}
import org.locationtech.jts.geom.Geometry
import org.apache.sedona.sql.utils.Adapter

import java.net.URI

/**
  * StPolygons -- Internal Reference table dataset for the Service Territory Polygons
  * @param serv_terr_blockid the census block (service territory) block ID value as a string
  * @param geometry the polygons representing the associated census block
  */
case class StPolygons(
  serv_terr_blockid: String,
  geometry: Geometry
)


object StPolygons {

  /**
    * Using Sedona's API, read the census blocks polygons from the given path into a dataframe with the desired schema
    * @param path URI path to GeoJson file(s) containing census block polygons
    * @param spark
    * @return
    */
  def loadPolygonsAsDataset(path: URI)(implicit spark: SparkSession): Dataset[StPolygons] = {
    import spark.implicits._
    val skipSyntacticallyInvalidGeometries = true
    val allowInvalidGeometrics = false
    val polys = GeoJsonReader.readToGeometryRDD(spark.sparkContext, path.toString, allowInvalidGeometrics, skipSyntacticallyInvalidGeometries)
    Adapter
      .toDf(polys, Seq("serv_terr_blockid"), spark)
      .select($"serv_terr_blockid", $"geometry")
      .as[StPolygons]
  }
}
