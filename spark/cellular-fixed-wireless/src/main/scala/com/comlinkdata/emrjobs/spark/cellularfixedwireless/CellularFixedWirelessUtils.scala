package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import org.apache.spark.sql.functions.{col, lit, udf}
import org.locationtech.jts.geom.Point
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.algo.DBScan

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering.OnesourceData
import com.comlinkdata.largescale.commons.{LocalDateRange, TimeSeriesLocation}
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import org.apache.spark.sql.{DataFrame, Dataset, SaveMode, SparkSession}
import com.typesafe.scalalogging.LazyLogging

import java.util.concurrent.ConcurrentHashMap
import java.net.URI
import java.time.{DayOfWeek, LocalDate}
import java.time.temporal.{TemporalAdjuster, TemporalAdjusters}
import scala.collection.immutable.HashMap



object CellularFixedWirelessUtils extends LazyLogging {

  // Shared Attributes
  var memo: ConcurrentHashMap[String, HashMap[Point,Int]] = new ConcurrentHashMap  // {key:cluster} used for non-binned dbscan
  var memoBins: ConcurrentHashMap[String, HashMap[Point,Int]] = new ConcurrentHashMap // {key:cluster} used for binned dbscan
  private val RADIUS: Double = 6378137.0  // radius of the earth, used for lat/lon to meter conversions


  private def getTemporalAdjuster(dayOfWeek: String): TemporalAdjuster = {
    val dow = dayOfWeek.toUpperCase() match {
      case "SUNDAY" | "SUN" => DayOfWeek.SUNDAY
      case "MONDAY" | "MON" => DayOfWeek.MONDAY
      case "TUESDAY" | "TUE" | "TUES" => DayOfWeek.TUESDAY
      case "WEDNESDAY" | "WED" => DayOfWeek.WEDNESDAY
      case "THURSDAY" | "THU" | "THR" => DayOfWeek.THURSDAY
      case "FRIDAY" | "FRI" => DayOfWeek.FRIDAY
      case "SATURDAY" | "SAT" => DayOfWeek.SATURDAY
    }
    TemporalAdjusters.previous(dow)
  }
  def getLastWeekdayDate(dayOfWeek: String="SATURDAY", date: LocalDate=LocalDate.now()): LocalDate = {
    date.`with`(getTemporalAdjuster(dayOfWeek))
  }


  /**
    * Compute the end-of-quarter date for the LocalDate given
    * @param d LocalDate containing the deired date to extract the end-of-quarter from. Default = current date
    * @return
    */
  def getQuarterEndDate(d: LocalDate=LocalDate.now()): LocalDate = {
    val month = d.getMonthValue
    if (month <= 3) LocalDate.of(d.getYear, 3, 31)  // Q1 ends on 31 March
    else if (month <= 6) LocalDate.of(d.getYear, 6, 30)  // Q2 ends on 30 June
    else if (month <= 9) LocalDate.of(d.getYear, 9, 30)  // Q3 ends on 30 September
    else LocalDate.of(d.getYear, 12, 31)  // Q4 ends on 31 December
  }


  /**
    * Compute the start-of-quarter date for the LocalDate given
    * @param d LocalDate containing the deired date to extract the start-of-quarter from. Default = current date
    * @return
    */
  def getQuarterStartDate(d: LocalDate=LocalDate.now()): LocalDate = {
    val month = d.getMonthValue
    if (month <= 3) LocalDate.of(d.getYear, 1, 1)  // Q1 starts Jan 1
    else if (month <= 6) LocalDate.of(d.getYear, 4, 1)  // Q2 starts Apr 1
    else if (month <= 9) LocalDate.of(d.getYear, 7, 1)  // Q3 starts Jul 1
    else LocalDate.of(d.getYear, 10, 1)  // Q4 starts Oct 1
  }


  /**
    * Given a base path to and a range of dates, produce a sequence of strings of the form "base/path/date={date in range}"
    *
    * @param basePath URI containing the base path for which we will add date partitons to. Supported with or without trailing /
    * @param dateRange LocalDateRange object. Each date in range added to base path as new parition path in the output sequence
    */
  def generateScanDataPaths(basePath: URI, dateRange: LocalDateRange, partitionName: String = "scan_date")(implicit spark: SparkSession): Seq[String] = {
    TimeSeriesLocation
      .ofDatePartitions(source=basePath, datePartitionName=partitionName)
      .build
      .validInputPartitionsOrThrow(dateRange)
  }


  /**
    * Returns a UDF for performing DBScan on the dataframe records. This version of the method performs DBScan without
    * binning (each individual record gets scanned). Uses memo to store particular key/cluster pairs, to avoid re-clustering
    * the same window multiple times. Not tested in utils spec because its specific use-cases are tested in the corresponding
    * job specs
    * @param eps distance in meters for the DBScan to use
    * @param minGeogs minimum number of geographies needed to form a cluster in DBScan
    * @return
    */
  def stClusterDbScan(eps: Int, minGeogs: Int) = {
    udf {
      (geogs: IndexedSeq[Point], key: String, pt: Point) => {
        if (memo.contains(key)){
          memo.get(key)(pt)
        }
        else {
          val (labels, clusterStats) = DBScan.clusterGPS(geogs, eps, minGeogs)
          val mapping = HashMap(geogs.zip(labels).distinct.map(x => x._1 -> x._2): _*)
          memo.put(key, mapping)
          mapping.getOrElse(pt,-1)
        }
      }
    }
  }


  /**
    * Returns a UDF for performing Binned DBScan on the dataframe records. This version of the method performs DBScan with
    * binning (weighted scan based on count of duplicate locations). Uses memoBins to store particular key/cluster pairs,
    * to avoid re-clustering the same window multiple times. Not tested in utils spec because its specific use-cases are
    * tested in the corresponding job specs
    * @param eps distance in meters for the DBScan to use
    * @param minGeogs minimum number of geographies needed to form a cluster in DBScan
    * @param spark
    * @return
    */
  def stClusterDbScanWithBinning(eps: Int, minGeogs: Int) = {
    udf {
      (geogs: IndexedSeq[Point], key: String, pt: Point) => {
        if (memo.contains(key) && memo.get(key).contains(pt)){
          memoBins.get(key).getOrElse(pt, -1)
        }
        else {
          val (bins, labels, centroids) = DBScan.clusterGPSWithBinning(geogs, eps, minGeogs)
          val mapping = HashMap(bins.zip(labels).map(x => x._1._2 -> x._2): _*)
          memoBins.put(key, mapping)
          mapping.getOrElse(pt, -1)
        }
      }
    }
  }


  /**
    * Convert a string with simple SQL wildcards to their regex equivalent for use in spark's rlike().
    * Replacing % with .*? and _ with . (No other regex components are used in the pattern filters for CFW.
    * Method will need to be updated if additional wildcard chars are used later
    * @param w: the string containing the SQL wildcard
    * @return string containing the equivalent wildcard with % and _ replaced with their regex equivalents
    */
  def wildcardToRegex(w: String) = {
    "(^" + w.replace(".","\\.").replace("%", ".*?").replace("\\_","_") + "$)"
  }


  /**
    * Condense the entire column of SQL wildcard-like patterns into a single regex string. Collect all wildcards as strings,
    * Map each wildcard string to regex using wildcardToRegex() above, then reducee the list by concatenating elements\
    * using the regex OR operator |. Not tested in utils spec because its specific use-cases are tested in the corresponding
    * job specs
    * @param data Dataframe to extract the wildcards from
    * @param columnName name of the column in data which contains the wildcards
    * @param spark
    * @return a single String containing a regex pattern to evaluate all wildcards in data as regex
    */
  def getRegexFromFilterTable(data: DataFrame, columnName: String)(implicit spark: SparkSession) = {
    import spark.implicits._
    data.select(col(columnName)).as[String].collect().map(wildcardToRegex).reduce(_ + "|" + _)
  }


  /**
    * Returns a UDF for converting a latitude value into meter-based Mercador Web (EPSG:3857)
    * UDF expects the latitude (as a Double) as input and returns a double containing the conversion results
    * @return
    */
  def latToM = {
    udf{
      (lat: Double) => {
        (Math.log(Math.tan((90d+lat) * Math.PI/360d)) / (Math.PI/180d)) * 20037508.34 / 180d
      }
    }
  }



  /**
    * Returns a UDF for converting a longitude value into meter-based Mercador Web (EPSG:3857)
    * UDF expects the longitude (as a Double) as input and returns a double containing the conversion results
    * @return
    */
  def lonToM = {
    udf{
      (lon: Double) => {
        Math.toRadians(lon) * RADIUS
      }
    }
  }


  /**
    * Method for adding year/month/day partitions to the given uri path using the given LocalDate
    * @param basePath URI for the partitions to be appended to
    * @param date local date corresponding to the added partitions
    * @return
    */
  def generateYearMonthDayPath(basePath: URI, date: LocalDate)(implicit spark: SparkSession): String = {
    TimeSeriesLocation.ofYmdDatePartitions(basePath).build.partition(date)
  }


  /**
    * Used for dynamically overwriting output files when the specified path already exists, otherwise write in append mode
    * No unit tests for method since writing/overwriting output to URI is involved. Has proven successful in testing environments
    * on S3, however.
    * @param data dataset to be written
    * @param path URI (with partitions) to be written into
    * @param spark
    * @param fileUtils
    * @tparam T
    */
  def dynamicOverwrite[T](data: Dataset[T], path: String, doLogging: Boolean=true)(implicit spark: SparkSession, fileUtils: CldFileUtils) = {
    if (fileUtils.exists(URI create path)) {
      if (doLogging) { logger.warn(s"Output path already exists -- contents overwritten in path: ${path}") }
      data.write.mode(SaveMode.Overwrite).parquet(path)
    }
    else {
      if (doLogging) { logger.info(s"Writing output data to path: ${path}") }
      data.write.mode(SaveMode.Append).parquet(path)
    }
  }

  /**
    * Add a column of nulls for every column that the given data is missing to ensure appropriate schema when
    * converting to WiFi scans. This method is needed to adapt older records, since values are
    * @param data dataframe to be converted into model.WiFiScans schema by adding null columns as needed
    * @param spark
    * @return
    */
  def adaptToWifiScans(data: DataFrame)(implicit spark: SparkSession): Dataset[OnesourceData] = {
    import spark.implicits._
    val dataCols = data.columns
    val missing = spark.emptyDataset[OnesourceData]
      .columns.toSeq
      .filter(!dataCols.toSeq.contains(_))
      .map{ colName:String => lit(null).as(colName) }
    if (missing.isEmpty) { data.as[OnesourceData] }
    else { data.select(dataCols.map(col) ++ missing: _*).as[OnesourceData] }
  }
}
