package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.model.LuDeviceFilterMapping
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import org.apache.spark.sql.functions.{broadcast, length, upper}
import org.apache.spark.sql.{Dataset, SparkSession}

import java.net.URI


/**
  * MappingBssid - internal view dataset containing carrier-allocation (PersistDataToFWFABlockMappingJob output) and maps
  * the results to their appropriate carrier information based on LU_DEVICE_FILTER_MAPPING lookup table
  * @param BLOCK_X the associated block_x relevant to the scan record
  * @param block_length value of the block (length of the block x)
  * @param CARRIER the carrier associated with the modem
  * @param TYPE the device type associated with the block_x
  * @param MAX_TECHNOLOGY the highest level of wireless technology associated with the data
  * @param VENDOR the vendor associated with the device
  * @param MODEL the device model
  */
case class MappingBssid(
  BLOCK_X: String,
  block_length: Int,
  CARRIER: String,
  TYPE: String,
  MAX_TECHNOLOGY: String,
  VENDOR: String,
  MODEL: String
)


object MappingBssid {

  /**
    * Given the Paths for FwFaBlockMapping and LuDeviceFilterMapping, validate the paths, load the tables, and join them
    * together to produce the appropriate view for Mapping_BSSID. Also perform filtering to only desired carriers at this
    * stage to save on runtime performance later.
    * @param fwFaBlockMappingPath PersistDataToFWFABlockMappingJob output path for the current processing quarter
    * @param luDeviceFilterMappingLocation S3 uri of the LuDeviceFilterMapping table for mapping SSID patterns to carriers
    * @param spark
    * @param fileUtils
    * @return
    */
  def createFromDataPaths(fwFaBlockMappingPath: URI, luDeviceFilterMappingLocation: URI, targetCarriers: List[String])(implicit spark: SparkSession, fileUtils: CldFileUtils): Dataset[MappingBssid] = {
    import spark.implicits._

    // Validate Input Paths
    fileUtils.allFilesInDirectoryAreReadableOrThrow(fwFaBlockMappingPath)
    fileUtils.allFilesInDirectoryAreReadableOrThrow(luDeviceFilterMappingLocation)

    // Load Data into Memory
    val fwFaBlockMapping = spark.read.parquet(fwFaBlockMappingPath.toString)
    val filterMapping = spark.read.option("header", "true")
      .csv(luDeviceFilterMappingLocation.toString)
      .as[LuDeviceFilterMapping]

    // Join and Select Desired Columns
    fwFaBlockMapping
      .join(
        broadcast(filterMapping.where(upper($"ENABLED") === "TRUE" && $"type" === "fixed")),
        Seq("SSID_PATTERN"),
        "left"
      )
      .where($"CARRIER".isInCollection(targetCarriers))
      .select(
        $"BLOCK_X",
        length($"BLOCK_X").as("block_length"),
        $"CARRIER",
        $"TYPE",
        $"MAX_TECHNOLOGY",
        $"VENDOR",
        $"MODEL"
      )
      .as[MappingBssid]
  }
}
