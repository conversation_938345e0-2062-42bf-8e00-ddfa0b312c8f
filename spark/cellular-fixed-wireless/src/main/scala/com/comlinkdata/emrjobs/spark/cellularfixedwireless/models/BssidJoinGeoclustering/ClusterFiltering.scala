package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering

import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Dataset, SparkSession}


/**
  * ClusterFilteringScans -- intermediary table containing the clusters collapsed into a single representative point
  * @param Scan_BSSID BSSID associated with the device scanned
  * @param Scan_SSID SSID associated with the device scanned
  * @param cluster_match "match" or "bad match" flag to indicate whether a point is the representative of its cluster. Output should
  *                      only have "match" values
  * @param cluster_num cluster number assigned to the record as a result of running DBScan to all matching BSSIDs with 2 points of location precision
  * @param count_best_clusters number of clusters in which a point matches the best/representative point of the cluster
  */
case class ClusterFilteringScans(
  Scan_BSSID: String,
  Scan_SSID: String,
  cluster_match: String,
  cluster_num: java.lang.Long,
  count_best_clusters: java.lang.Long
)



object ClusterFiltering {

  /**
    * Perform determine the metrics for the "best cluster" per BSSID/SSID pair
    * This private function determines this best cluster for each ssid-bssid pair
    * @param aggDataScans AggdataScans subquery results for performing aggregation
    * @param spark
    * @return
    */
  private def getMaxdataScans(aggDataScans: Dataset[AggDataScans])(implicit spark: SparkSession) = {
    import spark.implicits._
    aggDataScans
      .groupBy($"Scan_BSSID", $"Scan_SSID")
      .agg(
        max($"numrecords").as("max_records"),
        max($"count_UID").as("max_UID"),
        min($"earliest_date").as("min_timestamp")
      )
  }


  /**
    * Filter the results from AggDataScans by determining the "best" cluster for each BSSID/SSID pair, then filtering
    * AggDataScans values to only records within ther respective "best cluster"
    * @param aggDataScans AggdataScans subquery table outputs for use in aggregation/processing
    * @param spark
    * @return
    */
  def createFromScans(aggDataScans: Dataset[AggDataScans])(implicit spark: SparkSession): Dataset[ClusterFilteringScans] = {
    import spark.implicits._
    val window = Window.partitionBy($"Scan_BSSID", $"Scan_SSID")
    val maxdata = getMaxdataScans(aggDataScans)
    aggDataScans
      .join(maxdata, Seq("Scan_BSSID", "Scan_SSID"), "inner")
      .withColumn(
        "cluster_match",
        when($"numrecords" === $"max_records" && $"max_UID" === $"count_UID", lit("match"))
          .otherwise(lit("bad match"))
      )
      .where($"cluster_match" === "match")
      .select(
        $"Scan_BSSID",
        $"Scan_SSID",
        $"cluster_match",
        $"cluster_num",
        count($"*").over(window).as("count_best_clusters")
      )
      .as[ClusterFilteringScans]
  }
}
