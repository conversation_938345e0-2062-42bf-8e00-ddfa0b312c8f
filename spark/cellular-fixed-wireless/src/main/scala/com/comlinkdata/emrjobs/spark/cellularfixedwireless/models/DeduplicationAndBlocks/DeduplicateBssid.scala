package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks

import org.apache.spark.sql.{Dataset, SparkSession}
import org.apache.spark.sql.functions.{expr, mean, min}


/**
  * DeduplicateBssid -- intermediary dataset with the post-deduplication records
  * @param BSSID_Carrier carrier identified for the modem
  * @param BSSID_Technology modem technology identified
  * @param modem_model model of the identified modem
  * @param modem_vendor vendor of the identified modem
  * @param short_BSSID bssid substring to identify modem
  * @param centroid_lat centroid latitude of all locations of the same modem
  * @param centroid_lon centroid longitude of all locations of the same modem
  * @param first_observed_date earliest date the modem was observed this quarter
  */
case class DeduplicateBssid(
  BSSID_Carrier: String,
  BSSID_Technology: String,
  modem_model: String,
  modem_vendor: String,
  short_BSSID: String,
  centroid_lat: Double,
  centroid_lon: Double,
  first_observed_date: java.sql.Timestamp,
)


object DeduplicateBssid{

  /**
    * Perform deduplication by collapsing all duplicate points into a representative centroid for the data
    * @param cluster results of the cluster subquery on which deduplication is to be performed
    * @param spark
    * @return
    */
  def getDeduplicate(cluster: Dataset[ClusterBssid])(implicit spark: SparkSession): Dataset[DeduplicateBssid] = {
    import spark.implicits._
    cluster
      .withColumn("lon", expr("ST_X(aggregate_centroid)"))
      .withColumn("lat", expr("ST_Y(aggregate_centroid)"))
      .groupBy(
        $"BSSID_Carrier",
        $"BSSID_Technology",
        $"modem_model",
        $"modem_vendor",
        $"short_BSSID",
        $"clusternum"
      )
      .agg(
        mean($"lat").as("centroid_lat"),
        mean($"lon").as("centroid_lon"),
        min($"earliest_date").as("first_observed_date")
      )
      .select(
        $"BSSID_Carrier",
        $"BSSID_Technology",
        $"modem_model",
        $"modem_vendor",
        $"short_BSSID",
        $"centroid_lat",
        $"centroid_lon",
        $"first_observed_date"
      )
      .as[DeduplicateBssid]
  }
}
