package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering

import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Dataset, SparkSession}
import org.locationtech.jts.geom.Geometry


/**
  * AggDataScans -- Intermediary Dataset for aggregating the clusters
  * @param Scan_BSSID BSSID associated with the device scanned
  * @param Scan_SSID SSID associated with the device scanned
  * @param BSSID_Carrier Carrier associated with the device scanned
  * @param BSSID_Technology Technology associated with the device scanned
  * @param modem_vendor vendor associated with the modem scanned
  * @param modem_model model associated with the model scanned
  * @param cluster_num cluster number resulting from DBScan clustering of BSSIDs within a 2-point location precision
  * @param aggregate_centroid centroid results of aggregating all points within the same cluster
  * @param count_UID count of all UIDs within the same cluster
  * @param numrecords count the number of records within the same cluster
  * @param earliest_date earliest observed date of all points within the same cluster for processing quarter
  */
case class AggDataScans(
  Scan_BSSID: String,
  Scan_SSID: String,
  BSSID_Carrier: String,
  BSSID_Technology: String,
  modem_vendor: String,
  modem_model: String,
  cluster_num: java.lang.Long,
  aggregate_centroid: Geometry,
  count_UID: java.lang.Long,
  numrecords: java.lang.Long,
  earliest_date: java.sql.Timestamp
)


object AggData {

  /**
    * Aggregate each device's specific clustger to find all unique devices in the cluster, total number of records for
    * the cluster, the earliest record in each cluster, and the centroid for the cluster.
    * @param clusteredDataScans ClusteredDataScans subquery resoults for performing the aggregation on
    * @param spark
    * @return
    */
  def createFromScans(clusteredDataScans: Dataset[ClusteredDataScans])(implicit spark: SparkSession): Dataset[AggDataScans] = {
    import spark.implicits._
    clusteredDataScans
      .where($"cluster_num".isNotNull && $"cluster_num" =!= -1)  //bigquery sets noise to null, we set it to -1
      .groupBy(
        $"Scan_BSSID",
        $"Scan_SSID",
        $"BSSID_Carrier",
        $"BSSID_Technology",
        $"modem_vendor",
        $"modem_model",
        $"cluster_num"
      )
      .agg(
        mean($"Location_Latitude").as("meanLat"),
        mean($"Location_Longitude").as("meanLon"),
        countDistinct($"Device_UID").as("count_UID"),
        count($"*").as("numrecords"),
        min($"Scan_Timestamp").as("earliest_date")
      )
      .withColumn("aggregate_centroid", expr("ST_POINT(CAST(meanLon as DECIMAL(16,12)), CAST(meanLat as DECIMAL(16,12)))"))
      .drop("meanLat")
      .drop("meanLon")
      .as[AggDataScans]
  }
}
