package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.CellularFixedWirelessUtils.{dynamicOverwrite, generateYearMonthDayPath, getLastWeekdayDate}
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BlockXLoopCounts._
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.model._
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON>, SparkJobRunner, Utils}
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.BssidExclusionsLookup
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.DoubleType

import java.net.URI
import java.sql.Timestamp
import java.time.LocalDate
import java.util.UUID
import scala.util.Try

case class BlockXLoopCountsConfig (
  tmpQuarterPatternMatchedBlockRecordsLocation: URI,
  luDeviceFilterMappingLocation: URI,
  bssidExclusionsLocation: URI,
  mappedBlocksLocation: URI,
  bssidCountThresholdSmall: Int,
  bssidCountThresholdLarge: Int,
  bssidPatternMatchBlockCntThreshold: Double,
  bssidMaxPatternBlankBlockCntThreshold: Double,
  bssidMaxFiosBlockCntThreshold: Double,
  bssidMaxIotBlockCntThreshold: Double,
  bssidMaxSpectrumBlockCntThreshold: Double,
  leftSsidBlockCntThreshold: Double,
  blockLengths: List[Int],
  processEndDateInclusive: Option[LocalDate]
)

/**
  * Intermediate schema for BlockXLoopCounts: BLOCK_X values with corresponding counts and SSID_PATTERN
  * @param BLOCK_X blockLength substring of SCAN_BSSID, used to identify groups
  * @param SSID_PATTERN pattern used to identify wifi, IE NETGEAR ATT-WIFI etc.
  * @param LEFT_SSID ID following occurence of SSID_PATTERN in SCAN_SSID
  * @param MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT count of distinct BSSIDs that match the SSID_PATTERN
  * @param TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT count of SCAN_SSIDs that are null or empty
  * @param TOTAL_BLOCK_X_FIOS_CNT count of SCAN_SSIDs that contain "fios" (case insensitive)
  * @param TOTAL_BLOCK_X_IOT_CNT count of SCAN_SSIDs that contain "iot" (case insensitive)
  * @param TOTAL_BLOCK_X_SPECTRUM_CNT count of SCAN_SSIDs that contain "spectrum" (case insensitive)
  * @param TOTAL_BLOCK_X_BSSID_CNT count of distinct BSSIDs for the value of BLOCK_X
  * @param MAX_LEFT_SSID_BSSID_CNT count of unique BSSIDs in BLOCK_X with the same LEFT_SSID, not in
  *                                MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT
  */
case class CountsByX(
  BLOCK_X: String,
  SSID_PATTERN: String,
  LEFT_SSID: String,
  MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: Long,
  TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: Long,
  TOTAL_BLOCK_X_FIOS_CNT: Long,
  TOTAL_BLOCK_X_IOT_CNT: Long,
  TOTAL_BLOCK_X_SPECTRUM_CNT: Long,
  TOTAL_BLOCK_X_BSSID_CNT: Long,
  MAX_LEFT_SSID_BSSID_CNT: Long
)

object BlockXLoopCountsJob extends SparkJob(BlockXLoopCountsJobRunner)

object BlockXLoopCountsJobRunner extends SparkJobRunner[BlockXLoopCountsConfig] with LazyLogging {

  def runJob(config: BlockXLoopCountsConfig)(implicit spark: SparkSession): Unit =  {
    import spark.implicits._

    val processEndDateInclusive = config.processEndDateInclusive.getOrElse(getLastWeekdayDate("SATURDAY"))

    val patternMatchedPath = generateYearMonthDayPath(config.tmpQuarterPatternMatchedBlockRecordsLocation, processEndDateInclusive)
    val outputPath = generateYearMonthDayPath(config.mappedBlocksLocation, processEndDateInclusive)

    implicit val cldFileUtils = CldFileUtils.newBuilder
      .forUri(
        config.tmpQuarterPatternMatchedBlockRecordsLocation,
        config.luDeviceFilterMappingLocation,
        config.bssidExclusionsLocation,
        config.mappedBlocksLocation
      )(spark).build

    // required! - will work only in EMR. To test locally we would need file:// one. Maybe move to config
    spark.sparkContext.setCheckpointDir("hdfs:/spark-checkpoints/")

    logger.info(s"Loading Lookup: ${config.bssidExclusionsLocation}")
    var bssidExclusions = BssidExclusionsLookup.read(config.bssidExclusionsLocation)

    logger.info(s"loading : ${config.tmpQuarterPatternMatchedBlockRecordsLocation}")
    var tmpQuarterPatternMatchedBlockRecords = spark.read
      .parquet(patternMatchedPath)
      .as[FwTmpQuarterPatternMatchedBlockRecords]
      .transform(removeBssidExclusions(bssidExclusions))
      .cache()

    logger.info(s"loading : ${config.mappedBlocksLocation}")

    // check if mapped_blocks exists
    val tmpMappedBlocks = {
      Try(spark.read.parquet(config.mappedBlocksLocation.toString).as[FwTmpMappedBlocks]).toOption match {
        case Some(ds) => ds
        case None =>
          logger.warn("prior partitions do not exist/cannot be read")
          spark.emptyDataset[FwTmpMappedBlocks]
      }
    }

    logger.info(s"loading : ${config.luDeviceFilterMappingLocation}")
    val luDeviceFilters = spark.read
      .option("header","true")
      .csv(config.luDeviceFilterMappingLocation.toString)
      .as[LuDeviceFilterMapping]
      .cache()

    val uuid = UUID.randomUUID().toString()
    val runTimestamp = new Timestamp(System.currentTimeMillis())

    // iterate through all values of blockLengths

    var filteredOutputCombined = spark.emptyDataset[CountsByX]
    val ssidFilters = getSSIDFilters(luDeviceFilters).as[SsidFiltersLtrl].cache()

    for (i <- config.blockLengths.indices) {

      val blockX = getBlockX(i, config.blockLengths(i), config.bssidCountThresholdSmall, tmpQuarterPatternMatchedBlockRecords, tmpMappedBlocks).cache()

      val blockXMatchCounts = getBlockXMatchCounts(tmpQuarterPatternMatchedBlockRecords,ssidFilters,config.blockLengths(i))

      val ABJoin = blockX.join(blockXMatchCounts,Seq("BLOCK_X"),"inner")

      val blankBlockXCounts = getBlankBlockXCounts(tmpQuarterPatternMatchedBlockRecords,config.blockLengths(i))

      val fiosBlockXCounts = getFiosBlockXCounts(tmpQuarterPatternMatchedBlockRecords, config.blockLengths(i))

      val iotBlockXCounts = getIotBlockXCounts(tmpQuarterPatternMatchedBlockRecords, config.blockLengths(i))

      val spectrumBlockXCounts = getSpectrumBlockXCounts(tmpQuarterPatternMatchedBlockRecords, config.blockLengths(i))

      val dInput = ABJoin
        .join(blankBlockXCounts,Seq("BLOCK_X"),"left")
        .join(fiosBlockXCounts,Seq("BLOCK_X"),"left")
        .join(iotBlockXCounts, Seq("BLOCK_X"), "left")
        .join(spectrumBlockXCounts, Seq("BLOCK_X"), "left")

      val intData = applyIntDataFilters(dInput,blockX,config.bssidCountThresholdSmall,config.bssidPatternMatchBlockCntThreshold,config.bssidMaxPatternBlankBlockCntThreshold, config.bssidMaxFiosBlockCntThreshold, config.bssidMaxIotBlockCntThreshold, config.bssidMaxSpectrumBlockCntThreshold).cache()

      val countsByLeftSSID = getCountsByLeftSSID(tmpQuarterPatternMatchedBlockRecords,intData,config.blockLengths(i))

      val filteredOutput = applyLeftSSIDFilters(countsByLeftSSID,intData,config.leftSsidBlockCntThreshold).as[CountsByX].cache()

      if (i < config.blockLengths.size - 1)  {
        tmpQuarterPatternMatchedBlockRecords = tmpQuarterPatternMatchedBlockRecords
          .join(filteredOutput,substring($"SCAN_BSSID",0,config.blockLengths(i)) === $"BLOCK_X","left_anti")
          .select($"SCAN_BSSID",$"SCAN_SSID")
          .as[FwTmpQuarterPatternMatchedBlockRecords]
          .cache()
          .checkpoint()
      }
      filteredOutputCombined = filteredOutputCombined.union(filteredOutput).cache()

      blockX.unpersist()
      intData.unpersist()
      filteredOutput.unpersist()
    }

    val mappedBlocksOutput = filteredOutputCombined
      .withColumn("UUID",lit(uuid))
      .withColumn("RUN_TIMESTAMP",lit(runTimestamp))
      .select($"UUID",$"RUN_TIMESTAMP",$"BLOCK_X",$"SSID_PATTERN", $"LEFT_SSID", $"MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT", $"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT", $"TOTAL_BLOCK_X_BSSID_CNT", $"TOTAL_BLOCK_X_FIOS_CNT", $"TOTAL_BLOCK_X_IOT_CNT", $"TOTAL_BLOCK_X_SPECTRUM_CNT", $"MAX_LEFT_SSID_BSSID_CNT")
      .as[MappedBlocks].cache()
    val forceProcess = mappedBlocksOutput.count() // calling count in order to force the dataframe to be processed before potential output overwrite
    logger.info(s"Writing ${forceProcess} records to S3")
    dynamicOverwrite(mappedBlocksOutput, outputPath)
  }


  /**
    * Remove the BSSID block-8 exclusions from the BSSID-SSID pairs
    * @param exclusions lookup of BSSIDs block-8 substrings to exclude
    * @param input source table to have the BSSIDs removed from
    * @param spark
    * @return
    */
  def removeBssidExclusions(exclusions: Dataset[BssidExclusionsLookup])(input: Dataset[FwTmpQuarterPatternMatchedBlockRecords])(implicit spark: SparkSession): Dataset[FwTmpQuarterPatternMatchedBlockRecords] = {
    import spark.implicits._
    input
      .join(broadcast(exclusions), $"SCAN_BSSID".startsWith($"short_BSSID"), "left_anti")
      .as[FwTmpQuarterPatternMatchedBlockRecords]
  }


  /**
    * Computes BLOCK_X, the block of length blockLength[i] in the SCAN_BSSID with it's number of occurences
    * @param iteration the value in blockLengths being used: the first iteration has different behavior
    * @param blockLength the length of BLOCK_X
    * @param bssidCountThresholdSmall filter for minimum amount of occurences of a SCAN_BSSID to be counted
    * @param blockRecords output from step 2, all pairs of SCAN_SSID's and SCAN_BSSID's
    * @return BLOCK_X: String, TOTAL_BLOCK_X_BSSID_CNT: java.lang.Long
    */
  def getBlockX(iteration: Int, blockLength: Int, bssidCountThresholdSmall: Int, blockRecords: Dataset[FwTmpQuarterPatternMatchedBlockRecords], mappedBlocks: Dataset[FwTmpMappedBlocks])(implicit spark: SparkSession): Dataset[FwTmpBlockX] = {
    import spark.implicits._

    if (iteration == 0) {
      blockRecords
        .join(mappedBlocks,substring($"SCAN_BSSID",0,blockLength-1) === $"BLOCK_X","left_anti")
        .select(substring($"SCAN_BSSID",0,blockLength) as "BLOCK_X", $"SCAN_BSSID")
        .groupBy($"BLOCK_X")
        .agg(countDistinct("SCAN_BSSID") as "TOTAL_BLOCK_X_BSSID_CNT")
        .filter($"TOTAL_BLOCK_X_BSSID_CNT" >= bssidCountThresholdSmall)
        .as[FwTmpBlockX]
    }
    else {
      blockRecords
        .select(substring($"SCAN_BSSID",0,blockLength) as "BLOCK_X", $"SCAN_BSSID")
        .groupBy($"BLOCK_X")
        .agg(countDistinct("SCAN_BSSID") as "TOTAL_BLOCK_X_BSSID_CNT")
        .filter($"TOTAL_BLOCK_X_BSSID_CNT" >= bssidCountThresholdSmall)
        .as[FwTmpBlockX]
    }
  }

  /**
    * Extracts SSID Filters from LU_DEVICE_FILTER_MAPPING.csv
    * @param ldf input from LU_DEVICE_FILTER_MAPPING.csv
    * @return SSID_FILTER: String, SSID_PATTERN: String
    */
  def getSSIDFilters(ldf: Dataset[LuDeviceFilterMapping])(implicit spark: SparkSession): DataFrame =  {
    import spark.implicits._

    ldf
      .filter(($"ENABLED" === true || $"ENABLED" === "true") && col("SSID_PATTERN").isNotNull && $"SSID_PATTERN" =!= "")
      .select(
        concat(col("SSID_PATTERN"),lit("%")) as "SSID_FILTER",
        $"SSID_PATTERN",
        regexp_replace($"SSID_PATTERN","%","") as "PATTERN_LTRL")
      .dropDuplicates("SSID_FILTER")
  }

  /**
    * Finds all BLOCK_X (SCAN_BSSID substrings of length blocklength) whose corresponding SCAN_SSID contain
    * SSID_PATTERN, then aggregates the number of SCAN_BSSIDs per BLOCK_X as MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT
    *
    * @param ldf: input from tmpQuarterPatternMatchedBlockRecords
    * @param ssidFilters: SSID filters from getSSIDFilters
    * @param blockLength: length of BLOCK_X being processed
    * @return
    * DataFrame:
    * BLOCK_X: String
    * PATTERN_LTRL: String
    * SSID_PATTERN: String
    * MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: Int
    * ROW_NUM: Int
    */
  def getBlockXMatchCounts(ldf: Dataset[FwTmpQuarterPatternMatchedBlockRecords], ssidFilters: Dataset[SsidFiltersLtrl], blockLength: Int)(implicit spark: SparkSession): DataFrame =  {
    import spark.implicits._

    val window = Window.partitionBy($"BLOCK_X").orderBy(desc("MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT"))

    ldf
      .join(broadcast(ssidFilters),$"SCAN_SSID".contains($"PATTERN_LTRL"),"inner")
      .withColumn("BLOCK_X",substring($"SCAN_BSSID",0,blockLength))
      .repartition($"BLOCK_X", $"PATTERN_LTRL", $"SSID_PATTERN") // repartition here to remove exchange from physical plan
      .groupBy($"BLOCK_X",$"PATTERN_LTRL",$"SSID_PATTERN")
      .agg(countDistinct("SCAN_BSSID") as "MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT")
      .withColumn("ROW_NUM", row_number().over(window))
      .where($"ROW_NUM" === 1)
  }

  /**
    * Aggregates the number of blank (null or empty) SCAN_SSIDs per BLOCK_X as TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT
    *
    * @param ldf: input from tmpQuarterPatternMatchedBlockRecords
    * @param blockLength: length of BLOCK_X being processed
    *
    * @return
    * DataFrame:
    * BLOCK_X: String
    * TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: Int
    */
  def getBlankBlockXCounts(ldf: Dataset[FwTmpQuarterPatternMatchedBlockRecords], blockLength: Int)(implicit spark: SparkSession): DataFrame =  {
    import spark.implicits._

    ldf
      .where(col("SCAN_SSID").isNull || $"SCAN_SSID" === "")
      .withColumn("BLOCK_X",substring($"SCAN_BSSID",0,blockLength))
      .groupBy($"BLOCK_X")
      .agg(count("*") as "TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT")
  }

  /**
    * Get the total number of SSIDs which contain "fios" (case insensitive) for each block_x
    *
    * @param ldf: input from tmpQuarterPatternMatchedBlockRecords
    * @param blockLength: length of BLOCK_X being processed
    *
    * @return
    * DataFrame:
    * BLOCK_X: String
    * TOTAL_BLOCK_X_FIOS_CNT: Int
    */
  def getFiosBlockXCounts(ldf: Dataset[FwTmpQuarterPatternMatchedBlockRecords], blockLength: Int)(implicit spark: SparkSession): DataFrame =  {
    import spark.implicits._

    ldf
      .where(lower($"SCAN_SSID").like("%fios%"))
      .withColumn("BLOCK_X",substring($"SCAN_BSSID",0,blockLength))
      .groupBy($"BLOCK_X")
      .agg(count("*") as "TOTAL_BLOCK_X_FIOS_CNT")
  }

  /**
    * Get the total number of SSIDs which contain "IoT" (case insensitive) for each block_x
    *
    * @param ldf                  : input from tmpQuarterPatternMatchedBlockRecords
    * @param blockLength  : length of BLOCK_X being processed
    * @return
    * DataFrame:
    * BLOCK_X: String
    * TOTAL_BLOCK_X_IOT_CNT: Int
    */
  def getIotBlockXCounts(ldf: Dataset[FwTmpQuarterPatternMatchedBlockRecords], blockLength: Int)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    ldf
      .where(lower($"SCAN_SSID").like("%iot%"))
      .withColumn("BLOCK_X", substring($"SCAN_BSSID", 0, blockLength))
      .groupBy($"BLOCK_X")
      .agg(count("*") as "TOTAL_BLOCK_X_IOT_CNT")
  }


  /**
    * Get the total number of SSIDs which contain "spectrum" (case insensitive) for each block_x
    *
    * @param ldf          : input from tmpQuarterPatternMatchedBlockRecords
    * @param blockLength  : length of BLOCK_X being processed
    * @return
    * DataFrame:
    * BLOCK_X: String
    * TOTAL_BLOCK_X_SPECTRUM_CNT: Int
    */
  def getSpectrumBlockXCounts(ldf: Dataset[FwTmpQuarterPatternMatchedBlockRecords], blockLength: Int)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    ldf
      .where(lower($"SCAN_SSID").like("%spectrum%"))
      .withColumn("BLOCK_X", substring($"SCAN_BSSID", 0, blockLength))
      .groupBy($"BLOCK_X")
      .agg(count("*") as "TOTAL_BLOCK_X_SPECTRUM_CNT")
  }

  /**
    * Combines all counts per BLOCK_X, then applies business rules:
    * A >= 20, B > D, B/A > 0.1, ((B+D)/A >= .52
    *
    * @param ldf: result of blockX inner join blockXMatchCounts left join blankBlockXCounts; all valid BLOCK_X with
    * corresponding TOTAL_BLOCK_X_BSSID_CNT, MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT, TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT
    * @param blockX: Dataset of valid BLOCK_X
    * @param bssidCountThreshold: minimum threshold of BSSIDs
    * @param bssidPatternMatchBlockCntThreshold: minimum ratio of MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT to
    * TOTAL_BLOCK_X_BSSID_CNT
    * @param bssidMaxPatternBlankBlockCntThreshold: minimum ratio of (MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT +
    * TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT) / TOTAL_BLOCK_X_BSSID_CNT
    *
    * @return
    * Dataset[IntData]
    * BLOCK_X: String,
    * MAX_PATTERN_MATCH: String,
    * SSID_PATTERN: String,
    * TOTAL_BLOCK_X_BSSID_CNT: Long,
    * MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: Long,
    * TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: Long
    */
  def applyIntDataFilters(ldf: DataFrame, blockX: Dataset[FwTmpBlockX], bssidCountThreshold: Double, bssidPatternMatchBlockCntThreshold: Double, bssidMaxPatternBlankBlockCntThreshold: Double, bssidMaxFiosBlockCntThreshold: Double, bssidMaxIotBlockCntThreshold: Double, bssidMaxSpectrumBlockCntThreshold: Double)(implicit spark: SparkSession): Dataset[IntData] =  {
    import spark.implicits._

    ldf
      .na.fill(0, Seq("TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT"))
      .na.fill(0, Seq("TOTAL_BLOCK_X_FIOS_CNT"))
      .na.fill(0, Seq("TOTAL_BLOCK_X_IOT_CNT"))
      .na.fill(0, Seq("TOTAL_BLOCK_X_SPECTRUM_CNT"))
      .join(
        blockX,Seq("BLOCK_X","TOTAL_BLOCK_X_BSSID_CNT"),"inner")
      .select($"BLOCK_X",$"PATTERN_LTRL" as "MAX_PATTERN_MATCH",$"SSID_PATTERN",$"TOTAL_BLOCK_X_BSSID_CNT",
        $"MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT",$"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT", $"TOTAL_BLOCK_X_FIOS_CNT",
        $"TOTAL_BLOCK_X_IOT_CNT", $"TOTAL_BLOCK_X_SPECTRUM_CNT")
      .na.fill(0, Seq("TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT"))
      .na.fill(0, Seq("TOTAL_BLOCK_X_FIOS_CNT"))
      .na.fill(0, Seq("TOTAL_BLOCK_X_IOT_CNT"))
      .na.fill(0, Seq("TOTAL_BLOCK_X_SPECTRUM_CNT"))
      .where($"TOTAL_BLOCK_X_BSSID_CNT" >= bssidCountThreshold &&
        $"MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT" > $"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT" &&
        $"MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT".cast(DoubleType)/$"TOTAL_BLOCK_X_BSSID_CNT".cast(DoubleType) > bssidPatternMatchBlockCntThreshold &&
        ($"MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT" + $"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT").cast(DoubleType) / $"TOTAL_BLOCK_X_BSSID_CNT".cast(DoubleType) > bssidMaxPatternBlankBlockCntThreshold
        && ($"TOTAL_BLOCK_X_FIOS_CNT" + $"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT").cast(DoubleType) / ($"TOTAL_BLOCK_X_BSSID_CNT" - $"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT").cast(DoubleType) < bssidMaxFiosBlockCntThreshold
        && ($"TOTAL_BLOCK_X_IOT_CNT" + $"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT") / ($"TOTAL_BLOCK_X_BSSID_CNT" - $"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT").cast(DoubleType) < bssidMaxIotBlockCntThreshold
        && ($"TOTAL_BLOCK_X_SPECTRUM_CNT" + $"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT").cast(DoubleType) / ($"TOTAL_BLOCK_X_BSSID_CNT" - $"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT").cast(DoubleType) < bssidMaxSpectrumBlockCntThreshold
      )
      .as[IntData]
  }

  /**
    * Join block records with intData, filter out SCAN_SSID that are LIKE MAX_PATTERN_MATCH, include LEFT_SSID
    * (substring(SCAN_SSID,0,6)) then aggregate MAX_LEFT_SSID_BSSID_CNT from groupBy BLOCK_X, LEFT_SSID
    *
    * @param a: tmpQuarterPatternMatchedBlockRecords
    * @param intData: output of applyIntDataFilters
    * @param blockLength: length of BLOCK_X being processed
    *
    * @return
    * DataFrame
    * BLOCK_X: String
    * LEFT_SSID: Long
    * MAX_LEFT_SSID_BSSID_CNT: Long
    */
  def getCountsByLeftSSID(a: Dataset[FwTmpQuarterPatternMatchedBlockRecords], intData: Dataset[IntData], blockLength: Int)(implicit spark: SparkSession): DataFrame =  {
    import spark.implicits._

    a
      .join(
        intData,
        substring($"SCAN_BSSID",0,blockLength) === $"BLOCK_X" &&
          col("SCAN_SSID").isNotNull &&
          $"SCAN_SSID" =!= "" &&
          !upper($"SCAN_SSID").contains(upper($"MAX_PATTERN_MATCH")) &&
          check2($"SCAN_SSID",$"MAX_PATTERN_MATCH") =!= upper($"MAX_PATTERN_MATCH") &&
          upper(substring($"SCAN_SSID",0,6)) =!= upper(substring($"MAX_PATTERN_MATCH",0,6)),
        "inner"
      ).select(
      substring($"SCAN_BSSID",0,blockLength) as "BLOCK_X",
      $"MAX_PATTERN_MATCH",
      substring($"SCAN_SSID",0,6) as "LEFT_SSID"
    )
      .groupBy($"BLOCK_X",$"LEFT_SSID")
      .agg(count("*") as "MAX_LEFT_SSID_BSSID_CNT")
  }
  val check2 = udf((ssid: String, maxPatternMatch: String) => {
    ssid.substring(0, Math.min(maxPatternMatch.length,ssid.length)).toUpperCase
  })

  /**
    * Join intData with countsByLeftSSID, then apply business filter C/A < 0.2
    *
    * @param y: output from getCountsByLeftSSID
    * @param intData: BLOCK_X with counts after application of business rules
    * @param leftSSIDBlockCntThreshold: maximum ratio of MAX_LEFT_SSID_BSSID_CNT to TOTAL_BLOCK_X_BSSID_CNT
    * @return
    *
    * DataFrame:
    * BLOCK_X: String,
    * SSID_PATTERN: String,
    * LEFT_SSID: String,
    * MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: Long,
    * TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: Long,
    * TOTAL_BLOCK_X_BSSID_CNT: Long,
    * MAX_LEFT_SSID_BSSID_CNT: Long
    */
  def applyLeftSSIDFilters(y: DataFrame, intData: Dataset[IntData], leftSSIDBlockCntThreshold: Double)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    val window = Window.partitionBy($"BLOCK_X").orderBy(desc("MAX_LEFT_SSID_BSSID_CNT"))
    val temp = intData
      .join(y
        .select($"BLOCK_X", $"LEFT_SSID", $"MAX_LEFT_SSID_BSSID_CNT", row_number.over(window) as "ROW_NUM"),
        Seq("BLOCK_X"),
        "left")
    val temp2 = temp
      .where($"ROW_NUM" === 1)
      .na.fill(0, Seq("MAX_LEFT_SSID_BSSID_CNT"))
    temp2
      .where(
        $"MAX_LEFT_SSID_BSSID_CNT".cast(DoubleType) / $"TOTAL_BLOCK_X_BSSID_CNT".cast(DoubleType) < leftSSIDBlockCntThreshold
        && !lower($"LEFT_SSID").like("iot%")
        && !lower($"LEFT_SSID").like("fios%")
        && !lower($"LEFT_SSID").like("spectr%")
      )
      .select($"BLOCK_X",$"SSID_PATTERN",$"LEFT_SSID",$"MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT",
        $"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT",$"TOTAL_BLOCK_X_FIOS_CNT",$"TOTAL_BLOCK_X_IOT_CNT",
        $"TOTAL_BLOCK_X_SPECTRUM_CNT",$"TOTAL_BLOCK_X_BSSID_CNT",$"MAX_LEFT_SSID_BSSID_CNT")
  }
}