package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.CellularFixedWirelessUtils._
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.model.FwFaBlockMappingOutput
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.{SparkJob, SparkJobRunner}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

import java.net.URI
import java.time.LocalDate


case class PersistDataToFWFABlockMappingJobConfig(
  fwfaBlockMappingLocation: URI,
  mappedBlocksLocation: URI,
  processEndDateInclusive: Option[LocalDate]
)


object PersistDataToFWFABlockMappingJob extends SparkJob(PersistDataToFWFABlockMappingJobRunner)


object PersistDataToFWFABlockMapping<PERSON><PERSON><PERSON><PERSON><PERSON> extends SparkJobRunner[PersistDataToFWFABlockMappingJobConfig] with LazyLogging {

  def runJob(config: PersistDataToFWFABlockMappingJobConfig)(implicit spark: SparkSession) = {

    val processEndDateInclusive = config.processEndDateInclusive.getOrElse(getLastWeekdayDate("SATURDAY"))
    val mappedBlocksPath = generateYearMonthDayPath(config.mappedBlocksLocation, processEndDateInclusive)
    val outputPath = generateYearMonthDayPath(config.fwfaBlockMappingLocation, processEndDateInclusive)

    implicit val cldFileUtils = CldFileUtils.newBuilder.forUri(config.fwfaBlockMappingLocation, config.mappedBlocksLocation)(spark).build

    logger.info(s"loading : ${config.mappedBlocksLocation}")
    val mappedBlocks = spark.read.parquet(mappedBlocksPath)

    logger.info("Converting dataset to expected schema")
    val fwfaBlockMapping = convertToFwFaBlockMappingOutput(mappedBlocks)

    logger.info(s"writing to : ${config.fwfaBlockMappingLocation}")
    dynamicOverwrite(fwfaBlockMapping, outputPath)
  }



  /**
    * Converts the dataframe to the expected Carrier Identification output schema (intermediary between the two FWA
    * components)
    * @param mappedBlocks DataFrame to be converted into approperiate schema for output
    * @param spark
    * @return
    */
  def convertToFwFaBlockMappingOutput(mappedBlocks: DataFrame)(implicit spark: SparkSession):  Dataset[FwFaBlockMappingOutput] = {
    import spark.implicits._
    mappedBlocks
      .select(
        $"UUID",
        $"RUN_TIMESTAMP",
        $"BLOCK_X",
        $"SSID_PATTERN",
        $"LEFT_SSID",
        $"MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT",
        $"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT",
        $"TOTAL_BLOCK_X_FIOS_CNT",
        $"TOTAL_BLOCK_X_IOT_CNT",
        $"TOTAL_BLOCK_X_SPECTRUM_CNT",
        $"TOTAL_BLOCK_X_BSSID_CNT",
        $"MAX_LEFT_SSID_BSSID_CNT"
      )
      .as[FwFaBlockMappingOutput]
  }

}
