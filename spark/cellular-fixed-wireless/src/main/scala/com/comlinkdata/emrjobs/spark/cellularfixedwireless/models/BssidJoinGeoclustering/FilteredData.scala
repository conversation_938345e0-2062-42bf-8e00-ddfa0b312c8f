package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering

import org.apache.spark.sql.functions.expr
import org.apache.spark.sql.{Dataset, SparkSession}


/**
  * FilteredDataScans -- intermediary table for step output containing the original scan data rejoined to the representative
  * point for each cluster
  * @param Scan_BSSID BSSID associated with the device scanned
  * @param Scan_SSID SSID associated with the device scanned
  * @param BSSID_Carrier Carrier associated with the device scanned
  * @param BSSID_Technology Technology associated with the device scanned
  * @param modem_vendor vendor associated with the modem scanned
  * @param modem_model model associated with the model scanned
  * @param aggregate_centroid_latitude the representative latitude of the device across all scans
  * @param aggregate_centroid_longitude the representative longitude of the device across all scans
  * @param count_UID count of all UIDs within the same cluster
  * @param numrecords count the number of records within the same cluster
  * @param earliest_date earliest observed date of all points within the same cluster for processing quarter
  */
case class FilteredDataScans(
  Scan_BSSID: String,
  Scan_SSID: String,
  BSSID_Carrier: String,
  BSSID_Technology: String,
  modem_vendor: String,
  modem_model: String,
  aggregate_centroid_latitude: Double,
  aggregate_centroid_longitude: Double,
  count_UID: java.lang.Long,
  numrecords: java.lang.Long,
  earliest_date: java.sql.Timestamp
)



object FilteredData {

  /**
    * Filter AggDataScans data by re-joining it to its best clusters contained in ClusterFilteringScans. Further filter
    * the join results to only records with one "best cluster"
    * @param aggDataScans AggDataScans subquery table outputs to be filtered with the join
    * @param clusterFilteringScans ClusterFilteringScans qubquery table output to filter the aggdata to the right cluster
    * @param spark
    * @return
    */
  def createFromScans(aggDataScans: Dataset[AggDataScans], clusterFilteringScans: Dataset[ClusterFilteringScans])(implicit spark: SparkSession): Dataset[FilteredDataScans] = {
    import spark.implicits._
    aggDataScans
      .join(clusterFilteringScans, Seq("Scan_SSID", "Scan_BSSID", "cluster_num"), "inner")
      .where($"count_best_clusters" === 1)
      .select(
        $"Scan_BSSID",
        $"Scan_SSID",
        $"BSSID_Carrier",
        $"BSSID_Technology",
        $"modem_vendor",
        $"modem_model",
        expr("ST_Y(aggregate_centroid)").as("aggregate_centroid_latitude"),
        expr("ST_X(aggregate_centroid)").as("aggregate_centroid_longitude"),
        $"count_UID",
        $"numrecords",
        $"earliest_date"
      )
      .as[FilteredDataScans]
  }
}
