package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.ApplyOuiExclusionRules

import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import org.apache.spark.sql.functions.lower
import org.apache.spark.sql.{Dataset, SparkSession}

import java.net.URI


/**
  * Lookup table for well-known blocks and their associated vendors
  * @param BLOCK starting substring for the BSSID
  * @param VENDOR the name of the vendor assiciated with the block
  * @param DESCRIPTION description of the block/vendor, may be blank
  * @param COMMENT any additional comments left in the original OUI lookup table
  */
case class OuiLookupTable(
  BLOCK: String,
  VENDOR: String,
  DESCRIPTION: String,
  COMMENT: String
)


object OuiLookupTable {

  /**
    * Validate the given path is readable. If it is, read the appropriate schema for OuiLookupTable and return as
    * a dataset.
    * @param path URI path to the S3 location containing the OUI lookup table
    * @param spark
    * @param fileUtils
    *
    * @todo currently assuming the table will be in S3 as a CSV file. This may change to parquet along with schema changes
    *       once more information from the ticket is known.
    * @return
    */
  def loadValidDataOrThrow(path: URI)(implicit spark: SparkSession, fileUtils: CldFileUtils): Dataset[OuiLookupTable] = {
    import spark.implicits._
    fileUtils.allFilesInDirectoryAreReadableOrThrow(path)
    spark.read
      .option("header","true")
      .csv(path.toString)
      .select(
        lower($"BLOCK").as("BLOCK"),
        lower($"VENDOR").as("VENDOR"),
        lower($"DESCRIPTION").as("DESCRIPTION"),
        $"COMMENT"
      )
      .as[OuiLookupTable]
  }
}

