package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering

import org.apache.spark.sql.functions.{count, substring}
import org.apache.spark.sql.{Dataset, SparkSession}

/***
  * Custom Inclusion dataset to be processed in deduplication and blocks
  * @param BSSID_Carrier - Carrier
  * @param BSSID_Technology - FW technology
  * @param modem_model - Modem model
  * @param modem_vendor - Modem vendor
  * @param short_BSSID - Modem BSSID
  * @param centroid_lat - Latitude of modem
  * @param centroid_lon - Longitude of modem
  * @param first_observed_date - Date modem was first observed
  */
case class CustomInclusionData (
  BSSID_Carrier: String,
  BSSID_Technology: String,
  modem_model: String,
  modem_vendor: String,
  short_BSSID: String,
  centroid_lat: Double,
  centroid_lon: Double,
  first_observed_date: java.sql.Timestamp
)

/***
  * Intermediate dataType
  * @param Scan_BSSID - BSSID
  * @param num_UID - Number of UIDS with given BSSID
  */
case class BssidLevel (
  Scan_BSSID: String,
  num_UID: BigInt,
)

/***
  * Lookup info regarding Modems
  * @param OUI_long - OUI for matching to modems
  * @param WIFIScan_Manufacturer - Modem Manufacturer
  * @param WIFIScan_ModelName - Modem Model Name
  * @param WIFIScan_ModelNumber - Modem Model Number
  * @param numrecords - Number of records seen with this OUI_long
  */
case class OuiLookup (
  OUI_long: String,
  WIFIScan_Manufacturer: String,
  WIFIScan_ModelName: String,
  WIFIScan_ModelNumber: String,
  numrecords: BigInt
)

/***
  * Custom dataType for data of inclusion approach
  */
object CustomInclusionData{

  /***
    * Generate the custom inclusion dataset using an adapter specific to the carrier
    * @param rawScans - raw WifiScans
    * @param onesourceScans - Onesource Scans
    * @param adapter - Carrier adapter for carrier specific transformations
    * @param spark - spark
    * @return The Custom Inclusion Dataset to be processed later in the pipeline
    */
  def createInclusionDataset(onesourceScans: Dataset[OnesourceData], adapter: CarrierAdapter) (implicit spark: SparkSession): Dataset[CustomInclusionData] = {
    import spark.implicits._

    // Create the ouiLookup from the onesource scans filtering on the models
    val ouiLookup = onesourceScans
      .transform(adapter.applyPreFiltering)
      .groupBy(
        substring($"Scan_BSSID", 0, 11).as("OUI_long"),
        $"WIFIScan_Manufacturer",
        $"WIFIScan_ModelName",
        $"WIFIScan_ModelNumber"
      )
      .agg(count($"*").as("numrecords"))
      .filter($"numrecords">=5).as[OuiLookup]

    // Gather the list of BSSIDs from the rawscans and ouiLookup
    val bssidLevel = onesourceScans
      .transform(adapter.applyAdditionalFilters)
      .join(ouiLookup, substring($"Scan_BSSID", 0, 11) === $"OUI_LONG", "leftsemi")
       .groupBy(
         $"Scan_BSSID",
       )
       .agg(count($"Scan_BSSID").as("num_UID")).as[BssidLevel]

    // Process the data into the needed Schema to be used later in the pipeline
    val result = adapter.processBssids(bssidLevel, onesourceScans, ouiLookup)
      .as[CustomInclusionData]

    result
  }
}
