package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.CellularFixedWirelessUtils.stClusterDbScan
import org.apache.spark.sql.{Dataset, SparkSession}
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.{collect_list}
import org.locationtech.jts.geom.Geometry


/**
  * ClusterBssid -- intermediary table with each modem's aggregate centroids being clustered with DBscan
  * @param BSSID_Carrier carrier identified for the modem
  * @param BSSID_Technology modem technology identified
  * @param modem_model model of the identified modem
  * @param modem_vendor vendor of the identified modem
  * @param short_BSSID bssid substring to identify modem
  * @param aggregate_centroid Centroid of the modem locations (within a 2-decimal place lat-long locality)
  * @param earliest_date earliest date the modem was observed this quarter
  * @param clusternum cluster number of an associated bssid after running through DBScan across all matching short_BSSIDs
  */
case class ClusterBssid(
  BSSID_Carrier: String,
  BSSID_Technology: String,
  modem_model: String,
  modem_vendor: String,
  short_BSSID: String,
  aggregate_centroid: Geometry,
  earliest_date: java.sql.Timestamp,
  clusternum: java.lang.Long
)

object ClusterBssid{
  /**
    * Run DBScan on the centroids associated with each with each short_bssid to assign each record a cluster number
    * @param shortBssid the results of the shorter subquery, which extracts approprate short_bssid substring
    * @param spark
    * @return
    */
  def getClustered(shortBssid: Dataset[ShortBssid])(implicit spark: SparkSession): Dataset[ClusterBssid] = {
    import spark.implicits._
    val window = Window.partitionBy($"short_BSSID")
    shortBssid
      .withColumn("clusternum", stClusterDbScan(500, 1)(collect_list($"aggregate_centroid").over(window), $"short_BSSID", $"aggregate_centroid"))
      .select(
        $"BSSID_Carrier",
        $"BSSID_Technology",
        $"modem_model",
        $"modem_vendor",
        $"short_BSSID",
        $"aggregate_centroid",
        $"earliest_date",
        $"clusternum"
      )
      .as[ClusterBssid]
  }
}
