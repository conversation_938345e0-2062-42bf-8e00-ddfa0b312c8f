package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks

/***
  * Intermediate schema for fios filtering
  * @param block - census block
  * @param BSSID_Carrier - carrier
  * @param BSSID_Technology - technology
  * @param modem_model - model
  * @param modem_vendor - vendor
  * @param short_BSSID - bssid
  * @param centroid_lat - latitude
  * @param centroid_lon - longitude
  * @param first_observed_date - earliest date
  * @param max_pct_allowed - limit as percent of overall vzn observations based on fiber density
  * @param vz_fiber_density - fiber density of the given block
  */
case class DensityJoinedBssid(
  block: String,
  BSSID_Carrier: String,
  BSSID_Technology: String,
  modem_model: String,
  modem_vendor: String,
  short_BSSID: String,
  centroid_lat: Double,
  centroid_lon: Double,
  first_observed_date: java.sql.Timestamp,
  max_pct_allowed: String,
  vz_fiber_density: String
)