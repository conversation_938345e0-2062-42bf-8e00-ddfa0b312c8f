package com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks

import com.comlinkdata.largescale.schema.cellular_fixed_wireless.FixedWirelessModemOutput
import org.apache.spark.sql.{Dataset, SparkSession}


/**
  * ModemOutput -- Output Dataset
  * @param BSSID_Carrier carrier identified for the modem
  * @param modem_model model of the identified modem
  * @param modem_vendor vendor of the identified modem
  * @param short_BSSID bssid substring to identify modem
  * @param longitude centroid longitude associated with all instances of the modem
  * @param latitude centroid latitude associated with all instances of the modem
  */
case class ModemOutput(
  BSSID_Carrier: String,
  modem_vendor: String,
  modem_model: String,
  short_BSSID: String,
  longitude: Double,
  latitude: Double
)

object ModemOutput{

  /**
    * Used for preparing the desired output with post-clustering wireless modem related data
    * @param blockJoinedBssid block joined BSSID to be used for adapting into the desired modem output schema
    * @param spark
    * @return
    */
  def createFromBlockJoinedBssid(blockJoinedBssid: Dataset[BlockJoinedBssid])(implicit spark: SparkSession): Dataset[ModemOutput] = {
    import spark.implicits._
    blockJoinedBssid
      .select(
        $"BSSID_CARRIER",
        $"modem_vendor",
        $"modem_model",
        $"short_BSSID",
        $"centroid_lon".as("longitude"),
        $"centroid_lat".as("latitude")
      )
      .as[ModemOutput]
  }

  /**
    * Converts object type into the same present in the commons package to allow for proper resource sharing
    * @param mo ModemOutput dataset to be converted
    * @param spark
    * @return
    */
  def convertToFixedWirelessModemOutput(mo: Dataset[ModemOutput])(implicit spark: SparkSession): Dataset[FixedWirelessModemOutput] = {
    import spark.implicits._
    mo.select(FixedWirelessModemOutput.cols: _*).as[FixedWirelessModemOutput]
  }
}
