package com.comlinkdata.emrjobs.spark.cellularfixedwireless.algo

import com.github.davidmoten.rtree2.RTree
import com.github.davidmoten.rtree2.geometry.{Geometries, Geometry, Rectangle}

import scala.collection.mutable
import scala.jdk.CollectionConverters._
import org.locationtech.jts.geom.{GeometryFactory, Coordinate, Point}
import org.apache.commons.math3.util.FastMath
import java.lang.Math.{PI, abs, asin, atan2, cos, sin, toRadians}


object DBScan {
  private val EARTH_RADIUS_M = 6371.01 * 1000
  private val gf = new GeometryFactory()  // used for creating geometry points
  private val p180 = Math.PI / 180  // used in calculating distance between geographies
  private val radiusOfEarthX2InM = 6371 * 2 * 1000  // used in calculating the distance between geographies

  trait PointAccumulator[A] {
    def add(pt: A): Unit
    def centroid(): A
    def numberPtSeen(): Int
    def reset(): Unit
  }

  abstract class DbScanPointFunc[A] {
    def distBetween(pt1: A, pt2: A): Double
    def toBoundingBox(pt: A, dist: Double): Rectangle
    def toGeometry(pt: A): Geometry
    def pointAccumulator: PointAccumulator[A]
    def sizeOfCluster(neighbors: Seq[A], thisPoint: A): Int = neighbors.size + 1
  }

  private class TwoDeePoint extends DbScanPointFunc[(Float, Float)] {

    val ptAcc = new PointAccumulator[(Float, Float)] {
      var sumX = 0.0
      var sumY = 0.0
      var count = 0
      override def add(pt: (Float, Float)): Unit = { sumX += pt._1; sumY += pt._2; count += 1; }
      override def centroid(): (Float, Float) = ((sumX / count).toFloat, (sumY / count).toFloat)
      override def numberPtSeen(): Int = count
      override def reset(): Unit = { sumX = 0.0; sumY = 0.0; count = 0; }
    } // ptAcc

    override def distBetween(pt1: (Float, Float), pt2: (Float, Float)): Double = {
      val dx = pt1._1 - pt2._1
      val dy = pt1._2 - pt2._2
      Math.sqrt(dx*dx + dy*dy)
    }

    override def toBoundingBox(pt: (Float, Float), dist: Double): Rectangle = Geometries.rectangle(pt._1 - dist, pt._2 - dist, pt._1 + dist, pt._2 + dist)
    override def toGeometry(pt: (Float, Float)): Geometry = Geometries.point(pt._1, pt._2)
    override def pointAccumulator: PointAccumulator[(Float, Float)] = ptAcc

  } // TwoDeePoint


  def cluster2DPts(geogs: IndexedSeq[(Float, Float)], eps: Double, minGeogs: Int): (Array[Int], Seq[((Float, Float), Int)]) = {
    DBScan.cluster(geogs, eps, minGeogs, new TwoDeePoint())
  }

  private class GPSPoints extends DbScanPointFunc[Point] {

    val ptAcc = new PointAccumulator[Point] {
      var sumX = 0.0
      var sumY = 0.0
      var count = 0
      override def add(pt: Point): Unit = { sumX += pt.getX; sumY += pt.getY; count += 1; }
      override def centroid(): Point = gf.createPoint(new Coordinate((sumX / count).toFloat, (sumY / count).toFloat))
      override def numberPtSeen(): Int = count
      override def reset(): Unit = { sumX = 0.0; sumY = 0.0; count = 0; }
    } //ptAcc

    override def distBetween(pt1: Point, pt2: Point): Double = {
      val lat1 = pt1.getY
      val lon1 = pt1.getX
      val lat2 = pt2.getY
      val lon2 = pt2.getX
      val a = 0.5 - Math.cos((lat2-lat1)*p180)/2 + Math.cos(lat1*p180) * Math.cos(lat2*p180) * (1-Math.cos((lon2-lon1)*p180))/2
      radiusOfEarthX2InM * Math.asin(Math.sqrt(a))
    }

    override def toBoundingBox(pt: Point, dist: Double): Rectangle = {
      val north = getLatInDir(pt, dist, 0)
      val south = getLatInDir(pt, dist, 180)
      val east = getLonInDir(pt, dist, 90)
      val west = getLonInDir(pt, dist, 270)
      val result = Geometries.rectangle(south, west, north, east)
      assert(result.contains(pt.getY, pt.getX), "Bounding rectangle doesn't contain pt")
      result
    }

    override def toGeometry(pt: Point): Geometry = Geometries.point(pt.getY, pt.getX)
    override def pointAccumulator: PointAccumulator[Point] = ptAcc
  }

  private class BinnedGpsPoints extends DbScanPointFunc[(Int, Point)]{
    private val GPSFunc = new GPSPoints()
    private val pointAcc = new PointAccumulator[(Int, Point)] {
      var sumX = 0.0
      var sumY = 0.0
      var count = 0
      override def add(pt: (Int, Point)) = { sumX += pt._2.getX * pt._1; sumY += pt._2.getY * pt._1; count += pt._1; }
      override def centroid(): (Int, Point) = (count, gf.createPoint(new Coordinate((sumX/count).toFloat, (sumY/count).toFloat)))
      override def numberPtSeen(): Int = count
      override def reset() = { sumX = 0.0; sumY = 0.0; count = 0; }
    }
    override def distBetween(pt1: (Int, Point), pt2: (Int, Point)): Double = GPSFunc.distBetween(pt1._2, pt2._2)
    override def toBoundingBox(pt: (Int, Point), dist: Double): Rectangle = GPSFunc.toBoundingBox(pt._2, dist)
    override def toGeometry(pt: (Int, Point)): Geometry = GPSFunc.toGeometry(pt._2)
    override def pointAccumulator: PointAccumulator[(Int, Point)] = pointAcc
    override def sizeOfCluster(neighbors: Seq[(Int, Point)], thisPoint: (Int, Point)): Int = neighbors.map(_._1).sum + thisPoint._1
  }

  def clusterGPSWithBinning(pts: IndexedSeq[Point], nearestNeighDist: Double, minNumPts: Int): (IndexedSeq[(Int, Point)], Array[Int], Seq[(Point, Int)]) = {
    clusterGPSWithBinningTopK(pts, nearestNeighDist, minNumPts, None)
  }

  def clusterGPSWithBinningTopK(pts: IndexedSeq[Point], nearestNeighDist: Double, minNumPts: Int, maxNumUnique: Option[Int]): (IndexedSeq[(Int, Point)], Array[Int], Seq[(Point, Int)]) = {

    def hashPoint(pt: Point): String = pt.getY.toString + pt.getX.toString

    //val binnedPoints = pts.map(p => (p, hashPoint(p))).groupBy(_._2).map(p => (p._2.size, p._2.head._1)).toIndexedSeq
    val correctBins = pts.map(p=>(p, hashPoint(p))).groupBy(_._2).map(p => (p._2.head._1, p._2.size)).toIndexedSeq
    val binnedPoints = correctBins.map(p => (p._2, p._1)).toIndexedSeq

    val topPoints = if(maxNumUnique.isDefined && maxNumUnique.get < binnedPoints.size) {
      implicit object PointSizeTupleOrd extends math.Ordering[(Int, Point)] {
        override def compare(x: (Int, Point), y: (Int, Point)) = Integer.compare(x._1, y._1)
      }
      mutable.PriorityQueue(binnedPoints:_*).take(maxNumUnique.get).toIndexedSeq
    } else { binnedPoints }

    val result: (Array[Int], Seq[((Int, Point), Int)]) = DBScan.cluster(topPoints, nearestNeighDist, minNumPts, new BinnedGpsPoints())
    val trimOfCounts = result._2.map(p => (p._1._2, p._2))
    (topPoints, result._1, trimOfCounts)  // (bins, labels, centroids)
  }

  def clusterGPS(geogs: IndexedSeq[Point], eps: Double, minGeogs: Int): (Array[Int], Seq[(Point, Int)]) = {
    DBScan.cluster(geogs, eps, minGeogs, new GPSPoints)
  }

  def cluster[A <: AnyRef](pts: IndexedSeq[A], nearestNeighDist: Double, minNumPts: Int, dbScanFuncs: DbScanPointFunc[A]): (Array[Int], Seq[(A, Int)]) = {
    val n = pts.size  // save number of points
    val labels = Array.fill[Int](n)(0)  // output labels initialized to 0
    val result = mutable.ListBuffer[(A, Int)]()  // output centroids
    val rTreeEmpty = RTree.star.maxChildren(4).create[Int, Geometry]
    val rTreeEntryFactory = rTreeEmpty.context.factory()

    // Make implicit parameters to pass to other dbb scan functions (regionQuery, growCluster)
    implicit val minPts: Int = minNumPts
    implicit val eps: Double = nearestNeighDist
    implicit val ptsIn: Seq[A] = pts
    implicit val funcs: DbScanPointFunc[A] = dbScanFuncs
    implicit val rtree: RTree[Int, Geometry] = (0 until n)
      .map(i => rTreeEntryFactory.createEntry(i, dbScanFuncs.toGeometry(ptsIn(i))))
      .foldLeft(rTreeEmpty)(_.add(_))

    var currentCluster = 0
    for (i <- ptsIn.indices) {  // make sure every point gets a nonzero label
      val pt = ptsIn(i)
      if (labels(i) == 0) {  // If the point doesn't have a cluster and it hasn't been labeled as noise yet
        val neighborPts = regionQuery(pt)  // Get neighbors for the point
        if(dbScanFuncs.sizeOfCluster(neighborPts.map(ptsIn), pt) < minPts) { // if this point plus its neighbors is too few points for a cluster
          labels(i) = -1   // label it as noise
        } else {  // otherwise, we have a new cluster
          currentCluster += 1
          result += growCluster(currentCluster, i, neighborPts, labels)  // when this returns, all points in the cluster will be labeled
        }
      }
    }
    (labels, result)
  }

  private def regionQuery[A <: AnyRef](pt: A)(implicit ptsIn: Seq[A], rtree: RTree[Int, Geometry], eps: Double, funcs: DbScanPointFunc[A]): Seq[Int] = {
    val bb = funcs.toBoundingBox(pt, eps)
    val ptsInBB = rtree.search(bb)  // search for points in bounding box; this will be a superset of neighbors
    ptsInBB.asScala.map(_.value).filter(i => {
      val pNot = ptsIn(i)
      !(pt eq pNot) && (funcs.distBetween(pNot, pt) <= eps)  // find other points that are within epsilon distance of it
    }).toSeq
  }

  private def growCluster[A <: AnyRef]
  (newCluster: Int, ptIndex: Int, neighborIndexes: Iterable[Int], labels: Array[Int])
  (implicit ptsIn: Seq[A], rtree: RTree[Int, Geometry], eps: Double, minPts: Int, funcs: DbScanPointFunc[A]) = {
    funcs.pointAccumulator.reset()  // reset centroid
    labels(ptIndex) = newCluster  // label seed point
    funcs.pointAccumulator.add(ptsIn(ptIndex))  // add seed point to centroid
    val ptsQ = mutable.Queue[Int](neighborIndexes.toSeq:_*)  // create a FIFO list of neighbors to visit
    while(ptsQ.nonEmpty){  // while there are unvisited neighbors left
      val neighIndex = ptsQ.dequeue  //m pop a neighbor
      val labelNeigh = labels(neighIndex)  // cache its label
      if (labelNeigh == -1){  // if already labeled as noise, re-assign to current cluster since there's now enough neighbors
        labels(neighIndex) = newCluster  // reassign noise point to this cluster
        funcs.pointAccumulator.add(ptsIn(neighIndex))  // add it to the centroid
      } else if(labelNeigh == 0){  // if this point hasn't been seen before
        labels(neighIndex) = newCluster  // assign it to this cluster
        funcs.pointAccumulator.add(ptsIn(neighIndex))  // add it to the centroid
        val ptNeigh = ptsIn(neighIndex)
        val newNeighbors = regionQuery(ptNeigh)  // look for new possible neighbors
        if (funcs.sizeOfCluster(newNeighbors.map(ptsIn), ptNeigh) >= minPts) {  // if we found enough neighbors, add to stack
          ptsQ.enqueue(newNeighbors: _*)
        }
      }
    }
    (funcs.pointAccumulator.centroid(), funcs.pointAccumulator.numberPtSeen())
  }

  private def getLatInDir(pt: Point, distanceM: Double, courseDegrees: Double): Double = {
    val dr = distanceM / EARTH_RADIUS_M
    val latR = toRadians(pt.getY)
    val courseR = toRadians(courseDegrees)
    val lat2Radians = asin(sin(latR) * cos(dr) + cos(latR) * sin(dr) * cos(courseR))
    FastMath.toDegrees(lat2Radians)
  }

  private def getLonInDir(pt: Point, distanceM: Double, courseDegrees: Double): Double = {
    val dr = distanceM / EARTH_RADIUS_M
    val latR = toRadians(pt.getY)
    val lonR = toRadians(pt.getX)
    val courseR = toRadians(courseDegrees)
    val lat2Radians = asin(sin(latR) * cos(dr) + cos(latR) * sin(dr) * cos(courseR))
    val lon2Radians = atan2(sin(courseR) * sin(dr) * cos(latR), cos(dr) - sin(latR) * sin(lat2Radians))
    val lon3Radians = mod(lonR + lon2Radians + PI, 2 * PI) - PI
    FastMath.toDegrees(lon3Radians)
  }

  private def mod(y: Double, x: Double) = {
    val xAbs = abs(x)
    val n = (y / xAbs).toInt
    val mod = y - xAbs * n
    if (mod < 0) mod + xAbs else mod
  }
}
