package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.emrjobs.spark.cellularfixedwireless.CellularFixedWirelessUtils.{dynamicOverwrite, generateYearMonthDayPath, getLastWeekdayDate}
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.ApplyOuiExclusionRules.{OuiLookupTable, OuiMappedBlocksForAnalysis}
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BlockXLoopCounts.MappedBlocks
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.{SparkJob, SparkJobRunner}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.substring
import org.apache.spark.sql.{Dataset, SparkSession}

import java.net.URI
import java.time.LocalDate



case class ApplyOuiExclusionRulesJobConfig(
  processEndDateInclusive: Option[LocalDate],
  mappedBlocksLocation: URI,
  ouiLookupTableLocation: URI,
  outputLocation: URI
)


object ApplyOuiExclusionRulesJob extends SparkJob(ApplyOuiExclusionRulesJobRunner)


object ApplyOuiExclusionRulesJobRunner extends SparkJobRunner[ApplyOuiExclusionRulesJobConfig] with LazyLogging{

  def runJob(config: ApplyOuiExclusionRulesJobConfig)(implicit spark: SparkSession) = {
    import spark.implicits._
    import CldFileUtils.implicits._


    // 1 -- Dynamic Config Computation
    logger.info("Computing Dynamic Parameters from Config")
    val processEndDateInclusive = config.processEndDateInclusive.getOrElse(getLastWeekdayDate("SATURDAY"))


    // 2 -- Dynamic URI Path Computation
    val mappedBlocksPath = generateYearMonthDayPath(config.mappedBlocksLocation, processEndDateInclusive)
    val outputPath = generateYearMonthDayPath(config.outputLocation, processEndDateInclusive)


    // 3 -- Prepare CldFileUtils for input Validation (done within the load/create methods)
    implicit val cldFileUtils: CldFileUtils = CldFileUtils.newBuilder
      .forUri(
        config.mappedBlocksLocation,
        config.ouiLookupTableLocation,
        config.outputLocation
      )(spark).build


    // 4 -- Read Input Data
    logger.info(s"Reading OUI Lookup Table from path: ${config.ouiLookupTableLocation}")
    val ouiLookupTable = OuiLookupTable.loadValidDataOrThrow(config.ouiLookupTableLocation)
    logger.info(s"Reading Mapped Blocks data from path: ${mappedBlocksPath}")
    val mappedBlocks = MappedBlocks.loadValidDataOrThrow(mappedBlocksPath)


    // 5 -- Analyze OUI for blocks to remove
    logger.info(s"OUI Identification -- Determining BLOCK information associated with Verizon")
    val blocksForAnalysis = OuiMappedBlocksForAnalysis.getBlocksToAnalyze(mappedBlocks, ouiLookupTable)
    logger.info("OUI Identification -- Determining Blocks for Exclusion")
    val blocksToExclude = OuiMappedBlocksForAnalysis.getBlocksToExclude(blocksForAnalysis)


    // 6 -- Applying Exclusions
    logger.info("Removing C-Band blocks from MappedBlocks via anti-join")
    val finalMappedBlocks = mappedBlocks.join(blocksToExclude, substring($"BLOCK_X",0,8)===$"BLOCK_6", "leftanti")


    // 6 -- Write Output
    dynamicOverwrite(finalMappedBlocks, outputPath)
  }


  /**
    * Apply the exclusions to mapped blocks, by anti-joining with the original dataset on the condition that the BLOCK_6
    * of mapped blocks matches the BLOCK_6 selected for exclusion
    *
    * @param mappedBlocks The mapped blocks table to be filtered
    * @param exclusions The analyzed OUI table, whose BLOCK_6 field contain those desired for removal
    * @param spark
    * @return
    */
  def applyExclusionsToMappedBlocks(mappedBlocks: Dataset[MappedBlocks], exclusions: Dataset[OuiMappedBlocksForAnalysis])(implicit spark: SparkSession): Dataset[MappedBlocks] = {
    import spark.implicits._
    mappedBlocks
      .join(
        exclusions,
        $"SSID_PATTERN"==="Verizon_" && substring($"BLOCK_X",0,8)===$"BLOCK_6",
        "leftanti"
      )
      .as[MappedBlocks]
  }
}
