package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.BlockXLoopCountsSpec._
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.model._
import org.apache.spark.sql.functions.{countDistinct, substring}

object BlockXLoopCountsSpec {

  /**
    * Collection of filters with different wildcards
    * @param SSID_FILTER filters with default wildcards only
    * @param SSID_PATTERN filters with % added to end
    * @param PATTERN_LTRL filters with no wildcards
    */
  case class SSIDFiltersLtrl(SSID_FILTER: String, SSID_PATTERN: String, PATTERN_LTRL: String)

  /**
    * BLOCK_Xs with total BSSID counts
    * @param BLOCK_X
    * @param TOTAL_BLOCK_X_BSSID_CNT
    */
  case class totalBSSIDCounts(BLOCK_X: String, TOTAL_BLOCK_X_BSSID_CNT: Long)

  /**
    * BLOCK_Xs with count of BSSIDs that match the pattern
    * @param BLOCK_X
    * @param SSID_PATTERN
    * @param TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT
    * @param TOTAL_BLOCK_X_BSSID_CNT
    */
  case class totalPatternMatchCounts(BLOCK_X: String, SSID_PATTERN: String, TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: Long, TOTAL_BLOCK_X_BSSID_CNT: Long)

  /**
    * BLOCK_X with count of BSSIDs with blank SSIDs
    * @param BLOCK_X
    * @param TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT
    */
  case class blankSSIDBSSIDCounts(BLOCK_X: String, TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: Long)

  /**
    * BLOCK_X with count of BSSIDs which match the SSID_PATTERN
    * @param BLOCK_X
    * @param PATTERN_LTRL
    * @param SSID_PATTERN
    * @param MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT
    * @param ROW_NUM
    */
  case class blockXMatchCounts(BLOCK_X: String, PATTERN_LTRL: String, SSID_PATTERN: String, MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: Long, ROW_NUM: Int)

  /**
    * BLOCK_X with LEFT_SSIDs and counts only
    * @param BLOCK_X
    * @param LEFT_SSID
    * @param MAX_LEFT_SSID_BSSID_CNT
    */
  case class leftSSIDCounts(
    BLOCK_X: String,
    LEFT_SSID: String,
    MAX_LEFT_SSID_BSSID_CNT: Long)

  /**
    * BLOCK_X with full set of counts; output for applyLeftSSIDFilters
    * @param BLOCK_X
    * @param SSID_PATTERN
    * @param LEFT_SSID
    * @param MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT
    * @param TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT
    * @param TOTAL_BLOCK_X_BSSID_CNT
    * @param MAX_LEFT_SSID_BSSID_CNT
    */
  case class appliedLeftSSIDFilters(
    BLOCK_X: String,
    SSID_PATTERN: String,
    LEFT_SSID: String,
    MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: Long,
    TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: Long,
    TOTAL_BLOCK_X_FIOS_CNT: Long,
    TOTAL_BLOCK_X_IOT_CNT: Long,
    TOTAL_BLOCK_X_SPECTRUM_CNT: Long,
    TOTAL_BLOCK_X_BSSID_CNT: Long,
    MAX_LEFT_SSID_BSSID_CNT: Long
  )

  /**
    * The count of unique BSSIDs per BLOCK_X
    * @param BLOCK_X
    * @param TOTAL_BLOCK_X_BSSID_CNT
    * @param PATTERN_LTRL
    * @param SSID_PATTERN
    * @param MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT
    * @param TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT
    * @param ROW_NUM
    */
  case class fullBlockXCounts(
    BLOCK_X: String,
    TOTAL_BLOCK_X_BSSID_CNT: Long,
    PATTERN_LTRL: String,
    SSID_PATTERN: String,
    MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: Long,
    TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: Long,
    ROW_NUM: Int
  )
}

class BlockXLoopCountsSpec extends CldSparkBaseSpec {

  describe("initialize blockX")  {
    it ("correct data types") {
      import spark.implicits._
      val blockLength = 11

      val timestamp = new java.sql.Timestamp(System.currentTimeMillis())
      val blockRecordsRow1 = FwTmpQuarterPatternMatchedBlockRecords("AAAAA","BBBBB")
      val mappedBlocksRow1 = FwTmpMappedBlocks("1111",timestamp,1,2021,"AAAAA","1","1",1,1,0,1,1,1,1) // BLOCK_X length = 5

      val blockRecordsTemp = Seq(blockRecordsRow1).toDF().as[FwTmpQuarterPatternMatchedBlockRecords]
      val mappedBlocks = Seq(mappedBlocksRow1).toDF().as[FwTmpMappedBlocks]
      val expected = Seq(FwTmpBlockX("test",1)).toDF().as[FwTmpBlockX]

      val result = BlockXLoopCountsJobRunner.getBlockX(0, blockLength, 10, blockRecordsTemp, mappedBlocks)
      result.dtypes shouldBe expected.dtypes
    }
    it ("confirm single values")  {
      import spark.implicits._
      val blockLength = 5

      val timestamp = new java.sql.Timestamp(System.currentTimeMillis())
      val blockRecordsRow1 = FwTmpQuarterPatternMatchedBlockRecords("AAAAA","AAAAA")
      val mappedBlocksRow1 = FwTmpMappedBlocks("1111",timestamp,1,2021,"BBBBB","1","1",1,1,0,1,1,1,1) // BLOCK_X length = 5

      val blockRecordsTemp = Seq(blockRecordsRow1).toDF().as[FwTmpQuarterPatternMatchedBlockRecords]
      val mappedBlocksTemp = Seq(mappedBlocksRow1).toDF().as[FwTmpMappedBlocks]

      val result = BlockXLoopCountsJobRunner.getBlockX(0, blockLength, 1, blockRecordsTemp, mappedBlocksTemp)
//      result.show()
      result.select($"BLOCK_X").as[String].collect()(0) shouldBe "AAAAA"
    }
  }

  describe("getSSIDFilters")  {
    it("test data types") {
      import spark.implicits._
      val ldf = Seq(LuDeviceFilterMapping("carrier","type","status","launched","max_technology","ssid_pattern","vendor","model","true")).toDF().as[LuDeviceFilterMapping]
      val expected = Seq(SSIDFiltersLtrl("ssid_pattern%","ssid_pattern","pattern_ltrl")).toDF()

      val result = BlockXLoopCountsJobRunner.getSSIDFilters(ldf)

      result.dtypes shouldBe expected.dtypes
    }
    it("test filtering of distincts") {
      import spark.implicits._
      val ldfRow1 = LuDeviceFilterMapping("carrier","type","status","launched","max_technology","ssid_pattern1","vendor","model","true")
      val ldfRow2 = LuDeviceFilterMapping("carrier","type","status","launched","max_technology","ssid_pattern2","vendor","model","true")
      val ldfRow3 = LuDeviceFilterMapping("carrier","type","status","launched","max_technology","ssid_pattern2","vendor","model","true")

      val ldf = Seq(ldfRow1,ldfRow2,ldfRow3).toDF().as[LuDeviceFilterMapping]

      val expectedRow1 = SSIDFiltersLtrl("ssid_pattern1%","ssid_pattern1","pattern_ltrl")
      val expectedRow2 = SSIDFiltersLtrl("ssid_pattern2%","ssid_pattern2","pattern_ltrl")

      val expected = Seq(expectedRow1,expectedRow2).toDF()

      val result = BlockXLoopCountsJobRunner.getSSIDFilters(ldf)

//      print("expected:")
//      expected.show()

//      print("result:")
//      result.show()
    }
  }

  describe("getBlockXMatchCounts")  {
    it ("correct data types") {
      import spark.implicits._
      val input = Seq(FwTmpQuarterPatternMatchedBlockRecords("AAAA","AAAAA")).toDF().as[FwTmpQuarterPatternMatchedBlockRecords]
      val a = Seq(SSIDFiltersLtrl("ssid_pattern%","ssid_pattern","pattern_ltrl")).toDF().as[SsidFiltersLtrl]
      val expected = Seq(blockXMatchCounts("a","a","a",1,1)).toDF()
      val result = BlockXLoopCountsJobRunner.getBlockXMatchCounts(input,a,5)


      result.dtypes shouldBe expected.dtypes
    }
    it ("check case sensitivity") {
      import spark.implicits._

      val inputR1 = FwTmpQuarterPatternMatchedBlockRecords("AAAA","AAAA")
      val inputR2 = FwTmpQuarterPatternMatchedBlockRecords("AAAAB","AAAA")
      val inputR3 = FwTmpQuarterPatternMatchedBlockRecords("BBBB","BBBB")
      val input = Seq(inputR1,inputR2,inputR3).toDF().as[FwTmpQuarterPatternMatchedBlockRecords]

      val ssidFiltersR1 = SSIDFiltersLtrl("AAAA%","AAAA","AAAA")
      val ssidFiltersR2 = SSIDFiltersLtrl("BBBB%","BBBB","BBBB")
      val ssidFilters = Seq(ssidFiltersR1,ssidFiltersR2).toDF().as[SsidFiltersLtrl]

      val expectedR1 = blockXMatchCounts("BBBB","BBBB","BBBB",1,1)
      val expectedR2 = blockXMatchCounts("AAAA","AAAA","AAAA",2,1)
      val expected = Seq(expectedR1,expectedR2).toDF()

      val result = BlockXLoopCountsJobRunner.getBlockXMatchCounts(input,ssidFilters,4)
    }
    it ("correctly aggregate distinct SCAN_BSSIDs") {
      import spark.implicits._
      val inputR1 = FwTmpQuarterPatternMatchedBlockRecords("AAAA","AAAA")
      val inputR2 = FwTmpQuarterPatternMatchedBlockRecords("AAAAB","AAAA")
      val inputR3 = FwTmpQuarterPatternMatchedBlockRecords("BBBB","BBBB")
      val input = Seq(inputR1,inputR2,inputR3).toDF().as[FwTmpQuarterPatternMatchedBlockRecords]

      val ssidFiltersR1 = SSIDFiltersLtrl("AAAA%","AAAA","AAAA")
      val ssidFiltersR2 = SSIDFiltersLtrl("BBBB%","BBBB","BBBB")
      val ssidFilters = Seq(ssidFiltersR1,ssidFiltersR2).toDF().as[SsidFiltersLtrl]

      val expectedR1 = blockXMatchCounts("BBBB","BBBB","BBBB",1,1)
      val expectedR2 = blockXMatchCounts("AAAA","AAAA","AAAA",2,1)
      val expected = Seq(expectedR1,expectedR2).toDF()

      val result = BlockXLoopCountsJobRunner.getBlockXMatchCounts(input,ssidFilters,4).orderBy($"BLOCK_X".desc)

      (result.select($"BLOCK_X").as[String].collect() === expected.select($"BLOCK_X").as[String].collect() &&
        result.select($"PATTERN_LTRL").as[String].collect() === expected.select($"PATTERN_LTRL").as[String].collect() &&
        result.select($"SSID_PATTERN").as[String].collect() === expected.select($"SSID_PATTERN").as[String].collect() &&
        result.select($"MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT").as[String].collect() === expected.select($"MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT").as[String].collect()) shouldBe true

    }
  }
  describe("getBlankBlockXCounts")  {
    it ("correct data types") {
      import spark.implicits._
      val input = Seq(FwTmpQuarterPatternMatchedBlockRecords("AAAA","AAAAA")).toDF().as[FwTmpQuarterPatternMatchedBlockRecords]
      val expected = Seq(blankSSIDBSSIDCounts("a",1)).toDF()
      val result = BlockXLoopCountsJobRunner.getBlankBlockXCounts(input,6)

      result.dtypes shouldBe expected.dtypes
    }
    it ("check counting of null/empty values values")  {
      import spark.implicits._
      val inputR1 = FwTmpQuarterPatternMatchedBlockRecords("AAAA",null.asInstanceOf[String])
      val inputR2 = FwTmpQuarterPatternMatchedBlockRecords("AAAA","")
      val inputR3 = FwTmpQuarterPatternMatchedBlockRecords("AAAA","BBBB")
      val input = Seq(inputR1,inputR2,inputR3).toDF().as[FwTmpQuarterPatternMatchedBlockRecords]

      val expected = Seq(blankSSIDBSSIDCounts("AAAA",2)).toDF()

      val result = BlockXLoopCountsJobRunner.getBlankBlockXCounts(input,4)

      result.select($"BLOCK_X").as[String].collect() === expected.select($"BLOCK_X").as[String].collect() &&
        result.select($"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT").as[String].collect() === expected.select($"TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT").as[String].collect() shouldBe true
    }
  }
  describe("applyIntDataFilters")  {
    it ("correct data types") {
      import org.apache.spark.sql.functions.lit
      import spark.implicits._
      val input = Seq(fullBlockXCounts("AAAA",1,"AAAAA","AAAAA%",1,1,1)).toDF()
        .withColumn("TOTAL_BLOCK_X_FIOS_CNT",lit(0L))
        .withColumn("TOTAL_BLOCK_X_IOT_CNT",lit(0L))
        .withColumn("TOTAL_BLOCK_X_SPECTRUM_CNT", lit(0L))
      val blockX = Seq(FwTmpBlockX("a",1)).toDF().as[FwTmpBlockX]
      val expected = Seq(IntData("AAAA","AAAAA","AAAAA%",1,1,1,0,0,0)).toDF()
      val result = BlockXLoopCountsJobRunner.applyIntDataFilters(input,blockX,1,1,1,1.0,1.0,1.0)
      result.dtypes shouldBe expected.dtypes
    }
  }

  describe("getCountsByLeftSSID") {
    it ("correct data types") {
      import spark.implicits._
      val blockRecordsInput = Seq(FwTmpQuarterPatternMatchedBlockRecords("AAAA","AAAA")).toDF().as[FwTmpQuarterPatternMatchedBlockRecords]
      val intDataInput = Seq(IntData("AAAA","AAAA","AAAA",1,1,1,0,0,0)).toDF().as[IntData]
      val expected = Seq(leftSSIDCounts("AAAA","AAAA",1)).toDF()

      val result = BlockXLoopCountsJobRunner.getCountsByLeftSSID(blockRecordsInput,intDataInput,5)
      result.dtypes shouldBe expected.dtypes
    }
    it ("not like") {
      import spark.implicits._
      val blockRecordsInput = Seq(FwTmpQuarterPatternMatchedBlockRecords("AAAAAA","AAAAAA")).toDF().as[FwTmpQuarterPatternMatchedBlockRecords]
      val intDataInput = Seq(IntData("AAAA","AAAA","AAAA",1,1,1,0,0,0)).toDF().as[IntData]
      val expected = Seq(leftSSIDCounts("AAAA","AAAAAA",1)).toDF()

      val result = BlockXLoopCountsJobRunner.getCountsByLeftSSID(blockRecordsInput,intDataInput,4)
      result.count() shouldBe 0
    }
  }
  describe("applyLeftSSIDFilters")  {
    it ("correct data types") {
      import spark.implicits._

      val inputY = Seq(leftSSIDCounts("AAAA","AAAA",1)).toDF()
      val intDataInput = Seq(IntData("AAAA","AAAA","AAAA",1,1,1,0,0,0)).toDF().as[IntData]

      val expected = Seq(appliedLeftSSIDFilters("a","a","a",1,1,0,0,0,1,1)).toDF()

      val result = BlockXLoopCountsJobRunner.applyLeftSSIDFilters(inputY,intDataInput,1.0)
//      result.show()
      result.dtypes shouldBe expected.dtypes
    }
    it ("C/A < 0.2") {
      import spark.implicits._

      val inputY = Seq(leftSSIDCounts("AAAA","AAAA",1)).toDF()
      val intDataInput = Seq(IntData("AAAA","AAAA","AAAA",10,1,1,0,0,0)).toDF().as[IntData]

      val expected = Seq(appliedLeftSSIDFilters("a","a","a",1,1,0,0,0,1,1)).toDF()

      val result = BlockXLoopCountsJobRunner.applyLeftSSIDFilters(inputY,intDataInput,.2)
//      result.show()
    }
    it ("dev outputs")  {
      import spark.implicits._

      val inputYR1 = leftSSIDCounts("40:e1:e4:35:90","TMOBIL",15)
      val inputYR2 = leftSSIDCounts("38:a0:67:76:5c","TMOBIL",16)
      val inputYR3 = leftSSIDCounts("dc:8d:8a:49:b7","TMOBIL",23)
      val inputYR4 = leftSSIDCounts("08:9b:b9:d1:10","TMOBIL",14)
      val inputYR5 = leftSSIDCounts("c4:e5:32:dc:82","TMOBIL",14)

      val inputY = Seq(inputYR1,inputYR2,inputYR3,inputYR4,inputYR5).toDF()

      val intDataInputR1 = IntData("40:e1:e4:35:90","TMOBILE-","TMOBILE-",31,29,1,0,0,0)
      val intDataInputR2 = IntData("38:a0:67:76:5c","TMOBILE-","TMOBILE-",35,31,0,0,0,0)
      val intDataInputR3 = IntData("dc:8d:8a:49:b7","TMOBILE-","TMOBILE-",20,16,0,0,0,0)
      val intDataInputR4 = IntData("08:9b:b9:d1:10","TMOBILE-","TMOBILE-",20,18,0,0,0,0)
      val intDataInputR5 = IntData("c4:e5:32:dc:82","TMOBILE-","TMOBILE-",36,32,0,0,0,0)

      val intDataInput = Seq(intDataInputR1,intDataInputR2,intDataInputR3,intDataInputR4,intDataInputR5).toDF().as[IntData]
      val result = BlockXLoopCountsJobRunner.applyLeftSSIDFilters(inputY,intDataInput,.2)
//      result.show()
    }
  }

  describe("block_x test")  {
    it ("test 1") {
      import spark.implicits._
      val blockRecordsRow1 = FwTmpQuarterPatternMatchedBlockRecords("1c:3b:f3:cd:d3:56","CECILIA GONZALEZ")
      val blockRecordsRow2 = FwTmpQuarterPatternMatchedBlockRecords("1c:3b:f3:cd:d3:6e","the shannons deco")
      val blockRecordsRow3 = FwTmpQuarterPatternMatchedBlockRecords("1c:3b:f3:cd:d3:7a","Wampdeco")
      val blockRecordsRow4 = FwTmpQuarterPatternMatchedBlockRecords("1c:3b:f3:cd:d3:82","n32M510Lw3#01")
      val blockRecordsRow5 = FwTmpQuarterPatternMatchedBlockRecords("1c:3b:f3:cd:d3:86","ABC")

      val blockRecords = Seq(blockRecordsRow1,blockRecordsRow2,blockRecordsRow3,blockRecordsRow4,blockRecordsRow5).toDF().as[FwTmpQuarterPatternMatchedBlockRecords]

      blockRecords
        .groupBy(substring($"SCAN_BSSID",0,13))
        .agg(countDistinct($"SCAN_BSSID") as "TOTAL_BLOCK_X_BSSID_CNT")
        .select($"substring(SCAN_BSSID, 0, 13)" as "BLOCK_X", $"TOTAL_BLOCK_X_BSSID_CNT")
    }
  }

}