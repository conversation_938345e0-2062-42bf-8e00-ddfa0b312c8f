package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.CellularFixedWirelessUtils.{getQuarterEndDate, getQuarterStartDate}
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.model._
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering.OnesourceData
import com.comlinkdata.largescale.commons.LocalDateRange

import java.sql.Timestamp
import java.time.LocalDateTime
import java.time.temporal.IsoFields


class QuarterBlockMatchedRecordsJobSpec extends CldSparkBaseSpec {
  import spark.implicits._

  // Shared Constants
  val now = LocalDateTime.now()
  val filterYear = now.getYear()
  val filterQuarter = now.get(IsoFields.QUARTER_OF_YEAR)
  val tsNow = Timestamp.valueOf(now)
  val dateRange = LocalDateRange.of(getQuarterStartDate(now.toLocalDate), getQuarterEndDate(now.toLocalDate))

  // Object for shared dataset/dataframes to avoid reprocessing each test
  object SampleTables{
    val sampleBlock6 = Seq(FwTmpBlock6("AAAAAAAA")).toDS()
    val sampleWiFiScans = Seq(
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "AAAAAAAAAA", "a", Array(BigInt(10)), BigInt(0), "AAAAAAAAAA", 0, "AAAAA", "a", null, null, "AAAAA"), // A - good
      OnesourceData(Timestamp.valueOf(now.minusYears(1)), 0, 0.0, 0.0, 0, tsNow, "BBBBBBBBBB", "a", Array(BigInt(10)), BigInt(0), "BBBBBBBBBB", 0, "ABBBB", "a", null, null, "ABBBB"), // B - bad year
      OnesourceData(Timestamp.valueOf(now.minusMonths(3)), 0, 0.0, 0.0, 0, tsNow, "CCCCCCCCCC", "a", Array(BigInt(10)), BigInt(0), "CCCCCCCCCC", 0, "ACCCC", "a", null, null, "ACCCC"), // C - bad quarter
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, null, "a", Array(BigInt(10)), BigInt(0), null, 0, "ADDDD", "a", null, null, "ADDDD"), // D - bad null bssid
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "-16384", "a", Array(BigInt(10)), BigInt(0), "-16384", 0, "AEEEE", "a", null, null, "AEEEE"), // E - bad bssid "-16384"
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "-32768", "a", Array(BigInt(10)), BigInt(0), "-32768", 0, "AFFFF", "a", null, null, "AFFFF"), // F - bad bssid "-32768"
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "00:00:00:00:00:00", "a", Array(BigInt(10)), BigInt(0), "00:00:00:00:00:00", 0, "AGGGG", "a", null, null, "AGGGG"), // G - bad bssid "00:00:00:00:00:00"
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "HHHHHHHHHH", "a", Array(BigInt(1000)), BigInt(0), "HHHHHHHHHH", 0, "AHHHH", "a", null, null, "AHHHH"), // H - bad location_age > 600
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "IIIIIIIIII", "a", Array(BigInt(10)), BigInt(0), "IIIIIIIIII", 0, "IIIII", "a", null, null, "IIIII"), // I - bad SSID not in filter
      OnesourceData(Timestamp.valueOf(now.minusMinutes(5)), 0, 0.0, 0.0, 0, tsNow, "AAAAAAAAAA", "a", Array(BigInt(10)), BigInt(0), "AAAAAAAAAA", 0, "AAAAA", "a", null, null, "AAAAA"), // J - bad duplicate of A
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "KKKKKKKKKK", "a", Array(BigInt(10)), BigInt(0), "KKKKKKKKKK", 0, "AAAAA", "a", null, null, "AAAAA"), // K - good, another BSSID to pass
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "AAAAAAAALL", "a", Array(BigInt(10)), BigInt(0), "AAAAAAAALL", 0, "ALLLL", "a", null, null, "ALLLL") // L - bad not distinct BLOCK_6
    ).toDS()
    val sampleMatchedRecordsResults = QuarterBlockMatchedRecordsJobRunner.extractPatternMatchedBlockRecords(sampleWiFiScans, sampleBlock6, dateRange).cache()
  }


  describe("extractPatternMatchedBlockRecords"){
    it("Filters to only the specified year"){
      SampleTables.sampleMatchedRecordsResults.where($"SCAN_BSSID".isin("AAAAAAAABB", "AAAAAAAACC")).count() shouldEqual 0
    }
    it("Filters out unwanted Scan_BSSID values"){
      SampleTables.sampleMatchedRecordsResults
        .where(
          $"SCAN_BSSID".isNull
          || $"SCAN_BSSID".isin("-16384", "-32768", "00:00:00:00:00:00")
        ).count() shouldEqual 0
    }
    it("Filters Location_Age <= 600"){
      SampleTables.sampleMatchedRecordsResults.where($"SCAN_BSSID"==="AAAAAAAAHH").count() shouldEqual 0
    }
    it("Takes Distinct values only"){
      SampleTables.sampleMatchedRecordsResults.where($"SCAN_BSSID"==="AAAAAAAAAA" && $"SCAN_SSID"==="AAAAA").count() shouldEqual 1
    }
    it("Filters only to Scan_BSSID values present in block filter's BLOCK_6"){
      SampleTables.sampleMatchedRecordsResults.where($"SCAN_BSSID"==="JJJJJJJJJJ").count() shouldEqual 0
    }
  }
}
