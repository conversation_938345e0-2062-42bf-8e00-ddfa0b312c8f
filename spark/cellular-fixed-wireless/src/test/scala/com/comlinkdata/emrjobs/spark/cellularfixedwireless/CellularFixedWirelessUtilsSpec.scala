package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.CellularFixedWirelessUtils._
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering.OnesourceData
import com.comlinkdata.largescale.commons.LocalDateRange

import java.net.URI
import java.sql.Timestamp
import java.time.{LocalDate, LocalDateTime}


case class badData(
  Scan_Timestamp: java.sql.Timestamp,
  Scan_Frequency: java.lang.Long,
  Scan_Capabilities: String,
  Meta_CreatedDate: java.sql.Timestamp,
  Scan_SignalStrength: java.lang.Long,
  Device_UID: String,
  Scan_ConnectedBSSID: String,
  Location_Latitude: Double,
  Location_Age: java.lang.Long,
  Device_DeploymentName: String,
  Scan_BSSID: String,
  Location_Longitude: Double,
  Scan_SSID: String,
  Location_Availability: java.lang.Long
)

class CellularFixedWirelessUtilsSpec extends CldSparkBaseSpec {
  import spark.implicits._
  val now = LocalDateTime.now()
  val tsNow = Timestamp.valueOf(now)

  describe("getLastWeekdayDate"){
    val expectedSaturday = LocalDate.of(2022,1,1)
    it("Get Last Saturday from any Weekday"){
      val week = Seq(
        LocalDate.of(2022,1,2), // Sunday
        LocalDate.of(2022,1,3), // Monday
        LocalDate.of(2022,1,4), // Tuesday
        LocalDate.of(2022,1,5), // Wednesday
        LocalDate.of(2022,1,6), // Thursday
        LocalDate.of(2022,1,7), // Friday
        LocalDate.of(2022,1,8) // Next Saturday (should go back because of lag)
      )
      week.foreach(w => getLastWeekdayDate("SATURDAY", w) shouldEqual expectedSaturday)
    }
    it("Handles Shorthand for weekday names"){
      getLastWeekdayDate("SATURDAY") shouldEqual getLastWeekdayDate("SAT")
    }
    it("Handles Casing"){
      getLastWeekdayDate("SATURDAY") shouldEqual getLastWeekdayDate("Saturday")
      getLastWeekdayDate("SATURDAY") shouldEqual getLastWeekdayDate("saturday")
      getLastWeekdayDate("SATURDAY") shouldEqual getLastWeekdayDate("Sat")
      getLastWeekdayDate("SATURDAY") shouldEqual getLastWeekdayDate("sat")
    }
    it("Handles Other Weekdays"){
      val sun = LocalDate.of(2022,1,2)
      val mon = LocalDate.of(2022,1,3)
      val tue = LocalDate.of(2022,1,4)
      val wed = LocalDate.of(2022,1,5)
      val thu = LocalDate.of(2022,1,6)
      val fri = LocalDate.of(2022,1,7)
      getLastWeekdayDate("SUN", sun) shouldEqual sun.minusDays(7)
      getLastWeekdayDate("MON", mon) shouldEqual mon.minusDays(7)
      getLastWeekdayDate("TUE", tue) shouldEqual tue.minusDays(7)
      getLastWeekdayDate("WED", wed) shouldEqual wed.minusDays(7)
      getLastWeekdayDate("THU", thu) shouldEqual thu.minusDays(7)
      getLastWeekdayDate("FRI", fri) shouldEqual fri.minusDays(7)
    }
  }

  describe("getQuarterEndDate"){
    it("Q1 Correct Date"){
      val d = LocalDate.of(2022,2,1) // February is part of Q1
      val results = getQuarterEndDate(d)
      val expected = LocalDate.of(2022, 3, 31)  // Q1 ends Mar 31
      results shouldBe expected
    }
    it("Q2 Correct Date"){
      val d = LocalDate.of(2022,5,1) // May is part of Q2
      val results = getQuarterEndDate(d)
      val expected = LocalDate.of(2022, 6, 30)  // Q2 ends Jun 30
      results shouldBe expected
    }
    it("Q3 Correct Date"){
      val d = LocalDate.of(2022,8,1) // August is part of Q3
      val results = getQuarterEndDate(d)
      val expected = LocalDate.of(2022, 9, 30)  // Q13ends Sep 30
      results shouldBe expected
    }
    it("Q4 Correct Date"){
      val d = LocalDate.of(2022,10,1) // October is part of Q4
      val results = getQuarterEndDate(d)
      val expected = LocalDate.of(2022, 12, 31)  // Q4 ends Dec 31
      results shouldBe expected
    }
  }

  describe("getQuarterStartDate"){
    it("Q1 Correct Date"){
      val d = LocalDate.of(2022,2,1) // February is part of Q1
      val results = getQuarterStartDate(d)
      val expected = LocalDate.of(2022, 1, 1)  // Q1 starts Jan 1
      results shouldBe expected
    }
    it("Q2 Correct Date"){
      val d = LocalDate.of(2022,5,1) // May is part of Q2
      val results = getQuarterStartDate(d)
      val expected = LocalDate.of(2022, 4, 1)  // Q2 starts Apr 1
      results shouldBe expected
    }
    it("Q3 Correct Date"){
      val d = LocalDate.of(2022,8,1) // August is part of Q3
      val results = getQuarterStartDate(d)
      val expected = LocalDate.of(2022, 7, 1)  // Q3 starts Jul 1
      results shouldBe expected
    }
    it("Q4 Correct Date"){
      val d = LocalDate.of(2022,10,1) // October is part of Q4
      val results = getQuarterStartDate(d)
      val expected = LocalDate.of(2022, 10, 1)  // Q4 starts Oct 1
      results shouldBe expected
    }
  }

  describe("wildcardToRegex"){
    it("Wraps the wildcard in (^WILDCARD_PATTERN$)"){
      val r = CellularFixedWirelessUtils.wildcardToRegex("test")
      r shouldEqual "(^test$)"
    }
    it("Replaces . with \\."){
      val r = CellularFixedWirelessUtils.wildcardToRegex("te.st")
      r shouldEqual "(^te\\.st$)"
    }
    it("Replaces % with .*?"){
      val r = CellularFixedWirelessUtils.wildcardToRegex("te%st")
      r shouldEqual "(^te.*?st$)"
    }
    it("Replaces \\_ with _"){
      val r = CellularFixedWirelessUtils.wildcardToRegex("te\\_st")
      r shouldEqual "(^te_st$)"
    }
  }

  describe("generateYearMonthDayPath"){
    it("Produces expected results"){
      val path = URI create "sample/path"
      val date = LocalDate.of(2022, 1, 1)
      val result = CellularFixedWirelessUtils.generateYearMonthDayPath(path, date)
      result shouldEqual "sample/path/year=2022/month=01/day=01"
    }
    it("Disregards trailing backslashes"){
      val path1 = URI create "sample/path"
      val path2 = URI create "sample/path/"
      val date = LocalDate.of(2022, 1, 1)
      val result1 = CellularFixedWirelessUtils.generateYearMonthDayPath(path1, date)
      val result2 = CellularFixedWirelessUtils.generateYearMonthDayPath(path2, date)
      result1 shouldEqual result2
    }
  }

  describe("adaptToWifiScans"){
//    it("Adds empty columns as needed"){
//      val x = Array(badData(tsNow, 0, "a", tsNow, 0, "a", "a", 0.0, 0, "a", "a", 0.0, "a", 0)).toDF()
//      val y = OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "a", "a", Array(BigInt(0)), BigInt(0), "a", BigInt(0), "a", "a", "a", "a", "a")
//      val converted = CellularFixedWirelessUtils.adaptToWifiScans(x)
//      converted.where($"Scan_BSSID"==="a").collect()(0) shouldEqual y
//    }
//    it("Does nothing when already WiFiScans"){
//      val a = Array(BigInt(10))
//      val x = Seq(OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "a", "a", a, BigInt(0), "a", BigInt(0), "a", "a", "a", "a", "a")).toDF()
//      val y = OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "a", "a", a, BigInt(0), "a", BigInt(0), "a", "a", "a", "a", "a")
//      val converted = CellularFixedWirelessUtils.adaptToWifiScans(x)
//      converted.where($"Scan_BSSID"==="a").collect()(0) shouldEqual y
//    }
  }
}
