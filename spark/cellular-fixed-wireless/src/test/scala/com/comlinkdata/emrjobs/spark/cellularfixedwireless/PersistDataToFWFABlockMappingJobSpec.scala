package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.PersistDataToFWFABlockMappingJobSpec._
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.model._

import java.sql.Timestamp

object PersistDataToFWFABlockMappingJobSpec {
  case class output(UUID: String, RUN_TIMESTAMP: Timestamp, BLOCK_X: String, SSID_PATTERN: String, LEFT_SSID: String,
    MAX_TOTAL_BLOCK_X_PATTERN_MATCH_BSSID_CNT: Long, TOTAL_BLOCK_X_BLANK_SSID_BSSID_CNT: Long, TOTAL_BLOCK_X_FIOS_CNT: Long, TOTAL_BLOCK_X_IOT_CNT: Long, TOTAL_BLOCK_X_SPECTRUM_CNT: Long, TOTAL_BLOCK_X_BSSID_CNT: Long, MAX_LEFT_SSID_BSSID_CNT: Long)

}

class PersistDataToFWFABlockMappingJobSpec extends CldSparkBaseSpec {
  describe("convertToFwFaBlockMappingOutput"){
    it("correct data types"){
      import spark.implicits._
      val tempOutput = Seq(output("1111",new Timestamp(System.currentTimeMillis()), "AAAA", "AAAA", "AAAA", 1, 1, 1, 1, 1, 1, 1)).toDF()
      val expected = Seq(output("1111",new Timestamp(System.currentTimeMillis()), "AAAA", "AAAA", "AAAA", 1, 1, 1, 1, 1, 1, 1)).toDF()
      val result = PersistDataToFWFABlockMappingJobRunner.convertToFwFaBlockMappingOutput(tempOutput)
      result.dtypes shouldBe expected.dtypes
    }
    it("correct data values"){
      import spark.implicits._
      val timestamp = new Timestamp(System.currentTimeMillis())
      val tempOutput = Seq(output("1111", timestamp, "AAAA", "AAAA", "AAAA", 1, 1, 1, 1, 1, 1, 1)).toDF()
      val expected = Seq(output("1111",timestamp, "AAAA", "AAAA", "AAAA", 1, 1, 1, 1, 1, 1, 1)).toDF()
      val result = PersistDataToFWFABlockMappingJobRunner.convertToFwFaBlockMappingOutput(tempOutput)
      result.collect() shouldEqual expected.as[FwFaBlockMappingOutput].collect()
    }
  }
}