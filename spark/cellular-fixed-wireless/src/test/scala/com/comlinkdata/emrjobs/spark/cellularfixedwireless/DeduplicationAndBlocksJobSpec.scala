package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering.FilteredDataScans
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.DeduplicationAndBlocks._
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.ModemInfoLookup
import org.apache.sedona.core.formatMapper.GeoJsonReader
import org.apache.sedona.sql.utils.SedonaSQLRegistrator
import org.apache.spark.sql.functions.substring
import org.locationtech.jts.geom.{Coordinate, GeometryFactory}

import java.sql.Timestamp
import java.time.{LocalDate, LocalDateTime}


trait CldDeduplicationAndBlocksJobSpec extends CldSparkBaseSpec{
  override def beforeAll(): Unit = {
    super.beforeAll()
    SedonaSQLRegistrator.registerAll(spark)
  }
}

class DeduplicationAndBlocksJobSpec extends CldDeduplicationAndBlocksJobSpec {
  // Shared Imports
  import spark.implicits._

  // Shared Vals
  val gf = new GeometryFactory()
  val tsNow = Timestamp.valueOf(LocalDateTime.now())
  val tsEarly = Timestamp.valueOf(LocalDateTime.now().minusDays(1))



  describe("ShortBssid"){
    it("Picks different substring based on rules"){
      val rules = Seq(ShortBssidSpecialRules("Model2", 3, 2)).toDS()
      val geoclustered = Seq(
        FilteredDataScans("0123456789ABCDEFG", "SSID", "Carrier", "Tech", "Vendor", "Model1", 0, 0, 1, 1, tsNow),
        FilteredDataScans("0123456789ABCDEFG", "SSID", "Carrier", "Tech", "Vendor", "Model2", 0, 0, 1, 1, tsNow)
      ).toDS()
      val results = ShortBssid.getShortBssidFromData(geoclustered, rules).cache()
      results.where($"modem_model"==="Model1").select($"short_BSSID").as[String].collect()(0) shouldEqual "0123456789ABCDEF"  // default 0-16
      results.where($"modem_model"==="Model2").select($"short_BSSID").as[String].collect()(0) shouldEqual "23"  // special rule start idx 3, substirng len 13
    }
  }

  describe("ClusterBssid"){
    it("Clusters by Short_BSSID"){
      val sb = Seq(
        ShortBssid("Carrier_A", "Technology", "Model", "Vendor", "Short_BSSID_1", tsNow, gf.createPoint(new Coordinate(0,0))),
        ShortBssid("Carrier_B", "Technology", "Model", "Vendor", "Short_BSSID_1", tsNow, gf.createPoint(new Coordinate(0,0))),
        ShortBssid("Carrier_C", "Technology", "Model", "Vendor", "Short_BSSID_1", tsNow, gf.createPoint(new Coordinate(45,45))),
        ShortBssid("Carrier_D", "Technology", "Model", "Vendor", "Short_BSSID_2", tsNow, gf.createPoint(new Coordinate(45,45)))
      ).toDS()
      val results = ClusterBssid.getClustered(sb)
      val clusters = results.orderBy($"BSSID_Carrier").select($"clusternum").as[Int].collect()
      val cluster_A = clusters(0) // Carrier_A
      val cluster_B = clusters(1) // Carrier_B
      val cluster_C = clusters(2) // Carrier_C
      val cluster_D = clusters(3) // Carrier_D
      cluster_A shouldEqual cluster_B  // matching coordinates -- same cluster
      cluster_A != cluster_C shouldBe true  // far enough to be different clusters
      cluster_D shouldEqual 1  // only record in its window, should always get clusternum 1
    }
  }

  describe("DeduplicateBssid"){
    it("Aggregates Duplicates"){
      val cb = Seq(
        ClusterBssid("Carrier_1", "Tech_1", "Model_1", "Vendor_1", "short_BSSID_1", gf.createPoint(new Coordinate(0,0)), tsNow, 1),
        ClusterBssid("Carrier_1", "Tech_1", "Model_1", "Vendor_1", "short_BSSID_1", gf.createPoint(new Coordinate(2,2)), tsEarly, 1),
        ClusterBssid("Carrier_2", "Tech_2", "Model_2", "Vendor_2", "short_BSSID_2", gf.createPoint(new Coordinate(2,2)), tsNow, 1)
      ).toDS()
      val result = DeduplicateBssid.getDeduplicate(cb)//.cache()
      val lon = result.where($"BSSID_Carrier"==="Carrier_1").select($"centroid_lon").as[Double].collect()(0)
      val lat = result.where($"BSSID_Carrier"==="Carrier_1").select($"centroid_lat").as[Double].collect()(0)
      val aggTime = result.where($"BSSID_Carrier"==="Carrier_1").select("first_observed_date").as[Timestamp].collect()(0)
      result.select($"BSSID_Carrier").distinct().count() shouldEqual 2
      lon shouldEqual 1
      lat shouldEqual 1
      aggTime.before(tsNow) shouldBe true
    }
  }

  describe("ExtrapolatedBlocks"){
    it("Map to Census blocks using Sedona SQL API"){
      val blocks = Seq(
        BlockJoinedBssid("block", "BSSID_Carrier", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
        BlockJoinedBssid("block", "BSSID_Carrier", "BSSID_Technology", "LV55", "modem_vendor", "short_BSSID", 0, 0, tsNow),
        BlockJoinedBssid("block", "US Cellular", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
        BlockJoinedBssid("block", "US Cellular", "BSSID_Technology", "FG2000", "modem_vendor", "short_BSSID", 0, 0, tsNow),
        BlockJoinedBssid("block", "BSSID_Carrier", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow), // duplicate should remove
        BlockJoinedBssid("block", "BSSID_Carrier 2", "BSSID_Technology", "LV55", "modem_vendor", "short_BSSID", 2, 0, tsNow)  // not in any polygons should remove
      ).toDS()
      val results = ExtrapolatedBlocks.getExtrapolatedBlocks(blocks).cache()
      results.count() shouldEqual 4
      results.distinct().count() shouldEqual 4
      results.select($"modem_frequency").distinct().count() shouldEqual 2
    }
    it("Selects distinct"){
      val blocks = Seq(
        BlockJoinedBssid("123456789012345", "BSSID_Carrier", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
        BlockJoinedBssid("123456789012345", "BSSID_Carrier", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow)
      ).toDS()
      val results = ExtrapolatedBlocks.getExtrapolatedBlocks(blocks).cache()
      results.count() shouldEqual 1
      results.distinct().count() shouldEqual 1
    }
  }

  describe("SubsOutput"){
    it("Adds same date to all records"){
      val date = LocalDate.of(2022,3,31)
      val blocks = Seq(
        BlockJoinedBssid("block1", "Carrier1", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
        BlockJoinedBssid("block2", "Carrier1", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
        BlockJoinedBssid("block2", "Carrier1", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
        BlockJoinedBssid("block2", "Carrier2", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow)
      ).toDS()
      val results = SubsOutput.createFromBlockJoined(blocks, date).cache()
      results.select($"date").distinct().count() shouldEqual 1
      results.select($"date").distinct().collect()(0)(0) shouldEqual java.sql.Date.valueOf(date)
    }
    it("Aggregates by block by carrier"){
      val date = LocalDate.of(2022,3,31)
      val blocks = Seq(
        BlockJoinedBssid("block1", "Carrier1", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
        BlockJoinedBssid("block2", "Carrier1", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
        BlockJoinedBssid("block2", "Carrier1", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
        BlockJoinedBssid("block2", "Carrier2", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow)
      ).toDS()
      val results = SubsOutput.createFromBlockJoined(blocks, date).cache()
      results.where($"block"==="block1" && $"carrier"==="Carrier1").select($"num_networks").collect()(0)(0) shouldEqual 1
      results.where($"block"==="block2" && $"carrier"==="Carrier1").select($"num_networks").collect()(0)(0) shouldEqual 2
      results.where($"block"==="block2" && $"carrier"==="Carrier2").select($"num_networks").collect()(0)(0) shouldEqual 1
    }
  }

  describe("FilterBusiness") {
    it("Should filter out all Biztracker exclusive models") {
      val blockJoinedUnion = Seq(
        BlockJoinedBssid("block", "Carrier", "tech", "model", "vendor", "bssid", 0.0, 0.0, tsNow),
        BlockJoinedBssid("block", "Verizon", "tech", "vznModelDoNotInclude", "vendor", "bssid", 0.0, 0.0, tsNow),
        BlockJoinedBssid("block", "Verizon", "tech", "vznModelInclude", "vendor", "bssid", 0.0, 0.0, tsNow),
        BlockJoinedBssid("block", "AT&T", "tech", "model", "ASKEY COMPUTER CORP", "bssid", 0.0, 0.0, tsNow),
        BlockJoinedBssid("block", "AT&T", "tech", "model", "vendor", "bssid", 0.0, 0.0, tsNow)
      ).toDS()

      val modemInfo = Seq(
        ModemInfoLookup("Identifier", false, "Classification", "Carrier"),
        ModemInfoLookup("vznModelInclude", false, "Classification", "Verizon"),
        ModemInfoLookup("vznModelDoNotInclude", true, "Classification", "Verizon")
      ).toDS()

      blockJoinedUnion.where($"BSSID_Carrier" === "Verizon").count() shouldEqual 2
      blockJoinedUnion.where($"BSSID_Carrier" === "AT&T").count() shouldEqual 2
      val  results = DeduplicationAndBlocksJobRunner.filterBusinessModels(blockJoinedUnion, modemInfo)
      results.where($"BSSID_Carrier" === "Verizon").count() shouldEqual 1
      results.where($"BSSID_Carrier" === "AT&T").count() shouldEqual 1
      results.count() shouldEqual 3


    }
  }

  /**
    * @Note This unit test was a "hacky" workaround to an existing problem, and should remain only as a comment
    * until a permanent solution can be discovered.
    *
    * The problem:
    * - Spark Sedona agnostic of units when calculating distance
    * - The job requires mapping points to polygons which are within X meters of the point
    * - The source points/polygons are in degrees
    * - the source points are in lon-lat pairs
    * - the source polygons are in lat-lon pairs
    *
    * What needs to be done:
    * - both need to be converted into a meter based XY pair instead
    * - this conversion is from EPSG:4326 coordinate system into EPSG:3857 (Mercador Web)
    *
    * Attempted fixes (all worked locally, but failed on EMR cluster):
    * - convert in a similar way to the below during the job: failed with NoSuchAuthorityCodeException "EPSG" is unknown
    * - skip the conversion process in sedona by applying directly to the geometries via Geotools: similar error
    * - add gt-epsg-hsql dependency to project: same error
    * - add several other dependencies for the epsg hsql database: same error
    * - update geotools wrapper version: same error
    * - added a plugin which uses gt-epsg-hsql as a dependency to the project (osGeo): same error
    * - Manually use math to convert the coordinates into their appropriate meter-based values: took 4.5 hours to get to task 700/4096
    *
    * What the below does:
    * 1. I manually downloaded all the geojson census block polygons
    * 2. Load into memory the geojson files
    * 3. convert each lat-lon pair into lon-lat
    * 4. use SpatialRDD's built-in conversion method to convert to appropriate meter based systems
    * 5. save the results as new geojson biles locally
    * 6. re-upload the manually-converted files to s3
    * 7. Job still manually converts the centroid points into meters using math conversions, but runtime has been reduced
    *    to a single minute (sometimes 2)
    *
    * In the short term, this manually converted geojson will need to be replaced with a programatic solution in the job
    * but for current release, it's usable.
    *
    * Takes ~4 minutes to run the below tests locally
    */
//  describe("unit-convert geo-json"){
//    it("converts geo-json"){
//      val polys = StPolygons.loadPolygonsAsSpatialRdd("file:/Users/<USER>/Downloads/polygons_raw/") // local input path
//      polys.flipCoordinates() // lat,lon => lon,lat
//      polys.CRSTransform("epsg:4326", "epsg:3857")
//      polys.saveAsGeoJSON("file:/Users/<USER>/Downloads/polygons_meters/")  // local output path
//      println("done")
//    }
//    it("is readable"){
//      val polys = StPolygons.loadPolygonsAsDataset(URI create "file:/Users/<USER>/Downloads/polygons_meters/") // local path to geojson
//      polys.show()
//    }
//  }
//  describe("test"){
//    it("test"){
//      import org.apache.spark.sql.functions.expr
//      import java.net.URI
//      val blocks = Seq(
//        BlockJoinedBssid("block", "BSSID_Carrier", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
//        BlockJoinedBssid("block", "BSSID_Carrier", "BSSID_Technology", "LV55", "modem_vendor", "short_BSSID", 0, 0, tsNow),
//        BlockJoinedBssid("block", "US Cellular", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
//        BlockJoinedBssid("block", "US Cellular", "BSSID_Technology", "FG2000", "modem_vendor", "short_BSSID", 0, 0, tsNow),
//        BlockJoinedBssid("block", "BSSID_Carrier", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
//        BlockJoinedBssid("block", "BSSID_Carrier", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
//        BlockJoinedBssid("block", "BSSID_Carrier", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
//        BlockJoinedBssid("block", "BSSID_Carrier", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
//        BlockJoinedBssid("block", "BSSID_Carrier", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
//        BlockJoinedBssid("block", "BSSID_Carrier", "BSSID_Technology", "modem_model", "modem_vendor", "short_BSSID", 0, 0, tsNow),
//        BlockJoinedBssid("block", "BSSID_Carrier 2", "BSSID_Technology", "LV55", "modem_vendor", "short_BSSID", 2, 0, tsNow),
//        BlockJoinedBssid("block", "BSSID_Carrier 2", "BSSID_Technology", "LV55", "modem_vendor", "short_BSSID", 40, -105, tsNow)
//      ).toDS().repartition(4)
//      blocks.show()
//
//      val polys = StPolygons.loadPolygonsAsDataset(URI create "file:/Users/<USER>/Downloads/polygons_meters/")
//      polys.show(false)
//
//      blocks
//        .withColumn("x", CellularFixedWirelessUtils.lonToM($"centroid_lon"))
//        .withColumn("y", CellularFixedWirelessUtils.latToM($"centroid_lat"))
//        .withColumn("centroid", expr("ST_POINT(x,y)"))
//        .withColumn("centroidM", expr("ST_TRANSFORM(ST_POINT(centroid_lat, centroid_lon), \"EPSG:4326\", \"EPSG:3857\")"))
//        .withColumn("centroidG", expr("ST_TRANSFORM(ST_POINT(centroid_lat, centroid_lon), \"EPSG:4326\", \"EPSG:900913\")"))
//        .select($"centroid", $"centroidM", $"centroidG")
//        .join(polys, expr("ST_DISTANCE(centroid, geometry) <= 200"), "inner")
//        .show(false)
//    }
//  }
}
