package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering._
import com.comlinkdata.largescale.schema.cellular_fixed_wireless.lookup.{BssidExclusionsLookup, ManufacturerLookup, ModemInfoLookup}
import org.apache.sedona.sql.utils.SedonaSQLRegistrator
import org.checkerframework.checker.index.qual.SameLen
import org.locationtech.jts.geom.{Coordinate, GeometryFactory, Point}

import java.sql.Timestamp
import java.time.LocalDateTime


trait CldBssidJoinGeoclusteringJobSpec extends CldSparkBaseSpec{
  override def beforeAll(): Unit = {
    super.beforeAll()
    SedonaSQLRegistrator.registerAll(spark)
  }
}

class BssidJoinGeoclusteringJobSpec extends CldBssidJoinGeoclusteringJobSpec {
  // Shared Imports
  import spark.implicits._

  // Shared Vals
  val gf = new GeometryFactory()
  val tsNow = Timestamp.valueOf(LocalDateTime.now())

  // Object for sharing tables/query outputs between tests
  object SampleTables {
    // IngestedData shared values
    val sampleWifiData = Seq(
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "123456789ABCDEF", "B", Array(1, 2, 3), 100, "C", 100, "D", "LV55", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "FEDCBA987654321", "B", Array(1, 2, 3), 100, "C", 100, "D", "LV55", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),
    ).toDS()
    val sampleMappingBssid = Seq(
      MappingBssid("123456789ABCDEF", 16, "Verizon", "A", "A", "A", "LV55"),
      MappingBssid("FEDCBA98", 8, "US Cellular", "B", "B", "B", "FG2000")
    ).toDS()
    val ingestedDataScansResults = IngestedData.createFromScans(sampleWifiData, sampleMappingBssid, List("A", "B"))

    // ClustereddataScans shared values
    val sampleIngestedDataScans = Seq(
      IngestedDataScans("A", tsNow, "A", "A", 11.111, 22.222, "0.000", "0.000", 0, "A", "A", "A", "A"),
      IngestedDataScans("A", tsNow, "A", "A", 11.111, 22.222, "0.000", "0.000", 0, "B", "B", "B", "B"),
      IngestedDataScans("B", tsNow, "B", "B", 11.111, 22.222, "0.000", "0.000", 0, "B", "B", "B", "B")
    ).toDS()
    val clusteredDataScansResults = ClusteredData.createFromScans(sampleIngestedDataScans)

    // AggDataScans shared value
    val sampleClusteredDataScans = Seq(
      ClusteredDataScans("a", "a", "a", "a", "a", "a", gf.createPoint(new Coordinate(0,0)), 0, "a", tsNow, 0, 0),
      ClusteredDataScans("a", "a", "a", "a", "a", "a", gf.createPoint(new Coordinate(2,2)), 0, "a", tsNow, 2, 2),
      ClusteredDataScans("b", "b", "b", "b", "b", "b", gf.createPoint(new Coordinate(2,2)), 0, "b", tsNow, 2, 2),
      ClusteredDataScans("b", "b", "b", "b", "b", "b", gf.createPoint(new Coordinate(4,4)), 0, "b", tsNow, 4, 4)
    ).toDS()
    val aggDataScansResults = AggData.createFromScans(sampleClusteredDataScans)

    // ClusterFilteringScans shared values
    val sampleAggDataScans = Seq(
      AggDataScans("a", "a" , "a", "a", "a", "a", 1, gf.createPoint(new Coordinate(0,0)), 1, 1, tsNow),
      AggDataScans("a", "a" , "a", "a", "a", "a", 2, gf.createPoint(new Coordinate(0,0)), 2, 2, tsNow),
      AggDataScans("b", "b" , "a", "a", "a", "a", 2, gf.createPoint(new Coordinate(0,0)), 2, 2, tsNow)
    ).toDS()
    val clusterFilteringScansResults = ClusterFiltering.createFromScans(sampleAggDataScans)

    // FilteredDataScans shared values
    val sampleClusterFilteringScans = Seq(
      ClusterFilteringScans("a", "a", "match", 2, 2),
      ClusterFilteringScans("b", "b", "match", 2, 1)
    ).toDS()
    val filteredDataScansResults = FilteredData.createFromScans(sampleAggDataScans, sampleClusterFilteringScans)

    case class wifiLookup(Manufacturer: String, MacPrefix: String)

    val manufactuerLookup = Seq(
      ManufacturerLookup("A", "B"),
      ManufacturerLookup("PREFIX", "ASKEY COMPUTER CORP")
    ).toDS()

    val modemInfo = Seq(
      ModemInfoLookup("CGW450-400", false, "Residential", "AT&T"),
      ModemInfoLookup("ASK-NCQ1338E", false, "Residential", "Verizon"),
      ModemInfoLookup("LVSKR1", false, "Residential", "Verizon")
    ).toDS()

    val attAdapter = new AttAdapter(manufactuerLookup, modemInfo)
    val vznAdapter = new VznAdapter(modemInfo)

    val onesourceScans = Seq(
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "G", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, null, "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "G"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 3601, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "G"),
      OnesourceData(tsNow, 0.0, 0.0, 1000.1, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "G"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "ASK-NCQ1338E", "G", "F", "H"),
        OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "ASK-NCQ1338E", "G", "F", "H"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "ASK-NCQ1338E", "G", "F", "H"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "ASK-NCQ1338E", "G", "F", "H"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "ASK-NCQ1338E", "G", "F", "H"),
        OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "Z", "B", Array(1, 2, 3), 100, "C", 100, "D", "ASK-NCQ1338E", "G", "F", "H"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "Z", "B", Array(1, 2, 3), 100, "C", 100, "D", "ASK-NCQ1338E", "G", "F", "H"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "Z", "B", Array(1, 2, 3), 100, "C", 100, "D", "ASK-NCQ1338E", "G", "F", "H"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "Z", "B", Array(1, 2, 3), 100, "C", 100, "D", "ASK-NCQ1338E", "G", "F", "H"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "Z", "B", Array(1, 2, 3), 100, "C", 100, "D", "ASK-NCQ1338E", "G", "F", "H"),
        OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "PR:EF:IX", "B", Array(1, 2, 3), 100, "C", 100, "D", "I", "G", "PR:EF:IX", "ATT-WIFI"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "Z", "B", Array(1, 2, 3), 100, "C", 100, "D", "LVSKR1", "G", "F", "H")
    ).toDS()

    val wifiScans = Seq(
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),
      OnesourceData(tsNow, 0.0, 0.0, 0.0, 100, tsNow, "A", "B", Array(1, 2, 3), 100, "C", 100, "D", "E", "CGW450-400", "F", "abcdattefgh"),

    ).toDS()

    val exclusions = Seq(
      BssidExclusionsLookup("A", "A", 1)
    ).toDS()

    val customInclusionDataResults = CustomInclusionData.createInclusionDataset(onesourceScans, attAdapter)

    val vznIngestedData = IngestedData.createIngestedInclusions(onesourceScans, vznAdapter, exclusions)

  }




  describe("IngestedDataScans Subquery"){
    it("Joins on substring"){
      SampleTables.ingestedDataScansResults.where($"Scan_BSSID" === "123456789ABCDEF").select($"modem_model").as[String].collect()(0) shouldEqual "LV55"
      SampleTables.ingestedDataScansResults.where($"Scan_BSSID" === "FEDCBA987654321").select($"modem_model").as[String].collect()(0) shouldEqual "FG2000"
    }
    it("Inner Joins"){
      SampleTables.ingestedDataScansResults.where($"Scan_BSSID" === "00000").isEmpty shouldEqual true
    }
  }

  describe("ClusteredDataScans Subquery"){
    it("Assigns geography"){
      val point = SampleTables.clusteredDataScansResults.select($"geogpoint").collect()(0)(0).asInstanceOf[Point]
      point.getX() shouldEqual 22.222
      point.getY() shouldEqual 11.111
    }
    it("Performs DBScan on the geographies based on window"){
      SampleTables.clusteredDataScansResults.where($"Scan_BSSID" === "B" && $"cluster_num" === -1).count() shouldEqual 1
      SampleTables.clusteredDataScansResults.where($"Scan_BSSID" === "A"  && $"cluster_num" === 1).count() shouldEqual 2
    }
  }

  describe("AggDataScans Subquery"){
    it("Aggregates the centroids"){
      val centroidA = SampleTables.aggDataScansResults.where($"Scan_BSSID" === "a").select($"aggregate_centroid").collect()(0)(0).asInstanceOf[Point]
      val centroidB = SampleTables.aggDataScansResults.where($"Scan_BSSID" === "b").select($"aggregate_centroid").collect()(0)(0).asInstanceOf[Point]
      centroidA.getX() shouldEqual 1
      centroidA.getY() shouldEqual 1
      centroidB.getX() shouldEqual 3
      centroidB.getY() shouldEqual 3
    }
  }

  describe("ClusterFilteringScans Subquery"){
    it("Reduces the original table to only maxdata clusters"){
      SampleTables.clusterFilteringScansResults.where($"Scan_BSSID" === "a").select($"cluster_num").collect()(0)(0) shouldEqual 2
    }
    it("Only groups by BSSID and SSID pairs"){
      SampleTables.clusterFilteringScansResults.count() shouldEqual 2
      SampleTables.clusterFilteringScansResults.select($"Scan_SSID", $"Scan_BSSID").distinct().count() shouldEqual 2
    }
    it("Only contains 'match' records"){
      SampleTables.clusterFilteringScansResults.count() shouldEqual SampleTables.clusterFilteringScansResults.where($"cluster_match" === "match").count()
    }
  }

  describe("FilteredDataScans Subquery"){
    it("Filters to count_best_clusters === 1"){
      SampleTables.filteredDataScansResults.count() shouldEqual 1
      SampleTables.filteredDataScansResults.where($"Scan_BSSID" === "b").count() shouldEqual 1
    }
  }

  describe("AT&T Adapter testing") {
    it("Filters out model numbers not CGW450-400, null bssids, and location errors") {
      SampleTables.attAdapter.applyPreFiltering(SampleTables.onesourceScans).count() shouldEqual 8
    }
    it("only includes Wifidata with SSID containing att") {
      SampleTables.attAdapter.applyAdditionalFilters(SampleTables.wifiScans).count() shouldEqual 5
    }
  }

  describe("VZN adapter testing"){
    it("Filters out model numbers not in the vzn list, null bssids, and location errors") {
      SampleTables.vznAdapter.applyPreFiltering(SampleTables.onesourceScans).count() shouldEqual 11
    }
    it("Does no additional filtering") {
      SampleTables.vznAdapter.applyAdditionalFilters(SampleTables.wifiScans) shouldEqual SampleTables.wifiScans
    }
  }

  describe("CustomInclusionData testing for ATT") {
    it("Creates the desired dataset from the adapter") {
      SampleTables.customInclusionDataResults.count() shouldEqual 1
    }
  }

  describe("IngestedInclusions Testing for VZN") {
    it("Accurately creates the dataset with the inputs") {
      SampleTables.vznIngestedData.count() shouldEqual 6
    }
  }
}
