package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.ApplyOuiExclusionRules.{OuiLookupTable, OuiMappedBlocksForAnalysis}
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BlockXLoopCounts.MappedBlocks

import java.sql.Timestamp
import java.time.LocalDateTime


class ApplyOuiExclusionRulesJobSpec extends CldSparkBaseSpec {
  import spark.implicits._

  // Constants
  val now = LocalDateTime.now()
  val tsNow = Timestamp.valueOf(now)

  object SampleTables{

    // Sample Mapped Blocks Table
    val sampleMappedBlocks = Seq(
      MappedBlocks("UUID", tsNow, "AA:AA:AA:AA", "Verizon_", "LEFT_SSID", 0, 0, 0, 0, 0, 0, 0),  // A - Good
      MappedBlocks("UUID", tsNow, "BB:BB:BB:BB", "Pattern", "LEFT_SSID", 0, 0, 0, 0, 0, 0, 0),  // B - Bad not Verizon\\_
      MappedBlocks("UUID", tsNow, "AA:AA:AA:CC", "Verizon_", "LEFT_SSID", 0, 0, 0, 0, 0, 0, 0),  // C - Bad not distinct block_6
      MappedBlocks("UUID", tsNow, "DD:DD:DD:DD", "Verizon_", "LEFT_SSID", 0, 0, 0, 0, 0, 0, 0),  // D - Passes (used for askey end)
      MappedBlocks("UUID", tsNow, "EE:EE:EE:EE", "Verizon_", "LEFT_SSID", 0, 0, 0, 0, 0, 0, 0),  // E - Passes (used for askey start)
      MappedBlocks("UUID", tsNow, "FF:FF:FF:FF", "Verizon_", "LEFT_SSID", 0, 0, 0, 0, 0, 0, 0),  // F - Passes (used for askey start)
      MappedBlocks("UUID", tsNow, "GG:GG:GG:GG", "Verizon_", "LEFT_SSID", 0, 0, 0, 0, 0, 0, 0),  // G - maps but should be flagged for exclusion
      MappedBlocks("UUID", tsNow, "GG:GG:GG:HH", "Pattern", "LEFT_SSID", 0, 0, 0, 0, 0, 0, 0)  // H - block 6 flagged for removal, but shouldn't since not verizon
    ).toDS().cache()

    // Sample OUI Lookup Table
    val sampleOuiLookupTable = Seq(
      OuiLookupTable("00:00:00", "Vendor 1", "", "Blank description should pass"),
      OuiLookupTable("DD:DD:DD", "Vendor 2", "123 askey", "Ends with askey should pass"),
      OuiLookupTable("EE:EE:EE", "Vendor 3", "askey123", "Starts with askey should pass"),
      OuiLookupTable("FF:FF:FF", "Vendor 4", "123askey123", "Askey in middle should pass"),
      OuiLookupTable("GG:GG:GG", "Vendor 5", "ExcludeMe", "This Block should be flagged for exclusion")
    ).toDS()

    // Sample Blocks for Analysis Output
    val sampleBlocksForAnalysis = OuiMappedBlocksForAnalysis.getBlocksToAnalyze(sampleMappedBlocks, sampleOuiLookupTable).cache()

    // Sample Blocks to Exclude
    val sampleBlocksToExclude = OuiMappedBlocksForAnalysis.getBlocksToExclude(sampleBlocksForAnalysis).cache()

    // Sample Exclusions Applied to Mapped Blocks
    val sampleMappedBlocksExcluded = ApplyOuiExclusionRulesJobRunner.applyExclusionsToMappedBlocks(sampleMappedBlocks, sampleBlocksToExclude).cache()
  }



  describe("OuiMappedBlocksForAnalysis.getBlocksToAnalyze"){
    it("Filters to Verizon\\_ Patterns"){
      SampleTables.sampleBlocksForAnalysis.where($"BLOCK_6"==="BB:BB:BB").count() shouldEqual 0
    }
    it("Only Keeps Distinct BLOCK_6s"){
      SampleTables.sampleBlocksForAnalysis.where($"BLOCK_6"==="CC:CC:CC").count() shouldEqual 0
    }
  }

  describe("OuiMappedBlocksForAnalysis.getBlocksToExclude"){
    it("Retrieves rows which are not null/empty"){
      SampleTables.sampleBlocksToExclude.where($"DESCRIPTION".isNull || $"DESCRIPTION"==="").count() shouldEqual 0
    }
    it("Retrieves rows which are not associated with Askey"){
      SampleTables.sampleBlocksToExclude.where($"DESCRIPTION".like("%askey%")).count() shouldEqual 0
    }
    it("Keeps Expected Value for exclusion"){
      SampleTables.sampleBlocksToExclude.where($"DESCRIPTION"==="ExcludeMe").count() shouldEqual 1
    }
  }

  describe("applyExclusionsToMappedBlocks"){
    it("Removes records from mapped blocks"){
      SampleTables.sampleMappedBlocksExcluded.count() shouldBe SampleTables.sampleMappedBlocks.count() - 1
    }
    it("Removes correct record from mapped blocks"){
      SampleTables.sampleMappedBlocksExcluded.where($"BLOCK_X"==="GG:GG:GG:GG").count() shouldEqual 0
    }
    it("Only removes flagged blocks if associated with Verizon"){
      SampleTables.sampleMappedBlocksExcluded.where($"BLOCK_X"==="GG:GG:GG:HH").count() shouldEqual 1
    }
  }
}
