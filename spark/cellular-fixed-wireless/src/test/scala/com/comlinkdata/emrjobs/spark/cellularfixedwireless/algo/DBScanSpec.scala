package com.comlinkdata.emrjobs.spark.cellularfixedwireless.algo

import com.comlinkdata.commons.testing.DataHelperFunctions
import org.locationtech.jts.geom.{Coordinate, GeometryFactory}
import org.scalatest.{BeforeAndAfter, FunSpec, FunSpecLike, Matchers, PrivateMethodTester}

import scala.collection.immutable.HashMap
import scala.util.Random

class DBScanSpec extends FunSpec
  with FunSpecLike
  with Matchers
  with PrivateMethodTester
  with DataHelperFunctions
  with BeforeAndAfter
{
  val gf = new GeometryFactory()

  // (X, Y)
  val testPts = IndexedSeq(
    (190.47f, 261.84f),
    (192.56f, 199.05f),
    (190.47f, 138.35f),
    (205.12f, 111.14f),
    (196.74f, 171.84f),
    (211.4f,  234.63f),
    (190.47f, 305.79f),
    (188.37f, 347.65f),
    (221.86f, 347.65f),
    (265.81f, 347.65f),
    (297.21f, 316.26f),
    (297.21f, 263.93f),
    (288.84f, 207.42f),
    (282.56f, 161.37f),
    (249.07f, 113.23f),
    (567.21f, 132.07f),
    (567.21f, 165.56f),
    (567.21f, 217.88f),
    (567.21f, 238.81f),
    (567.21f, 278.58f),
    (558.84f, 318.35f),
    (556.74f, 362.3f),
    (569.3f,  136.26f),
    (602.79f, 134.16f),
    (640.47f, 134.16f),
    (644.65f, 137.3f),
    (671.86f, 180.21f),
    (665.58f, 203.23f),
    (638.37f, 217.88f),
    (621.63f, 217.88f),
    (609.07f, 222.07f),
    (590.23f, 226.26f),
    (1000.0f, 1000.0f)
  )

  // (Longitude, Latitude)
  val testBadGPS1 = IndexedSeq(
    gf.createPoint(new Coordinate(-86.3384f, 33.5879f)),
    gf.createPoint(new Coordinate(-85.282745f, 33.285187f)),
    gf.createPoint(new Coordinate(-85.282715f, 33.285187f)),
    gf.createPoint(new Coordinate(-86.3384f, 33.5879f)),
    gf.createPoint(new Coordinate(-86.3384f, 33.5879f)))

  // (Longitude, Latitude)
  val testBadGPS2= IndexedSeq(
    gf.createPoint(new Coordinate(-157.8138f, 21.4254f)),
    gf.createPoint(new Coordinate(-80.224594f, 26.176365f)),
    gf.createPoint(new Coordinate(-157.8138f, 21.4254f)),
    gf.createPoint(new Coordinate(-80.224594f, 26.176365f)),
    gf.createPoint(new Coordinate(-97.4757f, 35.6211f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176498f)),
    gf.createPoint(new Coordinate(-80.22482f, 26.17653f)),
    gf.createPoint(new Coordinate(-80.22481f, 26.176544f)),
    gf.createPoint(new Coordinate(-80.2246f, 26.176407f)),
    gf.createPoint(new Coordinate(-80.22463f, 26.17639f)),
    gf.createPoint(new Coordinate(-80.22463f, 26.17639f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176392f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176392f)),
    gf.createPoint(new Coordinate(-80.22461f, 26.1764f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176392f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176392f)),
    gf.createPoint(new Coordinate(-80.22463f, 26.17639f)),
    gf.createPoint(new Coordinate(-80.22463f, 26.17639f)),
    gf.createPoint(new Coordinate(-80.22462f, 26.17639f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176392f)),
    gf.createPoint(new Coordinate(-80.22462f, 26.17639f)),
    gf.createPoint(new Coordinate(-80.22463f, 26.17639f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176392f)),
    gf.createPoint(new Coordinate(-80.22463f, 26.17639f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176392f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176392f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176392f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176392f)),
    gf.createPoint(new Coordinate(-80.22463f, 26.17639f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176392f)),
    gf.createPoint(new Coordinate(-80.22463f, 26.17639f)),
    gf.createPoint(new Coordinate(-80.22462f, 26.17639f)),
    gf.createPoint(new Coordinate(-80.22462f, 26.17639f)),
    gf.createPoint(new Coordinate(-80.224625f, 26.176392f)),
    gf.createPoint(new Coordinate(-80.23236f, 26.17431f)))

  //IP 20 01 05 b0 47 c7 00 00 00 00 00 00 00 00 00 00
  //2021	10	02
  val testBadGPS3 = IndexedSeq(
    gf.createPoint(new Coordinate(32.945236f, -87.15225f)),
    gf.createPoint(new Coordinate(34.19592f, -88.49848f)),
    gf.createPoint(new Coordinate(32.94524f, -87.15227f)),
    gf.createPoint(new Coordinate(32.94524f, -87.15226f)),
    gf.createPoint(new Coordinate(33.04202f, -89.59194f)),
    gf.createPoint(new Coordinate(33.04202f, -89.59194f)),
    gf.createPoint(new Coordinate(33.011032f, -89.71921f)),
    gf.createPoint(new Coordinate(33.01104f, -89.71921f)),
    gf.createPoint(new Coordinate(33.011032f, -89.71921f)))

  //43 8e 70 f3
  //2021	10	02
  val testBadGPS4 = IndexedSeq(
    gf.createPoint(new Coordinate(-90.6709f, 38.5774f)),
    gf.createPoint(new Coordinate(-90.77027f, 30.8244f)),
    gf.createPoint(new Coordinate(-93.2185f, 36.6437f)),
    gf.createPoint(new Coordinate(-90.6709f, 38.5774f)),
    gf.createPoint(new Coordinate(-90.6709f, 38.5774f)),
    gf.createPoint(new Coordinate(-90.6709f, 38.57739f)),
    gf.createPoint(new Coordinate(-90.66381f, 38.58171f)),
    gf.createPoint(new Coordinate(-93.21851f, 36.643692f)),
    gf.createPoint(new Coordinate(-97.822f, 37.751f)),
    gf.createPoint(new Coordinate(-93.2185f, 36.6437f)),
    gf.createPoint(new Coordinate(-90.77027f, 30.8244f)),
    gf.createPoint(new Coordinate(-93.21851f, 36.643692f)),
    gf.createPoint(new Coordinate(-90.66382f, 38.58171f)),
    gf.createPoint(new Coordinate(-93.21851f, 36.643692f)),
    gf.createPoint(new Coordinate(-90.77027f, 30.8244f)),
    gf.createPoint(new Coordinate(-90.66381f, 38.58171f))
  )

  val testBadGPS5Repeats = IndexedSeq(
    gf.createPoint(new Coordinate(-83.08115f, 42.42887f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.428818f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.428818f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.42881f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.428818f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.42881f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.428818f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.428818f)),
    gf.createPoint(new Coordinate(-83.08126f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.428818f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.428818f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.428818f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.42881f)),
    gf.createPoint(new Coordinate(-83.08126f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08126f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08126f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.42881f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.42881f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.428818f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.428818f)),
    gf.createPoint(new Coordinate(-83.08126f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.42881f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.428818f)),
    gf.createPoint(new Coordinate(-83.08125f, 42.428818f)),
    gf.createPoint(new Coordinate(-83.08126f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08126f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08126f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08122f, 42.42888f)),
    gf.createPoint(new Coordinate(-83.08115f, 42.428814f)),
    gf.createPoint(new Coordinate(-83.08115f, 42.42881f)),
    gf.createPoint(new Coordinate(-83.08115f, 42.428814f)),
    gf.createPoint(new Coordinate(-83.08116f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08116f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08116f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08116f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08116f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08116f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08115f, 42.428814f)),
    gf.createPoint(new Coordinate(-83.08115f, 42.428814f)),
    gf.createPoint(new Coordinate(-83.08115f, 42.42881f)),
    gf.createPoint(new Coordinate(-83.08115f, 42.428814f)),
    gf.createPoint(new Coordinate(-83.08115f, 42.428814f)),
    gf.createPoint(new Coordinate(-83.08115f, 42.42881f)),
    gf.createPoint(new Coordinate(-83.08116f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08116f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08116f, 42.42882f)),
    gf.createPoint(new Coordinate(-83.08121f, 42.42887f)),
    gf.createPoint(new Coordinate(-83.08121f, 42.42887f)),
    gf.createPoint(new Coordinate(-83.08121f, 42.42887f)),
    gf.createPoint(new Coordinate(-83.08121f, 42.42887f)),
    gf.createPoint(new Coordinate(-83.08122f, 42.42888f)),
    gf.createPoint(new Coordinate(-83.08122f, 42.42888f)),
    gf.createPoint(new Coordinate(-83.08122f, 42.42888f)),
    gf.createPoint(new Coordinate(-83.08122f, 42.42888f)),
    gf.createPoint(new Coordinate(-83.08122f, 42.42888f)),
    gf.createPoint(new Coordinate(-83.08122f, 42.42888f)),
    gf.createPoint(new Coordinate(-89.1011f, 45.6789f)),
    gf.createPoint(new Coordinate(-89.1011f, 45.6789f)),
    gf.createPoint(new Coordinate(-89.1011f, 45.6789f)),
    gf.createPoint(new Coordinate(-89.1011f, 45.6789f)),
    gf.createPoint(new Coordinate(-89.1011f, 45.6789f))
  )


  describe("DB Scan"){
    it("test non GPS"){
      val (labels, clusterStats) = DBScan.cluster2DPts(testPts, 100, 3)
      for (i <- 0 to 14) labels(i) shouldEqual 1
      for (i <- 15 to 31) labels(i) shouldEqual 2
      labels(32) shouldEqual -1
    }
    it("test GPS1"){
      val (labels, clusterStats) = DBScan.clusterGPS(testBadGPS1, 1000, 3)
      clusterStats.size shouldEqual 1
    }
    it("test GPS2"){
      val (labels, clusterStats) = DBScan.clusterGPS(testBadGPS2, 1000, 3)
      clusterStats.size shouldEqual 1
    }
    it("test GPS3") {
      val (labels, clusterStats) = DBScan.clusterGPS(testBadGPS3, 1000, 3)
      labels(0) shouldEqual 1
      labels(1) shouldEqual -1
      labels(2) shouldEqual 1
      labels(3) shouldEqual 1
      labels(4) shouldEqual -1
      labels(5) shouldEqual -1
      labels(6) shouldEqual 2
      labels(7) shouldEqual 2
      labels(8) shouldEqual 2
    }
    it("test GPS4"){
      val (labels, clusterStats) = DBScan.clusterGPS(testBadGPS4, 1000, 3)
      (clusterStats.head._1.getX * 1000).toInt shouldEqual -90667
      (clusterStats.head._1.getY * 1000).toInt shouldEqual 38579
      (clusterStats(1)._1.getX * 1000).toInt shouldEqual -90770
      (clusterStats(1)._1.getY * 1000).toInt shouldEqual 30824
      (clusterStats(2)._1.getX * 1000).toInt shouldEqual -93218
      (clusterStats(2)._1.getY * 1000).toInt shouldEqual 36643
    }
    it("test GPS5"){
      val (x, y, clusterCentroids) = DBScan.clusterGPSWithBinning(testBadGPS5Repeats, 1000, 3)
      clusterCentroids.size shouldBe 2
      val (topPoints, _, clusterCentroidsTopK) = DBScan.clusterGPSWithBinningTopK(testBadGPS5Repeats, 1000, 3, Some(1))
      clusterCentroidsTopK.size shouldBe 1
      topPoints.head._1 shouldBe 12
    }
  }
}
