package com.comlinkdata.emrjobs.spark.cellularfixedwireless

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.CellularFixedWirelessUtils.{getQuarterEndDate, getQuarterStartDate}
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.model._
import com.comlinkdata.emrjobs.spark.cellularfixedwireless.models.BssidJoinGeoclustering.OnesourceData
import com.comlinkdata.largescale.commons.LocalDateRange
import org.scalatest.Succeeded

import java.time.temporal.IsoFields
import java.time.LocalDateTime
import java.time.LocalDate
import java.sql.Timestamp
import java.net.URI


class PatternBlockByQuarterJobSpec extends CldSparkBaseSpec {
  import spark.implicits._

  // Constants
  val now = LocalDateTime.now()
  val filterYear = now.getYear()
  val filterQuarter = now.get(IsoFields.QUARTER_OF_YEAR)
  val tsNow = Timestamp.valueOf(now)
  val dateRange = LocalDateRange.of(getQuarterStartDate(now.toLocalDate), getQuarterEndDate(now.toLocalDate))

  // for sharing tables between tests without re-processing every time
  object SampleTables {

    // Ssid Filters
    val sampleLuDeviceFilterMapping = Seq(
      LuDeviceFilterMapping("CARRIER", "TYPE", "STATUS", "LAUNCHED", "MAX_TECHNOLOGY", "A", "VENDOR", "MODEL", "true"), // should be in result
      LuDeviceFilterMapping("CARRIER", "TYPE", "STATUS", "LAUNCHED", "MAX_TECHNOLOGY", "B", "VENDOR", "MODEL", "false"), // should be removed
      LuDeviceFilterMapping("CARRIER", "TYPE", "STATUS", "LAUNCHED", "MAX_TECHNOLOGY", null, "VENDOR", "MODEL", "true") // should be removed
    ).toDS()
    val sampleSsidFiltersResults = PatternBlockByQuarterJobRunner.extractSsidFilters(sampleLuDeviceFilterMapping).cache()

    // Extract Block 6
    val sampleWifiScans = Seq(
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "Scan_ConnectedBSSID", "a", Array(BigInt(10)), BigInt(0), "AAAAAAAAAA", 0, "AAAAA", "a", null, null, "AAAAAAAAA"),  // A - good
      OnesourceData(Timestamp.valueOf(now.minusYears(1)), 0, 0.0, 0.0, 0, tsNow, "Scan_ConnectedBSSID", "a", Array(BigInt(10)), BigInt(0), "BBBBBBBBBB", 0, "ABBBB", "a", null, null, "AAAAAAAA"), // B - bad year
      OnesourceData(Timestamp.valueOf(now.minusMonths(3)), 0, 0.0, 0.0, 0, tsNow, "Scan_ConnectedBSSID", "a", Array(BigInt(10)), BigInt(0), "CCCCCCCCCC", 0, "ACCCC", "a", null, null, "AAAAAAA"), // C - bad quarter
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, null, "a", Array(BigInt(10)), BigInt(0), null, 0, "ADDDD", "a", null, null, "AAAAAAAAAA"),  // D - bad null bssid
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "Scan_ConnectedBSSID", "a", Array(BigInt(10)), BigInt(0), "-16384", 0, "AEEEE", "a", null, null, "AAAAAAAAAA"),  // E - bad bssid "-16384"
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "Scan_ConnectedBSSID", "a", Array(BigInt(10)), BigInt(0), "-32768", 0, "AFFFF", "a", null, null, "AAAAAAAAAA"),  // F - bad bssid "-32768"
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "Scan_ConnectedBSSID", "a", Array(BigInt(10)), BigInt(0), "00:00:00:00:00:00", 0, "AGGGG", "a", null, null, "AAAAAAAAAA"),  // G - bad bssid "00:00:00:00:00:00"
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "Scan_ConnectedBSSID", "a", Array(BigInt(1000)), BigInt(0), "HHHHHHHHHH", 0, "AHHHH", "a", null, null, "AAAAAAAAAA"),  // H - bad location_age > 600
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "Scan_ConnectedBSSID", "a", Array(BigInt(10)), BigInt(0), "IIIIIIIIII", 0, "IIIII", "a", null, null, "AAAAAAAAAA"),  // I - bad SSID not in filter
      OnesourceData(Timestamp.valueOf(now.minusMinutes(5)), 0, 0.0, 0.0, 0, tsNow, "Scan_ConnectedBSSID", "a", Array(BigInt(10)), BigInt(0), "AAAAAAAAAA", 0, "AAAAA", "a", null, null, "AAAAAA"),  // J - bad duplicate of A
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "Scan_ConnectedBSSID", "a", Array(BigInt(10)), BigInt(0), "KKKKKKKKKK", 0, "AAAAA", "a", null, null, "AAAA"),  // K - good, another BSSID to pass
      OnesourceData(tsNow, 0, 0.0, 0.0, 0, tsNow, "Scan_ConnectedBSSID", "a", Array(BigInt(10)), BigInt(0), "AAAAAAAALL", 0, "ALLLL", "a", null, null, "AAAAA")  // L - bad not distinct BLOCK_6
    ).toDS()
    val sampleExtractBlock6Results = PatternBlockByQuarterJobRunner.extractBlock6(sampleWifiScans, sampleSsidFiltersResults, 1, dateRange).cache()
  }


  describe("extractSsidFilters"){
    it("Filters where ENABLED is true"){
      SampleTables.sampleSsidFiltersResults.count() shouldEqual 1
      SampleTables.sampleSsidFiltersResults.select($"SSID_PATTERN").as[String].collect()(0) shouldEqual "A"
    }
    it("Filters where SSID_PATTERN is not null"){
      SampleTables.sampleSsidFiltersResults.where($"SSID_PATTERN".isNull).count() shouldEqual 0
    }
    it("Selects Distinct Patterns"){
      SampleTables.sampleSsidFiltersResults.count() shouldEqual SampleTables.sampleSsidFiltersResults.distinct.count()
    }
    it("Concatenates '%' to the end of the pattern"){
      SampleTables.sampleSsidFiltersResults.select($"SSID_FILTER").as[String].collect()(0) shouldEqual "A%"
    }
  }

  describe("extractBlock6"){
    it("Filters out Timestamps"){
      SampleTables.sampleExtractBlock6Results.where($"BLOCK_6".isin("BBBBBBB", "CCCCCCCC")).count() shouldEqual 0
    }
    it("Filters out Undesired Scan_BSSID values"){
      SampleTables.sampleExtractBlock6Results
        .where(
          $"BLOCK_6".isNull
          || $"BLOCK_6".isin("16384", "-32768", "00:00:00:00:00:00")
        ).count() shouldEqual 0
    }
    it("Filters Location_Age <= 600"){
      SampleTables.sampleExtractBlock6Results.where($"BLOCK_6"==="HHHHHHHH").count() shouldEqual 0
    }
    it("Filters out non-matching SSID Patterns"){
      SampleTables.sampleExtractBlock6Results.where($"BLOCK_6"==="IIIIIIII").count() shouldEqual 0
    }
    it("Takes distinct BLOCK_6 from each Scan_BSSID"){
      SampleTables.sampleExtractBlock6Results.where($"BLOCK_6"==="AAAAAAAA").count() shouldEqual
      SampleTables.sampleExtractBlock6Results.where($"BLOCK_6"==="KKKKKKKK").count() shouldEqual Succeeded
    }
    it("Filters to to keep BLOCK_6s with counts >= given threshold"){
      // since using a different count threshold for this test, wifi data will be processed fresh
      val ssidFilters = SampleTables.sampleSsidFiltersResults //PatternBlockByQuarterJobRunner.extractSsidFilters(sfr)
      val scans = Seq(OnesourceData(Timestamp.valueOf(now), 0, 0.0, 0.0, 0, tsNow, "AAAAAAAAAA", "0", Array(10), BigInt(0), "AAAAAAAAAA", BigInt(0), "AAAAA", "BigInt(0)", null, null, "AAAAA"), // good
      OnesourceData(Timestamp.valueOf(now), 0, 0.0, 0.0, 0, tsNow, "BBBBBBBBBB", "0", Array(10), BigInt(0), "BBBBBBBBBB", BigInt(0), "ABBBB", "BigInt(0)", null, null, "ABBBB"), // bad, bssid count only 1
      OnesourceData(Timestamp.valueOf(now), 0, 0.0, 0.0, 0, tsNow, "AAAAAAAACC", "0", Array(10), BigInt(0), "AAAAAAAACC", BigInt(0), "ACCCC", "BigInt(0)", null, null, "ACCCC") // good, add AAAAAAAA block 6 count to 2 (pass)
      ).toDS()
      val results = PatternBlockByQuarterJobRunner.extractBlock6(scans, ssidFilters, 2, dateRange).cache()
      results.count() shouldEqual 1
      results.select($"BLOCK_6").as[String].collect()(0) shouldEqual "AAAAAAAA"
    }
  }
}
