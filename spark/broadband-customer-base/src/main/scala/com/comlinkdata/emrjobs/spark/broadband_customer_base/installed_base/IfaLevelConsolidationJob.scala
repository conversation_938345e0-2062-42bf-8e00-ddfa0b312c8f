package com.comlinkdata.emrjobs.spark.broadband_customer_base.installed_base

import java.io.FileNotFoundException
import java.nio.file.FileAlreadyExistsException
import com.comlinkdata.emrjobs.spark.broadband_customer_base._
import com.comlinkdata.largescale.commons.{SparkJobRunner, Util<PERSON>, SparkJob}
import com.comlinkdata.largescale.schema.broadband.lookup.ConsolidatedCarrier.implicits.CarrierLookupOps
import com.comlinkdata.largescale.schema.broadband.lookup.{ConsolidatedCarrier, CarrierLookup}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.{broadcast, max}
import org.apache.spark.sql.{SparkSession, Dataset}

import scala.util.Try

object IfaLevelConsolidationJob extends SparkJob(IfaLevelConsolidationJobRunner)

object IfaLevelConsolidationJobRunner extends SparkJobRunner[IfaLevelConsolidationJobConfig]
  with LazyLogging with ProjectScopeFeatures {

  override def runJob(config: IfaLevelConsolidationJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val customerBaseDate = getCustomerBaseDateOrDefault(config.customerBaseDateOpt)
    val eligibleIfaSummaryPath = Utils.joinPaths(config.eligibleIfaBasePath, s"date=$customerBaseDate")
    val finalIfaLevelSummaryPath = Utils.joinPaths(config.finalIfaLevelSummaryBasePath, s"date=$customerBaseDate")
    val ifaLocationAndActivityHomeEligibleOpt = Try(spark.read.parquet(eligibleIfaSummaryPath).as[IfaLocationAndActivityEligible]).toOption
    val finalIfaLevelSummaryOpt = Try(spark.read.parquet(finalIfaLevelSummaryPath)).toOption

    val eligibleIfaSummary: Dataset[IfaLocationAndActivityEligible] = ifaLocationAndActivityHomeEligibleOpt getOrElse {
      throw new FileNotFoundException(s"Missing data from $eligibleIfaSummaryPath")
    }
    finalIfaLevelSummaryOpt match {
      case Some(_) =>
        throw new FileAlreadyExistsException(s"$finalIfaLevelSummaryPath already exists")
      case None =>
        val cbgToCbStats = spark.read.parquet(config.proportionalCensusBlockStatsPath).as[ProportionalCbgToCbStats]
        val cbgToCbSpStats = spark.read.parquet(config.proportionalCensusBlockSpStatsPath).as[ProportionalCbgToCbSpStats]
        val carrierLookup = CarrierLookup.read(config.carrierLookupPath)
        val consolidatedIfaLevelBase: Dataset[ConsolidatedCarrierIfaLevelBase] = eligibleIfaSummary
          .joinWith(broadcast(carrierLookup.consolidated), $"mw_carrier" === $"carrier")
          .map(toConsolidatedCarrierIfaLevelBase)
          .transform(limitToMaxMinDate)
        val blockShareIfaLevelBase: Dataset[BlockShareIfaLevelBase] = consolidatedIfaLevelBase
          .joinWith(cbgToCbSpStats,
            $"census_block_group" === cbgToCbSpStats("bg") and
            $"sp_id" === cbgToCbSpStats("consolidated_sp_id"), "left")
          .map(toIntermediaryBlockShareIfaLevelBase)
          .distinct
          .joinWith(cbgToCbStats,
            $"census_block_group" === cbgToCbStats("bg") and
            $"block_housing_units_share".isNull, "left")
          .map(toBlockShareIfaLevelBase)
          .distinct
        val finalIfaLevelBaseSummary: Dataset[FinalIfaLevelBase] = buildFinalIfaLevelBaseSummary(blockShareIfaLevelBase)
        val localOutputPath = Utils.joinPaths(config.localOutputBasePath, s"date=$customerBaseDate")
        finalIfaLevelBaseSummary.write.parquet(localOutputPath)
    }
  }

  /**
    * Join in consolidated carrier information (consolidated carrier, consolidated id, sp id, sp platform)
    *
    * @param tuple (IfaLocationAndActivityEligible, ConsolidatedCarrier)
    * @return
    */
  private def toConsolidatedCarrierIfaLevelBase(tuple: (IfaLocationAndActivityEligible, ConsolidatedCarrier)): ConsolidatedCarrierIfaLevelBase = {
    val (ifaLocationAndActivityEligible, consolidatedCarrier) = tuple
    ConsolidatedCarrierIfaLevelBase(
      ifaLocationAndActivityEligible.ifa,
      ifaLocationAndActivityEligible.is_ip_refresh,
      ifaLocationAndActivityEligible.census_block_group,
      ifaLocationAndActivityEligible.home_source_flag,
      consolidatedCarrier.mw_carrier,
      consolidatedCarrier.consolidated_carrier,
      consolidatedCarrier.consolidated_id,
      consolidatedCarrier.sp_id,
      consolidatedCarrier.sp_platform,
      consolidatedCarrier.start_date
    )
  }

  /**
    * Limit population of location and activity eligible devices to their max consolidated id
    *
    * @param input location and activity eligible devices with consolidated carrier info
    * @return
    */
  private def limitToMaxConsolidatedId(input: Dataset[ConsolidatedCarrierIfaLevelBase])
    (implicit spark: SparkSession): Dataset[ConsolidatedCarrierIfaLevelBase] = {

    import spark.implicits._
    val ifaConsolidatedIdPartition = Window.partitionBy($"ifa", $"mw_carrier")

    input
      .withColumn("max_consolidated_id", max($"consolidated_id") over ifaConsolidatedIdPartition)
      .filter($"consolidated_id" === $"max_consolidated_id")
      .drop("max_consolidated_id")
      .as[ConsolidatedCarrierIfaLevelBase]
  }

  private def limitToMaxMinDate(input: Dataset[ConsolidatedCarrierIfaLevelBase])
                               (implicit spark: SparkSession): Dataset[ConsolidatedCarrierIfaLevelBase] = {

    import spark.implicits._
    val ifaConsolidatedIdPartition = Window.partitionBy($"ifa", $"mw_carrier")

    input
      .withColumn("max_min_date", max($"start_date") over ifaConsolidatedIdPartition)
      .filter($"start_date" === $"max_min_date")
      .drop("max_min_date")
      .as[ConsolidatedCarrierIfaLevelBase]
  }
  /**
    * Reconcile ifa level base with consolidated carrier info to proportional census block group to census block by SP stats
    *
    * @param tuple (ConsolidatedCarrierIfaLevelBase, ProportionalCbgToCbSpStats)
    * @return
    */
  private def toIntermediaryBlockShareIfaLevelBase(tuple: (ConsolidatedCarrierIfaLevelBase, ProportionalCbgToCbSpStats)): BlockShareIfaLevelBase = tuple match {
    case (consolidatedIfaLevelBase, null) =>
      BlockShareIfaLevelBase(
        consolidatedIfaLevelBase.ifa,
        consolidatedIfaLevelBase.is_ip_refresh,
        consolidatedIfaLevelBase.consolidated_carrier,
        consolidatedIfaLevelBase.consolidated_id,
        consolidatedIfaLevelBase.sp_id,
        consolidatedIfaLevelBase.sp_platform,
        consolidatedIfaLevelBase.census_block_group,
        consolidatedIfaLevelBase.home_source_flag,
        None, None, None, None, None)
    case (consolidatedIfaLevelBase, proportionalCbgToCbSpStats) =>
      BlockShareIfaLevelBase(
        consolidatedIfaLevelBase.ifa,
        consolidatedIfaLevelBase.is_ip_refresh,
        consolidatedIfaLevelBase.consolidated_carrier,
        consolidatedIfaLevelBase.consolidated_id,
        consolidatedIfaLevelBase.sp_id,
        consolidatedIfaLevelBase.sp_platform,
        consolidatedIfaLevelBase.census_block_group,
        consolidatedIfaLevelBase.home_source_flag,
        Some(proportionalCbgToCbSpStats.block),
        Some(proportionalCbgToCbSpStats.serv_terr_id),
        Some(proportionalCbgToCbSpStats.hu),
        Some(proportionalCbgToCbSpStats.bg_sp_hu),
        Some(proportionalCbgToCbSpStats.hu_sp_share)
      )
  }

  /**
    * Reconcile ifa level base with consolidated carrier info to proportional census block group to census block stats
    *
    * @param tuple (BlockShareIfaLevelBase, ProportionalCbgToCbStats)
    * @return
    */
  private def toBlockShareIfaLevelBase(tuple: (BlockShareIfaLevelBase, ProportionalCbgToCbStats)): BlockShareIfaLevelBase = tuple match {
    case (intermediateBlockShareIfaLevelBase, null) => intermediateBlockShareIfaLevelBase
    case (intermediateBlockShareIfaLevelBase, proportionalCbgToCbStats) =>
      if (intermediateBlockShareIfaLevelBase.block_housing_units_share.isEmpty)
        intermediateBlockShareIfaLevelBase.copy(
          census_block = Some(proportionalCbgToCbStats.block),
          housing_units = Some(proportionalCbgToCbStats.hu),
          block_group_housing_units = Some(proportionalCbgToCbStats.bg_hu.toDouble),
          block_housing_units_share = Some(proportionalCbgToCbStats.block_hu_share)
        )
      else intermediateBlockShareIfaLevelBase
  }

  /**
    * Create final IFA-level base when census block exists
    *
    * @param input ifa-level base dataset with census block housing units and share
    * @return
    */
  private def buildFinalIfaLevelBaseSummary(
    input: Dataset[BlockShareIfaLevelBase])(implicit spark: SparkSession): Dataset[FinalIfaLevelBase] = {
    import spark.implicits._

    input
      .filter(_.census_block.isDefined)
      .groupByKey(row => (row.ifa, row.sp_id, row.census_block_group))
      .flatMapGroups {
        case ((_, _, _), ifaLevelBaseCbgToCbStats) =>
          ifaLevelBaseCbgToCbStats.map { intermediateIfaLevelBase =>
            FinalIfaLevelBase(
              intermediateIfaLevelBase.ifa,
              intermediateIfaLevelBase.is_ip_refresh,
              intermediateIfaLevelBase.consolidated_carrier,
              intermediateIfaLevelBase.sp_id,
              intermediateIfaLevelBase.sp_platform,
              intermediateIfaLevelBase.census_block_group,
              intermediateIfaLevelBase.home_source_flag,
              intermediateIfaLevelBase.census_block.get,
              intermediateIfaLevelBase.block_housing_units_share.get)
          }
      }
  }
}
