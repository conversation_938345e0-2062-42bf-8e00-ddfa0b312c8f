package com.comlinkdata.emrjobs.spark.broadband_customer_base.installed_base

import java.net.URI
import java.time.LocalDate

case class EligibleIfaJobConfig(
  customerBaseDateOpt: Option[LocalDate],
  ifaIpHomeLocationBlockGroupSummaryBasePath: String,
  bbcvIfaHomeCarrierPath: String,
  bbcvDailyHomesPath: String,
  carrierLookupPath: URI,
  localOutputBasePath: String,
  eligibleIfaBasePath: String)

