package com.comlinkdata.emrjobs.spark.broadband_customer_base.installed_base

import java.net.URI
import java.time.LocalDate

case class IfaLevelConsolidationJobConfig(
  customerBaseDateOpt: Option[LocalDate],
  proportionalCensusBlockStatsPath: String,
  proportionalCensusBlockSpStatsPath: String,
  eligibleIfaBasePath: String,
  carrierLookupPath: URI,
  localOutputBasePath: String,
  finalIfaLevelSummaryBasePath: String
)
