package com.comlinkdata.emrjobs.spark.broadband_customer_base.ip_location

import java.nio.file.FileAlreadyExistsException
import java.sql.Date
import com.comlinkdata.emrjobs.spark.broadband_customer_base._
import com.comlinkdata.largescale.commons.{SparkJobRunner, U<PERSON><PERSON>, SparkJob}
import com.comlinkdata.largescale.schema.udp.Ip
import com.comlinkdata.largescale.schema.udp.location.LocationStat.LocationStatsMap
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{IntegerType, FloatType}
import org.apache.spark.sql.{SparkSession, Dataset}

import scala.util.Try

object
IpPrimaryLocationJob extends SparkJob(IpPrimaryLocationJobRunner)

object IpPrimaryLocationJobRunner extends SparkJobRunner[IpPrimaryLocationJobConfig]
  with LazyLogging with ProjectScopeFeatures {

  override def runJob(config: IpPrimaryLocationJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val customerBaseDate = getCustomerBaseDateOrDefault(config.customerBaseDateOpt)
    val s3IpPrimaryLocationSummaryPath = Utils.joinPaths(config.ipPrimaryLocationSummaryBasePath, s"date=$customerBaseDate")
    val s3IpPrimaryLocationSummaryOpt = Try(spark.read.parquet(s3IpPrimaryLocationSummaryPath)).toOption

    s3IpPrimaryLocationSummaryOpt match {
      case Some(_) =>
        throw new FileAlreadyExistsException(s"$s3IpPrimaryLocationSummaryPath already exists")
      case None =>
        val lookBackWindow = LookBackWindow.fromCustomerBaseDate(customerBaseDate)
        val partitionDates = Utils
          .dateRange(lookBackWindow.startDate, lookBackWindow.endDate)
          .toList
        val mwDailyIfaObservationPathMw05 = Utils.joinPaths(config.s3UdpDailyIpObservation, "ds=mw05")

        val partitions = partitionDates.map(d => Utils.joinPaths(mwDailyIfaObservationPathMw05, Utils.ymdPartition(d)))

        val localWifiGpsLocations = "hdfs:/wifi-gps-locations"
        val wifiGpsLocations: Dataset[(Date, Ip, LocationStatsMap)] = spark.read
          .parquet(partitions: _*)
          .select($"partition_date" as "date", $"ip", $"location_stats")
          .as[(Date, Ip, LocationStatsMap)]
          // Filter does not work here, need to iterate over list of observations
        val location_stats: Dataset[WifiGpsLocation] = flatMapLocationStats(wifiGpsLocations)
        location_stats.write.parquet(localWifiGpsLocations)
        val ipPrimaryLocationSummary: Dataset[IpPrimaryLocation] = spark.read
          .parquet(localWifiGpsLocations)
          .as[WifiGpsLocation]
          .transform(calculateIpRoundedLocations)
          .groupByKey(_.ip)
          .mapGroups { case (ip, ipRoundedLocationsIterator) => determineIpPrimaryLocation(ip, ipRoundedLocationsIterator.toSeq) }
        val localOutputPath = Utils.joinPaths(config.localOutputBasePath, s"date=$customerBaseDate")
        ipPrimaryLocationSummary.write.parquet(localOutputPath)
    }
  }

  private def flatMapLocationStats(df: Dataset[(Date, Ip, LocationStatsMap)])(implicit spark: SparkSession) = {
    import spark.implicits._
    df.flatMap {
      case (date, ip, location_stats) =>
        location_stats flatMap {
          case obs if obs._1 == "GPS" =>
            Some(WifiGpsLocation(Utils.ipBinaryToString(ip), obs._2.centroid.lat, obs._2.centroid.lng, date))
          case _ => None
        }
    }
  }
  /**
    * Aggregate wifi gps locations by ip-lat4-long4 and calculate distinct dates and lat-longs within lat4-long4
    *
    * @param mwWifiGpsLocations list of wifi gps locations
    * @return
    */
  private def calculateIpRoundedLocations(mwWifiGpsLocations: Dataset[WifiGpsLocation])(implicit spark: SparkSession): Dataset[IpRoundedLocation] = {
    import spark.implicits._

    val (ip, lat4, long4, date, lat, long) = ($"ip", $"lat_4", $"long_4", $"date", $"latitude", $"longitude")
    val roundDigits = 4

    // to compute row_number over ip-lat4-long4-date
    val ipLat4Long4DatePartition = Window.partitionBy(ip, lat4, long4, date).orderBy(date)

    // to compute row_number over ip-lat-long
    val ipLatLongPartition = Window.partitionBy(ip, lat, long).orderBy(date)

    // to sum up row_number values equal to 1 over ip-lat4-long4
    val ipLat4Long4Partition = Window.partitionBy(ip, lat4, long4)
      .rangeBetween(Window.unboundedPreceding, Window.unboundedFollowing)

    mwWifiGpsLocations
      .withColumn("lat_4", round(lat, roundDigits) cast FloatType)
      .withColumn("long_4", round(long, roundDigits) cast FloatType)
      .withColumn("row_number_over_ip_lat4_long4_date", row_number() over ipLat4Long4DatePartition)
      .withColumn("row_number_over_ip_lat_long", row_number() over ipLatLongPartition)
      .select(ip, lat4, long4,
        sum(when($"row_number_over_ip_lat4_long4_date" === 1, 1).otherwise(0))
        over ipLat4Long4Partition cast IntegerType as "r4_distinct_dates",
        sum(when($"row_number_over_ip_lat_long" === 1, 1).otherwise(0))
        over ipLat4Long4Partition cast IntegerType as "r4_distinct_unrounded_lat_longs")
      .distinct
      .as[IpRoundedLocation]
  }

  /**
    * Determine primary lat4-long4 rounded location for each ip
    *
    * @param ip                      address ip
    * @param wifiGpsRoundedLocations list of wifi gps rounded locations (lat4-long4)
    * @return
    */
  private def determineIpPrimaryLocation(ip: String, wifiGpsRoundedLocations: Seq[IpRoundedLocation]): IpPrimaryLocation = {
    val wifiGpsRoundedLocationsSorted = wifiGpsRoundedLocations.sortBy(row => (row.r4_distinct_dates, row.r4_distinct_unrounded_lat_longs))
    val primaryRoundedLocation = wifiGpsRoundedLocationsSorted.last
    var isSecondaryLocationWithinPrimaryLat3Long3 = false

    if (wifiGpsRoundedLocationsSorted.length >= 2) {
      val secondaryRoundedLocation = wifiGpsRoundedLocationsSorted(wifiGpsRoundedLocationsSorted.length - 2)
      val primaryLocationLat3 = BigDecimal(primaryRoundedLocation.lat_4).setScale(3, BigDecimal.RoundingMode.HALF_UP)
      val primaryLocationLong3 = BigDecimal(primaryRoundedLocation.long_4).setScale(3, BigDecimal.RoundingMode.HALF_UP)
      val secondaryLocationLat3 = BigDecimal(secondaryRoundedLocation.lat_4).setScale(3, BigDecimal.RoundingMode.HALF_UP)
      val secondaryLocationLong3 = BigDecimal(secondaryRoundedLocation.long_4).setScale(3, BigDecimal.RoundingMode.HALF_UP)
      isSecondaryLocationWithinPrimaryLat3Long3 = primaryLocationLat3 == secondaryLocationLat3 && primaryLocationLong3 == secondaryLocationLong3
    }

    IpPrimaryLocation(
      ip,
      primaryRoundedLocation.lat_4,
      primaryRoundedLocation.long_4,
      primaryRoundedLocation.r4_distinct_dates,
      isSecondaryLocationWithinPrimaryLat3Long3
    )
  }
}

