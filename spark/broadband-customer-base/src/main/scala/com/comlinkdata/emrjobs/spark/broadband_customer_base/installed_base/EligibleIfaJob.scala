package com.comlinkdata.emrjobs.spark.broadband_customer_base.installed_base

import java.io.FileNotFoundException
import java.nio.file.FileAlreadyExistsException
import com.comlinkdata.emrjobs.spark.broadband_customer_base._
import com.comlinkdata.largescale.commons.{SparkJobRunner, U<PERSON><PERSON>, Spark<PERSON>ob}
import com.comlinkdata.largescale.schema.broadband.lookup.ConsolidatedCarrier.implicits.CarrierLookupOps
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SparkSession, Dataset}

import scala.util.Try

object EligibleIfaJob extends SparkJob(EligibleIfaJobRunner)

object EligibleIfaJobRunner extends SparkJobRunner[EligibleIfaJobConfig] with LazyLogging with ProjectScopeFeatures {

  override def runJob(config: EligibleIfaJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val customerBaseDate = getCustomerBaseDateOrDefault(config.customerBaseDateOpt)
    val ifaIpHomeLocationBlockGroupSummaryPath = Utils.joinPaths(config.ifaIpHomeLocationBlockGroupSummaryBasePath, s"date=$customerBaseDate")
    val eligibleIfaSummaryPath = Utils.joinPaths(config.eligibleIfaBasePath, s"date=$customerBaseDate")
    val ifaIpHomeLocationBlockGroupSummaryOpt = Try(spark.read.parquet(ifaIpHomeLocationBlockGroupSummaryPath).as[IfaIpHomeLocationBlockGroup]).toOption
    val eligibleIfaSummaryOpt = Try(spark.read.parquet(eligibleIfaSummaryPath)).toOption

    val ifaIpHomeLocationBlockGroupSummary: Dataset[IfaIpHomeLocationBlockGroup] = ifaIpHomeLocationBlockGroupSummaryOpt getOrElse {
      throw new FileNotFoundException(s"Missing data from $ifaIpHomeLocationBlockGroupSummaryPath")
    }
    eligibleIfaSummaryOpt match {
      case Some(_) =>
        throw new FileAlreadyExistsException(s"$eligibleIfaSummaryPath already exists")
      case None =>
        val lookBackWindow: LookBackWindow = LookBackWindow.fromCustomerBaseDate(customerBaseDate)
        val carrierLookup = CarrierLookup.read(config.carrierLookupPath)

        val homeLocationEligible: Dataset[IfaLocationEligible] = ifaIpHomeLocationBlockGroupSummary
          .select($"ifa",
            $"carrier",
            $"census_block_group",
            when(size($"ip_array") === 1, true) otherwise false as "is_ip_refresh")
          .distinct
          .as[IfaLocationEligible]

        val broadbandActivityEligible: Dataset[IfaHomeCarrierNoDate] = spark.read
          .parquet(config.bbcvIfaHomeCarrierPath)
          .as[IfaHomeCarrier]
          .filter(row => lookBackWindow.apply(row.date))
          .transform(carrierLookup.limitToBroadbandCarriers[IfaHomeCarrier]($"date"))
          .transform(nineOrMoreDistinctDaysOnSingleModalCarrier[IfaHomeCarrier]($"date"))
          .select($"ifa", $"carrier")
          .distinct
          .as[IfaHomeCarrierNoDate]
          .coalesce(256)

        val broadbandDailyHomesEligible: Dataset[BbcvDailyHomeEligible] = spark.read
          .parquet(config.bbcvDailyHomesPath)
          .as[BbcvDailyHome]
          .filter(row => lookBackWindow.apply(row.date))
          .transform(limitSameBlockGroupDevicesToAtLeastNineDistinctDays)
          .coalesce(512)

        val locationAndBroadbandActivityEligible: Dataset[IfaLocationAndActivityEligibleIntermediate] =
          combineLocationAndBroadbandActivityEligible(homeLocationEligible, broadbandActivityEligible)

        val eligibleIfaSummary: Dataset[IfaLocationAndActivityEligible] =
          joinToBroadbandDailyHomes(locationAndBroadbandActivityEligible, broadbandDailyHomesEligible)

        val localOutputPath = Utils.joinPaths(config.localOutputBasePath, s"date=$customerBaseDate")
        eligibleIfaSummary.write.parquet(localOutputPath)
    }
  }

  /**
    * Limit BBCV (Broadband Carrier Vision) daily homes to same block group devices to at 9 or more distinct days
    *
    * @param dailyHomes BBCV daily homes dataset
    * @return
    */
  private def limitSameBlockGroupDevicesToAtLeastNineDistinctDays(
    dailyHomes: Dataset[BbcvDailyHome])(implicit spark: SparkSession): Dataset[BbcvDailyHomeEligible] = {
    import spark.implicits._

      dailyHomes
        .groupBy($"ifa",$"geoid")
        .agg(countDistinct($"date") as "geoid_date_count")
        .withColumn(
          "geoid_rank",
          row_number over Window.partitionBy($"ifa").orderBy($"geoid_date_count".desc)
        )
        .filter($"geoid_rank" === 1)
        .filter($"geoid_date_count" >= 9)
        .select($"ifa",$"geoid" as "census_block_group")
        .distinct
        .as[BbcvDailyHomeEligible]
  }

  /**
    * Combine location and Broadband activity eligible devices (full outer join)
    *
    * @param left  ifa location eligible dataset
    * @param right ifa broadband activity eligible dataset
    * @return
    */
  private def combineLocationAndBroadbandActivityEligible(left: Dataset[IfaLocationEligible],
    right: Dataset[IfaHomeCarrierNoDate])(implicit spark: SparkSession): Dataset[IfaLocationAndActivityEligibleIntermediate] = {
    import spark.implicits._

    left
      .joinWith(right, left("ifa") === right("ifa"), "full")
      .map {
        case (null, ifaHomeCarrierNoDate) =>
          IfaLocationAndActivityEligibleIntermediate(
            ifaHomeCarrierNoDate.ifa,
            ifaHomeCarrierNoDate.carrier,
            None,
            None)
        case (ifaLocationEligible, null) =>
          IfaLocationAndActivityEligibleIntermediate(
            ifaLocationEligible.ifa,
            ifaLocationEligible.carrier,
            Some(ifaLocationEligible.is_ip_refresh),
            Some(ifaLocationEligible.census_block_group))
        case (ifaLocationEligible, ifaHomeCarrierNoDate) =>
          IfaLocationAndActivityEligibleIntermediate(
            ifaHomeCarrierNoDate.ifa,
            ifaHomeCarrierNoDate.carrier,
            Some(ifaLocationEligible.is_ip_refresh),
            Some(ifaLocationEligible.census_block_group))
      }
      .distinct
  }

  /**
    * Combine all location and Broadband activity with BBCV daily homes
    *
    * @param left  ifa location and Broadband activity eligible dataset
    * @param right BBCV daily homes dataset
    * @param spark implicit spark session
    * @return
    */
  private def joinToBroadbandDailyHomes(left: Dataset[IfaLocationAndActivityEligibleIntermediate],
    right: Dataset[BbcvDailyHomeEligible])(implicit spark: SparkSession): Dataset[IfaLocationAndActivityEligible] = {
    import spark.implicits._

    left
      .joinWith(right, left("ifa") === right("ifa"), "left")
      .flatMap {
        case (ifaLocationAndActivityEligible, null) =>
          ifaLocationAndActivityEligible.census_block_group match {
            case Some(censusBlockGroup) =>
              Some(IfaLocationAndActivityEligible(
                ifaLocationAndActivityEligible.ifa,
                ifaLocationAndActivityEligible.carrier,
                ifaLocationAndActivityEligible.is_ip_refresh,
                censusBlockGroup,
                "home_ip")
              )
            case None => None
          }
        case (ifaLocationAndActivityEligible, dailyHomeEligible) =>
          ifaLocationAndActivityEligible.census_block_group match {
            case Some(censusBlockGroup) =>
              Some(
                IfaLocationAndActivityEligible(
                  ifaLocationAndActivityEligible.ifa,
                  ifaLocationAndActivityEligible.carrier,
                  ifaLocationAndActivityEligible.is_ip_refresh,
                  censusBlockGroup,
                  "home_ip"
                )
              )
            case None =>
              Some(
                IfaLocationAndActivityEligible(
                  ifaLocationAndActivityEligible.ifa,
                  ifaLocationAndActivityEligible.carrier,
                  ifaLocationAndActivityEligible.is_ip_refresh,
                  dailyHomeEligible.census_block_group,
                  "daily_homes"
                )
              )
          }
      }
  }
}
