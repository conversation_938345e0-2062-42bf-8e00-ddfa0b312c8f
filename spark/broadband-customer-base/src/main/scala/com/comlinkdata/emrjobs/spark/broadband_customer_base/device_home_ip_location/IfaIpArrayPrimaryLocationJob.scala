package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip_location

import java.io.FileNotFoundException
import java.nio.file.FileAlreadyExistsException

import com.comlinkdata.emrjobs.spark.broadband_customer_base._
import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON>, Spark<PERSON><PERSON><PERSON>un<PERSON>, Utils}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import org.apache.spark.sql.{Dataset, SparkSession}

import scala.util.Try


object IfaIpArrayPrimaryLocationJob extends SparkJob(IfaIpArrayPrimaryLocationJobRunner)

object IfaIpArrayPrimaryLocationJobRunner extends SparkJobRunner[IfaIpArrayPrimaryLocationJobConfig]
  with LazyLogging with ProjectScopeFeatures {

  override def runJob(config: IfaIpArrayPrimaryLocationJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val customerBaseDate = getCustomerBaseDateOrDefault(config.customerBaseDateOpt)
    val ifaIpArraySummaryPath = Utils.joinPaths(config.ifaIpArraySummaryBasePath, s"date=$customerBaseDate")
    val ipPrimaryLocationSummaryPath = Utils.joinPaths(config.ipPrimaryLocationSummaryBasePath, s"date=$customerBaseDate")
    val ifaIpArrayPrimaryLocationSummaryPath = Utils.joinPaths(config.ifaIpArrayPrimaryLocationSummaryBasePath, s"date=$customerBaseDate")
    val ifaIpArraySummaryOpt = Try(spark.read.parquet(ifaIpArraySummaryPath).as[IfaIpArray]).toOption
    val ipPrimaryLocationSummaryOpt = Try(spark.read.parquet(ipPrimaryLocationSummaryPath).as[IpPrimaryLocation]).toOption
    val ifaIpArrayPrimaryLocationSummaryOpt = Try(spark.read.parquet(ifaIpArrayPrimaryLocationSummaryPath)).toOption

    val ifaIpArraySummary: Dataset[IfaIpArray] = ifaIpArraySummaryOpt getOrElse {
      throw new FileNotFoundException(s"Missing data from $ifaIpArraySummaryPath")
    }
    val ipPrimaryLocationSummary: Dataset[IpPrimaryLocation] = ipPrimaryLocationSummaryOpt getOrElse {
      throw new FileNotFoundException(s"Missing data from $ipPrimaryLocationSummaryPath")
    }
    ifaIpArrayPrimaryLocationSummaryOpt match {
      case Some(_) =>
        throw new FileAlreadyExistsException(s"$ifaIpArrayPrimaryLocationSummaryPath already exists")
      case None =>
        val ifaIpArrayLocationRankingSummary = calculateIpArrayLocationRanking(ifaIpArraySummary, ipPrimaryLocationSummary)
        val ifaIpArrayPrimaryLocationSummary = determineIfaIpArrayPrimaryLocations(ifaIpArrayLocationRankingSummary)
        val localOutputPath = Utils.joinPaths(config.localOutputBasePath, s"date=$customerBaseDate")
        ifaIpArrayPrimaryLocationSummary
          .write
          .parquet(localOutputPath)
    }
  }

  /**
    * Calculate ifa - ip array location ranking
    *
    * @param ifaIpRefreshSummary      ifa-ip refresh relationships
    * @param ipPrimaryLocationSummary ip primary locations
    * @return
    */
  private def calculateIpArrayLocationRanking(ifaIpRefreshSummary: Dataset[IfaIpArray],
    ipPrimaryLocationSummary: Dataset[IpPrimaryLocation])(implicit spark: SparkSession): Dataset[IfaIpArrayPrimaryLocation] = {
    import spark.implicits._

    val (ifa, ip, ip_array, lat4, long4) = ($"ifa", $"ip", $"ip_array", $"lat_4", $"long_4")
    val ipLat4Long4Partition = Window.partitionBy(ifa, ip_array, lat4, long4, ip).orderBy(ip)
    val ipArrayLat4Long4Partition = Window.partitionBy(ifa, ip_array, lat4, long4).orderBy(lat4, long4)
    val ipArrayPartition = Window.partitionBy(ifa, ip_array)

    ifaIpRefreshSummary
      .join(ipPrimaryLocationSummary, Seq("ip"))
      .withColumn("row_number_over_ip_lat4_long4", row_number() over ipLat4Long4Partition)
      .withColumn("row_number_over_ip_array_lat4_long4", row_number() over ipArrayLat4Long4Partition)
      .select(ifa, ip_array, lat4, long4,
        sum(when($"row_number_over_ip_lat4_long4" === 1, 1).otherwise(0))
        over ipArrayLat4Long4Partition cast IntegerType as "ip_count",
        sum($"r4_distinct_dates")
        over ipArrayLat4Long4Partition cast IntegerType as "sum_r4_distinct_dates",
        sum(when($"row_number_over_ip_array_lat4_long4" === 1, 1).otherwise(0))
        over ipArrayPartition cast IntegerType as "lat4_long4_count")
      .distinct
      .as[IfaIpArrayPrimaryLocation]
  }

  private def determineIfaIpArrayPrimaryLocations(input: Dataset[IfaIpArrayPrimaryLocation])(implicit spark: SparkSession): Dataset[IfaIpArrayPrimaryLocation] = {
    import spark.implicits._

    input
      .filter(row => row.ip_count >= 2 || (row.sum_r4_distinct_dates >= 4 && row.lat4_long4_count <= 2))
      .groupByKey(row => (row.ifa, row.ip_array))
      .mapGroups { case ((_, _), ipRefreshPrimaryLocationsIterator) =>
        choosePrimaryIpLocation(ipRefreshPrimaryLocationsIterator.toSeq)
      }
      .as[IfaIpArrayPrimaryLocation]
  }

  /**
    * Choose primary ip location based on ifa - ip array location ranking columns
    *
    * @param ipArrayPrimaryLocations ifa - ip array primary locations
    * @return
    */
  private def choosePrimaryIpLocation(ipArrayPrimaryLocations: Seq[IfaIpArrayPrimaryLocation]): IfaIpArrayPrimaryLocation =
    ipArrayPrimaryLocations.maxBy(row => (row.sum_r4_distinct_dates, row.ip_count))
}
