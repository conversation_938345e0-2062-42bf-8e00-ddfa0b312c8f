package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip_location

import java.io.FileNotFoundException
import java.nio.file.FileAlreadyExistsException

import com.comlinkdata.emrjobs.spark.broadband_customer_base._
import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON>, Spark<PERSON><PERSON><PERSON><PERSON><PERSON>, Utils}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.broadcast
import org.apache.spark.sql.magellan.dsl.expressions._
import org.apache.spark.sql.types.DoubleType
import org.apache.spark.sql.{Dataset, SparkSession}

import scala.util.Try

object IfaIpHomeLocationBlockGroupJob extends SparkJob(IfaIpHomeLocationBlockGroupJobRunner)

object IfaIpHomeLocationBlockGroupJobRunner extends SparkJobRunner[IfaIpHomeLocationBlockGroupJobConfig]
  with LazyLogging with ProjectScopeFeatures {

  override def runJob(config: IfaIpHomeLocationBlockGroupJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    magellan.Utils.injectRules(spark)

    val customerBaseDate = getCustomerBaseDateOrDefault(config.customerBaseDateOpt)
    val ifaIpArrayPrimaryLocationSummaryPath = Utils.joinPaths(config.ifaIpArrayPrimaryLocationSummaryBasePath, s"date=$customerBaseDate")
    val ifaPrimaryHomeIpSummaryPath = Utils.joinPaths(config.ifaPrimaryHomeIpSummaryBasePath, s"date=$customerBaseDate")
    val ifaIpHomeLocationBlockGroupSummaryPath = Utils.joinPaths(config.ifaIpHomeLocationBlockGroupSummaryBasePath, s"date=$customerBaseDate")
    val ifaIpArrayPrimaryLocationSummaryOpt = Try(spark.read.parquet(ifaIpArrayPrimaryLocationSummaryPath).as[IfaIpArrayPrimaryLocation]).toOption
    val ifaPrimaryHomeIpSummaryOpt = Try(spark.read.parquet(ifaPrimaryHomeIpSummaryPath).as[IfaPrimaryHomeIp]).toOption
    val ifaIpHomeLocationBlockGroupSummaryOpt = Try(spark.read.parquet(ifaIpHomeLocationBlockGroupSummaryPath)).toOption

    val ifaIpArrayPrimaryLocationSummary: Dataset[IfaIpArrayPrimaryLocation] = ifaIpArrayPrimaryLocationSummaryOpt getOrElse {
      throw new FileNotFoundException(s"Missing data from $ifaIpArrayPrimaryLocationSummaryPath")
    }
    val ifaPrimaryHomeIpSummary: Dataset[IfaPrimaryHomeIp] = ifaPrimaryHomeIpSummaryOpt getOrElse {
      throw new FileNotFoundException(s"Missing data from $ifaPrimaryHomeIpSummaryPath")
    }
    ifaIpHomeLocationBlockGroupSummaryOpt match {
      case Some(_) =>
        throw new FileAlreadyExistsException(s"$ifaIpHomeLocationBlockGroupSummaryPath already exists")
      case None =>
        val ifaIpHomeLocationSummary = joinDevicesPrimaryHomeIpToItsPrimaryLocation(ifaPrimaryHomeIpSummary, ifaIpArrayPrimaryLocationSummary)
        val blockGroups: Dataset[BlockGroup] = spark.read
          .parquet(config.s3CensusBlockGroupsLocation)
          .index(25)
          .as[BlockGroup]
        val localOutputPath = Utils.joinPaths(config.localOutputBasePath, s"date=$customerBaseDate")
        joinInBlockGroup(ifaIpHomeLocationSummary, blockGroups).write.parquet(localOutputPath)
    }
  }

  /**
    * Use ifa and ip_array columns to combine devices' primary home ip to its ip-array primary location
    *
    * @param left  ifa - primary home ip relationships
    * @param right ifa - primary location relationships
    * @return
    */
  private def joinDevicesPrimaryHomeIpToItsPrimaryLocation(left: Dataset[IfaPrimaryHomeIp],
    right: Dataset[IfaIpArrayPrimaryLocation])(implicit spark: SparkSession): Dataset[IfaIpHomeLocation] = {
    import spark.implicits._

    left
      .joinWith(right, left("ifa") === right("ifa") && left("ip_array") === right("ip_array"))
      .map { case (ifaPrimaryHomeIp: IfaPrimaryHomeIp, ifaIpArrayPrimaryLocation: IfaIpArrayPrimaryLocation) =>
        IfaIpHomeLocation(
          ifaIpArrayPrimaryLocation.ifa,
          ifaIpArrayPrimaryLocation.ip_array,
          ifaPrimaryHomeIp.carrier,
          ifaPrimaryHomeIp.min_date,
          ifaPrimaryHomeIp.max_date,
          ifaPrimaryHomeIp.distinct_days_over_ifa,
          ifaIpArrayPrimaryLocation.lat_4,
          ifaIpArrayPrimaryLocation.long_4
        )
      }
  }

  /**
    * Use lat4/long4 coordinates  within geoid's polygon to identify its census block group
    *
    * @param left  ifa - ip home location relationships
    * @param right block groups lookup table
    * @return
    */
  private def joinInBlockGroup(left: Dataset[IfaIpHomeLocation],
    right: Dataset[BlockGroup])(implicit spark: SparkSession): Dataset[IfaIpHomeLocationBlockGroup] = {
    import spark.implicits._

    val joinClause = point(
      left("long_4") cast DoubleType,
      left("lat_4") cast DoubleType) within right("polygon")

    left.joinWith(broadcast(right), joinClause)
      .repartition(200)
      .map { case (ifaIpHomeLocation: IfaIpHomeLocation, blockGroup: BlockGroup) =>
        IfaIpHomeLocationBlockGroup(
          ifaIpHomeLocation.ifa,
          ifaIpHomeLocation.ip_array,
          ifaIpHomeLocation.carrier,
          ifaIpHomeLocation.min_date,
          ifaIpHomeLocation.max_date,
          ifaIpHomeLocation.distinct_days_over_ifa,
          ifaIpHomeLocation.lat_4,
          ifaIpHomeLocation.long_4,
          blockGroup.geoid
        )
      }
  }
}
