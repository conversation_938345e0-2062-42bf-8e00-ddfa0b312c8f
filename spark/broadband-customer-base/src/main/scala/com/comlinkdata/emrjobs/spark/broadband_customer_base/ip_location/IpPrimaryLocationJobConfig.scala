package com.comlinkdata.emrjobs.spark.broadband_customer_base.ip_location

import java.time.LocalDate

case class IpPrimaryLocationJobConfig(
  customerBaseDateOpt: Option[LocalDate],
  s3UdpDailyIpObservation: String,
  localOutputBasePath: String,
  ipPrimaryLocationSummaryBasePath: String
)

object IpPrimaryLocationJobConfig {
  val example: IpPrimaryLocationJobConfig = IpPrimaryLocationJobConfig(
    customerBaseDateOpt = Some(LocalDate.parse("2019-07-01")),
    s3UdpDailyIpObservation = "s3://d000-comlinkdata-com/prod/private/udp/tier1/daily-ip-observation",
    localOutputBasePath = "hdfs:///ip-primary-location-summary",
    ipPrimaryLocationSummaryBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/d-ip-primary-location-summary/"
  )
}
