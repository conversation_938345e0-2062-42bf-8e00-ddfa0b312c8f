package com.comlinkdata.emrjobs.spark.broadband_customer_base.installed_base

import java.io.FileNotFoundException
import java.nio.file.FileAlreadyExistsException

import com.comlinkdata.emrjobs.spark.broadband_customer_base._
import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON>, Spark<PERSON>ob<PERSON><PERSON><PERSON>, Utils}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.FloatType
import org.apache.spark.sql.{Column, DataFrame, Dataset, SparkSession}

import scala.util.Try

case class CarrierAggRollup(
  child_sp_platform: Int,
  parent_sp_platform: Int,
  parent_carrier: String,
  limit_to_census_blockid: String
)

object CarrierAggRollup {
  def toDs(df: DataFrame)(implicit spark: SparkSession): Dataset[CarrierAggRollup] = {
    import spark.implicits._
    df.select(
      new Column("child_sp_platform"),
      new Column("parent_sp_platform"),
      new Column("parent_carrier"),
      explode(split(new Column("limit_to_census_blockids"), "\\|")).as("limit_to_census_blockid")
    ).as[CarrierAggRollup]
  }

  def loadFromS3(loc: String)(implicit spark: SparkSession): Dataset[CarrierAggRollup] =
    spark.read.parquet(loc).transform(toDs)

}

object AggregateJob extends SparkJob(AggregateJobRunner)

object AggregateJobRunner extends SparkJobRunner[AggregateJobConfig]
  with LazyLogging with ProjectScopeFeatures {

  override def runJob(config: AggregateJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    implicit val implicitConfig: AggregateJobConfig = config
    val customerBaseDate = getCustomerBaseDateOrDefault(config.customerBaseDateOpt)
    val finalIfaLevelSummaryPath = Utils.joinPaths(config.finalIfaLevelSummaryBasePath, s"date=$customerBaseDate")
    val aggCarrierRollup = CarrierAggRollup.loadFromS3(config.s3AggCarrierRollups)
    val finalCustomerBaseAggregatePath = Utils.joinPaths(config.finalCustomerBaseAggregateBasePath, s"date=$customerBaseDate")
    val finalIfaLevelSummaryOpt = Try(spark.read.parquet(finalIfaLevelSummaryPath).as[FinalIfaLevelBase]).toOption
    val finalCustomerBaseAggregateOpt = Try(spark.read.parquet(finalCustomerBaseAggregatePath)).toOption

    val finalIfaLevelSummary: Dataset[FinalIfaLevelBase] = finalIfaLevelSummaryOpt getOrElse {
      throw new FileNotFoundException(s"Missing data from $finalIfaLevelSummaryPath")
    }
    finalCustomerBaseAggregateOpt match {
      case Some(_) =>
        throw new FileAlreadyExistsException(s"$finalCustomerBaseAggregatePath already exists")
      case None =>
        val ifaMasterTable: Dataset[IfaMasterTableCarrierAndModelOnly] = spark.read
          .parquet(config.deviceMasterTablePath)
          .filter($"ifa".isNotNull)
          .select(unhex($"ifa") as "ifa", $"final_carrier_name", $"model_name", $"oem")
          .as[IfaMasterTableCarrierAndModelOnly]
        val intermediateCustomerBase: Dataset[IfaModelAndWirelessCarrierInfo] = finalIfaLevelSummary
          .transform(combineCarriers(aggCarrierRollup, "sp_id", "census_block"))
          .joinWith(ifaMasterTable, finalIfaLevelSummary("ifa") === ifaMasterTable("ifa"))
          .map(toIntermediateCustomerBase)
          .distinct
        val finalCustomerBaseAggregate: Dataset[FinalCustomerBaseAggregate] = calculateFinalCustomerBase(intermediateCustomerBase)
        val localOutputPath = Utils.joinPaths(config.localOutputBasePath, s"date=$customerBaseDate")
        finalCustomerBaseAggregate.coalesce(64)
          .write
          .parquet(localOutputPath)
    }
  }

  private def toIntermediateCustomerBase(tuple: (FinalIfaLevelBase, IfaMasterTableCarrierAndModelOnly))
    (implicit config: AggregateJobConfig): IfaModelAndWirelessCarrierInfo = tuple match {
    case (finalIfaLevelBase: FinalIfaLevelBase, ifaMasterTable: IfaMasterTableCarrierAndModelOnly) =>
      val consolidatedWirelessCarrierName = consolidateWirelessCarrier(ifaMasterTable.final_carrier_name)
      val consolidatedManufacturer = consolidateManufacturer(ifaMasterTable.oem)
      val consolidatedModelName = consolidateModelName(ifaMasterTable.model_name)
      IfaModelAndWirelessCarrierInfo(
        finalIfaLevelBase.ifa,
        finalIfaLevelBase.consolidated_carrier,
        finalIfaLevelBase.sp_id,
        finalIfaLevelBase.sp_platform,
        finalIfaLevelBase.census_block,
        finalIfaLevelBase.census_block_share,
        consolidatedWirelessCarrierName,
        consolidatedManufacturer,
        consolidatedModelName
      )
  }

  def combineCarriers(lookup: Dataset[CarrierAggRollup], matchSpIdColName: String, matchCensusBlockColName: String)(df: Dataset[FinalIfaLevelBase])(implicit spark: SparkSession): Dataset[FinalIfaLevelBase] = {
    import spark.implicits._

    val everywhere = -1
    val matchSpIdhCol = col(matchSpIdColName)
    val matchCensusBlockCol = col(matchCensusBlockColName)
    val consolidatedCarrierCol = col("consolidated_carrier")
    val platformSpCol = col("sp_platform")
    // * Smoosh carrier rollup lookup values onto the dataframe.
    // * Carrier rollup is specific to a block-id, unless its "-1", which means it applies everywhere.
    // * There is no time component in this table, it must be processed for all time anytime lookup table changes.

    df.withColumn("all_blockids", lit(everywhere)) // create an "everywhere" column in order to join it to the lookup table
      .join(lookup,
        Utils.and(
          $"child_sp_platform" === matchSpIdhCol, // we found a carrier match, now check for geography...
          $"all_blockids" === $"limit_to_census_blockid" || matchCensusBlockCol === $"limit_to_census_blockid" // geography check
        ),
        joinType = "left" // keep entire dataset, pass through any values that didn't join
      )
      .withColumn("temp_sp", coalesce($"parent_sp_platform", matchSpIdhCol)) // replace any values that had a hit in the lookup table, otherwise keep as original
      .withColumn("temp_sp_platform", coalesce($"parent_sp_platform", platformSpCol))
      .withColumn("temp_carrier_name", coalesce($"parent_carrier", consolidatedCarrierCol))
      .drop(matchSpIdhCol) // replace the original column with the newly generated temp column
      .drop(platformSpCol)
      .drop(consolidatedCarrierCol)
      .withColumnRenamed("temp_sp", matchSpIdColName)
      .withColumnRenamed("temp_sp_platform", "sp_platform")
      .withColumnRenamed("temp_carrier_name", "consolidated_carrier")
      .select( FinalIfaLevelBase.cols: _*
      ).as[FinalIfaLevelBase]
  }

  private def consolidateWirelessCarrier(wirelessCarrierName: Option[String])
    (implicit config: AggregateJobConfig): String = {

    wirelessCarrierName match {
      case Some(wirelessCarrier) =>
        if (config.consolidatedWirelessCarriers.contains(wirelessCarrier)) wirelessCarrier
        else if (List("US Cellular", "United States Cellular Corp").contains(wirelessCarrier)) "US Cellular"
        else "Other/Unknown"
      case None => "Other/Unknown"
    }
  }

  private def consolidateManufacturer(manufacturer: Option[String])
    (implicit config: AggregateJobConfig): String = {
    manufacturer match {
      case Some(oem) =>
        if (config.consolidatedManufacturers.contains(oem)) oem else "Other/Unknown"
      case None => "Other/Unknown"
    }
  }

  private def consolidateModelName(modelName: Option[String]): String = {
    modelName match {
      case Some(model) => if (model.isEmpty) "Undefined" else model
      case None => "Undefined"
    }
  }

  private def calculateFinalCustomerBase(input: Dataset[IfaModelAndWirelessCarrierInfo])
    (implicit spark: SparkSession): Dataset[FinalCustomerBaseAggregate] = {
    import spark.implicits._

    input
      .groupBy($"consolidated_carrier", $"sp_id", $"sp_platform", $"census_block",
        $"wireless_carrier_name", $"oem", $"model_name")
      .agg(
        sum($"census_block_share") cast FloatType as "customer_base")
      .as[FinalCustomerBaseAggregate]
  }
}
