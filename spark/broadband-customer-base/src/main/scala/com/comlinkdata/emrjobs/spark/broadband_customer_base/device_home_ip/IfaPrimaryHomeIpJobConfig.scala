package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip

import java.time.LocalDate

case class IfaPrimaryHomeIpJobConfig(
  customerBaseDateOpt: Option[LocalDate],
  ifaIpArraySummaryBasePath: String,
  ifaPrimaryHomeIpBasePath: String,
  localOutputBasePath: String)

object IfaPrimaryHomeIpJobConfig {
  val example: IfaPrimaryHomeIpJobConfig = IfaPrimaryHomeIpJobConfig(
    customerBaseDateOpt = Some(LocalDate.parse("2019-07-01")),
    ifaIpArraySummaryBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/b-ifa-ip-array-summary/",
    ifaPrimaryHomeIpBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/c-ifa-primary-home-ip-summary/",
    localOutputBasePath = "hdfs:///ifa-primary-home-ip-summary"
  )
}
