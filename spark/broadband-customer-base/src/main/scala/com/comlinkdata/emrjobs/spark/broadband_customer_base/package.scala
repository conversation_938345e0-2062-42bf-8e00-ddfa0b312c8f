package com.comlinkdata.emrjobs.spark

import com.comlinkdata.largescale.commons.Utils

import java.sql.Date
import java.time.LocalDate
import magellan.Polygon

package object broadband_customer_base {

  // ifa - ip relationship
  case class IfaIp(
    ifa: Array[Byte],
    ip: String,
    carrier: String,
    min_date: Date,
    max_date: Date,
    distinct_days_over_ifa: Int, // count of distinct days over an ifa to carry over for the customer base
    distinct_days_over_ifa_ip: Int // count of distinct days over an ifa and an ip which is then used to sum over ip array
  )

  // ifa - home carrier relationship (BBCV)
  case class IfaHomeCarrier(
    ifa: Array[Byte],
    carrier: String,
    date: Date)

  case class IfaHomeCarrierDistinctRanks(
    ifa: Array[Byte],
    carrier: String,
    distinct_ranks: Seq[Int])

  case class IfaHomeCarrierNoDate(
    ifa: Array[Byte],
    carrier: String)

  // ifa activity - broadband eligible
  case class IfaActivityEligible(
    ifa: Array[Byte],
    carrier: String)

  case class BbcvDailyHome(
    ifa: Array[Byte],
    location_rank: Short,
    geoid: String,
    distinct_hours: Long,
    date: Date)

  case class BbcvDailyHomeEligible(
    ifa: Array[Byte],
    census_block_group: String)

  // ifa - ip relationship with its next ip chronologically and overlap between the two ips
  case class IfaIpNextIpOverlap(
    ifa: Array[Byte],
    ip: String,
    carrier: String,
    min_date: Date,
    max_date: Date,
    distinct_days_over_ifa: Int,
    distinct_days_over_ifa_ip: Int,
    next_ip: Option[String],
    next_ip_min_date: Option[Date],
    next_ip_max_date: Option[Date],
    next_ip_distinct_days_over_ifa_ip: Option[Int],
    overlap: Option[String])

  // ifa - ip array relationship
  case class IfaIpArray(
    ifa: Array[Byte],
    ip: String,
    carrier: String,
    min_date: Date,
    max_date: Date,
    distinct_days_over_ifa: Int,
    distinct_days_over_ifa_ip: Int,
    ip_array: Seq[String])

  // ifa - primary home ip relationship
  case class IfaPrimaryHomeIp(
    ifa: Array[Byte],
    carrier: String,
    min_date: Date,
    max_date: Date,
    distinct_days_over_ifa: Int,
    sum_distinct_days_over_ifa_ip: Int,
    ip_array: Seq[String],
    activity: Float)

  // wifi gps ip location
  case class WifiGpsLocation(
    ip: String,
    latitude: Double,
    longitude: Double,
    date: Date)

  // wifi gps ip location with rounded lat/long coordinates, distinct dates and distinct un-rounded lat/longs
  case class IpRoundedLocation(
    ip: String,
    lat_4: Float,
    long_4: Float,
    r4_distinct_dates: Int,
    r4_distinct_unrounded_lat_longs: Int)

  // ip - primary location relationship
  case class IpPrimaryLocation(
    ip: String,
    lat_4: Float,
    long_4: Float,
    r4_distinct_dates: Int,
    is_secondary_location_within_lat_3_long_3: Boolean)

  // ifa - ip array - primary location relationship
  case class IfaIpArrayPrimaryLocation(
    ifa: Array[Byte],
    ip_array: Seq[String],
    lat_4: Float,
    long_4: Float,
    ip_count: Int,
    sum_r4_distinct_dates: Int,
    lat4_long4_count: Int)

  // ifa - ip home location relationship
  case class IfaIpHomeLocation(
    ifa: Array[Byte],
    ip_array: Seq[String],
    carrier: String,
    min_date: Date,
    max_date: Date,
    distinct_days_over_ifa: Int,
    lat_4: Float,
    long_4: Float)

  // census block group with polygon
  case class BlockGroup(
    geoid: String,
    polygon: Polygon)

  case class IfaIpHomeLocationBlockGroup(
    ifa: Array[Byte],
    ip_array: Seq[String],
    carrier: String,
    min_date: Date,
    max_date: Date,
    distinct_days_over_ifa: Int,
    lat_4: Float,
    long_4: Float,
    census_block_group: String)

  case class IfaLocationEligible(
    ifa: Array[Byte],
    carrier: String,
    census_block_group: String,
    is_ip_refresh: Boolean)

  case class IfaLocationAndActivityEligibleIntermediate(
    ifa: Array[Byte],
    carrier: String,
    is_ip_refresh: Option[Boolean],
    census_block_group: Option[String])

  case class IfaLocationAndActivityEligible(
    ifa: Array[Byte],
    carrier: String,
    is_ip_refresh: Option[Boolean],
    census_block_group: String,
    home_source_flag: String)

  case class ConsolidatedCarrierIfaLevelBase(
    ifa: Array[Byte],
    is_ip_refresh: Option[Boolean],
    census_block_group: String,
    home_source_flag: String,
    mw_carrier: String,
    consolidated_carrier: String,
    consolidated_id: String,
    sp_id: String,
    sp_platform: String,
    start_date: Date)

  case class ProportionalCbgToCbStats(
    block: String,
    bg: String, // block group
    hu: Double, // housing units
    bg_hu: Int, // housing units within block group
    block_hu_share: Double // share of housing units for census block
  )

  case class ProportionalCbgToCbSpStats(
    block: String,
    bg: String, // block group
    serv_terr_id: Int, // service territory id
    consolidated_sp_id: BigInt,
    hu: Double, // housing units
    bg_sp_hu: Double, // housing units within block group and sp
    hu_sp_share: Double // share of housing units for census block and p
  )

  case class BlockShareIfaLevelBase(
    ifa: Array[Byte],
    is_ip_refresh: Option[Boolean],
    consolidated_carrier: String,
    consolidated_id: String,
    sp_id: String,
    sp_platform: String,
    census_block_group: String,
    home_source_flag: String,
    census_block: Option[String],
    service_territory_id: Option[Int],
    housing_units: Option[Double],
    block_group_housing_units: Option[Double],
    block_housing_units_share: Option[Double])

  case class FinalIfaLevelBase(
    ifa: Array[Byte],
    is_ip_refresh: Option[Boolean],
    consolidated_carrier: String,
    sp_id: String,
    sp_platform: String,
    census_block_group: String,
    home_source_flag: String,
    census_block: String,
    census_block_share: Double)

  object FinalIfaLevelBase extends Utils.reflection.ColumnNames[FinalIfaLevelBase]

  case class IfaMasterTableCarrierAndModelOnly(
    ifa: String,
    final_carrier_name: Option[String],
    model_name: Option[String],
    oem: Option[String])

  case class IfaModelAndWirelessCarrierInfo(
    ifa: Array[Byte],
    consolidated_carrier: String,
    sp_id: String,
    sp_platform: String,
    census_block: String,
    census_block_share: Double,
    wireless_carrier_name: String,
    oem: String,
    model_name: String)

  case class FinalCustomerBaseAggregate(
    consolidated_carrier: String,
    sp_id: String,
    sp_platform: String,
    census_block: String,
    wireless_carrier_name: String,
    oem: String,
    model_name: String,
    customer_base: Float)

}
