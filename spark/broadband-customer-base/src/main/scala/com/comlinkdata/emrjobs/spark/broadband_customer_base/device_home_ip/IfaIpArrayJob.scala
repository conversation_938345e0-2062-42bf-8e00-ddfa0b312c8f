package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip

import java.io.FileNotFoundException
import java.nio.file.FileAlreadyExistsException
import java.sql.Date
import java.util.concurrent.TimeUnit

import com.comlinkdata.emrjobs.spark.broadband_customer_base._
import com.comlinkdata.largescale.commons.RichDate._
import com.comlinkdata.largescale.commons.{Spark<PERSON>ob, SparkJobRunner, Utils}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.{UserDefinedFunction, Window}
import org.apache.spark.sql.functions.{lead, struct, udf}
import org.apache.spark.sql.{Column, Dataset, SparkSession}

import scala.util.Try

object IfaIpArrayJob extends SparkJob(IfaIpArrayJobRunner)

object IfaIpArrayJobRunner extends SparkJobRunner[IfaIpArrayJobConfig] with LazyLogging with ProjectScopeFeatures {

  override def runJob(config: IfaIpArrayJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val customerBaseDate = getCustomerBaseDateOrDefault(config.customerBaseDateOpt)
    val ifaIpSummaryPath = Utils.joinPaths(config.ifaIpSummaryBasePath, s"date=$customerBaseDate")
    val ifaIpArraySummaryPath = Utils.joinPaths(config.ifaIpArraySummaryBasePath, s"date=$customerBaseDate")
    val ifaIpSummaryOpt = Try(spark.read.parquet(ifaIpSummaryPath).as[IfaIp]).toOption
    val ifaIpArraySummaryOpt = Try(spark.read.parquet(ifaIpArraySummaryPath)).toOption

    val ifaIpSummary: Dataset[IfaIp] = ifaIpSummaryOpt getOrElse {
      throw new FileNotFoundException(s"Missing data from $ifaIpSummaryPath")
    }
    ifaIpArraySummaryOpt match {
      case Some(_) =>
        throw new FileAlreadyExistsException(s"$ifaIpArraySummaryPath already exists")
      case None =>
        val localIfaIpArraySummaryPath = Utils.joinPaths(config.localOutputBasePath, s"date=$customerBaseDate")
        ifaIpSummary
          .transform(addNextIpWithOverlap)
          .transform(prepareIfaIpArraySummary)
          .write
          .parquet(localIfaIpArraySummaryPath)
    }
  }

  /**
    * Add new ip to each ifa - ip relationship including their overlap
    *
    * @param ifaIpSummary ifa-ip relationships
    * @return
    */
  private def addNextIpWithOverlap(ifaIpSummary: Dataset[IfaIp])(implicit spark: SparkSession): Dataset[IfaIpNextIpOverlap] = {
    import spark.implicits._

    val originalColumns = ifaIpSummary.columns.map(new Column(_))
    val ifaPartition = Window.partitionBy($"ifa").orderBy($"min_date")
    val calculateOverlapUdf: UserDefinedFunction = udf[Option[String], Date, Date](calculateOverlap)
    val overlap = calculateOverlapUdf($"max_date", $"next_row.next_ip_min_date") as "overlap"

    ifaIpSummary
      .withColumn("next_row", lead(struct(
        $"ip" as "next_ip",
        $"min_date" as "next_ip_min_date",
        $"max_date" as "next_ip_max_date",
        $"distinct_days_over_ifa_ip" as "next_ip_distinct_days_over_ifa_ip"), 1) over ifaPartition)
      .select(originalColumns :+ $"next_row.*" :+ overlap: _*)
      .as[IfaIpNextIpOverlap]
  }

  /**
    * Calculate overlap for an ifa-ip-next_ip relationship
    *
    * @param currentIpMaxDate max date of current ip
    * @param nextIpMinDate    min date of next ip
    * @return
    */
  private def calculateOverlap(currentIpMaxDate: Date, nextIpMinDate: Date): Option[String] = {
    nextIpMinDate match {
      // nextIpMinDate can't be an option because this method is used as a UDF from a dataframe
      case null => None
      case _ =>
        val diffInMilliseconds = nextIpMinDate.getTime - currentIpMaxDate.getTime
        val daysDiff = TimeUnit.DAYS.convert(diffInMilliseconds, TimeUnit.MILLISECONDS)
        if (daysDiff < 0) Some("O") else Some("N")
    }
  }

  /**
    * Build ifa-ip array valid relationships for both ip refreshes and non-refreshes
    *
    * @param ifaIpNextIpOverlapSummary list of ifa-ip-next_ip relationships
    * @return
    */
  private def prepareIfaIpArraySummary(ifaIpNextIpOverlapSummary: Dataset[IfaIpNextIpOverlap])(implicit spark: SparkSession): Dataset[IfaIpArray] = {
    import spark.implicits._

    ifaIpNextIpOverlapSummary
      .groupByKey(k => k.ifa)
      .flatMapGroups { case (_, ifaIpNextIpOverlapRelationshipsIterator) =>
        val ifaIpNextIpOverlapRelationships = ifaIpNextIpOverlapRelationshipsIterator.toIndexedSeq
        val isIpRefresh = isValidIpRefresh(ifaIpNextIpOverlapRelationships)
        if (isIpRefresh) {
          val ipRefreshArray = buildIpRefreshArray(ifaIpNextIpOverlapRelationships)
          ifaIpNextIpOverlapRelationships.map(record => createIfaIpArrayRelationship(record, ipRefreshArray))
        } else {
          val firstIfaIpRelationship = ifaIpNextIpOverlapRelationships.minBy(_.min_date)
          Some(createIfaIpArrayRelationship(firstIfaIpRelationship, List(firstIfaIpRelationship.ip)))
        }
      }
  }

  /**
    * Check if combinations of overlaps/non-overlaps for all current_ip - next_ip relationships are valid
    *
    * @param ifaIpNextIpOverlapRelationships list indicating whether each ip and its next ip overlap or not
    * @return
    */
  private def isValidIpRefresh(ifaIpNextIpOverlapRelationships: Seq[IfaIpNextIpOverlap]): Boolean = {
    var stack = List.empty[String]
    var index = 0
    var countOverlap = 0
    var countNonOverlap = 0
    var valid = true
    while (index < ifaIpNextIpOverlapRelationships.length && valid) {
      ifaIpNextIpOverlapRelationships(index).overlap match {
        case None =>
        case Some(overlap) =>
          if (stack.nonEmpty) {
            if (overlap == "O" && stack.head == "N" && countOverlap > 0) valid = false
            if (overlap == "N" && stack.head == "O" && countNonOverlap > 0) valid = false
          }
          if (overlap == "N") countNonOverlap += 1
          if (overlap == "O") countOverlap += 1
          stack = overlap :: stack
      }
      index += 1
    }
    if (countNonOverlap == 0) valid = false
    valid
  }

  /**
    * Build ip refresh array without overlapped ips
    *
    * @param ipNextIpOverlaps list indicating whether each ip and its next ip overlap or not
    * @return
    */
  private def buildIpRefreshArray(ipNextIpOverlaps: Seq[IfaIpNextIpOverlap]): Seq[String] = {
    var buffer = Vector.empty[String]
    var index = 0
    var stop = false
    while (index < ipNextIpOverlaps.length && !stop) {
      val ifaIpWithOverlap = ipNextIpOverlaps(index)
      ifaIpWithOverlap.overlap match {
        case None =>
          val lastOverlap = ipNextIpOverlaps(index - 1).overlap.get // overlap before a null one cannot be null
          if (lastOverlap == "N") buffer = buffer :+ ifaIpWithOverlap.ip
        case Some(overlap) =>
          if (buffer.nonEmpty && overlap == "O") {
            buffer = buffer :+ ifaIpWithOverlap.ip
            stop = true
          }
          if (overlap == "N") buffer = buffer :+ ifaIpWithOverlap.ip
      }
      index += 1
    }
    buffer
  }

  /**
    * Create ifa-ip array relationship with list of non-overlapping ips for both ip refreshes and non-refreshes
    *
    * @param ifaIpNextIpOverlap ifa-ip relationship with its next ip
    * @param ipArray            ip array from non-overlapping ip(s)
    * @return
    */
  private def createIfaIpArrayRelationship(ifaIpNextIpOverlap: IfaIpNextIpOverlap, ipArray: Seq[String]): IfaIpArray =
    IfaIpArray(
      ifaIpNextIpOverlap.ifa,
      ifaIpNextIpOverlap.ip,
      ifaIpNextIpOverlap.carrier,
      ifaIpNextIpOverlap.min_date,
      ifaIpNextIpOverlap.max_date,
      ifaIpNextIpOverlap.distinct_days_over_ifa,
      ifaIpNextIpOverlap.distinct_days_over_ifa_ip,
      ipArray)
}
