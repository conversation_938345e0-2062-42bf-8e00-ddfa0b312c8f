package com.comlinkdata.emrjobs.spark.broadband_customer_base

import java.time.LocalDate
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.{Column, Dataset, Encoder, SparkSession}
import org.apache.spark.sql.functions.{row_number, sum, when}

trait ProjectScopeFeatures {

  /**
    * Get customer base date from option or default to current year month
    *
    * @param customerBaseDateOpt local date option
    * @return
    */
  def getCustomerBaseDateOrDefault(customerBaseDateOpt: Option[LocalDate]): LocalDate = {
    customerBaseDateOpt getOrElse LocalDate.now.withDayOfMonth(1)
  }

  /**
    * Limit devices to 9+ distinct dates on **single** modal carrier
    *
    * Every IFA must have 9 or more distinct dates on their most frequent Broadband carrier
    * AND no IFA may have 9 or more distinct dates on multiple carriers
    *
    * @param input Broadband ifa raw wifi carrier agg dataset
    * @param spark implicit spark session
    * @return
    */
  def nineOrMoreDistinctDaysOnSingleModalCarrier[T : Encoder](dateCol: Column)(input: Dataset[T])
    (implicit spark: SparkSession): Dataset[T] = {
    import spark.implicits._

    // `orderBy(...)` WAS ADDED HERE BECAUSE `ifaCarrierDatePartition` IS *ONLY* USED TO APPLY row_number,
    val ifaCarrierDatePartition = Window.partitionBy($"ifa", $"carrier", dateCol).orderBy(dateCol)
    // `ifaCarrierPartition` IS USED TO CALCULATE SUM row_number=1, DO NOT ADD ANY `orderBy(...)` here
    val ifaCarrierPartition = Window.partitionBy($"ifa", $"carrier")
    // `ifaPartition` IS USED TO CALCULATE SUM row_number=1, DO NOT ADD ANY `orderBy(...)` here
    val ifaPartition = Window.partitionBy($"ifa")

    // NOTE: adding `orderBy(...)` to window function when summing values over a partition will output incorrect results

    input
      .withColumn("carrier_date_row_number", row_number over ifaCarrierDatePartition)
      .withColumn("distinct_date_count", sum(
        when($"carrier_date_row_number" === 1, 1).otherwise(0)) over ifaCarrierPartition)
      .filter($"distinct_date_count" >= 9)
      .withColumn("carrier_row_number", row_number over ifaCarrierPartition.orderBy(dateCol))
      .withColumn("distinct_carrier_count", sum(
        when($"carrier_row_number" === 1, 1).otherwise(0)) over ifaPartition)
      .filter($"distinct_carrier_count" === 1)
      .drop("carrier_date_row_number", "distinct_date_count",
        "carrier_row_number", "distinct_carrier_count")
      .as[T]
  }

}
