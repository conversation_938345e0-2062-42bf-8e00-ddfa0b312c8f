package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip_location

import java.time.LocalDate

case class IfaIpHomeLocationBlockGroupJobConfig(
  customerBaseDateOpt: Option[LocalDate],
  ifaIpArrayPrimaryLocationSummaryBasePath: String,
  ifaPrimaryHomeIpSummaryBasePath: String,
  ifaIpHomeLocationBlockGroupSummaryBasePath: String,
  localOutputBasePath: String,
  s3CensusBlockGroupsLocation: String)

object IfaIpHomeLocationBlockGroupJobConfig {
  val example: IfaIpHomeLocationBlockGroupJobConfig = IfaIpHomeLocationBlockGroupJobConfig(
    customerBaseDateOpt = Some(LocalDate.parse("2019-07-01")),
    ifaIpArrayPrimaryLocationSummaryBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/e-ip-array-primary-location-summary",
    ifaPrimaryHomeIpSummaryBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/c-ifa-primary-home-ip-summary",
    ifaIpHomeLocationBlockGroupSummaryBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/f-ifa-ip-home-location-block-group-summary/",
    localOutputBasePath = "hdfs:///ifa-ip-home-location-block-group",
    s3CensusBlockGroupsLocation = "s3://d000-comlinkdata-com/prod/private/broadband/lookup_tables/cb_2016_us_block_groups_parquet"
  )
}
