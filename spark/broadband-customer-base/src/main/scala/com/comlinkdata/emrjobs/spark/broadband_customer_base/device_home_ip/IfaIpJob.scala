package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip

import java.nio.file.FileAlreadyExistsException
import java.time.DayOfWeek
import com.comlinkdata.emrjobs.spark.broadband_customer_base._
import com.comlinkdata.largescale.commons.{SparkJob<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spark<PERSON>ob}
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband.lookup.ConsolidatedCarrier.implicits.CarrierLookupOps
import com.comlinkdata.largescale.schema.tapad.DailyIfaIpAgg
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import org.apache.spark.sql.{SparkSession, Dataset}

import scala.util.Try

object IfaIpJob extends SparkJob(IfaIpJobRunner)

object IfaIpJobRunner extends SparkJobRunner[IfaIpJobConfig] with LazyLogging with ProjectScopeFeatures {

  override def runJob(config: IfaIpJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val customerBaseDate = getCustomerBaseDateOrDefault(config.customerBaseDateOpt)
    val s3IfaIpSummaryPath = Utils.joinPaths(config.ifaIpSummaryBasePath, s"date=$customerBaseDate")
    val s3IfaIpSummaryOpt = Try(spark.read.parquet(s3IfaIpSummaryPath)).toOption
    val IfaWifiIpAggMw05Path = Utils.joinPaths(config.s3MwIfaWifiIpAggPath, "ds=mw05")

    s3IfaIpSummaryOpt match {
      case Some(_) =>
        throw new FileAlreadyExistsException(s"$s3IfaIpSummaryPath already exists")
      case None =>
        val lookBackWindow: LookBackWindow = LookBackWindow.fromCustomerBaseDate(customerBaseDate)
        val carrierLookup = CarrierLookup.read(config.s3CarrierLookupLocation)
        val mw05IfaWifiIpAgg: Dataset[DailyIfaIpAgg] = spark.read
          .parquet(IfaWifiIpAggMw05Path)
          .as[DailyIfaIpAgg]
        val mwIfaWifiIpAgg: Dataset[DailyIfaIpAgg] = mw05IfaWifiIpAgg
          .filter(row => lookBackWindow.apply(row.local_date))
          .filter(row => isWeekendDay(row) || row.night_ind)
          .transform(carrierLookup.limitToBroadbandCarriers[DailyIfaIpAgg]($"local_date"))
          .transform(nineOrMoreDistinctDaysOnSingleModalCarrier[DailyIfaIpAgg]($"local_date"))
        val ifaIpSummary: Dataset[IfaIp] = createIfaIpSummary(mwIfaWifiIpAgg)
        val localOutputPath = Utils.joinPaths(config.localOutputBasePath, s"date=$customerBaseDate")
        ifaIpSummary.write.parquet(localOutputPath)
    }
  }

  /**
    * Extracts the day of the week from the date and checks if it is on a Saturday or Sunday
    *
    * @param row type DailyIfaIpAgg
    * @return
    */
  private def isWeekendDay(row: DailyIfaIpAgg): Boolean = {
    val dayOfWeek: DayOfWeek = row.local_date.toLocalDate.getDayOfWeek
    if (dayOfWeek == DayOfWeek.SUNDAY || dayOfWeek == DayOfWeek.SATURDAY) true else false
  }

  /**
    * Create ifa-ip relationships with min/max dates, distinct days over ifa and ifa-ip
    *
    * @param input daily ifa-ip aggregate from MobileWalla
    * @return
    */
  private def createIfaIpSummary(input: Dataset[DailyIfaIpAgg])(implicit spark: SparkSession): Dataset[IfaIp] = {
    import spark.implicits._

    // to compute distinct_days_over_ifa
    val ifaDateCarrierPartition = Window.partitionBy($"ifa", $"local_date").orderBy($"local_date")
    val ifaCarrierPartition = Window.partitionBy($"ifa")

    // to compute distinct_days_over_ifa_ip
    val ifaIpDatePartition = Window.partitionBy($"ifa", $"ip", $"local_date").orderBy($"local_date")
    val ifaIpPartition = Window.partitionBy($"ifa", $"ip")

    input
      .withColumn("ifa_row_number", row_number over ifaDateCarrierPartition)
      .withColumn("ifa_ip_row_number", row_number over ifaIpDatePartition)
      .withColumn("distinct_days_over_ifa", sum(when($"ifa_row_number" === 1, 1)
        .otherwise(0)) over ifaCarrierPartition)
      .withColumn("distinct_days_over_ifa_ip", sum(when($"ifa_ip_row_number" === 1, 1)
        .otherwise(0)) over ifaIpPartition)
      .select(
        $"ifa", $"ip", $"carrier",
        min($"local_date") over ifaIpPartition as "min_date",
        max($"local_date") over ifaIpPartition as "max_date",
        $"distinct_days_over_ifa" cast IntegerType as "distinct_days_over_ifa",
        $"distinct_days_over_ifa_ip" cast IntegerType as "distinct_days_over_ifa_ip")
      .distinct
      .as[IfaIp]
      .filter(_.distinct_days_over_ifa_ip >= 4)
  }
}
