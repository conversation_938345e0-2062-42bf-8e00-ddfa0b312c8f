package com.comlinkdata.emrjobs.spark.broadband_customer_base.installed_base

import java.time.LocalDate

case class AggregateJobConfig(
  customerBaseDateOpt: Option[LocalDate],
  finalIfaLevelSummaryBasePath: String,
  s3AggCarrierRollups: String,
  deviceMasterTablePath: String,
  consolidatedWirelessCarriers: List[String],
  consolidatedManufacturers: List[String],
  localOutputBasePath: String,
  finalCustomerBaseAggregateBasePath: String)

object AggregateJobConfig {
  val example: AggregateJobConfig = AggregateJobConfig(
    customerBaseDateOpt = Some(LocalDate.parse("2019-07-01")),
    finalIfaLevelSummaryBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/h-final-ifa-level-base-summary/",
    s3AggCarrierRollups = "s3://some-path/",
    deviceMasterTablePath = "s3://d000-comlinkdata-com/prod/private/device-switching/ifa_master_table",
    consolidatedWirelessCarriers = List("AT&T Wireless", "Sprint Wireless", "T-Mobile Wireless", "Verizon Wireless", "CSpire Wireless"),
    consolidatedManufacturers = List("Alcatel", "Apple", "HTC", "Google", "LG", "Samsung", "Motorola", "OnePlus", "ZTE"),
    localOutputBasePath = "hdfs:///final-customer-base-aggregate",
    finalCustomerBaseAggregateBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/i-final-customer-base-aggregate/"
  )
}
