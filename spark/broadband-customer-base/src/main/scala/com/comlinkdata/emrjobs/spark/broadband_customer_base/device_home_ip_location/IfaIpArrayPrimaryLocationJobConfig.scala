package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip_location

import java.time.LocalDate

case class IfaIpArrayPrimaryLocationJobConfig(
  customerBaseDateOpt: Option[LocalDate],
  ifaIpArraySummaryBasePath: String,
  ipPrimaryLocationSummaryBasePath: String,
  ifaIpArrayPrimaryLocationSummaryBasePath: String,
  localOutputBasePath: String)

object IfaIpArrayPrimaryLocationJobConfig {
  val example: IfaIpArrayPrimaryLocationJobConfig = IfaIpArrayPrimaryLocationJobConfig(
    customerBaseDateOpt = Some(LocalDate.parse("2019-07-01")),
    ifaIpArraySummaryBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/b-ifa-ip-array-summary/",
    ipPrimaryLocationSummaryBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/d-ip-primary-location-summary/",
    ifaIpArrayPrimaryLocationSummaryBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/e-ip-array-primary-location-summary/",
    localOutputBasePath = "hdfs:///ifa-ip-array-primary-location-summary"
  )
}
