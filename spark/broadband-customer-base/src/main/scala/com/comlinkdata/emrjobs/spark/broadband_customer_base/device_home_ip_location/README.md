### Device Home IP Location Module

Use data generated in `Device Home IP` and `IP Location` modules

Determining the customer's (device) primary IP or IP array physical home location, by grouping the customer's primary home IP relationships with the WIFI IP address's primary physical location

* Calculate IFA - IP / IP array primary location relationships for both IP refreshes and non-refreshes
* Calculate IFA - IP home location block group relationships from census block groups using Magellan geocoding lookup