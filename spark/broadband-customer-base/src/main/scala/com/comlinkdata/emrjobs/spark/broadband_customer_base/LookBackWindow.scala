package com.comlinkdata.emrjobs.spark.broadband_customer_base

import java.sql.Date
import java.time.LocalDate

import com.comlinkdata.largescale.commons.RichDate._

case class LookBackWindow(
  startDate: LocalDate,
  endDate: LocalDate) {

  def apply(date: Date): Boolean = {
    val localDate = date.toLocalDate
    localDate.isAfterOrEqualTo(startDate) && localDate.isBeforeOrEqualTo(endDate)
  }

}

object LookBackWindow {
  val lookBackPeriodDays: Int = 84

  def fromCustomerBaseDate(customerBaseDate: LocalDate): LookBackWindow =
    new LookBackWindow(customerBaseDate.minusDays(lookBackPeriodDays), customerBaseDate.minusDays(1))
}