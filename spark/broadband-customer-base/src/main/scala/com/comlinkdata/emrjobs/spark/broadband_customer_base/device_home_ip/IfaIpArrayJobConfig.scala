package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip

import java.time.LocalDate

case class IfaIpArrayJobConfig(
  customerBaseDateOpt: Option[LocalDate],
  ifaIpSummaryBasePath: String,
  ifaIpArraySummaryBasePath: String,
  localOutputBasePath: String)

object IfaIpArrayJobConfig {
  val example: IfaIpArrayJobConfig = IfaIpArrayJobConfig(
    customerBaseDateOpt = Some(LocalDate.parse("2019-07-01")),
    ifaIpSummaryBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/a-ifa-ip-summary/",
    ifaIpArraySummaryBasePath = "s3://d000-comlinkdata-com/dev/private/broadband-customer-base/b-ifa-ip-array-summary/",
    localOutputBasePath = "hdfs:///ifa-ip-array-summary/"
  )
}
