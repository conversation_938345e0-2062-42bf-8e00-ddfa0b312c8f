package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip

import java.io.FileNotFoundException
import java.nio.file.FileAlreadyExistsException

import com.comlinkdata.emrjobs.spark.broadband_customer_base._
import com.comlinkdata.largescale.commons.{Spark<PERSON>ob, Spark<PERSON><PERSON><PERSON><PERSON><PERSON>, Utils}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.{round, sum}
import org.apache.spark.sql.types.{FloatType, IntegerType}
import org.apache.spark.sql.{Dataset, SparkSession}

import scala.util.Try


object IfaPrimaryHomeIpJob extends SparkJob(IfaPrimaryHomeIpJobRunner)

object IfaPrimaryHomeIpJobRunner extends SparkJobRunner[IfaPrimaryHomeIpJobConfig]
  with LazyLogging with ProjectScopeFeatures {

  override def runJob(config: IfaPrimaryHomeIpJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val customerBaseDate = getCustomerBaseDateOrDefault(config.customerBaseDateOpt)
    val ifaIpArraySummaryPath = Utils.joinPaths(config.ifaIpArraySummaryBasePath, s"date=$customerBaseDate")
    val ifaPrimaryHomeIpPath = Utils.joinPaths(config.ifaPrimaryHomeIpBasePath, s"date=$customerBaseDate")
    val ifaIpArraySummaryOpt = Try(spark.read.parquet(ifaIpArraySummaryPath).as[IfaIpArray]).toOption
    val ifaPrimaryHomeIpOpt = Try(spark.read.parquet(ifaPrimaryHomeIpPath)).toOption

    val ifaIpArraySummary: Dataset[IfaIpArray] = ifaIpArraySummaryOpt getOrElse {
      throw new FileNotFoundException(s"Missing data from $ifaIpArraySummaryPath")
    }
    ifaPrimaryHomeIpOpt match {
      case Some(_) =>
        throw new FileAlreadyExistsException(s"$ifaPrimaryHomeIpPath already exists")
      case None =>
        val ifaPrimaryHomeIpSummary: Dataset[IfaPrimaryHomeIp] = ifaIpArraySummary
          .transform(calculateActivityOverIpArray)
          .groupByKey(_.ifa)
          .flatMapGroups { case (_, ifaIpActivitiesIterator) =>
            excludeDeviceIfPrimaryHomeIpIsNotSatisfactory(ifaIpActivitiesIterator.toSeq)
          }.flatMap(ifaIpActivities => ifaIpActivities)
        val localOutputPath = Utils.joinPaths(config.localOutputBasePath, s"date=$customerBaseDate")
        ifaPrimaryHomeIpSummary.write.parquet(localOutputPath)
    }
  }

  /**
    * Aggregate ifa-ip array relationships by ip array and with devices' activity
    *
    * @param ifaIpArraySummary ifa-ip array relationships
    * @return
    */
  private def calculateActivityOverIpArray(ifaIpArraySummary: Dataset[IfaIpArray])(implicit spark: SparkSession): Dataset[IfaPrimaryHomeIp] = {
    import spark.implicits._

    val ifaIpRefreshPartition = Window.partitionBy($"ifa", $"ip_array")
    val ifaPartition = Window.partitionBy($"ifa")

    ifaIpArraySummary
      .withColumn("sum_distinct_days_over_ifa_ip", sum($"distinct_days_over_ifa_ip") over ifaIpRefreshPartition cast IntegerType)
      .withColumn("ifa_total_activity", sum($"sum_distinct_days_over_ifa_ip") over ifaPartition cast IntegerType)
      .select(
        $"ifa",
        $"carrier",
        $"min_date",
        $"max_date",
        $"distinct_days_over_ifa",
        $"sum_distinct_days_over_ifa_ip",
        $"ip_array",
        round($"sum_distinct_days_over_ifa_ip" / $"ifa_total_activity", 4) cast FloatType as "activity")
      .distinct
      .as[IfaPrimaryHomeIp]
  }

  /**
    * Search for primary home ip and if there isn't any meeting satisfactory criteria exclude the device
    *
    * @param ifaIpActivities ifa-ip activities (refreshes and non-refreshes)
    * @return
    */
  private def excludeDeviceIfPrimaryHomeIpIsNotSatisfactory(ifaIpActivities: Seq[IfaPrimaryHomeIp]): Option[Seq[IfaPrimaryHomeIp]] = {
    val ifaPrimaryHomeIp = searchIfaPrimaryHomeIp(ifaIpActivities)

    ifaPrimaryHomeIp match {
      case None => None
      case Some(_) => Some(ifaIpActivities)
    }
  }

  /**
    * Search a primary home ip for a device (might not exist)
    *
    * @param ifaIpActivities ifaIpActivities ifa-ip activities (refreshes and non-refreshes)
    * @return
    */
  private def searchIfaPrimaryHomeIp(ifaIpActivities: Seq[IfaPrimaryHomeIp]): Option[IfaPrimaryHomeIp] = {
    if (ifaIpActivities.length == 1) return Some(ifaIpActivities.head)

    val ifaIpActivitiesByActivity = ifaIpActivities.sortBy(_.activity)
    val ifaPrimaryIpActivity = ifaIpActivitiesByActivity.last
    val ifaSecondaryIpActivity = ifaIpActivitiesByActivity(ifaIpActivitiesByActivity.length - 2)

    if (ifaPrimaryIpActivity.activity >= 0.5) Some(ifaPrimaryIpActivity)
    else {
      if (isPrimaryIpActivitySatisfactory(ifaPrimaryIpActivity, ifaSecondaryIpActivity)) Some(ifaPrimaryIpActivity)
      else None
    }
  }

  /**
    * Check is primary ip activity meet certain criteria
    *
    * @param primaryHomeIp   primary home ip activity
    * @param secondaryHomeIp secondary home ip activity
    * @return
    */
  private def isPrimaryIpActivitySatisfactory(primaryHomeIp: IfaPrimaryHomeIp, secondaryHomeIp: IfaPrimaryHomeIp): Boolean = {
    val primaryHomeIpDistinctDays = primaryHomeIp.sum_distinct_days_over_ifa_ip
    val secondaryHomeIpDistinctDays = secondaryHomeIp.sum_distinct_days_over_ifa_ip
    val secondaryToPrimaryIpActivityRatio = getSecondaryToPrimaryIpActivityRatio(primaryHomeIpDistinctDays)

    secondaryToPrimaryIpActivityRatio exists (primaryHomeIpDistinctDays >= secondaryHomeIpDistinctDays * _)
  }

  /**
    * Get secondary to primary ip activity ratio depending on the primary home ip's activity
    * https://comniscient.atlassian.net/wiki/spaces/RD/pages/859340836/Broadband+Customer+Base+v2.0+Home+IP+methodology+Requirements+-+v2
    * (see Table 2B: IFA - Primary Home IP in requirements)
    * @param primaryIpDistinctDays distinct days of primary home ip
    * @return
    */
  private def getSecondaryToPrimaryIpActivityRatio(primaryIpDistinctDays: Int): Option[Double] = {
    if (primaryIpDistinctDays >= 3 && primaryIpDistinctDays <= 10) Some(2)
    else if (primaryIpDistinctDays >= 11 && primaryIpDistinctDays <= 20) Some(1.75)
    else if (primaryIpDistinctDays >= 21 && primaryIpDistinctDays <= 30) Some(1.5)
    else if (primaryIpDistinctDays >= 31) Some(1.25)
    else None
  }
}
