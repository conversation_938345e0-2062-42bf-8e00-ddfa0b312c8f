package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip_location

import java.sql.Date

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.broadband_customer_base.{IfaIpArray, IfaIpArrayPrimaryLocation, IpPrimaryLocation}
import com.comlinkdata.largescale.commons.Utils
import org.apache.spark.sql.Dataset

class IfaIpArrayPrimaryLocationJobSpec extends CldSparkBaseSpec {

  private val calculateIpArrayLocationRanking = PrivateMethod[Dataset[IfaIpArrayPrimaryLocation]]('calculateIpArrayLocationRanking)
  private val determineIfaIpArrayPrimaryLocations = PrivateMethod[Dataset[IfaIpArrayPrimaryLocation]]('determineIfaIpArrayPrimaryLocations)
  private val choosePrimaryIpLocation = PrivateMethod[IfaIpArrayPrimaryLocation]('choosePrimaryIpLocation)

  val ifaBinary: Array[Byte] = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
  val ifaBinary2: Array[Byte] = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b7")

  describe("test calculateIpRefreshLocationRanking") {
    it("should calculate location ranking over lat4/long4 coordinates for IP refreshes") {
      import spark.implicits._

      val ifaIpRefreshSummary: Dataset[IfaIpArray] = spark.createDataset[IfaIpArray](sc.parallelize(Seq(
        IfaIpArray(ifaBinary, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 2, Seq("*******", "*******")),
        IfaIpArray(ifaBinary, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 3, Seq("*******", "*******")),
        IfaIpArray(ifaBinary, "*******", "CARRIER-A", Date.valueOf("2020-01-19"), Date.valueOf("2020-01-19"), 5, 1, Seq("*******", "*******"))
      )))
      val ipPrimaryLocationSummary: Dataset[IpPrimaryLocation] = spark.createDataset[IpPrimaryLocation](sc.parallelize(Seq(
        IpPrimaryLocation("*******", 12.1237F, 5.7653F, 4, is_secondary_location_within_lat_3_long_3 = true),
        IpPrimaryLocation("*******", 13.1237F, 6.7653F, 4, is_secondary_location_within_lat_3_long_3 = true),
        IpPrimaryLocation("*******", 13.1237F, 6.7653F, 4, is_secondary_location_within_lat_3_long_3 = true)
      )))
      val output: Dataset[IfaIpArrayPrimaryLocation] = IfaIpArrayPrimaryLocationJobRunner invokePrivate
                                                       calculateIpArrayLocationRanking(ifaIpRefreshSummary, ipPrimaryLocationSummary, spark)
      val expectedOutput: Dataset[IfaIpArrayPrimaryLocation] = spark.createDataset[IfaIpArrayPrimaryLocation](sc.parallelize(Seq(
        IfaIpArrayPrimaryLocation(ifaBinary, Seq("*******", "*******"), 12.1237F, 5.7653F, 1, 4, 2),
        IfaIpArrayPrimaryLocation(ifaBinary, Seq("*******", "*******"), 13.1237F, 6.7653F, 2, 8, 2)
      )))
      assertDatasetEquals(expectedOutput.toDF, output.toDF)
    }
  }

  describe("test determineIfaIpArrayPrimaryLocations") {
    it("should determine the ip array primary location for each device") {
      import spark.implicits._

      val ipArray = Seq("*******", "*******")
      val ipArray2 = Seq("*******", "*******")
      val dummyLat4 = 123.123F
      val dummyLong4 = 456.456F
      val input: Dataset[IfaIpArrayPrimaryLocation] = spark.createDataset[IfaIpArrayPrimaryLocation](sc.parallelize(Seq(
        IfaIpArrayPrimaryLocation(ifaBinary, ipArray, dummyLat4, dummyLong4, 1, 7, 3),
        IfaIpArrayPrimaryLocation(ifaBinary, ipArray, dummyLat4, dummyLong4, 1, 9, 1),
        IfaIpArrayPrimaryLocation(ifaBinary, ipArray, dummyLat4, dummyLong4, 2, 8, 2),
        IfaIpArrayPrimaryLocation(ifaBinary, ipArray, dummyLat4, dummyLong4, 2, 7, 4),
        IfaIpArrayPrimaryLocation(ifaBinary, ipArray, dummyLat4, dummyLong4, 3, 8, 2),
        IfaIpArrayPrimaryLocation(ifaBinary2, ipArray2, dummyLat4, dummyLong4, 3, 5, 4),
        IfaIpArrayPrimaryLocation(ifaBinary2, ipArray2, dummyLat4, dummyLong4, 2, 6, 3),
        IfaIpArrayPrimaryLocation(ifaBinary2, ipArray2, dummyLat4, dummyLong4, 2, 4, 6),
        IfaIpArrayPrimaryLocation(ifaBinary2, ipArray2, dummyLat4, dummyLong4, 1, 4, 4),
        IfaIpArrayPrimaryLocation(ifaBinary2, ipArray2, dummyLat4, dummyLong4, 4, 5, 2)
      )))
      val output = IfaIpArrayPrimaryLocationJobRunner invokePrivate determineIfaIpArrayPrimaryLocations(input, spark)
      val expectedOutput: Dataset[IfaIpArrayPrimaryLocation] = spark.createDataset[IfaIpArrayPrimaryLocation](sc.parallelize(Seq(
        IfaIpArrayPrimaryLocation(ifaBinary, ipArray, dummyLat4, dummyLong4, 1, 9, 1),
        IfaIpArrayPrimaryLocation(ifaBinary2, ipArray2, dummyLat4, dummyLong4, 2, 6, 3)
      )))
      assertDatasetEquals(expectedOutput.toDF, output.toDF)
    }
  }

  describe("test choosePrimaryIpLocation") {
    it("should pick the primary ip location from a list of ip-array primary locations for a given device") {
      val ipArray = Seq("*******", "*******")
      val dummyLat4 = 123.123F
      val dummyLong4 = 456.456F
      val input: Seq[IfaIpArrayPrimaryLocation] = Seq(
        IfaIpArrayPrimaryLocation(ifaBinary, ipArray, dummyLat4, dummyLong4, 1, 7, 3),
        IfaIpArrayPrimaryLocation(ifaBinary, ipArray, dummyLat4, dummyLong4, 1, 9, 1),
        IfaIpArrayPrimaryLocation(ifaBinary, ipArray, dummyLat4, dummyLong4, 2, 8, 2),
        IfaIpArrayPrimaryLocation(ifaBinary, ipArray, dummyLat4, dummyLong4, 2, 7, 4),
        IfaIpArrayPrimaryLocation(ifaBinary, ipArray, dummyLat4, dummyLong4, 3, 8, 2)
      )
      val output = IfaIpArrayPrimaryLocationJobRunner invokePrivate choosePrimaryIpLocation(input)
      val expectedOutput = IfaIpArrayPrimaryLocation(ifaBinary, ipArray, dummyLat4, dummyLong4, 1, 9, 1)
      output shouldBe expectedOutput
    }
  }
}
