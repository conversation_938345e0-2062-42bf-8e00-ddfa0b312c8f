package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip

import java.sql.Date

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.broadband_customer_base.{IfaIp, IfaIpArray, IfaIpNextIpOverlap}
import com.comlinkdata.largescale.commons.Utils
import org.apache.spark.sql.Dataset

class IfaIpArrayJobSpec extends CldSparkBaseSpec {

  private val calculateOverlap = PrivateMethod[Option[String]]('calculateOverlap)
  private val addNextIpWithOverlap = PrivateMethod[Dataset[IfaIpNextIpOverlap]]('addNextIpWithOverlap)
  private val isValidIpRefresh = PrivateMethod[Boolean]('isValidIpRefresh)
  private val buildIpRefreshArray = PrivateMethod[Seq[String]]('buildIpRefreshArray)
  private val createIfaIpArrayRelationship = PrivateMethod[IfaIpArray]('createIfaIpArrayRelationship)
  private val prepareIfaIpArraySummary = PrivateMethod[Dataset[IfaIpArray]]('prepareIfaIpArraySummary)

  describe("test calculateOverlap") {
    it("should not determine whether or not two consecutive IPs overlap because the next IP min date is null") {
      val currentIpMaxDate = Date.valueOf("2020-01-20")
      val overlapOpt = IfaIpArrayJobRunner invokePrivate calculateOverlap(currentIpMaxDate, null)
      overlapOpt shouldBe empty
    }

    it("should determine that the two IPs overlap") {
      val currentIpMaxDate = Date.valueOf("2020-01-20")
      val nextIpMinDate = Date.valueOf("2020-01-19")
      val overlapOpt = IfaIpArrayJobRunner invokePrivate calculateOverlap(currentIpMaxDate, nextIpMinDate)
      overlapOpt shouldBe defined
      overlapOpt.get shouldBe "O"
    }

    it("should determine that the two IPs do not overlap") {
      val currentIpMaxDate = Date.valueOf("2020-01-20")
      val nextIpMinDate = Date.valueOf("2020-01-21")
      val overlapOpt = IfaIpArrayJobRunner invokePrivate calculateOverlap(currentIpMaxDate, nextIpMinDate)
      overlapOpt shouldBe defined
      overlapOpt.get shouldBe "N"
    }

    it("should determine that the two IPs do not overlap when current IP max date equals next IP min date") {
      val currentIpMaxDate = Date.valueOf("2020-01-20")
      val nextIpMinDate = Date.valueOf("2020-01-20")
      val overlapOpt = IfaIpArrayJobRunner invokePrivate calculateOverlap(currentIpMaxDate, nextIpMinDate)
      overlapOpt shouldBe defined
      overlapOpt.get shouldBe "N"
    }
  }

  describe("test addNextIpWithOverlap") {
    it("should add the next consecutive IP to each current IP for a device") {
      import spark.implicits._

      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val input: Dataset[IfaIp] = spark.createDataset[IfaIp](sc.parallelize(Seq(
        IfaIp(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 2),
        IfaIp(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 3),
        IfaIp(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-19"), Date.valueOf("2020-01-19"), 5, 1)
      )))
      val output = IfaIpArrayJobRunner invokePrivate addNextIpWithOverlap(input, spark)
      val expectedOutput: Dataset[IfaIpNextIpOverlap] = spark.createDataset[IfaIpNextIpOverlap](sc.parallelize(Seq(
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 2,
          Some("*******"), Some(Date.valueOf("2020-01-15")), Some(Date.valueOf("2020-01-18")), Some(3), Some("O")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 3,
          Some("*******"), Some(Date.valueOf("2020-01-19")), Some(Date.valueOf("2020-01-19")), Some(1), Some("N")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-19"), Date.valueOf("2020-01-19"), 5, 1,
          None, None, None, None, None)
      )))
      assertDatasetEquals(expectedOutput.toDF, output.toDF)
    }
  }

  describe("test isValidIpRefresh") {
    it("should check if ip - next ip overlap relationships constitute a valid IP refresh case (valid-1)") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val dummyDate = Date.valueOf("2020-01-01")
      val dummyIp = "*******"
      val input: Seq[IfaIpNextIpOverlap] = Seq(
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 2, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(3), Some("O")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 3, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(1), Some("N")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 1, None, None, None, None, None)
      )
      val boolOutput = IfaIpArrayJobRunner invokePrivate isValidIpRefresh(input)
      boolOutput shouldBe true
    }

    it("should check if ip - next ip overlap relationships constitute a valid IP refresh case (valid-2)") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val dummyDate = Date.valueOf("2020-01-01")
      val dummyIp = "*******"
      val input: Seq[IfaIpNextIpOverlap] = Seq(
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 2, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(3), Some("N")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 3, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(1), Some("O")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 1, None, None, None, None, None)
      )
      val boolOutput = IfaIpArrayJobRunner invokePrivate isValidIpRefresh(input)
      boolOutput shouldBe true
    }

    it("should check if ip - next ip overlap relationships constitute a valid IP refresh case (valid-3)") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val dummyDate = Date.valueOf("2020-01-01")
      val dummyIp = "*******"
      val input: Seq[IfaIpNextIpOverlap] = Seq(
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 2, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(3), Some("N")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 3, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(1), Some("N")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 1, None, None, None, None, None)
      )
      val boolOutput = IfaIpArrayJobRunner invokePrivate isValidIpRefresh(input)
      boolOutput shouldBe true
    }

    it("should check if ip - next ip overlap relationships constitute a valid IP refresh case (invalid-1)") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val dummyDate = Date.valueOf("2020-01-01")
      val dummyIp = "*******"
      val input: Seq[IfaIpNextIpOverlap] = Seq(
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 2, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(3), Some("O")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 3, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(1), Some("O")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 1, None, None, None, None, None)
      )
      val boolOutput = IfaIpArrayJobRunner invokePrivate isValidIpRefresh(input)
      boolOutput shouldBe false
    }

    it("should check if ip - next ip overlap relationships constitute a valid IP refresh case (invalid-2)") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val dummyDate = Date.valueOf("2020-01-01")
      val dummyIp = "*******"
      val input: Seq[IfaIpNextIpOverlap] = Seq(
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 2, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(3), Some("N")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 3, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(1), Some("O")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 3, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(1), Some("N")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 1, None, None, None, None, None)
      )
      val boolOutput = IfaIpArrayJobRunner invokePrivate isValidIpRefresh(input)
      boolOutput shouldBe false
    }

    it("should check if ip - next ip overlap relationships constitute a valid IP refresh case (invalid-3)") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val dummyDate = Date.valueOf("2020-01-01")
      val dummyIp = "*******"
      val input: Seq[IfaIpNextIpOverlap] = Seq(
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 2, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(3), Some("O")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 3, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(1), Some("N")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 3, Some(dummyIp), Some(dummyDate), Some(dummyDate), Some(1), Some("O")),
        IfaIpNextIpOverlap(ifaBin, dummyIp, "CARRIER-A", dummyDate, dummyDate, 5, 1, None, None, None, None, None)
      )
      val boolOutput = IfaIpArrayJobRunner invokePrivate isValidIpRefresh(input)
      boolOutput shouldBe false
    }
  }

  describe("test buildIpRefreshArray") {
    it("should build array of non-overlapping refreshed IPs: example-1") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val dummyDate = Date.valueOf("2020-01-01")
      val input: Seq[IfaIpNextIpOverlap] = Seq(
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", dummyDate, dummyDate, 5, 2, Some("*******"), Some(dummyDate), Some(dummyDate), Some(3), Some("O")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", dummyDate, dummyDate, 5, 3, Some("*******"), Some(dummyDate), Some(dummyDate), Some(1), Some("N")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", dummyDate, dummyDate, 5, 1, None, None, None, None, None)
      )
      val seqOutput = IfaIpArrayJobRunner invokePrivate buildIpRefreshArray(input)
      seqOutput shouldBe Seq("*******", "*******")
    }

    it("should build array of non-overlapping refreshed IPs: example-2") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val dummyDate = Date.valueOf("2020-01-01")
      val input: Seq[IfaIpNextIpOverlap] = Seq(
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", dummyDate, dummyDate, 5, 2, Some("*******"), Some(dummyDate), Some(dummyDate), Some(3), Some("N")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", dummyDate, dummyDate, 5, 3, Some("*******"), Some(dummyDate), Some(dummyDate), Some(1), Some("O")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", dummyDate, dummyDate, 5, 1, None, None, None, None, None)
      )
      val seqOutput = IfaIpArrayJobRunner invokePrivate buildIpRefreshArray(input)
      seqOutput shouldBe Seq("*******", "*******")
    }

    it("should build array of non-overlapping refreshed IPs: example-3") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val dummyDate = Date.valueOf("2020-01-01")
      val input: Seq[IfaIpNextIpOverlap] = Seq(
        IfaIpNextIpOverlap(ifaBin, "0.0.0.0", "CARRIER-A", dummyDate, dummyDate, 5, 0, Some("*******"), Some(dummyDate), Some(dummyDate), Some(2), Some("N")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", dummyDate, dummyDate, 5, 2, Some("*******"), Some(dummyDate), Some(dummyDate), Some(3), Some("N")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", dummyDate, dummyDate, 5, 3, Some("*******"), Some(dummyDate), Some(dummyDate), Some(1), Some("O")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", dummyDate, dummyDate, 5, 1, None, None, None, None, None)
      )
      val seqOutput = IfaIpArrayJobRunner invokePrivate buildIpRefreshArray(input)
      seqOutput shouldBe Seq("0.0.0.0", "*******", "*******")
    }

    it("should build array of non-overlapping refreshed IPs: example-4") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val dummyDate = Date.valueOf("2020-01-01")
      val input: Seq[IfaIpNextIpOverlap] = Seq(
        IfaIpNextIpOverlap(ifaBin, "0.0.0.0", "CARRIER-A", dummyDate, dummyDate, 5, 0, Some("*******"), Some(dummyDate), Some(dummyDate), Some(2), Some("N")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", dummyDate, dummyDate, 5, 2, Some("*******"), Some(dummyDate), Some(dummyDate), Some(3), Some("N")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", dummyDate, dummyDate, 5, 3, Some("*******"), Some(dummyDate), Some(dummyDate), Some(1), Some("N")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", dummyDate, dummyDate, 5, 1, None, None, None, None, None)
      )
      val seqOutput = IfaIpArrayJobRunner invokePrivate buildIpRefreshArray(input)
      seqOutput shouldBe Seq("0.0.0.0", "*******", "*******", "*******")
    }
  }

  describe("test createIfaIpArrayRelationship") {
    it("should create an instance of an ifa - ip array relationship") {
      val ipArray = Seq("*******", "*******")
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val dummyDate = Date.valueOf("2020-01-01")
      val ifaIpNextIpOverlap = IfaIpNextIpOverlap(ifaBin, "*******", "carrier-x", dummyDate, dummyDate, 5, 3, None, None, None, None, None)
      val ifaIpArrayRelationship = IfaIpArrayJobRunner invokePrivate createIfaIpArrayRelationship(ifaIpNextIpOverlap, ipArray)
      val expectedInstance = IfaIpArray(ifaBin, "*******", "carrier-x", dummyDate, dummyDate, 5, 3, ipArray)
      ifaIpArrayRelationship shouldBe expectedInstance
    }
  }

  describe("test prepareIfaIpArraySummary") {
    it("should prepare a dataset of valid ifa - ip array relationships") {
      import spark.implicits._

      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val input: Dataset[IfaIpNextIpOverlap] = spark.createDataset[IfaIpNextIpOverlap](sc.parallelize(Seq(
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 2,
          Some("*******"), Some(Date.valueOf("2020-01-15")), Some(Date.valueOf("2020-01-18")), Some(3), Some("O")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 3,
          Some("*******"), Some(Date.valueOf("2020-01-19")), Some(Date.valueOf("2020-01-19")), Some(1), Some("N")),
        IfaIpNextIpOverlap(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-19"), Date.valueOf("2020-01-19"), 5, 1,
          None, None, None, None, None)
      )))
      val output = IfaIpArrayJobRunner invokePrivate prepareIfaIpArraySummary(input, spark)
      val expectedOutput: Dataset[IfaIpArray] = spark.createDataset[IfaIpArray](sc.parallelize(Seq(
        IfaIpArray(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 2, Seq("*******", "*******")),
        IfaIpArray(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 3, Seq("*******", "*******")),
        IfaIpArray(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-19"), Date.valueOf("2020-01-19"), 5, 1, Seq("*******", "*******"))
      )))
      assertDatasetEquals(expectedOutput.toDF, output.toDF)
    }
  }
}
