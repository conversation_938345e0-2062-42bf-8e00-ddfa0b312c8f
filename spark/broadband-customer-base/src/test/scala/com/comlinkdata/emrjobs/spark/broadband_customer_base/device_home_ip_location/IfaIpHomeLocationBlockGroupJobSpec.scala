package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip_location

import java.sql.Date

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.broadband_customer_base._
import com.comlinkdata.largescale.commons.Utils
import magellan.{Point, Polygon}
import org.apache.spark.sql.Dataset

class IfaIpHomeLocationBlockGroupJobSpec extends CldSparkBaseSpec {

  val ifaBinary: Array[Byte] = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
  private val joinDevicesPrimaryHomeIpToItsPrimaryLocation = PrivateMethod[Dataset[IfaIpHomeLocation]]('joinDevicesPrimaryHomeIpToItsPrimaryLocation)
  private val joinInBlockGroup = PrivateMethod[Dataset[IfaIpHomeLocationBlockGroup]]('joinInBlockGroup)

  describe("test joinDevicesPrimaryHomeIpToItsPrimaryLocation") {
    it("should combine primary home ip and primary location based on ifa and ip_array") {
      import spark.implicits._

      val left: Dataset[IfaPrimaryHomeIp] = spark.createDataset[IfaPrimaryHomeIp](sc.parallelize(Seq(
        IfaPrimaryHomeIp(ifaBinary, "carrier-x", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 3, Seq("*******", "*******"), 0.67F)
      )))
      val right: Dataset[IfaIpArrayPrimaryLocation] = spark.createDataset[IfaIpArrayPrimaryLocation](sc.parallelize(Seq(
        IfaIpArrayPrimaryLocation(ifaBinary, Seq("*******", "*******"), 0.5F, 0.5F, 0, 0, 0)
      )))
      val output = IfaIpHomeLocationBlockGroupJobRunner invokePrivate joinDevicesPrimaryHomeIpToItsPrimaryLocation(left, right, spark)
      val expectedOutput: Dataset[IfaIpHomeLocation] = spark.createDataset[IfaIpHomeLocation](sc.parallelize(Seq(
        IfaIpHomeLocation(ifaBinary, Seq("*******", "*******"), "carrier-x", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 0.5F, 0.5F)
      )))
      assertDatasetEquals(expectedOutput.toDF, output.toDF)
    }
  }

  describe("test joinInBlockGroup") {
    it("should add in geo id / census block group using geoid's polygon and lat4/long4 coordinates") {
      import spark.implicits._
      println(s"spark.sparkContext.version = ${spark.sparkContext.version}")

      val ring = Array(Point(1.0, 1.0), Point(1.0, -1.0), Point(-1.0, -1.0), Point(-1.0, 1.0), Point(1.0, 1.0))
      val polygon = Polygon(Array(0), ring)
      val blockGroups: Dataset[BlockGroup] = spark.createDataset[BlockGroup](sc.parallelize(Seq(BlockGroup("geoid-x", polygon))))
      val ifaIpHomeLocationSummary: Dataset[IfaIpHomeLocation] = spark.createDataset[IfaIpHomeLocation](sc.parallelize(Seq(
        IfaIpHomeLocation(ifaBinary, Seq("*******", "*******"), "carrier-x", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 0.5F, 0.5F)
      )))
      val output = IfaIpHomeLocationBlockGroupJobRunner invokePrivate joinInBlockGroup(ifaIpHomeLocationSummary, blockGroups, spark)
      val expectedOutput: Dataset[IfaIpHomeLocationBlockGroup] = spark.createDataset[IfaIpHomeLocationBlockGroup](sc.parallelize(Seq(
        IfaIpHomeLocationBlockGroup(ifaBinary, Seq("*******", "*******"), "carrier-x", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 0.5F, 0.5F, "geoid-x")
      )))
      assertDatasetEquals(expectedOutput.toDF, output.toDF)
    }
  }

}
