package com.comlinkdata.emrjobs.spark.broadband_customer_base

import java.sql.Date
import java.time.LocalDate

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import org.apache.spark.sql.Dataset

class LookBackWindowSpec extends CldSparkBaseSpec {

  describe("test constructor fromCustomerBaseDate") {
    it("should create an instance of a look-back window object with the right start and end dates given a customer base date") {
      val customerBaseDate = LocalDate.parse("2019-07-01")
      val lookBackWindow = LookBackWindow.fromCustomerBaseDate(customerBaseDate)
      lookBackWindow.startDate shouldBe LocalDate.parse("2019-04-08")
      lookBackWindow.endDate shouldBe LocalDate.parse("2019-06-30")
    }
  }

  describe("test lookup back window apply") {
    it("should a dataframe/dataset using start and end dates from the look-back window object") {
      import spark.implicits._

      val customerBaseDate = LocalDate.parse("2019-07-01")
      val lookBackWindow = LookBackWindow.fromCustomerBaseDate(customerBaseDate)
      val input: Dataset[Date] = spark.createDataset[Date](sc.parallelize(Seq(
        Date.valueOf("2019-04-06"),
        Date.valueOf("2019-04-07"),
        Date.valueOf("2019-04-08"),
        Date.valueOf("2019-04-09"),
        Date.valueOf("2019-05-23"),
        Date.valueOf("2019-06-29"),
        Date.valueOf("2019-06-30"),
        Date.valueOf("2019-07-01"),
        Date.valueOf("2019-07-02")
      )))
      val output: Dataset[Date] = input.filter(row => lookBackWindow.apply(row))
      val expectedOutput: Dataset[Date] = spark.createDataset[Date](sc.parallelize(Seq(
        Date.valueOf("2019-04-08"),
        Date.valueOf("2019-04-09"),
        Date.valueOf("2019-05-23"),
        Date.valueOf("2019-06-29"),
        Date.valueOf("2019-06-30")
      )))
      assertDatasetEquals(expectedOutput, output)
    }
  }

}
