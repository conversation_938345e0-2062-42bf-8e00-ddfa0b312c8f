package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip

import java.sql.Date
import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.broadband_customer_base.IfaIp
import com.comlinkdata.largescale.commons.Utils
import com.comlinkdata.largescale.schema.tapad.DailyIfaIpAgg
import org.apache.spark.sql.Dataset

class IfaIpJobSpec extends CldSparkBaseSpec {

  private val isWeekendDay = PrivateMethod[Boolean]('isWeekendDay)
  private val createIfaIpSummary = PrivateMethod[Dataset[IfaIp]]('createIfaIpSummary)

  describe("test isWeekendDay") {
    it("should check if a Saturday date is a weekend day") {
      val row = DailyIfaIpAgg(Date.valueOf("2020-01-12"), Array(0), "fake-ip", "fake-carrier", true, 0)
      val boolOutput = IfaIpJobRunner invokePrivate isWeekendDay(row)
      boolOutput shouldBe true
    }

    it("should check if a Sunday date is a weekend day") {
      val row = DailyIfaIpAgg(Date.valueOf("2020-01-18"), Array(0), "fake-ip", "fake-carrier", true, 0)
      val boolOutput = IfaIpJobRunner invokePrivate isWeekendDay(row)
      boolOutput shouldBe true
    }

    it("should check if a date that is neither a Saturday nor a Sunday is a non-weekend day") {
      val row = DailyIfaIpAgg(Date.valueOf("2020-01-15"), Array(0), "fake-ip", "fake-carrier", true, 0)
      val boolOutput = IfaIpJobRunner invokePrivate isWeekendDay(row)
      boolOutput shouldBe false
    }
  }


  describe("test createIfaIpSummary") {
    it("should calculate ifa - ip relationships") {
      import spark.implicits._

      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val input: Dataset[DailyIfaIpAgg] = spark.createDataset[DailyIfaIpAgg](sc.parallelize(Seq(
        DailyIfaIpAgg(Date.valueOf("2020-01-15"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-16"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-17"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-18"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-16"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-17"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-18"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-19"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-19"), ifaBin, "*******", "CARRIER-A", true, 0)
      )))
      val output: Dataset[IfaIp] = IfaIpJobRunner invokePrivate createIfaIpSummary(input, spark)
      val expectedOutput: Dataset[IfaIp] = spark.createDataset[IfaIp](sc.parallelize(Seq(
        IfaIp(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 4),
        IfaIp(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-16"), Date.valueOf("2020-01-19"), 5, 4)
      )))
      assertDatasetEquals(expectedOutput.sort($"ifa", $"ip").toDF, output.sort($"ifa", $"ip").toDF)
    }

    it("should return an empty summary") {
      import spark.implicits._

      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val input: Dataset[DailyIfaIpAgg] = spark.createDataset[DailyIfaIpAgg](sc.parallelize(Seq(
        DailyIfaIpAgg(Date.valueOf("2020-01-15"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-16"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-16"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-17"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-18"), ifaBin, "*******", "CARRIER-A", true, 0),
        DailyIfaIpAgg(Date.valueOf("2020-01-19"), ifaBin, "*******", "CARRIER-A", true, 0)
      )))
      val output: Dataset[IfaIp] = IfaIpJobRunner invokePrivate createIfaIpSummary(input, spark)
      output.count() == 0
    }
  }

}
