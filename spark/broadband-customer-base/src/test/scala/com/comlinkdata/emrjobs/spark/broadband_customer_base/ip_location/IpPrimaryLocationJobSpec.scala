package com.comlinkdata.emrjobs.spark.broadband_customer_base.ip_location

import java.sql.Date
import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.broadband_customer_base.{IpRoundedLocation, IpPrimaryLocation, WifiGpsLocation}
import com.comlinkdata.largescale.commons.Utils
import com.comlinkdata.largescale.schema.udp.Ip
import com.comlinkdata.largescale.schema.udp.location.LocationStat.LocationStatsMap
import com.comlinkdata.largescale.schema.udp.location.{Rectangle, LocationStat, Point}
import org.apache.spark.sql.Dataset

class IpPrimaryLocationJobSpec extends CldSparkBaseSpec {

  private val calculateIpRoundedLocations = PrivateMethod[Dataset[IpRoundedLocation]]('calculateIpRoundedLocations)
  private val determineIpPrimaryLocation = PrivateMethod[IpPrimaryLocation]('determineIpPrimaryLocation)

  describe("calculateIpRoundedLocations") {
    it("should calculate lat/long coordinates rounded to 4 digits and two distinct counts for dates and unrounded lat/longs") {
      import spark.implicits._

      val input: Dataset[WifiGpsLocation] = spark.createDataset[WifiGpsLocation](sc.parallelize(Seq(
        WifiGpsLocation("*******", 12.1234567, 5.7654321, Date.valueOf("2020-01-19")),
        WifiGpsLocation("*******", 12.1234567, 5.7654322, Date.valueOf("2020-01-20")),
        WifiGpsLocation("*******", 12.1234567, 5.7654323, Date.valueOf("2020-01-20")),
        WifiGpsLocation("*******", 12.1235677, 5.7653219, Date.valueOf("2020-01-19")),
        WifiGpsLocation("*******", 12.1235678, 5.7653219, Date.valueOf("2020-01-20")),
        WifiGpsLocation("*******", 12.1235679, 5.7653219, Date.valueOf("2020-01-21"))
      )))
      val output = IpPrimaryLocationJobRunner invokePrivate calculateIpRoundedLocations(input, spark)
      val expectedOutput: Dataset[IpRoundedLocation] = spark.createDataset[IpRoundedLocation](sc.parallelize(Seq(
        IpRoundedLocation("*******", 12.1235F, 5.7654F, 2, 3),
        IpRoundedLocation("*******", 12.1236F, 5.7653F, 3, 3))))
      assertDatasetEquals(expectedOutput.toDF, output.toDF)
    }
  }

  describe("test determineIpPrimaryLocation") {
    it("should determine the ip's primary physical location from a list of wifi gps rounded locations") {
      val myFancyIp = "*******"
      val input = Seq(
        IpRoundedLocation(myFancyIp, 12.1235F, 5.7654F, 2, 3),
        IpRoundedLocation(myFancyIp, 12.1236F, 5.7653F, 3, 3),
        IpRoundedLocation(myFancyIp, 12.1237F, 5.7653F, 4, 2),
        IpRoundedLocation(myFancyIp, 12.1238F, 5.7654F, 4, 1)
      )
      val output = IpPrimaryLocationJobRunner invokePrivate determineIpPrimaryLocation(myFancyIp, input)
      val expectedOutput = IpPrimaryLocation(myFancyIp, 12.1237F, 5.7653F, 4, is_secondary_location_within_lat_3_long_3 = true)
      output shouldBe expectedOutput
    }

    // TODO: should add a unit test to check `is_secondary_location_within_lat_3_long_3` can be false
  }

  describe("break out location stats from location_stats map column") {
    import spark.implicits._
    it("should break out centroid lat and longs from location_stats column") {
      val ip0 = Utils.ipStringToBinary("***********")
      val ip1 = Utils.ipStringToBinary("***********")
      val ip2 = Utils.ipStringToBinary("***********")

      val map0 = Map("GPS" -> LocationStat(Rectangle(12.1238F, 5.7654F, 12.1238F, 5.7654F), Point(10.1240F, 5.7660F)))
      val map1 = Map("IP" -> LocationStat(Rectangle(12.1238F, 5.7654F, 12.1238F, 5.7654F), Point(11.1240F, 6.7660F)))
      val map2 = Map("GPS" -> LocationStat(Rectangle(12.1238F, 5.7654F, 12.1238F, 5.7654F), Point(12.1240F, 7.7660F)))

      val input: Dataset[(Date, Ip, LocationStatsMap)] = List(
        (Date.valueOf("2020-07-01"), ip0, map0),
        (Date.valueOf("2020-07-02"), ip1, map1),
        (Date.valueOf("2020-07-03"), ip2, map2)
      ).toDS

      def flatMapLocationStats(df: Dataset[(Date, Ip, LocationStatsMap)]) = {
        df.flatMap {
          case (date, ip, location_stats) =>
            location_stats flatMap {
              case obs if obs._1 == "GPS" =>
                Some(WifiGpsLocation(Utils.ipBinaryToString(ip), obs._2.centroid.lat, obs._2.centroid.lng, date))
              case _ => None
            }
        }
      }

      val actual = flatMapLocationStats(input)
      val expected = List(
        WifiGpsLocation("***********", 10.1240F, 5.7660F, Date.valueOf("2020-07-01")),
        WifiGpsLocation("***********", 12.1240F, 7.7660F, Date.valueOf("2020-07-03"))).toDS

      assertDatasetEquals(actual, expected)


    }
  }
}
