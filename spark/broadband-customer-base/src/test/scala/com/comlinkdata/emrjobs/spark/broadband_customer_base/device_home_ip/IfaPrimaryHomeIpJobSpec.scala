package com.comlinkdata.emrjobs.spark.broadband_customer_base.device_home_ip

import java.sql.Date

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.broadband_customer_base.{IfaIpArray, IfaPrimaryHomeIp}
import com.comlinkdata.largescale.commons.Utils
import org.apache.spark.sql.Dataset

class IfaPrimaryHomeIpJobSpec extends CldSparkBaseSpec {

  private val calculateActivityOverIpArray = PrivateMethod[Dataset[IfaPrimaryHomeIp]]('calculateActivityOverIpArray)
  private val searchIfaPrimaryHomeIp = PrivateMethod[Option[IfaPrimaryHomeIp]]('searchIfaPrimaryHomeIp)
  private val isPrimaryIpActivitySatisfactory = PrivateMethod[Boolean]('isPrimaryIpActivitySatisfactory)
  private val getSecondaryToPrimaryIpActivityRatio = PrivateMethod[Option[Double]]('getSecondaryToPrimaryIpActivityRatio)
  private val excludeDeviceIfPrimaryHomeIpIsNotSatisfactory = PrivateMethod[Option[Seq[IfaPrimaryHomeIp]]]('excludeDeviceIfPrimaryHomeIpIsNotSatisfactory)

  describe("test calculateActivityOverIpArray") {
    it("should calculate device activity over ip array") {
      import spark.implicits._

      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val ipArray = Seq("*******", "*******")
      val input: Dataset[IfaIpArray] = spark.createDataset[IfaIpArray](sc.parallelize(Seq(
        IfaIpArray(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 2, ipArray),
        IfaIpArray(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 3, ipArray),
        IfaIpArray(ifaBin, "*******", "CARRIER-A", Date.valueOf("2020-01-19"), Date.valueOf("2020-01-19"), 5, 1, ipArray)
      )))
      val output = IfaPrimaryHomeIpJobRunner invokePrivate calculateActivityOverIpArray(input, spark)
      val expectedOutput: Dataset[IfaPrimaryHomeIp] = spark.createDataset[IfaPrimaryHomeIp](sc.parallelize(Seq(
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 6, ipArray, 0.3333F),
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 6, ipArray, 0.3333F),
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-19"), Date.valueOf("2020-01-19"), 5, 6, ipArray, 0.3333F)
      )))
      assertDatasetEquals(expectedOutput.toDF, output.toDF)
    }
  }

  describe("test excludeDeviceIfPrimaryHomeIpIsNotSatisfactory") {
    it("should exclude device's ip array relationships if its primary home ip does not meet satisfactory criteria: example-1") {

      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val ipArray = Seq("*******", "*******")
      val input = Seq(
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 6, ipArray, 0.3333F),
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 6, ipArray, 0.3333F),
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-19"), Date.valueOf("2020-01-19"), 5, 6, ipArray, 0.3333F)
      )
      val output = IfaPrimaryHomeIpJobRunner invokePrivate excludeDeviceIfPrimaryHomeIpIsNotSatisfactory(input)
      output shouldBe empty
    }
  }

  describe("test searchIfaPrimaryHomeIp") {
    it("should attempt to find the device's primary home ip: example-1") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val ipArray = Seq("*******", "*******")
      val input = Seq(
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 6, ipArray, 0.3333F),
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 6, ipArray, 0.3333F),
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-19"), Date.valueOf("2020-01-19"), 5, 6, ipArray, 0.3333F)
      )
      val output = IfaPrimaryHomeIpJobRunner invokePrivate searchIfaPrimaryHomeIp(input)
      output shouldBe None
    }

    it("should attempt to find the device's primary home ip: example-2") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val ipArray = Seq("*******", "*******")
      val input = Seq(
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 6, ipArray, 0.55F),
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 6, ipArray, 0.21F),
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-19"), Date.valueOf("2020-01-19"), 5, 6, ipArray, 0.24F)
      )
      val output = IfaPrimaryHomeIpJobRunner invokePrivate searchIfaPrimaryHomeIp(input)
      output shouldBe defined
      output.get shouldBe IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 6, ipArray, 0.55F)
    }

    it("should attempt to find the device's primary home ip: example-3") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val ipArray = Seq("*******", "*******")
      val input = Seq(
        IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 6, ipArray, 0.21F)
      )
      val output = IfaPrimaryHomeIpJobRunner invokePrivate searchIfaPrimaryHomeIp(input)
      output shouldBe defined
      output.get shouldBe IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-18"), 5, 6, ipArray, 0.21F)
    }
  }

  describe("test isPrimaryIpActivitySatisfactory") {
    it("should check if the primary home ip meet satisfactory criteria with the secondary home ip: example-1") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val ipArray = Seq("*******", "*******")
      val primaryIpActivity = IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 2, ipArray, 0.45F) // primaryHomeIpDistinctDays = 2
      val secondaryIpActivity = IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-17"), Date.valueOf("2020-01-18"), 5, 1, ipArray, 0.19F)
      val boolOutput = IfaPrimaryHomeIpJobRunner invokePrivate isPrimaryIpActivitySatisfactory(primaryIpActivity, secondaryIpActivity)
      boolOutput shouldBe false
    }

    it("should check if the primary home ip meet satisfactory criteria with the secondary home ip: example-2") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val ipArray = Seq("*******", "*******")
      val primaryIpActivity = IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 6, ipArray, 0.45F) // primaryHomeIpDistinctDays = 6
      val secondaryIpActivity = IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-17"), Date.valueOf("2020-01-18"), 5, 3, ipArray, 0.19F) // secondaryHomeIpDistinctDays = 3
      val boolOutput = IfaPrimaryHomeIpJobRunner invokePrivate isPrimaryIpActivitySatisfactory(primaryIpActivity, secondaryIpActivity)
      boolOutput shouldBe true
    }

    it("should check if the primary home ip meet satisfactory criteria with the secondary home ip: example-3") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val ipArray = Seq("*******", "*******")
      val primaryIpActivity = IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 6, ipArray, 0.45F) // primaryHomeIpDistinctDays = 6
      val secondaryIpActivity = IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-17"), Date.valueOf("2020-01-18"), 5, 4, ipArray, 0.19F) // secondaryHomeIpDistinctDays = 3
      val boolOutput = IfaPrimaryHomeIpJobRunner invokePrivate isPrimaryIpActivitySatisfactory(primaryIpActivity, secondaryIpActivity)
      boolOutput shouldBe false
    }

    it("should check if the primary home ip meet satisfactory criteria with the secondary home ip: example-4") {
      val ifaBin = Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6")
      val ipArray = Seq("*******", "*******")
      val primaryIpActivity = IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-15"), Date.valueOf("2020-01-16"), 5, 69, ipArray, 0.43F) // primaryHomeIpDistinctDays = 6
      val secondaryIpActivity = IfaPrimaryHomeIp(ifaBin, "CARRIER-A", Date.valueOf("2020-01-17"), Date.valueOf("2020-01-18"), 5, 55, ipArray, 0.34F) // secondaryHomeIpDistinctDays = 3
      val boolOutput = IfaPrimaryHomeIpJobRunner invokePrivate isPrimaryIpActivitySatisfactory(primaryIpActivity, secondaryIpActivity)
      boolOutput shouldBe true
    }
  }

  describe("test getSecondaryToPrimaryIpActivityRatio") {
    it("should get the secondary to primary home ip activity ratio depending on each IP's distinct days: example-1") {
      val primaryIpDistinctDays = 4
      val ratio = IfaPrimaryHomeIpJobRunner invokePrivate getSecondaryToPrimaryIpActivityRatio(primaryIpDistinctDays)
      ratio shouldBe defined
      ratio.get shouldBe 2
    }

    it("should get the secondary to primary home ip activity ratio depending on each IP's distinct days: example-2") {
      val primaryIpDistinctDays = 13
      val ratio = IfaPrimaryHomeIpJobRunner invokePrivate getSecondaryToPrimaryIpActivityRatio(primaryIpDistinctDays)
      ratio shouldBe defined
      ratio.get shouldBe 1.75
    }

    it("should get the secondary to primary home ip activity ratio depending on each IP's distinct days: example-3") {
      val primaryIpDistinctDays = 27
      val ratio = IfaPrimaryHomeIpJobRunner invokePrivate getSecondaryToPrimaryIpActivityRatio(primaryIpDistinctDays)
      ratio shouldBe defined
      ratio.get shouldBe 1.5
    }

    it("should get the secondary to primary home ip activity ratio depending on each IP's distinct days: example-4") {
      val primaryIpDistinctDays = 33
      val ratio = IfaPrimaryHomeIpJobRunner invokePrivate getSecondaryToPrimaryIpActivityRatio(primaryIpDistinctDays)
      ratio shouldBe defined
      ratio.get shouldBe 1.25
    }

    it("should get the secondary to primary home ip activity ratio depending on each IP's distinct days: example-5") {
      val primaryIpDistinctDays = 2
      val ratio = IfaPrimaryHomeIpJobRunner invokePrivate getSecondaryToPrimaryIpActivityRatio(primaryIpDistinctDays)
      ratio shouldBe None
    }
  }

}
