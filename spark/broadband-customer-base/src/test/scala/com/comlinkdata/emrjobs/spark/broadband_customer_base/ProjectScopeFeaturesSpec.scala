package com.comlinkdata.emrjobs.spark.broadband_customer_base

import java.sql.Date
import java.time.LocalDate

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.commons.Utils
import org.apache.spark.sql.{Dataset, SparkSession}

class ProjectScopeFeaturesSpec extends CldSparkBaseSpec with ProjectScopeFeatures {

  describe("test nineOrMoreDistinctDaysOnSingleModalCarrier") {
    it("should exclude devices that don't meet the nemesis rule's criteria") {
      import spark.implicits._

      def dateIn2020(month: Int, dayOfMonth: Int) = Date.valueOf(LocalDate.of(2020, month, dayOfMonth))

      val myDummyIfas = Seq(
        Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b5"),
        Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b6"),
        Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b7"),
        Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b8"),
        Utils.uuidHexString2Binary("0105e4dd-6b13-4570-a71e-cdb5759ec7b9")
      )
      val input: Dataset[IfaHomeCarrier] = spark.createDataset(sc.parallelize(Seq(
        // 9+ distinct dates on only one carrier
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 10)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 11)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 11)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 12)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 12)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 13)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 14)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 15)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 16)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 17)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 18)),

        // 9+ distinct dates on only one carrier plus another carrier with less than 9 dates
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 10)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 11)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 12)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 13)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 14)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 15)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 16)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 17)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 18)),
        IfaHomeCarrier(myDummyIfas(1), "XAN", dateIn2020(1, 19)),

        // more than one carrier have 9+ distinct dates
        IfaHomeCarrier(myDummyIfas(2), "KAK", dateIn2020(1, 10)),
        IfaHomeCarrier(myDummyIfas(2), "KAK", dateIn2020(1, 11)),
        IfaHomeCarrier(myDummyIfas(2), "KAK", dateIn2020(1, 12)),
        IfaHomeCarrier(myDummyIfas(2), "KAK", dateIn2020(1, 13)),
        IfaHomeCarrier(myDummyIfas(2), "KAK", dateIn2020(1, 14)),
        IfaHomeCarrier(myDummyIfas(2), "KAK", dateIn2020(1, 15)),
        IfaHomeCarrier(myDummyIfas(2), "KAK", dateIn2020(1, 16)),
        IfaHomeCarrier(myDummyIfas(2), "KAK", dateIn2020(1, 17)),
        IfaHomeCarrier(myDummyIfas(2), "KAK", dateIn2020(1, 18)),
        IfaHomeCarrier(myDummyIfas(2), "KAK", dateIn2020(1, 19)),

        IfaHomeCarrier(myDummyIfas(2), "DED", dateIn2020(1, 10)),
        IfaHomeCarrier(myDummyIfas(2), "DED", dateIn2020(1, 11)),
        IfaHomeCarrier(myDummyIfas(2), "DED", dateIn2020(1, 12)),
        IfaHomeCarrier(myDummyIfas(2), "DED", dateIn2020(1, 13)),
        IfaHomeCarrier(myDummyIfas(2), "DED", dateIn2020(1, 14)),
        IfaHomeCarrier(myDummyIfas(2), "DED", dateIn2020(1, 15)),
        IfaHomeCarrier(myDummyIfas(2), "DED", dateIn2020(1, 16)),
        IfaHomeCarrier(myDummyIfas(2), "DED", dateIn2020(1, 17)),
        IfaHomeCarrier(myDummyIfas(2), "DED", dateIn2020(1, 18)),

        // less than 9 dates on one carrier
        IfaHomeCarrier(myDummyIfas(3), "TUS", dateIn2020(1, 10)),
        IfaHomeCarrier(myDummyIfas(3), "TUS", dateIn2020(1, 11)),
        IfaHomeCarrier(myDummyIfas(3), "TUS", dateIn2020(1, 12)),
        IfaHomeCarrier(myDummyIfas(3), "TUS", dateIn2020(1, 13)),
        IfaHomeCarrier(myDummyIfas(3), "TUS", dateIn2020(1, 14)),
        IfaHomeCarrier(myDummyIfas(3), "TUS", dateIn2020(1, 15)),
        IfaHomeCarrier(myDummyIfas(3), "TUS", dateIn2020(1, 16)),
        IfaHomeCarrier(myDummyIfas(3), "TUS", dateIn2020(1, 17)),

        // less than 9 dates on multiples carriers
        IfaHomeCarrier(myDummyIfas(4), "WIF", dateIn2020(1, 10)),
        IfaHomeCarrier(myDummyIfas(4), "WIF", dateIn2020(1, 11)),
        IfaHomeCarrier(myDummyIfas(4), "WIF", dateIn2020(1, 12)),
        IfaHomeCarrier(myDummyIfas(4), "PLU", dateIn2020(1, 13)),
        IfaHomeCarrier(myDummyIfas(4), "PLU", dateIn2020(1, 14)),
        IfaHomeCarrier(myDummyIfas(4), "PLU", dateIn2020(1, 15))
      )))
      val output = input.transform(nineOrMoreDistinctDaysOnSingleModalCarrier[IfaHomeCarrier]($"date"))
      val expectedOutput: Dataset[IfaHomeCarrier] = spark.createDataset(sc.parallelize(Seq(
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 10)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 11)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 11)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 12)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 12)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 13)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 14)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 15)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 16)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 17)),
        IfaHomeCarrier(myDummyIfas.head, "VAN", dateIn2020(1, 18)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 10)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 11)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 12)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 13)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 14)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 15)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 16)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 17)),
        IfaHomeCarrier(myDummyIfas(1), "PAN", dateIn2020(1, 18))
      ))).sort($"ifa", $"carrier", $"date")
      assertDatasetEquals(expectedOutput.toDF, output.sort($"ifa", $"carrier", $"date").toDF)
    }
  }

}
