package com.comlinkdata.emrjobs.spark.udp.tier0

import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.commons.parsing.JsonUtils
import com.comlinkdata.largescale.metrics.CldSparkMetricsRegistry
import com.comlinkdata.largescale.schema.udp.tier0.{Mw02, Reveal, UdpRaw, UdpRaw2}
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.comlinkdata.largescale.udp._
import com.typesafe.scalalogging.LazyLogging
import java.lang.{Boolean => JBoolean, Float => JFloat, Long => JLong}
import java.net.URI
import java.sql.{Date, Timestamp}
import java.time.format.DateTimeFormatter
import java.time.{Instant, LocalDate, ZoneId}
import net.iakovlev.timeshape.TimeZoneEngine
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}
import org.apache.spark.util.LongAccumulator
import scala.collection.compat.immutable.LazyList
import scala.util.{Failure, Success, Try}

/**
  * example usage of adapter in combojob, ability to transform original dataset
  * into uniform UdpRaw for tier 1 processing
  *
  * val df = spark.read.parquet....
  * val result = Dataset[UdpRawAll] = df.transform(adapter.rawToDataset)
  */
trait UdpComboJobDatasourceAdapter {
  def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2]

  /**
    * Gets the latest complete partition for the datasource.
    *
    * This ignores lookahead/lookback/lagDays, unlike latestInputDate.
    */
  def latestPartition2(implicit spark: SparkSession): LocalDate

  def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw]

  def loadDatePartitions(dateRange: LocalDateRange)(implicit spark: SparkSession): DataFrame

  val datasource: ComlinkdataDatasource
}


object UdpComboJobDatasourceAdapter extends SparkConstants {

  import RichDate._

  private val stringToDate = udf[Date, String](arg => Date.valueOf(arg.substring(0, 10)))
  private val stringToTimestamp = udf[Timestamp, String](Timestamp.valueOf)
  private val timestampToDate = udf[Date, Timestamp](_.getUtcDate)
  private val longToTimestamp = udf[Timestamp, Long](new Timestamp(_))
  private val uuidToBinary = udf[Ifa, String](arg => Try(Utils.uuidString2Binary(arg)).toOption.orNull)
  private val ipToBinary = udf[Ip, String](ipString => Try(Utils.ipStringToBinary(ipString)).toOption.orNull)
  private val toLocationType = udf[String, Option[Int]] {
    case Some(1) => "GPS"
    case Some(2) => "IP"
    case _ => "User-provided"
  }
  private val toDeviceType = udf[String, Option[Int]] {
    case Some(1) => "Mobile/Tablet - General"
    case Some(2) => "Personal Computer"
    case Some(3) => "Connected TV"
    case Some(4) => "Phone"
    case Some(5) => "Tablet"
    case Some(6) => "Connected Device"
    case Some(7) => "Set Top Box"
    case Some(8) => "OOH Device"
    case _ => "UNKNOWN"
  }
  private val toConnectionType = udf[String, Option[Int]] {
    case Some(1) => "ETHERNET"
    case Some(2) => "WIFI"
    case Some(3) | Some(4) | Some(5) | Some(6) | Some(7) => "CELLULAR"
    case _ => "UNKNOWN"
  }
  private val nullString = typedLit[String](null)
  private val nullBoolean = typedLit[JBoolean](null)
  private val nullFloat = typedLit[JFloat](null)
  private val nullLong = typedLit[JLong](null)
  private val nullTS = typedLit[Timestamp](null)
  private val ambiguousModels = Seq(
    "iphone",
    "ipad",
    "iphone 6s - 13 pro max",
    "iphone xr - 15 pro max",
    "iphone 8 - x",
    "iphone 6s - 7 plus",
  )

  private def map2obj[T: JsonUtils.Jsonable](map: Map[String, Any]): T = JsonUtils.fromJson[T](JsonUtils.toJson(map))

  def forDatasource(ds: ComlinkdataDatasource, mapConfig: Map[String, Any]): UdpComboJobDatasourceAdapter = ds match {
    case ComlinkdataDatasource.kochava =>
      val config = map2obj[KochavaAdapter.Config](mapConfig)
      KochavaAdapter(config)
    case ComlinkdataDatasource.reveal =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      RevealAdapter(config)
    case ComlinkdataDatasource.mw =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      MobileWallaAdapter(config)
    case ComlinkdataDatasource.mw05 =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      MobileWalla05Adapter(config)
    case ComlinkdataDatasource.mw07 =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      MobileWalla07Adapter(config)
    case ComlinkdataDatasource.`mw05_CA` =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      MobileWallaCanadaAdapter(config)
    case ComlinkdataDatasource.mw03 =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      MobileWalla03Adapter(config)
    case ComlinkdataDatasource.quadrant =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      QuadrantAdapter(config)
    case ComlinkdataDatasource.mw03s =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      MobileWalla03sAdapter(config)
    case ComlinkdataDatasource.onemata =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      OnemataAdapter(config)
    case ComlinkdataDatasource.reveal02 =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      Reveal02Adapter(config)
    case ComlinkdataDatasource.gamoshi =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      GamoshiAdapter(config)
    case ComlinkdataDatasource.gamoshi03 =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      Gamoshi03Adapter(config)
    case ComlinkdataDatasource.eskimi =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      EskimiAdapter(config)
    case ComlinkdataDatasource.eskimi_CA =>
      val config = map2obj[SingleSourceConfig](mapConfig)
      EskimiCanadaAdapter(config)
  }

  case class SingleSourceConfig(datasourceSource: URI)

  trait SinglySourcedDatasourceAdapter extends UdpComboJobDatasourceAdapter with LazyLogging {
    def config: SingleSourceConfig

    /** We only use lag days b/c we want to process any _complete_ date partitions that are available. */
    protected def ts2(implicit spark: SparkSession): TimeSeriesLocation = TimeSeriesLocation
      .ofDatePartitions(config.datasourceSource)
      .withLagDays(datasource.lagDays)
      .build

    override def latestPartition2(implicit spark: SparkSession): LocalDate = ts2.latestDate

    /**
      * Load the date partitions specified.  Will honor lag days and throw an exception if partial date partitions are requested
      *
      * @param dateRange the date range of raw partitions to load
      */
    override def loadDatePartitions(dateRange: LocalDateRange)(implicit spark: SparkSession): DataFrame = {
      val parts = ts2.validInputPartitionsOrThrow(dateRange)
      logger.info(s"Reading partitions: $parts.")
      spark.read
        .option(ReadOpts.basePath, config.datasourceSource.toString)
        .parquet(parts: _*)
    }
  }

  case class QuadrantAdapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {
    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.quadrant
    private val badRecordAccumulatorName = "quadrant-adapter-bad-record"

    private def adjustIp(cnt: LongAccumulator)(ipStr: String): Option[Ip] = {
      Try(Utils.ipStringToBinary(ipStr)) match {
        case Success(ip) => Some(ip)
        case Failure(_: java.net.UnknownHostException) =>
          cnt.add(1)
          None
        case Failure(e) => throw e
      }
    }

    /** convert to binary, but discard any bad ip strings */
    private def adjustIps(accum: LongAccumulator)(df: DataFrame): DataFrame = {
      val ipUdf = udf[Option[Ip], String](adjustIp(accum)(_))
      df
        .withColumn("ip", ipUdf(col("ip_address")))
        .filter(col("ip").isNotNull)
        .drop("ip_address")
    }

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] = {
      import spark.implicits._
      val accum = metrics.newAccumulator(badRecordAccumulatorName, startDate, "each")
      df
        .withColumnRenamed("date", "pdate")
        .withColumn("datetime", (col("timestamp") / 1000).cast(TimestampType))
        .withColumn("date", timestampToDate($"datetime"))
        .transform(Utils.emptyStringToNull("carrier_name", "device_manufacturer", "device_model", "app_id"))
        .transform(adjustIps(accum))
        .withColumn("ifa", uuidToBinary(col("device_id")))
        .withColumnRenamed("user_agent", "useragent")
        .withColumn("carrier", lit(null).cast(StringType))
        .withColumnRenamed("carrier_name", "newcarrier")
        // latitude, longitude - no change
        .withColumn("locationtype", lit("GPS"))
        .withColumn("suspiciouslocation", lit(null).cast(BooleanType))
        .withColumn("devicetype", lit(null).cast(StringType))
        .withColumnRenamed("device_manufacturer", "make")
        .withColumnRenamed("device_model", "model")
        .withColumn("connectiontype", lit(null).cast(StringType))
        .withColumnRenamed("app_id", "appid")
        .withColumn("appname", lit(null).cast(StringType))
        .withColumnRenamed("device_os", "os")
        .withColumn("accuracy", lit(null).cast(FloatType))
        .withColumn("gps_speed", lit(null).cast(FloatType))
        .withColumn("place_name", lit(null).cast(StringType))
        .withColumn("place_id", lit(null).cast(LongType))
        .withColumn("category", lit(null).cast(StringType))
        .withColumn("country_iso3", lit(null).cast(StringType))
        .withColumn("localdatetime", Conversions.filteredLocalTimeFromDatetimeAndTz("datetime"))
        .filter('ifa.isNotNull)
        .select(UdpRaw2.cols: _*)
        .as[UdpRaw2]
    }

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      val accum = metrics.newAccumulator(badRecordAccumulatorName, startDate, "each")
      import spark.implicits._
      df
        .withColumn("datetime", (col("timestamp") / 1000).cast(TimestampType))
        .withColumn("date", timestampToDate($"datetime"))
        .filter($"date" >= Date.valueOf(startDate) and $"date" <= Date.valueOf(endDate))
        .transform(Utils.emptyStringToNull("carrier_name", "device_manufacturer", "device_model", "app_id"))
        .transform(adjustIps(accum))
        .withColumn("ifa", uuidToBinary(col("device_id")))
        .withColumnRenamed("user_agent", "useragent")
        .withColumnRenamed("carrier_name", "newcarrier")
        .withColumn("carrier", lit(null).cast(StringType))
        // latitude, longitude - no change
        .withColumn("locationtype", lit("GPS"))
        .withColumn("suspiciouslocation", lit(null).cast(BooleanType))
        .withColumn("devicetype", lit(null).cast(StringType))
        .withColumnRenamed("device_manufacturer", "make")
        .withColumnRenamed("device_model", "model")
        .withColumn("connectiontype", lit(null).cast(StringType))
        .withColumnRenamed("app_id", "appid")
        .withColumn("appname", lit(null).cast(StringType))
        .withColumnRenamed("device_os", "os")
        .withColumn("accuracy", lit(null).cast(FloatType))
        .withColumn("gps_speed", lit(null).cast(FloatType))
        .withColumn("place_name", lit(null).cast(StringType))
        .withColumn("place_id", lit(null).cast(LongType))
        .withColumn("category", lit(null).cast(StringType))
        .withColumn("country_iso3", lit(null).cast(StringType))
        .withColumn("localdatetime", Conversions.filteredLocalTimeFromDatetimeAndTz("datetime"))
        .filter('ifa.isNotNull)
        .select(UdpRaw.cols: _*)
        .as[UdpRaw]
    }
  }

  case class RevealAdapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {
    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.reveal

    //FIXME rename || reorganize
    private def adaptSchema2(df: DataFrame)(implicit spark: SparkSession): Dataset[UdpRaw2] = {
      import spark.implicits._
      df
        .withColumnRenamed("event_type", "locationtype")
        .withColumnRenamed("connection_type", "connectiontype")
        .withColumnRenamed("utc_timestamp", "datetime")
        .withColumnRenamed("app_id", "appid")
        .withColumn("connectiontype", upper($"connectiontype"))
        .withColumn("useragent", lit(null).cast(StringType))
        .withColumn("newcarrier", lit(null).cast(StringType))
        .withColumn("suspiciouslocation", lit(null).cast(BooleanType))
        .withColumn("devicetype", lit(null).cast(StringType))
        .withColumn("appname", lit(null).cast(StringType))
        .select(UdpRaw2.cols: _*)
        .as[UdpRaw2]
    }

    //FIXME rename || reorganize
    private def adaptSchema(df: DataFrame)(implicit spark: SparkSession): Dataset[UdpRaw] = {
      import spark.implicits._
      df
        .withColumnRenamed("event_type", "locationtype")
        .withColumnRenamed("connection_type", "connectiontype")
        .withColumnRenamed("utc_timestamp", "datetime")
        .withColumnRenamed("app_id", "appid")
        .withColumn("connectiontype", upper($"connectiontype"))
        .withColumn("useragent", lit(null).cast(StringType))
        .withColumnRenamed("carrier", "newcarrier")
        .withColumn("carrier", lit(null).cast(StringType))
        .withColumn("suspiciouslocation", lit(null).cast(BooleanType))
        .withColumn("devicetype", lit(null).cast(StringType))
        .withColumn("appname", lit(null).cast(StringType))
        .as[UdpRaw]
    }

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] = {
      import spark.implicits._
      df.withColumn("pdate", 'date)
        .transform(Reveal.cleanRevealInputForUdpRawAll)
        .filter($"ifa".isNotNull)
        .transform(adaptSchema2)
    }

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      import spark.implicits._
      df
        .transform(Reveal.cleanRevealInputForUdpRawAll)
        .filter($"ifa".isNotNull)
        .transform(adaptSchema)
      // WARNING do not use a filter over a flatmap, bug in spark compiler causes "Only code-generated evaluation is supported"
      //.flatMap(Some(_).filter(x => dayFilter(startDate, endDate)(x.datetime))) // TODO do we need this? lookback/ahead always 0, reveal has utc aligned date partitions
      // do not delete this line so we know what _doesn't_ work.
      //.filter(row => RichDate.timestampIsWithinBounds(startDate, endDate)(row.datetime))
      //.transform(UdpUtils.correctDateColumnFromTimestamp(_)) // TODO do we need this? date column should be utc aligned in reveal
    }
  }

  object MobileWalla03Adapter {
    def rawToUdpRawDataset2(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] = {
      import spark.implicits._
      df
        .withColumn("ip", when($"ip" =!= lit("NIL") and $"ip" =!= "", $"ip").otherwise(lit(null)))
        .withColumn("ip", when($"ip" =!= lit("NIL"), $"ip").otherwise(lit(null)))
        .transform(MobileWallaAdapter.performTransformation2)
    }

    def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      import spark.implicits._
      val dateBoundCheck: UserDefinedFunction = RichDate.timestampIsWithinBoundsUdf(startDate, endDate)
      df
        .filter(dateBoundCheck($"datetime")) // first throw away any records not relevant to the processing window
        .withColumn("ip", when($"ip" =!= lit("NIL") and $"ip" =!= "", $"ip").otherwise(lit(null)))
        .withColumn("ip", when($"ip" =!= lit("NIL"), $"ip").otherwise(lit(null)))
        .transform(MobileWallaAdapter.performTransformation)
    }
  }

  case class MobileWalla03Adapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {
    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.mw03

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] =
      df.transform(MobileWalla03Adapter.rawToUdpRawDataset2)

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] =
      MobileWalla03Adapter.rawToUdpRawDataset(startDate, endDate, df)
  }

  case class MobileWalla03sAdapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {
    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.mw03s

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] =
      df.transform(MobileWalla03Adapter.rawToUdpRawDataset2)

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] =
      MobileWalla03Adapter.rawToUdpRawDataset(startDate, endDate, df)
  }

  case class MobileWalla05Adapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {
    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.mw05

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] = {
      import spark.implicits._
      df
        .withColumn("ip", when($"ip" =!= "", $"ip").otherwise(lit(null)))
        .transform(MobileWallaAdapter.performTransformation2)
    }

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      import spark.implicits._
      val dateBoundCheck: UserDefinedFunction = RichDate.timestampIsWithinBoundsUdf(startDate, endDate)
      df
        .filter(dateBoundCheck($"datetime")) // first throw away any records not relevant to the processing window
        .withColumn("ip", when($"ip" =!= "", $"ip").otherwise(lit(null)))
        .transform(MobileWallaAdapter.performTransformation)
    }
  }

  case class MobileWalla07Adapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {
    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.mw07

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] = {
      import spark.implicits._
      df
        .withColumn("ip", when($"ip" =!= "", $"ip").otherwise(lit(null)))
        .transform(MobileWallaAdapter.performTransformation2)
    }

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      import spark.implicits._
      val dateBoundCheck: UserDefinedFunction = RichDate.timestampIsWithinBoundsUdf(startDate, endDate)
      df
        .filter(dateBoundCheck($"datetime")) // first throw away any records not relevant to the processing window
        .withColumn("ip", when($"ip" =!= "", $"ip").otherwise(lit(null)))
        .transform(MobileWallaAdapter.performTransformation)
    }
  }

  case class MobileWallaCanadaAdapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {
    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.mw05_CA

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] = {
      import spark.implicits._
      df
        .withColumn("ip", when($"ip" =!= "", $"ip").otherwise(lit(null)))
        .transform(MobileWallaAdapter.performTransformation2)
    }

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      import spark.implicits._
      val dateBoundCheck: UserDefinedFunction = RichDate.timestampIsWithinBoundsUdf(startDate, endDate)
      df
        .filter(dateBoundCheck($"datetime")) // first throw away any records not relevant to the processing window
        .withColumn("ip", when($"ip" =!= "", $"ip").otherwise(lit(null)))
        .transform(MobileWallaAdapter.performTransformation)
    }
  }

  object MobileWallaAdapter {
    private def adaptSchema2(df: DataFrame)(implicit spark: SparkSession): Dataset[UdpRaw2] = {
      import spark.implicits._
      df
        .withColumn("os", lit(null).cast(StringType))
        .withColumn("accuracy", lit(null).cast(FloatType))
        .withColumn("gps_speed", lit(null).cast(FloatType))
        .withColumn("place_name", lit(null).cast(StringType))
        .withColumn("place_id", lit(null).cast(IntegerType))
        .withColumn("category", lit(null).cast(StringType))
        .withColumn("country_iso3", lit(null).cast(StringType))
        .select(UdpRaw2.cols: _*)
        .as[UdpRaw2]
    }


    private def adaptSchema(df: DataFrame)(implicit spark: SparkSession): Dataset[UdpRaw] = {
      import spark.implicits._
      df
        .withColumn("os", lit(null).cast(StringType))
        .withColumn("accuracy", lit(null).cast(FloatType))
        .withColumn("gps_speed", lit(null).cast(FloatType))
        .withColumn("place_name", lit(null).cast(StringType))
        .withColumn("place_id", lit(null).cast(IntegerType))
        .withColumn("category", lit(null).cast(StringType))
        .withColumn("country_iso3", lit(null).cast(StringType))
        .withColumn("datetime", to_timestamp($"datetime"))
        .as[UdpRaw]
    }

    def performTransformation2(df: DataFrame)(implicit spark: SparkSession): Dataset[UdpRaw2] = {
      import spark.implicits._
      df
        .withColumn("pdate", col("date"))
        .transform(Mw02.checkAppNameColumn)
        .transform(UdpRaw.adjustMw02RecordsToUdpRaw)
        .filter($"ifa".isNotNull)
        .transform(adaptSchema2)
        .transform(UdpUtils.correctDateColumnFromTimestamp()(_))
    }

    def performTransformation(df: DataFrame)(implicit spark: SparkSession): Dataset[UdpRaw] = {
      import spark.implicits._
      df.transform(Mw02.checkAppNameColumn)
        .transform(UdpRaw.adjustMw02RecordsToUdpRaw)
        .filter($"ifa".isNotNull)
        .transform(UdpUtils.correctDateColumnFromTimestampDf()(_))
        .transform(adaptSchema)
    }
  }

  case class MobileWallaAdapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {

    import MobileWallaAdapter._

    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.mw

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] =
      df.transform(performTransformation2)

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      import spark.implicits._
      val dateBoundCheck: UserDefinedFunction = RichDate.timestampIsWithinBoundsUdf(startDate, endDate)
      df.filter(dateBoundCheck($"datetime")) // first throw away any records not relevant to the processing window
        .transform(performTransformation)
    }
  }

  object OnemataAdapter {
    def transform(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
      import spark.implicits._
      df.filter('country === "US")
        .select(
          'date.as("pdate"),
          stringToDate('utc_timestamp).as("date"),
          uuidToBinary('mobile_ad_id).as("ifa"),
          coalesce(ipToBinary('ipv_4), ipToBinary('ipv_6)).as("ip"),
          'user_agent.as("useragent"),
          nullString.as("carrier"),
          nullString.as("newcarrier"),
          'latitude.cast(FloatType),
          'longitude.cast(FloatType),
          lit("GPS").as("locationtype"),
          nullBoolean.as("suspiciouslocation"),
          nullString.as("devicetype"),
          nullString.as("make"),
          nullString.as("model"),
          nullString.as("connectiontype"),
          stringToTimestamp('utc_timestamp).as("datetime"),
          'publisher.as("appid"),
          nullString.as("appname"),
          'mobile_ad_id_type.as("os"),
          'horizontal_accuracy.cast(FloatType).as("accuracy"),
          'speed.cast(FloatType).as("gps_speed"),
          nullString.as("place_name"),
          nullLong.as("place_id"),
          nullString.as("category"),
          lit("USA").as("country_iso3"),
          Conversions.filteredLocalTimeFromDatetimeAndTz("utc_timestamp").as("localdatetime")
        ).filter('ip.isNotNull && 'ifa.isNotNull)
    }
  }

  case class OnemataAdapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {

    import OnemataAdapter._

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] = {
      import spark.implicits._
      transform(df).as[UdpRaw2]
    }

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      import spark.implicits._
      transform(df.filter(RichDate.timestampIsWithinBoundsUdf(startDate, endDate)('utc_timestamp))).as[UdpRaw]
    }

    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.onemata
  }

  object Reveal02Adapter {
    def transform(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
      import spark.implicits._
      df.select(
        $"date".as("pdate"),
        $"date",
        uuidToBinary($"maid").as("ifa"),
        ipToBinary($"ip_address").as("ip"),
        nullString.as("useragent"),
        when($"carrier" =!= "", $"carrier").as("carrier"),
        nullString.as("newcarrier"),
        $"lat".as("latitude"),
        $"lon".as("longitude"),
        $"event_type".as("locationtype"),
        nullBoolean.as("suspiciouslocation"),
        nullString.as("devicetype"),
        when($"make" =!= "", $"make").as("make"),
        when($"model" =!= "", $"model").as("model"),
        upper($"connection_type").as("connectiontype"),
        $"utc_timestamp".as("datetime"),
        $"app_id".as("appid"),
        nullString.as("appname"),
        $"os",
        $"accuracy",
        $"gps_speed",
        $"place_name",
        $"place_id".cast(LongType).as("place_id"),
        $"category",
        $"country_iso3",
        Conversions.filteredLocalTimeFromDatetimeAndTz("utc_timestamp", "lat", "lon").as("localdatetime")
      ).filter($"ifa".isNotNull)
    }
  }

  case class Reveal02Adapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {

    import Reveal02Adapter._

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] = {
      import spark.implicits._
      transform(df).as[UdpRaw2]
    }

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      import spark.implicits._
      transform(df.filter(RichDate.timestampIsWithinBoundsUdf(startDate, endDate)($"utc_timestamp")))
        .drop("pdate")
        .drop("newcarrier")
        .withColumnRenamed("carrier", "newcarrier")
        .withColumn("carrier", lit(null).cast(StringType))
        .as[UdpRaw]
    }

    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.reveal02
  }

  object Gamoshi03Adapter {
    private val instantToTS = udf[Timestamp, String] { ts =>
      Timestamp.valueOf(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.of("UTC")).format(Instant.parse(ts)))
    }

    def transform(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
      import spark.implicits._
      df.withColumn("latitude", $"lat".cast(FloatType))
        .withColumn("longitude", $"lon".cast(FloatType))
        .filter($"country_2" === "US"
          && $"latitude".isNotNull
          && $"longitude".isNotNull
          && !($"latitude" === 0.0 && $"longitude" === 0.0) // Filter only invalid (0,0) - should be null but gamoshi sends 0.0
          && $"device_id" =!= "00000000-0000-0000-0000-000000000000")
        .withColumn("timestamp", instantToTS($"timestamp"))
        .select(
          concat($"year", lit("-"), $"month", lit("-"), $"day") cast DateType as "pdate",
          timestampToDate($"timestamp").as("date"),
          uuidToBinary($"device_id").as("ifa"),
          coalesce(ipToBinary($"ipv6"), ipToBinary($"ip")).as("ip"),
          $"ua".as("useragent"),
          nullString.as("carrier"),
          $"carrier".as("newcarrier"),
          $"latitude",
          $"longitude",
          toLocationType($"geo_source_type").as("locationtype"),
          nullBoolean.as("suspiciouslocation"),
          toDeviceType($"device_type").as("devicetype"),
          $"make",
          when(lower($"model").isin(ambiguousModels: _*) && $"hwv".isNotNull, $"hwv")
            otherwise $"model" as "model",
          toConnectionType($"connectionType").as("connectiontype"),
          $"timestamp".as("datetime"),
          $"app_id".as("appid"),
          $"app_name".as("appname"),
          $"os",
          nullFloat.as("accuracy"),
          nullFloat.as("gps_speed"),
          nullString.as("place_name"),
          nullLong.as("place_id"),
          nullString.as("category"),
          lit("USA").as("country_iso3"),
          Conversions.filteredLocalTimeFromDatetimeAndTz("timestamp").as("localdatetime")
        ).filter($"ifa".isNotNull)
    }
  }

  object GamoshiAdapter {
    private val instantToTS = udf[Timestamp, String] { ts =>
      Timestamp.valueOf(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.of("UTC")).format(Instant.parse(ts)))
    }

    def transform(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
      import spark.implicits._
      df.withColumn("latitude", $"lat.member1".cast(FloatType))
        .withColumn("longitude", $"lon.member1".cast(FloatType))
        .filter($"country_2" === "US"
          && $"latitude".isNotNull
          && $"longitude".isNotNull
          && $"device_id" =!= "00000000-0000-0000-0000-000000000000")
        .withColumn("timestamp", instantToTS($"timestamp"))
        .select(
          $"date".as("pdate"),
          timestampToDate($"timestamp").as("date"),
          uuidToBinary($"device_id").as("ifa"),
          coalesce(ipToBinary($"ipv6"), ipToBinary($"ip")).as("ip"),
          $"ua".as("useragent"),
          nullString.as("carrier"),
          $"carrier".as("newcarrier"),
          $"latitude",
          $"longitude",
          toLocationType($"geo_source_type.member0").as("locationtype"),
          nullBoolean.as("suspiciouslocation"),
          toDeviceType($"device_type.member0").as("devicetype"),
          $"make",
          when(lower($"model").isin(ambiguousModels: _*) && $"hwv".isNotNull, $"hwv")
            otherwise $"model" as "model",
          toConnectionType($"connectionType.member0").as("connectiontype"),
          $"timestamp".as("datetime"),
          $"app_id".as("appid"),
          $"app_name".as("appname"),
          $"os",
          nullFloat.as("accuracy"),
          nullFloat.as("gps_speed"),
          nullString.as("place_name"),
          nullLong.as("place_id"),
          nullString.as("category"),
          lit("USA").as("country_iso3"),
          Conversions.filteredLocalTimeFromDatetimeAndTz("timestamp").as("localdatetime")
        ).filter($"ifa".isNotNull)
    }
  }

  case class GamoshiAdapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {
    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.gamoshi

    import GamoshiAdapter._

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] = {
      import spark.implicits._
      transform(df).as[UdpRaw2]
    }

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      import spark.implicits._
      transform(df.filter(RichDate.timestampIsWithinBoundsUdf(startDate, endDate)($"utc_timestamp")))
        .drop("pdate")
        .as[UdpRaw]
    }

    override def latestPartition2(implicit spark: SparkSession): LocalDate = {
      val start = super.latestPartition2
      val fs = FileSystem.get(ts2.source, spark.sparkContext.hadoopConfiguration)
      LazyList.from(0).map(start.minusDays(_)).takeWhile(_.isAfter(datasource.firstDate)).find { d =>
        fs.listStatus(new Path(s"${ts2.source}/date=$d")).map(_.getPath.getName).contains("_SUCCESS")
      }.getOrElse(datasource.firstDate)
    }
  }

  case class Gamoshi03Adapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {
    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.gamoshi03

    import Gamoshi03Adapter._

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] = {
      import spark.implicits._
      transform(df).as[UdpRaw2]
    }

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      import spark.implicits._
      transform(df.filter(RichDate.timestampIsWithinBoundsUdf(startDate, endDate)($"utc_timestamp")))
        .drop("pdate")
        .as[UdpRaw]
    }
    override def ts2(implicit spark: SparkSession): TimeSeriesLocation = TimeSeriesLocation
      .ofYmdDatePartitions(config.datasourceSource)
      .withLagDays(datasource.lagDays)
      .build

    override def latestPartition2(implicit spark: SparkSession): LocalDate = {
      val start = super.latestPartition2
      val fs = FileSystem.get(ts2.source, spark.sparkContext.hadoopConfiguration)

      LazyList.from(0).map(start.minusDays(_)).takeWhile(_.isAfter(datasource.firstDate)).find { d =>
        val year = d.getYear
        val month = "%02d".format(d.getMonthValue)
        val day = "%02d".format(d.getDayOfMonth)

        val partitionPath = new Path(s"${ts2.source}/year=$year/month=$month/day=$day")

        try {
          fs.exists(partitionPath) &&
            fs.listStatus(partitionPath).exists(_.getPath.getName == "_SUCCESS")
        } catch {
          case _: Exception => false
        }
      }.getOrElse(datasource.firstDate)
    }
  }

  object EskimiAdapter {
    def transform(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
      import spark.implicits._
      df.filter(!($"dnt" <=> 1))
        .withColumn("timestamp", longToTimestamp($"timestamp"))
        .select(
          concat($"year", lit("-"), $"month", lit("-"), $"day") cast DateType as "pdate",
          timestampToDate($"timestamp") as "date",
          uuidToBinary($"idfa") as "ifa",
          ipToBinary($"ip") as "ip",
          $"userAgent" as "useragent",
          $"carrier",
          $"primaryMccmnc" as "newcarrier",
          $"lat" cast FloatType as "latitude",
          $"lon" cast FloatType as "longitude",
          toLocationType($"locationType") as "locationtype",
          nullBoolean as "suspiciouslocation",
          toDeviceType($"deviceTypeId") as "devicetype",
          $"deviceMake" as "make",
          when(lower($"deviceModel").isin(ambiguousModels: _*) && $"hwv".isNotNull, $"hwv")
            otherwise $"deviceModel" as "model",
          toConnectionType($"connectionType") as "connectiontype",
          $"timestamp" as "datetime",
          $"exchangeId" cast StringType as "appid",
          $"app" as "appname",
          $"deviceOs" as "os",
          $"accuracy",
          nullFloat as "gps_speed",
          nullString as "place_name",
          nullLong as "place_id",
          nullString as "category",
          lit("USA") as "country_iso3",
          Conversions.filteredLocalTimeFromDatetimeAndTz("timestamp", "lat", "lon") as "localdatetime"
        ).filter($"ifa".isNotNull && $"latitude".isNotNull && $"longitude".isNotNull)
    }
  }

  object EskimiCanadaAdapter {
    def transform(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
      import spark.implicits._
      df.filter(!($"dnt" <=> 1))
        .withColumn("timestamp", longToTimestamp($"timestamp"))
        .select(
          concat($"year", lit("-"), $"month", lit("-"), $"day") cast DateType as "pdate",
          timestampToDate($"timestamp") as "date",
          uuidToBinary($"idfa") as "ifa",
          ipToBinary($"ip") as "ip",
          $"userAgent" as "useragent",
          $"carrier",
          $"primaryMccmnc" as "newcarrier",
          $"lat" cast FloatType as "latitude",
          $"lon" cast FloatType as "longitude",
          toLocationType($"locationType") as "locationtype",
          nullBoolean as "suspiciouslocation",
          toDeviceType($"deviceTypeId") as "devicetype",
          $"deviceMake" as "make",
          when(lower($"deviceModel").isin(ambiguousModels: _*) && $"hwv".isNotNull, $"hwv")
            otherwise $"deviceModel" as "model",
          toConnectionType($"connectionType") as "connectiontype",
          $"timestamp" as "datetime",
          $"exchangeId" cast StringType as "appid",
          $"app" as "appname",
          $"deviceOs" as "os",
          $"accuracy",
          nullFloat as "gps_speed",
          nullString as "place_name",
          nullLong as "place_id",
          nullString as "category",
          lit("CAN") as "country_iso3",
          nullTS as "localdatetime"
        ).filter($"ifa".isNotNull && $"latitude".isNotNull && $"longitude".isNotNull)
    }
  }

  case class EskimiAdapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {
    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.eskimi

    import EskimiAdapter._

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] = {
      import spark.implicits._
      transform(df).as[UdpRaw2]
    }

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      import spark.implicits._
      transform(df.filter(RichDate.timestampIsWithinBoundsUdf(startDate, endDate)($"utc_timestamp")))
        .drop("pdate")
        .as[UdpRaw]
    }

    override def ts2(implicit spark: SparkSession): TimeSeriesLocation = TimeSeriesLocation
      .ofYmdDatePartitions(config.datasourceSource)
      .withLagDays(datasource.lagDays)
      .build

    override def latestPartition2(implicit spark: SparkSession): LocalDate = {
      val start = super.latestPartition2
      val fs = FileSystem.get(ts2.source, spark.sparkContext.hadoopConfiguration)
      LazyList.from(0).map(start.minusDays(_)).takeWhile(_.isAfter(datasource.firstDate)).find { d =>
        fs.listStatus(new Path(ts2.partition(d))).map(_.getPath.getName).contains("_SUCCESS")
      }.getOrElse(datasource.firstDate)
    }
  }

  case class EskimiCanadaAdapter(config: SingleSourceConfig) extends SinglySourcedDatasourceAdapter {
    override val datasource: ComlinkdataDatasource = ComlinkdataDatasource.eskimi_CA

    import EskimiCanadaAdapter._

    private def getLocalTime(
      utcTime: Timestamp,
      latOption: Option[Float],
      lngOption: Option[Float],
      tzEngine: TimeZoneEngine): Option[Timestamp] =
      (latOption, lngOption) match {
        case (Some(lat), Some(lng)) =>
          val tz = tzEngine.query(lat, lng)
          (if (tz.isPresent) Some(tz.get()) else None)
            .map(utcTime.toLocalDateTime.atZone(ZoneId.of("UTC")).withZoneSameInstant(_).toLocalDateTime)
            .map(Timestamp.valueOf)
        case _ => None
      }

    override def rawToUdpRawDataset2(startDate: LocalDate)(df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw2] = {
      import spark.implicits._
      transform(df).as[UdpRaw2]
        .mapPartitions { rows =>
          val tzEngine = TimeZoneEngine.initialize(41.0, -142.0, 84.0, -52.0, true)
          rows.map(row => row.copy(localdatetime = getLocalTime(row.datetime, row.latitude, row.longitude, tzEngine)))
        }
        .withColumn("accuracy", $"accuracy" cast IntegerType)
        .as[UdpRaw2]
    }

    override def rawToUdpRawDataset(startDate: LocalDate, endDate: LocalDate, df: DataFrame)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Dataset[UdpRaw] = {
      import spark.implicits._
      transform(df.filter(RichDate.timestampIsWithinBoundsUdf(startDate, endDate)($"utc_timestamp")))
        .drop("pdate")
        .as[UdpRaw]
        .mapPartitions { rows =>
          val tzEngine = TimeZoneEngine.initialize(41.0, -142.0, 84.0, -52.0, true)
          rows.map(row => row.copy(localdatetime = getLocalTime(row.datetime, row.latitude, row.longitude, tzEngine)))
        }
        .withColumn("accuracy", $"accuracy" cast IntegerType)
        .as[UdpRaw]
    }

    override def ts2(implicit spark: SparkSession): TimeSeriesLocation = TimeSeriesLocation
      .ofYmdDatePartitions(config.datasourceSource)
      .withLagDays(datasource.lagDays)
      .build

    override def latestPartition2(implicit spark: SparkSession): LocalDate = {
      val start = super.latestPartition2
      val fs = FileSystem.get(ts2.source, spark.sparkContext.hadoopConfiguration)
      LazyList.from(0).map(start.minusDays(_)).takeWhile(_.isAfter(datasource.firstDate)).find { d =>
        fs.listStatus(new Path(ts2.partition(d))).map(_.getPath.getName).contains("_SUCCESS")
      }.getOrElse(datasource.firstDate)
    }
  }
}
