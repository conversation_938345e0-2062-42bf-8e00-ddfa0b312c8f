package com.comlinkdata.emrjobs.spark.udp.iplist

import com.comlinkdata.emrjobs.spark.udp.iplist.IpListCommon._
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils.implicits._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, SaveMode, SparkSession}

case class MvnoIpListConfig(
  ifaIpAggPath: String,
  vzRangeLookup: String,
  tmoRangeLookup: String,
  attRangeLookup: String,
  ipListPath: String,
  quarter: Option[String]
)

object MvnoIpListJob extends SparkJob(MvnoIpListRunner)

object MvnoIpListRunner extends SparkJobRunner[MvnoIpListConfig] with SparkConstants with LazyLogging {
  def findMvnoCategories(
    vzIpList: Seq[String],
    vzIpLength: Int,
    attIpList: Seq[String],
    attIpLength: Int,
    tmoIpList: Seq[String],
    tmoIpLength: Int)(input: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    input
      .filter(length('ip) === 8)
      .filter('connection_type === "Cellular")
      .filter((lower('carrier).like("%verizon%") && 'ip.substr(1, vzIpLength).isin(vzIpList: _*))
        || (lower('carrier).like("%at&t%") && 'ip.substr(1, attIpLength).isin(attIpList: _*))
        || ((lower('carrier).like("%t-mobile%") || lower('carrier).like("%tmobile%") || lower('carrier).like("%sprint%")) && 'ip.substr(1, tmoIpLength).isin(tmoIpList: _*)))
  }

  override def runJob(config: MvnoIpListConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    require(CldFileUtils.newBuilder.forUri(config.ifaIpAggPath).build.isDirectory(config.ifaIpAggPath))
    require(CldFileUtils.newBuilder.forUri(config.vzRangeLookup).build.exists(config.vzRangeLookup))
    require(CldFileUtils.newBuilder.forUri(config.tmoRangeLookup).build.exists(config.tmoRangeLookup))
    require(CldFileUtils.newBuilder.forUri(config.attRangeLookup).build.exists(config.attRangeLookup))
    val (lastIpList, destPath, dr) = loadLastIpList(config.ipListPath, config.quarter)
    val ifaIpAgg = loadValidInRange(config.ifaIpAggPath, dr, TimeSeriesLocation.ofYmdDatePartitions(_))
      .withColumn("ip", lower(hex('ip)))
    val (vzIpList, vzIpLength) = loadIpRangeLookup(spark.read.text(config.vzRangeLookup).as[String], "Verizon")
    val (tmoIpList, tmoIpLength) = loadIpRangeLookup(spark.read.text(config.tmoRangeLookup).as[String], "TMO Boost")
    val (attIpList, attIpLength) = loadIpRangeLookup(spark.read.text(config.attRangeLookup).as[String], "AT&T")
    val mvno = ifaIpAgg
      .transform(findMvnoCategories(vzIpList, vzIpLength, attIpList, attIpLength, tmoIpList, tmoIpLength))
      .transform(retainNewCategories(lastIpList))
      .select(unhex('ip).as("ip"), 'carrier)
    val hdfsTempPath = "hdfs:/ip-lists/"
    mvno.write.mode(SaveMode.Overwrite).parquet(hdfsTempPath)
    spark.read.parquet(hdfsTempPath)
      .distinct
      .repartition(1)
      .write.mode(SaveMode.ErrorIfExists)
      .parquet(destPath)
  }
}
