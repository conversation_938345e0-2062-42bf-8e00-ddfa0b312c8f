package com.comlinkdata.emrjobs.spark.udp.locationset

import com.comlinkdata.emrjobs.spark.udp.ipset.UdpIpSetRunner.loadMaxmind
import com.comlinkdata.emrjobs.spark.udp.locationset.LocationSetUDFs._
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.udp.tier1.{UdpDailyIfaNoYmd, UdpLocationSetObservation}
import com.comlinkdata.largescale.schema.udp.tier2.HourHistory.TTL_DAYS
import com.comlinkdata.largescale.schema.udp.tier2.{CarrierSeen, UdpLocationSet}
import com.comlinkdata.largescale.schema.udp.{MccMnc, IfaVec}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SparkSession, Dataset, SaveMode}
import org.apache.spark.sql.functions._

import java.util.Objects
import java.net.URI
import java.sql.Date
import java.time.temporal.ChronoUnit
import java.time.LocalDate

/**
  * Location Set run config parameters
  *
  * @param datasources List of datasources to run
  * @param ifaObsPath Path to root of Ifa Observations
  * @param maxmindPath Path to root of maxmind data (need to get carriers)
  * @param outputPath Path to output Location Set
  * @param startDate What day to start computing LocationSet from. If the day before this doesn't exist this day will be output. If it does, adds will be added on to the LocationSet from the most recent.
  * @param maxNumDaysToRun Optional, max days to run
  * @param outputRepartition Optional, repartition before output
  */
case class UdpLocationSetConfig(
  datasources: List[ComlinkdataDatasource],
  ifaObsPath : URI,
  maxmindPath : URI,
  outputPath: URI,
  startDate: LocalDate,
  maxNumDaysToRun: Option[Int],
  outputRepartition: Option[Int] = Some(250)
)

/**
  * Internal use (not written). Struct of lat & long int, ifa, and date
  * @param lat_4f latitude int
  * @param lng_4f longitude int
  * @param ifa ifa
  * @param date date ifa was seen
  */
case class IfaDate(
  lat_4f: Int,
  lng_4f: Int,
  ifa: IfaVec,
  date: Date
)

object UdpLocationSetJob
  extends SparkJob(UdpLocationSetRunner)

object UdpLocationSetRunner
  extends SparkJobRunner[UdpLocationSetConfig]
    with SparkConstants
    with LazyLogging {

  private val dayHourSum = new DayHourSumUDAF
  private val carrierUDAF = new CarrierUDAF
  val NUM_LOCS_IN_CARRIER_MAP = 2
  private val NUM_GPS_DECIMALS = 4
  private val PRECISION = Math.pow(10, NUM_GPS_DECIMALS)
  private val MAX_IFAs = 50


  /**
    * Entry point for spark job
    * @param config run paramters
    * @param spark implict spark session
    */
  override def runJob(config: UdpLocationSetConfig)(implicit spark: SparkSession): Unit = {

    for(ds <- config.datasources) {
      val dsStartDate = if (ds.firstDate.isAfter(config.startDate)) {
        logger.info(s"Datasource ($ds) hasn't started yet. Skipping to: ${ds.firstDate}")
        ds.firstDate
      } else {
        config.startDate
      }

      val tslLocSet = TimeSeriesLocation
        .ofYmdDatePartitions(config.outputPath)
        .withPartition(ds)
        .build

      val tslIfaObs = TimeSeriesLocation
        .ofYmdDatePartitions(config.ifaObsPath)
        .withPartition(ds)
        .build

      val daysToRun =
        getDaysToRun(tslLocSet, tslIfaObs, dsStartDate, config.maxNumDaysToRun)
      logger.info(s"Running location set for $daysToRun")

      //Always read in the read previous days (will be empty of previous day doesn't exist)
      var prevDayData = readPreviousLocSet(tslLocSet, daysToRun.head.minusDays(1))
      for (day <- daysToRun.toList) {
        logger.info(s"Starting: $day")

        //Location set for this day
        val newDaysLocSet = processOneIfaObs(tslIfaObs, config.maxmindPath, day, ds).cache()

        //Combine yesterday and today
        val combinedNewDay = combine(newDaysLocSet, prevDayData, day).cache()

        //Ouput
        val fullOutPath = tslLocSet.partition(day)
        val outDF = if (config.outputRepartition.isDefined) {
          combinedNewDay.repartition(config.outputRepartition.get)
        } else {
          combinedNewDay
        }

        UriDFTableRW.fromStr(fullOutPath).writeWithHistory(outDF.toDF(), SaveMode.ErrorIfExists, configs = Seq(config))

        prevDayData.unpersist()
        newDaysLocSet.unpersist()

        prevDayData = combinedNewDay
      }
    }
  }

  /**
    * Get days to run
    * @param tslLocSet Location of previously computed LocationSet days
    * @param tslIfaObs Location of source Ifa Observations
    * @param beginningOfTime Earliest date to start from
    * @return Sequence of days to be computed & output
    */
  private def getDaysToRun(
    tslLocSet: TimeSeriesLocation,
    tslIfaObs: TimeSeriesLocation,
    beginningOfTime: LocalDate,
    optNumDaysToRun: Option[Int]
  ): Seq[LocalDate] = {

    val latestIfaObsDate = tslIfaObs.latestDate

    //startDate = latest date previously computed or beginningOfTime
    val startDate =
      if (tslLocSet.exists)
        tslLocSet.latestDate.plusDays(1)
      else
        beginningOfTime

    val maxPossibleDays = ChronoUnit.DAYS.between(startDate, latestIfaObsDate) + 1
    val numDaysToRun = optNumDaysToRun match {
      case None => maxPossibleDays
      case Some(daysToRunOpt) => Math.min(maxPossibleDays, daysToRunOpt)
    }

    for (i <- 0 until numDaysToRun.toInt) yield startDate.plusDays(i)
  }

  /**
    * Read one day of existing LocationSet or return empty Dataset if LocationSet doesn't exist for given date
    * @param tsl TimeSeriesLocation for existing LocationSet
    * @param date date to read
    * @param spark implicit spark session
    * @return UdpLocationSet Dataset
    */
  private def readPreviousLocSet(tsl: TimeSeriesLocation, date: LocalDate)(implicit spark: SparkSession) = {
    import spark.implicits._

    if(tsl.exists(date)) {
      spark.read.parquet(tsl.partition(date)).as[UdpLocationSet]
    } else {
      spark.emptyDataset[UdpLocationSet]
    }
  }

  /**
    * Compute one days LocationSet
    * @param ifaObsTsl TSL to read Ifa Obs from
    * @param dailyMaxmindPath path to maxmind carrier data
    * @param date date to compute
    * @param datasource datasource to compute
    * @param spark implicit spark session
    * @return computed LocationSet
    */
  private def processOneIfaObs(
    ifaObsTsl: TimeSeriesLocation,
    dailyMaxmindPath: URI,
    date: LocalDate,
    datasource: ComlinkdataDatasource
  )(implicit spark: SparkSession): Dataset[UdpLocationSet] = {
    import spark.implicits._

    if (!ifaObsTsl.exists(date)) {
      throw new IllegalArgumentException(f"$ifaObsTsl for date $date does not exist")
    }

    val fullInputPath = ifaObsTsl.partition(date)
    logger.info(f"Reading in dataset from $fullInputPath")
    val ds = spark.read.parquet(fullInputPath).as[UdpDailyIfaNoYmd]

    //Flat map out every individual observation from every ifa in IFA observations
    val locDs = ds.flatMap(dsRow => {
      dsRow.observations.map(obs => {
        UdpLocationSetObservation(
          (obs.location.lat * PRECISION).asInstanceOf[MccMnc], //Go to 4 digit precision
          (obs.location.lng * PRECISION).asInstanceOf[MccMnc], //Go to 4 digit precision
          Objects.hashCode(obs.location.lat, obs.location.lng),
          dsRow.ifa,
          obs.t_utc,
          obs.t_local,
          obs.ip,
          obs.location_type,
          obs.connection_type,
          obs.accuracy,
          obs.gps_speed,
          obs.place_id)
      })
    })

    //Only need ip & carrier from maxmind
    val maxmindDs = loadMaxmind(dailyMaxmindPath, datasource, date)
      .select($"ip", $"carrier", $"connection_type" as "connection_type_maxmind")

    //Join ifa obs & maxmind on ip's
    //The maxmind dataset is "only" around 500mb; Broadcasting makes this join faster
    //My (DP) testing shows 1hr/day runtime without it vs 45 min with it.
    val unGrouped = locDs.join(broadcast(maxmindDs), Seq("ip"), "left")
      .select(
        "lat_4f",
        "lng_4f",
        "location_hash",
        "ip",
        "ifa",
        "location_type",
        "accuracy",
        "t_local",
        "carrier",
        "connection_type_maxmind")
      .withColumn("date", to_date($"t_local")) //Get date of observation
      .where($"date".isNotNull) //Ignore observations that don't have local time
      .withColumn("day_hour", toHourOfDay($"t_local")) // get hours of the day
      .withColumn("connection_type_maxmind",
        when($"connection_type_maxmind" isin ("Cable/DSL", "Corporate", "Dialup", "Satellite"), "WIFI")
          .when($"connection_type_maxmind" === "Cellular", "CELLULAR")
          .otherwise(null)
      )
      .dropDuplicates("lat_4f", "lng_4f", "ifa", "day_hour") // only allow one observation per hour
      .withColumn("ifa_wifi", when($"connection_type_maxmind" === "WIFI", $"ifa"))
      .withColumn("ifa_cell", when($"connection_type_maxmind" === "CELLULAR", $"ifa"))
      .withColumn("ip_wifi", when($"connection_type_maxmind" === "WIFI", $"ip"))
      .withColumn("ip_cell", when($"connection_type_maxmind" === "CELLULAR", $"ip"))
      .cache()

    val groupedCounts = unGrouped
      .groupBy("lat_4f", "lng_4f").agg( //aggregate on lat & long
      countDistinct("ifa").as("ifas_1"),
      countDistinct("ifa_wifi").as("ifas_wifi_1"),
      countDistinct("ifa_cell").as("ifas_cell_1"),
      countDistinct("location_hash").as("precise_loc_count"),
      bround(avg("accuracy"), 2).as("avg_acc"),
      bround(stddev("accuracy"), 2).as("std_acc"),
      countDistinct("ip").as("ips_1"),
      countDistinct("ip_wifi").as("ips_wifi_1"),
      countDistinct("ip_cell").as("ips_cell_1"),
      when(
        countDistinct("ifa") < MAX_IFAs,
        dayHourSum($"day_hour", $"connection_type_maxmind")
      ) as "day_hour_struct",
      when(
        countDistinct("ifa") < MAX_IFAs,
        collect_set($"ifa")
      ).otherwise(null) as "distinct_ifas",
      when(
        countDistinct("ip") < MAX_IFAs,
        collect_set($"ip")
      ).otherwise(null) as "distinct_ips",
      when(
        countDistinct("ifa") < MAX_IFAs,
        carrierUDAF($"carrier", $"connection_type_maxmind", $"date", $"location_hash")
      ) as "carrier_map_struct"
    )
      .withColumn("ifa_last_seen_map", toVecMapWithDate(date)($"distinct_ifas"))
      .withColumn("ifas_7", distinctCountRollingWindow(7, date)($"ifa_last_seen_map"))
      .withColumn("ifas_28", distinctCountRollingWindow(28, date)($"ifa_last_seen_map"))
      .withColumn("ifas_91", distinctCountRollingWindow(91, date)($"ifa_last_seen_map"))
      .withColumn("ifas_126", distinctCountRollingWindow(126, date)($"ifa_last_seen_map"))
      .withColumn("ip_last_seen_map", toVecMapWithDate(date)($"distinct_ips"))
      .withColumn("ips_7", distinctCountRollingWindow(7, date)($"ip_last_seen_map"))
      .withColumn("ips_28", distinctCountRollingWindow(28, date)($"ip_last_seen_map"))
      .withColumn("ips_91", distinctCountRollingWindow(91, date)($"ip_last_seen_map"))
      .withColumn("ips_126", distinctCountRollingWindow(126, date)($"ip_last_seen_map"))
      .withColumn("day_hours", when($"day_hour_struct".isNotNull, $"day_hour_struct"("day_hours")).otherwise(Array.empty[Int]))
      .withColumn("day_hours_cell", when($"day_hour_struct".isNotNull, $"day_hour_struct"("day_hours_cell")).otherwise(Array.empty[Int]))
      .withColumn("day_hours_wifi", when($"day_hour_struct".isNotNull, $"day_hour_struct"("day_hours_wifi")).otherwise(Array.empty[Int]))
      .withColumn("day_hours_history", emptyHourHistory()) // will be added in combine function
      .withColumn("week_hours_1", lit(Array.ofDim[Float](0, 0))) // will be added in combine function
      .withColumn("week_hours_4", lit(Array.ofDim[Float](0, 0))) // will be added in combine function
      .withColumn("week_hours_7", lit(Array.ofDim[Float](0, 0))) // will be added in combine function
      .withColumn("week_hours_18", lit(Array.ofDim[Float](0, 0))) // will be added in combine function
      .withColumn("date", lit(Date.valueOf(date)))
      .withColumn("carrier_map_wifi_history", when($"carrier_map_struct".isNotNull, $"carrier_map_struct"("wifi")))
      .withColumn("carrier_map_cell_history", when($"carrier_map_struct".isNotNull, $"carrier_map_struct"("cell")))
      .withColumn("carrier_count_map_wifi", typedLit(Map.empty[String, CarrierSeen])) // will be added in combine function
      .withColumn("carrier_count_map_cell", typedLit(Map.empty[String, CarrierSeen])) // will be added in combine function
      .drop("day_hour_struct", "distinct_ifas", "distinct_ips", "carrier_map_struct")

    groupedCounts.as[UdpLocationSet]
  }

  /**
    * Combine previous days LocationSet with newly computed LocationSet
    * @param newDaysLocSet newly computed LocationSet
    * @param prevDay previous days LocationSet
    * @param runDate date of for the newly computed LocationSet
    * @param spark implicit spark session
    * @return computed data
    */
  private def combine(newDaysLocSet: Dataset[UdpLocationSet], prevDay: Dataset[UdpLocationSet], runDate: LocalDate)(implicit spark: SparkSession) = {
    import spark.implicits._

    //Select off locations that were seen yesterday but not today. We won't touch them; just add rows back on at the end
    val prevDayNoNewData = prevDay
      .join(newDaysLocSet, Seq("lat_4f", "lng_4f"), "leftanti")
      .where($"date" >= Date.valueOf(runDate.minusDays(TTL_DAYS)))

    //Grabs rows seen today and possibly seen yesterday and prepend yesterday's history columns with 'prev_' so no conflict
    val joined = newDaysLocSet.join(
      prevDay.select(
        $"lat_4f",
        $"lng_4f",
        $"ifa_last_seen_map" as "prev_ifa_last_seen_map",
        $"ip_last_seen_map" as "prev_ip_last_seen_map",
        $"day_hours_history" as "prev_day_hours",
        $"carrier_map_wifi_history" as "prev_carrier_map_wifi",
        $"carrier_map_cell_history" as "prev_carrier_map_cell"),
      Seq("lat_4f", "lng_4f"),
      "left"
    )

    //Combine yesterday and today with updated historys
    val combined = joined
      .withColumn("ifa_last_seen_map", lastSeenMapCombine(Date.valueOf(runDate.minusDays(TTL_DAYS)))($"prev_ifa_last_seen_map", $"ifa_last_seen_map"))
      .withColumn("ip_last_seen_map", lastSeenMapCombine(Date.valueOf(runDate.minusDays(TTL_DAYS)))($"prev_ip_last_seen_map", $"ip_last_seen_map"))
      .withColumn("carrier_map_wifi_history", lastCarrierMapCombine(Date.valueOf(runDate.minusDays(TTL_DAYS)))($"prev_carrier_map_wifi", $"carrier_map_wifi_history"))
      .withColumn("carrier_map_cell_history", lastCarrierMapCombine(Date.valueOf(runDate.minusDays(TTL_DAYS)))($"prev_carrier_map_cell", $"carrier_map_cell_history"))
      .drop("prev_ifa_last_seen_map", "prev_ip_last_seen_map", "day_hours_history", "prev_carrier_map_wifi", "prev_carrier_map_cell")
      .withColumnRenamed("prev_day_hours", "day_hours_history")
      .withColumn("day_hours_history", dayHourCombine($"day_hours_history", $"day_hours"))
      .withColumn("week_hours_1", lastNWeekNorm(1, runDate)($"day_hours_history"))
      .withColumn("week_hours_4", lastNWeekNorm(4, runDate)($"day_hours_history"))
      .withColumn("week_hours_7", lastNWeekNorm(7, runDate)($"day_hours_history"))
      .withColumn("week_hours_18", lastNWeekNorm(18, runDate)($"day_hours_history"))
      .withColumn("carrier_count_map_wifi", toCarrierCount($"carrier_map_wifi_history"))
      .withColumn("carrier_count_map_cell", toCarrierCount($"carrier_map_cell_history"))

    //add back in the rows from yesterday that weren't seen today (cache to possibly speed up running tomorrow's data)
    combined.unionByName(prevDayNoNewData).as[UdpLocationSet].cache()
  }
}