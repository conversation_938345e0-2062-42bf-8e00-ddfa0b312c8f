package com.comlinkdata.emrjobs.spark.udp.tier1

import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.metrics.CldSparkMetricsRegistry
import com.comlinkdata.largescale.schema.udp.location.{Rectangle, LocationStat, Point}
import com.comlinkdata.largescale.schema.udp.tier0.UdpRaw
import com.comlinkdata.largescale.schema.udp.tier1.{UdpIpObservation, UdpDirectories}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SparkSession, SaveMode}

object DailyIpObservationsJob extends MeteredSparkJob(DailyIpObservationsRunner, forceKryoRegistration = true) {
  override def kryoSerializationClasses = super.kryoSerializationClasses ++ Seq(
    classOf[LocationStat],
    classOf[Rectangle.Float],
    classOf[Point.Float],
    classOf[UdpRaw],
    classOf[UdpIpObservation]
  )

  override def configureSpark(builder: SparkSession.Builder): SparkSession.Builder =
    super.configureSpark(builder)
      .config("spark.kryoserializer.buffer.max", "256m")
}

object DailyIpObservationsRunner extends MeteredSparkJobRunner[Tier1JobConfig] with LazyLogging {
  override def run(config: Config)(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Unit = {
    import spark.implicits._
    val tsIn = TimeSeriesLocation
      .ofDatasource(config.sourcePath, config.datasource, PartitionType.DatePartitionType("pdate"))
      .withPartition(config.datasource)
      .build
    val tsOut = TimeSeriesLocation
      .ofYmdDatePartitions(config.destinationPath)
      .withSubfolder(UdpDirectories.dailyIpObservation)
      .withPartition(config.datasource)
      .build
    config.dateRangeOpt.getOrElse(getRange(config.maxDaysToRun, tsIn, tsOut))
      .foreach { day =>
        val dest = tsOut.validOutputPartitionOrThrow(day)
        val partitions = getInputPartitions(day, tsIn)
        logger.info(s"Reading partitions $partitions for day $day writing to $dest")
        val result = spark.read
          .option(ReadOpts.basePath, tsIn.source.toString)
          .parquet(partitions: _*)
          .as[UdpRaw]
          .transform(UdpIpObservationRunner.createUnifiedIpDataset(config.datasource, _))
          .repartition(config.numFilesPerPartition)
          .cache
        result
          .drop("ds", "year", "month", "day")
          .write
          .mode(SaveMode.ErrorIfExists)
          .parquet(dest)
        UdpIpObservationRunner.meterIpDataByDate(result)
      }
  }
}
