package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.largescale.commons.{TimeSeriesLocation, SparkJobRunner, Util<PERSON>, SparkJob}
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband.lookup.ConsolidatedCarrier.implicits.CarrierLookupOps
import com.comlinkdata.largescale.schema.udp.installbase.{IfaLevelSummary, IPHighConfidence}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql._
import org.apache.spark.sql.functions.lit

import java.net.URI
import java.time.LocalDate


/**
  *
  * @param highConfidenceLocation  : Location of high confidence dataset
  * @param carrierLookupLocation   : Location of carrier lookup table for sp_id/sp_platform
  * @param currQuarterDate         : Date for current quarter data
  * @param prevQuarterDate         : Date for previous quarter data (3 months prior to current date)
  * @param ifaLevelSummaryLocation : output location
  * @param outputPartitions        : number of partitions
  */
case class IFALevelSummaryJoinConfig(
  highConfidenceLocation: URI,
  carrierLookupLocation: URI,
  currQuarterDate: Option[LocalDate],
  prevQuarterDate: Option[LocalDate],
  ifaLevelSummaryLocation: URI,
  outputPartitions: Int
)


object IFALevelSummaryJoinJob extends SparkJob(IFALevelSummaryJoinJobRunner)

object IFALevelSummaryJoinJobRunner extends SparkJobRunner[IFALevelSummaryJoinConfig] with LazyLogging {

  def runJob(config: IFALevelSummaryJoinConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    // Calculate current quarter and previous quarter date to store the quarterly results of ifa-summary
    val currQuarterDateOutput = config.currQuarterDate.getOrElse(Utils.calculateCurrQuarterDate(LocalDate.now()))
    val prevQuarterDateOutput = config.prevQuarterDate.getOrElse(Utils.calculatePrevQuarterDate(LocalDate.now()))

    // Read high-confidence dataset for the latest available date
    val tsl = IPHighConfidence.tsl(config.highConfidenceLocation)
    val latestQuarterDate = tsl.latestDate
    val previousQuarterDate = tsl.latestDate(beforeOrEqual = Option(latestQuarterDate.minusMonths(3)))

    val carrierLookup = CarrierLookup.read(path = config.carrierLookupLocation)

    // Read high-confidence data for prev and curr quarter
    logger.info(s"Get high-confidence data for current quarter from loc: ${config.highConfidenceLocation}, date: $latestQuarterDate")
    val currentQuarterHighConfidence = IPHighConfidence.read(config.highConfidenceLocation, latestQuarterDate)
    logger.info(s"Get high-confidence data for previous quarter from loc: ${config.highConfidenceLocation}, date: $previousQuarterDate")
    val prevQuarterHighConfidence = IPHighConfidence.read(config.highConfidenceLocation, previousQuarterDate)

    // get sp_id and sp_platform
    val spCarrierCurrent = getSPCarrier(currentQuarterHighConfidence, carrierLookup, latestQuarterDate)
    val spCarrierPrevious = getSPCarrier(prevQuarterHighConfidence, carrierLookup, latestQuarterDate)

    // Writing data to S3 location
    overwrite(spCarrierCurrent, currQuarterDateOutput, config.outputPartitions, IfaLevelSummary.tsl(config.ifaLevelSummaryLocation))
    overwrite(spCarrierPrevious, prevQuarterDateOutput, config.outputPartitions, IfaLevelSummary.tsl(config.ifaLevelSummaryLocation))
  }

  /**
    * Reads in data from High Confidence from date corresponding to processing date
    *
    * @param target      : source DataSet of High Confidence
    * @param currentDate : processing date of job
    * @return
    */
  def getQuarterData(target: Dataset[IPHighConfidence], currentDate: LocalDate)(implicit spark: SparkSession): Dataset[IPHighConfidence] = {
    import spark.implicits._
    val day = currentDate.getDayOfMonth.toString
    val formattedDay = if (day.length == 1) "0".concat(day) else day
    val month = currentDate.getMonthValue.toString
    val formattedMonth = if (month.length == 1) "0".concat(month) else month
    target
      .where($"year" === currentDate.getYear.toString &&
        $"month" === formattedMonth &&
        $"day" === formattedDay)
      .as[IPHighConfidence]
  }

  /**
    * Joins data from High Confidence with carrier lookup table for Legacy Sharetracker Processing.
    *
    * @param target        : High Confidence data from processing date
    * @param carrierLookup contents of carrierLookup lookup table
    * @return
    */
  private[installbase] def getSPCarrier(target: Dataset[IPHighConfidence], carrierLookup: Dataset[CarrierLookup], runDate: LocalDate)(implicit spark: SparkSession): Dataset[IfaLevelSummary] = {
    import spark.implicits._
    target
      .transform(carrierLookup.addIDsRunDate[IPHighConfidence](
        carrier = $"carrier",
        date = runDate,
        additionalOutputColumns = Seq("consolidated_carrier")))
      .withColumnRenamed("census_block_id", "census_block")
      .withColumnRenamed("census_group_id", "census_block_group")
      .withColumnRenamed("sp", "sp_id")
      .withColumn("is_ip_refresh", lit(false))
      .withColumn("home_source_flag", lit(""))
      .withColumn("census_block_share", lit(1))
      .select($"ifa", $"is_ip_refresh", $"consolidated_carrier", $"sp_id", $"sp_platform", $"census_block_group", $"home_source_flag",
        $"census_block", $"census_block_share")
      .as[IfaLevelSummary]
  }

  /**
    * Writes or overwrites given dataset on a date-partitioned path.
    *
    * The final path is composed as "$basePath/date=$date".
    *
    * @param ds    dataset
    * @param date  date
    * @param parts output number of partitions
    * @param spark spark session
    * @tparam T data type
    */
  def overwrite[T: Manifest](
    ds: Dataset[T],
    date: LocalDate,
    parts: Int,
    tsl: TimeSeriesLocation
  )(implicit spark: SparkSession): Unit = {
    val outputPath = tsl.partition(date)
    logger.info(s"Writing output to: $outputPath")
    ds
      .repartition(parts)
      .write
      .mode(SaveMode.Overwrite)
      .parquet(outputPath)
  }
}