package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.emrjobs.spark.udp.installbase.LocationFilters
import com.comlinkdata.emrjobs.spark.udp.installbase.LocationFilters.{IFA_PER_IP_CUTOFF, locCountsFromLocTs, locWithHourFromObs}
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.udp.installbase.{IfaToLocCountsExploded, IfaToLocCounts, IfaToIpWithObs}
import com.comlinkdata.largescale.schema.udp.tier1.UdpDailyDevice
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SparkSession, Dataset, DataFrame, SaveMode}

import java.net.URI
import java.time.LocalDate

case class TMOIfaToLocationsConfig(
  ifaObsPath: URI,
  maxmindPath: URI,
  carrierLookupPath: URI,
  carrierLookupFwPath: URI,
  ipv6TruncationLookupPath: URI,
  hourWeightLookupPath: URI,
  dailyIpCarrierPath: Option[URI],
  udpDailyDeviceLocation: URI,
  tmoFwDeviceList: List[String],
  datasources: Seq[ComlinkdataDatasource],
  startDate: LocalDate,
  endDate: LocalDate,
  repartition: Option[Int],
  ifaToLocationOutPath: URI
) extends IfaObsWithMMConfig {
  override def getRepartition: Int = {
    repartition.getOrElse(1)
  }

  override def getOutPath: URI = ifaToLocationOutPath
}

object TMOIfaToLocationsJob extends SparkJob(TMOIfaToLocationsRunner)

object TMOIfaToLocationsRunner
  extends SparkJobRunner[TMOIfaToLocationsConfig]
  with SparkConstants
  with LazyLogging {

  override def runJob(config: TMOIfaToLocationsConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    for (day <- LocalDateRange.of(config.startDate, config.endDate)) {
      val dsDailyDevice = UdpDailyDevice.read(config.udpDailyDeviceLocation, day, config.datasources)
      val dfDailyDevice = dsDailyDevice
        .filter(col("ds") === "mw05") // we do not use ds input here, because only mw05 has the `device_type` flag
        .filter(lower(col("device_type")).isin(config.tmoFwDeviceList: _*))
        .select("ifa")
        .distinct()

      def dsToIfaObsWithMM: Map[ComlinkdataDatasource, IfaObsMMNightTime.Base] =
        IfaObsMMNightTime.dsToTMOIfaObsWithObs(config)

      val dataOut: Dataset[IfaToLocCountsExploded] = config.datasources.map(ds => {
        logger.info(s"Starting: $day. Running datasource: $ds")
        val dsToIfa = dsToIfaObsWithMM(ds).read(day)
        val withDevice = dsToIfa
          .join(dfDailyDevice, Seq("ifa"), "inner")
          .as[IfaToIpWithObs]
        withDevice
      })
        .map(computeLocationSingleDS)
        .reduce(_ unionByName _)
        .transform(aggLocation)
        .transform(explodeCounts)
        .join(dfDailyDevice, Seq("ifa"), "inner")
        .as[IfaToLocCountsExploded]

      val dfOut: DataFrame = dataOut.repartition(config.getRepartition).toDF()

      val tslOut = TimeSeriesLocation
        .ofYmdDatePartitions(config.getOutPath)
        .build.partition(day)

      UriDFTableRW.fromStr(tslOut).writeWithHistory(
        dfOut,
        SaveMode.ErrorIfExists,
        Seq(config)
      )
    }
  }

  def computeLocationSingleDS(ipToIfa: Dataset[IfaToIpWithObs])(implicit spark: SparkSession): Dataset[IfaToLocationNoStats] = {
    import spark.implicits._

    val approxCountIfa = ipToIfa
      .filter($"consolidated_id".isNotNull) // only run locations for tracked carriers
      .select(
        $"ifa",
        $"ip",
        $"obs_count",
        $"night_obs_count",
        $"observations",
        approx_count_distinct("ifa").over(Window.partitionBy($"ip")) as "approx_ifas")
      .where($"approx_ifas" < IFA_PER_IP_CUTOFF)

    val locFilter = approxCountIfa
      .select($"ifa",
        $"ip",
        $"obs_count",
        $"night_obs_count",
        explode($"observations") as "obs")
      .transform(LocationFilters.locationFilters($"ifa", $"ip", $"obs_count", $"night_obs_count"))

    val locAgg = locFilter.groupBy("ifa", "obs_count", "night_obs_count")
      .agg(
        collect_set("ip") as "ips",
        flatten(collect_list($"observations")) as "obs")
      .withColumn("locations", locWithHourFromObs($"obs")).drop("obs")
      .withColumn("hours_seen", array_distinct(transform($"locations.ts", x => hour(x))))

    val loc = locAgg
      .withColumn("ip_count", size($"ips"))
      .select(
        $"ifa",
        $"obs_count",
        $"night_obs_count",
        $"ips",
        $"ip_count",
        $"hours_seen",
        $"locations")
      .as[IfaToLocationNoStats]

    loc
  }

  def aggLocation(allDsIn: Dataset[IfaToLocationNoStats])(implicit spark: SparkSession): Dataset[IfaToLocCounts] = {
    import spark.implicits._

    allDsIn
      .groupBy("ifa")
      .agg(
        sum($"obs_count") as "obs_count",
        sum($"night_obs_count") as "night_obs_count",
        array_distinct(flatten(collect_set("hours_seen"))) as "hours_seen",
        array_distinct(flatten(collect_set("ips"))) as "ips",
        flatten(collect_list("locations")) as "locations")
      .withColumn("ip_count", size($"ips"))
      .withColumn("loc_counts", locCountsFromLocTs($"locations"))
      .as[IfaToLocCounts]
  }

  def explodeCounts(raw: Dataset[IfaToLocCounts])(implicit spark: SparkSession): Dataset[IfaToLocCountsExploded] = {
    import spark.implicits._
    raw
      .select(IfaToLocCounts.cols ++ Seq(explode($"loc_counts")): _*)
      .withColumnRenamed("key", "point")
      .withColumnRenamed("value", "point_obs")
      .select(IfaToLocCountsExploded.cols: _*)
      .as[IfaToLocCountsExploded]
  }
}
