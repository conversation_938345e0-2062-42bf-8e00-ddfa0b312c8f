package com.comlinkdata.emrjobs.spark.udp.locationset

import com.comlinkdata.emrjobs.spark.udp.locationset.UdpLocationSetRunner.NUM_LOCS_IN_CARRIER_MAP
import com.comlinkdata.largescale.schema.udp.tier2.{HourHistory, CarrierSeen}
import com.comlinkdata.largescale.schema.udp.tier2.HourHistory.TTL_DAYS
import org.apache.spark.sql.Row
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions.udf

import java.sql.{Timestamp, Date}
import java.time.LocalDate
import scala.collection.mutable

/**
  * UDF's for LoctionSet
  */
object LocationSetUDFs {

  /**
    * Convert a timestamp to hour of day
    * @return Int from 0 to 23
    */
  def toHourOfDay: UserDefinedFunction = udf((localTS:Timestamp) => _toHourOfDay(localTS))

  /**
    * Convert a timestamp to hour of day
    * @param localTS Local time stamp of ping
    * @return Int from 0 to 23 (or null if localTs is null)
    */
  def _toHourOfDay(localTS:Timestamp): Int = {
    localTS match {
      case null => -1
      case _ => localTS.toLocalDateTime.getHour
    }
  }

  /** Get an empty option of History */
  def emptyHourHistory: UserDefinedFunction =udf(() => None: Option[HourHistory])

  /**
    * Concatenate day's to make up a week and add weeks together
    * @return 2D Array 7x24 or IFA counts
    */
  def lastNWeekSum: (Int, LocalDate) => UserDefinedFunction = (nWeeks: Int, runDate: LocalDate) =>
    udf((historyUncast: Row) => _lastNWeekSum(nWeeks, runDate, historyUncast))

  /**
    * Concatenate day's to make up a week and add weeks together
    * @param nWeeks Number of weeks to sum
    * @param runDate Today's date (used to back out the day of the week)
    * @param historyUncast HourHistory as GenericRowWithSchema
    * @return 2D Array 7x24 or IFA counts
    */
  def _lastNWeekSum(nWeeks: Int, runDate: LocalDate, historyUncast: Row): Array[Array[Float]] = {
    require(nWeeks >= 1 && nWeeks <= 18)
    val result = Array.ofDim[Float](7, 24)

    val iHist = historyUncast.getAs[Int](0)
    val history = historyUncast.getAs[mutable.WrappedArray[mutable.WrappedArray[Int]]](1)

    if(history.nonEmpty) {
      for (iWeek <- 0 until nWeeks) {
        for (iDay <- 0 until 7) {
          val daysFromRunDate = iDay + (iWeek * 7)
          val iMinus = iHist - daysFromRunDate
          val iNext = if (iMinus < 0) iMinus + TTL_DAYS else iMinus

          val thisDayOfWeek = runDate.minusDays(daysFromRunDate).getDayOfWeek.ordinal()

          for (iHour <- 0 until 24) {
            if(history(iNext).nonEmpty)
              result(thisDayOfWeek)(iHour) += history(iNext)(iHour)
          }
        }
      }
    }

    result
  }

  def lastNWeekNorm: (Int, LocalDate) => UserDefinedFunction = (nWeeks: Int, runDate: LocalDate) =>
    udf((historyUncast: Row) => _lastNWeekNorm(nWeeks, runDate, historyUncast))

  def _lastNWeekNorm(nWeeks: Int, runDate: LocalDate, historyUncast: Row): Array[Array[Float]] = {
    if (historyUncast == null) {
      Array.ofDim[Float](7, 24)
    } else {
      val result = _lastNWeekSum(nWeeks, runDate, historyUncast)

      for(iDay <- 0 until 7) {
        for (iHour <- 0 until 24) {
          result(iDay)(iHour) /= nWeeks
        }
      }

      result
    }
  }

  /**
    * Count Distinct IFA's in a window using IFA to last seen date map
    * @return number of distinct IFA's in the window
    */
  def distinctCountRollingWindow: (Int, LocalDate) => UserDefinedFunction =
    (daysToLookBack: Int, today: LocalDate) => udf((ifaDate: Map[Vector[Byte], Date]) =>
      _distinctCountRollingWindow(daysToLookBack, today, ifaDate))

  /**
    * Count Distinct IFA's in a window using IFA to last seen date map
    * @param daysToLookBack size of window
    * @param today date of day 0 in the window
    * @param ifaDate IFA to last seen date map
    * @return number of distinct IFA's in the window
    */
  def _distinctCountRollingWindow(daysToLookBack: Int, today: LocalDate, ifaDate: Map[Vector[Byte], Date]): Int = {
    if(ifaDate == null) -1
    else {
      val dateMustBeAfter = Date.valueOf(today.minusDays(daysToLookBack))
      ifaDate.count(p => p._2.after(dateMustBeAfter))
    }
  }

  /**
    * UDF that creates a distinct mapping of ifas to a day
    * @return Map with keys for all given IFA's and values of given date
    */
  def toVecMapWithDate: LocalDate => UserDefinedFunction =
      (today: LocalDate) => udf((ifas: mutable.WrappedArray[Array[Byte]]) => _toVecMapWithDate(today, ifas))

  /**
    * Make map of IFA to last seen date map
    * @param today date should be mapped to. Usually current day.
    * @param ifas List of IFAs for day
    * @return Map with keys for all given IFA's and values of given date
    */
  def _toVecMapWithDate(today: LocalDate, ifas: mutable.WrappedArray[Array[Byte]]): Map[Vector[Byte], Date] = {
    if(ifas == null || today == null) Map.empty[Vector[Byte], Date]
    else {
      ifas.map(ifa => {
        val ifaVel = ifa.toVector
        (ifaVel, Date.valueOf(today))
      }).toMap[Vector[Byte], Date]
    }
  }

  /**
    * Helper function that will return the max date
    * @param a First Date
    * @param b Second Date
    * @return Max date between a and b
    */
  private def max(a: Date, b: Date): Date = if (a.after(b)) a else b

  /**
    * Merges two maps with Time to live rules
    *
    * Merge rules, join on key (IFA values):
    *   if prev.key is not null and curr.key is not null, update value with curr.value (latest date)
    *   if prev.key is null, add new key
    *   if curr.key is null and prev.value is >= TTL, keep key
    *   if curr.key is null and prev.Value < TTL, throw away key
    *
    * @param ttlDate Time to Live date, should remove any items that are older than this dates
    * @return Merged IFA Maps
    */
  def lastSeenMapCombine (ttlDate: Date): UserDefinedFunction =
    udf((prev: Map[Seq[Byte], Date], curr: Map[Seq[Byte], Date]) =>
      _lastSeenMapCombine(ttlDate, prev, curr))

  /**
    * Merges two maps with time to live rules
    * (Scala shortcut) map1 ++ map2 rules:
    *   replaces any (k,v) from the map on the left side of ++ (here map1) by (k,v) from the right side map
    *   if (k,_) already exists in the left side map (here map1), e.g. Map(1->1) ++ Map(1->2) results in Map(1->2)
    *   Treat as full outer join, with right side map as higher priority
    *
    * Merge rules, join on key (IFA values):
    *   if prev.key is not null and curr.key is not null, update value with curr.value (latest date)
    *   if prev.key is null, add new key
    *   if curr.key is null and prev.value is >= TTL, keep key
    *   if curr.key is null and prev.Value < TTL, throw away key
    *
    * @param ttlDate Time to Live date, should remove any items that are older than this dates
    * @param prev previous map of items to days, built from toVecMapWithDate
    * @param curr current map of items to days, built from toVecMapWithDate
    * @return Merged IFA Maps
    */
  def _lastSeenMapCombine(ttlDate: Date, prev: Map[Seq[Byte], Date], curr: Map[Seq[Byte], Date]) : Map[Seq[Byte], Date] = {
    (prev, curr) match {
      case (null, null) => Map.empty[Seq[Byte], Date]
      case (null, _) => curr
      case (_, null) => prev.filter(_._2.after(ttlDate))
      case (_, _) =>
        (prev.keySet ++ curr.keySet).map(ifa => {
          val outTs = (prev.get(ifa), curr.get(ifa)) match {
            case (None, Some(x)) => x
            case (Some(x), None) => x
            case (Some(x), Some(y)) => max(x, y)
            case _ => throw new IllegalStateException("Must have at least one input map")
          }
          (ifa, outTs)
        }).filter(_._2.after(ttlDate)).toMap
    }
  }

  /**
    * Combine last carrier history maps from yesterday and today
    * @param ttlDate date too TTL from
    * @return merged carrier map
    */
  def lastCarrierMapCombine (ttlDate: Date): UserDefinedFunction =
    udf((prev: Map[(String, Date), Map[Int, Int]], curr: Map[(String, Date), Map[Int, Int]]) => _lastCarrierMapCombine(ttlDate, prev, curr))

  /**
    * Combine last carrier history maps from yesterday and today
    * @param ttlDate date too TTL from
    * @param prev previous day's carrier map
    * @param curr today day's carrier map
    * @return merged carrier map
    */
  def _lastCarrierMapCombine (
    ttlDate: Date,
    prev: Map[(String, Date), Map[Int, Int]],
    curr: Map[(String, Date), Map[Int, Int]]
  ): Map[(String, Date), Map[Int, Int]] = {
    val result = (prev, curr) match {
      case (null, null) => Map.empty[(String, Date), Map[Int, Int]]
      case (_, null) => prev
      case (null, _) => curr
      case (_, _) =>
        val currAsTuple4 = curr //_toRowAndBackAgain(curr)
        val pervAsTuple4 = prev //_toRowAndBackAgain(prev)

        (currAsTuple4.keySet ++ pervAsTuple4.keySet).map(key => {
          (pervAsTuple4.get(key), currAsTuple4.get(key)) match {
            case (None, Some(locCountMap)) => (key, locCountMap)
            case (Some(locCountMap), None) => (key, locCountMap)
            case (Some(locCountMap1), Some(locCountMap2)) =>
              val updatedLocMap =
                (locCountMap1.keySet ++ locCountMap2.keySet).map(locKey =>
                  (locCountMap1.get(locKey), locCountMap2.get(locKey)) match {
                    case (None, Some(count)) => (locKey, count)
                    case (Some(count), None) => (locKey, count)
                    case (Some(count1), Some(count2)) => (locKey, count1 + count2)
                  }).toMap[Int, Int]
              (key, updatedLocMap)
            case _ => throw new IllegalStateException("Must have at least one input map")
          }
        }).filter(_._1._2.after(ttlDate)).toMap[(String, Date), Map[Int, Int]]
    }
    result.filter(_._2.size > NUM_LOCS_IN_CARRIER_MAP) // Make sure there are 2+ precise loc's on every date
  }

  /**
    * Combine day HourHistory's from previous days and today.
    * Functions as a fixed length 1ueue where i is the head.
    * Instead of values being pushed out, they are overwritten.
    * @return current day's counts are added to HourHistory (and last day is dropped from HourHistory)
    */
  def dayHourCombine: UserDefinedFunction =
    udf((historyUncast: Row, currentDay: Array[Int]) => _dayHourCombine(historyUncast, currentDay))

  /**
    * Combine day HourHistory's from previous days and today.
    * Functions as a fixed length 1ueue where i is the head.
    * Instead of values being pushed out, they are overwritten.
    * @param historyUncast GenericRow from spark of HourHistory
    * @param currentDay current days hour counts
    * @return current day's counts are added to HourHistory (and last day is dropped from HourHistory)
    */
  def _dayHourCombine(historyUncast: Row, currentDay: Array[Int]): Some[HourHistory] = {
    if (historyUncast == null) {
      val history = Array.ofDim[Int](TTL_DAYS, 24)
      history(0) = currentDay
      Some(HourHistory.fromArray(1, history))
    } else {
      val history = historyUncast.getAs[mutable.WrappedArray[mutable.WrappedArray[Int]]](1)
      val i = historyUncast.getAs[Int](0)
      history(i) = currentDay
      val iNext = if (i + 1 == history.length) 0 else i + 1
      Some(HourHistory(iNext, history))
    }
  }

  /**
    * Create a map of carriers to the number of days they were seen over the last 126 days. (Drop carriers that were seen at < 3 locations)
    * @return map of carriers to the number of days they were seen over the last 126 days
    */
  def toCarrierCount: UserDefinedFunction = udf((carrierMap: Map[(String, Date), Map[Int, Int]]) => _toCarrierCount(carrierMap))


  /**
    * Create a map of carriers to the number of days they were seen over the last 126 days. (Drop carriers that were seen at < 3 locations)
    * @param carrierMap Carrier History map
    * @return map of carriers to the number of days they were seen over the last 126 days
    */
  def _toCarrierCount(carrierMap: Map[(String, Date), Map[Int, Int]]): Map[String, CarrierSeen] = {
    if(carrierMap == null) {
      Map.empty[String, CarrierSeen]
    } else {
      val unGrouped = carrierMap.toSeq.filter(_._2.size > NUM_LOCS_IN_CARRIER_MAP).map(_._1)

      // Group by carrier name
      val carriersToDatesSeen = unGrouped.groupBy(_._1).map(carrierToDates => {
        (carrierToDates._1, carrierToDates._2.map(_._2))
      })

      carriersToDatesSeen.map(carrierToDate => (carrierToDate._1, CarrierSeen.fromDates(carrierToDate._2)))
    }
  }
}
