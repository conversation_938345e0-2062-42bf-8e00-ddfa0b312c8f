package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.broadband.lookup.ConsolidatedCarrier.implicits.CarrierLookupOps
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.udp.installbase.{IPHighConfidence, TransferMoversChurn}
import com.comlinkdata.largescale.schema.udp.location.Point
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql
import org.apache.spark.sql.{SparkSession, Dataset, Row, SaveMode}
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions.{lit, udf, substring}

import java.net.URI
import java.time.LocalDate


case class TransferMoversConfig(
  highConfPath: URI,
  carrierLookupPath: URI,
  hcDate: LocalDate,
  hcPrevDate: LocalDate,
  minMoveDist: Option[Float],
  outPath: URI,
  repartition: Option[Int]
)

object TransferMoversJob extends SparkJob(TransferMoversRunner)

object TransferMoversRunner
  extends SparkJobRunner[TransferMoversConfig]
    with SparkConstants
    with LazyLogging {

  //Anything less than this is too small to be a real move (in meters)
  val MIN_MOVE_DIST = 3000

  //If this many digits of census block are the same also don't consider it a real move
  val NUM_CB_DIGITS_NOT_MATCH = 11

  //Sum of "percent_block" from current and previous high conf must be above this (or we assume we don't have a good handle on the loc for this IFA)
  val MIN_RESOLVE_PERCENT = 145

  private[installbase] def readHC(highConfPath: URI, hcDate: LocalDate)(implicit spark: SparkSession): sql.DataFrame = {
    IPHighConfidence.read(highConfPath, hcDate)
      .select("percent_block", "census_block_id", "best_point", "ifa", "carrier", "ip_min_date")
  }

  def moveDist: UserDefinedFunction =
    udf[Double, Point.Float, Point.Float]((pt1: Point.Float, pt2: Point.Float) => LocationFilters.distanceBetweenPts(pt1, pt2))

  override def runJob(config: TransferMoversConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    val carrierLookup: Dataset[CarrierLookup] = CarrierLookup.read(config.carrierLookupPath).cache()

    val hcIn = readHC(config.highConfPath, config.hcDate)

    val hcW = hcIn.transform(carrierLookup.addIDsRunDate[Row](carrier = $"carrier", date = config.hcDate))
      .drop("household_id").alias("hcW")

    val hcL = readHC(config.highConfPath, config.hcPrevDate)
      .transform(carrierLookup.addIDsRunDate[Row](carrier = $"carrier", date = config.hcDate, Seq("consolidated_carrier")))
      .alias("hcL")

    val joined = hcW.join(hcL, $"hcW.ifa" === $"hcL.ifa" && $"hcW.sp_platform" === $"hcL.sp_platform")
    val transferMovers = joined.where($"hcW.census_block_id" =!= $"hcL.census_block_id")

    val goodResolve = transferMovers.where($"hcW.percent_block" + $"hcL.percent_block" >= MIN_RESOLVE_PERCENT)

    val temp = goodResolve
      .withColumn("move_dist", moveDist($"hcW.best_point", $"hcL.best_point"))
      .withColumn("is_close", $"move_dist" <= config.minMoveDist.getOrElse(MIN_MOVE_DIST))
      .withColumn("is_ct_match",
        substring($"hcW.census_block_id", 1, NUM_CB_DIGITS_NOT_MATCH)
          === substring($"hcL.census_block_id", 1, NUM_CB_DIGITS_NOT_MATCH))

    val result = temp.where(!$"is_close" && !$"is_ct_match")
      .selectExpr(
        "hcW.ifa as ifa",
        "hcW.sp_platform as winning_sp",
        "hcL.sp_platform as losing_sp",
        "hcW.ip_min_date as churn_date",
        "move_dist",
        "hcW.best_point as winning_best_point",
        "hcL.best_point as losing_best_point",
        "hcW.census_block_id as winning_census_block_id",
        "hcL.census_block_id as losing_census_block_id",
        "hcW.percent_block as winning_percent_block",
        "hcL.percent_block as losing_percent_block"
      )
      .withColumn("wireless_carrier", lit(""))

    val outTSL = TimeSeriesLocation
      .ofYmdDatePartitions(config.outPath)
      .build

    val dataRepartition = if (config.repartition.isDefined) {
      result.repartition(config.repartition.get)
    } else {
      result
    }

    UriDFTableRW
      .fromStr(outTSL.partition(config.hcDate))
      .writeWithHistory(dataRepartition.as[TransferMoversChurn].toDF(), SaveMode.ErrorIfExists, Seq(config))
  }
}