package com.comlinkdata.emrjobs.spark.udp.tier1

import java.sql.Date
import com.comlinkdata.largescale.metrics.CldSparkMetricsRegistry
import com.comlinkdata.largescale.schema.udp.Ip
import com.comlinkdata.largescale.schema.udp.tier0.UdpRaw
import com.comlinkdata.largescale.schema.udp.tier1.UdpDailyIpCarrier
import com.comlinkdata.largescale.udp._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SparkSession, Dataset}

object UdpDailyIpCarrierRunner extends LazyLogging {

  def createDailyIpCarrierDataset(ds: ComlinkdataDatasource, raw: Dataset[UdpRaw])(implicit spark: SparkSession): Dataset[UdpDailyIpCarrier] = {
    import spark.implicits._
    raw
      .filter(_.ip.isDefined)
      .groupByKey(row => (row.ip.get, row.date))
      .flatMapGroups { case ((ip: Ip, date: Date), mw02s: Iterator[UdpRaw]) =>
        uniqueIpCarriers(ds, ip, mw02s.toVector)
      }
  }

  def uniqueIpCarriers(ds: ComlinkdataDatasource, ip: Ip, mw02s: Vector[UdpRaw]): Vector[UdpDailyIpCarrier] = {
    import com.comlinkdata.largescale.commons.RichDate._
    mw02s.toList.filter(_.carrier.isDefined).sortBy(_.datetime) match {
      case Nil => Vector.empty[UdpDailyIpCarrier]
      case head :: Nil => Vector(UdpDailyIpCarrier.fromUdpRawAll(ip, head, 1, ds.toString))
      case head :: tail =>
        val (lastItem: UdpRaw, endingCount: Int, ipCarriers: Vector[UdpDailyIpCarrier]) =
          tail.foldLeft((head, 1, Vector.empty[UdpDailyIpCarrier])) {
            case ((current: UdpRaw, count: Int, soFar: Vector[UdpDailyIpCarrier]), next: UdpRaw) =>
              if (UdpDailyIpCarrier.isSameIpCarrier(current, next)) {
                (current, count + 1, soFar)
              } else {
                (next, 1, soFar :+ UdpDailyIpCarrier.fromUdpRawAll(ip, current, count, ds.toString))
              }
          }
        ipCarriers :+ UdpDailyIpCarrier.fromUdpRawAll(ip, lastItem, endingCount, ds.toString)
    }
  }

  def meterIpCarrierDataByDate(adjusted: Dataset[UdpDailyIpCarrier])(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Unit = {
    import com.comlinkdata.emrjobs.spark.udp.UdpMetrics.MetricUnits
    import org.apache.spark.sql.functions._
    import spark.implicits._

    val result = adjusted
      .groupBy(col("partition_date"))
      .agg(count("*") as "counts", countDistinct("Ip") as "distinct_ip_carriers")
      .as[(Date, Long, Long)]
      .collect

    result.foreach { case (dd, counts, distinctIpCarriers) =>
      val d = dd.toLocalDate
      metrics.putLiteral(d, "daily-ip-carrier-total", counts, MetricUnits.each)
      metrics.putLiteral(d, "daily-ip-carrier-distinct-total", distinctIpCarriers, MetricUnits.each)
    }
  }

}
