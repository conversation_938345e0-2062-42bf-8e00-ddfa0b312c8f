package com.comlinkdata.emrjobs.spark.udp.iplist

import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.{LocalDateRange, SparkConstants, TimeSeriesLocation}
import com.typesafe.scalalogging.LazyLogging
import java.net.URI
import java.time.LocalDate
import org.apache.spark.sql.functions.{hex, lower, max}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

object IpListCommon extends SparkConstants with LazyLogging {
  case class IpList(q: String, ip: String, carrier: String)

  private val pattern = """([0-9]{4})q([1-4])""".r
  val ipToHex: Array[String] => String = (_: Array[String]).map(s => f"${s.trim.toInt}%02x").mkString("")

  import CldFileUtils.implicits._

  def loadIpRangeLookup(ipData: Dataset[String], name: String): (Seq[String], Int) = {
    val ipList = ipData.collect.map(_.split(",")).map(ipToHex).toSeq
    val maxLength = ipList.map(_.length).max
    val minLength = ipList.map(_.length).min
    require(maxLength == minLength,
      s"$name IP list should have uniform IP length; found values ranging from $minLength to $maxLength long")
    (ipList, maxLength)
  }

  def nextQuarter(quarter: String): String = quarter match {
    case pattern(y, q) =>
      s"${y.toInt + q.toInt / 4}q${q.toInt % 4 + 1}"
    case _ =>
      scala.sys.error(s"Invalid quarter $quarter")
  }

  def getRange(quarter: String): LocalDateRange = quarter match {
    case pattern(y, q) =>
      val startDate = LocalDate.of(y.toInt, (q.toInt - 1) * 3 + 1, 1)
      val endDate = startDate.plusMonths(3).minusDays(1)
      LocalDateRange.of(startDate, endDate)
  }

  def maxQuarter(path: String)(implicit spark: SparkSession): String = {
    val quarters = CldFileUtils.newBuilder.forUri(path).build.listDirectories(path).map(_.toString).flatMap {
      case pattern(y, q) => Some((y.toInt, q.toInt))
      case _ => None
    }
    val (y, q) = quarters.max
    s"${y}q$q"
  }

  def loadValidInRange(path: String, dr: LocalDateRange, tsl: URI => TimeSeriesLocation.Builder, depth: Int = 1)(implicit spark: SparkSession): DataFrame = {
    val uri = URI.create(path)
    val cld = CldFileUtils.newBuilder.forUri(uri).build
    val partitions = (1 to depth)
      .foldLeft(Seq(uri)) { case (list, _) => list.flatMap(cld.listDirectories) }
      .flatMap(tsl(_).build.partitionsExistingBetween(dr))
      .map(_._2)
    logger.info(s"Loading partitions $partitions")
    spark.read
      .option(ReadOpts.basePath, path)
      .parquet(partitions: _*)
  }

  def loadLastIpList(path: String, quarterConfig: Option[String], dateRangeOpt: Option[LocalDateRange] = None)(implicit spark: SparkSession): (Dataset[IpList], String, LocalDateRange) = {
    import spark.implicits._
    val (lastIpList, quarter) =
      if (CldFileUtils.newBuilder.forUri(path).build.isDirectory(path)) {
        val data = spark.read.parquet(path).withColumn("ip", lower(hex('ip))).as[IpList]
        (data, quarterConfig.getOrElse(nextQuarter(data.agg(max('q)).as[String].collect.head)))
      } else {
        (spark.emptyDataset[IpList], quarterConfig.getOrElse(scala.sys.error("No quarter specified and no existing data found")))
      }
    val destPath = s"$path/q=$quarter"
    require(!CldFileUtils.newBuilder.forUri(destPath).build.exists(destPath), s"Data already exists at the output path $destPath")
    val quarterRange = getRange(quarter)
    val dr = dateRangeOpt match {
      case None => quarterRange
      case Some(dateRange)
        if !dateRange.startDate.isBefore(quarterRange.startDate)
          && !dateRange.endDate.isAfter(quarterRange.endDate) => dateRange
      case Some(dateRange) => scala.sys.error(s"Specified date range $dateRange falls outside of quarter $quarter")
    }
    require(!dr.contains(LocalDate.now), s"Quarter $quarter is incomplete")
    logger.info(s"Running for quarter $quarter, date range $dr")
    (lastIpList, destPath, dr)
  }

  def retainNewCategories(lastIpList: Dataset[IpList])(input: DataFrame): DataFrame = {
    input.join(lastIpList, Seq("ip"), "leftanti")
  }
}
