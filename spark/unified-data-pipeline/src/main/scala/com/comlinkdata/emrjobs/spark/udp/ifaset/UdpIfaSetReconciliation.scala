package com.comlinkdata.emrjobs.spark.udp.ifaset

import com.comlinkdata.largescale.schema.udp.tier2.{IfaSet, DayStat}

@SerialVersionUID(**********)
class UdpIfaSetReconciliation(mwMccMncMapping: Map[String, String]) extends Serializable {

  private val RESOLUTION_WINDOW_MAX_DAYS: Int = 21

  private val mwRawCarrierMapping: Map[String, String] = {
    Map(
      "Verizon Wireless" -> "Verizon Wireless",
      "AT&T Wireless" -> "AT&T Wireless",
      "Sprint PCS" -> "Sprint Wireless",
      "T-Mobile USA" -> "T-Mobile Wireless",
      "Wireless Data Service Provider Corporation" -> "US Cellular",
      "United States Cellular Corp." -> "US Cellular",
      "CSpire Wireless" -> "CSpire Wireless"
    )
  }

  /**
    * Reconcile final carrier name and most frequent model code if max activity date is still within 21 days
    *
    * @param ifaSet latest state of ifa set
    * @return
    */
  def applyReconciliation(ifaSet: IfaSet): IfaSet = {
    if (!isMaxDateWithinResolutionWindow(ifaSet)) return ifaSet

    val modelCode = ifaSet.iphone_disambiguated_model match {
      case Some(_) => ifaSet.iphone_disambiguated_model
      case None => findMostFrequentModelCode(ifaSet.model_code_freqs)
    }
    ifaSet.copy(
      final_carrier_name = reconcileFinalCarrierName(ifaSet.carrier_freqs, ifaSet.mcc_mnc_freqs),
      modal_model_code = modelCode
    )
  }

  /**
    * Reconcile final device type applied from a static lookup
    * @param ifaSet        latest state of ifa set
    * @param oldDeviceType set1 (daily device) ifa set history
    * @param newDeviceType set2 (daily device) device type
    * @return
    */
  def stabilizeDeviceType(ifaSet: IfaSet, oldDeviceType: Option[String], newDeviceType: Option[String]): IfaSet = {
    if (!isMaxDateWithinResolutionWindow(ifaSet)) return ifaSet
    ifaSet.copy(
      device_type = newDeviceType.orElse(oldDeviceType)
    )
  }

  /**
    * Check if max activity date is within resolution window
    *
    * @param ifaSet latest state of ifa set
    * @return
    */
  def isMaxDateWithinResolutionWindow(ifaSet: IfaSet): Boolean = {
    import com.comlinkdata.largescale.commons.RichDate._
    val minDate = ifaSet.days.head.toLocalDate
    val maxDate = ifaSet.days.last.toLocalDate

    require(maxDate.toEpochDay >= minDate.toEpochDay, "Max date must not be less than the min date in IfaSet")
    maxDate isBeforeOrEqualTo minDate.plusDays(RESOLUTION_WINDOW_MAX_DAYS)
  }

  /**
    * Try to reconcile carrier name from carrier and mcc-mnc frequencies maps
    *
    * @param carrierFrequencies map of carrier frequencies
    * @param mccMncFrequencies  map of mcc-mnc frequencies
    * @return
    */
  def reconcileFinalCarrierName(carrierFrequencies: Map[String, DayStat], mccMncFrequencies: Map[String, DayStat]): Option[String] = {
    val resolvedCarriers = carrierFrequencies.flatMap { case (k, v) => mwRawCarrierMapping.get(k).map(_ -> v) }
    val resolvedMccMnc = mccMncFrequencies.flatMap { case (k, v) => mwMccMncMapping.get(k).map(_ -> v) }
    val mostFrequentCarrierOpt = findMostFrequentNonBlankString(resolvedCarriers)
    val mostFrequentMccMncOpt = findMostFrequentNonBlankString(resolvedMccMnc)
    val mostFrequentStrings = Set(mostFrequentCarrierOpt, mostFrequentMccMncOpt).flatten
    if (mostFrequentStrings.size == 1) mostFrequentStrings.headOption
    else None
  }

  /**
    * Find most frequent non-blank string from map of frequencies
    *
    * @param frequencies map of frequencies
    * @return
    */
  def findMostFrequentNonBlankString(frequencies: Map[String, DayStat]): Option[String] = {
    val noBlankFrequencies = frequencies - ""
    if (noBlankFrequencies.isEmpty) None
    else Some(noBlankFrequencies.maxBy(e => (e._2.distinct_days, e._1))._1)
  }

  /**
    * Find most frequent model code from map of model code frequencies
    *
    * @param modelFrequencies map of model code frequencies
    * @return
    */
  def findMostFrequentModelCode(modelFrequencies: Map[String, DayStat]): Option[String] = {
    val noBlankFrequencies = modelFrequencies - ""
    if (noBlankFrequencies.isEmpty) None
    else if (noBlankFrequencies.keys == Set("iPhone")) Some("iPhone")
    else Some((noBlankFrequencies - "iPhone").maxBy(e => (e._2.distinct_days, e._1))._1)
  }
}
