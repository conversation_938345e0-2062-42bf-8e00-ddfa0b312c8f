package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import scala.reflect.runtime.universe.TypeTag
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, UriDFTableRW, LocalDateRange}
import com.comlinkdata.largescale.schema.udp.installbase.IfaToIpWithObs
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SparkSession, Dataset, DataFrame, SaveMode}

object GenericPreFilterRunner extends LazyLogging {

  def run [U,T, A <: Product : TypeTag] (
    dsToIfaObsWithMM: Map[ComlinkdataDatasource, IfaObsMMNightTime.Base],
    dsLevelTransform: Dataset[IfaToIpWithObs] => Dataset[U],
    postUnionTransform: Dataset[U] => Dataset[T],
    configHistory: A,
    config: IfaObsWithMMConfig
  )(implicit spark: SparkSession) = {

    for (day <- LocalDateRange.of(config.startDate, config.endDate)) {
      val dataOut:Dataset[T] = config.datasources.map(ds => {
        logger.info(s"Starting: $day. Running datasource: $ds")
        dsToIfaObsWithMM(ds).read(day)
      })
        .map(dsLevelTransform)
        .reduce(_ unionByName _)
        .transform(postUnionTransform)

      val dfOut:DataFrame = dataOut.repartition(config.getRepartition).toDF()

      val tslOut = TimeSeriesLocation
        .ofYmdDatePartitions(config.getOutPath)
        .build.partition(day)
      UriDFTableRW.fromStr(tslOut).writeWithHistory(
        dfOut,
        SaveMode.ErrorIfExists,
        Seq(configHistory)
      )
    }

  }
}
