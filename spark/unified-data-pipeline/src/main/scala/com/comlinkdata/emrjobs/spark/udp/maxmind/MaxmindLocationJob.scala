package com.comlinkdata.emrjobs.spark.udp.maxmind

import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner, TimeSeriesLocation}
import com.comlinkdata.largescale.schema.udp.Ip
import com.comlinkdata.largescale.schema.udp.tier1.MaxmindIp2Location
import com.comlinkdata.largescale.udp.{Cidr, ComlinkdataDatasource, IpRangeMap}
import com.typesafe.scalalogging.LazyLogging
import java.net.URI
import java.time.LocalDate
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, Dataset, SaveMode, SparkSession}
import scala.util.Try

case class MaxmindLocationConfig(
  udpDailyIpSource: URI,
  datasource: ComlinkdataDatasource,
  ipVersion: Int,
  maxmindLocationSource: URI,
  target: URI,
  dateRangeOpt: Option[LocalDateRange])

case class Location(
  iprange_min: Ip,
  iprange_max: Ip,
  latitude: Option[Double],
  longitude: Option[Double])

case class MaxmindLocationNoYmd(
  ip: Ip,
  latitude: Option[Double],
  longitude: Option[Double])

object MaxmindLocationJob extends SparkJob(MaxmindLocationRunner, forceKryoRegistration = false)

object MaxmindLocationRunner extends SparkJobRunner[MaxmindLocationConfig] with LazyLogging {
  val maxMaxmindAgeDays = 30
  val ipV4Bytes = 4
  val ipV6Bytes = 16

  import com.comlinkdata.largescale.commons.fileutils.CldFileUtils.implicits._

  override def runJob(config: MaxmindLocationConfig)(implicit spark: SparkSession): Unit = {
    require(Seq(4, 6).contains(config.ipVersion), s"Unknown ip version ${config.ipVersion}, must be 4 or 6.")
    val outTs = TimeSeriesLocation
      .ofYmdDatePartitions(config.target)
      .withPartition("ds", config.datasource)
      .withPartition("ipversion", config.ipVersion)
      .build
    val dates = config.dateRangeOpt.getOrElse {
      logger.info("No date range specified inspecting data locations for pending work.")
      val inTs = TimeSeriesLocation.ofYmdDatePartitions(config.udpDailyIpSource)
        .withPartition("ds", config.datasource)
        .build
      val startDate = Try(outTs.latestDate).toOption.map(_.plusDays(1)).getOrElse(inTs.earliestDate)
      val endDate = inTs.latestInputPartition
      if (startDate isAfter endDate) {
        logger.warn(s"Start date after end date ${startDate -> endDate}, no work to do.  Exiting.")
        return
      }
      LocalDateRange(startDate, endDate)
    }
    logger.info(s"Running daily jobs for date range $dates.")
    dates.iterator.foreach { day =>
      logger.info(s"Running day $day.")
      runDay(day, config, outTs)
      spark.catalog.clearCache()
    }
  }

  def runDay(pDate: LocalDate, config: MaxmindLocationConfig, outTs: TimeSeriesLocation)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    val inputLocation = TimeSeriesLocation.ofYmdDatePartitions(config.udpDailyIpSource)
      .withPartition("ds", config.datasource)
      .build
    val mmd0 = pDate.minusDays(maxMaxmindAgeDays) // min allowed maxmind date
    val mmd1 = pDate.minusDays(1) // max allowed maxmind date
    logger.info(s"Running with processing date of $pDate.")
    logger.info(s"Maxmind date bounds are ${(mmd0, mmd1)}.")
    val input = inputLocation.partition(pDate)
    inputLocation.cldFileUtils.allDirectoryStringsHaveDataOrThrow(input :: Nil, "input data for processing date must exist")

    def ipFilter(df: DataFrame): DataFrame = {
      val len = config.ipVersion match {
        case 4 => ipV4Bytes
        case 6 => ipV6Bytes
      }
      df.filter(length($"ip") === len)
    }

    logger.info(s"Reading ips from udpInput $input.")
    val dailyIps = spark.read
      .option(ReadOpts.basePath, config.udpDailyIpSource.toString)
      .parquet(input)
      .select("ip")
      .transform(ipFilter)
      .repartition()
      .as[Ip]
    val dest: URI = outTs.partition(pDate)
    logger.info(s"Writing output with locations to $dest.")
    dailyIps
      .transform(joinLocation(loadLocation(config.maxmindLocationSource, mmd0, mmd1)))
      .repartition(1)
      .write.mode(SaveMode.ErrorIfExists)
      .parquet(dest.toString)
  }


  def loadLocation(source: URI, d0: LocalDate, d1: LocalDate)(implicit spark: SparkSession): Dataset[Location] = {
    import spark.implicits._
    logger.info(s"Searching for most recent maxmind location table between ${d0 -> d1} at $source.")
    val partitions: String = TimeSeriesLocation.ofDatePartitions(source).build
      .partitionsExistingBetween(LocalDateRange.of(d0, d1))
      .maxBy(_._1.toEpochDay)._2
    logger.info(s"Reading maxmind location table from $partitions.")
    MaxmindIp2Location.readFromSrcsDf(source, partitions)
      .transform(Cidr.convertCidrToIpRange("cidr")).as[Location]
  }

  def joinLocation(locations: Dataset[Location])(ips: Dataset[Ip])(implicit spark: SparkSession): Dataset[MaxmindLocationNoYmd] = {
    import spark.implicits._
    logger.info("Collecting location data to driver")
    val localLookup = locations
      .map(row => IpRangeMap.Element(row.iprange_min, row.iprange_max, (row.latitude, row.longitude)))
      .collect
    logger.info("Joining local location data using RangeMap in mapPartitions")
    ips.mapPartitions { partition =>
      // within each partition, create a lookup table
      val lookup = IpRangeMap.newBuilder[(Option[Double], Option[Double])].putAll(localLookup).build
      partition.flatMap { ip =>
        Option(lookup.get(ip)).map { case (latitude, longitude) =>
          MaxmindLocationNoYmd(ip, latitude, longitude)
        }
      }
    }
  }
}
