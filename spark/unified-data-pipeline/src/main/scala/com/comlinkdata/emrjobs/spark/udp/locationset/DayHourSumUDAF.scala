package com.comlinkdata.emrjobs.spark.udp.locationset

import org.apache.spark.sql.Row
import org.apache.spark.sql.expressions.{MutableAggregationBuffer, UserDefinedAggregateFunction}
import org.apache.spark.sql.types.{ArrayType, IntegerType, StringType, StructField, StructType}

import scala.collection.mutable

/**
  * User defined aggregation function.
  * GroupBy on Lat and Long Ints & aggregate into a struct of arrays of unique ifa's seen per 24 hours.
  * Struct has 3 arrays of length 24 one for all ifa's, cell only ifa's, and wifi ifa's
  */
class DayHourSumUDAF extends UserDefinedAggregateFunction {
  override def inputSchema: StructType = StructType(
    StructField("day_hour", IntegerType) ::
      StructField("connection_type", StringType) :: Nil
  )

  override def bufferSchema: StructType = StructType(
    StructField("day_hours", ArrayType(IntegerType)) ::
      Struct<PERSON>ield("day_hours_cell", ArrayType(IntegerType)) ::
      StructField("day_hours_wifi", ArrayType(IntegerType)) :: Nil
  )

  //Output type of agg function.
  override def dataType: StructType = StructType(
    StructField("day_hours", ArrayType(IntegerType)) ::
      StructField("day_hours_cell", ArrayType(IntegerType)) ::
      StructField("day_hours_wifi", ArrayType(IntegerType)) :: Nil
  )

  override def deterministic: Boolean = true

  override def initialize(buffer: MutableAggregationBuffer): Unit = {
    buffer(0) = Array.ofDim[Int]( 24)
    buffer(1) = Array.ofDim[Int]( 24)
    buffer(2) = Array.ofDim[Int]( 24)
  }

  override def update(buffer: MutableAggregationBuffer, input: Row): Unit = {
    if (input.get(0) != null && input.getAs[Int](0) >= 0) {
      val bufferArray = buffer.getAs[mutable.WrappedArray[Int]](0)
      val rowInputInt = input.getAs[Int](0)

      bufferArray(rowInputInt) += 1
      buffer(0) = bufferArray
      val conn_type = input.getString(1)
      if(conn_type != null) {
        if (conn_type.equalsIgnoreCase("WIFI")) {
          val bufferArrayWifi = buffer.getAs[mutable.WrappedArray[Int]](2)
          bufferArrayWifi(rowInputInt) += 1
          buffer(2) = bufferArrayWifi
        } else if (conn_type.equalsIgnoreCase("CELLULAR")){
          val bufferArrayCell = buffer.getAs[mutable.WrappedArray[Int]](1)
          bufferArrayCell(rowInputInt) += 1
          buffer(1) = bufferArrayCell
        }
      }
    }
  }

  override def merge(buffer1: MutableAggregationBuffer, buffer2: Row): Unit = {
    for(j <- 0 to 2) {
      val bufferArray = buffer1.getAs[mutable.WrappedArray[Int]](j)
      val rowArray = buffer2.getAs[mutable.WrappedArray[Int]](j)
      for (i <- bufferArray.indices) {
        bufferArray(i) += rowArray(i)
      }
      buffer1(j) = bufferArray
    }
  }

  override def evaluate(buffer: Row): (Array[Int], Array[Int], Array[Int]) = {
    (buffer.getAs[Array[Int]](0),
      buffer.getAs[Array[Int]](1),
      buffer.getAs[Array[Int]](2))
  }
}
