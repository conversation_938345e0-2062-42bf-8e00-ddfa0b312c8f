package com.comlinkdata.emrjobs.spark.udp.ifaipagg

import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.udp.tier1.UdpTier1DirectoryResolver
import com.comlinkdata.largescale.schema.udp.tier2.UdpIfaIpAgg
import com.comlinkdata.largescale.schema.udp.{tier2, Ifa, Ip}
import com.comlinkdata.largescale.udp._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import org.apache.spark.sql.{SparkSession, Dataset}

import java.net.URI
import java.sql.Timestamp
import java.time.LocalDate

object UdpIfaIpAggSchema{

  case class SkinnyIfa(
    ifa: Ifa,
    observations: Array[SkinnyIfaObs]
  )
  object SkinnyIfa extends Utils.reflection.ColumnNames[SkinnyIfa]

  case class SkinnyIfaObs(
    t_local: Option[Timestamp],
    ip: Option[Ip]
  )

  case class FlatIfaObs(
    ifa: Ifa,
    ip: Ip,
    t_local: Timestamp
  )

  object FlatIfaObs extends Utils.reflection.ColumnNames[FlatIfaObs]

  case class LittleMaxmind(ip: Ip, connection_type: String, carrier: String)

  object LittleMaxmind extends Utils.reflection.ColumnNames[LittleMaxmind]

  case class UdpIfaIpAggConfig(
    udpTier1Loc: URI,
    maxmindLoc: URI,
    datasources: List[ComlinkdataDatasource],
    dateRangeOpt: Option[LocalDateRange],
    ifaIpAggDest: URI,
    filesPerPartition: Int
  )
}


object UdpIfaIpAggJob extends SparkJob(UdpIfaIpAggRunner) {
  override def configureSpark(builder: SparkSession.Builder) = super.configureSpark(builder)
    .config("spark.kryoserializer.buffer.max", "256m")
}

object UdpIfaIpAggRunner extends SparkJobRunner[UdpIfaIpAggSchema.UdpIfaIpAggConfig] with LazyLogging {
  import UdpIfaIpAggSchema._
  import com.comlinkdata.largescale.commons.RichDate._

  override def runJob(config: Config)(implicit spark: SparkSession): Unit = {
    val t1Resolver = UdpTier1DirectoryResolver.resolver(config.udpTier1Loc)

    config.datasources.foreach { ds =>
      val dr = config.dateRangeOpt getOrElse {
        val endDate = TimeSeriesLocation
          .ofYmdDatePartitions(
            source = t1Resolver.dailyIfaObservationLocation,
            firstDate = Some(ds.firstDate),
            lastDate = ds.lastDate
          )
          .withPartition(ds)
          .build
          .latestInputPartition

        val startDate = TimeSeriesLocation.ofYmdDatePartitions(config.ifaIpAggDest)
          .withPartition(ds)
          .build
          .latestDate.plusDays(1)
        LocalDateRange(startDate, endDate)

      }

      if (dr.nonEmpty) {
        logger.info(s"Running for ds=$ds dateRange=$dr.")
        dr.foreach{day =>
          logger.info(s"Starting ds-day ${(ds,day)}.")
          val (_, elapsed) = Utils.timer(runJobForDs(config, day, ds))
          logger.info(s"Completed ds-day${(ds,day)} in $elapsed.")
        }
        spark.sqlContext.clearCache()
      } else {
        logger.warn(s"Skipping ds=$ds because there are no dates to process between $dr.")
      }
    }
  }


  def runJobForDs(config: Config, day: LocalDate, ds: ComlinkdataDatasource)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val t1Resolver = UdpTier1DirectoryResolver.resolver(config.udpTier1Loc)

    val dateRange = LocalDateRange.of(day)

    val destTs = TimeSeriesLocation.ofYmdDatePartitions(config.ifaIpAggDest)
      .withPartition(ds)
      .build

    val dest = destTs.validOutputPartitionOrThrow(day)

    val tsIn = TimeSeriesLocation
      .ofYmdDatePartitions(
        source = t1Resolver.dailyIfaObservationLocation,
        firstDate = Some(ds.firstDate),
        lastDate = ds.lastDate
      )
      .withPartition(ds)
      .build
      .validInputPartitionsOrThrow(dateRange)

    if (tsIn.isEmpty) {
      logger.warn(s"Skipping processing: no valid input for datasource $ds on day $day.")
      return
    }

    logger.info(s"Reading input partitions $tsIn.")
    val input: Dataset[FlatIfaObs] = spark.read.parquet(tsIn: _*)
      .select(SkinnyIfa.cols: _*)
      .as[SkinnyIfa]
      .flatMap { r =>
        r.observations
          .filter(obs => obs.ip.isDefined && obs.t_local.isDefined)
          .map(obs =>
            FlatIfaObs(r.ifa, obs.ip.get, obs.t_local.get)
          )
      }

    val mmTsBuilder = TimeSeriesLocation.ofYmdDatePartitions(config.maxmindLoc)
      .withPartition(ds)

    val mmIn =
      mmTsBuilder
        .withPartition("ipversion", 4)
        .withFirstDate(ComlinkdataDatasource.Maxmind.ip4.firstDate)
        .build
        .validInputPartitionsOrThrow(dateRange) ++
        mmTsBuilder
          .withPartition("ipversion", 6)
          .withFirstDate(ComlinkdataDatasource.Maxmind.ip6.firstDate)
          .build
          .validInputPartitionsOrThrow(dateRange)

    require(mmIn.nonEmpty, s"no valid maxmind data for dates $dateRange.")

    logger.info(s"Reading maxmind partitions $mmIn.")
    val mmInput: Dataset[LittleMaxmind] = spark.read.parquet(mmIn: _*)
      .select(LittleMaxmind.cols: _*)
      .as[LittleMaxmind]

    //TODO consider making night_ind "if observed at night on this date" - so that table is distinct on ifa-ip
    val joined: Dataset[UdpIfaIpAgg] = input.joinWith(mmInput, input("ip") === mmInput("ip"), JoinType.inner)
      .map { case (in, mm) =>
        tier2.UdpIfaIpAgg(
          ifa = in.ifa,
          ip = in.ip,
          connection_type = mm.connection_type,
          carrier = mm.carrier,
          night_ind = UdpUtils.isNight(in.t_local),
          local_date = in.t_local.getUtcDate,
          obs_count = 1
        )
      }.groupBy("ifa", "ip", "connection_type", "carrier", "night_ind", "local_date")
      .agg(
        sum("obs_count").cast(IntegerType) as "obs_count"
      )
      .select(UdpIfaIpAgg.cols: _*)
      .as[UdpIfaIpAgg]

    logger.info(s"Writing results to $dest.")
    joined.coalesce(config.filesPerPartition).write.parquet(dest)
  }
}
