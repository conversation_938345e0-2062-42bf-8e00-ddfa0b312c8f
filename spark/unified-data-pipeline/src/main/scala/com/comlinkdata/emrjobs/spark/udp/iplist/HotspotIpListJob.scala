package com.comlinkdata.emrjobs.spark.udp.iplist

import com.comlinkdata.emrjobs.spark.udp.iplist.IpListCommon._
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils.implicits._
import com.typesafe.scalalogging.LazyLogging
import java.net.URI
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import org.apache.spark.sql.{SaveMode, SparkSession}

case class HotspotIpListConfig(
  ifaIpAggPath: String,
  ipObsPath: String,
  maxmindPath: String,
  tapadPath: URI,
  maxmindLookup: String,
  tmoRangeLookup: String,
  distinctIfaThreshold: Int,
  distanceThreshold: Float,
  homeCarrierThreshold: Float,
  ipListPath: String,
  quarter: Option[String],
  dateRange: Option[LocalDateRange]
)

object HotspotIpListJob extends SparkJob(HotspotIpListRunner)

object HotspotIpListRunner extends SparkJobRunner[HotspotIpListConfig] with SparkConstants with LazyLogging {
  override def runJob(config: HotspotIpListConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    require(CldFileUtils.newBuilder.forUri(config.ifaIpAggPath).build.isDirectory(config.ifaIpAggPath))
    require(CldFileUtils.newBuilder.forUri(config.ipObsPath).build.isDirectory(config.ipObsPath))
    require(CldFileUtils.newBuilder.forUri(config.maxmindPath).build.isDirectory(config.maxmindPath))
    require(CldFileUtils.newBuilder.forUri(config.tapadPath).build.isDirectory(config.tapadPath))
    require(CldFileUtils.newBuilder.forUri(config.maxmindLookup).build.exists(config.maxmindLookup))
    require(CldFileUtils.newBuilder.forUri(config.tmoRangeLookup).build.exists(config.tmoRangeLookup))
    val (lastIpList, destPath, dr) = loadLastIpList(config.ipListPath, config.quarter, config.dateRange)
    val hdfsTempPath = "hdfs:/ip-lists/"
    val ifaIpAgg = loadValidInRange(config.ifaIpAggPath, dr, TimeSeriesLocation.ofYmdDatePartitions(_))
      .withColumn("ip", lower(hex('ip)))
      .withColumn("carrier", lower('carrier))
    val (tmoIpList, tmoIpLength) = loadIpRangeLookup(spark.read.text(config.tmoRangeLookup).as[String], "TMO Boost")
    val tmoHotspots = ifaIpAgg
      .filter(length('ip) === 8) // it's already a hex string
      .filter(('carrier.like("%altice%") || 'carrier.like("%optimum%"))
        && 'connection_type === "Cable/DSL"
        && 'ip.substr(1, tmoIpLength).isin(tmoIpList: _*))
      .join(lastIpList, Seq("ip"), "leftanti")
      .select(unhex('ip).as("ip"), 'carrier)
    tmoHotspots
      .write.mode(SaveMode.Overwrite)
      .parquet(hdfsTempPath)
    val xfinityHotspots = loadValidInRange(config.maxmindPath, dr, TimeSeriesLocation.ofYmdDatePartitions(_), 2)
      .withColumn("ip", lower(hex('ip)))
      .withColumn("carrier", lower('carrier))
      .filter('carrier === "comcast cable"
        && 'organization === "XFINITY WiFi")
      .join(lastIpList, Seq("ip"), "leftanti")
      .select(unhex('ip).as("ip"), 'carrier)
    xfinityHotspots
      .write.mode(SaveMode.Append)
      .parquet(hdfsTempPath)
    val dist = pow(sin(radians(($"bl.lat" - $"tr.lat") / 2)), 2) +
      pow(sin(radians(($"bl.lng" - $"tr.lng") / 2)), 2) * cos(radians($"tr.lat")) * cos(radians($"bl.lat"))
    val ipObs = loadValidInRange(config.ipObsPath, dr, TimeSeriesLocation.ofYmdDatePartitions(_))
      .withColumn("gps", 'location_stats("GPS"))
      .select(lower(hex('ip)).as("ip"), $"gps.envelope.bl".as("bl"), $"gps.envelope.tr".as("tr"))
      .filter(lit(2 * 3961) * asin(sqrt(dist)) >= config.distanceThreshold)
    val tapadTsl = TimeSeriesLocation.ofDatePartitions(config.tapadPath).build
    val tapad = spark.read.parquet(tapadTsl.partition(tapadTsl.latestInputPartition))
      .select(
        'ifa,
        substring_index('tp_ip, ".", 3).as("ip_1_3"),
        substring_index('tp_ip, ".", -1).cast(IntegerType).as("ip_4"))
    val maxmind = spark.read.parquet(config.maxmindLookup)
      .select(
        lower('isp).as("isp"),
        concat_ws(".", 'ip_part_1, 'ip_part_2, 'ip_part_3).as("ip_1_3"),
        'ip_part_4_start,
        'ip_part_4_end)
    val tapadMaxmind = tapad
      .join(maxmind, "ip_1_3")
      .filter('ip_4.between('ip_part_4_start, 'ip_part_4_end))
    val hotspots = ifaIpAgg
      .join(ipObs, "ip")
      .join(tapadMaxmind, "ifa")
      .filter('carrier.isin("comcast cable", "spectrum")
        && 'connection_type === "Cable/DSL")
      .groupBy('ip, 'carrier)
      .agg(
        countDistinct('ifa).as("distinct_ifas"),
        (sum(when('carrier === 'isp, 100).otherwise(0)) / count('isp)).as("home_carrier"))
      .filter('distinct_ifas >= config.distinctIfaThreshold
        && 'home_carrier >= config.homeCarrierThreshold)
      .join(lastIpList, Seq("ip"), "leftanti")
      .select(unhex('ip).as("ip"), 'carrier)
    hotspots
      .write.mode(SaveMode.Append)
      .parquet(hdfsTempPath)
    spark.read.parquet(hdfsTempPath)
      .distinct
      .repartition(1)
      .write.mode(SaveMode.ErrorIfExists)
      .parquet(destPath)
  }
}
