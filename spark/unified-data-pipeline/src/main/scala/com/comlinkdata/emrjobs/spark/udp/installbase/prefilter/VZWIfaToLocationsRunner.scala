package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.emrjobs.spark.udp.installbase.LocationFilters
import com.comlinkdata.emrjobs.spark.udp.installbase.LocationFilters.{IFA_PER_IP_CUTOFF, locCountsFromLocTs, locWithHourFromObs}
import com.comlinkdata.largescale.commons.{SparkJobRunner, SparkConstants, SparkJob}
import com.comlinkdata.largescale.schema.udp.installbase.{IfaToLocCountsExploded, IfaToLocCounts, IfaToIpWithObs}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SparkSession, Dataset}

import java.net.URI
import java.time.LocalDate

case class VZWIfaToLocationsConfig(
  ifaObsPath: URI,
  maxmindPath: URI,
  carrierLookupPath: URI,
  carrierLookupFwPath: URI,
  ipv6TruncationLookupPath: URI,
  hourWeightLookupPath: URI,
  dailyIpCarrierPath: Option[URI],
  verizonFwIpLocation: URI,
  datasources: Seq[ComlinkdataDatasource],
  startDate: LocalDate,
  endDate: LocalDate,
  repartition: Option[Int],
  ifaToLocationOutPath: URI
) extends IfaObsWithMMConfig {
  override def getRepartition: Int = {
    repartition.getOrElse(1)
  }

  override def getOutPath: URI = ifaToLocationOutPath
}

object VZWIfaToLocationsJob extends SparkJob(VZWIfaToLocationsRunner)

object VZWIfaToLocationsRunner
  extends SparkJobRunner[VZWIfaToLocationsConfig]
  with SparkConstants
  with LazyLogging {

  override def runJob(config: VZWIfaToLocationsConfig)(implicit spark: SparkSession): Unit = {

    GenericPreFilterRunner.run(
      IfaObsMMNightTime.dsToVZWIfaObsWithObs(config, config.verizonFwIpLocation),
      computeLocationSingleDS,
      (ds: Dataset[IfaToLocationNoStats]) => ds.transform(aggLocation).transform(explodeCounts),
      config,
      config
    )
  }

  def computeLocationSingleDS(ipToIfa: Dataset[IfaToIpWithObs])(implicit spark: SparkSession): Dataset[IfaToLocationNoStats] = {
    import spark.implicits._

    val approxCountIfa = ipToIfa
      .filter($"consolidated_id".isNotNull) // only run locations for tracked carriers
      .select(
        $"ifa",
        $"ip",
        $"obs_count",
        $"night_obs_count",
        $"observations",
        approx_count_distinct("ifa").over(Window.partitionBy($"ip")) as "approx_ifas")
      .where($"approx_ifas" < IFA_PER_IP_CUTOFF)

    val locFilter = approxCountIfa
      .select($"ifa",
        $"ip",
        $"obs_count",
        $"night_obs_count",
        explode($"observations") as "obs")
      .transform(LocationFilters.locationFilters($"ifa", $"ip", $"obs_count", $"night_obs_count"))

    val locAgg = locFilter.groupBy("ifa", "obs_count", "night_obs_count")
      .agg(
        collect_set("ip") as "ips",
        flatten(collect_list($"observations")) as "obs")
      .withColumn("locations", locWithHourFromObs($"obs")).drop("obs")
      .withColumn("hours_seen", array_distinct(transform($"locations.ts", x => hour(x))))

    val loc = locAgg
      .withColumn("ip_count", size($"ips"))
      .select(
        $"ifa",
        $"obs_count",
        $"night_obs_count",
        $"ips",
        $"ip_count",
        $"hours_seen",
        $"locations")
      .as[IfaToLocationNoStats]

    loc
  }

  def aggLocation(allDsIn: Dataset[IfaToLocationNoStats])(implicit spark: SparkSession): Dataset[IfaToLocCounts] = {
    import spark.implicits._

    allDsIn
      .groupBy("ifa")
      .agg(
        sum($"obs_count") as "obs_count",
        sum($"night_obs_count") as "night_obs_count",
        array_distinct(flatten(collect_set("hours_seen"))) as "hours_seen",
        array_distinct(flatten(collect_set("ips"))) as "ips",
        flatten(collect_list("locations")) as "locations")
      .withColumn("ip_count", size($"ips"))
      .withColumn("loc_counts", locCountsFromLocTs($"locations"))
      .as[IfaToLocCounts]
  }

  def explodeCounts(raw: Dataset[IfaToLocCounts])(implicit spark: SparkSession): Dataset[IfaToLocCountsExploded] = {
    import spark.implicits._
    raw
      .select(IfaToLocCounts.cols ++ Seq(explode($"loc_counts")): _*)
      .withColumnRenamed("key", "point")
      .withColumnRenamed("value", "point_obs")
      .select(IfaToLocCountsExploded.cols: _*)
      .as[IfaToLocCountsExploded]
  }

  def run(ifaToIpAllDsWithObs: Seq[Option[Dataset[IfaToIpWithObs]]])(implicit spark: SparkSession): Dataset[IfaToLocCountsExploded] = {
    val union = ifaToIpAllDsWithObs
      .filter(_.isDefined).map(_.get) // remove empty or undefined datasets
      .map(computeLocationSingleDS) // tranform - major work here
      .reduce(_ unionByName _) // aggregate all datasets together and remove datasource

    val locs = union
      .transform(aggLocation) // further aggregation
      .transform(explodeCounts)

    locs
  }


}
