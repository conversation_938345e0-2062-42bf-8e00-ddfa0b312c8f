package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.emrjobs.spark.udp.installbase.IPSequenceSetRunner.{residentialFilter, ifaWithIpCandidates}
import com.comlinkdata.emrjobs.spark.udp.installbase.UdpHouseholdSetRunner.newHouseholdId
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.udp.installbase.{IPHighConfidenceNoLoc, IPHighConfidence}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SparkSession, Dataset, SaveMode}
import org.apache.spark.sql.functions._
import org.apache.sedona.core.serde.SedonaKryoRegistrator
import org.apache.sedona.sql.utils.SedonaSQLRegistrator
import org.apache.spark.serializer.KryoSerializer
import org.apache.spark.sql.expressions.Window

import java.net.URI
import java.time.{Duration, LocalDate}

/**
  * IPHighConfidenceSet Config
  * @param ifaToIpPath Input S3: Output from PreFilterJob
  * @param outputPath Output S3 location
  * @param startDate start date
  * @param daysToLookBack days to look back for history of ip
  * @param daysToLookAhead days to look forward for future of ip
  * @param ipLocsPath Input S3: Output from PreFilterJob
  * @param polygonsLocation GeoJSON polygon files
  * @param repartition Optional repartition strategy per year month day partition
  */
case class IPHighConfidenceSetConfig(
  ifaToIpPath: URI,
  outputPath: URI,
  startDate: LocalDate,
  daysToLookBack: Int,
  daysToLookAhead: Int,
  ipLocsPath: URI,
  polygonsLocation: URI,
  repartition: Option[Int]
)

object IPHighConfidenceSet extends SparkJob(IPHighConfidenceSetRunner) {
  /**
    * Register sedona before calling run.  This cannot happen in "configure spark" because it only
    * provides a SparkBuilder at that point
    */
  override def run(config: IPHighConfidenceSetConfig): Duration = {
    SedonaSQLRegistrator.registerAll(spark)
    super.run(config)
  }

  /**
    * Need for sedona
    * @param builder config builder
    * @return builder with new parameters
    */
  override def configureSpark(builder: SparkSession.Builder): SparkSession.Builder = {
    builder
      .config("spark.kryo.registrator", classOf[SedonaKryoRegistrator].getName)
      .config("spark.serializer", classOf[KryoSerializer].getName)
  }
}

object IPHighConfidenceSetRunner
  extends SparkJobRunner[IPHighConfidenceSetConfig]
    with SparkConstants
    with LazyLogging {

  private val MAX_UNIQUE_IP = 50
  private val MAX_UNIQUE_IFA = 50
  private val MIN_HOME_HOUR_DATES = 4

  override def runJob(config: IPHighConfidenceSetConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val tslIfaIp = TimeSeriesLocation
      .ofYmdDatePartitions(config.ifaToIpPath)
      .build

    logger.info(s"Running High Confidence for date: $config.startDate")

    val ldrIfa = LocalDateRange.of(config.startDate.minusDays(config.daysToLookBack), config.startDate.minusDays(1))
    val lastDay = config.startDate.plusDays(config.daysToLookAhead)
    val ldrIfaForward = LocalDateRange.of(config.startDate, lastDay)

    logger.info(s"Look back range: $ldrIfa")
    logger.info(s"Look forward range: $ldrIfaForward")

    if(!tslIfaIp.exists(lastDay)) {
      logger.warn("Ifa to Ip is missing last day in look forward days. We haven't gotten to the next Q yet. Not running.")
      return
    }

    if(!ldrIfa.forall(tslIfaIp.exists)) {
      throw new IllegalStateException("Ifa Obs is missing look back days.")
    }

    if(!ldrIfaForward.forall(tslIfaIp.exists)) {
      throw new IllegalStateException("Ifa Obs is missing look forward days.")
    }

    val tslIfa = tslIfaIp.partitions(ldrIfa)
    val tslIfaFuture = tslIfaIp.partitions(ldrIfaForward)

    val dsIfa: Dataset[IfaToIpYMD] = spark.read
      .option(ReadOpts.basePath, config.ifaToIpPath.toString)
      .parquet(tslIfa:_*)
      .as[IfaToIpYMD]

    val dsIfaFuture: Dataset[IfaToIpYMD] = spark.read
      .option(ReadOpts.basePath, config.ifaToIpPath.toString)
      .parquet(tslIfaFuture:_*)
      .as[IfaToIpYMD]

    val highConfOutNoLocs: Dataset[IPHighConfidenceNoLoc] = process(dsIfa, dsIfaFuture, config.startDate)
    val hcDataset: Dataset[IPHighConfidence] = processWithLoc(
      highConfOutNoLocs,
      config.startDate,
      config.daysToLookBack,
      config.daysToLookAhead,
      config.ipLocsPath,
      config.polygonsLocation
    )

    val dataRepartition = if(config.repartition.isDefined) {
      hcDataset.repartition(config.repartition.get)
    } else {
      hcDataset
    }

    val tslOutSet = TimeSeriesLocation
      .ofYmdDatePartitions(config.outputPath)
      .build

    UriDFTableRW.fromStr(tslOutSet.partition(config.startDate))
      .writeWithHistory(
        dataRepartition.drop("year", "month", "day").toDF(),
        SaveMode.ErrorIfExists,
        Seq(config)
      )
  }

  def processWithLoc(
    highConfOutNoLocs: Dataset[IPHighConfidenceNoLoc],
    startDate: LocalDate,
    daysToLookBack: Int,
    daysToLookAhead: Int,
    ipLocsPath: URI,
    polygonsLocation: URI
  )(implicit spark: SparkSession): Dataset[IPHighConfidence] = {
    import spark.implicits._

    val startDateLocs = startDate.minusDays(daysToLookBack)
    val endDateLocs = startDate.plusDays(daysToLookAhead)

    val dateRangeLocs = LocalDateRange.of(
      startDateInclusive = startDateLocs,
      endDateInclusive = endDateLocs
    )

    val withLocsDF = HouseholdToLocsJobRunner
      .attachLocations(ipLocsPath, polygonsLocation, highConfOutNoLocs.toDF(), dateRangeLocs)

    if(withLocsDF.isEmpty) {
      throw new IllegalArgumentException("Missing Ip to Location dates")
    }

    withLocsDF.get.toDF().as[IPHighConfidence]
  }
  /**
    * Process a single dataset of high confidence
    * @param dsIfa prefilter output
    * @param dsIfaFuture prefilter output
    * @param startDate high confidence day
    * @param spark spark session
    * @return high confidence dataset
    */
  def process(
    dsIfa:Dataset[IfaToIpYMD],
    dsIfaFuture: Dataset[IfaToIpYMD],
    startDate: LocalDate
  )(implicit spark: SparkSession): Dataset[IPHighConfidenceNoLoc] = {
    import spark.implicits._

    val current = dsIfa
      .transform(residentialFilter(MAX_UNIQUE_IP, MAX_UNIQUE_IFA, MIN_HOME_HOUR_DATES))
      .transform(ifaWithIpCandidates)
      .transform(highConfidence(dsIfaFuture))
      .withColumn("case_method", lit(0))
      .withColumn("global_ip_min_date", min($"ip_min_date").over(Window.partitionBy($"ip")))
      .drop("date_count", "ip_set")

    val joined = current
      .withColumn("household_id", newHouseholdId($"ip", $"global_ip_min_date"))

    // add the columns to match schema
    joined
      .withColumn("year", lit(startDate.getYearString))
      .withColumn("month", lit(startDate.getMonthValueString))
      .withColumn("day", lit(startDate.getDayOfMonthString))
      .as[IPHighConfidenceNoLoc]
  }

  /**
    * Given a long set of past history, preserve IFA/IP key that exist in the future dataset. Throwaway others.
    * @param dsIfaFuture start date to startdate + config.daysToLookAhead from input data
    * @param dsIfaWithIpCandidates Ifas with best ranked IPs from history
    * @return High confidence ifa ips that should be considered best in class for a certain time range
    */
  def highConfidence(dsIfaFuture:Dataset[IfaToIpYMD])(dsIfaWithIpCandidates:Dataset[IfaWithIpCandidate])(implicit spark: SparkSession): Dataset[IfaWithIpCandidate] = {
    import spark.implicits._
    val uniqueFutureIfaIp = dsIfaFuture
      .select("ifa", "ip")
      .distinct()

    dsIfaWithIpCandidates
      .as("candidates")
      .join(uniqueFutureIfaIp, Seq("ifa", "ip"), "inner")
      .selectExpr("candidates.*")
      .as[IfaWithIpCandidate]
  }
}
