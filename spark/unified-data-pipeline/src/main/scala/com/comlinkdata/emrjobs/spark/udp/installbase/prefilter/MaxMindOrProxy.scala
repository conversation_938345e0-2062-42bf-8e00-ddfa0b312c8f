package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.emrjobs.spark.udp.installbase.prefilter.IfaDayConnType.IfaDayConnType
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.apache.spark.sql.{SparkSession, Row, DataFrame}
import com.comlinkdata.largescale.schema.broadband.lookup.ConsolidatedCarrier.implicits.CarrierLookupOps

import java.net.URI
import java.time.LocalDate

private object IfaDayConnType extends Enumeration {
  type IfaDayConnType = Value
  val Wifi, VZW, TMO, WifiCell = Value
}

object MaxMindOrProxy {
  private val MAXMIND_UDP_1ST_DATE = LocalDate.of(2020, 4, 1)

  class Base(
    ds: ComlinkdataDatasource,
    maxmindPath: URI,
    carrierLookupPath: URI,
    carrierLookupFwPath: URI,
    dailyIpCarrierPath: Option[URI],
    verizonFwIpLocation: Option[URI],
    connType: IfaDayConnType
  )(implicit spark: SparkSession) {

    private lazy val vzwIpBlocksCache = VerizonFwIp.read(verizonFwIpLocation.get).cache()

    def getDs: ComlinkdataDatasource = {
      ds
    }

    def read(runDate: LocalDate): DataFrame = {
      import spark.implicits._

      val isMMDay = isValidMaxmindDay(runDate)

      if (!isMMDay) {
        if (ds != ComlinkdataDatasource.mw) {
          throw new IllegalArgumentException(s"Carrier and platform backfill pre $MAXMIND_UDP_1ST_DATE is only support for mw datasource.")
        }
        if (dailyIpCarrierPath.isEmpty) {
          throw new IllegalArgumentException(s"Must provide dailyIpCarrierPath before $MAXMIND_UDP_1ST_DATE.")
        }
      }

      val mmOrProxy: DataFrame =
        (isMMDay, connType) match {
          case (true, IfaDayConnType.VZW) => new MMReader.MaxMindVZW(ds, maxmindPath, vzwIpBlocksCache).read(runDate)
          case (true, IfaDayConnType.TMO) => new MMReader.MaxMindTMO(ds, maxmindPath).read(runDate)
          case (true, IfaDayConnType.Wifi) => new MMReader.MaxMindWifi(ds, maxmindPath).read(runDate)
          case (true, IfaDayConnType.WifiCell) => new MMReader.MaxMindAll(ds, maxmindPath).read(runDate)
          case (false, IfaDayConnType.VZW) => new DailyIpCarrierAsMM.DailyIpCarrierVZW(dailyIpCarrierPath.get, vzwIpBlocksCache).read(runDate)
          case (false, IfaDayConnType.TMO) => new DailyIpCarrierAsMM.DailyIpCarrierTMO(dailyIpCarrierPath.get).read(runDate)
          case (false, IfaDayConnType.Wifi) => new DailyIpCarrierAsMM.DailyIpCarrierWifi(dailyIpCarrierPath.get).read(runDate)
          case (false, IfaDayConnType.WifiCell) => new DailyIpCarrierAsMM.DailyIpCarrierWifiCell(dailyIpCarrierPath.get).read(runDate)
          case (a, b) => throw  new IllegalArgumentException(s"Unknown state: $a, $b")
        }

      val carrierLookupFw = CarrierLookup.read(carrierLookupFwPath)
      val carrierLookup = CarrierLookup.read(carrierLookupPath).unionByName(carrierLookupFw)

      mmOrProxy
        .transform(carrierLookup.addIDsRunDate[Row](carrier = $"carrier", date = runDate))
        .select(
          $"ip",
          $"carrier",
          $"consolidated_id",
          $"connection_type",
          $"sp_platform",
          $"organization",
          $"autonomous_system_number"
        )
    }
  }

  def isValidMaxmindDay(day: LocalDate): Boolean = {
    MAXMIND_UDP_1ST_DATE.isBefore(day) || MAXMIND_UDP_1ST_DATE.isEqual(day)
  }

  class Wifi (
    ds: ComlinkdataDatasource,
    maxmindPath: URI,
    carrierLookupPath: URI,
    carrierLookupFwPath: URI,
    dailyIpCarrierPath: Option[URI])(implicit spark: SparkSession)
    extends Base (
      ds,
      maxmindPath,
      carrierLookupPath,
      carrierLookupFwPath,
      dailyIpCarrierPath,
      None,
      IfaDayConnType.Wifi
    )

  class VZW (
    ds: ComlinkdataDatasource,
    maxmindPath: URI,
    carrierLookupPath: URI,
    carrierLookupFwPath: URI,
    dailyIpCarrierPath: Option[URI],
    verizonFwIpLocation: URI
  )(implicit spark: SparkSession)
    extends Base (
      ds,
      maxmindPath,
      carrierLookupPath,
      carrierLookupFwPath,
      dailyIpCarrierPath,
      Some(verizonFwIpLocation),
      IfaDayConnType.VZW
    )

  class TMO(
    ds: ComlinkdataDatasource,
    maxmindPath: URI,
    carrierLookupPath: URI,
    carrierLookupFwPath: URI,
    dailyIpCarrierPath: Option[URI],
  )(implicit spark: SparkSession)
    extends Base(
      ds,
      maxmindPath,
      carrierLookupPath,
      carrierLookupFwPath,
      dailyIpCarrierPath,
      None,
      IfaDayConnType.TMO
    )

  class CellOnly (
    ds: ComlinkdataDatasource,
    maxmindPath: URI,
    carrierLookupPath: URI,
    carrierLookupFwPath: URI,
    dailyIpCarrierPath: Option[URI])(implicit spark: SparkSession)
    extends Base (
      ds,
      maxmindPath,
      carrierLookupPath,
      carrierLookupFwPath,
      dailyIpCarrierPath,
      None,
      IfaDayConnType.WifiCell
    )
}
