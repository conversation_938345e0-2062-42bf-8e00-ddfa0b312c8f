package com.comlinkdata.emrjobs.spark.udp.tier1
import java.sql.Date
import com.comlinkdata.largescale.metrics.CldSparkMetricsRegistry
import com.comlinkdata.largescale.schema.udp.Ip
import com.comlinkdata.largescale.schema.udp.location.Point
import com.comlinkdata.largescale.schema.udp.tier0.UdpRaw
import com.comlinkdata.largescale.schema.udp.tier1.{UdpIpObservation, UdpDailyIp}
import com.comlinkdata.largescale.udp._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SparkSession, Dataset}

object UdpIpObservationRunner extends LazyLogging {

  def createUnifiedIpDataset(ds: ComlinkdataDatasource, data: Dataset[UdpRaw])(implicit spark: SparkSession): Dataset[UdpDailyIp] = {
    import spark.implicits._
    data
      .filter(r => r.ip.isDefined && r.latitude.isDefined && r.longitude.isDefined)
      .filter(UdpStaticIpFilter.filterIpsByCidr("ip"))
      .map(udpRaw2ip)
      .groupByKey(item => (item._1, item._2))
      .mapGroups { case ((ip, date), observations) =>
        aggregateIpDaily(ip, date, observations, ds.toString)
      }
  }

  def aggregateIpDaily(ip: Ip, date: Date, items: Iterator[(Ip, Date, Option[String], UdpIpObservation)], ds: String): UdpDailyIp =
    UdpDailyIp(ip, date.toLocalDate, ds, items.map(_._4).toVector)

  def udpRaw2ip(row: UdpRaw): (Ip, Date, Option[String], UdpIpObservation) = {
    val ip: Ip = row.ip.get
    val day: Date = row.date
    val carrier = row.carrier

    val obs = UdpIpObservation(
      row.datetime,
      row.localdatetime,
      row.ifa,
      Point(row.latitude.get, row.longitude.get),
      row.locationtype.get,
      row.connectiontype,
      row.place_id
    )

    (ip, day, carrier, obs)
  }

  /** Add record count and distinct ip count by day */
  def meterIpDataByDate(adjusted: Dataset[UdpDailyIp])(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Unit = {

    import com.comlinkdata.emrjobs.spark.udp.UdpMetrics.MetricUnits
    import org.apache.spark.sql.functions._
    import spark.implicits._

    val result: Array[(Date, Long, Long)] = adjusted
      .groupBy(col("partition_date"))
      .agg(count("*") as "counts", countDistinct("ip") as "distinct_ips")
      .as[(Date, Long, Long)]
      .collect
    // partition_date, counts, distinct_ips

    result.foreach { case (d, counts, distinctIps) =>
      metrics.putLiteral(d.toLocalDate, "daily-ip-total", counts, MetricUnits.each)
      metrics.putLiteral(d.toLocalDate, "daily-ip-distinct-total", distinctIps, MetricUnits.each)
    }
  }

}
