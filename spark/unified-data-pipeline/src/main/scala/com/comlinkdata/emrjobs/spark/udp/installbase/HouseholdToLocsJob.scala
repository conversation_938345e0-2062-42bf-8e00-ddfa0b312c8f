package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.emrjobs.spark.georesolver.GeometryResolver
import com.comlinkdata.emrjobs.spark.udp.installbase.LocationFilters.{howManyDecimalsInPt, hashPointAtNDigits}
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.udp.installbase.IpToLocation
import com.comlinkdata.largescale.schema.udp.location.Point
import com.typesafe.scalalogging.LazyLogging
import org.apache.sedona.core.serde.SedonaKryoRegistrator
import org.apache.sedona.sql.utils.SedonaSQLRegistrator
import org.apache.spark.serializer.KryoSerializer
import org.apache.spark.sql.expressions.{Window, UserDefinedFunction}
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.FloatType

import java.net.URI
import java.time.temporal.ChronoUnit
import java.time.{Duration, LocalDate}
import scala.collection.mutable

/**
  * Household To Locations Config
  * @param householdPath path the high confidence household data
  * @param ipLocsPath path to IpToLocs data
  * @param polygonsLocation Geolocation census block dataset
  * @param outputDate date for output dateset, if not given will check output path and only run if there is enough input data to process a new Q
  * @param lookBackDays days from output date to look back in history
  * @param hcLookForwardDays number of look forward days used to compute high confidence dataset
  * @param householdWithLocsOutPath output dateset path
  */
case class HouseholdToLocsConfig (
  householdPath: URI,
  ipLocsPath: URI,
  polygonsLocation: URI,
  outputDate: Option[LocalDate],
  lookBackDays: Int,
  hcLookForwardDays: Option[Int],
  householdWithLocsOutPath: URI
)

object HouseholdToLocsJob extends SparkJob(HouseholdToLocsJobRunner) {
  /**
    * Register sedona before calling run.  This cannot happen in "configure spark" because it only
    * provides a SparkBuilder at that point
    */
  override def run(config: HouseholdToLocsConfig): Duration = {
    SedonaSQLRegistrator.registerAll(spark)
    super.run(config)
  }

  /**
    * Need for sedona
    * @param builder config builder
    * @return builder with new parameters
    */
  override def configureSpark(builder: SparkSession.Builder): SparkSession.Builder = {
    builder
      .config("spark.kryo.registrator", classOf[SedonaKryoRegistrator].getName)
      .config("spark.serializer", classOf[KryoSerializer].getName)
  }
}

object HouseholdToLocsJobRunner
    extends SparkJobRunner[HouseholdToLocsConfig]
      with SparkConstants
      with LazyLogging {

  private val MAX_PT_COUNTS = 25
  private val DISCOUNT_RATE = .99f  // Exponential penalty per day for locations seen before output date

  private def readHouseHold(hhTSL: TimeSeriesLocation, dateIn:LocalDate)(implicit spark: SparkSession) = {
    val theOneHouseholdDataset = hhTSL.partition(dateIn) // get the last day of households

    UriDFTableRW.fromStr(theOneHouseholdDataset).read()
  }

  private def write(outTSL: TimeSeriesLocation, outputDate: LocalDate, config: HouseholdToLocsConfig, outDF: Dataset[Row])(implicit spark: SparkSession): Unit = {
    UriDFTableRW.fromStr(outTSL.partition(outputDate)).writeWithHistory(outDF, SaveMode.ErrorIfExists, Seq(config))
  }

  def mergeLocCounts(listOfLocs: Seq[Map[Point.Float, (Int, Float)]]): Map[Point.Float, (Int, Float)] = {

    val ptsCounts = listOfLocs.flatMap(ptMap => ptMap.toSeq.map(ptCtDisc =>
      (hashPointAtNDigits(3)(ptCtDisc._1), ptCtDisc)
    ))

    val ptsGroupedByHash = ptsCounts.groupBy(_._1)

    val ptsCombined = ptsGroupedByHash.map(hashedPt => {
      val ptList = hashedPt._2
      val ptOut = ptList.maxBy(innerPt => howManyDecimalsInPt(innerPt._2._1))._2._1
      val totalCount = ptList.map(innerPt => innerPt._2._2._1).sum
      val totalDisc = ptList.map(innerPt => innerPt._2._2._2).sum
      (ptOut, (totalCount, totalDisc))
    })

    mutable.PriorityQueue(ptsCombined.toSeq: _*)(Ordering.by((_: (Point.Float, (Int, Float)))._2._2))
      .take(MAX_PT_COUNTS).toMap
  }

  def getMergeLocCountsFun: UserDefinedFunction = udf[Map[Point.Float, (Int, Float)], Seq[Map[Point.Float, (Int, Float)]]] (
    mergeLocCounts
  )

  val mergeLocCountsUDF: UserDefinedFunction = udf[Map[Point.Float, (Int, Float)], Seq[Map[Point.Float, (Int, Float)]]] (
      (listOfLocs: Seq[Map[Point.Float, (Int, Float)]]) => mergeLocCounts(listOfLocs)
    )

  private val topLatLong = udf[Point.Float, Seq[(Float, Float, Float)]] (
    (listOfLatLngCount: Seq[(Float, Float, Float)]) => {

      val hashedPoints = listOfLatLngCount.map(latLngDiscount => (Point(latLngDiscount._1, latLngDiscount._2), latLngDiscount._3))
        .map(ptDiscount => (ptDiscount, hashPointAtNDigits(3)(ptDiscount._1)))
        .groupBy(_._2)
      val hashedSumed = hashedPoints.map(hashedPt => {

        val ptList = hashedPt._2
        val ptOut = ptList.maxBy(innerPt => howManyDecimalsInPt(innerPt._1._1))._1._1
        val totalDisc = ptList.map(innerPt => innerPt._1._2).sum

        (ptOut, totalDisc)
      })

      hashedSumed.maxBy(_._2)._1
    })

    private def expDiscount(daysFromDayZero: Int, expFactor: Float) =
      udf[Map[Point.Float, (Int, Float)], Map[Point.Float, Int]] (
      (locsCountsIn: Map[Point.Float, Int]) => {
        val discountFactor = Math.pow(expFactor, daysFromDayZero).toFloat
        locsCountsIn.mapValues(count => (count, count * discountFactor))
      }
    )

  private def mapListToMap =
    udf[Map[Point.Float, Int], Seq[Map[Point.Float, Int]]] (
      (mapListIn: Seq[Map[Point.Float, Int]]) => {
        mapListIn.flatten.groupBy(_._1).mapValues(_.map(_._2).sum)
      }
    )

    override def runJob(config: HouseholdToLocsConfig)(implicit spark: SparkSession): Unit = {
      val hhTSL = TimeSeriesLocation
        .ofYmdDatePartitions(config.householdPath)
        .build

      if(!hhTSL.existsAndHasData) {
        logger.warn("No household data found, giving up.") // early out if no household data yet
        return
      }

      val outTSL = TimeSeriesLocation
        .ofYmdDatePartitions(config.householdWithLocsOutPath)
        .build

      val outputWithoutLookForward = config.outputDate.getOrElse(hhTSL.latestDate)

      val hhDs = readHouseHold(hhTSL, outputWithoutLookForward).dropDuplicates("ip").cache()

      if(outTSL.existsAndHasData && config.outputDate.isEmpty && outTSL.latestDate.equals(outputWithoutLookForward)) {
        logger.info(s"No new data to run.")
        return
      }

      logger.info(s"Running. $outputWithoutLookForward")

      val startDate = outputWithoutLookForward.minusDays(config.lookBackDays)

      val lookForwardDays = config.hcLookForwardDays.getOrElse(0)
      val endDate = outputWithoutLookForward.plusDays(lookForwardDays)

      val dateRange = LocalDateRange.of(
            startDateInclusive = startDate,
            endDateInclusive = endDate
      )

      val outputDF = attachLocations(config.ipLocsPath, config.polygonsLocation, hhDs, dateRange)

      if (outputDF.isEmpty && config.outputDate.isDefined) {
        throw new IllegalArgumentException("Not enough input data to compute quarter")
      } else {
        write(outTSL, outputWithoutLookForward, config, outputDF.get)
      }
    }

  private [installbase] def attachLocations(ipLocsPath: URI, polygonsPath: URI, hhDs: Dataset[Row], dateRange: LocalDateRange)(implicit spark: SparkSession): Option[DataFrame] = {
    import spark.implicits._

    logger.info(s"Lookback Date Range: $dateRange")

    val ipLocsTSL = TimeSeriesLocation
      .ofYmdDatePartitions(ipLocsPath)
      .build

    val missingDates = dateRange.filter(!ipLocsTSL.exists(_))
    if (missingDates.nonEmpty) {
      logger.warn(s"Not enough input data to compute next quarter. Missing Dates: $missingDates")
      None
    } else {
      val hhIpDate = hhDs.select($"ip", $"ip_min_date").cache()

      val seqOfDsDays = for (day <- dateRange) yield {
        val locsIn = IpToLocation
          .read(ipLocsPath, day)
          .select($"ip", $"point", $"point_obs")
          .groupBy( $"ip")
          .agg(
            collect_list(map($"point",$"point_obs")) as "loc_counts_array"
          ).withColumn("loc_counts", mapListToMap($"loc_counts_array"))

        val daysFromZero = ChronoUnit.DAYS.between(day, dateRange.endDate).toInt

        val joined = hhIpDate.join(locsIn, Seq("ip"))

        val filtered = joined.where($"ip_min_date" <= day)

        val result = filtered
          .select($"ip", $"loc_counts")
          .withColumn("loc_counts", expDiscount(daysFromZero, DISCOUNT_RATE)($"loc_counts"))

        result
      }

      val unioned = seqOfDsDays.reduce(_ unionByName _).groupBy($"ip")
        .agg(collect_list("loc_counts").as("loc_counts_list"))
        .select($"ip", explode(mergeLocCountsUDF($"loc_counts_list").as("loc_counts")))
        .select($"ip", $"key.lat" as "latitude", $"key.lng" as "longitude", $"value._1" as "count", $"value._2" as "value")

      val geoResolved = {
        val cbr = GeometryResolver[Row](polygonsPath)
        cbr.resolveAsFlatDf(
          unioned,
          $"latitude",
          $"longitude",
          "census_block_id"
        )
      }

      val window = Window.partitionBy("ip").orderBy($"value".desc)

      val topCBs = geoResolved
        .groupBy("ip", "census_block_id").agg(
        sum("count") as "count",
        sum("value") as "value",
        collect_list(struct("latitude", "longitude", "value")) as "latLngValue"
      ).select(
        $"ip",
        $"census_block_id",
        $"count" as "household_count",
        $"value".cast(FloatType) as "discounted_count",
        row_number().over(window) as "rank",
        ($"count" / sum("count").over(window) * 100).cast(FloatType) as "percent_block",
        topLatLong($"latLngValue") as "best_point"
      ).where($"rank" === 1).drop("rank")
        .withColumn("census_group_id", substring(col("census_block_id"), 0, 12))

      val outputDF = topCBs.join(hhDs, Seq("ip"))
      hhIpDate.unpersist()

      Some(outputDF)
    }
  }
}

