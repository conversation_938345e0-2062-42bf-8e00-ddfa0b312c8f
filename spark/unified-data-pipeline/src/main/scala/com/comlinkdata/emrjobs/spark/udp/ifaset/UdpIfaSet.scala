package com.comlinkdata.emrjobs.spark.udp.ifaset

import com.comlinkdata.emrjobs.spark.udp.ifaset.FrequencyUtils.{mergeDayStats, createDayStats, filterRecentEntries}
import com.comlinkdata.emrjobs.spark.udp.ifaset.UdpIfaSet._
import com.comlinkdata.largescale.commons.RichDate.{localDateOrdering, sqlDateOrdering, toRichDate}
import com.comlinkdata.largescale.commons.Utils.emptyStringToNull
import com.comlinkdata.largescale.schema.udp.lookup.DeviceTypeLookup
import com.comlinkdata.largescale.schema.udp.tier1.UdpDailyDevice
import com.comlinkdata.largescale.schema.udp.tier2.IfaSet
import org.apache.spark.sql.{SparkSession, Dataset}
import org.apache.spark.sql.functions._

import java.time.LocalDate

class UdpIfaSet(implicit spark: SparkSession) {

  import spark.implicits._

  def runDay(
    day: LocalDate,
    ifaSet: Dataset[IfaSet],
    dailyDevices: Dataset[UdpDailyDevice],
    mccMncLookup: Map[String, String],
    staticDeviceTypeLookup: Dataset[DeviceTypeLookup]
  ): Dataset[IfaSet] = {
    val udpCustomReconciliation = new UdpIfaSetReconciliation(mccMncLookup)

    val newIfaSet = dailyDevices
      .groupByKey(_.ifa)
      .mapGroups { case (ifa, records) =>
        val deviceRecords = records.toVector
        val device = deviceRecords.head
        val pd = device.partition_date
        val newIfaSet = IfaSet(
          ds = device.ds,
          year = day.getYearString,
          month = day.getMonthValueString,
          day = day.getDayOfMonthString,
          ifa = ifa,
          days = Vector(pd),
          apps = createDayStats(deviceRecords.flatMap(_.app_id), MAX_APP_SIZE, pd),
          device_type = device.device_type,
          latest_carrier_name = device.carrier,
          final_carrier_name = None,
          iphone_disambiguated_model = None,
          modal_model_code = None,
          carrier_freqs = createDayStats(deviceRecords.flatMap(_.carrier), MAX_CARRIER_SIZE, pd),
          mcc_mnc_freqs = createDayStats(deviceRecords.flatMap(_.mcc_mnc).map(_.toLowerCase), MAX_MCC_SIZE, pd),
          model_code_freqs = createDayStats(deviceRecords.flatMap(_.model_code), MAX_MODEL_SIZE, pd),
          user_agent_string_freqs = createDayStats(deviceRecords.flatMap(_.user_agent), MAX_AGENT_SIZE, pd)
        )
        udpCustomReconciliation.applyReconciliation(newIfaSet)
      }

    ifaSet
      .select(IfaSet.cols: _*)
      .as[IfaSet]
      .unionByName(newIfaSet)
      .groupByKey(_.ifa)
      .mapGroups { case (_, sets) => sets.reduceLeft(mergeSets(_, _, udpCustomReconciliation)) }
      .as[IfaSet]
      .transform(stabilizeDeviceType(staticDeviceTypeLookup))
  }

  /**
    * Stabilizes device type across model codes by using given lookup table.
    *
    * @param deviceTypeLookup statically created lookup table that maps modal_model_code to device_type
    * @param ifaSet           ifa set
    * @return ifa set with device type stabilization
    */
  def stabilizeDeviceType(deviceTypeLookup: Dataset[DeviceTypeLookup])(ifaSet: Dataset[IfaSet]): Dataset[IfaSet] = {
    val lookupRenamed = deviceTypeLookup.withColumnRenamed("device_type", "device_type_new")
    ifaSet
      .transform(emptyStringToNull("device_type"))
      .join(broadcast(lookupRenamed), Seq("modal_model_code"), "left")
      .withColumn("device_type", coalesce($"device_type_new", $"device_type"))
      .drop("device_type_new")
      .as[IfaSet]
  }
}

object UdpIfaSet {
  private final val MAX_APP_SIZE = 50
  private final val MAX_MODEL_SIZE = 12
  private final val MAX_MCC_SIZE = 25
  private final val MAX_AGENT_SIZE = 12
  private final val MAX_CARRIER_SIZE = 6

  /**
    *
    * @param set1                    Accumulator - Yesterday's set
    * @param set2                    Current day set, need to append to Accumlator
    * @param udpIfaSetReconciliation Reconciliation steps to adjust carrier and Modal_model_code
    * @return combined Ifa Set
    */
  private def mergeSets(set1: IfaSet, set2: IfaSet, udpIfaSetReconciliation: UdpIfaSetReconciliation): IfaSet = {
    val maxDate = Seq(
      LocalDate.of(set1.year.toInt, set1.month.toInt, set1.day.toInt),
      LocalDate.of(set2.year.toInt, set2.month.toInt, set2.day.toInt)
    ).max

    val yesterdayToday = Seq((set1.year, set1.month, set1.day), (set2.year, set2.month, set2.day)).sorted
    val yesterdayYmd = yesterdayToday.head
    val todayYmd = yesterdayToday(1)

    val yesterday = Seq(set1, set2)
      .find(s => s.year == yesterdayYmd._1 && s.month == yesterdayYmd._2 && s.day == yesterdayYmd._3)
      .get

    val today = Seq(set1, set2)
      .find(s => s.year == todayYmd._1 && s.month == todayYmd._2 && s.day == todayYmd._3)
      .get

    val result = IfaSet(
      ds = yesterday.ds,
      year = maxDate.getYearString,
      month = maxDate.getMonthValueString,
      day = maxDate.getDayOfMonthString,
      ifa = yesterday.ifa,
      days = (yesterday.days ++ today.days).sorted,
      apps = filterRecentEntries(mergeDayStats(yesterday.apps, today.apps), MAX_APP_SIZE),
      device_type = yesterday.device_type.orElse(today.device_type),
      final_carrier_name = yesterday.final_carrier_name.orElse(today.final_carrier_name),
      latest_carrier_name = yesterday.latest_carrier_name.orElse(today.latest_carrier_name),
      iphone_disambiguated_model = yesterday.iphone_disambiguated_model.orElse(today.iphone_disambiguated_model),
      modal_model_code = yesterday.modal_model_code.orElse(today.modal_model_code),
      carrier_freqs = filterRecentEntries(mergeDayStats(yesterday.carrier_freqs, today.carrier_freqs), MAX_CARRIER_SIZE),
      mcc_mnc_freqs = filterRecentEntries(mergeDayStats(yesterday.mcc_mnc_freqs, today.mcc_mnc_freqs), MAX_MCC_SIZE),
      model_code_freqs = filterRecentEntries(mergeDayStats(yesterday.model_code_freqs, today.model_code_freqs), MAX_MODEL_SIZE),
      user_agent_string_freqs = filterRecentEntries(mergeDayStats(yesterday.user_agent_string_freqs, today.user_agent_string_freqs), MAX_AGENT_SIZE)
    )
    val reconcile = udpIfaSetReconciliation.applyReconciliation(result)
    val stabilize = udpIfaSetReconciliation.stabilizeDeviceType(reconcile, yesterday.device_type, today.device_type)
    stabilize
  }

  def apply()(implicit spark: SparkSession): UdpIfaSet = new UdpIfaSet()
}