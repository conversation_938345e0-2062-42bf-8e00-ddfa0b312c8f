package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.emrjobs.spark.udp.installbase.LocationFilters
import com.comlinkdata.largescale.commons.UriDFTableRW
import com.comlinkdata.largescale.schema.udp.installbase.IfaToIpWithObs
import com.comlinkdata.largescale.schema.udp.lookup.HourWeightLookUp
import com.comlinkdata.largescale.schema.udp.tier1.UdpIfaObservation
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.apache.spark.sql.{SparkSession, Dataset}
import org.apache.spark.sql.functions._

import java.net.URI
import java.time.LocalDate

abstract class IfaObsWithMMConfig {
  def ifaObsPath: URI
  def maxmindPath: URI
  def carrierLookupPath: URI
  def carrierLookupFwPath: URI
  def ipv6TruncationLookupPath: URI
  def hourWeightLookupPath: URI
  def dailyIpCarrierPath: Option[URI]
  def datasources: Seq[ComlinkdataDatasource]
  def startDate: LocalDate
  def endDate: LocalDate
  def getRepartition: Int
  def getOutPath: URI
}

object IfaObsMMNightTime {
  abstract class Base(
    hourWeightLookupPath: URI,
    includeObs: Boolean,
    ifaObs: IfaObsWithMM
  )(implicit spark: SparkSession) {

    import spark.implicits._

    private lazy val dsHourWeightLookUp: Dataset[HourWeightLookUp] =
      UriDFTableRW(hourWeightLookupPath).read()
        .as[HourWeightLookUp].cache()

    private val empty_obs = udf(() => Array.empty[UdpIfaObservation])

    def read(runDate: LocalDate): Dataset[IfaToIpWithObs] = {
      val mmJoined = ifaObs.read(runDate)

      mmJoined
        .withColumn("is_night_obs", when(LocationFilters.isNightTime($"obs.t_local"), 1).otherwise(0))
        .withColumn("is_hh_obs", when(LocationFilters.isHouseholdHour($"obs.t_local"), 1).otherwise(0))
        .withColumn("hour", hour($"obs.t_local"))
        .join(dsHourWeightLookUp.hint("broadcast").as("weights"), Seq("hour"), "inner")
        .groupBy("ip", "ifa", "carrier", "consolidated_id", "connection_type", "sp_platform", "organization", "autonomous_system_number")
        .agg(
          count("obs") as "obs_count",
          sum($"is_night_obs") as "night_obs_count",
          sum($"is_hh_obs") as "household_obs_count",
          sum($"home_weight") as "household_weighted_obs_count",
          sum($"bus_weight") as "business_weighted_obs_count",
          (if (includeObs) collect_list($"obs") else empty_obs()) as "observations"
        ).as[IfaToIpWithObs]
    }
  }

  class WithObs(hourWeightLookupPath: URI, ifaObs: IfaObsWithMM)(implicit spark: SparkSession)
    extends Base(hourWeightLookupPath: URI, true, ifaObs: IfaObsWithMM)

  class NoObs(hourWeightLookupPath: URI, ifaObs: IfaObsWithMM)(implicit spark: SparkSession)
    extends Base(hourWeightLookupPath: URI, false, ifaObs: IfaObsWithMM)

  def dsToIfaObsNoObs(config: IfaObsWithMMConfig)(implicit spark: SparkSession): Map[ComlinkdataDatasource, IfaObsMMNightTime.Base] = {
    dsToIfaObs(config, (x: URI, y: IfaObsWithMM) => new NoObs(x, y))
  }

  def dsToIfaObsWithObs(config: IfaObsWithMMConfig)(implicit spark: SparkSession): Map[ComlinkdataDatasource, IfaObsMMNightTime.Base] = {
    dsToIfaObs(config, (x: URI, y: IfaObsWithMM) => new WithObs(x, y))
  }

  def dsToVZWIfaObsWithObs(config: IfaObsWithMMConfig, verizonFwIpLocation: URI)(implicit spark: SparkSession): Map[ComlinkdataDatasource, IfaObsMMNightTime.Base] = {
    config.datasources.map(ds => {
      val mmOrProxy = new MaxMindOrProxy.VZW(
        ds,
        config.maxmindPath,
        config.carrierLookupPath,
        config.carrierLookupFwPath,
        config.dailyIpCarrierPath,
        verizonFwIpLocation
      )
      val ifaObsWithMM = new IfaObsWithMM(config.ifaObsPath, config.ipv6TruncationLookupPath, mmOrProxy)
      (ds, new WithObs(config.hourWeightLookupPath, ifaObsWithMM))
    }).toMap
  }

  def dsToTMOIfaObsWithObs(config: IfaObsWithMMConfig)(implicit spark: SparkSession): Map[ComlinkdataDatasource, IfaObsMMNightTime.Base] = {
    config.datasources.map(ds => {
      val mmOrProxy = new MaxMindOrProxy.TMO(
        ds,
        config.maxmindPath,
        config.carrierLookupPath,
        config.carrierLookupFwPath,
        config.dailyIpCarrierPath
      )
      val ifaObsWithMM = new IfaObsWithMM(config.ifaObsPath, config.ipv6TruncationLookupPath, mmOrProxy)
      (ds, new WithObs(config.hourWeightLookupPath, ifaObsWithMM))
    }).toMap
  }

  private def dsToIfaObs(config: IfaObsWithMMConfig, constructor: (URI, IfaObsWithMM) => Base)(implicit spark: SparkSession): Map[ComlinkdataDatasource, IfaObsMMNightTime.Base] = {
    config.datasources.map(ds => {
      val mmOrProxy = new MaxMindOrProxy.Wifi(
        ds,
        config.maxmindPath,
        config.carrierLookupPath,
        config.carrierLookupFwPath,
        config.dailyIpCarrierPath
      )
      val ifaObsWithMM = new IfaObsWithMM(config.ifaObsPath, config.ipv6TruncationLookupPath, mmOrProxy)
      (ds, constructor(config.hourWeightLookupPath, ifaObsWithMM))
    }).toMap
  }
}
