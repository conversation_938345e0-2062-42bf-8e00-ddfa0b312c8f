package com.comlinkdata.emrjobs.spark.udp.tier1

import com.comlinkdata.largescale.metrics.CldSparkMetricsRegistry
import com.comlinkdata.largescale.schema.udp.tier0.UdpRaw
import com.comlinkdata.largescale.schema.udp.tier1.UdpDailyDevice
import com.comlinkdata.largescale.udp._

import java.sql.Date
import org.apache.spark.sql.{SparkSession, Dataset}

object UdpDailyDeviceRunner {
  def createDeviceDataset(ds: ComlinkdataDatasource, raw: Dataset[UdpRaw])(implicit spark: SparkSession): Dataset[UdpDailyDevice] = {
    import spark.implicits._
    raw
      .groupByKey(row => (row.ifa, row.date))
      .flatMapGroups { case ((ifa, _), mw02s) =>
        uniqueDevices(ds.toString, ifa, mw02s.toVector)
      }
  }

  //TODO add tests
  def uniqueDevices(ds: String, ifa: Array[Byte], mw02s: Vector[UdpRaw]): Vector[UdpDailyDevice] = {
    import com.comlinkdata.largescale.commons.RichDate._ // ordering by timestamp
    val sorted = mw02s.sortBy(_.datetime) // will break if datetime is null, we expect it isn't
    val (lastItem, endingCount, devices) =
      sorted.tail.foldLeft((sorted.head, 1, Vector.empty[UdpDailyDevice])) {
        case ((current: UdpRaw, count: Int, soFar: Vector[UdpDailyDevice]), next: UdpRaw) =>
          //TODO current and next double computation, remove because isSameDevice converts argument to daily device
          if (UdpDailyDevice.isSameDevice(current, next)) { // same as last, increment count
            (current, count + 1, soFar)
          } else { // new device information, set next as current, reset count, append to completed device to vector
            (next, 1, soFar :+ UdpDailyDevice.fromUdpRaw(ifa, current, count, ds))
          }
      }
    // add lastItem to devices and return.
    devices :+ UdpDailyDevice.fromUdpRaw(ifa, lastItem, endingCount, ds)
  }

  def meterDeviceDataByDate(adjusted: Dataset[UdpDailyDevice])(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Unit = {
    import com.comlinkdata.emrjobs.spark.udp.UdpMetrics.MetricUnits
    import org.apache.spark.sql.functions._
    import spark.implicits._

    val result = adjusted
      .groupBy(col("partition_date"))
      .agg(count("*") as "counts", countDistinct("ifa") as "distinct_devices")
      .as[(Date, Long, Long)]
      .collect

    result.foreach { case (dd, counts, distinctDevices) =>
      val d = dd.toLocalDate
      metrics.putLiteral(d, "daily-device-total", counts, MetricUnits.each)
      metrics.putLiteral(d, "daily-device-distinct-total", distinctDevices, MetricUnits.each)
    }
  }
}
