package com.comlinkdata.emrjobs.spark.udp.installbase

import java.time.{LocalDate, Month}

/**
  * Functions for scheduling was days should be run
  */
object Scheduling {

  /**
    * Get next Q start day given a date
    * @param date input date
    * @return next Q start date
    */
  def nextQ(date: LocalDate): LocalDate = {
    date.getMonth match {
      case Month.JANUARY | Month.FEBRUARY | Month.MARCH => LocalDate.of(date.getYear, Month.APRIL, 1)
      case Month.APRIL | Month.MAY | Month.JUNE => LocalDate.of(date.getYear, Month.JULY, 1)
      case Month.JULY | Month.AUGUST | Month.SEPTEMBER => LocalDate.of(date.getYear, Month.OCTOBER, 1)
      case Month.OCTOBER | Month.NOVEMBER | Month.DECEMBER => LocalDate.of(date.getYear + 1, Month.JANUARY, 1)
    }
  }

}
