package com.comlinkdata.emrjobs.spark.udp.tier1

import com.comlinkdata.largescale.udp.Cidr
import org.apache.spark.sql.Column
import org.apache.spark.sql.functions.col

object UdpStaticIpFilter {

  //https://en.wikipedia.org/wiki/Reserved_IP_addresses
  // (mnob<PERSON> wants to leave ***********/16 in the data for ip-obs) 2021-05-25
  // https://comniscient.atlassian.net/browse/AGG-2864
  val ipCidrToSuppress = List(
    "***********/16",
    "*******/32",
    "127.0.0.1/32"
  )

  def filterIpsByCidr[T](ipColName: String, cidrs: List[String] = ipCidrToSuppress): Column = {
    val ipCol = col(ipColName)
    cidrs.map(cidr => Cidr.ipMinMax(cidr))
      .map { case (ip0, ip1) => (ipCol < ip0) or (ipCol > ip1) }
      .reduce(_ and _)
  }
}
