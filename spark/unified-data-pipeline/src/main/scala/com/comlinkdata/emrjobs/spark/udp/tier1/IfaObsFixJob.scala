package com.comlinkdata.emrjobs.spark.udp.tier1

import com.comlinkdata.largescale.commons.{Spark<PERSON>ob, SparkJobRunner}
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.location.LocationStat.LocationStatsMap
import com.comlinkdata.largescale.schema.udp.location.Point
import com.comlinkdata.largescale.schema.udp.tier1.{UdpDailyIfa, UdpIfaObservation}
import java.sql.{Date, Timestamp}
import java.time.ZoneId
import net.iakovlev.timeshape.TimeZoneEngine
import org.apache.spark.sql.{SaveMode, SparkSession}

case class IfaObs(
  partition_date: Date,
  ifa: Ifa,
  observations: Seq[UdpIfaObservation],
  observation_count: Int,
  unique_ip_count: Int,
  location_stats: LocationStatsMap)

case class IfaObsFixConfig(src: String, dest: String)

object IfaObsFixJob extends SparkJob(IfaObsFixRunner)

object IfaObsFixRunner extends SparkJobRunner[IfaObsFixConfig] {
  private def getLocalTime(utcTime: Timestamp, location: Point.Float, tzEngine: TimeZoneEngine): Option[Timestamp] = {
    val tz = tzEngine.query(location.lat, location.lng)
    (if (tz.isPresent) Some(tz.get()) else None)
      .map(utcTime.toLocalDateTime.atZone(ZoneId.of("UTC")).withZoneSameInstant(_).toLocalDateTime)
      .map(Timestamp.valueOf)
  }

  override def runJob(config: IfaObsFixConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    spark.read.parquet(config.src).as[IfaObs]
      .mapPartitions { i =>
        val tzEngine = TimeZoneEngine.initialize(41.0, -142.0, 84.0, -52.0, true)
        i.map { row =>
          row.copy(observations = row.observations.map { obs =>
            obs.copy(t_local = getLocalTime(obs.t_utc, obs.location, tzEngine))
          })
        }
      }
      .write.mode(SaveMode.Overwrite).parquet(config.dest)
  }
}
