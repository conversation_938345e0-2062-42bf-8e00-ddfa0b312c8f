package com.comlinkdata.emrjobs.spark.udp.maxmind

import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.udp.Ip
import com.comlinkdata.largescale.schema.udp.tier1.{MaxmindIp2ConnectionType, UdpDailyMaxmindNoYmd, MaxmindIp2Isp}
import com.comlinkdata.largescale.udp._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SparkSession, Dataset, DataFrame, SaveMode}

import java.net.URI
import java.time.LocalDate

case class MaxmindIngestionConfig(
  udpDailyIpSrc: URI,
  datasource: ComlinkdataDatasource,
  ipversion: Int,
  maxmindConnectionTypeSrc: URI,
  maxmindIspSrc: URI,
  maxmindResolvedIpsTempDest: URI,
  maxmindResolvedIpsDestCheck: URI,
  dateRangeOpt: Option[LocalDateRange]
)

//network,isp,organization,autonomous_system_number,autonomous_system_organization
case class LittleMaxmindIsp(iprange_min: Ip, iprange_max: Ip, isp: Option[String], organization: Option[String], autonomous_system_number: Option[String], autonomous_system_organization: Option[String])
case class MaxmindIspIpRangeValueType(isp: Option[String], organization: Option[String], autonomous_system_number: Option[String], autonomous_system_organization: Option[String])
case class LittleMaxmindConnectionType(iprange_min: Ip, iprange_max: Ip, connection_type: String)


/**
 * Read maxmind data and apply it to UdpDailyIp table to resolve connection type and carrier.  Write results
 * to IpCarrier table
 */
object MaxmindIngestionJob extends SparkJob(MaxmindIngestionRunner, forceKryoRegistration = false)

object MaxmindIngestionRunner extends SparkJobRunner[MaxmindIngestionConfig] with LazyLogging {

  import com.comlinkdata.largescale.commons.fileutils.CldFileUtils.implicits._

  val maxMaxmindAgeDays = 30
  val ipV4Bytes = 4
  val ipV6Bytes = 16

  override def runJob(config: MaxmindIngestionConfig)(implicit spark: SparkSession): Unit = {

    require(Seq(4,6).contains(config.ipversion), s"Unknown ip version ${config.ipversion}, must be 4 or 6.")

    val checkTs = TimeSeriesLocation
      .ofYmdDatePartitions(config.maxmindResolvedIpsDestCheck)
      .withPartition("ds", config.datasource)
      .withPartition("ipversion", config.ipversion)
      .build

    val outTs = TimeSeriesLocation
      .ofYmdDatePartitions(config.maxmindResolvedIpsTempDest)
      .withPartition("ds", config.datasource)
      .withPartition("ipversion", config.ipversion)
      .build

    val dates = config.dateRangeOpt.getOrElse {
      logger.info("No date range specified inspecting data locations for pending work.")
      val inputLocation = TimeSeriesLocation.ofYmdDatePartitions(config.udpDailyIpSrc)
        .withPartition("ds", config.datasource)
        .build

      val latestOutDate = checkTs.latestDate
      logger.info(s"Latest output partition date is $latestOutDate.")

      val startDate = latestOutDate.plusDays(1)
      val endDate = inputLocation.latestInputPartition
      if (startDate isAfter endDate) {
        logger.warn(s"Start date after end date ${startDate -> endDate}, no work to do.  Exiting.")
        return
      }
      LocalDateRange(startDate, endDate)
    }

    logger.info(s"Running daily jobs for date range $dates.")
    dates.iterator.foreach { day =>
      logger.info(s"Running day $day.")
      val (_, dur) = Utils.timer(runDay(day, config, outTs))
      logger.info(s"Day $day completed in duration $dur.")
      spark.catalog.clearCache()
    }
  }

  def runDay(pDate: LocalDate, config: MaxmindIngestionConfig, outTs: TimeSeriesLocation)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val inputLocation = TimeSeriesLocation.ofYmdDatePartitions(config.udpDailyIpSrc)
      .withPartition("ds", config.datasource)
      .build

    val mmd0 = pDate.minusDays(maxMaxmindAgeDays) // min allowed maxmind date
    val mmd1 = pDate.minusDays(1) // max allowed maxmind date
    logger.info(s"Running with processing date of $pDate.")
    logger.info(s"Maxmind date bounds are ${(mmd0,mmd1)}.")

    val input = inputLocation.partition(pDate)
    inputLocation.cldFileUtils.allDirectoryStringsHaveDataOrThrow(input :: Nil, "input data for processing date must exist")

    def ipFilter(df: DataFrame): DataFrame = {
      val len = config.ipversion match {
        case 4 => ipV4Bytes
        case 6 => ipV6Bytes
      }
      df.filter(length($"ip") === len)
    }

    logger.info(s"Reading ips from udpInput $input.")
    val dailyIps = spark.read
      .option(ReadOpts.basePath, config.udpDailyIpSrc.toString)
      .parquet(input)
      .select("ip")
      .transform(ipFilter)
      .repartition()
      .as[Ip]


    val dest: URI = outTs.partition(pDate)
    logger.info(s"Writing output with isp and connection type to $dest.")

    dailyIps
      .transform(joinConnDf(loadMaxmindConnection(config, mmd0, mmd1)))
      .transform(joinIspDf(loadMaxmindIsp(config, mmd0, mmd1)))
      .repartition(1)
      .write.mode(SaveMode.ErrorIfExists)
      .parquet(dest.toString)
  }


  def loadMaxmindIsp(
    config: MaxmindIngestionConfig,
    d0: LocalDate, d1: LocalDate
  )(implicit spark:SparkSession): Dataset[LittleMaxmindIsp] = {
    import spark.implicits._

    logger.info(s"Searching for most recent maxmind isp table between ${d0 -> d1} at ${config.maxmindIspSrc}.")

    val mmIspLoc: String = TimeSeriesLocation.ofDatePartitions(config.maxmindIspSrc).build
      .partitionsExistingBetween(LocalDateRange.of(d0, d1))
      .maxBy(_._1.toEpochDay)._2
    logger.info(s"Reading maxmind isp table from $mmIspLoc.")


    MaxmindIp2Isp.readFromSrcsDf(config.maxmindIspSrc, mmIspLoc)
      .select("network", "isp", "organization", "autonomous_system_number", "autonomous_system_organization")
      .transform(Cidr.convertCidrToIpRange("network"))
      .as[LittleMaxmindIsp]
  }

  def loadMaxmindConnection(
    config: MaxmindIngestionConfig,
    d0: LocalDate, d1: LocalDate
  )(implicit spark: SparkSession): Dataset[LittleMaxmindConnectionType] = {

    import spark.implicits._

    logger.info(s"Searching for most recent maxmind connection table between ${d0 -> d1} at ${config.maxmindConnectionTypeSrc}.")

    // if a valid date doesn't exist, this will throw (by design).
    val mmConnLoc: String = TimeSeriesLocation.ofDatePartitions(config.maxmindConnectionTypeSrc).build
      .partitionsExistingBetween(LocalDateRange.of(d0, d1))
      .maxBy(_._1.toEpochDay)._2
    logger.info(s"Reading maxmind connection table from $mmConnLoc.")

    MaxmindIp2ConnectionType
      .readFromSrcsDf(config.maxmindConnectionTypeSrc, mmConnLoc)
      .select("network", "connection_type")
      .transform(Cidr.convertCidrToIpRange("network"))
      .as[LittleMaxmindConnectionType]
  }

  def joinConnDf(connections: Dataset[LittleMaxmindConnectionType])(ips: Dataset[Ip])(implicit spark: SparkSession): Dataset[(Ip, Option[String])] = {

    import spark.implicits._

    logger.info("Collecting conn data to driver.")
    val connDataLocal = connections
      .map(row => IpRangeMap.Element(row.iprange_min, row.iprange_max, row.connection_type))
      .collect

    logger.info("Joining local conn data using RangeMap in mapPartitions.")

    ips.mapPartitions { partition =>

      // within each partition, create a lookup table
      val lookup = IpRangeMap.newBuilder[String]
        .putAll(connDataLocal)
        .build

      partition.map { ip: Ip =>
        lookup.get(ip) match {
          case null => ip -> None
          case conn => ip -> Some(conn)
        }
      }
    }
  }

  def joinIspDf(isps: Dataset[LittleMaxmindIsp]
  )(ips: Dataset[(Ip, Option[String])]
  )(implicit spark: SparkSession
  ): Dataset[UdpDailyMaxmindNoYmd] = {

    import spark.implicits._

    val ispDataLocal = isps
      .map{row =>
        val v = MaxmindIspIpRangeValueType(row.isp, row.organization, row.autonomous_system_number, row.autonomous_system_organization)
        IpRangeMap.Element(row.iprange_min, row.iprange_max, v)
      }
      .collect

    logger.info("Joining local ISP data using RangeMap in mapPartitions.")

    ips.mapPartitions { partition =>

      // within each partition, create a lookup table
      val lookup = IpRangeMap.newBuilder[MaxmindIspIpRangeValueType]
        .putAll(ispDataLocal)
        .build

      partition.map { case (ip, connOpt) =>
        lookup.get(ip) match {
          case null => UdpDailyMaxmindNoYmd(ip, connOpt, None, None, None, None)
          case mm => UdpDailyMaxmindNoYmd(ip, connOpt, mm.isp, mm.organization, mm.autonomous_system_number, mm.autonomous_system_organization)
        }
      }
    }
  }


}
