package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.emrjobs.spark.georesolver.GeometryResolver
import com.comlinkdata.emrjobs.spark.udp.installbase.prefilter.{IfaObsWithMM, MaxMindOrProxy}
import com.comlinkdata.largescale.commons.{RichDate, _}
import com.comlinkdata.largescale.schema.udp.installbase.CellOnly
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging
import org.apache.sedona.sql.utils.SedonaSQLRegistrator
import org.apache.spark.sql
import org.apache.spark.sql.functions._
import org.apache.spark.sql._
import org.apache.spark.sql.expressions.Window

import java.net.URI
import java.time.{Duration, LocalDate}

/**
  * Parameters for Cell Only Job
  *
  * @param ifaObsPath               udp daily IFA Observations path (eg. s3://d000-comlinkdata-com/prod/private/udp/tier1/daily-ifa-observation/)
  * @param maxmindPath              maxmind path (eg. s3://d000-comlinkdata-com/prod/private/udp/daily-maxmind/)
  * @param highConfPath             high confidence root dataset path (eg. s3://e000-comlinkdata-com/staging/broadband/installbase/high_confidence/v=0.0/r=1/)
  * @param carrierLookupPath        Carrier Lookup Path (eg. s3://d000-comlinkdata-com/prod/private/lookup-tables/broadband/carrier-lookup/v=1.7.019/)
  * @param polygonsLocation         Geolocation census block dataset (eg. s3://d000-comlinkdata-com/prod/private/lookup-tables/udp/census_block_polygons/v=1.0.0/)
  * @param ipv6TruncationLookupPath ip v6 carrier truncations (eg. s3://c400-athena-dev-comlinkdata-com/maggie_sandbox/KEEP_references/ip_truncation_lookup/date=2022-03-16/)
  * @param hcDate                   output high confidence date
  * @param hcPrevDate               previous high confidence date (if you want non-cord cutter removed)
  * @param datasources              Seq of input datasources
  * @param preGeoResolveRepartition size of repartition before georesolve (if not given 700 is used)
  * @param outputRepartition        repartition before output (default 8)
  * @param outPath                  Output root path
  * @param dailyIpCarrierPath       if processing pre 2020/4/1 data need to provide path to daily ip carrier
  */
case class CellOnlyConfig(
  ifaObsPath: URI,
  maxmindPath: URI,
  highConfPath: URI,
  carrierLookupPath: URI,
  carrierLookupFwPath: URI,
  polygonsLocation: URI,
  ipv6TruncationLookupPath: URI,
  hcDate: LocalDate,
  outPath: URI,
  hcPrevDate: Option[LocalDate] = None,
  datasources: Seq[ComlinkdataDatasource],
  preGeoResolveRepartition: Option[Int] = None,
  outputRepartition: Option[Int] = None,
  dailyIpCarrierPath: Option[URI],
)

object CellOnlyJob extends SparkJob(CellOnlyRunner) {

  override def allowDefaultArgs(): Boolean = true

  /**
    * Register sedona before calling run.  This cannot happen in "configure spark" because it only
    * provides a SparkBuilder at that point
    */
  override def run(config: CellOnlyConfig): Duration = {
    SedonaSQLRegistrator.registerAll(spark)
    super.run(config)
  }
}

object CellOnlyRunner
  extends SparkJobRunner[CellOnlyConfig]
    with SparkConstants
    with LazyLogging {

  private val DEFAULT_GEO_PART = 700
  private val DEFAULT_OUT_PART = 8

  private val DAY_WINDOW_SIZE = 14
  private val CELL_ONLY_CUT_OFF = 0.85f
  private val MIN_NUMBER_DAYS_IFA_SEEN = 10

  /**
    * If the prev date is given limit output cell only ifas to ifas that don't continue to exist in high conf
    *
    * @param highConfPath root high conf path
    * @param hcPrevDate   previous high conf dataset
    * @param cellOnlyOut  unfiltered cell only dataset
    * @param hcThisDate   high conf dataset for output date
    * @param spark        implicit spark
    * @return optionally filtered cell only dataset
    */
  private[installbase] def filterBroadbandSurvivors(highConfPath: URI, hcPrevDate: Option[LocalDate], cellOnlyOut: sql.DataFrame, hcThisDate: sql.DataFrame)(implicit spark: SparkSession): sql.DataFrame = {
    import spark.implicits._

    if (hcPrevDate.isDefined) {
      val hhTSL = TimeSeriesLocation
        .ofYmdDatePartitions(highConfPath)
        .build

      val prevHc = UriDFTableRW.fromStr(hhTSL.partition(hcPrevDate.get)).read()
        .select($"ifa", $"ip", $"household_id")
        .alias("hcPrev")

      val currHc = hcThisDate.select("ifa", "ip")

      //Find if any ifa, ip combo's that exist in both previous high conf and current high conf, call them surviving households
      val survHH = currHc.join(prevHc, Seq("ifa", "ip"))
        .select("household_id")
        .withColumn("survivor", lit(true))
        .alias("survHH")

      val filteredResult = cellOnlyOut.alias("co")
        .join(prevHc, $"co.ifa" === $"hcPrev.ifa", "left")
        .select("co.*", "hcPrev.household_id")
        .withColumn("ifa_type", when($"hcPrev.household_id".isNull, "new-co-ifa").otherwise("cord-cutter")) //if this ifa is new call it "new-co-ifa" else it is a possible "cord-cutter"
        .join(survHH, $"hcPrev.household_id" === $"survHH.household_id", "left") //join to find out if this ifa existed previously in a surviving BB household
        .where(($"ifa_type" === "cord-cutter" && $"survivor".isNull) || $"ifa_type" === "new-co-ifa")

      filteredResult.select("ifa",
        "ifa_type",
        "perc_cell",
        "census_block_id",
        "census_group_id",
        "perc_in_block",
        "perc_in_group",
        "hour_days",
        "cell_only_days_count",
        "lat",
        "lng",
        "carrier")
    } else { //don't filter but add ifa_type column to keep schema consistent
      cellOnlyOut.withColumn("ifa_type", lit("NA"))
    }
  }

  private[installbase] def readHC(highConfPath: URI, hcDate: LocalDate)(implicit spark: SparkSession): sql.DataFrame = {

    val hhTSL = TimeSeriesLocation
      .ofYmdDatePartitions(highConfPath)
      .build

    UriDFTableRW.fromStr(hhTSL.partition(hcDate)).read()
  }

  private[installbase] def readIfaDay(ifaObsPath: URI, ifaDate: LocalDate)(implicit spark: SparkSession): sql.DataFrame = {
    val hhTSL = TimeSeriesLocation
      .ofYmdDatePartitions(ifaObsPath)
      .build

    UriDFTableRW.fromStr(hhTSL.partition(ifaDate)).read()
  }


  override def runJob(config: CellOnlyConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    import RichDate.localDateOrdering._

    val hcThisDate = readHC(config.highConfPath, config.hcDate).select("ifa", "ip", "household_id").cache()

    val ifasInHc = hcThisDate.select($"ifa").distinct()

    val allIfaObs = LocalDateRange.of(
      startDateInclusive = config.hcDate.minusDays(DAY_WINDOW_SIZE - 1),
      endDateInclusive = config.hcDate).flatMap(day => {

      config
        .datasources
        .filter(ds => day >= ds.firstDate && day <= ds.lastDate.getOrElse(LocalDate.of(9999, 1, 1)))
        .map(
          ds => {
            val mmOrProxy = new MaxMindOrProxy.CellOnly(
              ds,
              config.maxmindPath,
              config.carrierLookupPath,
              config.carrierLookupFwPath,
              config.dailyIpCarrierPath
            )

            val ifaObsWithMM = new IfaObsWithMM(config.ifaObsPath, config.ipv6TruncationLookupPath, mmOrProxy)
            ifaObsWithMM.read(day)
          }
        )
    }).reduce(_ unionByName _)

    val hcIfasRemoved = antiJoinHc(ifasInHc, allIfaObs).cache()

    val allNightIfaObs = hcIfasRemoved.where(LocationFilters.isHouseholdHour($"t_local"))

    val ifaCellOnlyIfas = cellOnlyIfas(allNightIfaObs, config.hcDate, CELL_ONLY_CUT_OFF, MIN_NUMBER_DAYS_IFA_SEEN).cache()

    val modalCarriers = findModalCarrierForIfas(config, hcIfasRemoved, ifaCellOnlyIfas)

    val windowIfaObCount = Window.partitionBy("ifa").orderBy($"count".desc)

    // Go back to input and collect all locations that are above cutoff
    // Remove 2 digit locs, restrict locs to home hour, and celluar only connection_type

    val ifaLocations = allNightIfaObs
      .where($"location_type" === "GPS")
      .where(lower($"connection_type") === "cellular")
      .where((round($"lat", 2) =!= $"lat") && (round($"lng", 2) =!= $"lng"))
      .where((round($"lat", 1) =!= $"lat") && (round($"lng", 1) =!= $"lng"))
      .where((round($"lat", 0) =!= $"lat") && (round($"lng", 0) =!= $"lng"))
      .join(ifaCellOnlyIfas.select("ifa", "perc_cell", "hour_days", "cell_only_days_count"), Seq("ifa"))
      .withColumn("lat", round($"lat", 3))
      .withColumn("lng", round($"lng", 3))
      .groupBy($"ifa", $"perc_cell", $"hour_days", $"cell_only_days_count", $"lat", $"lng").count()
      .select($"ifa", $"perc_cell", $"hour_days", $"cell_only_days_count", $"lat", $"lng", $"count", row_number().over(windowIfaObCount).alias("rank"))
      .where($"rank" <= 10)
      .cache() // before georesolver
      .repartition(config.preGeoResolveRepartition.getOrElse(DEFAULT_GEO_PART), $"ifa") // added to speedup

    val geoResolved = {
      val cbr = GeometryResolver[Row](config.polygonsLocation)
      cbr.resolveAsFlatDf(
        ifaLocations,
        $"lat",
        $"lng",
        "census_block_id"
      )
    }

    val windowIFA_CB = Window.partitionBy("ifa", "census_block_id")
    val windowIFA_CG = Window.partitionBy("ifa", "census_group_id")
    val windowIFAObCbCount = Window.partitionBy("ifa").orderBy($"sum_in_cb".desc, $"count".desc)

    val blocked = geoResolved
      .withColumn("census_group_id", substring(col("census_block_id"), 0, 12))
      .withColumn("sum_ifa", sum($"count").over(Window.partitionBy("ifa")))
      .withColumn("sum_in_cb", sum($"count").over(windowIFA_CB))
      .withColumn("sum_in_cg", sum($"count").over(windowIFA_CG))
      .withColumn("cb_rank", row_number().over(windowIFAObCbCount))
      .where(col("cb_rank") === 1)
      .withColumn("perc_in_block", ($"sum_in_cb" / $"sum_ifa").cast("float"))
      .withColumn("perc_in_group", ($"sum_in_cg" / $"sum_ifa").cast("float"))
      .select("ifa", "perc_cell", "census_block_id", "census_group_id", "perc_in_block", "perc_in_group", "hour_days", "cell_only_days_count", "lat", "lng")
      .join(modalCarriers, $"co_ifa" === $"ifa", "left")
      .drop("co_ifa")

    val filterOut = filterBroadbandSurvivors(config.highConfPath, config.hcPrevDate, blocked, hcThisDate).as[CellOnly]
      .cache()

    val dataRepartition = filterOut.repartition(config.outputRepartition.getOrElse(DEFAULT_OUT_PART))

    val outTSL = TimeSeriesLocation
      .ofYmdDatePartitions(config.outPath)
      .build

    UriDFTableRW.fromStr(outTSL.partition(config.hcDate)).writeWithHistory(dataRepartition.toDF(), SaveMode.ErrorIfExists, Seq(config))
  }

  private def findModalCarrierForIfas(config: CellOnlyConfig, hcIfasRemoved: DataFrame, ifaCellOnlyIfas: Dataset[Row])(implicit spark: SparkSession) = {
    import spark.implicits._

    val windowModalCarrier = Window.partitionBy("ifa").orderBy(
      $"date_count".desc, $"night_obs".desc, $"total_obs".desc
    )

    hcIfasRemoved.where(lower($"connection_type") === "cellular")
      .join(ifaCellOnlyIfas.select($"ifa" as "co_ifa"), $"ifa" === $"co_ifa", "left")
      .withColumn("days_from_hc", datediff(lit(config.hcDate), $"t_local"))
      .withColumn("is_night", LocationFilters.isHouseholdHour($"t_local"))
      .groupBy("ifa", "carrier")
      .agg(
        countDistinct("days_from_hc").alias("date_count"),
        sum(when(col("is_night"), 1).otherwise(0)).alias("night_obs"),
        count(lit(1)).alias("total_obs")
      )
      .select($"ifa" as "co_ifa", $"carrier", row_number().over(windowModalCarrier).alias("rank"))
      .where(col("rank") === 1)
      .drop("rank").cache()
  }

  private[installbase] def cellOnlyIfas(allNightIfaObs: Dataset[Row], hcDate: LocalDate, cell_only_cut_off: Float, min_number_days_ifa_seen: Int)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    allNightIfaObs
      .withColumn("hour", hour($"t_local"))
      .withColumn("days_from_hc", datediff(lit(hcDate), $"t_local"))
      .withColumn("hour_day", $"days_from_hc" + ($"hour" / 100.0))
      .groupBy("ifa")
      .agg(
        countDistinct(when(lower($"connection_type") === "cellular", $"hour_day")) as "cell_hour_day",
        countDistinct(col("hour_day")) as "hour_days",
        countDistinct(when(lower($"connection_type") === "cellular", $"days_from_hc")).alias("cell_only_days_count")
      )
      .withColumn("perc_cell", ($"cell_hour_day" / $"hour_days").cast("float"))
      .where($"perc_cell" >= cell_only_cut_off)
      .where($"cell_only_days_count" >= min_number_days_ifa_seen)
  }

  private[installbase] def antiJoinHc(ifasInHc: DataFrame, mmJoined: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    val result = mmJoined
      .join(ifasInHc, Seq("ifa"), JoinType.leftAnti)
      .select(
        $"mm.ifa" as "ifa",
        $"mm.carrier" as "carrier",
        $"mm.connection_type" as "connection_type",
        $"obs.t_local",
        $"obs.ip",
        $"obs.location.lat",
        $"obs.location.lng",
        $"obs.location_type",
        $"obs.accuracy",
        $"obs.gps_speed"
      )

    result
  }
}