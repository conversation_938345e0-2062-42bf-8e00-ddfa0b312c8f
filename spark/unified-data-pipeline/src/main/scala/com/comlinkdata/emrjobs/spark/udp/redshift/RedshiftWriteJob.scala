package com.comlinkdata.emrjobs.spark.udp.redshift

import com.comlinkdata.largescale.commons.RedshiftUtils.RedshiftConfig
import com.comlinkdata.largescale.commons.{RedshiftUtils, SparkJob, SparkJobRunner}
import org.apache.spark.sql.{SaveMode, SparkSession}

/**
  * @param source            S3 source data location.
  *                          Do not add partitioned paths (even with wildcards),
  *                          unless you want partitioned columns to be excluded from the output
  * @param filter            Valid Spark SQL condition to filter the source input.
  *                          Use this to filter specific partition values,
  *                          while still retaining the partition columns in the output
  * @param redshiftTableName Target Redshift table name, with schema
  * @param saveMode          Save mode to use for Redshift writing.
  *                          Defaults to Append.
  *                          Please know what you're doing before using Overwrite.
  * @param preAction         Pre-action Redshift statement to execute.
  *                          Use this to clean up existing data
  * @param redshiftConfig    Redshift configuration to use
  */
case class RedshiftWriteConfig(
  source: String,
  filter: Option[String],
  redshiftTableName: String,
  saveMode: Option[String],
  preAction: Option[String],
  redshiftConfig: RedshiftConfig
)

object RedshiftWriteJob extends SparkJob(RedshiftWriteRunner)

object RedshiftWriteRunner extends SparkJobRunner[RedshiftWriteConfig] {
  override def runJob(config: RedshiftWriteConfig)(implicit spark: SparkSession): Unit = {
    val source = spark.read.parquet(config.source)
    val data = config.filter.map(source.filter).getOrElse(source)
    RedshiftUtils.redshiftWrite(
      data = data,
      tableName = config.redshiftTableName,
      saveMode = config.saveMode.map(SaveMode.valueOf).getOrElse(SaveMode.Append),
      preActionQuery = config.preAction)(config.redshiftConfig)
  }
}
