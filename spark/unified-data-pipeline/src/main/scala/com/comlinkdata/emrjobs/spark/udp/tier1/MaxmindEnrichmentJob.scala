package com.comlinkdata.emrjobs.spark.udp.tier1

import com.comlinkdata.emrjobs.spark.udp.maxmind.MaxmindIngestionRunner.maxMaxmindAgeDays
import com.comlinkdata.emrjobs.spark.udp.tier1.MaxmindSchema._
import com.comlinkdata.largescale.commons.PartitionType.DatePartitionType
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, SparkJobRunner, SparkJob, LocalDateRange}
import com.comlinkdata.largescale.schema.udp.tier0.UdpRaw
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.comlinkdata.largescale.udp._
import com.typesafe.scalalogging.LazyLogging

import java.net.URI
import java.sql.{Timestamp, Date}
import org.apache.spark.sql.{SparkSession, Dataset, DataFrame, SaveMode}

case class MaxmindEnrichmentConfig(
  datasource: ComlinkdataDatasource,
  sourcePath: URI,
  destinationPath: URI,
  maxmindConnectionPath: URI,
  maxmindIspPath: URI,
  dateRangeOpt: Option[LocalDateRange],
  maxDaysToRun: Int
)

object MaxmindSchema {
  case class IspRow(
    iprange_min: Ip,
    iprange_max: Ip,
    network: String,
    isp: Option[String],
    organization: Option[String],
    autonomous_system_number: Option[String],
    autonomous_system_organization: Option[String],
    mobile_country_code: Option[String],
    mobile_network_code: Option[String])

  case class Isp(
    network: String,
    isp: Option[String],
    organization: Option[String],
    autonomous_system_number: Option[String],
    autonomous_system_organization: Option[String],
    mobile_country_code: Option[String],
    mobile_network_code: Option[String])

  object Isp {
    def apply(row: IspRow): Isp = Isp(
      row.network,
      row.isp,
      row.organization,
      row.autonomous_system_number,
      row.autonomous_system_organization,
      row.mobile_country_code,
      row.mobile_network_code)
  }

  case class ConnectionTypeRow(
    iprange_min: Ip,
    iprange_max: Ip,
    network: String,
    connection_type: String)

  case class ConnectionType(
    network: String,
    connection_type: String)

  object ConnectionType {
    def apply(row: ConnectionTypeRow): ConnectionType = ConnectionType(
      network = row.network,
      connection_type = row.connection_type
    )
  }

  case class EnrichedUdp(
    date: Date,
    ifa: Ifa,
    ip: Option[Ip],
    useragent: Option[String],
    carrier: Option[String],
    newcarrier: Option[String],
    latitude: Option[Float],
    longitude: Option[Float],
    locationtype: Option[String],
    suspiciouslocation: Option[Boolean],
    devicetype: Option[String],
    make: Option[String],
    model: Option[String],
    connectiontype: Option[String],
    datetime: Timestamp,
    appid: Option[String],
    appname: Option[String],
    os: Option[String],
    accuracy: Option[Float],
    gps_speed: Option[Float],
    place_name: Option[String],
    place_id: Option[Long],
    category: Option[String],
    country_iso3: Option[String],
    localdatetime: Option[Timestamp],
    network: Option[String],
    organization: Option[String],
    autonomous_system_number: Option[String],
    autonomous_system_organization: Option[String],
    mobile_country_code: Option[String],
    mobile_network_code: Option[String])

  object EnrichedUdp {
    def apply(raw: UdpRaw, ispData: Option[Isp], connectionTypeData: Option[ConnectionType]): EnrichedUdp = EnrichedUdp(
      date = raw.date,
      ifa = raw.ifa,
      ip = raw.ip,
      useragent = raw.useragent,
      carrier = ispData.flatMap(_.isp) orElse raw.carrier,
      newcarrier = raw.newcarrier,
      latitude = raw.latitude,
      longitude = raw.longitude,
      locationtype = raw.locationtype,
      suspiciouslocation = raw.suspiciouslocation,
      devicetype = raw.devicetype,
      make = raw.make,
      model = raw.model,
      connectiontype = connectionTypeData.map(_.connection_type) orElse raw.connectiontype,
      datetime = raw.datetime,
      appid = raw.appid,
      appname = raw.appname,
      os = raw.os,
      accuracy = raw.accuracy,
      gps_speed = raw.gps_speed,
      place_name = raw.place_name,
      place_id = raw.place_id,
      category = raw.category,
      country_iso3 = raw.country_iso3,
      localdatetime = raw.localdatetime,
      network = ispData.map(_.network) orElse connectionTypeData.map(_.network),
      organization = ispData.flatMap(_.organization),
      autonomous_system_number = ispData.flatMap(_.autonomous_system_number),
      autonomous_system_organization = ispData.flatMap(_.autonomous_system_organization),
      mobile_country_code = ispData.flatMap(_.mobile_country_code),
      mobile_network_code = ispData.flatMap(_.mobile_network_code))
  }
}

object MaxmindEnrichmentJob extends SparkJob(MaxmindEnrichmentRunner)

object MaxmindEnrichmentRunner extends SparkJobRunner[MaxmindEnrichmentConfig] with LazyLogging {
  private def readMaxmind(path: URI, range: LocalDateRange, ipVersion: Int)(implicit spark: SparkSession): DataFrame = {
    val maxmindSource = TimeSeriesLocation
      .ofDatePartitions(path)
      .withPartition("ipversion", ipVersion)
      .build
      .partitionsExistingBetween(range)
      .maxBy(_._1.toEpochDay)._2
    logger.info(s"Reading maxmind isp table from $maxmindSource.")
    spark.read
      .option(ReadOpts.csv.inferSchema, "true")
      .option(ReadOpts.csv.header, "true")
      .option(ReadOpts.basePath, path.toString)
      .csv(maxmindSource)
  }

  private def maxmindIspRangeMap(df: DataFrame)(implicit spark: SparkSession): RangeMap[Isp] = {
    import spark.implicits._
    val data = df
      .transform(Cidr.convertCidrToIpRange("network", dropCidrCol = false))
      .as[IspRow]
      .collect
      .map(row => new RangeMap.Entry(row.iprange_min, row.iprange_max, Isp(row)))
    new RangeMap(data)
  }

  private def maxmindConnectionRangeMap(df: DataFrame)(implicit spark: SparkSession): RangeMap[ConnectionType] = {
    import spark.implicits._
    val data = df
      .transform(Cidr.convertCidrToIpRange("network", dropCidrCol = false))
      .as[ConnectionTypeRow]
      .collect
      .map(row => new RangeMap.Entry(row.iprange_min, row.iprange_max, ConnectionType(row)))
    new RangeMap(data)
  }

  def maxmindEnrichment(
    isp4Map: RangeMap[Isp],
    isp6Map: RangeMap[Isp],
    connectionType4Map: RangeMap[ConnectionType],
    connectionType6Map: RangeMap[ConnectionType]
  )(source: Dataset[UdpRaw])(implicit spark: SparkSession): Dataset[EnrichedUdp] = {
    import spark.implicits._
    source.map { raw =>
      val ispData = raw.ip.flatMap { ip =>
        val ispMap =
          if (ip.length == 4) Some(isp4Map)
          else if (ip.length == 16) Some(isp6Map)
          else None
        ispMap.flatMap(map => Option(map.find(ip)))
      }
      val connectionTypeData = raw.ip.flatMap { ip =>
        val connectionTypeMap =
          if (ip.length == 4) Some(connectionType4Map)
          else if (ip.length == 16) Some(connectionType6Map)
          else None
        connectionTypeMap.flatMap(map => Option(map.find(ip)))
      }
      EnrichedUdp(raw, ispData, connectionTypeData)
    }
  }

  override def runJob(config: MaxmindEnrichmentConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    val tsIn = TimeSeriesLocation
      .ofDatasource(config.sourcePath, config.datasource, DatePartitionType("pdate"))
      .withPartition(config.datasource)
      .build
    val tsOut = TimeSeriesLocation
      .ofYmdDatePartitions(config.destinationPath)
      .withPartition(config.datasource)
      .build
    config.dateRangeOpt.getOrElse(getRange(config.maxDaysToRun, tsIn, tsOut))
      .foreach { day =>
        val partitions = getInputPartitions(day, tsIn)
        logger.info(s"Reading source partitions $partitions for day $day")
        val source = spark.read
          .option(ReadOpts.basePath, tsIn.source.toString)
          .parquet(partitions: _*)
          .as[UdpRaw]
        val maxmindRange = LocalDateRange.of(day.minusDays(maxMaxmindAgeDays), day.minusDays(1))
        logger.info(s"Reading Maxmind data for range $maxmindRange")
        val mmIsp4 = maxmindIspRangeMap(readMaxmind(config.maxmindIspPath, maxmindRange, 4))
        val mmIsp6 = maxmindIspRangeMap(readMaxmind(config.maxmindIspPath, maxmindRange, 6))
        val mmConn4 = maxmindConnectionRangeMap(readMaxmind(config.maxmindConnectionPath, maxmindRange, 4))
        val mmConn6 = maxmindConnectionRangeMap(readMaxmind(config.maxmindConnectionPath, maxmindRange, 6))
        val dest = tsOut.validOutputPartitionOrThrow(day)
        logger.info(s"Writing to $dest")
        source
          .transform(maxmindEnrichment(mmIsp4, mmIsp6, mmConn4, mmConn6))
          .write
          .mode(SaveMode.ErrorIfExists)
          .parquet(dest)
      }
  }
}
