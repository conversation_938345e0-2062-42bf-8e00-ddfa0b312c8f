package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.largescale.commons._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql._
import org.apache.spark.sql.expressions.{Window, UserDefinedFunction}
import org.apache.spark.sql.functions._

import java.net.URI
import java.sql.Date
import java.time.{LocalTime, LocalDate}
import scala.collection.mutable
import com.comlinkdata.largescale.commons.RichDate.{localDateOrdering, toRichDate}
import com.comlinkdata.largescale.schema.udp.{IpVec, Ip}
import com.comlinkdata.largescale.schema.udp.installbase.{UdpHousehold, IPSequence}


/**
  * Configs for UdpHouseholdSetJob
  * @param ipSequencePath input from ipSequenceSetJob
  * @param householdOutPath output from this job
  * @param startDate start day to optional only run certain days (if not given will check input for days not yet output)
  * @param endDate end day to optional only run certain days (if not given will check input for days not yet output)
  * @param maxDaysToRun optional cap the number days to run at once
  */
case class UdpHouseholdSetConfig(
  ipSequencePath: URI,
  householdOutPath: URI,
  startDate: Option[LocalDate],
  endDate: Option[LocalDate],
  maxDaysToRun: Option[Int],
  repartition: Option[Int] = Some(400)
)

object UdpHouseholdSet extends SparkJob(UdpHouseholdSetRunner)

object UdpHouseholdSetRunner
  extends SparkJobRunner[UdpHouseholdSetConfig]
    with SparkConstants
    with LazyLogging {

  private val TTL = 14

  override def runJob(config: UdpHouseholdSetConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val tslInput = TimeSeriesLocation
      .ofYmdDatePartitions(config.ipSequencePath)
      .build

    val tslOutput = TimeSeriesLocation
      .ofYmdDatePartitions(config.householdOutPath)
      .build

    if(!tslInput.existsAndHasData) {
      logger.info(s"No input data found. IP Sequence hasn't completed successfully yet.")
      return
    }

    val startDate =
      if(tslOutput.existsAndHasData) {
        tslOutput.latestDate.plusDays(1)
      } else {
        config.startDate.getOrElse(
          tslInput.earliestDate
        )
      }

    val endDate = config.endDate.getOrElse(
     tslInput.latestDate
    )

    logger.info(s"Running IP Pattern Match for date: $startDate to $endDate")

    val daysToRun: Seq[LocalDate] = LocalDateRange.of(startDate, endDate)

    if (daysToRun.isEmpty) throw new IllegalArgumentException("No days to run. Is upstream working?")

    val daysToRunCapped = if(config.maxDaysToRun.isDefined) daysToRun.take(config.maxDaysToRun.get) else daysToRun

    for (day <- daysToRunCapped) {
      println(s"${LocalTime.now()}: starting day: $day")
      val dsCurrIPSequence: Dataset[IPSequence] = spark.read
        .option("basepath", config.ipSequencePath.toString)
        .parquet(tslInput.partition(day))
        .as[IPSequence]

      val dsPrevUdpHousehold: Dataset[UdpHousehold] = if (day.isAfter(startDate)){
        spark.read
          .option("basepath", config.householdOutPath.toString)
          .parquet(tslOutput.partition(day.minusDays(1)))
          .as[UdpHousehold]
      } else {
        spark.emptyDataset[UdpHousehold]
      }

      val dsHH: Dataset[UdpHousehold] = processDay(dsCurrIPSequence, dsPrevUdpHousehold, startDate, day)
      val fullOutPath = tslOutput.partition(day)

      val dataRepartition = if(config.repartition.isDefined) {
        dsHH.repartition(config.repartition.get)
      } else {
        dsHH
      }

      dataRepartition
        .drop("year", "month", "day")
        .write.mode(SaveMode.ErrorIfExists)
        .parquet(fullOutPath)
    }
  }

  def processDay(
    dsCurrIPSequence: Dataset[IPSequence],
    dsPrevUdpHousehold: Dataset[UdpHousehold],
    startDate: LocalDate,
    processDate: LocalDate
  )(implicit spark: SparkSession): Dataset[UdpHousehold] = {
    import spark.implicits._

    val oldData: Dataset[UdpHousehold] = dsPrevUdpHousehold.as("prev")
      .join(dsCurrIPSequence.as("curr"), Seq("ifa"), "leftanti")
      .filter(datediff(lit(processDate), $"prev.ip_max_date") < TTL)
      .as[UdpHousehold]

    val joined = dsCurrIPSequence
      .withColumn("date", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))
      .withColumn("map", map(date_format($"date", "yyyy-MM-dd"), $"ip"))
      .as("curr")
      .join(dsPrevUdpHousehold.as("prev"), Seq("ifa"), "left")
      // select statement to label these columns for the next part
      .select(
        $"ifa",
        $"curr.ip" as "curr_ip",
        $"curr.carrier" as "carrier",
        $"curr.map" as "map",
        $"curr.date" as "date",
        $"curr.ip_min_date" as "curr_ip_min_date",
        $"date" as "ip_max_date",
        $"prev.ip" as "prev_ip",
        $"prev.household_id" as "prev_household_id",
        $"prev.carrier" as "prev_carrier",
        $"prev.is_hh_split" as "prev_is_hh_split",
        $"prev.potential_switch_ip" as "prev_potential_switch_ip",
        $"prev.ip_min_date" as "prev_ip_min_date",
        $"prev.global_ip_min_date" as "prev_global_ip_min_date",
        $"prev.days_seen_count" as "prev_days_seen_count",
        $"prev.ip_sequence_raw_history" as "prev_ip_sequence_raw_history",
        $"curr.year",
        $"curr.month",
        $"curr.day"
      )

    val combinedSequences = joined
      .withColumn("ip_sequence_raw_history", udfCombineMap($"prev_ip_sequence_raw_history", $"map"))
      .withColumn("ip_sequence_cleansed_map", udfCleanseSequence(startDate)($"date", $"ip_sequence_raw_history"))
      .withColumn("ip_sequence", udfSortedIpSequence($"ip_sequence_cleansed_map"))
      .transform(bestIp)

    val withDates = combinedSequences
      .withColumn("potential_switch_ip", $"curr_ip")
      .withColumn("ip_min_date", when($"ip" === $"prev_ip", $"prev_ip_min_date").otherwise($"curr_ip_min_date"))
      .withColumn("local_ip_min_date", min($"ip_min_date").over(Window.partitionBy($"ip")))
      .withColumn("global_ip_min_date",
        when($"ip" === $"prev_ip" && $"local_ip_min_date" > $"prev_global_ip_min_date", $"prev_global_ip_min_date")
          .otherwise($"local_ip_min_date"))
      .withColumn("days_seen_count", datediff($"date", $"ip_min_date"))

    val dfHousehold: Dataset[UdpHousehold] = withDates
      .transform(getHouseholdId($"prev_household_id", $"prev_ip", $"ip", $"global_ip_min_date"))
      .select(
        $"ifa",
        $"ip",
        $"household_id",
        $"carrier",
        $"is_hh_split",
        $"potential_switch_ip",
        $"global_ip_min_date",
        $"ip_min_date",
        $"ip_max_date",
        $"days_seen_count",
        $"ip_sequence_raw_history",
        $"ip_sequence_cleansed_map",
        $"ip_sequence")
      .withColumn("year", lit(processDate.getYearString))
      .withColumn("month", lit(processDate.getMonthValueString))
      .withColumn("day", lit(processDate.getDayOfMonthString))
      .as[UdpHousehold]

    dfHousehold.unionByName(oldData)
  }

  /**
    * Get the latest non null value from ip_sequence_cleansed_map to display as the "best ip"
    * @param df dataframe containing ip_sequence_cleansed_map, an output from UDF cleanseSequence
    * @param spark spark implicit
    * @return Same dataframe with a new ip column
    */
  def bestIp(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    val bestIpWindow = Window
      .partitionBy($"ifa") //Probably not necessary
      .orderBy($"is_ip_null", to_date($"seq_date").desc_nulls_last)

    df
      .select($"*", explode($"ip_sequence_cleansed_map").as(Seq("seq_date", "ip")))
      .withColumn("is_ip_null", when($"ip".isNull, 1).otherwise(0))
      .withColumn("rank", row_number().over(bestIpWindow))
      .filter($"rank" === 1)
      .drop("seq_date", "rank")
  }

  /**
    * Used to calculate a household id based on two booleans:
    *   - is_hh_split - Check to see if a household contains a new ip
    *   - is_same_ip - Check to see if current ip (ip) == prev.ip
    *
    * Example if we have a stable house (parent's house) with a college student that has a primary residence
    *   and off residence (parent's house).
    *
    *
    * Generate is_hh_split by grouping on old hhid, and selecting distinct new ips:
    * ifa | IP | HHID | prev ip | prev hhid | distinct IP by old hhid
    * 1   | B | B     | A       | A         | 2
    * 2   | A | A     | A       | A         | 2
    *
    * ifa | IP | HHID | prev ip | prev hhid | distinct IP | is_hh_split | is_ip_same |
    * 1   | B | B     | A       | A         | 2           | true        | False      | 1 is moving over to V because household is split and not on old ip
    * 2   | A | A     | A       | A         | 2           | true        | True       | 2 is keeping X because a.ip = b.ip
    *
    * surviving_ip | is_hh_split | old.ip == new.ip |
    * F            | T           | T                | parent example -> keep previous hhid
    * F            | T           | F                | college example -> use new hhid
    * F            | F           | T                | no change -> keep previous hhid
    * F            | F           | F                | IP expire -> keep previous hhid
    * T            | T           | T                | survivor has precedence, use survivor_hhid
    * T            | T           | F                | survivor has precedence, use survivor_hhid
    * T            | F           | T                | survivor has precedence, use survivor_hhid
    * T            | F           | F                | survivor has precedence, use survivor_hhid
    *
    * @param prevHH previous dataset's household id (grouped on)
    * @param prevIP previous dataset's ip
    * @param currHH current dataset's household id - usually generated on the fly (new)
    * @param currIP current dataset's ip (distinct counted)
    * @param df DataFrame that is two sequential household days (prev and current) joined together on IFA.
    *           Should contain the following columns:
    *             - prev_household_id - previous household id
    *             - prev_ip - previous ip
    *             - global_ip_min_date - used for generating new household id
    *             - ip - best IP and used for generating new household id
    * @param spark spark implicit
    * @return DataFrame with two new columns:
    *           - is_hh_split - boolean required to calculate household_id
    *           - household_id - new household id based on rules set above
    */
  def getHouseholdId(
    prevHH: Column,
    prevIP: Column,
    currIP: Column,
    ipMinDate: Column
  )(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    val survivingIP: DataFrame = df
      .filter(prevIP === currIP)
      .withColumn("rank", row_number().over(Window.partitionBy(currIP).orderBy(prevHH)))
      .select(prevHH as "survivor_household_id", currIP as "survivor_ip")

    val survivingIPs = survivingIP
      .filter($"rank" === 1)

    df
      .withColumn("curr_household_id", newHouseholdId(currIP, ipMinDate))
      .withColumn("distinct_ip", when(prevHH.isNull, 0).otherwise(approx_count_distinct(currIP).over(Window.partitionBy(prevHH))))
      .withColumn("is_hh_split", when($"distinct_ip" > 1, true).otherwise(false))
      .join(survivingIPs, currIP === $"survivor_ip", "left")
      .withColumn("household_id",
         when($"survivor_household_id".isNotNull, $"survivor_household_id")
        .when($"is_hh_split" === true && prevIP =!= currIP, $"curr_household_id")
        .when(prevHH.isNull, $"curr_household_id") // new hh id if previous data doesn't have household
        .otherwise(prevHH))
      .drop("curr_household_id", "distinct_ip")
  }

  def newHouseholdId(exprs: Column*): Column = unhex(sha2(concat(exprs:_*), 512))

  /**
    * Given a map[Dates, Ip], ensure ips are sorted by dates ascending
    * @param input map of [Dates, Ip]
    * @return ip array sorted by dates
    */
  def sortedIpSequence(input: mutable.Map[String, Ip]): Array[Ip] = {
    input
      .toSeq
      .sortBy(struct => Date.valueOf(struct._1).toLocalDate) // Imported richDate ordering, so use it
      .map(_._2) // map only the values { (k, v) => v }
      .toArray[Ip]
  }

  /**
    * Wrapper for sortedIpSequence
    * @return
    */
  def udfSortedIpSequence: UserDefinedFunction =
    udf((input: mutable.Map[String, Ip]) => sortedIpSequence(input))

  /**
    * todo: implement broadband's carrier churn algo
    * Given a sequence of IPs, attempt to "clean" the values.
    * Iterate through each day between startDate and endDate -> day
    *   1. Get IP from input hashmap (day -> ip) using day as key
    *   2. Check if day is in history and if there is a switch between current and previous index
    *     a. (day is in history) Replace from index i to current index with IP and set lock. Nothing before lockIndex can be modified.
    *     b. (day not in history) Save day -> index into history map (day -> index)
    * @param startDate Startdate of dataset
    * @param endDate current days value
    * @param input sequence of ips
    * @return cleaned sequence of ips
    */
  def cleanseSequence(startDate: LocalDate, endDateAsDate: Date, input: mutable.Map[String, Ip]): Map[String, Ip] = {
    val endDate: LocalDate = endDateAsDate.toLocalDate
    val dates: Seq[LocalDate] = (startDate to endDate).toSeq
    var lockDate: LocalDate = dates.head.minusDays(1)
    val history: mutable.Map[IpVec, LocalDate] = mutable.Map.empty[IpVec, LocalDate]
    var result: mutable.Map[LocalDate, Ip] = mutable.Map.empty[LocalDate, Ip]

    for (day <- dates) {
      val ip: Ip = input.getOrElse(day.toString, null)
      val lastIp: Ip = input.getOrElse(day.minusDays(1).toString, null)
      val switch: Boolean = ip != null && (lastIp == null || ip.toVector != lastIp.toVector)
      result += (day -> ip)

      if (switch) {
        val historyLastDate: LocalDate = history.getOrElse(ip.toVector, null)
        if (historyLastDate != null && day.toEpochDay - historyLastDate.toEpochDay < 30 && historyLastDate.isAfter(lockDate)) {
          lockDate = day
          val newPartialResult: Map[LocalDate, Ip] = (historyLastDate to day).toSeq.map(iDay => Map(iDay -> ip)).reduce(_ ++ _)
          result = result ++ newPartialResult
          history.clear()
        }
        if (ip != null) history += (ip.toVector -> day)
      }
    }
    result
      .map{ case (key, value) => key.toString -> value }
      .toMap[String, Ip]
  }

  /**
    * Wrapper for udfCleanseSequence
    * @return
    */
  def udfCleanseSequence: LocalDate => UserDefinedFunction =
    (startDate: LocalDate) => udf((endDate: Date, input: mutable.Map[String, Ip]) => cleanseSequence(startDate, endDate, input))

  def combineMap(history: mutable.Map[String, Ip], input: mutable.Map[String, Ip]): mutable.Map[String, Ip] = {
    if (history == null) input
    else if (input == null) history
    else history ++ input
  }

  /**
    * Wrapper for combineMap
    * @return udf
    */
  def udfCombineMap: UserDefinedFunction =
    udf((history: mutable.Map[String, Ip], input: mutable.Map[String, Ip]) => combineMap(history, input))

  def debug(): Unit ={
    // todo: this is for comparison checking. We should be using an Athena view instead
    //    val hhUpdate1_1: DataFrame = ifaWithLatestIP_carrier_hhid
    //      .as("new")
    //      .join(dfDay0WithHHID.as("old"), Seq("ifa"), "left")
    //      .select(
    //        $"ifa",
    //        $"new.ip" as "ip_end",
    //        $"old.ip" as "ip_start",
    //        $"new.hhid" as "hhid_end", // this is a newly generated uuid
    //        $"old.hhid" as "hhid_start", // todo: how do we persist this uuid over time
    //        $"new.carrier" as "carrier_end",
    //        $"old.carrier" as "carrier_start")
    //
    //    val hhUpdate2 = hhUpdate1_1
    //      .join(hhUpdate1Splits.as("splits"), $"hhid_end" === $"splits.hhid", "left")
    //      .withColumn("is_hh_split", when($"splits.hhid".isNull, lit(false)).otherwise(lit(true)))
    //      .withColumn("hhid_end", when($"is_hh_split" === true && $"ip_start" =!= $"ip_end", $"hhid_end")
    //        .otherwise($"hhid_start"))
    //      .withColumn("status_summary",
    //        when($"ip_start".isNull, lit("new data"))
    //          .when($"ip_start" === $"ip_end", lit("update or lapse"))
    //          .when($"carrier_start" =!= $"carrier_end", lit("carrier switch"))
    //          .when($"carrier_start" === $"carrier_end" && $"ip_start" =!= $"ip_end", lit("refresh"))
    //          .otherwise(lit("unknown")))
    //      .as[UdpHousehold]

    //    hhUpdate2
    //      .write.mode(SaveMode.ErrorIfExists)
    //      .parquet(tslOutput.partition(config.endDate))
  }
}