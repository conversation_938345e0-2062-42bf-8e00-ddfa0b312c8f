package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.emrjobs.spark.udp.installbase.LocationFilters
import com.comlinkdata.emrjobs.spark.udp.installbase.LocationFilters.{IFA_PER_IP_CUTOFF, locCountsFromLocTs, locWithHourFromObs}
import com.comlinkdata.largescale.commons.{SparkJobRunner, SparkConstants, SparkJob}
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.comlinkdata.largescale.schema.udp.installbase.{IpToLocCounts, IpToLocCountsExploded, LocTs, IfaToIpWithObs, IpToLocationNoStats}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SparkSession, Dataset}

import java.net.URI
import java.time.LocalDate

case class IpToLocationsConfig (
  ifaObsPath: URI,
  maxmindPath: URI,
  carrierLookupPath: URI,
  carrierLookupFwPath: URI,
  ipv6TruncationLookupPath: URI,
  hourWeightLookupPath: URI,
  dailyIpCarrierPath: Option[URI],
  datasources: Seq[ComlinkdataDatasource],
  startDate: LocalDate,
  endDate: LocalDate,
  repartition: Option[Int],
  ipToLocationOutPath: URI
) extends IfaObsWithMMConfig {
  override def getRepartition: Int = {
    repartition.getOrElse(30)
  }

  override def getOutPath: URI = ipToLocationOutPath
}

case class IfaToLocationNoStats(
  ifa: Ifa,
  obs_count: Long,
  night_obs_count: Long,
  ips: Seq[Ip],
  ip_count: Int,
  hours_seen: Seq[Int],
  locations: Seq[LocTs]
)

object IpToLocationsJob extends SparkJob(IpToLocationsRunner)

object IpToLocationsRunner
  extends SparkJobRunner[IpToLocationsConfig]
    with SparkConstants
    with LazyLogging {
  override def runJob(config: IpToLocationsConfig)(implicit spark: SparkSession): Unit = {
    GenericPreFilterRunner.run(
      IfaObsMMNightTime.dsToIfaObsWithObs(config),
      computeLocationSingleDS,
      (ds: Dataset[IpToLocationNoStats]) => ds.transform(aggLocation).transform(explodeCounts),
      config,
      config
    )
  }

  def computeLocationSingleDS(ipToIfa: Dataset[IfaToIpWithObs])(implicit spark: SparkSession): Dataset[IpToLocationNoStats] = {
    import spark.implicits._

    val approxCountIfa = ipToIfa
      .filter($"consolidated_id".isNotNull) // only run locations for tracked carriers
      .select(
        $"ifa",
        $"ip",
        $"obs_count",
        $"night_obs_count",
        $"observations",
        approx_count_distinct("ifa").over(Window.partitionBy($"ip")) as "approx_ifas")
      .where($"approx_ifas" < IFA_PER_IP_CUTOFF)

    val locFilter = approxCountIfa.select($"ifa",
      $"ip",
      $"obs_count",
      $"night_obs_count",
      explode($"observations") as "obs")
      .transform(LocationFilters.locationFilters($"ifa", $"ip", $"obs_count", $"night_obs_count"))

    val ipToLocAgg = locFilter.groupBy("ip", "obs_count", "night_obs_count").agg(
      collect_set("ifa") as "ifas",
      flatten(collect_list($"observations")) as "obs")
      .withColumn("locations", locWithHourFromObs($"obs")).drop("obs")
      .withColumn("hours_seen", array_distinct(transform($"locations.ts", x => hour(x))))

    val ipToLoc = ipToLocAgg
      .withColumn("ifa_count", size($"ifas"))
      .select(
        $"ip",
        $"obs_count",
        $"night_obs_count",
        $"ifas",
        $"ifa_count",
        $"hours_seen",
        $"locations").as[IpToLocationNoStats]
    ipToLoc
  }

  def aggLocation(allDsIn: Dataset[IpToLocationNoStats])(implicit spark: SparkSession): Dataset[IpToLocCounts] = {
    import spark.implicits._

    allDsIn
      .groupBy("ip")
      .agg(
        sum($"obs_count") as "obs_count",
        sum($"night_obs_count") as "night_obs_count",
        array_distinct(flatten(collect_set("hours_seen"))) as "hours_seen",
        array_distinct(flatten(collect_set("ifas"))) as "ifas",
        flatten(collect_list("locations")) as "locations")
      .withColumn("ifa_count", size($"ifas"))
      .withColumn("loc_counts", locCountsFromLocTs($"locations"))
      .as[IpToLocCounts]
  }

  def explodeCounts(raw: Dataset[IpToLocCounts])(implicit spark: SparkSession): Dataset[IpToLocCountsExploded] = {
    import spark.implicits._
    raw
      .select(IpToLocCounts.cols ++ Seq(explode($"loc_counts")): _*)
      .withColumnRenamed("key", "point")
      .withColumnRenamed("value", "point_obs")
      .select(IpToLocCountsExploded.cols: _*)
      .as[IpToLocCountsExploded]
  }

  def run(ifaToIpAllDsWithObs: Seq[Option[Dataset[IfaToIpWithObs]]])(implicit spark: SparkSession): Dataset[IpToLocCountsExploded] = {
    val union = ifaToIpAllDsWithObs
      .filter(_.isDefined).map(_.get) // remove empty or undefined datasets
      .map(computeLocationSingleDS) // transform - major work here
      .reduce(_ unionByName _) // aggregate all datasets together and remove datasource

    val locs = union
      .transform(aggLocation) // further aggregation
      .transform(explodeCounts)

    locs
  }
}
