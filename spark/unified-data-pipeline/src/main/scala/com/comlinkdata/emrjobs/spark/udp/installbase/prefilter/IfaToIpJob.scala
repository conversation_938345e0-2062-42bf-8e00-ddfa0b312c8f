package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.largescale.commons.{SparkJobRunner, SparkConstants, SparkJob}
import com.comlinkdata.largescale.schema.udp.installbase.{IfaToIpWithObs, IfaToIp}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.sum
import org.apache.spark.sql.{SparkSession, Dataset}

import java.net.URI
import java.time.LocalDate

case class IfaToIpConfig(
  ifaObsPath: URI,
  maxmindPath: URI,
  carrierLookupPath: URI,
  carrierLookupFwPath: URI,
  ipv6TruncationLookupPath: URI,
  hourWeightLookupPath: URI,
  dailyIpCarrierPath: Option[URI],
  datasources: Seq[ComlinkdataDatasource],
  startDate: LocalDate,
  endDate: LocalDate,
  repartition: Option[Int],
  ifaToIpOutPath: URI
) extends IfaObsWithMMConfig {
  override def getRepartition: Int = {
    repartition.getOrElse(40)
  }
  override def getOutPath: URI = ifaToIpOutPath
}

object IfaToIpJob extends SparkJob(IfaToIpRunner)

object IfaToIpRunner
  extends SparkJobRunner[IfaToIpConfig]
    with SparkConstants
    with LazyLogging {
  override def runJob(config: IfaToIpConfig)(implicit spark: SparkSession): Unit ={
    import spark.implicits._

    GenericPreFilterRunner.run(
      IfaObsMMNightTime.dsToIfaObsNoObs(config),
      x => x,
      (ds: Dataset[IfaToIpWithObs]) => ds.as[IfaToIp].transform(aggIfaToIp),
      config,
      config
    )
  }

  def aggIfaToIp(readIn: Dataset[IfaToIp])(implicit spark: SparkSession): Dataset[IfaToIp] = {
    import spark.implicits._
    readIn
      .groupBy("ip", "ifa", "carrier", "consolidated_id", "connection_type", "sp_platform", "organization", "autonomous_system_number")
      .agg(
        sum($"obs_count") as "obs_count",
        sum($"night_obs_count") as "night_obs_count",
        sum($"household_obs_count") as "household_obs_count",
        sum($"household_weighted_obs_count") as "household_weighted_obs_count",
        sum($"business_weighted_obs_count") as "business_weighted_obs_count"
      ).as[IfaToIp]
  }
}
