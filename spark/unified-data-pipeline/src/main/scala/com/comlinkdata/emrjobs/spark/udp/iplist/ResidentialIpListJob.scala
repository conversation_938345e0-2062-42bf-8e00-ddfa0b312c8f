package com.comlinkdata.emrjobs.spark.udp.iplist

import com.comlinkdata.emrjobs.spark.udp.iplist.IpListCommon._
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils.implicits._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SaveMode, SparkSession}

case class ResidentialIpListConfig(
  ifaIpAggPath: String,
  carrierLookup: String,
  distinctIfaThreshold: Int,
  nightWeekendThreshold: Float,
  ipListPath: String,
  quarter: Option[String]
)

object ResidentialIpListJob extends SparkJob(ResidentialIpListRunner)

object ResidentialIpListRunner extends SparkJobRunner[ResidentialIpListConfig] with SparkConstants with LazyLogging {
  override def runJob(config: ResidentialIpListConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    require(CldFileUtils.newBuilder.forUri(config.ifaIpAggPath).build.isDirectory(config.ifaIpAggPath))
    require(CldFileUtils.newBuilder.forUri(config.carrierLookup).build.exists(config.carrierLookup))
    val (lastIpList, destPath, dr) = loadLastIpList(config.ipListPath, config.quarter)
    val ifaIpAgg = loadValidInRange(config.ifaIpAggPath, dr, TimeSeriesLocation.ofYmdDatePartitions(_))
      .withColumn("ip", lower(hex('ip)))
      .withColumn("carrier", lower('carrier))
    val carriers = spark.read
      .parquet(config.carrierLookup)
      .select(lower('mw_carrier).as("carrier"))
      .distinct
    val residential = ifaIpAgg
      .withColumn("night_weekend", when('night_ind || dayofweek('local_date).isin(1, 7), 100).otherwise(0))
      .join(carriers, Seq("carrier"), "leftsemi")
      .filter('connection_type =!= "Cellular")
      .groupBy('ip, 'carrier)
      .agg(
        countDistinct('ifa).as("distinct_ifas"),
        (sum('night_weekend) / count('night_weekend)).as("night_weekend_rate"))
      .filter('distinct_ifas < config.distinctIfaThreshold
        && 'night_weekend_rate >= config.nightWeekendThreshold)
      .join(lastIpList, Seq("ip"), "leftanti")
      .select(unhex('ip).as("ip"), 'carrier)
    val hdfsTempPath = "hdfs:/ip-lists/"
    residential.write.mode(SaveMode.Overwrite).parquet(hdfsTempPath)
    spark.read.parquet(hdfsTempPath)
      .distinct
      .repartition(50)
      .write.mode(SaveMode.ErrorIfExists)
      .parquet(destPath)
  }
}
