package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.emrjobs.spark.udp.installbase.LocationFilters.isTruncatedIP
import com.comlinkdata.largescale.commons.{LocalDateRange, Utils}
import com.comlinkdata.largescale.schema.udp.lookup.IPv6Lookup
import com.comlinkdata.largescale.schema.udp.tier1.{UdpDailyIfa, UdpDailyIfaNoYmd}
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.functions.{broadcast, coalesce, explode, lit}

import java.net.URI
import java.time.LocalDate

class IfaObsWithMM(
  ifaObsPath: URI,
  ipv6TruncationLookupPath: URI,
  mmOrProxy: MaxMindOrProxy.Base
)(implicit spark: SparkSession) {

  private lazy val dsIPv6LookupCache = IPv6Lookup.read(ipv6TruncationLookupPath).cache()

  def read(runDate: LocalDate): DataFrame = {
    import spark.implicits._

    val ipNotNull = UdpDailyIfa
        .readExisting(ifaObsPath, LocalDateRange.of(runDate), Seq(mmOrProxy.getDs))
        .as[UdpDailyIfaNoYmd]
        .select($"ifa", explode($"observations") as "obs")
        .filter($"obs.t_local".isNotNull) //Very few records have null t_local but if this is a problem we should use utc and gps to compute t_local
        .filter($"obs.ip".isNotNull)
        .filter($"obs.ip" =!= Utils.ipStringToBinary("*******")) // This is a frequent bad IP

    val mm = mmOrProxy.read(runDate)

    val obsAndMM = ipNotNull.join(mm, $"obs.ip" === $"ip").as("mm")

    val mmJoined =
      obsAndMM
        .join(broadcast(dsIPv6LookupCache.as("ipv6")), Seq("carrier"), "left")
        .filter(!isTruncatedIP($"obs.ip", coalesce($"ipv6.index", lit(-1))))

    mmJoined.select("mm.*")
  }
}


