package com.comlinkdata.emrjobs.spark.udp.tier1

import com.comlinkdata.largescale.schema.udp.tier0.UdpRaw
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.apache.spark.sql.types.LongType
import org.apache.spark.sql.{SparkSession, Column, Dataset, DataFrame}

import java.sql.Date

case class SegmentReportData(
  ds: String,
  year: String,
  month: String,
  day: String,
  partition_date: Date,
  segment: String,
  metric: String,
  records: Long,
  distinct_ifas: Long,
  distinct_ips: Option[Long]
)

case class SkinnySegmentReportData(
  date: Date,
  segment: String,
  metric: String,
  records: Long,
  distinct_ifas: Long,
  distinct_ips: Option[Long]
)

object SegmentReportData {

  import com.comlinkdata.largescale.commons.customFunctions
  import org.apache.spark.sql.functions._

  def fromSkinnyTransform(ds: ComlinkdataDatasource)(skinny: Dataset[SkinnySegmentReportData])(implicit spark: SparkSession): Dataset[SegmentReportData] = {
    import spark.implicits._
    skinny
      .withColumn("ds", lit(ds.toString))
      .withColumnRenamed("date", "partition_date")
      .transform(customFunctions.addYmdCols(col("partition_date")))
      .as[SegmentReportData]
  }

}

object UdpSegmentReportRunner {

  import org.apache.spark.sql.functions._

  def createReport(datasource: ComlinkdataDatasource, df: Dataset[UdpRaw])(implicit spark: SparkSession): List[Dataset[SegmentReportData]] = {
    import spark.implicits._
    val sep = "||"

    def segmentName(a: String, b: String): Column = lit(a + sep + b)

    val segmentColumns: List[Column] = classOf[SkinnySegmentReportData].getDeclaredFields.map(_.getName).toList.map(col)

    def metricName(a: String, b: String): Column = {
      concat(coalesce(col(a), lit("")), lit(sep), coalesce(col(b), lit("")))
    }

    def rollupOs: DataFrame = df
      .groupBy("date", "os")
      .agg(
        countDistinct("ifa").as("distinct_ifas"),
        count("*").as("records")
      )
      .withColumn("segment", lit("os"))
      .withColumn("distinct_ips", lit(null) cast LongType)
      .withColumn("metric", $"os")

    def rollupOsLocationType: DataFrame = df.rollup("date", "os", "locationtype")
      .agg(
        countDistinct("ifa").as("distinct_ifas"),
        countDistinct("ip").as("distinct_ips"),
        count("*").as("records")
      )
      .withColumn("segment", segmentName("os", "locationtype"))
      .withColumn("metric", metricName("os", "locationtype"))

    def rollupMakeModel: DataFrame = df.rollup("date", "make", "model")
      .agg(
        countDistinct("ifa").as("distinct_ifas"),
        countDistinct("ip").as("distinct_ips"),
        count("*").as("records")
      )
      .withColumn("segment", segmentName("make", "model"))
      .withColumn("metric", metricName("make", "model"))

    def rollupApp: DataFrame = df.rollup("date", "appname", "appid")
      .agg(
        countDistinct("ifa").as("distinct_ifas"),
        countDistinct("ip").as("distinct_ips"),
        count("*").as("records")
      )
      .withColumn("segment", segmentName("appname", "appid"))
      .withColumn("metric", metricName("appname", "appid"))

    def rollupLoc: DataFrame = df.rollup("date", "locationtype")
      .agg(
        countDistinct("ifa").as("distinct_ifas"),
        count("*").as("records")
      )
      .withColumn("segment", lit("locationtype"))
      .withColumn("distinct_ips", lit(null) cast LongType)
      .withColumnRenamed("locationtype", "metric")

    def rollupCon: DataFrame = df.rollup("date", "connectiontype")
      .agg(
        countDistinct("ifa").as("distinct_ifas"),
        count("*").as("records")
      )
      .withColumn("segment", lit("connectiontype"))
      .withColumn("distinct_ips", lit(null) cast LongType)
      .withColumnRenamed("connectiontype", "metric")

    def rollupCarrier: DataFrame = df.rollup("date", "carrier", "newcarrier")
      .agg(
        countDistinct("ifa").as("distinct_ifas"),
        countDistinct("ip").as("distinct_ips"),
        count("*").as("records")
      )
      .withColumn("segment", segmentName("carrier", "newcarrier"))
      .withColumn("metric", metricName("carrier", "newcarrier"))

    List(rollupOs, rollupOsLocationType, rollupApp, rollupLoc, rollupCon, rollupCarrier, rollupMakeModel)
      .map(_.select(segmentColumns:_*)) // ensure uniform ordering of columns
      .map(_.filter($"date".isNotNull)) // date == null is one of the rollup candidates))
      .map(_.as[SkinnySegmentReportData])
      .map(_.transform(SegmentReportData.fromSkinnyTransform(datasource)).distinct)
  }

  def segmentBy(colName: String)(df: Dataset[UdpRaw])(implicit spark: SparkSession): Dataset[SkinnySegmentReportData] = {
    import spark.implicits._
    df
      .withColumnRenamed(colName, "metric")
      .groupBy($"date", $"metric")
      .agg(countDistinct("ifa") as "distinct_ifas", countDistinct("ip") as "distinct_ips", count("*") as "records")
      .withColumn("segment", lit(colName))
      .as[SkinnySegmentReportData]
  }
}
