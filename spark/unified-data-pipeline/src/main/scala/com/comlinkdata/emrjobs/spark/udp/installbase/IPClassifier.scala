package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.largescale.commons.{SparkJob<PERSON><PERSON><PERSON>, Utils, SparkConstants, SparkJob}
import com.comlinkdata.largescale.schema.udp.Ip
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SparkSession, Column, Dataset, DataFrame}
import org.apache.spark.sql.functions._
import org.apache.spark.ml.classification.{RandomForestClassificationModel, RandomForestClassifier}
import org.apache.spark.ml.feature.VectorAssembler
import org.apache.spark.ml.linalg.Vector
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.types.IntegerType

import java.net.URI
import java.time.LocalDate

case class ClassifiedIP (
  ip: Ip,
  device_count_3_day_min: Long,
  device_count_2_day_min: Long,
  ifa_count: Long,
  home_ifa_hrs_count: Long,
  bus_hour_weight: Double,
  prediction: Double,
  probability: Vector
)
object ClassifiedIP extends Utils.reflection.ColumnNames[ClassifiedIP]

/**
  * todo: other inputs like tapad
  * todo: verify what udp.iplist is doing
  * todo: this is not runnable -- We might have to rely on notebooks to generate this output
  * Generates a new Random Forest Model to classify IPs as residential or business
  * @param ifaToIpPath Input S3: Output from PreFilterJob
  * @param ipClassifierModel Output S3: RandomForest Model
  * @param startDate Start date
  * @param startDate End date
  */
case class IPClassifierConfig(
  ifaToIpPath: URI,
  ipClassifierModel: URI,
  startDate: LocalDate,
  endDate: LocalDate
)

object IPClassifier extends SparkJob(IPClassifierRunner)

object IPClassifierRunner
  extends SparkJobRunner[IPClassifierConfig]
    with SparkConstants
    with LazyLogging {

  final val RESIDENTIAL = 0.0
  final val BUSINESS = 1.0

  //order is important and should contain columns from ClassifiedIP
  private val ivs = Array(
    "ifa_count",
    "home_ifa_hrs_count",
    "device_count_2_day_min",
    "device_count_3_day_min",
    "bus_hour_weight"
  )

  //todo: test this
  override def runJob(config: IPClassifierConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    // step 0: gather inputs via TSL
    val dfIfaObs: DataFrame = spark.emptyDataFrame
    val dsIfa: Dataset[IfaToIpYMD] = spark.emptyDataset[IfaToIpYMD]
    val dfAllDatesIfa: DataFrame = spark.emptyDataFrame
    val dfIfaMaster: DataFrame = spark.emptyDataFrame
    val tapad: DataFrame = spark.emptyDataFrame

    // step 1: run all classifiers with inputs from previous step
    val classifiers: Array[DataFrame] = Array(
      classifierA(dfIfaObs), //todo: this can be dsIfa instead
      classifierD(tapad), //not used
      classifierG(dsIfa, dfAllDatesIfa), //not used
      classifierJ(dfIfaObs, dfIfaMaster), //not used, also maybe dsIfa instead. Only need obs for "reveal02"
      classifierCHI(dsIfa)
    )

    // step 2: use classifier A as base and left join all others and select the features
    val data: DataFrame = classifiers
      .tail //using head as accumulator
      .foldLeft(classifiers.head)( (acc, df) => acc.join(df, Seq("ip"), "left"))

    // step 3: apply basic residential or business label and filter out bad data
    data
      .withColumn("label", labelCondition)
      .filter($"label" =!= 2)
      .filter(
        $"ifa_count".isNotNull &&
        $"carrier_count".isNotNull &&
        $"home_ifa_hrs_count".isNotNull &&
        $"tapad_flag".isNotNull &&
        $"device_count_2_day_min".isNotNull &&
        $"device_count_3_day_min".isNotNull &&
        $"device_count".isNotNull &&
        $"bus_hour_weight".isNotNull &&
        $"res_hour_weight".isNotNull)
      .withColumn("asn", $"asn".cast(IntegerType))

    // step 4: model training
    val assembler = new VectorAssembler()
      .setInputCols(ivs)
      .setOutputCol("features")
    val featureDf = assembler.transform(data)
    val splitFeatureDf = featureDf.randomSplit(Array(0.67, 0.33), 42)
    val train = splitFeatureDf(0)
    val test = splitFeatureDf(1)

    //todo: other requirements here to ensure same tree depths every time?
    val lr = new RandomForestClassifier()
      .setLabelCol("label")
      .setFeaturesCol("features")
      .setImpurity("gini")
      //.setMaxDepth(3) // maybe sqrt(ivs.length).roundUp?
      .setNumTrees(20)
      //.setFeatureSubsetStrategy("auto")
      .setSeed(42)

    val model = lr.fit(train)
    model.save(config.ipClassifierModel.toString)
  }

  /**
    * This is a very basic approach to determining if a record is a business or residential ip.
    * Checks carrier and organization to see if it's a business
    * Checks how often this record appears on the weekend
    * @param spark implicit Spark Session
    * @return 0 - Residential
    *         1 - Business
    *         2 - Neither (Throw away these records)
    */
  def labelCondition(implicit spark: SparkSession): Column = {
    import spark.implicits._
    when(lower($"carrier").like("%business%") || lower($"organization") && !$"weekend_pct" >= .5, 1)
      .when(!lower($"carrier").like("%business%") && $"weekend_pct" >= .5, 0)
      .otherwise(2)
  }

  /**
    * count distinct ifas over a period of days
    * count distinct night / household days over a period of days
    * @param dsIfaObs prefilter output - ifa observations that has been filtered by day
    * @param spark
    * @return
    */
  def classifierA(dsIfaObs: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    dsIfaObs
      .withColumn("date", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))
      .withColumn("hh_dates", when($"night_ind" === true, $"date"))
      .groupBy("ip")
      .agg(
        approx_count_distinct("ifa") as "ifa_count",
        approx_count_distinct($"hh_dates") as "hh_dates"
      )
  }

  /**
    * https://comniscient.atlassian.net/wiki/spaces/RD/pages/1450999811/Understanding+the+Benefit+of+Incorporating+TapAd+into+BBCV+2.0
    * Specifically using Tapad to find residential ips
    * warning: this breaks the concept of UDP!
    * @param tapad raw master tapad data (tapad_prod.tapad_master_table)
    * @param spark
    * @return
    */
  def classifierD(tapad: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    tapad
      .select("tp_ip")
      .distinct()
      .withColumn("ip", $"tp_ip") //todo: convert to hex, also why does tapad have raw ips?
  }

  /**
    * todo: label the class instead of Athena table
    *
    * @param dsIfa Output from prefilter
    * @param dfAllDatesIfa udp_prod.all_dates_ifa_set
    * @param spark
    * @return
    */
  def classifierG(dsIfa: Dataset[IfaToIpYMD], dfAllDatesIfa: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    val ifaAndCarriers = dfAllDatesIfa
      .groupBy("ifa")
      .agg(max($"carrier") as "carrier")

    dsIfa
      .select($"ifa", $"ip")
      .distinct()
      .join(ifaAndCarriers.as("b"), Seq("ifa"), "left")
      .groupBy("ip")
      .agg(approx_count_distinct($"carrier") as "carrier_count")
  }

  /**
    *
    * @param dsIfaObs b2b_broadband
    * @param ifaMaster device_master_table_prod.ifa_master_table
    * @param spark
    * @return
    */
  def classifierJ(dsIfaObs: DataFrame, dfIfaMaster: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    dsIfaObs.as("a")
      .filter($"ds" === "reveal02")
      .join(dfIfaMaster.as("b"), Seq("ifa"), "left")
      .groupBy("a.ip")
      .agg(
        approx_count_distinct($"a.ifa") as "device_count",
        approx_count_distinct($"b.final_carrier_name") as "carrier_count"
      )
  }

  /**
    * CHI is not an acronym! It's a combination of classifierC, classifierH, and classifierI for optimization.
    * classifierC - Home hours count column - distinct count of days that have atleast 1 home hour observation.
    * classifierH - bus_hour_weight column - Aggregated prefilter column. Sum(Business hourly weight (0 ~ 1) * observation count)
    * classifierI - dates per ifa column - counts days an ifa was active on an IP. Any IP that has a device(ifa) that lives more than 2 or 3 days are distinctly counted
    *
    * @param dsIfa prefilter output that holds multiple dates worth of data
    * @param spark
    * @return DataFrame of features to be joined on IP back to main dataframe
    */
  def classifierCHI(dsIfa: Dataset[IfaToIpYMD])(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    dsIfa
      .withColumn("date", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))
      .withColumn("dates_per_ifa", approx_count_distinct($"date").over(Window.partitionBy($"ip", $"ifa")))
      .groupBy($"ip")
      .agg(
        approx_count_distinct(when($"dates_per_ifa" >= 3, $"ifa")) as "device_count_3_day_min",
        approx_count_distinct(when($"dates_per_ifa" >= 2, $"ifa")) as "device_count_2_day_min",
        approx_count_distinct($"ifa") as "ifa_count",
        approx_count_distinct(when($"household_obs_count" >= 1, $"date")) as "home_ifa_hrs_count",
        sum($"business_weighted_obs_count") as "bus_hour_weight"
      )
  }

  /**
    * https://comniscient.atlassian.net/wiki/spaces/RD/pages/3150053484/Residential+Business+Classifier+for+IP+Chaining+Modeling
    * Using heuristics generated in IPClassifier (one time) to determine which IPs are considered residential
    * @param dsIfa all ifa observations, aggregated at the ifa level
    * @param spark SparkSession
    * @return ips classified as residential (0.0) or business (1.0)
    */
  def ipClassifier(ipClassifierModel: RandomForestClassificationModel)(dsIfa:Dataset[IfaToIpYMD])(implicit spark: SparkSession): Dataset[ClassifiedIP] = {
    import spark.implicits._

    val dfIPMetrics = dsIfa.transform(classifierCHI)

    val assembler = new VectorAssembler()
      .setInputCols(ivs)
      .setOutputCol("features")
    val dfFeature = assembler.transform(dfIPMetrics)
    val data = ipClassifierModel
      .transform(dfFeature)
      .drop("features", "rawPrediction")

    data.as[ClassifiedIP]
  }
}
