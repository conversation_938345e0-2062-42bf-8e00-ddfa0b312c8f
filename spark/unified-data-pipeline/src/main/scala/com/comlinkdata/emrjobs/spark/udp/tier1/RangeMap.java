package com.comlinkdata.emrjobs.spark.udp.tier1;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

public class RangeMap<V> implements Serializable {
    public static final class Entry<V> {
        private final BigInteger start;
        private final BigInteger end;
        private final V value;

        public Entry(BigInteger start, BigInteger end, V value) {
            this.start = start;
            this.end = end;
            this.value = value;
        }

        public Entry(final byte[] start, final byte[] end, final V value) {
            this(new BigInteger(start), new BigInteger(end), value);
        }
    }

    private final BigInteger[] keys;
    private final List<V> values;

    public RangeMap(final Entry<V>[] entries) {
        Arrays.sort(entries, Comparator.comparing(e -> e.start));
        keys = new BigInteger[entries.length * 2];
        values = new ArrayList<>(entries.length);
        int index = 0;
        for (final Entry<V> e : entries) {
            if (index > 0 && keys[index - 1].compareTo(e.start) >= 0)
                throw new IllegalStateException("Malformed ranges");
            keys[index++] = e.start;
            keys[index++] = e.end;
            values.add(e.value);
        }
    }

    public V find(final BigInteger key) {
        final int index = Arrays.binarySearch(keys, key, Comparator.naturalOrder());
        if (index >= 0)
            return values.get(index / 2); // IP matches the exact start or end of a range - return the match
        // no exact match found - index contains (-(insertion point) - 1)
        if (-index % 2 == 1)
            return null; // insertion point is between ranges, no match
        return values.get(-index / 2 - 1); // insertion point is within a range
    }

    public V find(final byte[] key) {
        return find(new BigInteger(key));
    }
}
