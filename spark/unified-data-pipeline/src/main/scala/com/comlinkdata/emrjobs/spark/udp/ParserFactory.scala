package com.comlinkdata.emrjobs.spark.udp

import java.util.{List => JList, Map => JMap}
import org.uaparser.scala.Device.DeviceParser
import org.uaparser.scala.OS.OSParser
import org.uaparser.scala.UserAgent.UserAgentParser
import org.yaml.snakeyaml.{Yaml, LoaderOptions}
import org.yaml.snakeyaml.constructor.SafeConstructor

import scala.collection.JavaConverters._

object ParserFactory {
  private def config: Map[String, List[Map[String, String]]] =
    new Yaml(new SafeConstructor(new LoaderOptions()))
      .load(this.getClass.getResourceAsStream("/regexes.yaml"))
      .asInstanceOf[JMap[String, JList[JMap[String, String]]]]
      .asScala.toMap
      .mapValues(_.asScala.toList.map(_.asScala.toMap.filterNot { case (_, value) => value eq null }))

  lazy val osParser: OSParser = OSParser.fromList(config.getOrElse("os_parsers", Nil))

  lazy val userAgentParser: UserAgentParser = UserAgentParser.fromList(config.getOrElse("user_agent_parsers", Nil))

  lazy val deviceParser: DeviceParser = DeviceParser.fromList(config.getOrElse("device_parsers", Nil))
}
