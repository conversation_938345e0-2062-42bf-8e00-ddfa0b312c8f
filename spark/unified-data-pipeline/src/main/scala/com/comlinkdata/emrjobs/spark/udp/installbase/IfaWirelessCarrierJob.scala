package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.largescale.commons.customFunctions.addDateColFromYMD
import org.apache.spark.sql.{Column, Dataset, DataFrame, SparkSession, SaveMode}
import com.comlinkdata.largescale.commons.{SparkJob, UriDFTableRW, LocalDateRange, SparkJobRunner, Utils}
import com.comlinkdata.largescale.schema.udp.installbase.{IfaWirelessCarrierYMD, IfaWirelessCarrier}
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.lookup.{MccMncLookupTable, WirelessCarrierIpBlock, SingleNetworkBrand, AllowedNetworkBrandPair}
import com.comlinkdata.largescale.schema.udp.tier2.{IfaAgg, UdpIfaIpAggYMD}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._

import java.net.URI
import java.time.LocalDate

/**
  *
  * @param datasources list of datasources
  * @param ifaIpAggLocation
  * @param ifaAggLocation
  * @param mccMncLookupLocation
  * @param wirelessCarrierIpBlockLookupLocation
  * @param singleNetworkBrandLocation
  * @param allowedNetworkBrandPairLocation
  * @param startDate
  * @param endDate
  * @param outputLocation
  * @param outputPartitions
  */
case class IfaWirelessCarrierConfig(
  datasources: Seq[ComlinkdataDatasource],
  ifaIpAggLocation: URI,
  ifaAggLocation: URI,
  mccMncLookupLocation: URI,
  wirelessCarrierIpBlockLookupLocation: URI,
  singleNetworkBrandLocation: URI,
  allowedNetworkBrandPairLocation: URI,
  startDate: LocalDate,
  endDate: LocalDate,
  outputLocation: URI,
  outputPartitions: Option[Int]
)

case class SimCarrier(
  ifa: Ifa,
  b_lifetime_sim_carrier_mno: String,
  c_recent_sim_carrier_mno: String,
  d_lifetime_sim_carrier_nonmno: String,
  e_recent_sim_carrier_nonmno: String
)

case class AllField(
  ifa: Ifa,
  network: String,
  network_source: String,
  network_short: String,
  brand: String,
  brand_source: String,
  a_recent_cellular_mno: String,
  b_lifetime_sim_carrier_mno: String,
  c_recent_sim_carrier_mno: String,
  d_lifetime_sim_carrier_nonmno: String,
  e_recent_sim_carrier_nonmno: String,
  g_verizon_reserved_range_dates: Long,
  h_att_reserved_range_dates: Long,
  i_tmo_reserved_range_dates: Long,
  j_total_dates: Long,
)

object IfaWirelessCarrierJob extends SparkJob(IfaWirelessCarrierJobRunner)

object IfaWirelessCarrierJobRunner extends SparkJobRunner[IfaWirelessCarrierConfig] with LazyLogging {

  //todo: I don't like hardcoding this, is there a lookup somewhere? @RND
  val WIRELESS_CARRIER = Seq(
    "Verizon",
    "AT&T Wireless",
    "T-Mobile",
    "Sprint",
    "US Cellular",
    "Cellcom",
    "Carolina West",
    "Cellular One",
    "C Spire"
  )

  def runJob(config: IfaWirelessCarrierConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val ldr: LocalDateRange = LocalDateRange(config.startDate, config.endDate)

    val dsIfaIpAgg = UdpIfaIpAggYMD.read(config.ifaIpAggLocation, ldr, config.datasources)
    val dsIfaAgg = IfaAgg.readLatest(config.ifaAggLocation)._2
    val dsMccMncLookup = MccMncLookupTable.read(config.mccMncLookupLocation)
    val dsSingleNetworkBrand = SingleNetworkBrand.read(config.singleNetworkBrandLocation)
    val dsAllowedNetworkBrandPair = AllowedNetworkBrandPair.read(config.allowedNetworkBrandPairLocation)
    val dsIpBlocks = WirelessCarrierIpBlock.read(config.wirelessCarrierIpBlockLookupLocation)

    val dfIfaAggFiltered = dsIfaAgg.transform(filterIfaAgg(config.startDate, config.endDate))
    val dsSimCarrier = dfIfaAggFiltered.transform(simCarrier(dsMccMncLookup, config.startDate)).cache()

    val dfCellularCarrier = dsIfaIpAgg.transform(cellularCarrier(dsSimCarrier))
    val dfIpRangeCarrier = dsIfaIpAgg.transform(ipRangeCarrier(dsIpBlocks, dsSimCarrier))
    val dfAllCarrierFields = allCarrierFields(dsSimCarrier, dfCellularCarrier, dfIpRangeCarrier)
    val dfCleanSingleNetworkBrands = dfAllCarrierFields.transform(cleanSingleNetworkBrands(dsSingleNetworkBrand))
    val dfAllowedPairs = dfCleanSingleNetworkBrands.transform(cleanRemoveMismatches(dsAllowedNetworkBrandPair))

    val data: Dataset[IfaWirelessCarrier] = dfAllowedPairs.as[IfaWirelessCarrier]

    val dataRepartition: Dataset[IfaWirelessCarrier] = config.outputPartitions match {
      case Some(partitions) => data.repartition(partitions)
      case None => data
    }

    val tsl = IfaWirelessCarrierYMD.tsl(config.outputLocation)
    UriDFTableRW
      .fromStr(tsl.partition(config.endDate))
      .writeWithHistory(
        dataRepartition.toDF(),
        SaveMode.ErrorIfExists,
        Seq(config))

  }

  def filterIfaAgg(
    startDate: LocalDate,
    endDate: LocalDate
  )(dsIfaAgg: Dataset[IfaAgg])(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    dsIfaAgg
      .select(
        $"ifa",
        $"mcc_mnc_freqs",
        exists($"days", (x: Column) => x.between(startDate, endDate)) as "days"
      )
      .filter($"days".contains(lit(true)))
  }


  /**
    * appends the following columns to the entire dataset:
    *  - b_lifetime_sim_carrier_mno - best carrier over the entire period
    *  - c_recent_sim_carrier_mno - most recent carrier
    *  - d_lifetime_sim_carrier_nonmno - best brand over the entire period
    *  - e_recent_sim_carrier_nonmno - most recent brand
    *
    * Will only have either [b, c] (network) or [d, e] (brand)    *
    *
    * @param dsMccMncLookup MccMnc Lookup for carriers and subbrands
    * @param startDate      start date of dataset
    * @param endDate        end date of dataset
    * @param dsIfaAgg       Ifa Agg daily
    * @param spark          spark implicit
    * @return Dataset of Ifa and either best network or best carrier
    */
  def simCarrier(
    dsMccMncLookup: Dataset[MccMncLookupTable],
    startDate: LocalDate
  )(dfIfaAgg: DataFrame)(implicit spark: SparkSession): Dataset[SimCarrier] = {
    import spark.implicits._

    val sim_carrier_breakout = dfIfaAgg
      .select($"*", explode($"mcc_mnc_freqs"))
      .select(
        $"ifa",
        $"key" as "sim_carrier",
        $"key" as "mcc_mnc", // used for joining only
        $"value.last" as "max_date",
        $"value.distinct_days" as "distinct_days")

    val sim_carrier_breakout_standardized = sim_carrier_breakout.alias("breakout").
      join(broadcast(dsMccMncLookup).alias("lookup"), Seq("mcc_mnc"), "left")
      .select(
        $"breakout.ifa",
        $"breakout.max_date",
        $"breakout.distinct_days",
        $"lookup.subbrand" as "brand",
        when($"lookup.subbrand".isNull
          && lower($"lookup.carrier") =!= "unknown"
          && lower($"lookup.carrier") =!= "uknown",
          $"lookup.carrier") as "network")

    val lifetime_sim_carrier_mno_window = Window.partitionBy($"ifa").orderBy($"distinct_days".desc)
    val b_lifetime_sim_carrier_mno = sim_carrier_breakout_standardized
      .filter($"network".isNotNull)
      .withColumn("rank", row_number.over(lifetime_sim_carrier_mno_window))
      .filter($"rank" === 1)
      .drop($"rank")
      .select($"ifa", $"network")

    val c_recent_sim_carrier_mno = sim_carrier_breakout_standardized
      .filter($"network".isNotNull)
      .filter($"max_date" >= startDate)
      .withColumn("rank", row_number.over(lifetime_sim_carrier_mno_window))
      .filter($"rank" === 1)
      .drop($"rank")
      .select($"ifa", $"network")

    val d_lifetime_sim_carrier_nonmno = sim_carrier_breakout_standardized
      .filter($"brand".isNotNull)
      .withColumn("rank", row_number.over(lifetime_sim_carrier_mno_window))
      .filter($"rank" === 1)
      .drop($"rank")
      .select($"ifa", $"brand")

    val e_recent_sim_carrier_nonmno = sim_carrier_breakout_standardized
      .filter($"brand".isNotNull)
      .filter($"max_date" >= startDate)
      .withColumn("rank", row_number.over(lifetime_sim_carrier_mno_window))
      .filter($"rank" === 1)
      .drop($"rank")
      .select($"ifa", $"brand")

    dfIfaAgg.alias("a")
      .join(b_lifetime_sim_carrier_mno.alias("b"), Seq("ifa"), "left")
      .join(c_recent_sim_carrier_mno.alias("c"), Seq("ifa"), "left")
      .join(d_lifetime_sim_carrier_nonmno.alias("d"), Seq("ifa"), "left")
      .join(e_recent_sim_carrier_nonmno.alias("e"), Seq("ifa"), "left")
      .select(
        $"a.ifa",
        $"b.network" as "b_lifetime_sim_carrier_mno",
        $"c.network" as "c_recent_sim_carrier_mno",
        $"d.brand" as "d_lifetime_sim_carrier_nonmno",
        $"e.brand" as "e_recent_sim_carrier_nonmno")
      .as[SimCarrier]
  }

  /**
    *
    * @param dsSimCarrier
    * @param dsIfaIpAgg
    * @param spark
    * @return
    */
  def cellularCarrier(
    dsSimCarrier: Dataset[SimCarrier]
  )(dsIfaIpAgg: Dataset[UdpIfaIpAggYMD])(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    val recent_cellular_mno = dsIfaIpAgg
      .join(dsSimCarrier, Seq("ifa"), "inner")
      .filter(lower($"connection_type") === "cellular")
      .filter(Utils.or(WIRELESS_CARRIER.map(c => $"carrier".rlike(f"(?i)${c.toLowerCase()}")): _*))
      .transform(addDateColFromYMD($"year", $"month", $"day"))
      .groupBy($"ifa", $"carrier")
      .agg(approx_count_distinct($"date") as "distinct_days")

    recent_cellular_mno
      .withColumn("rank", row_number.over(Window.partitionBy($"ifa").orderBy($"distinct_days".desc)))
      .filter($"rank" === 1)
      .drop($"rank")
      .select(
        $"ifa",
        $"carrier" as "a_recent_cellular_mno"
      )
  }

  /**
    * Attach counts of how many times a network appears on a date by using a ip block look up
    *
    * @param dsWirelessCarrierIpBlock ip block look up
    * @param dsSimCarrier             dataset containing ifas to filter on
    * @param dsIfaIpAgg               raw dataset with ifa and ip
    * @param spark                    spark implicit
    * @return dataset with (ifa -> carrier dates)
    */
  def ipRangeCarrier(
    dsWirelessCarrierIpBlock: Dataset[WirelessCarrierIpBlock],
    dsSimCarrier: Dataset[SimCarrier]
  )(dsIfaIpAgg: Dataset[UdpIfaIpAggYMD])(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    val filtered = dsIfaIpAgg
      .filter(lower($"connection_type") === "cellular")
      .filter(length($"ip") === 4)
      .join(dsSimCarrier, Seq("ifa"), "inner")
      .select(UdpIfaIpAggYMD.cols: _*)

    val joined = filtered
      .alias("a")
      .withColumn("ip_string", Utils.ipBinaryToStringColumn($"ip"))
      .join(
        broadcast(dsWirelessCarrierIpBlock).alias("b"),
        lower($"a.carrier").contains(lower($"b.carrier")) && $"ip_string".startsWith($"b.ip_block"),
        "left")
      .select(
        $"a.*",
        lower($"b.carrier") as "carrier_cleansed"
      )

    val aggregated = joined
      .transform(addDateColFromYMD())
      .groupBy($"a.ifa")
      .agg(
        approx_count_distinct(when($"carrier_cleansed" === "verizon", $"date")) as "g_verizon_reserved_range_dates",
        approx_count_distinct(when($"carrier_cleansed" === "at&t", $"date")) as "h_att_reserved_range_dates",
        approx_count_distinct(when($"carrier_cleansed" === "t-mobile" || $"carrier_cleansed" === "sprint", $"date")) as "i_tmo_reserved_range_dates",
        approx_count_distinct($"date") as "j_total_dates"
      )
    aggregated
  }

  /**
    * Combine all three sources into a single table, and create network and brand columns.
    * prefer recent SIM carrier, then recent "Unspecified" MVNO if signal exists, then lifetime SIM MVNO
    * (which is most likely of any assigment to contradict the established MNO)
    *
    * @param dsSimCarrier      Network carriers from sim
    * @param dfCellularCarrier Network carriers from cell
    * @param dfIpRangeCarrier  heuristics to choose carriers from other two dataset
    * @param spark             spark implicit
    * @return ifa -> network and brand
    */
  def allCarrierFields(
    dsSimCarrier: Dataset[SimCarrier],
    dfCellularCarrier: DataFrame,
    dfIpRangeCarrier: DataFrame
  )(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    val joined = dsSimCarrier
      .join(dfCellularCarrier, Seq("ifa"), "left")
      .join(dfIpRangeCarrier, Seq("ifa"), "left")

    joined
      .transform(cleanNetworkField)
      .transform(cleanBrandField)
      .as[AllField]
      .toDF()
  }

  /**
    * Helper function for allCarrierFields that cleanses the sources and create a network column. All subbrand
    * information comes from the MCC/MNC/HNI lookup, it should already be in a standardized string pattern
    *
    * @param df    dataset with raw network names
    * @param spark spark implicit
    * @return dataframe with new columns (network, network_source, network_short)
    */
  private def cleanNetworkField(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    val cleanNetwork = Seq(
      ("verizon", "Verizon Wireless", "vz"),
      ("at&t", "AT&T Wireless", "att"),
      ("att", "AT&T Wireless", "att"),
      ("t-mobile", "T-Mobile Wireless", "tmo"),
      ("tmobile", "T-Mobile Wireless", "tmo"),
      ("sprint", "T-Mobile Wireless", "tmo"),
      ("us cellular", "US Cellular", null),
      ("cellcom", "Cellcom", null),
      ("carolina west", "Carolina West Wireless", null),
      ("cellular one", "Cellular One", null),
      ("c spire", "C Spire Wireless", null),
    ).toDF("network", "network_clean", "network_short")

    df
      .withColumn("network_raw",
        when($"c_recent_sim_carrier_mno".isNotNull, $"c_recent_sim_carrier_mno")
          .when($"a_recent_cellular_mno".isNotNull && $"j_total_dates" >= 10, $"a_recent_cellular_mno")
          .when($"b_lifetime_sim_carrier_mno".isNotNull, $"b_lifetime_sim_carrier_mno"))
      // keep source column
      .withColumn("network_source",
        when($"c_recent_sim_carrier_mno".isNotNull, lit("c_recent_sim_carrier_mno"))
          .when($"a_recent_cellular_mno".isNotNull && $"j_total_dates" >= 10, lit("a_recent_cellular_mno"))
          .when($"b_lifetime_sim_carrier_mno".isNotNull, lit("b_lifetime_sim_carrier_mno")))
      .join(cleanNetwork, lower($"network_raw").contains(cleanNetwork("network")), "left")
      .select(
        df("*"),
        $"network_source",
        coalesce(cleanNetwork("network_clean"), when($"network_raw".isNotNull, "Other MNO")) as "network",
        cleanNetwork("network_short") as "network_short"
      )
  }

  /**
    * Helper function for allCarrierFields that cleanses the sources and create a brand column. Also handles business
    * logic of wiping out the brand Spectrum Wireless if it does not come from a very specific source
    *
    * @param df    dataframe with raw brand names and network names
    * @param spark spark implicits
    * @return dataframe with new columns (brand, brand_source)
    */
  private[installbase] def cleanBrandField(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    df
      .withColumn("brand_raw",
        when($"e_recent_sim_carrier_nonmno".isNotNull, $"e_recent_sim_carrier_nonmno")
          .when($"network_short" === "vz" and $"g_verizon_reserved_range_dates" > 0, lit("Probable Verizon MVNO"))
          //.when($"network_short".isin(Seq("tmo", "spr"):_*) and $"i_tmo_reserved_range_dates" > 0, lit("Probable T-Mobile MVNO"))
          //.when($"network_short" === "att" and $"h_att_Reserved_Range_dates" > 0, lit("Probable AT&T MVNO"))
          .when($"d_lifetime_sim_carrier_nonmno".isNotNull, $"d_lifetime_sim_carrier_nonmno"))
      .withColumn("brand_source_raw",
        when($"e_recent_sim_carrier_nonmno".isNotNull, lit("e_recent_sim_carrier_nonmno"))
          .when($"network_short" === "vz" and $"g_verizon_reserved_range_dates" > 0, lit("Probable Verizon MVNO"))
          //.when($"network_short".isin(Seq("tmo", "spr"):_*) and $"i_tmo_reserved_range_dates" > 0, lit("Probable T-Mobile MVNO")),
          //.when($"network_short" === "att" and $"h_att_Reserved_Range_dates" > 0, lit("Probable AT&T MVNO"))
          .when($"d_lifetime_sim_carrier_nonmno".isNotNull, lit("d_lifetime_sim_carrier_nonmno")))
      // apply spectrum specific business rules
      .withColumn("brand",
        when(lower($"brand_raw") =!= "spectrum wireless", $"brand_raw")
          .when(lower($"brand_raw") === "spectrum wireless" and $"brand_source_raw" === "e_recent_sim_carrier_nonmno", $"brand_raw")
          .otherwise(lit(null)))
      .withColumn("brand_source",
        when(lower($"brand_raw") =!= "spectrum wireless", $"brand_source_raw")
          .when(lower($"brand_raw") === "spectrum wireless" and $"brand_source_raw" === "e_recent_sim_carrier_nonmno", $"brand_source_raw")
          .otherwise(lit(null)))
  }

  /**
    * Find and replace specific edge cases of networks on sim and specific brands
    *
    * @param df
    * @param spark
    * @return
    */
  def cleanSingleNetworkBrands(
    dsSingleNetworkBrand: Dataset[SingleNetworkBrand]
  )(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    val brands = dsSingleNetworkBrand
      .withColumn("use_brand", lit(true))
      .withColumn("network_source", lit("network_implied_by_brand"))
      .withColumn("lower_brand", lower($"brand"))

    df.as("df")
      .withColumn("use_brand", $"brand_source" === "e_recent_sim_carrier_nonmno" or $"network_source" === "b_lifetime_sim_carrier_mno")
      .withColumn("lower_brand", lower($"brand"))
      .join(broadcast(brands).as("single"), Seq("lower_brand", "use_brand"), "left")
      // jank method to keep existing columns and replace two
      .select(
        $"df.*",
        coalesce($"single.network", $"df.network") as "network_clean",
        coalesce($"single.network_source", $"df.network_source") as "network_source_clean")
      .drop("network", "network_source")
      .withColumnRenamed("network_clean", "network")
      .withColumnRenamed("network_source_clean", "network_source")
  }

  /**
    * List is based on the brands observed within this lookup; it may not be completely exhaustive of all brands in
    * the mcc_mnc lookup.
    * TODO - check this and add any missing brand/network rollups.
    * TODO - have someone review this list of brand/network rollups to make sure the correct network(s) are
    * mapped to each brand
    *
    * @param dfAllowedPairs
    * @param df
    * @param spark
    * @return
    */
  def cleanRemoveMismatches(
    dfAllowedPairs: Dataset[AllowedNetworkBrandPair]
  )(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._
    val allowed = df
      .filter($"brand".isNotNull)
      .join(
        broadcast(dfAllowedPairs),
        lower(df("brand")) === lower(dfAllowedPairs("brand")) && lower(df("network")).contains(lower(dfAllowedPairs("network"))),
        "inner")
      .select(df("*"))

    // Big carriers will have null sub brands and will be filtered out by the above inner join
    val mno = df
      .filter($"brand".isNull)
      .filter($"network".isNotNull)

    allowed.unionByName(mno)
  }
}

