package com.comlinkdata.emrjobs.spark.udp.maxmind

import java.net.URI
import java.sql.Date
import java.time.LocalDate
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, SparkJobRunner, SparkJob}
import com.comlinkdata.largescale.schema.udp.tier1.UdpDailyMaxmindNoYmd
import com.comlinkdata.largescale.schema.udp.tier2.{UdpMaxmindSetNoYmd, MaxmindHistoryElement}
import com.comlinkdata.largescale.udp._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.broadcast
import org.apache.spark.sql.{SparkSession, Dataset}


case class MaxmindIpSetConfig(
  maxmindDailyIpSrc: URI,
  datasource: ComlinkdataDatasource,
  maxmindIpSetDest: URI,
  bootstrapDateOpt: Option[LocalDate]
)

object MaxmindIpSetJob extends SparkJob(MaxmindIpSetRunner, forceKryoRegistration = false) {
  override def kryoSerializationClasses = List(
    classOf[UdpMaxmindSetNoYmd],
    classOf[UdpDailyMaxmindNoYmd]
  )
}

object MaxmindIpSetRunner extends SparkJobRunner[MaxmindIpSetConfig] with LazyLogging {
  override def runJob(config: MaxmindIpSetConfig)(implicit spark: SparkSession): Unit = {

    import spark.implicits._

    val inputLocation = TimeSeriesLocation.ofYmdDatePartitions(config.maxmindDailyIpSrc)
      .withPartition(ComlinkdataDatasource.partitionName, config.datasource)
      .withPartition("ipversion", 4)
      .build

    val outputLocation = TimeSeriesLocation.ofYmdDatePartitions(config.maxmindIpSetDest)
      .withPartition(ComlinkdataDatasource.partitionName, config.datasource)
      .withPartition("ipversion", 4)
      .build

    if (config.bootstrapDateOpt.isDefined) require(
      !outputLocation.cldFileUtils.isDirectory(outputLocation.source),
      s"Cannot bootstrap into an existing destination (${outputLocation.source})."
    )

    val pDate = config.bootstrapDateOpt.getOrElse(outputLocation.latestDate.plusDays(1))

    if (!inputLocation.exists(pDate)) {
      logger.warn(s"Processing date $pDate has no corresponding input partition ${outputLocation.partition(pDate)}, exiting.")
      return
    }

    val set: Dataset[UdpMaxmindSetNoYmd] = // read yesterdays set in
      if (config.bootstrapDateOpt.isDefined) spark.emptyDataset[UdpMaxmindSetNoYmd]
      else spark.read.parquet(outputLocation.partition(pDate.minusDays(1))).as[UdpMaxmindSetNoYmd]

    val partitionDate = Date.valueOf(pDate)
    val days: Dataset[UdpDailyMaxmindNoYmd] = spark.read.parquet(inputLocation.partition(pDate)).as[UdpDailyMaxmindNoYmd]
    val newSet = set.joinWith(broadcast(days), set("ip") === days("ip"), JoinType.outer)
      .map {
        case (setRow, null) => setRow // no new information, record stays the same
        case (null, daysRow) => newIpRecord(partitionDate, daysRow)
        case (setRow, daysRow) => updateIpRecord(partitionDate, setRow, daysRow)
      }

    val dest = outputLocation.partition(pDate)
    logger.info(s"Writing new set to $dest.")
    newSet.write.parquet(dest)

  }

  def updateIpRecord(date: Date, set: UdpMaxmindSetNoYmd, row: UdpDailyMaxmindNoYmd): UdpMaxmindSetNoYmd =
    if (set.carrier == row.carrier && set.connection_type == row.connection_type) {
      set.copy( // update last observed
        date_last_observed = date
      )
    } else { // carrier or connection type has changed, update values and add new element to the history
      set.copy(
        date_last_observed = date,
        history = MaxmindHistoryElement(date, row.carrier, row.connection_type) :: set.history,
        date_first_observed = date,
        carrier = row.carrier,
        connection_type = row.connection_type
      )
    }

  def newIpRecord(day: Date, row: UdpDailyMaxmindNoYmd): UdpMaxmindSetNoYmd = {
    UdpMaxmindSetNoYmd(
      ip = row.ip,
      history = List(
        MaxmindHistoryElement(day, row.carrier, row.connection_type)
      ),
      date_ip_first_observed = day,
      date_last_observed = day,
      date_first_observed = day,
      carrier = row.carrier,
      connection_type = row.connection_type
    )
  }


}
