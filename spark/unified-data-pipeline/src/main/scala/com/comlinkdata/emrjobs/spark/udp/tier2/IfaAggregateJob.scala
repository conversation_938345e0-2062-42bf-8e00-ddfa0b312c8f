package com.comlinkdata.emrjobs.spark.udp.tier2

import com.comlinkdata.emrjobs.spark.udp.ParserFactory
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, SparkJobRunner, Utils, SparkJob}
import com.comlinkdata.largescale.schema.udp.tier2.{IfaAgg, IfaSet, DayStat}
import com.comlinkdata.largescale.schema.udp.{Ifa, tier2}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.typesafe.scalalogging.LazyLogging

import java.net.URI
import java.sql.Date
import java.time.format.DateTimeFormatter
import java.time.{LocalDateTime, LocalDate}
import org.apache.spark.broadcast.Broadcast
import org.apache.spark.sql._
import org.apache.spark.sql.functions.{concat_ws, max, explode}
import org.uaparser.scala.Device.DeviceParser
import org.uaparser.scala.OS
import org.uaparser.scala.OS.OSParser

import scala.math.Ordering.Implicits.infixOrderingOps

case class IfaAggregateConfig(
  datasources: List[ComlinkdataDatasource],
  ifaSetPath: URI,
  mccMncLookupPath: URI,
  ifaAggPath: URI,
  window: Int
)

case class IfaAggHolder(
  ifa: Ifa,
  days: IndexedSeq[Date],
  ds_apps: Map[String, DayStat],
  mcc_mnc_freqs: Map[String, DayStat],
  model_code_freqs: Map[String, DayStat],
  user_agent_string_freqs: Map[String, DayStat],
  parsed_os_freqs: Map[String, DayStat],
  device_family: Option[String],
  device_brand: Option[String],
  device_model: Option[String],
  date: String
)

object IfaAggHolder extends Utils.reflection.ColumnNames[IfaAggHolder]

object IfaAggregateJob extends SparkJob(IfaAggregateRunner)

object IfaAggregateRunner extends SparkJobRunner[IfaAggregateConfig] with LazyLogging {

  import com.comlinkdata.largescale.commons.RichDate._

  def readLookup(lookup: DataFrame, key: Column, value: Column)(implicit spark: SparkSession): Map[String, String] = {
    import spark.implicits._
    lookup.select(key, value).sort(key).as[(String, String)].collect.filter(_._2 != null).filterNot(_._2.isEmpty).toMap
  }

  def osToString(os: OS): String = (os.major, os.minor, os.patch) match {
    case (Some(major), Some(minor), Some(patch)) => s"${os.family} $major.$minor.$patch"
    case (Some(major), Some(minor), None) => s"${os.family} $major.$minor"
    case (Some(major), None, _) => s"${os.family} $major"
    case (None, _, _) => os.family
  }

  def createAggHolder(ifaSet: IfaSet, deviceParser: Broadcast[DeviceParser], osParser: Broadcast[OSParser]): IfaAggHolder = {
    val (family, brand, model) =
      if (ifaSet.user_agent_string_freqs.isEmpty) (None, None, None)
      else {
        val device = deviceParser.value.parse(ifaSet.user_agent_string_freqs.toSeq.minBy(_._2.first)._1)
        (Some(device.family), device.brand, device.model)
      }
    IfaAggHolder(
      ifa = ifaSet.ifa,
      days = ifaSet.days,
      ds_apps = ifaSet.apps.map { case (app, stat) => s"${ifaSet.ds}:$app" -> stat },
      mcc_mnc_freqs = ifaSet.mcc_mnc_freqs,
      model_code_freqs = ifaSet.model_code_freqs,
      user_agent_string_freqs = ifaSet.user_agent_string_freqs,
      parsed_os_freqs = ifaSet.user_agent_string_freqs.toSeq
        .groupBy { case (ua, _) => osToString(osParser.value.parse(ua)) }
        .mapValues(_.map(_._2).reduceLeft((left, right) => mergeStat(left, Some(right)).copy(distinct_days = 0))),
      device_family = family,
      device_brand = brand,
      device_model = model,
      // if you change the below logic, also review the maxDate calculation in runJob(), as they have to match
      date = ifaSet.days.last.toLocalDate.toString
    )
  }

  def mergeStat(left: DayStat, rightOption: Option[DayStat]): DayStat = rightOption match {
    case Some(right) => DayStat(
      first = left.first.min(right.first),
      last = left.last.max(right.last),
      distinct_days = left.distinct_days + right.distinct_days)
    case _ => left
  }

  def mergeStats(left: Map[String, DayStat], right: Map[String, DayStat]): Map[String, DayStat] =
    (left.toSeq ++ right.toSeq)
      .foldLeft(Map.empty[String, DayStat]) {
        case (map, (key, stat)) => map + (key -> mergeStat(stat, map.get(key)))
      }

  def mergeAgg(agg1: IfaAggHolder, agg2: IfaAggHolder): IfaAggHolder = agg1.copy(
    days = (agg2.days.toSet ++ agg1.days.toSet).toIndexedSeq.sorted,
    ds_apps = mergeStats(agg1.ds_apps, agg2.ds_apps),
    mcc_mnc_freqs = mergeStats(agg1.mcc_mnc_freqs, agg2.mcc_mnc_freqs),
    model_code_freqs = mergeStats(agg1.model_code_freqs, agg2.model_code_freqs),
    user_agent_string_freqs = mergeStats(agg1.user_agent_string_freqs, agg2.user_agent_string_freqs),
    parsed_os_freqs = mergeStats(agg1.parsed_os_freqs, agg2.parsed_os_freqs),
    date = agg2.days.last.toLocalDate.max(agg1.days.last.toLocalDate).toString
  )

  def getFirstLast(stats: Map[String, DayStat], lookup: Map[String, String], window: Int): (Option[String], Option[String]) = {
    val combinedStats = stats.flatMap { case (key, stat) => lookup.get(key).map(_ -> stat) }.toSeq
    if (combinedStats.isEmpty) (None, None)
    else {
      val firstDateMax = combinedStats.map(_._2.first).min.toLocalDate.plusDays(window)
      val lastDateMin = combinedStats.map(_._2.last).max.toLocalDate.minusDays(window)
      val earliestKey = combinedStats
        .filter(_._2.first.toLocalDate.isBefore(firstDateMax))
        .sortBy { case (_, DayStat(first, _, dd)) => (-dd, first.getTime) }
        .headOption.map(_._1)
      val latestKey = combinedStats
        .filter(_._2.last.toLocalDate.isAfter(lastDateMin))
        .sortBy { case (_, DayStat(_, last, dd)) => (-dd, -last.getTime) }
        .headOption.map(_._1)
      (earliestKey, latestKey)
    }
  }

  def getLastOs(stats: Map[String, DayStat], window: Int): Option[String] =
    if (stats.isEmpty) None
    else {
      val lastDateMin = stats.map(_._2.last).max.toLocalDate.minusDays(window)
      stats.toSeq
        .filter(_._2.last.toLocalDate.isAfter(lastDateMin))
        .map(_._1).sorted.reverse.headOption
    }

  def createAgg(agg: IfaAggHolder, carriers: Map[String, String], mvnos: Map[String, String], maxDate: Date, window: Int): IfaAgg = {
    val (firstMno, lastMno) = getFirstLast(agg.mcc_mnc_freqs, carriers, window)
    val (firstMvno, lastMvno) = getFirstLast(agg.mcc_mnc_freqs, mvnos, window)
    tier2.IfaAgg(
      ifa = agg.ifa,
      days = agg.days,
      ds_apps = agg.ds_apps,
      mcc_mnc_freqs = agg.mcc_mnc_freqs,
      first_mno = firstMno,
      first_mvno = firstMvno,
      last_mno = lastMno,
      last_mvno = lastMvno,
      last_os = getLastOs(agg.parsed_os_freqs, window),
      device_family = agg.device_family,
      device_brand = agg.device_brand,
      device_model = agg.device_model,
      model_code_freqs = agg.model_code_freqs,
      user_agent_string_freqs = agg.user_agent_string_freqs,
      parsed_os_freqs = agg.parsed_os_freqs,
      year = maxDate.toLocalDate.getYearString,
      month = maxDate.toLocalDate.getMonthValueString,
      day = maxDate.toLocalDate.getDayOfMonthString
    )
  }


  private def now = DateTimeFormatter.ofPattern("YYYYMMddHHmmss").format(LocalDateTime.now)

  def runJob(config: IfaAggregateConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    val hdfsHolderPath = "hdfs:/holder/"
    val osParser = spark.sparkContext.broadcast(ParserFactory.osParser)
    val deviceParser = spark.sparkContext.broadcast(ParserFactory.deviceParser)
    val maxDate = config.datasources.foldLeft(Option.empty[LocalDate]) { (maxDate, ds) =>
      val tsIn = TimeSeriesLocation.ofYmdDatePartitions(config.ifaSetPath).withPartition(ds).build
      val ifaSet = spark.read.option(ReadOpts.basePath, config.ifaSetPath.toString)
        .parquet(tsIn.partition(tsIn.latestDate)).as[IfaSet]
      val maxDsDate = ifaSet.select(explode('days).as("date")).agg(max('date)).as[Date].collect.head.toLocalDate
      logger.info(s"Max IFA Set observed date for $ds is $maxDate")
      logger.info(s"Writing $ds intermediate agg to $hdfsHolderPath")
      ifaSet.map(createAggHolder(_, deviceParser, osParser)).write.mode(SaveMode.Overwrite).parquet(s"$hdfsHolderPath/ts=$now")
      Seq(maxDate, Some(maxDsDate)).max
    }.get
    val outputTS = TimeSeriesLocation.ofDatePartitions(config.ifaAggPath).build
    if (outputTS.exists) {
      val lastDate = outputTS.latestDate
      if (!maxDate.isAfter(lastDate)) {
        logger.info(s"No new dates available since $lastDate, exiting")
        return
      }
      val ifaAggPartition = outputTS.partition(lastDate)
      val lastAgg = spark.read.parquet(ifaAggPartition)
        .withColumn("date", concat_ws("-", 'year, 'month, 'day))
        .select(IfaAggHolder.cols: _*)
        .as[IfaAggHolder]
      logger.info(s"Writing previous agg from $ifaAggPartition to $hdfsHolderPath")
      lastAgg.write.mode(SaveMode.Overwrite).parquet(s"$hdfsHolderPath/ts=$now")
    }
    val hdfsAggPath = "hdfs:/agg/"
    logger.info(s"Writing IFA aggregate to $hdfsAggPath")
    spark.read.parquet(hdfsHolderPath).as[IfaAggHolder]
      .groupByKey(_.ifa).mapGroups((_, rows) => rows.reduceLeft(mergeAgg))
      .write.mode(SaveMode.Overwrite).parquet(hdfsAggPath)
    val lookup = spark.read.parquet(config.mccMncLookupPath.toString)
    val carrierBroadcast = spark.sparkContext.broadcast(readLookup(lookup, 'mcc_mnc, 'carrier))
    val mvnoBroadcast = spark.sparkContext.broadcast(readLookup(lookup, 'mcc_mnc, 'subbrand))
    val dateBroadcast = spark.sparkContext.broadcast(maxDate.toDate)
    logger.info(s"Calculating final IFA agg")
    spark.read.parquet(hdfsAggPath).as[IfaAggHolder]
      .map(createAgg(_, carrierBroadcast.value, mvnoBroadcast.value, dateBroadcast.value, config.window))
      .write.mode(SaveMode.ErrorIfExists).parquet(outputTS.partition(maxDate))
  }
}
