package com.comlinkdata.emrjobs.spark.udp.ifaset

import com.comlinkdata.largescale.commons.RichDate._
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.udp.lookup.{DeviceTypeLookup, MccMncLookupTable}
import com.comlinkdata.largescale.schema.udp.tier1.UdpDailyDevice
import com.comlinkdata.largescale.schema.udp.tier2.IfaSet
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Dataset, SaveMode, SparkSession}

import java.net.URI
import java.sql.Date
import java.time.LocalDate
import java.time.temporal.ChronoUnit

case class UdpIfaSetJobConfig(
  datasource: String,
  dailyDeviceLoc: URI,
  mccMncLookupPath: URI,
  ifaSetSourceLoc: URI,
  ifaSetDestLoc: URI,
  deviceTypeLookupLoc: URI,
  bootstrapDateOpt: Option[LocalDate],
  maxDaysToRun: Int
)

object UdpIfaSetJob extends SparkJob(UdpIfaSetRunner)

object UdpIfaSetRunner extends SparkJobRunner[UdpIfaSetJobConfig] with LazyLogging {

  def runJob(config: UdpIfaSetJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val sourceTsl = TimeSeriesLocation.ofYmdDatePartitions(config.ifaSetSourceLoc).withPartition("ds", config.datasource).build
    val targetTsl = TimeSeriesLocation.ofYmdDatePartitions(config.ifaSetDestLoc).withPartition("ds", config.datasource).build

    // set the latest date to the day before bootstrap, so that the bootstrap date will be the "next day"
    val lastIfaSetDate = config
      .bootstrapDateOpt.map(_.minusDays(1))
      .getOrElse(sourceTsl.latestDate)
    logger.info(s"Latest IFA set available date is $lastIfaSetDate")

    val ddTSL = TimeSeriesLocation.ofYmdDatePartitions(config.dailyDeviceLoc).withPartition("ds", config.datasource).build
    val lastDDDate = ddTSL.latestDate
    logger.info(s"Latest daily device available date is $lastDDDate")

    val initialIfaSet = config.bootstrapDateOpt match {
      case Some(_) =>
        require(!sourceTsl.exists, "Bootstrap date expects empty path at ifaset location.")
        logger.info("Setting IfaSet to empty for bootstrap run.")
        spark.emptyDataset[IfaSet]

      case None =>
        val ifaSetSrc = sourceTsl.partition(lastIfaSetDate)
        logger.info(s"ifaSet source located at $ifaSetSrc")

        // Read the dataset
        val ifaSet = spark.read
          .option(ReadOpts.basePath, config.ifaSetSourceLoc.toString)
          .parquet(ifaSetSrc)

        // Add "distinct_apps" column if missing
        val ifaSetWithDistinctApps = if (!ifaSet.columns.contains("distinct_apps")) {
          ifaSet.withColumn("distinct_apps", lit(Array.empty[String]))
        } else {
          ifaSet
        }
        val filteredIfaSet = ifaSetWithDistinctApps.filter(row => {
          val ds = row.getAs[String]("ds")
          if (ds == "gamoshi") {
            val days = row.getAs[Seq[Date]]("days").map(_.toLocalDate) // Convert java.sql.Date to LocalDate
            val minDate = if (days.isEmpty) LocalDate.MIN else days.min
            val daysSinceMinDate = ChronoUnit.DAYS.between(minDate, lastIfaSetDate)
            val isSeenOnce = days.size <= 1
            val isOlderThan70Days = daysSinceMinDate > 70
            !(isOlderThan70Days && isSeenOnce)
          } else {
            true // Keep non-Gamoshi rows as-is
          }
        })
        filteredIfaSet.as[IfaSet]
    }

    logger.info(s"Reading MccMnc source lookup table from ${config.mccMncLookupPath.toString}.")
    val mccMncLookup: Map[String, String] = MccMncLookupTable
      .readNoSubbrand(config.mccMncLookupPath)
      .as[(String,String)]
      .collect
      .toMap

    val deviceTypeLookup: Dataset[DeviceTypeLookup] = DeviceTypeLookup
      .read(config.deviceTypeLookupLoc)

    val startDate = lastIfaSetDate.plusDays(1)
    val endDate = Seq(lastDDDate, lastIfaSetDate.plusDays(config.maxDaysToRun)).min
    val dateRange = LocalDateRange.of(startDate, endDate)
    if (dateRange.isEmpty) {
      logger.warn(f"No valid dates to run, exiting. StartDate=$startDate, EndDate=$endDate")
      return
    }
    // check that no dates withing the processing range already exist in the destination
    targetTsl.validOutputPartitionsOrThrow(dateRange)
    logger.info(s"Running for date range $dateRange.")

    val udpIfaSet = UdpIfaSet()
    val iterateIfaSet = (yesterdaySet: Dataset[IfaSet], day: LocalDate) => {
      ddTSL.validInputPartitionsOrThrow(LocalDateRange.of(day))

      logger.info(s"Processing day $day.")

      val dailyDeviceSrc = ddTSL.partition(day)
      logger.info(s"Reading daily device data from $dailyDeviceSrc.")
      val dailyDevices: Dataset[UdpDailyDevice] = spark.read
        .option(ReadOpts.basePath, config.dailyDeviceLoc.toString)
        .parquet(dailyDeviceSrc).as[UdpDailyDevice]

      val (todaysIfaSet, runDayDuration) = Utils.timer(udpIfaSet.runDay(
        day,
        yesterdaySet,
        dailyDevices,
        mccMncLookup,
        deviceTypeLookup
      ))

      logger.info(s"RunDay $day completed in $runDayDuration")
      todaysIfaSet
    }

    val dest = targetTsl.partition(endDate)
    logger.info(s"Writing ifa set to $dest")

    dateRange
      .foldLeft(initialIfaSet)(iterateIfaSet)
      .drop("ds", "year", "month", "day")
      .write
      .mode(SaveMode.ErrorIfExists)
      .parquet(dest)
  }
}
