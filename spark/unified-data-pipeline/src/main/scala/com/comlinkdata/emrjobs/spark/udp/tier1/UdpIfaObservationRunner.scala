package com.comlinkdata.emrjobs.spark.udp.tier1

import com.comlinkdata.largescale.metrics.CldSparkMetricsRegistry
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.location.Point
import com.comlinkdata.largescale.schema.udp.tier0.UdpRaw
import com.comlinkdata.largescale.schema.udp.tier1.{UdpIfaObservation, UdpDailyIfa}
import com.comlinkdata.largescale.udp._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SparkSession, Dataset}

import java.sql.Date


object UdpIfaObservationRunner extends LazyLogging {

  def createUnifiedIfaDataset(ds: ComlinkdataDatasource, raw: Dataset[UdpRaw])(implicit spark: SparkSession): Dataset[UdpDailyIfa] = {
    import spark.implicits._
    raw
      .filter(r => r.latitude.isDefined && r.longitude.isDefined)
      .map(udpRaw2ifa) // changes a udpRawAll record into a ifa-observation record, throws away bad uuid's
      .groupByKey(item => (item._1, item._2)) // (ifa, date)
      .mapGroups { case ((ifa, date), observations) =>
        aggregateIfaDaily(ds, ifa, date, observations)
      }
  }

  private def udpRaw2ifa(row: UdpRaw): (Ifa, Date, UdpIfaObservation) = {
    val ifa = row.ifa
    val date: Date = row.date
    val t = row.datetime
    val lt = row.localdatetime
    val ip = row.ip
    val locType = row.locationtype.get
    val location: Point.Float = Point(row.latitude.get, row.longitude.get)
    val connType = row.connectiontype
    val ac = row.accuracy
    val gs = row.gps_speed
    val pi = row.place_id
    val obs = UdpIfaObservation(
      t, // assume it always exists and see if we are correct
      lt,
      ip,
      location,
      locType,
      connType,
      ac,
      gs,
      pi
    )
    (ifa, date, obs)
  }

  private def aggregateIfaDaily(
    ds: ComlinkdataDatasource,
    ifa: Ifa,
    date: Date,
    items: Iterator[(Ifa, Date, UdpIfaObservation)]
  ): UdpDailyIfa = UdpDailyIfa(
    ifa,
    ds.toString,
    date.toLocalDate,
    items.map(_._3).toSeq
  )

  def meterIfaDataByDate(adjusted: Dataset[UdpDailyIfa])(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Unit = {

    import com.comlinkdata.emrjobs.spark.udp.UdpMetrics.MetricUnits
    import org.apache.spark.sql.functions._
    import spark.implicits._

    val result: Array[(Date, Long, Long)] = adjusted
      .groupBy(col("partition_date"))
      .agg(count("*") as "counts", countDistinct("ifa") as "distinct_ifas")
      .as[(Date, Long, Long)]
      .collect

    result.foreach { case (dd, counts, distinctIfas) =>
      val d = dd.toLocalDate
      metrics.putLiteral(d, "daily-ifa-total", counts, MetricUnits.each)
      metrics.putLiteral(d, "daily-ifa-distinct-total", distinctIfas, MetricUnits.each)
    }

  }

}
