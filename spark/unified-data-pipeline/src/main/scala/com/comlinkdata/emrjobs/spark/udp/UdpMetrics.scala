package com.comlinkdata.emrjobs.spark.udp

import java.time.LocalDate
import com.comlinkdata.largescale.metrics.CldSparkMetricsRegistry

trait UdpMetrics {
  final lazy val today = LocalDate.now

  object MetricNames {
    lazy val moduleUdp = "udp"
    lazy val runTime = "run-time"
    lazy val endDate = "end-date"
    lazy val startDate = "start-date"
    lazy val previousPartitionExists = "previous-partition-exists"
  }
   object MetricUnits {
     lazy val epochDay = "epoch-day"
     lazy val bool = "bool"
     lazy val seconds = "s"
     lazy val each = "each"
     lazy val days = "days"
     lazy val version = "version"
   }
  object metricsReporter {
    def reportEndDate(endDate: LocalDate, hint: Option[String] = None)(implicit reg: CldSparkMetricsRegistry): Unit = {
      reg.putLiteral(today, MetricNames.endDate, endDate.toEpochDay, MetricUnits.epochDay, hint)
    }

    def reportStartDate(startDate: LocalDate, hint: Option[String] = None)(implicit reg: CldSparkMetricsRegistry): Unit = {
      reg.putLiteral(today, MetricNames.startDate, startDate.toEpochDay, MetricUnits.epochDay, hint)
    }

    def reportPreviousPartitionExists(startDate: LocalDate, exists: Boolean, hint: Option[String] = None)(implicit reg: CldSparkMetricsRegistry): Unit = {
      reg.putLiteralBool(startDate, MetricNames.previousPartitionExists, exists, MetricUnits.bool, hint)
    }
  }
}

object UdpMetrics extends UdpMetrics