package com.comlinkdata.emrjobs.spark.udp.ifaset

import com.comlinkdata.largescale.commons.{SparkJobRun<PERSON>, Utils, SparkJob}
import com.comlinkdata.largescale.schema.udp.lookup.DeviceTypeLookup
import com.comlinkdata.largescale.schema.udp.tier2.IfaSet
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SparkSession, Dataset, DataFrame, SaveMode}

import scala.util.Try
import java.net.URI

case class DeviceTypeLookupJobConfig(
  datasource: ComlinkdataDatasource,
  ifaSetLocation: URI,
  deviceTypeLookupLocation: URI
)

object DeviceTypeLookupJob extends SparkJob[DeviceTypeLookupJobConfig](DeviceTypeLookupJobRunner)

object DeviceTypeLookupJobRunner extends SparkJobRunner[DeviceTypeLookupJobConfig] {
  final val MIN_MODEL_CODE_AGE = 7 // minimum model code age in order to consider it for device type stabilization

  def runJob(config: DeviceTypeLookupJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    // Initial lookup is not required
    val lookup = Try(DeviceTypeLookup.read(config.deviceTypeLookupLocation))
      .getOrElse(spark.emptyDataset[DeviceTypeLookup])
    val ifaSet = IfaSet.readLatest(config.ifaSetLocation, config.datasource)._2

    val newLookup = stabilizeMissingDeviceTypes(lookup, ifaSet).repartition(1)
    val outputPath = Utils.joinPaths(config.deviceTypeLookupLocation, s"ds=${config.datasource}")

    // Appends new device types by datasource in
    newLookup.write.mode(SaveMode.Append).parquet(outputPath.toString)
  }


  /**
    * Finds and stabilizes device types which are not in given lookup table.
    * It does guarantee stability of model codes which live longer than MIN_MODEL_CODE_AGE.
    *
    * @param lookup existing device-type lookup table that maps modal_model_code to device_type
    * @param ifaSet ifa set
    * @return lookup table with new model codes/device type combinations
    */
  def stabilizeMissingDeviceTypes(lookup: Dataset[DeviceTypeLookup], ifaSet: Dataset[IfaSet])(implicit spark: SparkSession): Dataset[DeviceTypeLookup] = {
    import spark.implicits._

    val ifaSetNormalized = ifaSet
      .transform(emptyStringToNull("device_type"))
      .as[IfaSet]

    // distinct model codes eligible for updating their device type
    val eligibleModelCodes = ifaSet
      .select($"modal_model_code", array_min($"days") as "min_date", array_max($"days") as "max_date")
      .transform(emptyStringToNull("modal_model_code"))
      .filter($"modal_model_code".isNotNull)
      .groupBy($"modal_model_code")
      .agg(min($"min_date") as "min_date", max($"max_date") as "max_date")
      .filter(datediff($"max_date", $"min_date") >= MIN_MODEL_CODE_AGE)
      .select($"modal_model_code")

    val byCount = Window
      .partitionBy($"modal_model_code")
      .orderBy($"device_type_null", $"all_days_count".desc, $"device_type".desc)

    ifaSetNormalized
      .join(broadcast(lookup), Seq("modal_model_code"), "leftanti") // do not consider values present in the static lookup
      .join(broadcast(eligibleModelCodes), Seq("modal_model_code"), "leftsemi") // keep only model codes eligible for update
      .withColumn("days_count", size($"days"))
      .groupBy($"modal_model_code", $"device_type")
      .agg(sum($"days_count") as "all_days_count")
      .withColumn("device_type_null", when($"device_type".isNull, 1).otherwise(0))
      .select($"modal_model_code", $"device_type", row_number().over(byCount) as "rank")
      .filter($"rank" === 1)
      .drop("ifa_count", "rank")
      .select(DeviceTypeLookup.cols: _*)
      .as[DeviceTypeLookup]
  }

  def emptyStringToNull[T](columns: String*)(data: Dataset[T]): DataFrame = {
    columns.foldLeft(data.toDF) { case (df, column) =>
      df.withColumn(column, when(trim(col(column)) === "", null).otherwise(col(column)))
    }
  }
}