package com.comlinkdata.emrjobs.spark.udp.maxmind

import org.apache.spark.sql.{DataFrame, SparkSession}

import java.net.URI

object MaxmindIpLocationDao {
  import com.comlinkdata.largescale.commons.SparkConstants._

  val maxmindIpLocSchema: String =
    """
      |  `network` string,
      |  `network_start_integer` string,
      |  `network_end_integer` string,
      |  `network_start_ip` string,
      |  `network_end_ip` string,
      |  `postal_code` string,
      |  `latitude` decimal(7,4),
      |  `longitude` decimal(7,4),
      |  `accuracy_radius` string,
      |  `country_name` string,
      |  `subdivision_1_name` string,
      |  `subdivision_2_name` string,
      |  `city_name` string,
      |  `metro_code` string,
      |  `geoname_id` string,
      |  `registered_country_geoname_id` string,
      |  `represented_country_geoname_id` string,
      |  `is_anonymous_proxy` integer,
      |  `is_satellite_provider` integer""".stripMargin

  def load(tableLocation: URI)(implicit spark:SparkSession): DataFrame = {
    spark.read
      .schema(maxmindIpLocSchema)
      .option(ReadOpts.csv.mode, ReadOpts.csv.modeValuePermissive)
      .csv(tableLocation.toString)
  }

}
