package com.comlinkdata.emrjobs.spark.udp.ifaset

import com.comlinkdata.largescale.commons.RichDate.{toRichSqlDate, sqlDateOrdering}
import com.comlinkdata.largescale.schema.udp.tier2
import com.comlinkdata.largescale.schema.udp.tier2.DayStat

import java.sql.Date
import java.time.Period

object FrequencyUtils extends Serializable {

  type FrequencyMap[K] = Map[K, DayStat]

  def createDayStats(ids: Seq[String], max: Int, pd: Date): FrequencyMap[String] =
    ids.distinct.sorted.take(max).map(_ -> tier2.DayStat(pd, pd, 1)).toMap

  def mergeDayStats(day1: FrequencyMap[String], day2: FrequencyMap[String]): FrequencyMap[String] =
    (day1.toIndexedSeq ++ day2.toIndexedSeq)
      .groupBy(_._1)
      .mapValues(_.map(_._2).distinct.reduceLeft(mergeDayStat))

  def mergeDayStat(stat1: DayStat, stat2: DayStat): DayStat = {
    val from = Seq(stat1.first, stat2.first).min
    val to = Seq(stat1.last, stat2.last).max
    val diff = Period.between(from.toLocalDate, to.plusDays(1).toLocalDate).getDays
    val distinctDays = Seq(stat1.distinct_days + stat2.distinct_days, diff).min
    DayStat(from, to, distinctDays)
  }

  def filterRecentEntries(stats: FrequencyMap[String], max: Int): FrequencyMap[String] = {
    if (stats.isEmpty)
      stats // so that we don't have to deal with an empty head later
    else {
      val first = stats.toIndexedSeq.minBy(e => (e._2.first, -e._2.distinct_days, e._1))
      val last = stats.toIndexedSeq.sortBy(e => (-e._2.last.getTime, e._2.distinct_days, e._1)).take(max - 1)
      (first +: last).toMap
    }
  }
}
