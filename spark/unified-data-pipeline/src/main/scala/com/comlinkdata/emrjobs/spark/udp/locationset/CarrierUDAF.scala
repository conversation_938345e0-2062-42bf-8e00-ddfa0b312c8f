package com.comlinkdata.emrjobs.spark.udp.locationset

import org.apache.spark.sql.Row
import org.apache.spark.sql.catalyst.expressions.GenericRowWithSchema
import org.apache.spark.sql.expressions.{MutableAggregationB<PERSON>er, UserDefinedAggregateFunction}
import org.apache.spark.sql.types._

import java.sql.Date

/**
  * User defined aggregation function. GroupBy on Lat & Long Ints and aggregate into a map of
  * (carriers, date) -> (location, count)
  */
class CarrierUDAF extends UserDefinedAggregateFunction {
  val MIN_LOC_COUNT = 2

  override def inputSchema: StructType = StructType(
    StructField("carrier", StringType) ::
      StructField("connection_type", StringType) ::
      StructField("date", DateType) ::
      StructField("location_hash", IntegerType) ::
      Nil
  )

  def carrierDateCountMap: MapType =
      MapType(
        StructType(
          StructField("_1", StringType) ::
            Struct<PERSON><PERSON>("_2", DateType) ::
            Nil
        ), MapType(IntegerType, IntegerType), valueContainsNull = false)

  override def bufferSchema: StructType =
    StructType(StructField("wifi", carrierDateCountMap) :: StructField("cell", carrierDateCountMap) :: Nil)

  override def dataType: DataType =
    StructType(StructField("wifi", carrierDateCountMap) :: StructField("cell", carrierDateCountMap) :: Nil)

  override def deterministic: Boolean = true

  override def initialize(buffer: MutableAggregationBuffer): Unit = {
    buffer(0) = Map.empty[(String, Date), Map[Int, Int]]
    buffer(1) = Map.empty[(String, Date), Map[Int, Int]]
  }

  def getMap(buffer: Row, i: Int): Map[(String, Date), Map[Int, Int]] = {
    buffer.getMap[GenericRowWithSchema, Map[Int, Int]](i)
      .map(kv => ((kv._1.getString(0), kv._1.getDate(1)), kv._2))
      .toMap[(String, Date), Map[Int, Int]]
  }

  override def update(buffer: MutableAggregationBuffer, input: Row): Unit = {
    if (input.get(0) != null) {

      val carrier = input.getString(0)
      val conn_type = input.getString(1)
      val date = input.getDate(2)
      val locHash = input.getInt(3)

      if(conn_type != null) {
        val conn_int =
          if (conn_type.equalsIgnoreCase("CELLULAR")) {
            1
          } else { // Other possible values: Corporate, Cable/DSL, Dialup, Satellite
            0
          }

        val carrierConnTypeDateKey = (carrier, date)
        val outerMap = getMap(buffer, conn_int)

        val locCountMap =
          outerMap.get(carrierConnTypeDateKey) match {
            case None => Seq((locHash, 1)).toMap[Int, Int]
            case Some(gotMap) => gotMap + (locHash -> gotMap.get(locHash).map(_ + 1).getOrElse(1))
          }

        buffer(conn_int) = outerMap + (carrierConnTypeDateKey -> locCountMap)
      }
    }
  }

  override def merge(buffer1: MutableAggregationBuffer, buffer2: Row): Unit = {
    for (i <- 0 to 1) {
      val map1 = getMap(buffer1, i)
      val map2 = getMap(buffer2, i)

      buffer1(i) = (map1.keySet ++ map2.keySet).map(key => {
        (map1.get(key), map2.get(key)) match {
          case (None, Some(locCountMap)) => (key, locCountMap)
          case (Some(locCountMap), None) => (key, locCountMap)
          case (Some(locCountMap1), Some(locCountMap2)) =>
            val updatedLocMap =
              (locCountMap1.keySet ++ locCountMap2.keySet).map(locKey =>
                (locCountMap1.get(locKey), locCountMap2.get(locKey)) match {
                  case (None, Some(count)) => (locKey, count)
                  case (Some(count), None) => (locKey, count)
                  case (Some(count1), Some(count2)) => (locKey, count1 + count2)
                }).toMap[Int, Int]
            (key, updatedLocMap)
        }
      }).toMap[(String, Date), Map[Int, Int]]
    }
  }

  override def evaluate(buffer: Row): Any = {
    buffer
  }
}
