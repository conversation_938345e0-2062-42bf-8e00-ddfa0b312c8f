package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.emrjobs.spark.udp.installbase.IPClassifierRunner.RESIDENTIAL
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons._
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.comlinkdata.largescale.schema.udp.installbase.{IPSequence, IPHighConfidence}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SparkSession, Dataset, DataFrame, SaveMode}
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._

import java.net.URI
import java.sql.Date
import java.time.{LocalTime, LocalDate}

/**
  * IP Sequence config.
  *
  * startDate, endData, & highConfidenceDay are optional. If they aren't given it will run as many days as there are input days available (capped at maxDaysToRun)
  * @param ifaToIpPath Input S3: Output from PreFilterJob
  * @param outputPath Output S3 location
  * @param startDate start date
  * @param endDate end date
  * @param highConfidenceDay day of high confidence data to compare against
  * @param maxDaysToRun max number of days to run (only used when above dates aren't given)
  * @param highConfidencePath Input S3: Output from HighConfidence (day0)
  * @param daysToLookBack days to look back for history of ip
  */
case class IPSequenceSetConfig(
  ifaToIpPath: URI,
  outputPath: URI,
  highConfidencePath: URI,
  startDate: Option[LocalDate],
  endDate: Option[LocalDate],
  highConfidenceDay: Option[LocalDate],
  maxDaysToRun: Option[Int],
  daysToLookBack: Int,
  repartition: Option[Int] = Some(100)
)

/**
  * Intermediary schema
  * @param ip IP Address (unique key)
  * @param ip_min_date Min date of ip seen across all IFAs
  * @param ip_max_date Max date of ip seen across all IFAs
  * @param carrier IP carrier
  */
case class IPLifespan(
  ip: Ip,
  ip_min_date: Date,
  ip_max_date: Date,
  carrier: String
)

/**
  * If to ifa output schema with partition
  * @param ip ip address
  * @param ifa ifa ID
  * @param carrier maxmind carrier
  * @param consolidated_id cld carrier id from CarrierLookup
  * @param connection_type maxmind connection type
  * @param sp_platform sp platofrom
  * @param obs_count observation count
  * @param night_obs_count night time observation count
  * @param household_obs_count household observation count
  * @param household_weighted_obs_count household weighted observation count
  * @param business_weighted_obs_count business weighted observation count
  * @param year struct of all observations
  * @param month struct of all observations
  * @param day struct of all observations
  */
case class IfaToIpYMD (
  ip: Ip,
  ifa: Ifa,
  carrier: String,
  consolidated_id: String,
  connection_type: String,
  sp_platform: String,
  organization: String,
  autonomous_system_number: String,
  obs_count: Long,
  night_obs_count: Long,
  household_obs_count: Long,
  household_weighted_obs_count: Double,
  business_weighted_obs_count: Double,
  year: String,
  month: String,
  day: String
)

case class IfaWithIpCandidate (
  ifa: Ifa,
  ip: Ip,
  date_count: Long,
  home_hour_date_count: Long,
  obs_count: Long,
  night_obs_count: Long,
  ip_min_date: Date,
  ip_max_date: Date,
  carrier: String,
  ip_set: Array[Ip],
  ifa_max_date: Date
)

case class CaseMethod (
  ifa: Ifa,
  ip: Ip,
  obs_count: Long,
  night_obs_count: Long,
  ip_min_date: Date,
  ip_max_date: Date,
  case_method: Int,
  old_ip: Ip
)

object IPSequenceSet extends SparkJob(IPSequenceSetRunner)

object IPSequenceSetRunner
  extends SparkJobRunner[IPSequenceSetConfig]
    with SparkConstants
    with LazyLogging {

  private val MAX_UNIQUE_IP = 25
  private val MAX_UNIQUE_IFA = 25
  private val MIN_HOME_HOUR_DATES = 2

  override def runJob(config: IPSequenceSetConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val tslIfaIp = TimeSeriesLocation
      .ofYmdDatePartitions(config.ifaToIpPath)
      .build

    val tslOutSet = TimeSeriesLocation
      .ofYmdDatePartitions(config.outputPath)
      .build

    val tslHighConfidence = IPHighConfidence.tsl(config.highConfidencePath)

    val highConfidenceDay = config.highConfidenceDay.getOrElse(
      if (!tslHighConfidence.existsAndHasData) {
        null
      } else {
        tslHighConfidence.latestDate
    })

    val dsHighConfidence:Dataset[IPHighConfidence] = IPHighConfidence.read(config.highConfidencePath, highConfidenceDay).cache()

    val startDate = config.startDate.getOrElse(
      if(tslOutSet.existsAndHasData)
        tslOutSet.latestDate.plusDays(1)
      else
        highConfidenceDay
    )

    val endDate = config.endDate.getOrElse(
      tslIfaIp.latestDate
    )

    logger.info(s"Running IP Sequence for date: $startDate to $endDate")

    val daysToRun: Seq[LocalDate] = (startDate to endDate).toSeq

    if (daysToRun.isEmpty) throw new IllegalArgumentException("No days to run. Is upstream working?")

    val daysToRunCapped = if(config.maxDaysToRun.isDefined) daysToRun.take(config.maxDaysToRun.get) else daysToRun

    for (day <- daysToRunCapped) {
      println(s"${LocalTime.now()}: starting day: $day")

      val startDate = if (tslIfaIp.earliestDate.isAfter(day.minusDays(config.daysToLookBack))) {
        tslIfaIp.earliestDate
      } else {
        day.minusDays(config.daysToLookBack)
      }

      val tslIfa = tslIfaIp.partitions(startDate, day)
      val dsIfa: Dataset[IfaToIpYMD] = spark.read
        .option(ReadOpts.basePath, config.ifaToIpPath.toString)
        .parquet(tslIfa:_*)
        .as[IfaToIpYMD]

      val data: Dataset[IPSequence] = processDay(dsIfa, dsHighConfidence, day)
      val dataRepartition = if(config.repartition.isDefined) {
        data.repartition(config.repartition.get)
      } else {
        data
      }

      UriDFTableRW
        .fromStr(tslOutSet.partition(day))
        .writeWithHistory(
          dataRepartition.drop("year", "month", "day"),
          SaveMode.ErrorIfExists,
          Seq(config))
    }
  }

  def processDay(
    dsIfa: Dataset[IfaToIpYMD],
    dsHighConfidence: Dataset[IPHighConfidence],
    day:LocalDate
  )(implicit spark:SparkSession): Dataset[IPSequence] = {
    import spark.implicits._

    val dsIfaFiltered: Dataset[IfaToIpYMD] = dsIfa.transform(residentialFilter(MAX_UNIQUE_IP, MAX_UNIQUE_IFA, MIN_HOME_HOUR_DATES)).cache()

    val dsIfaWithIpCandidates: Dataset[IfaWithIpCandidate] = dsIfaFiltered
      .transform(ifaWithIpCandidates)
      .filter($"ifa_max_date" === day)
      .cache()
    val dsIPLifespan: Dataset[IPLifespan] = dsIfaFiltered.transform(ipLifespan).cache()
    val cases: Dataset[CaseMethod] = dsIfaWithIpCandidates.transform(caseMethod(dsHighConfidence)).cache()
    val case2: Dataset[CaseMethod] = cases.transform(changeCandidates(dsIPLifespan))

    val union: Dataset[CaseMethod] = cases
      .filter($"case_method" isin (1, 3))
      .unionByName(case2)

    val unionWithCarrier: Dataset[IPSequence] = union.as("union")
      .join(dsIPLifespan.as("mm"), Seq("ip"), "left")
      .select($"union.*", $"mm.carrier")
      .withColumn("year", lit(day.getYearString))
      .withColumn("month", lit(day.getMonthValueString))
      .withColumn("day", lit(day.getDayOfMonthString))
      .select(IPSequence.cols:_*)
      .as[IPSequence]

    // assume unpersist is lazy since cache is also lazy?
    dsIfaFiltered.unpersist()
    dsIfaWithIpCandidates.unpersist()
    dsIPLifespan.unpersist()
    cases.unpersist()

    unionWithCarrier
  }

  /**
    * Takes all ifa observations and applies filters for:
    *   WIFI
    *   Non truncated IPs
    *   Exists in carrier lookup
    *   Does not exceed max unique ifas
    *   Observed at night for at least minimum days
    * @param dsIfa all ifa observations, aggregated at the ifa level
    * @return ifa observations filtered down to only residential
    */
  def residentialFilter(maxUniqueIp: Int, maxUniqueIfa: Int, minHomeHourDates: Int)(dsIfa: Dataset[IfaToIpYMD])(implicit spark: SparkSession): Dataset[IfaToIpYMD] = {
    import spark.implicits._

    val filter1 = dsIfa
      .withColumn("date", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))
      .filter(upper($"connection_type") isin ("CABLE/DSL", "DIALUP", "CORPORATE", "SATELLITE"))
      .filter($"consolidated_id".isNotNull) // exists in carrierLookup

    // unique ifa count filtering
    val filter2 = filter1
      .withColumn("ifa_count", approx_count_distinct($"ifa").over(Window.partitionBy($"ip")))
      .filter($"ifa_count" < maxUniqueIfa)
      .drop("ifa_count")

    // house hold / residential filtering
    val filter3 = filter2
      .withColumn("night_date", when($"night_obs_count" > 0, $"date").otherwise(null))
      .withColumn("home_hour_dates", approx_count_distinct($"night_date").over(Window.partitionBy($"ip")))
      .filter($"home_hour_dates" >= minHomeHourDates )
      .drop("night_date", "home_hour_dates")

    // unique ip count filtering -- wasn't this in the base dataset?
    val filter4 = filter3
      .withColumn("unique_ip", approx_count_distinct($"ip").over(Window.partitionBy($"ifa")))
      .filter($"unique_ip" < maxUniqueIp)
      .drop("unique_ip")
      .as[IfaToIpYMD]

    filter4
  }

  /**
    * Using a list of residential IPs, filter down the ifa list
    * @param dsClassifiedIP list of IPs that are classified as residential or business
    * @param dsIfa all ifa observations, aggregated at the ifa level
    * @param spark sparkContext
    * @return ifa observations filtered down to only residential
    */
  def residentialFilter(dsClassifiedIP: Dataset[ClassifiedIP])(dsIfa: Dataset[IfaToIpYMD])(implicit spark: SparkSession): Dataset[IfaToIpYMD] = {
    import spark.implicits._
    val dsResidentialIP = dsClassifiedIP.filter($"prediction" === RESIDENTIAL)
    dsIfa.as("a")
      .join(dsResidentialIP, Seq("ip"), "inner")
      .select("a.*")
      .as[IfaToIpYMD]
  }

  /**
    * Creates a Dataframe of ifa to their best IPs based on:
    *   House hold days descending - how many days this ifa was seen during "night"
    *   Total days descending
    *   Night observations count descending
    *   Total observations count descending
    * @param dsIfaFiltered ifa observations filtered down to only residential
    * @return DataFrame of ifas with their best ranked IPs
    */
  def ifaWithIpCandidates(
    dsIfaFiltered:Dataset[IfaToIpYMD]
  )(implicit spark: SparkSession): Dataset[IfaWithIpCandidate] = {
    import spark.implicits._

    // assuming dsIfaFiltered spans multiple days
    val grouped = dsIfaFiltered
      .withColumn("date", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))
      .groupBy("ifa", "ip")
      .agg(
        countDistinct($"date") as "date_count",
        countDistinct(when($"night_obs_count" > 0, $"date").otherwise(null)) as "home_hour_date_count",
        sum($"obs_count") as "obs_count",
        sum($"night_obs_count") as "night_obs_count",
        min($"date") as "ip_min_date",
        max($"date") as "ip_max_date",
        max($"date") as "ifa_max_date",
        min($"carrier") as "carrier" // should be 1:1 with ip, but just in case
      )

    val ranked = grouped
      .withColumn("x", struct(
        $"home_hour_date_count",
        $"date_count",
        $"night_obs_count",
        $"obs_count",
        $"ip", //tiebreaker
        $"ip_min_date",
        $"ip_max_date",
        $"carrier"))
      .groupBy($"ifa")
      .agg(
        max("x").as("x"), // essentially a row_number() over ... where rank = 1
        collect_list($"ip").as("ip_set"),
        max($"ifa_max_date").as("ifa_max_date")
      )

    ranked
      .select(
        $"ifa",
        $"x.ip",
        $"x.date_count",
        $"x.home_hour_date_count",
        $"x.obs_count",
        $"x.night_obs_count",
        $"x.ip_min_date",
        $"x.ip_max_date",
        $"x.carrier",
        $"ip_set",
        $"ifa_max_date")
      .as[IfaWithIpCandidate]
  }

  /**
    * Aggregate stats by ip
    * @param dsIfa filtered ifa observations, aggregated at the ifa level
    * @return lifespan of ips
    */
  def ipLifespan(dsIfa: Dataset[IfaToIpYMD])(implicit spark: SparkSession): Dataset[IPLifespan] = {
    import spark.implicits._

    val grouped = dsIfa
      .withColumn("date", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))
      .groupBy("ip", "carrier")
      .agg(
        countDistinct($"date") as "date_count",
        countDistinct(when($"night_obs_count" > 0, $"date").otherwise(null)) as "home_hour_date_count",
        sum($"obs_count") as "obs_count",
        sum($"night_obs_count") as "night_obs_count",
        min($"date") as "ip_min_date",
        max($"date") as "ip_max_date",
      )

    val ranked = grouped
      .withColumn("x", struct(
        $"home_hour_date_count",
        $"date_count",
        $"night_obs_count",
        $"obs_count",
        $"ip_max_date",// tiebreak
        $"carrier"))
      .groupBy($"ip")
      .agg(
        min($"ip_min_date") as "ip_min_date",
        min($"ip_max_date") as "ip_max_date",
        max("x").as("x"), // essentially a row_number() over ... where rank = 1
      )

    ranked
      .select(
        $"ip",
        $"ip_min_date",
        $"ip_max_date",
        $"x.carrier")
      .as[IPLifespan]
  }

  /**
    * Deprecated - useing ipLifespan instead
    * Pick the latest carrier per IP
    * @param dsIfa all ifa observations, aggregated at the ifa level
    * @return Dataset of IP to best carrier info
    */
  @deprecated
  def rankedCarrier(dsLifespan: Dataset[IPLifespan])(dsIfa: Dataset[IfaToIpYMD])(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    val window = Window
      .partitionBy($"ip")
      .orderBy(
        $"date".desc,
        $"carrier" // tie break
      )

    val windowed = dsIfa
      .withColumn("date", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))
      .withColumn("rank", row_number().over(window))
      .filter($"rank" === 1)
      .select(
        $"ip",
        $"carrier" // raw carrier name
      )

    windowed
      .join(dsLifespan.as("agg"), Seq("ip"), "inner")
      .select(
        $"ip",
        $"ip_min_date",
        $"ip_max_date",
        $"carrier"
      )
  }

  /**
    * Adds a case method column that compares High Confidence (day 0) data to current data.
    * Each case is checked sequentially
    *   case 1 - Same IP - If the high confidence IP has appeared in the ifa candidates ip_set list. This ignored any candidate ranking and picks High Confidence IP as best
    *   case 2 - Changed IP - Send over to changedCandidates(IPLifeSpan)
    *   case 3 - New IFA - current device did not have a high confidence IP, so add it.
    * @param hc High Confidence data
    * @param dsCandidates Ifa with IP Candidates for current day
    * @return Joined dataset with case_method column
    */
  def caseMethod(hc: Dataset[IPHighConfidence])(dsCandidates: Dataset[IfaWithIpCandidate])(implicit spark: SparkSession): Dataset[CaseMethod] = {
    import spark.implicits._
    hc.as("old")
      .join(dsCandidates.as("new"), Seq("ifa"), "fullouter")
      .withColumn("case_method",
        when($"old.ip" === $"new.ip", 1)
          .when(array_contains( $"new.ip_set", $"old.ip"), 1)
          .when($"old.ip".isNotNull && $"new.ip".isNotNull && $"old.ip" =!= $"new.ip", 2)
          .when($"old.ip".isNull, 3)
          .when($"new.ip".isNull, 4) // should throw this away
          .otherwise(lit(5)) // check counts on these, should be 0
      )
      .filter($"case_method" isin( 1, 2, 3))
      .select(
        $"ifa",
        $"new.ip" as "ip",
        $"new.obs_count" as "obs_count",
        $"new.night_obs_count" as "night_obs_count",
        $"new.ip_min_date" as "ip_min_date",
        $"new.ip_max_date" as "ip_max_date",
        $"case_method",
        $"old.ip" as "old_ip")
      .as[CaseMethod]
  }

  /**
    * case 2
    * Identify IFAs having possible IP changes
    * Check IP min/max date overlaps here.
    * Assemble a “final” IP chain from several of these slices, but don’t even attempt to assign an IP change candidate if the min/max dates don’t align with the existing IP.
    * Accounts for IP change outcome cases “refresh”, “switch”, “household split”.
    * this calculates a new IP for all IFAs over the update period.
    * @param dsIfaFiltered ifa observations filtered down to only residential
    * @param dsIfaWithIpCandidates Ifas with best ranked IPs
    * @param day0 Initial Dataset to be compared to
    * @param dfSameIp Case 1 dataset - Only used for excluding any IFA/IPs that were handled by Case 1
    * @return IFAs with IPs based on Case 2
    */
  def changeCandidates(lifespan:Dataset[IPLifespan])(cases:Dataset[CaseMethod])(implicit spark: SparkSession): Dataset[CaseMethod] = {
    import spark.implicits._

    val candidates = cases
      .filter($"case_method" === 2)
      .withColumn("new_ip", $"ip")

    val output = candidates
      .as("candidates")
      .join(lifespan.as("old"), $"candidates.old_ip" === $"old.ip", "left")
      .join(lifespan.as("new"), $"candidates.new_ip" === $"new.ip", "left")
      .withColumn("case_ip",
        when($"old.ip".isNull, $"candidates.new_ip")
          .when($"new.ip".isNull, $"candidates.old_ip") // this should never happen
          .when(to_date($"old.ip_max_date") <= to_date($"new.ip_min_date"), $"candidates.new_ip")
          .otherwise(lit(null)))
      .filter($"case_ip".isNotNull)
      .select(
        $"candidates.ifa" as "ifa",
        $"case_ip" as "ip",
        $"candidates.obs_count" as "obs_count",
        $"candidates.night_obs_count" as "night_obs_count",
        $"candidates.ip_min_date" as "ip_min_date",
        $"candidates.ip_max_date" as "ip_max_date"
      )
      .withColumn("old_ip", lit(null))
      .withColumn("case_method", lit(2))
      .as[CaseMethod]

    output
  }
}
