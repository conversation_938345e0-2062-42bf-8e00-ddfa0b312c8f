package com.comlinkdata.emrjobs.spark.udp.ifaset

import org.apache.spark.sql.functions.{col, hex, lower}
import org.apache.spark.sql.{Column, Dataset, SparkSession}

case class IphoneModelResolution(ifa: Array[Byte], model: String)

object LookupTableReader {

  def readMccMncLookup(path: String)(implicit spark: SparkSession): Map[String, String] = {
    import spark.implicits._

    val cols: Seq[Column] = Seq(col("mcc_mnc"), col("carrier"))
    spark.read
      .parquet(path)
      .select(cols: _*)
      .as[(String, String)]
      .collect
      .toMap
  }

  def readIphoneModelResolutionLookup(path: String)(implicit spark: SparkSession): Dataset[IphoneModelResolution] = {
    import spark.implicits._

    spark.read
      .parquet(path)
      .select(col("ifa"), col("model"))
      .as[IphoneModelResolution]
      .repartition(256)
  }

}
