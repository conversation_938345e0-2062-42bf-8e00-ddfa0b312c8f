package com.comlinkdata.emrjobs.spark.udp.tier1

import java.sql.Date
import com.comlinkdata.largescale.metrics.CldSparkMetricsRegistry
import com.comlinkdata.largescale.schema.udp.location.Point
import com.comlinkdata.largescale.schema.udp.tier0.UdpRaw
import com.comlinkdata.largescale.schema.udp.tier1.{UdpDailyPlaceObservation, UdpDailyPlace}
import com.comlinkdata.largescale.udp._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SparkSession, Dataset}

object UdpDailyPlacesRunner extends LazyLogging {

  def createDailyPlaces(ds: ComlinkdataDatasource, raw: Dataset[UdpRaw])(implicit spark: SparkSession): Dataset[UdpDailyPlace] = {
    import spark.implicits._
    val placeRecords = raw.filter(_.place_id.isDefined).map(udpRow2DailyPlaceObs)
    placeRecords.groupByKey(row => (row.date, row.place_id, row.place_name, row.category))
      .mapGroups { case ((date, placeId, placeName, category), itemsIter) =>
        UdpDailyPlace(ds.toString, date.toLocalDate, placeId, placeName, category, itemsIter.toSeq)
      }
  }

  def udpRow2DailyPlaceObs(row: UdpRaw): UdpDailyPlaceObservation = {
    val date: java.sql.Date = row.date
    val ifa = row.ifa
    val ip = row.ip
    val latitude = row.latitude
    val longitude = row.longitude
    val locationtype = row.locationtype
    val datetime = row.datetime
    val place_name = row.place_name
    val place_id = row.place_id.get
    val category = row.category
    val localdatetime = row.localdatetime
    val location: Point.Float = Point(row.latitude.get, row.longitude.get)
    UdpDailyPlaceObservation(date,
      ifa.toVector,
      ip.map(_.toVector),
      latitude,
      longitude,
      locationtype,
      datetime,
      place_name,
      place_id,
      category,
      localdatetime,
      location
    )
  }

  //TODO make this method (and similar methods) return the array of results and put them in metrics at the call point.
  def meterDailyPlacesDataByDate(adjusted: Dataset[UdpDailyPlace])(implicit spark: SparkSession, metrics: CldSparkMetricsRegistry): Unit = {

    import com.comlinkdata.emrjobs.spark.udp.UdpMetrics.MetricUnits
    import org.apache.spark.sql.functions._
    import spark.implicits._

    val result: Array[(Date, Long, Long)] = adjusted
      .groupBy(col("partition_date"))
      .agg(count("*") as "counts", countDistinct("place_id") as "distinct_places")
      .as[(Date, Long, Long)]
      .collect

    result.foreach { case (dd, counts, distinctPlaces) =>
      val d = dd.toLocalDate
      metrics.putLiteral(d, "daily-place-total", counts, MetricUnits.each)
      metrics.putLiteral(d, "daily-places-distinct-total", distinctPlaces, MetricUnits.each)
    }
  }

}
