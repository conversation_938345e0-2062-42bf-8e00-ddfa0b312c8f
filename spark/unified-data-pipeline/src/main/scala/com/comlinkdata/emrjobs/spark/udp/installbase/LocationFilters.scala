package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.emrjobs.spark.udp.algo.DBScan
import com.comlinkdata.largescale.schema.udp.installbase.LocTs
import com.comlinkdata.largescale.schema.udp.location.Point
import com.comlinkdata.largescale.schema.udp.tier1.UdpIfaObservation
import com.comlinkdata.largescale.udp.LocationUtils.twoDigitGPSAfter2023ThreeBefore
import org.apache.spark.sql.{SparkSession, Column, DataFrame}
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions._

import com.comlinkdata.largescale.commons.RichDate._

import java.sql.Timestamp
import java.time.temporal.ChronoField
import scala.collection.mutable

/**
  * Common Location Related Filters for Install Base
  */
object LocationFilters {

  private[installbase] val ACCURACY_CUTOFF = 75f // meters
  private[installbase] val SPEED_CUTOFF = 5f // m/s
  private[installbase] val IFA_PER_IP_CUTOFF = 15

  private val p180 = Math.PI/180
  private val radiusOfEarthX2InM = 6371 * 2 * 1000
  private val secondsInHour = 60 * 60
  private val secondsInDay = 24 * secondsInHour
  private val DB_SCAN_N_CUTTOFF = 5
  private val DB_SCAN_MAX_UNIQUE_PTS = 100

  private val GOOD_CENTRIOD_CUTTOFF = 500

  private val NEIGHBORHOOD_CUTOFF = 1000
  private val NEIGHBORHOOD_MIN_SIZE = 3

  private val N_MOST_FREQ_POINTS = 10


  /**
    * Common Location Stat
    * @param modal_loc modal location
    * @param centroid centroid location
    * @param avg_centroid_dist average distance from centroid
    */
  case class LocationStats (
    modal_loc: Point.Float,
    centroid: Point.Float,
    avg_centroid_dist: Float,
    var_centroid_dist: Float
  )

  case class LocationStatsExtended (
    modal_loc: Point.Float,
    centroid: Point.Float,
    avg_centroid_dist: Float,
    var_centroid_dist: Float,
    db_centroid: Option[Point.Float],
    db_avg_centroid_dist: Option[Float],
    db_count: Option[Int],
    dist_to_db: Option[Float],
    dist_to_modal: Option[Float],
  )

  object LocationStatsExtended {
    def fromSimpleStats(simpleStates: LocationStats): LocationStatsExtended = {
      LocationStatsExtended(
        simpleStates.modal_loc,
        simpleStates.centroid,
        simpleStates.avg_centroid_dist,
        simpleStates.var_centroid_dist,
        None,
        None,
        None,
        None,
        None
      )
    }

  }
  /**
    * Great circle distance between two points
    * @param lat1 latitude of 1st point
    * @param lng1 longitude of 1st point
    * @param lat2 latitude of 2nd point
    * @param lng2 longitude of 2nd point
    * @return distance between points
    */
  def distanceBetweenPts(lat1: Float, lng1: Float, lat2: Float, lng2: Float):Double = {
    val a = 0.5 - Math.cos((lat2-lat1)*p180)/2 + Math.cos(lat1*p180) * Math.cos(lat2*p180) * (1-Math.cos((lng2-lng1)*p180))/2
    radiusOfEarthX2InM *  Math.asin(Math.sqrt(a))
  }

  /**
    * Great circle distance between two points
    * @param pt1 1st point
    * @param pt2 2nd point
    * @return distance between points (in meters)
    */
  def distanceBetweenPts(pt1: Point.Float, pt2: Point.Float): Double = distanceBetweenPts(pt1.lat, pt1.lng, pt2.lat, pt2.lng)

  /**
    * Seconds between two timestamps
    * @param t1 timestamp 1
    * @param t2 timestamp 2
    * @return seconds between two given timestamps
    */
  def secondsBetweenTimeStamps(t1: Timestamp, t2: Timestamp): Double = {
    Math.abs(t2.getTime - t1.getTime)/1000.0 // Milliseconds to seconds
  }

  /**
    * How many decimals are in a floating pt number
    * @param x float
    * @return number of digits past the decimal in the given number
    */
  private [installbase] def howManyDecimals(x: Float) : Int = {
    val xStr = x.toString
    xStr.length - xStr.indexOf(".") - 1
  }

  /**
    * How many combined decimals are in a point
    * @param pt point
    * @return number of combined digits past the decimal in the given point
    */
  private [installbase] def howManyDecimalsInPt(pt: Point.Float) : Int = {
    howManyDecimals(pt.lat) + howManyDecimals(pt.lng)
  }

  /**
    * How many seconds is a timestamp before or past midnight
    * @param t timestamp to measure
    * @return seconds is the given timestamp before or past midnight
    */
  private [installbase] def secondsFromMidNight(t : Timestamp):Long = {
    val secOfDay = ChronoField.SECOND_OF_DAY.getFrom(t.toLocalDateTime)
    Math.min(secOfDay, secondsInDay - secOfDay)
  }

  /**
    * True if 1st observation is closer to midnight than 2nd observation
    * @param prev 1st observation
    * @param next 2nd observation
    * @return true if 1st observation is closer to midnight than 2nd observation
    */
  private [installbase] def isPrevCloserToMidnight(prev: UdpIfaObservation, next: UdpIfaObservation):Boolean =
    secondsFromMidNight(prev.t_local.get) < secondsFromMidNight(next.t_local.get)

  /**
    * True if 1st observation is more accurate or closer to midnight than 2nd observation
    * @param prev 1st observation
    * @param next 2nd observation
    * @return true if 1st observation is more accurate or closer to midnight than 2nd observation
    */
  private [installbase] def isPrevObsMoreAccOrCloserToMidnightThanPrev(prev: UdpIfaObservation, next: UdpIfaObservation): Boolean = {
    if(prev.location == next.location) { //Same location; Prefer closer to midnight (night hours)
      isPrevCloserToMidnight(prev, next)
    } else {
       //prefer most decmials digits
       val prevDecimals = howManyDecimalsInPt(prev.location)
       val nextDecimals = howManyDecimalsInPt(next.location)
      if(prevDecimals == nextDecimals) {
        isPrevCloserToMidnight(prev, next)
      } else {
        prevDecimals > nextDecimals
      }
    }
  }

  /**
    * True if prev point is closer to the centroid than the 2nd point.
    * (Use _isPrevObsMoreAccOrCloserToMidnightThanPrev if both have the same point)
    * @param prev 1st observation
    * @param next 2nd observation
    * @param centroid previously computed centroid
    * @return True if prev point is closer to the centroid than the 2nd point.
    */
  private def isPrevObsCloserToCentroidOrMoreAccOrCloserToMidnightThanPrev(
    prev: UdpIfaObservation,
    next: UdpIfaObservation,
    centroid: Point.Float): Boolean = {
    if(centroid == null || prev.location == next.location) {
      isPrevObsMoreAccOrCloserToMidnightThanPrev(prev, next)
    } else {
      val metersPrev = distanceBetweenPts(prev.location.lat, prev.location.lng, centroid.lat, centroid.lng)
      val metersNext = distanceBetweenPts(next.location.lat, next.location.lng, centroid.lat, centroid.lng)

      metersPrev < metersNext
    }
  }

  /**
    * Remove non night time observations
    * @param allObs input observations
    * @return filtered observations
    */
  private def filterNightTime(allObs: Seq[UdpIfaObservation]): Seq[UdpIfaObservation] = {
    allObs.filter(obs => {
      val h = ChronoField.HOUR_OF_DAY.getFrom(obs.t_local.get.toLocalDateTime)
      h <= 6 || h >= 19
    })
  }

  /**
    * Compute centroid of given observations
    * @param allObs observations
    * @return filtered observations
    */
  private def centroid(allObs: Seq[UdpIfaObservation]): Point.Float = {
    val latSum = allObs.map(_.location.lat).sum
    val lngSum = allObs.map(_.location.lng).sum
    val count = allObs.size

    if (count == 0) {
      null
    } else {
      new Point(latSum / count, lngSum / count)
    }
  }

  /**
    * Centroid of points within night time hours
    * @param allObs sequence of UdpIfaObservation
    * @return Centroid point
    */
  private [installbase] def nightTimeCentroid(allObs: Seq[UdpIfaObservation]): Point.Float = {
    if (allObs.isEmpty) {
      null
    } else {
      val nightTimePts = filterNightTime(allObs)
      if (nightTimePts.nonEmpty) {
        centroid(nightTimePts)
      } else {
        centroid(allObs)
      }
    }
  }

  /**
    * Remove observations within the same hour. Prefer more accurate records that are closer to midnight
    * @param obsIn input observations
    * @return observations with ones removed
    */
  private [installbase] def filterWithinOneHour(obsIn: Seq[UdpIfaObservation]): Seq[UdpIfaObservation] = {
    val result = scala.collection.mutable.Stack[UdpIfaObservation](obsIn.head)

    for(next <- obsIn.tail) {
      val prev = result.last
      val secBetween = secondsBetweenTimeStamps(prev.t_local.get, next.t_local.get)

      if(secBetween > secondsInHour) {
        result.push(next)
      } else {
        if(!isPrevObsMoreAccOrCloserToMidnightThanPrev(prev, next)) {
          result.pop()
          result.push(next)
        } // else prev point was better; leave it & continue
      }
    }
    result
  }

  /**
    * Implied meters per second between two observations
    * @param prev 1st observation
    * @param next 2nd observation
    * @return computed meters per second
    */
  private [installbase] def mpsBetweenPoints(prev: UdpIfaObservation, next: UdpIfaObservation): Double = {
    val secBetween = secondsBetweenTimeStamps(prev.t_local.get, next.t_local.get)
    val metersBetween = distanceBetweenPts(prev.location.lat, prev.location.lng, next.location.lat, next.location.lng)
    metersBetween / secBetween
  }

  /**
    * Filter observations that move too fast
    * @param obsIn input observations
    * @return observations with two fast observations removed
    */
  private [installbase] def filterFastMovers(obsIn: Seq[UdpIfaObservation]): Seq[UdpIfaObservation] = {

    if(obsIn.size <= 1) {
      obsIn
    } else if (obsIn.size == 2) {
      if (mpsBetweenPoints(obsIn.head, obsIn(1)) <= SPEED_CUTOFF) {
        obsIn
      } else {
        if(isPrevCloserToMidnight(obsIn.head, obsIn(1))) {
          Seq(obsIn.head)
        } else {
          Seq(obsIn(1))
        }
      }
    } else {
      val nightCentroid = nightTimeCentroid(obsIn)
      val result = scala.collection.mutable.Stack[UdpIfaObservation](obsIn.head)

      for (next <- obsIn.tail) {
        val prev = result.last

        if (mpsBetweenPoints(prev, next) <= SPEED_CUTOFF) {
          result.push(next)
        } else {
          if (!isPrevObsCloserToCentroidOrMoreAccOrCloserToMidnightThanPrev(prev, next, nightCentroid)) {
            result.pop()
            result.push(next)
          } // else prev point was better; leave it & continue
        }
      }
      result
    }
  }

  /**
    * Common call for _filterWithinOneHour & _filterFastMovers
    * @return Observations with repeats and fast movers removed
    */
  def filterGPS: UserDefinedFunction =
    udf[Seq[UdpIfaObservation], Seq[UdpIfaObservation]]((obsIn: Seq[UdpIfaObservation]) => {

      if(obsIn.size < 2) {
        obsIn
      } else {
        val sortedObs = obsIn.sortBy(_.t_local)

        val oneHourFiltered = filterWithinOneHour(sortedObs)
        filterFastMovers(oneHourFiltered)
      }
    })

  /**
    * All locationFilters
    * @param cols columns to group by and aggregate on
    * @param spark spark session
    * @return observations with 'bad' location observations removed
    */
  def locationFilters(cols: Column*)(implicit spark: SparkSession): DataFrame => DataFrame =
    df => {

      val locTypeFilter =  df.filter(upper(col("obs.location_type")) === "GPS" || upper(col("obs.location_type")) === "BEACON")

      val accAndSpeedFilter = locTypeFilter
        .filter(col("obs.accuracy").isNull ||  col("obs.accuracy") < ACCURACY_CUTOFF)
        .filter(col("obs.gps_speed").isNull ||  col("obs.gps_speed") < SPEED_CUTOFF)
        .filter(twoDigitGPSAfter2023ThreeBefore("obs.t_local", "obs.location.lat", "obs.location.lng"))

      val outColumns = cols ++ Seq(LocationFilters.filterGPS(col("observations")).as("observations"))

      val filterOneHourAndFastMovers = accAndSpeedFilter
        .groupBy(cols: _*).agg(collect_list(col("obs")) as "observations")
        .select(outColumns: _*)

      filterOneHourAndFastMovers
    }

  /**
    * Compute LocationStats for sequence of UdpIfaObservation
    * @return LocationStats for sequence from UdpIfaObservation
    */
  def locsStatsFromObs: UserDefinedFunction =
    udf[Option[LocationStats], Seq[UdpIfaObservation]]((obsIn: Seq[UdpIfaObservation]) => {
      locStats(obsIn.map(_.location))
    })

  /**
    * Get a Location with Hour seen from Seq of UdpIfaObservation
    * @return Seq of UdpIfaObservation transformed into LocHour's
    */
  def locWithHourFromObs: UserDefinedFunction =
    udf[Seq[LocTs], Seq[UdpIfaObservation]]((obsIn: Seq[UdpIfaObservation]) => {
      obsIn.map(ob => LocTs(ob.location, ob.t_local.get))
    })

  /**
    * Get extended location stats from location timestamps (Can be removed if we don't use DB Scan in prod)
    * @return extended location stats
    */
  private def locStatsFromLocTs: UserDefinedFunction =
    udf[Option[LocationStatsExtended], Seq[LocTs]]((ptTsIn: Seq[LocTs]) => {
      locStatsExtended(ptTsIn)
    })

  /**
    * Get top ten location counts from location timestamps
    * @return top ten location counts
    */
  def locCountsFromLocTs: UserDefinedFunction =
    udf[Map[Point.Float, Int], Seq[LocTs]]((ptTsIn: Seq[LocTs]) => {
      getThreeDigitModalPoints(ptTsIn.map(_.point), N_MOST_FREQ_POINTS)
    })

  private def ptsDists(ptsIn: Seq[Point.Float], centroid: Point.Float): Seq[Double] = {
    ptsIn.map(pt => distanceBetweenPts(pt.lat, pt.lng, centroid.lat, centroid.lng))
  }

  /**
    * Average Distance from centroid
    * @param ptsIn input points
    * @param centroid centroid
    * @return average distance from centroid
    */
  private def avgDist(ptsIn: Seq[Point.Float], centroid: Point.Float): Float = {
    (ptsDists(ptsIn, centroid).sum / ptsIn.size).toFloat
  }

  /**
    * Average Distance from centroid
    * @param ptsIn input points with counts of times seen
    * @param centroid centroid
    * @return average distance from centroid
    */
  private def avgDistWithCounts(ptsIn: Seq[(Int, Point.Float)], centroid: Point.Float): Float = {
    (ptsIn.map(p => distanceBetweenPts(p._2.lat, p._2.lng, centroid.lat, centroid.lng) * p._1).sum / ptsIn.map(_._1).sum).toFloat
  }

  /**
    * Compute LocationStats for sequence of points
    * @param ptsIn input points
    * @return cationStats for sequence input points
    */
  private [installbase] def locStats(ptsIn: Seq[Point.Float]):Option[LocationStats] = {
    val n = ptsIn.size

    if (n == 0) {
      None
    } else {
      val (lats, lngs) = ptsIn.unzip
      val modalPoint = modal4DigitPoint(ptsIn)
      val centroid = Point(
        lats.sum / n,
        lngs.sum / n
      )
      val distsSeq = ptsDists(ptsIn, centroid)
      val avgDist =ptsDists(ptsIn, centroid).sum / ptsIn.size
      val varDist = distsSeq.map(a => math.pow(a - avgDist, 2)).sum / distsSeq.size
      Some(LocationStats(modalPoint, centroid, avgDist.toFloat, varDist.toFloat))
    }
  }

  private [installbase] def hashPointAtNDigits(nDigits: Int): Point.Float => Int = (pt: Point.Float) => {
    val multiplier = math.pow(10, nDigits)
    val x = (pt.lat * multiplier).round.hashCode() // round to 4 digits
    val y = (pt.lng * multiplier).round.hashCode()
    x * 31 + y
  }

  /**
    * Count the number of times each point is seen when hashed at 3 digits
    * @param ptsIn input points
    * @param maxPoints only return at most this many points
    * @return point mapped to the number of times they were seen
    */
  private [installbase] def getThreeDigitModalPoints(ptsIn: Seq[Point.Float], maxPoints: Int): Map[Point.Float, Int] = {
    val pts = ptsIn.map(p => (p, hashPointAtNDigits(3)(p))).groupBy(_._2)
    val ptsCounts = pts.map(pList => (pList._2.maxBy(innerPt => howManyDecimalsInPt(innerPt._1))._1, pList._2.size))
    if(maxPoints < 0) {
      ptsCounts
    } else {
      mutable.PriorityQueue(ptsCounts.toSeq: _*)(Ordering.by((_: (Point.Float, Int))._2)).take(maxPoints).toMap
    }
  }

  /**
    * Get model 4 digit point
    * @param ptsIn points in
    * @return highest precision 4 digit point
    */
  private [installbase] def modal4DigitPoint(ptsIn: Seq[Point.Float]): Point.Float = {
    val winningPoints = ptsIn.map(p => (p, hashPointAtNDigits(4)(p))).groupBy(_._2).maxBy(_._2.size)._2
    winningPoints.maxBy(p => howManyDecimalsInPt(p._1))._1
  }

  private def locStatsExtended(locTsIn: Seq[LocTs]): Option[LocationStatsExtended] = {
    val simpleLocStats = locStats(locTsIn.map(_.point))

    simpleLocStats.map(simpleStates => {
      if(locTsIn.size >= DB_SCAN_N_CUTTOFF &&
        simpleStates.avg_centroid_dist > GOOD_CENTRIOD_CUTTOFF
      ) {
        runDBScan(locTsIn, simpleStates)
      } else {
        // If we have too few point, we already have a good cluster, or we have too bad of a cluster to even try use simple stats
        LocationStatsExtended.fromSimpleStats(simpleStates)
      }
    })
  }


  /**
    * Run DB Scan
    * @param locTsIn Locations in
    * @param simpleStates simple, centroid based, stats
    * @return DB Scan Extended stats
    */
  def runDBScan(locTsIn: Seq[LocTs], simpleStates: LocationStats): LocationStatsExtended = {
    val indexedSeqPts = locTsIn.map(_.point).toIndexedSeq
    val (binnedPts, labels, centroidsAndCounts) = DBScan.clusterGPSWithBinningTopK(
      indexedSeqPts, NEIGHBORHOOD_CUTOFF, NEIGHBORHOOD_MIN_SIZE, Some(DB_SCAN_MAX_UNIQUE_PTS))

    if (centroidsAndCounts.nonEmpty) {
      val biggestClusterIndex = centroidsAndCounts.indices.map(i => (centroidsAndCounts(i)._2, i)).maxBy(_._1)._2
      val biggestCentroid = centroidsAndCounts(biggestClusterIndex)._1

      val biggestClusterPts = binnedPts.indices.filter(labels(_) == biggestClusterIndex + 1).map(binnedPts)
      val biggestCentroidDist = avgDistWithCounts(biggestClusterPts, biggestCentroid)

      val biggestCentroidCount = biggestClusterPts.map(p => p._1).sum

      LocationStatsExtended(
        simpleStates.modal_loc,
        simpleStates.centroid,
        simpleStates.avg_centroid_dist,
        simpleStates.var_centroid_dist,
        Some(biggestCentroid),
        Some(biggestCentroidDist),
        Some(biggestCentroidCount),
        Some(distanceBetweenPts(simpleStates.centroid.lat, simpleStates.centroid.lng, biggestCentroid.lat, biggestCentroid.lng).toFloat),
        Some(distanceBetweenPts(simpleStates.modal_loc.lat, simpleStates.modal_loc.lng, biggestCentroid.lat, biggestCentroid.lng).toFloat)
      )
    } else {
      LocationStatsExtended.fromSimpleStats(simpleStates)
    }
  }


  /**
    * True if IP is truncated
    * param ip IP address to check if truncated
    * param index If IP is v6, we use a lookup table by carrier that outputs an index that should have trailing 0s
    * @return true if IP is truncated
    */
  def isTruncatedIP : UserDefinedFunction =
    udf[Boolean, Array[Byte], Int] ((ip: Array[Byte], index: Int) => {
      if (ip == null)
        true
      else if(ip.length == 4)
        ip(3) == 0
      else if (index > 0 && index <= 15)
        ip(16-index) == 0
      else {
        ip(4) == 0 &&
          ip(5) == 0 &&
          ip(6) == 0 &&
          ip(7) == 0 &&
          ip(8) == 0 &&
          ip(9) == 0 &&
          ip(10) == 0 &&
          ip(11) == 0 &&
          ip(12) == 0 &&
          ip(13) == 0 &&
          ip(14) == 0 &&
          ip(15) == 0
      }
    })

  /**
    * True if column 'obs.t_local' is inside night time hours
    * @param spark spark session
    * @return true if column 'obs.t_local' is inside night time hours
    */
  def isNightTime(c: Column)(implicit spark: SparkSession): Column = {
    hour(c) <= 6 || hour(c) >= 19
  }

  /**
    * True if column 'obs.t_local' is inside house hold hours
    * @param spark spark session
    * @return true if column 'obs.t_local' is inside house hold hours
    */
  def isHouseholdHour(c: Column)(implicit spark: SparkSession): Column = {

    (hour(c) <= 5  && (dayofweek(c) >= 2 && dayofweek(c) <= 6 )) || // before 5 am and Mon thru Friday
    (hour(c) >= 20 && (dayofweek(c) >= 2 &&dayofweek(c) <= 5)) || // after 8 pm and Mon thru Thurs
    (hour(c) >= 20 && dayofweek(c) === 1)   // after 8 pm on Sunday then 1
  }


}
