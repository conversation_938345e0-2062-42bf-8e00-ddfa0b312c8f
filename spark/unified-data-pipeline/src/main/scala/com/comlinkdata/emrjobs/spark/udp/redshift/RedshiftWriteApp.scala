package com.comlinkdata.emrjobs.spark.udp.redshift

import com.comlinkdata.largescale.commons.RedshiftUtils.{RedshiftConfig, redshiftWrite}
import java.net.URI
import org.apache.spark.sql.{SaveMode, SparkSession}
import scopt.OptionParser


object RedshiftWriteApp extends App {
  private case class Config(
    source: String = null,
    table: String = null,
    mode: SaveMode = SaveMode.Append,
    filter: Option[String] = None,
    columns: Seq[String] = Seq.empty,
    columnRename: Map[String, String] = Map.empty,
    tempLocation: String = null,
    jdbcEndpoint: String = null,
    userName: String = null,
    parameterStoreKey: String = null
  )

  new OptionParser[Config]("RS_WRITE") {
    opt[String]('s', "source").required.action((source, config) => config.copy(source = source))
    opt[String]('t', "table").required.action((table, config) => config.copy(table = table))
    opt[String]('m', "mode").action((mode, config) => config.copy(mode = SaveMode.valueOf(mode)))
    opt[String]('f', "filter").action((filter, config) => config.copy(filter = Some(filter)))
    opt[Seq[String]]('c', "columns").action((columns, config) => config.copy(columns = columns))
    opt[Map[String, String]]('r', "rename").action((renames, config) => config.copy(columnRename = renames))
    opt[String]('l', "temp-location").required.action((location, config) => config.copy(tempLocation = location))
    opt[String]('j', "jdbc-endpoint").required.action((jdbcEndpoint, config) => config.copy(jdbcEndpoint = jdbcEndpoint))
    opt[String]('u', "user-name").required.action((userName, config) => config.copy(userName = userName))
    opt[String]('p', "param-store-key").required.action((psk, config) => config.copy(parameterStoreKey = psk))
  }.parse(args, Config()) match {
    case Some(config) =>
      println(s"Running with config $config")
      implicit val rs: RedshiftConfig = RedshiftConfig(
        rsTemporaryLocation = URI create config.tempLocation,
        rsJdbcEndpoint = config.jdbcEndpoint,
        rsUserName = config.userName,
        rsParameterStoreKey = config.parameterStoreKey)
      val spark = SparkSession.builder.appName(getClass.getSimpleName.stripSuffix("$")).master("local[*]").getOrCreate
      val unfilteredData = spark.read.parquet(config.source)
      val columns = if (config.columns.isEmpty) unfilteredData.columns.toSeq else config.columns
      val (filteredData, deleteSql) = config.filter match {
        case Some(filter) =>
          println(s"Filtering source data using [$filter]")
          val whereClause = config.columnRename.foldLeft(filter) {
            case (expr, (oldName, newName)) =>
              expr.replace(oldName, newName)
          }
          val deleteSql = s"DELETE FROM ${config.table} WHERE $whereClause"
          println(s"Cleaning up existing data using [$deleteSql]")
          (unfilteredData.filter(filter), Some(deleteSql))
        case _ =>
          (unfilteredData, None)
      }
      val renamedData = config.columnRename.foldLeft(filteredData.selectExpr(columns: _*)) {
        case (df, (oldName, newName)) =>
          df.withColumnRenamed(oldName, newName)
      }
      println(s"Writing ${renamedData.count} records to ${config.table}")
      redshiftWrite(data = renamedData, tableName = config.table, saveMode = config.mode, preActionQuery = deleteSql)
    case _ =>
  }
}
