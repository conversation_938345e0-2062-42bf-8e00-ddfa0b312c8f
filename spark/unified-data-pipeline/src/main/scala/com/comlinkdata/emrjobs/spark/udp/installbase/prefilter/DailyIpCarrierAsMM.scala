package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.largescale.commons.{TimeSeriesLocation, UriDFTableRW}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.apache.spark.sql.functions.{col, lit, upper, when}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

import java.net.URI
import java.time.LocalDate

object DailyIpCarrierAsMM {
  abstract class DCBase(dailyIpCarrierPath: URI, filter: DataFrame => DataFrame) {

    private def readDailyIpCarrier(runDate: LocalDate)(implicit spark: SparkSession): DataFrame = {
      val tsl = TimeSeriesLocation
        .ofYmdDatePartitions(dailyIpCarrierPath)
        .withPartition(ComlinkdataDatasource.mw)
        .build.partition(runDate)
      UriDFTableRW.fromStr(tsl).read()
    }

    private def selectOutCols(df: DataFrame)(implicit spark: SparkSession): DataFrame = {
      import spark.implicits._
      df.select(
        $"ip",
        $"carrier",
        when($"connection_type" === "WIFI", "CABLE/DSL").otherwise($"connection_type") as "connection_type",
        lit("") as "organization",
        lit("") as "autonomous_system_number")
    }

    def read(runDate: LocalDate)(implicit spark: SparkSession): DataFrame = {
      val dailyIpCIn = readDailyIpCarrier(runDate: LocalDate)
      val filtered = dailyIpCIn.transform(filter)
      selectOutCols(filtered)
    }
  }

  class DailyIpCarrierWifi(dailyIpCarrierPath: URI) extends
      DCBase(dailyIpCarrierPath, df => df.filter(upper(col("connection_type")) === "WIFI"))
  class DailyIpCarrierVZW(dailyIpCarrierPath: URI, verizonIpBlocks: Dataset[VerizonFwIp]) extends
    DCBase(dailyIpCarrierPath, MMReader.verizonFilter(verizonIpBlocks))
  class DailyIpCarrierTMO(dailyIpCarrierPath: URI) extends DCBase(dailyIpCarrierPath, MMReader.tmobileFilter)
  class DailyIpCarrierWifiCell(dailyIpCarrierPath: URI) extends
    DCBase(dailyIpCarrierPath, df => df.filter(upper(col("connection_type")) === "WIFI" || upper(col("connection_type")) === "CELLULAR"))
}
