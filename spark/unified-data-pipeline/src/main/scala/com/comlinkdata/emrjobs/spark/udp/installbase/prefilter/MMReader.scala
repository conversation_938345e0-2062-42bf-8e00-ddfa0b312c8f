package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.largescale.commons.{TimeSeriesLocation, UriDFTableRW}
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SparkSession, Dataset, Row, DataFrame}

import java.net.URI
import java.time.LocalDate

object MMReader {
  abstract class MMBase(ds: ComlinkdataDatasource, maxmindPath: URI, mmFilter: DataFrame => DataFrame) {
    def read(runDate: LocalDate)(implicit spark: SparkSession): DataFrame = {

      val mmIn: DataFrame = readBaseMM(runDate)
      val mmFiltered = mmIn.transform(mmFilter)
      val maxmindOutCols: DataFrame = revealOrgAug2020Fix(mmFiltered)

      maxmindOutCols
    }

    private def readBaseMM(runDate: LocalDate)(implicit spark: SparkSession) = {
      val maxmindPaths = Seq(
        TimeSeriesLocation
          .ofYmdDatePartitions(maxmindPath)
          .withPartition(ds)
          .withPartition("ipversion", 4)
          .build.partition(runDate),
        TimeSeriesLocation
          .ofYmdDatePartitions(maxmindPath)
          .withPartition(ds)
          .withPartition("ipversion", 6)
          .build.partition(runDate)
      )

      val mmIn = UriDFTableRW.readAll(maxmindPaths: _*)
      mmIn
    }

    private def revealOrgAug2020Fix(mmFiltered: Dataset[Row])(implicit spark: SparkSession): DataFrame = {
      import spark.implicits._

      val maxmindOutCols = if (mmFiltered.columns.contains("organization")) {
        mmFiltered.select(
          $"ip",
          $"carrier",
          $"connection_type",
          $"organization",
          $"autonomous_system_number")
      } else { // Starting Aug 2020 Reveal doesn't have some columns
        mmFiltered.select(
          $"ip",
          $"carrier",
          $"connection_type",
          lit("") as "organization",
          lit("") as "autonomous_system_number"
        )
      }
      maxmindOutCols
    }
  }

  class MaxMindWifi(ds: ComlinkdataDatasource, maxmindPath: URI) extends MMBase(ds, maxmindPath, wifiFilter)
  class MaxMindVZW(ds: ComlinkdataDatasource, maxmindPath: URI, verizonIpBlocks: Dataset[VerizonFwIp]) extends MMBase(ds, maxmindPath, verizonFilter(verizonIpBlocks))
  class MaxMindTMO(ds: ComlinkdataDatasource, maxmindPath: URI) extends MMBase(ds, maxmindPath, tmobileFilter)
  class MaxMindAll(ds: ComlinkdataDatasource, maxmindPath: URI) extends MMBase(ds, maxmindPath, x=>x)

  private def wifiFilter(df: DataFrame): DataFrame ={
    df.filter(upper(col("connection_type")) === "CABLE/DSL" ||
      upper(col("connection_type")) === "DIALUP" ||
      upper(col("connection_type")) === "CORPORATE" ||
      upper(col("connection_type")) === "SATELLITE")
  }

  private [prefilter] def verizonFilter(verizonIpBlocks: Dataset[VerizonFwIp])(df: DataFrame): DataFrame ={
    val verizonIps = verizonIpBlocks.select(col("ip_2_octets"))
    val twoOctets = udf((ip: Array[Byte]) => ip(0) + "." + ip(1))
    df
      .filter(col("carrier").rlike("(?i)verizon"))
      .filter(length(col("ip")) === 4)
      .join(verizonIps, col("ip_2_octets") === twoOctets(col("ip")))
  }

  private[prefilter] def tmobileFilter(df: DataFrame): DataFrame = {
    df.filter(col("carrier").rlike("(?i)t-mobile usa"))
  }
}
