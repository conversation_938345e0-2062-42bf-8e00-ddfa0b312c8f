{"class": "org.apache.spark.ml.classification.RandomForestClassificationModel", "timestamp": 1649085394968, "sparkVersion": "2.4.0", "uid": "RandomForestClassifier_82577bccf517", "paramMap": {"featuresCol": "features", "labelCol": "label"}, "defaultParamMap": {"minInstancesPerNode": 1, "rawPredictionCol": "rawPrediction", "probabilityCol": "probability", "cacheNodeIds": false, "maxBins": 32, "featuresCol": "features", "maxMemoryInMB": 256, "impurity": "gini", "numTrees": 20, "labelCol": "label", "predictionCol": "prediction", "checkpointInterval": 10, "maxDepth": 5, "featureSubsetStrategy": "auto", "seed": -4853434554338539879, "minInfoGain": 0.0, "subsamplingRate": 1.0}, "numFeatures": 5, "numClasses": 2, "numTrees": 20}