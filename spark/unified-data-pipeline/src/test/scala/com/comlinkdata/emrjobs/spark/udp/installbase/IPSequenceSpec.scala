package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.installbase.IPSequenceSetRunner._
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, LocalDateRange}
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.comlinkdata.largescale.schema.udp.installbase.IPHighConfidence
import com.comlinkdata.largescale.schema.udp.location.Point
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.functions.{to_date, lit, concat}
import org.junit.rules.TemporaryFolder

import java.net.URI
import java.sql.Date
import java.time.LocalDate


class IPSequenceSpec extends CldSparkBaseSpec with LazyLogging {
  import spark.implicits._

  private val startDate = LocalDate.of(2021, 10, 1)
  private val endDate = LocalDate.of(2021, 10, 3)

  private val ifas: Array[Ifa] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]
  private val ips: Array[Ip] = (32 to 62).map( x => Array(192.toByte, 168.toByte, 0.toByte, x.toByte)).toArray[Array[Byte]]
  private val hhid: Array[Array[Byte]] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]

  private val DAYS_TO_LOOK_BACK = 7

  describe("full run") {
    val ifaToIpSeq: Seq[IfaToIpYMD] = Seq(
      (ifas(0), ips(0), "2021", "09", "28"), // case1 -- has high confidence data
      (ifas(0), ips(0), "2021", "09", "29"), // case1
      (ifas(0), ips(0), "2021", "09", "30"), // case1
      (ifas(0), ips(0), "2021", "10", "01"), // case1
      (ifas(0), ips(0), "2021", "10", "02"), // case1
      (ifas(0), ips(0), "2021", "10", "03"), // case1

      (ifas(1), ips(2), "2021", "09", "28"), // case2 -- has high confidence on old ip
      (ifas(1), ips(2), "2021", "09", "29"), // case2
      (ifas(1), ips(2), "2021", "09", "30"), // case2
      (ifas(1), ips(2), "2021", "10", "01"), // case2
      (ifas(1), ips(2), "2021", "10", "02"), // case2
      (ifas(1), ips(2), "2021", "10", "03"), // case2

      (ifas(2), ips(3), "2021", "09", "28"), // case3
      (ifas(2), ips(3), "2021", "09", "29"), // case3
      (ifas(2), ips(3), "2021", "09", "30"), // case3
      (ifas(2), ips(3), "2021", "10", "01"), // case3
      (ifas(2), ips(3), "2021", "10", "02"), // case3
      (ifas(2), ips(3), "2021", "10", "03"), // case3
    ).map(i => IfaToIpYMD(
      ip = i._2,
      ifa = i._1,
      carrier = "Comcast",
      consolidated_id = "Comcast",
      connection_type = "Cable/DSL",
      organization = "Comcast",
      sp_platform = "1005",
      autonomous_system_number = "1",
      obs_count = 10,
      night_obs_count = 1 ,
      household_obs_count =0 ,
      household_weighted_obs_count = 1 ,
      business_weighted_obs_count = 0,
      year = i._3,
      month = i._4,
      day = i._5))

    val highConfidenceSeq: Seq[IPHighConfidence] = Seq(
      IPHighConfidence(
        ifa = ifas(0),
        ip = ips(0),
        household_id = hhid(0),
        obs_count = 100,
        night_obs_count = 90,
        carrier = "Comcast",
        ip_min_date = Date.valueOf("2021-09-01"),
        ip_max_date = Date.valueOf("2021-10-01"),
        case_method = 0,
        census_block_id = "",
        census_group_id = "",
        percent_block = 0f,
        discounted_count = 0f,
        household_count = 1l,
        best_point = Point(0f, 0f),
        ifa_max_date = Date.valueOf("2021-10-01"),
        home_hour_date_count = 1,
        global_ip_min_date = Date.valueOf("2021-10-01"),
        year = "2021",
        month = "10",
        day = "01"),
      IPHighConfidence(
        ifas(1),
        ips(1),
        household_id = hhid(1),
        obs_count = 100,
        night_obs_count = 90,
        carrier = "Comcast",
        ip_min_date = Date.valueOf("2021-09-01"),
        ip_max_date = Date.valueOf("2021-10-01"),
        case_method = 0,
        census_block_id = "",
        census_group_id = "",
        percent_block = 0f,
        discounted_count = 0f,
        household_count = 1l,
        best_point = Point(0f, 0f),
        ifa_max_date = Date.valueOf("2021-10-01"),
        home_hour_date_count = 1,
        global_ip_min_date = Date.valueOf("2021-10-01"),
        year = "2021",
        month = "10",
        day = "01")
    )

    val ipClassifierPath = this.getClass().getResource("/com/comlinkdata/emrjobs/spark/udp/installbase/bus_res_classifier.model").toURI

    ignore("with parquet") {

      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()
      val folder = beforeAllTempFolder.newFolder()
      folder.delete()
      val basePath: URI = folder.toURI

      val ifaToIpDataRoot = basePath.resolve("ifaToIp")
      spark
        .createDataFrame(ifaToIpSeq).write
        .partitionBy("year", "month", "day")
        .parquet(ifaToIpDataRoot.toString)

      val hcPath = basePath.resolve("hc")
      spark
        .createDataFrame(highConfidenceSeq).write
        .partitionBy("year", "month", "day")
        .parquet(hcPath.toString)

      val outPath = basePath.resolve("out")
      val config: IPSequenceSetConfig = IPSequenceSetConfig(
        ifaToIpPath = ifaToIpDataRoot,
        highConfidencePath = hcPath,
        outputPath = outPath,
        startDate = Some(startDate),
        endDate = Some(endDate),
        highConfidenceDay = Some(startDate),
        maxDaysToRun = None,
        daysToLookBack = DAYS_TO_LOOK_BACK,
        repartition = None
      )

      IPSequenceSetRunner.runJob(config)

      val outDates = {
        LocalDateRange.of(startDate, endDate)
      }
      val outPaths = outDates.map(TimeSeriesLocation.ofYmdDatePartitions(outPath).build.partition)
      val data = spark.read
        .option(ReadOpts.basePath, outPath.toString)
        .parquet(outPaths: _*).orderBy("day")

      data.count shouldBe 9
    }

    it("without parquet") {

      val dsDay0: Dataset[IPHighConfidence] = highConfidenceSeq.toDS()

      val dsIfa = ifaToIpSeq.toDS()
        .withColumn("date", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))
        .filter($"date" <= LocalDate.of(2021, 10, 1))
        .drop("date")
        .as[IfaToIpYMD]

      val data = processDay(dsIfa, dsDay0, LocalDate.of(2021, 10, 1))

      data.count shouldBe 3
    }
  }

  describe("Ifa Candidates"){
    it("Single day"){
      // night count >> obs count
      val ds: Dataset[IfaToIpYMD] = Seq(
        // night count diff
        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "01"),
        IfaToIpYMD(ips(1), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 10, 1, 1, 10, "2021", "10", "01"), // pick this one
        // obs count diff
        IfaToIpYMD(ips(2), ifas(2), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 10, 1, 1, 10, "2021", "10", "01"),
        IfaToIpYMD(ips(3), ifas(2), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 20, 10, 1, 1, 10, "2021", "10", "01"), // pick this one
      ).toDS()

      val rows: Array[IfaWithIpCandidate] = ds
        .transform(ifaWithIpCandidates)
        .orderBy($"ifa")
        .collect()
      rows.length shouldBe 2
      rows(0).ip shouldBe ips(1)
      rows(1).ip shouldBe ips(3)
    }

    it("Multiday"){
      val ds: Dataset[IfaToIpYMD] = Seq(
        //night_day_count >> date_count
        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 0, 1, 1, 10, "2021", "10", "01"),
        IfaToIpYMD(ips(1), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 10, 1, 1, 10, "2021", "10", "01"), // pick me
        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 10, 1, 1, 10, "2021", "10", "02"),
        IfaToIpYMD(ips(1), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 10, 1, 1, 10, "2021", "10", "02"), // pick me
        //night_Day_count > date_count > night_obs
        IfaToIpYMD(ips(2), ifas(2), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "01"),
        IfaToIpYMD(ips(3), ifas(2), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 10, 1, 1, 10, "2021", "10", "01"), //pick me
        IfaToIpYMD(ips(2), ifas(2), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 10, 1, 1, 10, "2021", "10", "02"),
        IfaToIpYMD(ips(3), ifas(2), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 10, 1, 1, 10, "2021", "10", "02")  //pick me
      ).toDS()

      val rows: Array[IfaWithIpCandidate] = ds
        .transform(ifaWithIpCandidates)
        .orderBy($"ifa")
        .collect()
      rows.length shouldBe 2
      rows(0).ip shouldBe ips(1)
      rows(0).ip_min_date.toLocalDate shouldBe LocalDate.of(2021, 10, 1)
      rows(0).ip_max_date.toLocalDate shouldBe LocalDate.of(2021, 10, 2)
      rows(1).ip shouldBe ips(3)
      rows(1).ip_min_date.toLocalDate shouldBe LocalDate.of(2021, 10, 1)
      rows(1).ip_max_date.toLocalDate shouldBe LocalDate.of(2021, 10, 2)
    }

    it("Multiday, ifa with shared IPs"){
      val ds: Dataset[IfaToIpYMD] = Seq(
        //night_day_count >> date_count

        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 0, 1, 1, 10, "2021", "10", "01"),
        IfaToIpYMD(ips(0), ifas(1), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 10, 1, 1, 10, "2021", "10", "01"),
        IfaToIpYMD(ips(0), ifas(2), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 10, 1, 1, 10, "2021", "10", "02"),
        IfaToIpYMD(ips(0), ifas(3), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 10, 1, 1, 10, "2021", "10", "02"),
      ).toDS()

      val rows: Array[IfaWithIpCandidate] = ds
        .transform(ifaWithIpCandidates)
        .orderBy($"ifa")
        .collect()

      rows.length shouldBe 4
      rows(0).ip shouldBe ips(0)
      rows(1).ip shouldBe ips(0)
      rows(2).ip shouldBe ips(0)
      rows(3).ip shouldBe ips(0)

    }
  }

  describe("IP Lifespan"){
    it("Single day, single ifa"){

      val ds: Dataset[IfaToIpYMD] = Seq(
        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "01")

      ).toDS()
      val rows: Array[IPLifespan] = ds
        .transform(ipLifespan)
        .orderBy($"ip")
        .collect()
      rows.length shouldBe 1
      rows(0).ip shouldBe ips(0)
      rows(0).ip_min_date.toLocalDate shouldBe LocalDate.of(2021, 10, 1)
      rows(0).ip_max_date.toLocalDate shouldBe LocalDate.of(2021, 10, 1)
    }
    it("Single day, multi ip"){
      val ds: Dataset[IfaToIpYMD] = Seq(
        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "01"),
        IfaToIpYMD(ips(1), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "01")
      ).toDS()
      val rows: Array[IPLifespan] = ds
        .transform(ipLifespan)
        .orderBy($"ip")
        .collect()
      rows.length shouldBe 2
      rows(0).ip shouldBe ips(0)
      rows(0).ip_min_date.toLocalDate shouldBe LocalDate.of(2021, 10, 1)
      rows(0).ip_max_date.toLocalDate shouldBe LocalDate.of(2021, 10, 1)
      rows(1).ip shouldBe ips(1)
      rows(1).ip_min_date.toLocalDate shouldBe LocalDate.of(2021, 10, 1)
      rows(1).ip_max_date.toLocalDate shouldBe LocalDate.of(2021, 10, 1)
    }
    it("multi day, single ip"){

      val ds: Dataset[IfaToIpYMD] = Seq(
        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "01"),
        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "02")
      ).toDS()
      val rows: Array[IPLifespan] = ds
        .transform(ipLifespan)
        .orderBy($"ip")
        .collect()
      rows.length shouldBe 1
      rows(0).ip shouldBe ips(0)
      rows(0).ip_min_date.toLocalDate shouldBe LocalDate.of(2021, 10, 1)
      rows(0).ip_max_date.toLocalDate shouldBe LocalDate.of(2021, 10, 2)
    }
    it("multi day, multi ifa"){
      val ds: Dataset[IfaToIpYMD] = Seq(
        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "01"),
        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "02"),
        IfaToIpYMD(ips(1), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "01"),
        IfaToIpYMD(ips(1), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "02")
      ).toDS()
      val rows: Array[IPLifespan] = ds
        .transform(ipLifespan)
        .orderBy($"ip")
        .collect()
      rows.length shouldBe 2
      rows(0).ip shouldBe ips(0)
      rows(0).ip_min_date.toLocalDate shouldBe LocalDate.of(2021, 10, 1)
      rows(0).ip_max_date.toLocalDate shouldBe LocalDate.of(2021, 10, 2)
      rows(1).ip shouldBe ips(1)
      rows(1).ip_min_date.toLocalDate shouldBe LocalDate.of(2021, 10, 1)
      rows(1).ip_max_date.toLocalDate shouldBe LocalDate.of(2021, 10, 2)
    }
    it("best carrier"){

      val ds: Dataset[IfaToIpYMD] = Seq(
        IfaToIpYMD(ips(0), ifas(0), "C1", "C1", "Cable/DSL", "Comcast", "1005", "1", 20, 1, 1, 1, 10, "2021", "10", "01"), // this one because more count
        IfaToIpYMD(ips(0), ifas(0), "C2", "C2", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "02"),
        IfaToIpYMD(ips(1), ifas(0), "C2", "C2", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "01"),
        IfaToIpYMD(ips(1), ifas(0), "C1", "C1", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "02"), // this one because later
      ).toDS()
      val rows: Array[IPLifespan] = ds
        .transform(ipLifespan)
        .orderBy($"ip")
        .collect()
      rows.length shouldBe 2

      rows(0).ip shouldBe ips(0)
      rows(0).carrier shouldBe "C1"

      rows(1).ip shouldBe ips(1)
      rows(1).carrier shouldBe "C1"
    }
  }

  describe("Cases"){
    val highConfidenceSeq: Seq[IPHighConfidence] = Seq(
      IPHighConfidence(
        ifa = ifas(0),
        ip = ips(0),
        household_id = hhid(0),
        obs_count = 100,
        night_obs_count = 90,
        carrier = "Comcast",
        ip_min_date = Date.valueOf("2021-09-01"),
        ip_max_date = Date.valueOf("2021-10-01"),
        case_method = 0,
        census_block_id = "",
        census_group_id = "",
        percent_block = 0f,
        discounted_count = 0f,
        household_count = 1l,
        best_point = Point(0f, 0f),
        ifa_max_date = Date.valueOf("2021-10-01"),
        home_hour_date_count = 1,
        global_ip_min_date = Date.valueOf("2021-10-01"),
        year = "2021",
        month = "10",
        day = "01"),
      IPHighConfidence(
        ifas(1),
        ips(1),
        household_id = hhid(1),
        obs_count = 100,
        night_obs_count = 90,
        carrier = "Comcast",
        ip_min_date = Date.valueOf("2021-09-01"),
        ip_max_date = Date.valueOf("2021-10-01"),
        case_method = 0,
        census_block_id = "",
        census_group_id = "",
        percent_block = 0f,
        discounted_count = 0f,
        household_count = 1l,
        best_point = Point(0f, 0f),
        ifa_max_date = Date.valueOf("2021-10-01"),
        home_hour_date_count = 1,
        global_ip_min_date = Date.valueOf("2021-10-01"),
        year = "2021",
        month = "10",
        day = "01")
    )

    it("1 - Same Ips - Matched IP"){
      val hc = highConfidenceSeq.toDS()
      val ifa: Dataset[IfaWithIpCandidate] = Seq(
        IfaWithIpCandidate(ifas(0), ips(0), 1, 1, 1, 1, Date.valueOf("2021-10-01"), Date.valueOf("2021-10-01"), "Comcast", Array(ips(0)), Date.valueOf("2021-10-01"))
      ).toDS()
      val rows: Array[CaseMethod] = ifa
        .transform(caseMethod(hc))
        .orderBy($"ifa")
        .collect()

      rows.length shouldBe 1
      rows(0).case_method shouldBe 1
    }
    it("1 - Same Ips - Matched IP History"){
      val hc = highConfidenceSeq.toDS()
      val ifa: Dataset[IfaWithIpCandidate] = Seq(
        IfaWithIpCandidate(ifas(0), ips(1), 1, 1, 1, 1, Date.valueOf("2021-10-01"), Date.valueOf("2021-10-01"), "Comcast", Array(ips(0), ips(1)),Date.valueOf("2021-10-01"))
      ).toDS()
      val rows: Array[CaseMethod] = ifa
        .transform(caseMethod(hc))
        .orderBy($"ifa")
        .collect()

      rows.length shouldBe 1
      rows(0).case_method shouldBe 1
    }
    it("2 - Potential changed IPs"){
      val hc = highConfidenceSeq.toDS()
      val ifa: Dataset[IfaWithIpCandidate] = Seq(
        IfaWithIpCandidate(ifas(0), ips(1), 1, 1, 1, 1, Date.valueOf("2021-10-01"), Date.valueOf("2021-10-01"), "Comcast", Array(ips(1)),Date.valueOf("2021-10-01"))
      ).toDS()
      val rows: Array[CaseMethod] = ifa
        .transform(caseMethod(hc))
        .orderBy($"ifa")
        .collect()

      rows.length shouldBe 1
      rows(0).case_method shouldBe 2
    }
    it("3 - New Ifas (Devices)"){
      val hc = highConfidenceSeq.toDS()
      val ifa: Dataset[IfaWithIpCandidate] = Seq(
        IfaWithIpCandidate(ifas(2), ips(1), 1, 1, 1, 1, Date.valueOf("2021-10-01"), Date.valueOf("2021-10-01"), "Comcast", Array(ips(1)),Date.valueOf("2021-10-01"))
      ).toDS()
      val rows: Array[CaseMethod] = ifa
        .transform(caseMethod(hc))
        .orderBy($"ifa")
        .collect()

      rows.length shouldBe 1
      rows(0).case_method shouldBe 3
    }
  }

  describe("Case 2 (change candidates)"){
    val dataLifespan: Seq[IPLifespan] = Seq(
      IPLifespan(ips(0),Date.valueOf("2021-09-23"), Date.valueOf("2021-10-01"), "carrier"),
      IPLifespan(ips(1),Date.valueOf("2021-10-01"), Date.valueOf("2021-10-07"), "carrier")
    )
    it("has no case 2s"){
      val dsLifespan: Dataset[IPLifespan] = dataLifespan.toDS()
      val dsCases: Dataset[CaseMethod] = Seq(
        CaseMethod(
          ifa = ifas(0),
          ip = ips(1),
          obs_count = 1,
          night_obs_count = 1,
          ip_min_date = Date.valueOf("2021-10-01"),
          ip_max_date = Date.valueOf("2021-10-01"),
          case_method = 3,
          old_ip = ips(3))
      ).toDS()
      val rows: Array[CaseMethod] = dsCases
        .transform(changeCandidates(dsLifespan))
        .orderBy($"ifa")
        .collect()
      rows.length shouldBe 0
    }
    it("old.ip does not exist - switch ips"){
      val dsLifespan: Dataset[IPLifespan] = dataLifespan.toDS()
      val dsCases: Dataset[CaseMethod] = Seq(
        CaseMethod(
          ifa = ifas(0),
          ip = ips(1),
          obs_count = 1,
          night_obs_count = 1,
          ip_min_date = Date.valueOf("2021-10-01"),
          ip_max_date = Date.valueOf("2021-10-01"),
          case_method = 2,
          old_ip = ips(3))
      ).toDS()
      val rows: Array[CaseMethod] = dsCases
        .transform(changeCandidates(dsLifespan))
        .orderBy($"ifa")
        .collect()
      rows.length shouldBe 1
      rows(0).ip shouldBe ips(1)
    }
    it("old.ip is before new.ip - switch ips"){
      val dsLifespan: Dataset[IPLifespan] = dataLifespan.toDS()
      val dsCases: Dataset[CaseMethod] = Seq(
        CaseMethod(
          ifa = ifas(0),
          ip = ips(1),
          obs_count = 1,
          night_obs_count = 1,
          ip_min_date = Date.valueOf("2021-10-01"),
          ip_max_date = Date.valueOf("2021-10-01"),
          case_method = 2,
          old_ip = ips(0))
      ).toDS()
      val rows: Array[CaseMethod] = dsCases
        .transform(changeCandidates(dsLifespan))
        .orderBy($"ifa")
        .collect()
      rows.length shouldBe 1
      rows(0).ip shouldBe ips(1)
    }
    it("new.ip is before old.ip - throw away record"){
      val dsLifespan: Dataset[IPLifespan] = dataLifespan.toDS()
      val dsCases: Dataset[CaseMethod] = Seq(
        CaseMethod(
          ifa = ifas(0),
          ip = ips(0),
          obs_count = 1,
          night_obs_count = 1,
          ip_min_date = Date.valueOf("2021-10-01"),
          ip_max_date = Date.valueOf("2021-10-01"),
          case_method = 2,
          old_ip = ips(1))
      ).toDS()
      val rows: Array[CaseMethod] = dsCases
        .transform(changeCandidates(dsLifespan))
        .orderBy($"ifa")
        .collect()
      rows.length shouldBe 0
    }
  }
}
