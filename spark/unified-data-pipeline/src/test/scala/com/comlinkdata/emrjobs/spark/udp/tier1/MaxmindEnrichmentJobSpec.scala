package com.comlinkdata.emrjobs.spark.udp.tier1

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.tier1.MaxmindSchema.{ConnectionType, Isp}
import com.comlinkdata.emrjobs.spark.udp.tier1.RangeMap.Entry
import com.comlinkdata.largescale.commons.Utils.ipBinaryToStringColumn
import com.comlinkdata.largescale.schema.udp.Ip
import com.comlinkdata.largescale.schema.udp.tier0.UdpRaw

import java.net.InetAddress
import java.sql.{Timestamp, Date}
import org.apache.spark.sql.functions.{lit, when}
import org.jboss.netty.handler.ipfilter.CIDR

import scala.language.implicitConversions

class MaxmindEnrichmentJobSpec extends CldSparkBaseSpec {
  implicit def cidrToEntry[T](value: (String, T)): Entry[T] = {
    val su = CIDR.newCIDR(value._1)
    new Entry(su.getBaseAddress.getAddress, su.getEndAddress.getAddress, value._2)
  }

  implicit def stringToIp(value: String): Ip =
    InetAddress.getByName(value).getAddress

  private val sample = UdpRaw(
    Date.valueOf("2020-06-06"), "*******", None, None, None, None, None, None, None, None, None, None, None, None,
    Timestamp.valueOf("2020-06-06 12:00:01"), None, None, None, None, None, None, None, None, None, None)
  it("can enrich IPv4 data") {
    import spark.implicits._
    val ip1 = "*********"
    val ip2 = "*******"
    val ip3 = "*******"
    val ip4 = "*******5"
    val ip5 = "*******6"
    val ip6 = "***********"
    val ip7 = "*******"
    val ip8 = "*******"
    val ip9 = "**********"
    val ip10 = "********"
    val ip11 = "ab9:0:0:0:0:0:0:0"
    val ip12 = "abc:0:0:0:0:0:0:0"
    val ip13 = "abc:0:0:0:0:0:0:de"
    val ip14 = "abc:0:0:0:0:0:0:ff"
    val ip15 = "abc:0:0:0:0:0:0:ffff"
    val ip16 = "dee:0:0:0:0:0:0:0"
    val ip17 = "def:0:0:0:0:0:0:0"
    val ip18 = "def:0:0:0:0:0:0:ff"
    val ip19 = "def:0:0:0:0:0:3:ffff"
    val ip20 = "def:0:0:0:0:0:4:ffff"
    val data = Seq(
      sample,
      sample.copy(ip = Some(ip1)),
      sample.copy(ip = Some(ip2)),
      sample.copy(ip = Some(ip3)),
      sample.copy(ip = Some(ip4)),
      sample.copy(ip = Some(ip5)),
      sample.copy(ip = Some(ip6)),
      sample.copy(ip = Some(ip7)),
      sample.copy(ip = Some(ip8)),
      sample.copy(ip = Some(ip9)),
      sample.copy(ip = Some(ip10)),
      sample.copy(ip = Some(ip11)),
      sample.copy(ip = Some(ip12)),
      sample.copy(ip = Some(ip13)),
      sample.copy(ip = Some(ip14)),
      sample.copy(ip = Some(ip15)),
      sample.copy(ip = Some(ip16)),
      sample.copy(ip = Some(ip17)),
      sample.copy(ip = Some(ip18)),
      sample.copy(ip = Some(ip19)),
      sample.copy(ip = Some(ip20))
    ).toDS
    val ctv4 = new RangeMap(Array[Entry[ConnectionType]](
      "*******/20" -> ConnectionType("*******/20", "WIFI"),
      "*******/28" -> ConnectionType("*******/28", "CELL")))
    val ispv4 = new RangeMap(Array[Entry[Isp]](
      "*******/20" -> Isp("*******/20", Some("Verizon"), None, None, None, None, None),
      "*******/28" -> Isp("*******/28", Some("Comcast"), None, None, None, None, None)))
    val ctv6 = new RangeMap(Array[Entry[ConnectionType]](
      "def::/110" -> ConnectionType("def::/110", "CABLE"),
      "abc::/120" -> ConnectionType("abc::/120", "CORPORATE")))
    val ispv6 = new RangeMap(Array[Entry[Isp]](
      "def::/110" -> Isp("def::/110", Some("AT&T"), None, None, None, None, None),
      "abc::/120" -> Isp("abc::/120", Some("Sprint"), None, None, None, None, None)))
    data
      .transform(MaxmindEnrichmentRunner.maxmindEnrichment(ispv4, ispv6, ctv4, ctv6))
      .select(when('ip.isNull, lit("NULL")).otherwise(ipBinaryToStringColumn('ip)).as("ip"), 'carrier, 'connectiontype)
      .sort('ip)
      .as[(String, Option[String], Option[String])]
      .collect shouldBe Array(
      (ip1, None, None),
      (ip2, Some("Comcast"), Some("CELL")),
      (ip3, Some("Comcast"), Some("CELL")),
      (ip4, Some("Comcast"), Some("CELL")),
      (ip5, None, None),
      (ip6, None, None),
      (ip7, Some("Verizon"), Some("WIFI")),
      (ip9, Some("Verizon"), Some("WIFI")),
      (ip10, None, None),
      (ip8, Some("Verizon"), Some("WIFI")),
      ("NULL", None, None),
      (ip11, None, None),
      (ip12, Some("Sprint"), Some("CORPORATE")),
      (ip13, Some("Sprint"), Some("CORPORATE")),
      (ip14, Some("Sprint"), Some("CORPORATE")),
      (ip15, None, None),
      (ip16, None, None),
      (ip17, Some("AT&T"), Some("CABLE")),
      (ip18, Some("AT&T"), Some("CABLE")),
      (ip19, Some("AT&T"), Some("CABLE")),
      (ip20, None, None)
    )
  }
}
