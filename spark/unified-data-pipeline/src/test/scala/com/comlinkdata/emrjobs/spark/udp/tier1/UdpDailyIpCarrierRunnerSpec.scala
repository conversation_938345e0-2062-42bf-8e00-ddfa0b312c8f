package com.comlinkdata.emrjobs.spark.udp.tier1

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.tier0.UdpComboJobDatasourceAdapter
import com.comlinkdata.emrjobs.spark.udp.tier0.UdpComboJobDatasourceAdapter.SingleSourceConfig
import com.comlinkdata.largescale.commons.Utils
import com.comlinkdata.largescale.commons.parsing.JsonUtils
import com.comlinkdata.largescale.schema.udp.tier0.{UdpRaw, Mw02}
import com.comlinkdata.largescale.schema.udp.tier1.UdpDailyIpCarrier
import com.comlinkdata.largescale.udp.ComlinkdataDatasource

import java.net.URI
import java.sql.{Timestamp, Date}
import java.time.{LocalDateTime, LocalDate}
import java.util.UUID
import org.apache.spark.sql.functions.{to_timestamp, lit}
import org.apache.spark.sql.types._
import org.apache.spark.sql.{SparkSession, Dataset}

class UdpDailyIpCarrierRunnerSpec extends CldSparkBaseSpec {
  val singleSourceConfig = SingleSourceConfig(URI create "s3://some-bucket/path/")
  val configMap = JsonUtils.fromJson[Map[String, Any]](JsonUtils.toJson(singleSourceConfig))
  val mw1ksampleURL = getClass.getResource("mw-sample-1k.snappy.parquet")
  val reveal1ksampleURL = getClass.getResource("reveal-sample-1k.snappy.parquet")
  val datasourceReveal = ComlinkdataDatasource.reveal
  val datasourceMobileWalla = ComlinkdataDatasource.mw
  val adapterReveal = UdpComboJobDatasourceAdapter.forDatasource(datasourceReveal, configMap)
  val adapterMobileWalla = UdpComboJobDatasourceAdapter.forDatasource(datasourceMobileWalla, configMap)
  val startDateLocalDate: LocalDate = LocalDate.of(2020,6,6)
  val endDateLocalDate: LocalDate = LocalDate.of(2020,6,6)
  val udpRawAllSeq: Seq[UdpRaw] =
    Seq(
      UdpRaw(
        date = Date.valueOf("2020-06-06"), // java.sql.Date,
        ifa = Array[Byte](192.toByte, 168.toByte, 1, 9), // Ifa,
        ip = Some(Array[Byte](192.toByte, 168.toByte, 1, 9)), // Ip,
        useragent = Some("useragent"), // Option[String],
        carrier = Some("ATT"), // Option[String],
        newcarrier = Some("ATT"), // Option[String],
        latitude = Some(10f), // Float, // from Reveal lat
        longitude = Some(10f), // Float, // from Reveal lon
        locationtype = Some("String"), // Option[String], // from Reveal event_type
        suspiciouslocation = Some(false), // Option[Boolean],
        devicetype = Some("devicetype"), // Option[String],
        make = Some("Galaxy"),
        model = Some("Not-Apple"),
        connectiontype = Some("WIFI"), // Option[String], // from reveal connection_type
        datetime = Timestamp.valueOf("2020-06-06 12:00:01"), // java.sql.Timestamp, // from reveal utc_timestamp
        appid = Some("string"), // Option[String], // from reveal app_id
        appname = None, // Option[String],
        os = Some("os"), // Option[String],
        accuracy = Some(10f), // Option[Float],
        gps_speed = Some(10f), // Option[Float],
        place_name = Some("burgerking"), // Option[String],
        place_id = Some(1), // Option[Int],
        category = Some("string"), // Option[String],
        country_iso3 = Some("string"), // Option[String],
        localdatetime = Some(Timestamp.valueOf("2020-06-06 12:00:01"))),
      UdpRaw(
        date = Date.valueOf("2020-06-06"), // java.sql.Date,
        ifa = Array[Byte](192.toByte, 168.toByte, 1, 9), // Ifa,
        ip = Some(Array[Byte](192.toByte, 168.toByte, 1, 9)), // Ip,
        useragent = Some("useragent"), // Option[String],
        carrier = Some("ATT"), // Option[String],
        newcarrier = Some("ATT"), // Option[String],
        latitude = Some(10f), // Float, // from Reveal lat
        longitude = Some(10f), // Float, // from Reveal lon
        locationtype = Some("String"), // Option[String], // from Reveal event_type
        suspiciouslocation = Some(false), // Option[Boolean],
        devicetype = Some("devicetype"), // Option[String],
        make = Some("Galaxy"),
        model = Some("Not-Apple"),
        connectiontype = Some("WIFI"), // Option[String], // from reveal connection_type
        datetime = Timestamp.valueOf("2020-06-06 12:00:01"), // java.sql.Timestamp, // from reveal utc_timestamp
        appid = Some("string"), // Option[String], // from reveal app_id
        appname = None, // Option[String],
        os = Some("os"), // Option[String],
        accuracy = Some(10f), // Option[Float],
        gps_speed = Some(10f), // Option[Float],
        place_name = Some("burgerking"), // Option[String],
        place_id = Some(1), // Option[Int],
        category = Some("string"), // Option[String],
        country_iso3 = Some("string"), // Option[String],
        localdatetime = Some(Timestamp.valueOf("2020-06-06 12:00:01")))// Option[java.sql.Timestamp]
    )

  val d0 = LocalDate.now()
  val ifa0 = Utils.uuidString2Binary(UUID.randomUUID().toString)
  val ip0 = Utils.ipStringToBinary("**********")
  val dt0 = LocalDateTime.now
  val ldt0 = dt0.minusHours(5)

  val r0 = UdpRaw(
    date = Date.valueOf(d0),
    useragent = Some("useragent"),
    ifa = ifa0,
    carrier = Some("carrier"),
    newcarrier = Some("newcarrier"),
    latitude = Some(0.1f),
    longitude = Some(0.2f),
    locationtype = Some("locationtype"),
    suspiciouslocation = Some(false),
    devicetype = Some("devicetype"),
    make = Some("make"),
    model = Some("model"),
    connectiontype = Some("WIFI"),
    ip = Some(ip0),
    datetime = Timestamp.valueOf(dt0),
    appid = Some("appid"),
    appname = Some("appname"),
    localdatetime = Some(Timestamp.valueOf(ldt0)),
    os = Some("String"),
    accuracy = Some(10F),
    gps_speed = Some(10F),
    place_name = Some("String"),
    place_id = Some(1000000),
    category = Some("String"),
    country_iso3 = Some("String")
  )

  describe("ip sample") {
    val sampleUrl = getClass.getResource("example_mw_ip_day.csv")
    require(sampleUrl != null, "resource not found, cannot execute test.")

    def loadSample(urlStr: String, spark: SparkSession): Dataset[UdpRaw] = {
      import spark.implicits._
      spark
        .read
        .options(Map("inferSchema" -> "true", "delimiter" -> ",", "header" -> "true"))
        .csv(sampleUrl.toString)
        .withColumn("date", $"date".cast(DateType)) // thinks its a datetime
        .withColumn("datetime", to_timestamp($"datetime"))
        .withColumn("latitude", $"latitude".cast(FloatType)) // thinks its a double
        .withColumn("longitude", $"longitude".cast(FloatType)) // thinks its a double
        .withColumn("os", lit("String"))
        .withColumn("accuracy", lit(10F))
        .withColumn("gps_speed", lit(10F))
        .withColumn("place_name", lit("String"))
        .withColumn("place_id", lit(1000000))
        .withColumn("category", lit("String"))
        .withColumn("country_iso3", lit("String"))
        .transform(Mw02.checkAppNameColumn)
        .transform(UdpRaw.adjustMw02RecordsToUdpRaw)
        .as[UdpRaw]
    }

    it("runs to completion") {
      noException should be thrownBy loadSample(sampleUrl.toString, spark).count()
    }

    it("creates a single ip carrier record") {
      val df = loadSample(sampleUrl.toString, spark)
      val records: Vector[UdpRaw] = df.collect.toVector
      records.foreach(println)
      val result = UdpDailyIpCarrierRunner.uniqueIpCarriers(ComlinkdataDatasource.mw, df.head.ip.get, records)
      result should have length 1
    }

    it("it correctly counts observations") {
      val df = loadSample(sampleUrl.toString, spark)
      val records = df.collect.toVector
      val result = UdpDailyIpCarrierRunner.uniqueIpCarriers(ComlinkdataDatasource.mw, df.head.ip.get, records)
      result.head.observation_count shouldBe records.size
    }
  }

  it("can create a unified daily ip carrier set from udp raw all with 2 manual records") {
    import spark.implicits._
    
    val df_1: Dataset[UdpRaw] = udpRawAllSeq.toDS
    val result = df_1.transform(UdpDailyIpCarrierRunner.createDailyIpCarrierDataset(ComlinkdataDatasource.reveal, _))
    result.head().carrier shouldEqual Some("ATT")
  }

  it("can create a unified daily ip carrier set from mw with 1k sample file") {
    val df = spark.read.parquet(mw1ksampleURL.toString)
      .withColumn("date", lit(Date.valueOf("2020-06-06"))).toDF
    val df_1 = df.transform(adapterMobileWalla.rawToUdpRawDataset(startDateLocalDate, endDateLocalDate, _))
    val result = df_1.transform(UdpDailyIpCarrierRunner.createDailyIpCarrierDataset(datasourceReveal, _))

    result.count() shouldBe 15
  }

  describe("uniqueIpCarriers") {
    val d1 = d0.plusDays(1)
    val dt1 = dt0.plusDays(1)
    val ldt1 = ldt0.plusDays(1)

    val r1: UdpRaw = r0.copy(
      date = Date.valueOf(d1),
      datetime = Timestamp.valueOf(dt1),
      localdatetime = Some(Timestamp.valueOf(ldt1))
    )
    describe("combine 2 similar records with different timestamps") {

      val result: Seq[UdpDailyIpCarrier] =
        UdpDailyIpCarrierRunner.uniqueIpCarriers(ComlinkdataDatasource.mw, ip0, Vector(r0, r1))

      it("combines 2 like records") {
        result should have size 1
      }

      it("should have observation count of 2") {
        result.head.observation_count shouldBe 2
      }
    }
  }
}
