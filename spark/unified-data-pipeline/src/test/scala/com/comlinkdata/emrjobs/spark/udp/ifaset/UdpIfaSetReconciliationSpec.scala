package com.comlinkdata.emrjobs.spark.udp.ifaset

import com.comlinkdata.commons.testing.CldBaseSpec
import com.comlinkdata.emrjobs.spark.udp.ifaset.FrequencyUtils.FrequencyMap
import com.comlinkdata.largescale.schema.udp.tier2
import com.comlinkdata.largescale.schema.udp.tier2.{IfaSet, DayStat}

import java.sql.Date
import java.time.LocalDate
import java.util.UUID.randomUUID
import scala.language.implicitConversions

class UdpIfaSetReconciliationSpec extends CldBaseSpec {

  lazy val mccMncMappingSample: Map[String, String] = Map(
    "310880" -> "Advantage Cellular",
    "310-880" -> "Advantage Cellular",
    "310850" -> "Aeris Comm. Inc.",
    "310-850" -> "Aeris Comm. Inc.",
    "310510" -> "Airtel Wireless LLC",
    "310-510" -> "Airtel Wireless LLC"
  )
  lazy val testIfaSetReconciliation: UdpIfaSetReconciliation = new UdpIfaSetReconciliation(mccMncMappingSample)
  lazy val testIfaSet: IfaSet = {
    import com.comlinkdata.largescale.commons.RichDate._

    val d = LocalDate.parse("2020-01-02")
    val pd = Date.valueOf(d)
    tier2.IfaSet(
      ds = "mw",
      year = d.getYearString,
      month = d.getMonthValueString,
      day = d.getDayOfMonthString,
      //partition_date = pd,
      ifa = getIfa,
      days = Vector(pd),
      apps = Map.empty[String, DayStat],
      //day_stats = Vector(dayStat),
      //distinct_days = 1, //: Int,
      //distinct_apps = dayStat.distinct_apps,
//      distinct_hour_days_utc = -1,
//      days_count_by_hour_ltz = Vector.empty,
//      days_count_by_hour_utc = Vector.empty,
//      days_count_by_weekday_ltz = Vector.empty,
//      days_count_by_weekday_utc = Vector.empty,
      //min_date = pd,
      //max_date = pd,
      device_type = Some("SMART PHONE"),
      latest_carrier_name = Some("Verizon Wireless"),
      final_carrier_name = None,
      iphone_disambiguated_model = None,
      modal_model_code = None,
      carrier_freqs = Map.empty[String, DayStat],
      mcc_mnc_freqs = Map.empty[String, DayStat],
      model_code_freqs = Map.empty[String, DayStat],
      user_agent_string_freqs = Map.empty[String, DayStat] //,
//      earliest_location = Map.empty,
//      latest_location = Map.empty
    )
  }

  def getIfa: Array[Byte] = randomUUID.toString.getBytes

//  describe("test isMaxDateWithinResolutionWindow") {
//    describe("check if the device's max activity date is within the 21-day resolution window") {
//      it("should return true because the device's max activity date equal to its min activity date") {
//        val bool = testIfaSetReconciliation.isMaxDateWithinResolutionWindow(testIfaSet)
//        bool shouldBe true
//      }
//      it("should return true because the device's max activity date is exactly 21 days after its min activity date") {
//        val newMaxDate = testIfaSet.min_date.toLocalDate.plusDays(21)
//        val alteredTestIfaSet = testIfaSet.copy(max_date = Date.valueOf(newMaxDate))
//        val bool = testIfaSetReconciliation.isMaxDateWithinResolutionWindow(alteredTestIfaSet)
//        bool shouldBe true
//      }
//      it("should return false because the device's max activity date is outside the 21-day resolution window") {
//        val newMaxDate = testIfaSet.min_date.toLocalDate.plusDays(22)
//        val alteredTestIfaSet = testIfaSet.copy(max_date = Date.valueOf(newMaxDate))
//        val bool = testIfaSetReconciliation.isMaxDateWithinResolutionWindow(alteredTestIfaSet)
//        bool shouldBe false
//      }
//      it("should throw an error because the device's max activity date is before its min activity date") {
//        val newMaxDate = testIfaSet.min_date.toLocalDate.minusDays(1)
//        val alteredTestIfaSet = testIfaSet.copy(max_date = Date.valueOf(newMaxDate))
//        assertThrows[java.lang.IllegalArgumentException](testIfaSetReconciliation.isMaxDateWithinResolutionWindow(alteredTestIfaSet))
//      }
//    }
//  }

  import com.comlinkdata.largescale.commons.RichDate._

  private val start = LocalDate.now.toDate
  private val end = LocalDate.now.toDate

  implicit def toDayStat(i: Int): DayStat = tier2.DayStat(start, end, i)

  describe("test findMostFrequentModelCode") {
    it("should return disambiguated iphone model with highest frequency") {
      val modelFrequencyMap: FrequencyMap[String] = Map(
        "iPhone XS" -> tier2.DayStat(start, end, 3),
        "iPhone 9" -> 56,
        "iPhone" -> 89
      )
      val modalModelCode = testIfaSetReconciliation.findMostFrequentModelCode(modelFrequencyMap)
      modalModelCode shouldBe Some("iPhone 9")
    }
    it("should return generic 'ambiguated' iphone model") {
      val modelFrequencyMap: FrequencyMap[String] = Map(
        "iPhone" -> 23,
        "" -> 87
      )
      val modalModelCode = testIfaSetReconciliation.findMostFrequentModelCode(modelFrequencyMap)
      modalModelCode shouldBe Some("iPhone")
    }
    it("should return model with highest frequency") {
      val modelFrequencyMap: FrequencyMap[String] = Map(
        "smartphoneX" -> 4,
        "smartphoneY" -> 87,
        "smartphoneZ" -> 123
      )
      val modalModelCode = testIfaSetReconciliation.findMostFrequentModelCode(modelFrequencyMap)
      modalModelCode shouldBe Some("smartphoneZ")
    }
    it("should return non-blank model with highest frequency") {
      val modelFrequencyMap: FrequencyMap[String] = Map(
        "" -> 45,
        "nonBlankA" -> 42,
        "nonBlankB" -> 41
      )
      val modalModelCode = testIfaSetReconciliation.findMostFrequentModelCode(modelFrequencyMap)
      modalModelCode shouldBe Some("nonBlankA")
    }
    it("should return none because there is non-blank model") {
      val modelFrequencyMap: FrequencyMap[String] = Map(
        "" -> 45
      )
      val modalModelCode = testIfaSetReconciliation.findMostFrequentModelCode(modelFrequencyMap)
      modalModelCode shouldBe None
    }
  }

  describe("test findMostFrequentNonBlankString") {
    it("should return string with highest frequency") {
      val carrierFrequencyMap: FrequencyMap[String] = Map(
        "abc" -> 33,
        "def" -> 22,
        "ghi" -> 11
      )
      val mostFrequentCarrier = testIfaSetReconciliation.findMostFrequentNonBlankString(carrierFrequencyMap)
      mostFrequentCarrier shouldBe Some("abc")
    }
    it("should return string with highest frequency without taking the blank string into account") {
      val carrierFrequencyMap: FrequencyMap[String] = Map(
        "abc" -> 33,
        "" -> 667,
        "ghi" -> 44
      )
      val mostFrequentCarrier = testIfaSetReconciliation.findMostFrequentNonBlankString(carrierFrequencyMap)
      mostFrequentCarrier shouldBe Some("ghi")
    }
    it("should return none because there is no non-blank string") {
      val carrierFrequencyMap: FrequencyMap[String] = Map(
        "" -> 21
      )
      val mostFrequentCarrier = testIfaSetReconciliation.findMostFrequentNonBlankString(carrierFrequencyMap)
      mostFrequentCarrier shouldBe None
    }
  }

  describe("test reconcileFinalCarrierName") {
    it("should choose carrier name from mcc-mnc code because none of the raw carrier names are of interest") {
      val carrierFrequencyMap: FrequencyMap[String] = Map(
        "abc" -> 33,
        "def" -> 22,
        "ghi" -> 11
      )
      val mccMncFrequencyMap: FrequencyMap[String] = Map(
        "310-850" -> 55,
        "310850" -> 44
      )
      val finalCarrierName = testIfaSetReconciliation.reconcileFinalCarrierName(carrierFrequencyMap, mccMncFrequencyMap)
      finalCarrierName shouldBe Some("Aeris Comm. Inc.")
    }
    it("should return none because carrier from the highest mcc-mnc code does not match at least one carrier of interest") {
      val carrierFrequencyMap: FrequencyMap[String] = Map(
        "Wireless Data Service Provider Corporation" -> 33, // US Cellular
        "def" -> 22,
        "ghi" -> 11
      )
      val mccMncFrequencyMap: FrequencyMap[String] = Map(
        "310-850" -> 55,
        "310850" -> 44
      )
      val finalCarrierName = testIfaSetReconciliation.reconcileFinalCarrierName(carrierFrequencyMap, mccMncFrequencyMap)
      finalCarrierName shouldBe None
    }
    it("should return carrier name with highest frequency") {
      val carrierFrequencyMap: FrequencyMap[String] = Map(
        "Aeris Comm. Inc." -> 24,
        "foo" -> 2,
        "foo2" -> 5
      )
      val mccMncFrequencyMap: FrequencyMap[String] = Map(
        "310-850" -> 18,
        "310850" -> 4,
        "bar" -> 5
      )
      val finalCarrierName = testIfaSetReconciliation.reconcileFinalCarrierName(carrierFrequencyMap, mccMncFrequencyMap)
      finalCarrierName shouldBe Some("Aeris Comm. Inc.")
    }
    it("should prefer resolvable values in case of equal distinct_days") {
      val ifaSet = tier2.IfaSet(
        ds = null,
        year = null,
        month = null,
        day = null,
        ifa = null,
        days = IndexedSeq(Date.valueOf("2020-08-30"), Date.valueOf("2020-09-04")),
        apps = Map.empty[String, DayStat],
        device_type = None,
        final_carrier_name = None,
        latest_carrier_name = None,
        iphone_disambiguated_model = None,
        modal_model_code = None,
        carrier_freqs = Map(
          "Spectrum" -> tier2.DayStat(first = Date.valueOf("2020-08-30"), last = Date.valueOf("2020-08-30"), distinct_days = 1),
          "Verizon Wireless" -> tier2.DayStat(first = Date.valueOf("2020-09-04"), last = Date.valueOf("2020-09-04"), distinct_days = 1)
        ),
        mcc_mnc_freqs = Map(
          "70092" -> tier2.DayStat(first = Date.valueOf("2020-09-04"), last = Date.valueOf("2020-09-04"), distinct_days = 1)
        ),
        model_code_freqs = Map.empty[String, DayStat],
        user_agent_string_freqs = Map.empty[String, DayStat]
      )
      testIfaSetReconciliation.applyReconciliation(ifaSet).final_carrier_name shouldBe Some("Verizon Wireless")
    }
  }

//  describe("test applyReconciliation") {
//    it("should do nothing because we're already beyond the 21-day resolution window") {
//      val newMaxDate = testIfaSet.min_date.toLocalDate.plusDays(45)
//      val alteredTestIfaSet = testIfaSet.copy(max_date = Date.valueOf(newMaxDate))
//      val ifaSet = testIfaSetReconciliation.applyReconciliation(alteredTestIfaSet)
//      ifaSet shouldBe alteredTestIfaSet
//    }
//    it("should reconcile final carrier name and modal model code") {
//      val newMaxDate = testIfaSet.min_date.toLocalDate.plusDays(6)
//      val withMoreFrequencies = testIfaSet.day_stats.head.copy(
//        model_code_freqs = Map("iPhone" -> 23, "" -> 87),
//        carrier_freqs = Map("Sprint PCS" -> 8, "def" -> 7)
//      )
//      val alteredTestIfaSet = testIfaSet.copy(
//        max_date = Date.valueOf(newMaxDate),
//        day_stats = testIfaSet.day_stats :+ withMoreFrequencies
//      )
//      val ifaSet = testIfaSetReconciliation.applyReconciliation(alteredTestIfaSet)
//      ifaSet.final_carrier_name shouldBe Some("Sprint Wireless")
//      ifaSet.modal_model_code shouldBe Some("iPhone")
//    }
//    it("should find modal model code and not pick carrier with highest frequency since it is not of interest") {
//      val newMaxDate = testIfaSet.min_date.toLocalDate.plusDays(6)
//      val withMoreFrequencies = testIfaSet.day_stats.head.copy(
//        model_code_freqs = Map("iPhone 10" -> 23, "iPhone" -> 87),
//        carrier_freqs = Map("Sprint PCS" -> 6, "def" -> 7)
//      )
//      val alteredTestIfaSet = testIfaSet.copy(
//        max_date = Date.valueOf(newMaxDate),
//        day_stats = testIfaSet.day_stats :+ withMoreFrequencies
//      )
//      val ifaSet = testIfaSetReconciliation.applyReconciliation(alteredTestIfaSet)
//      ifaSet.final_carrier_name shouldBe None
//      ifaSet.modal_model_code shouldBe Some("iPhone 10")
//    }
//    it("should keep disambiguated iphone model instead of model found from model code frequencies") {
//      val newMaxDate = testIfaSet.min_date.toLocalDate.plusDays(10)
//      val withMoreFrequencies = testIfaSet.day_stats.head.copy(
//        model_code_freqs = Map("iPhone 10" -> 23, "iPhone" -> 87),
//        carrier_freqs = Map("Sprint PCS" -> 6, "def" -> 7)
//      )
//      val alteredTestIfaSet = testIfaSet.copy(
//        max_date = Date.valueOf(newMaxDate),
//        day_stats = testIfaSet.day_stats :+ withMoreFrequencies,
//        iphone_disambiguated_model = Some("iPhone X")
//      )
//      val ifaSet = testIfaSetReconciliation.applyReconciliation(alteredTestIfaSet)
//      ifaSet.final_carrier_name shouldBe None
//      ifaSet.modal_model_code shouldBe Some("iPhone X")
//    }
//  }
}
