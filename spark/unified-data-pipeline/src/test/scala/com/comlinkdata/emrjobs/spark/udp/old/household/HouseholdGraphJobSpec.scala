package com.comlinkdata.emrjobs.spark.udp.old.household

import java.time.LocalDate

import com.comlinkdata.commons.testing.CldSparkBaseSpec

class HouseholdGraphJobSpec extends CldSparkBaseSpec {

  describe("integration data") {

    val baseDir = "unified-data-pipeline/data"

    def makeDir(p: String) = s"$baseDir/$p"

    val config = HouseholdGraphConfig(
      mwSource = makeDir("mw02"),
      tapadSource = makeDir("tapad"),
      mwIfaWifiIpAggSource = makeDir("mw-wifi-ip-agg"),
      ifaMasterTableSource = makeDir("ifa-master-table"),
      tapadGraphDate = LocalDate.of(2019, 11, 18),
      mwLookbackDays = 0,
      debugFolderPrefixOpt = Some(makeDir("debug-model-inputs")),
      rawGraphDest = makeDir("householdGraphRunnerOutput")
    )

    ignore("should run without errors") {
      implicit val sparkImplicit = spark
      val runner = HouseholdGraphRunner
      noException should be thrownBy runner.runJob(config)

    }
  }

}
