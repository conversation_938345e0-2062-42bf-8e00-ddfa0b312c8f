package com.comlinkdata.emrjobs.spark.udp.locationset

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.commons.UriDFTableRW
import com.comlinkdata.largescale.udp.ComlinkdataDatasource._
import com.typesafe.scalalogging.LazyLogging

import java.net.URI
import java.time.LocalDate

class UdpLocationSetJobSpec extends CldSparkBaseSpec with LazyLogging {
  private val ifaObsTestData = this.getClass().getResource("/com/comlinkdata/emrjobs/spark/udp/locationset/")
  private val dailyMaxMindTestData = this.getClass().getResource("/com/comlinkdata/emrjobs/spark/udp/locationset_maxmind/")
  private val outPath: URI = new URI(f"table://LocationSetOut")

  describe("Test With Saved Data") {
    it("Test Ifa Obs Single Day") {

      val config = UdpLocationSetConfig(
        datasources = List(mw05),
        ifaObsPath = ifaObsTestData.toURI,
        maxmindPath = dailyMaxMindTestData.toURI,
        outputPath = outPath,
        startDate = LocalDate.parse("2021-11-20"),
        maxNumDaysToRun = None
      )

      UdpLocationSetRunner.runJob(config)
      val uriTableRW = UriDFTableRW.fromStr(outPath.toString)

      val outputedDF = uriTableRW.readBasePath().cache
      outputedDF.count() shouldBe 366

      uriTableRW.dropAll()
    }
  }
}
