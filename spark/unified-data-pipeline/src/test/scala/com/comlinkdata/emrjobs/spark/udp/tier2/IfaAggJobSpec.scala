package com.comlinkdata.emrjobs.spark.udp.tier2

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.schema.udp.tier2

import java.sql.Date
import scala.language.implicitConversions

class IfaAggJobSpec extends CldSparkBaseSpec {

  import spark.implicits._

  implicit def stringToDate(arg: String): Date = Date.valueOf(arg)

  describe("Lookup") {
    it("can convert data frames into lookup maps") {
      val source = Seq(
        ("mcc_mnc1", "carrier1", "mvno1"),
        ("mcc_mnc2", "carrier2", "mvno2")
      ).toDF("mcc_mnc", "carrier", "mvno")
      IfaAggregateRunner.readLookup(source, 'mcc_mnc, 'mvno) shouldBe Map(
        "mcc_mnc1" -> "mvno1",
        "mcc_mnc2" -> "mvno2"
      )
      IfaAggregateRunner.readLookup(source, 'mcc_mnc, 'carrier) shouldBe Map(
        "mcc_mnc1" -> "carrier1",
        "mcc_mnc2" -> "carrier2"
      )
    }
  }
  describe("Stats Merging") {
    it("merges multiple observations for the same key") {
      IfaAggregateRunner.mergeStats(
        left = Map(
          "mcc_mnc1" -> tier2.DayStat("2021-05-09", "2021-06-30", 1),
          "mcc_mnc2" -> tier2.DayStat("2021-05-09", "2021-06-30", 1)),
        right = Map(
          "mcc_mnc1" -> tier2.DayStat("2021-06-15", "2021-07-30", 1),
          "mcc_mnc3" -> tier2.DayStat("2021-06-15", "2021-07-30", 1))
      ) shouldBe Map(
        "mcc_mnc1" -> tier2.DayStat("2021-05-09", "2021-07-30", 2),
        "mcc_mnc2" -> tier2.DayStat("2021-05-09", "2021-06-30", 1),
        "mcc_mnc3" -> tier2.DayStat("2021-06-15", "2021-07-30", 1))
    }
  }
  describe("First/Last Calculation") {
    it("prefers stats with greater distinct_days") {
      IfaAggregateRunner.getFirstLast(
        stats = Map(
          "mcc_mnc1" -> tier2.DayStat("2021-05-01", "2021-06-01", 1),
          "mcc_mnc2" -> tier2.DayStat("2021-05-09", "2021-06-01", 1),
          "mcc_mnc3" -> tier2.DayStat("2021-05-09", "2021-06-01", 2),
          "mcc_mnc4" -> tier2.DayStat("2021-05-19", "2021-06-07", 2),
          "mcc_mnc5" -> tier2.DayStat("2021-05-19", "2021-06-07", 1),
          "mcc_mnc6" -> tier2.DayStat("2021-05-19", "2021-06-30", 1)),
        lookup = Map(
          "mcc_mnc2" -> "mvno2",
          "mcc_mnc3" -> "mvno3",
          "mcc_mnc4" -> "mvno4",
          "mcc_mnc5" -> "mvno5"
        ),
        window = 28
      ) shouldBe(Some("mvno3"), Some("mvno4"))
    }
    it("only returns resolved values") {
      IfaAggregateRunner.getFirstLast(
        stats = Map(
          "mcc_mnc1" -> tier2.DayStat("2021-05-01", "2021-06-01", 1),
          "mcc_mnc2" -> tier2.DayStat("2021-05-19", "2021-06-30", 1)),
        lookup = Map(),
        window = 28
      ) shouldBe(None, None)
      IfaAggregateRunner.getFirstLast(
        stats = Map(
          "mcc_mnc1" -> tier2.DayStat("2021-05-01", "2021-06-01", 10),
          "mcc_mnc2" -> tier2.DayStat("2021-05-09", "2021-06-05", 1),
          "mcc_mnc3" -> tier2.DayStat("2021-05-19", "2021-06-30", 10)),
        lookup = Map("mcc_mnc2" -> "mvno2"),
        window = 28
      ) shouldBe(Some("mvno2"), Some("mvno2"))
    }
  }
  describe("OS calculation") {
    it("picks the highest version, including minor/patch, that's observed on the latest date") {
      IfaAggregateRunner.getLastOs(
        stats = Map(
          "iOS 14" -> tier2.DayStat("2021-05-01", "2021-06-01", 0),
          "iOS 14.1" -> tier2.DayStat("2021-05-01", "2021-06-01", 0),
          "iOS 14.1.1" -> tier2.DayStat("2021-05-01", "2021-06-01", 0),
          "iOS 14.1.2" -> tier2.DayStat("2021-05-01", "2021-06-01", 0),
          "iOS 14.2" -> tier2.DayStat("2021-05-01", "2021-06-01", 0),
          "iOS 14.2.1" -> tier2.DayStat("2021-05-01", "2021-06-01", 0)
        ),
        window = 28) shouldBe Some("iOS 14.2.1")
    }
  }
}
