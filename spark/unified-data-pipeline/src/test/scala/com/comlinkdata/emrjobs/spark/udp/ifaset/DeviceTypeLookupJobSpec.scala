package com.comlinkdata.emrjobs.spark.udp.ifaset

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.common.generate
import com.comlinkdata.commons.testing.generators.udp.genIfaSet
import com.comlinkdata.emrjobs.spark.udp.ifaset.UdpIfaSetSpec.d
import com.comlinkdata.largescale.schema.udp.lookup.DeviceTypeLookup

import java.sql.Date

class DeviceTypeLookupJobSpec extends CldSparkBaseSpec {

  import spark.implicits._

  describe("new device type lookup from empty initial lookup") {
    lazy val days = Vector(Date.valueOf("2023-02-28"), Date.valueOf("2023-02-18"))
    lazy val noLookup = spark.emptyDataset[DeviceTypeLookup]

    it("algorithm works") {
      val ifaSet = Seq(
        generate(3, genIfaSet()).map(_.copy(device_type = Some("A3"), days = days)), // winning device type for all
        generate(2, genIfaSet()).map(_.copy(device_type = Some("B2"), days = days)),
        generate(1, genIfaSet()).map(_.copy(device_type = Some("C1"), days = days))
      ).flatten
        .map(_.copy(modal_model_code = Some("M")))
        .toDS()

      val result = DeviceTypeLookupJobRunner.stabilizeMissingDeviceTypes(noLookup, ifaSet)
      val expected = dataset[DeviceTypeLookup] {
        """
          |modal_model_code|device_type|
          |M               |A3         |"""
      }

      assertDatasetEquals(expected, result)
    }

    it("null device types majority") {
      val ifaSet = Seq(
        generate(3, genIfaSet()).map(_.copy(device_type = None, days = days)),
        generate(1, genIfaSet()).map(_.copy(device_type = Some("D1"), days = days))
      ).flatten
        .map(_.copy(modal_model_code = Some("M")))
        .toDS()

      val result = DeviceTypeLookupJobRunner.stabilizeMissingDeviceTypes(noLookup, ifaSet)
      val expected = dataset[DeviceTypeLookup] {
        """
          |modal_model_code|device_type|
          |M               |D1         |"""
      }

      assertDatasetEquals(expected, result)
    }

    it("device type for models younger than 7 days is not touched") {
      val date = d("2023-03-28")
      val ifaSet = Seq(
        generate(3, genIfaSet()).map(_.copy(device_type = Some("D3"), days = Vector(date))),
        generate(1, genIfaSet()).map(_.copy(device_type = Some("D1"), days = Vector(date)))
      ).flatten
        .map(_.copy(modal_model_code = Some("M")))
        .toDS()

      val result = DeviceTypeLookupJobRunner.stabilizeMissingDeviceTypes(noLookup, ifaSet)
      assert(result.isEmpty)
    }

    it("prefers non-blanks over blanks on count ties") {
      val ifaSet = Seq(
        generate(2, genIfaSet()).map(_.copy(device_type = Some("D3"), days = days)), // winning device type for all
        generate(2, genIfaSet()).map(_.copy(device_type = Some(""), days = days)),
        generate(1, genIfaSet()).map(_.copy(device_type = Some("D1"), days = days))
      ).flatten
        .map(_.copy(modal_model_code = Some("M")))
        .toDS()

      val result = DeviceTypeLookupJobRunner.stabilizeMissingDeviceTypes(noLookup, ifaSet)
      val expected = dataset[DeviceTypeLookup] {
        """
          |modal_model_code|device_type|
          |M               |D3         |"""
      }

      assertDatasetEquals(expected, result)
    }

    it("prefers non-blanks over blanks even if blanks prevail") {
      val ifaSet = Seq(
        generate(2, genIfaSet()).map(_.copy(device_type = Some("A3"), days = days)), // winning device type for all
        generate(3, genIfaSet()).map(_.copy(device_type = Some(""), days = days)),
        generate(1, genIfaSet()).map(_.copy(device_type = Some("B1"), days = days))
      ).flatten
        .map(_.copy(modal_model_code = Some("M")))
        .toDS()

      val result = DeviceTypeLookupJobRunner.stabilizeMissingDeviceTypes(noLookup, ifaSet)
      val expected = dataset[DeviceTypeLookup] {
        """
          |modal_model_code|device_type|
          |M               |A3         |"""
      }

      assertDatasetEquals(expected, result)
    }

    it("picks just one if all are blanks") {
      val ifaSet = Seq(
        generate(2, genIfaSet()).map(_.copy(device_type = Some(""), days = days)),
        generate(3, genIfaSet()).map(_.copy(device_type = Some(""), days = days))
      ).flatten
        .map(_.copy(modal_model_code = Some("M")))
        .toDS()

      val result = DeviceTypeLookupJobRunner.stabilizeMissingDeviceTypes(noLookup, ifaSet)
      val expected = dataset[DeviceTypeLookup] {
        """
          |modal_model_code|device_type|
          |M               |null       |"""
      }

      assertDatasetEquals(expected, result)
    }
  }

  describe("non-empty initial lookup") {
    lazy val days = Vector(Date.valueOf("2023-02-28"), Date.valueOf("2023-02-18"))
    lazy val lookup = dataset[DeviceTypeLookup] {
      """
        |modal_model_code|device_type|
        |M               |TABLET     |"""
    }

    it("keeps old lookup value even if device type is different now") {
      val ifaSet = Seq(
        generate(3, genIfaSet()).map(_.copy(device_type = Some("A3"), days = days)), // winning device type for all
        generate(2, genIfaSet()).map(_.copy(device_type = Some("B2"), days = days)),
        generate(1, genIfaSet()).map(_.copy(device_type = Some("C1"), days = days))
      ).flatten
        .map(_.copy(modal_model_code = Some("M")))
        .toDS()

      val result = DeviceTypeLookupJobRunner.stabilizeMissingDeviceTypes(lookup, ifaSet)
      assert(result.isEmpty)
    }

    it("ignores irrelevant values of the old lookup") {
      val ifaSet = Seq(
        generate(3, genIfaSet()).map(_.copy(device_type = Some("A3"), days = days)), // winning device type for all
        generate(2, genIfaSet()).map(_.copy(device_type = Some("B2"), days = days)),
        generate(1, genIfaSet()).map(_.copy(device_type = Some("C1"), days = days))
      ).flatten
        .map(_.copy(modal_model_code = Some("M1")))
        .toDS()

      val result = DeviceTypeLookupJobRunner.stabilizeMissingDeviceTypes(lookup, ifaSet)
      val expected = dataset[DeviceTypeLookup] {
        """
          |modal_model_code|device_type|
          |M1              |A3         |"""
      }

      assertDatasetEquals(expected, result)
    }
  }
}
