package com.comlinkdata.emrjobs.spark.udp.tier1

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.tier0.UdpComboJobDatasourceAdapter
import com.comlinkdata.emrjobs.spark.udp.tier0.UdpComboJobDatasourceAdapter.SingleSourceConfig
import com.comlinkdata.largescale.commons.parsing.JsonUtils
import com.comlinkdata.largescale.schema.udp.tier0.UdpRaw
import com.comlinkdata.largescale.udp.ComlinkdataDatasource

import java.net.URI
import java.sql.{Timestamp, Date}
import java.time.LocalDate
import org.apache.spark.sql.functions.lit

class UdpSegmentReportRunnerSpec extends CldSparkBaseSpec {
  val singleSourceConfig = SingleSourceConfig(URI create "s3://some-bucket/path/")
  val configMap = JsonUtils.fromJson[Map[String, Any]](JsonUtils.toJson(singleSourceConfig))
  val mw1ksampleURL = getClass.getResource("mw-sample-1k.snappy.parquet")
  val reveal1ksampleURL = getClass.getResource("reveal-sample-1k.snappy.parquet")
  val startDateLocalDate: LocalDate = LocalDate.of(2020, 6, 6)
  val endDateLocalDate: LocalDate = LocalDate.of(2020, 6, 6)
  val startDate: Date = Date.valueOf(startDateLocalDate)
  val endDate: Date = startDate
  val datasourceReveal = ComlinkdataDatasource.reveal
  val datasourceMobileWalla = ComlinkdataDatasource.mw
  val adapterReveal = UdpComboJobDatasourceAdapter.forDatasource(datasourceReveal, configMap)
  val adapterMobileWalla = UdpComboJobDatasourceAdapter.forDatasource(datasourceMobileWalla, configMap)

  describe("log metrics") {
    val udpDs: Seq[UdpRaw] =
      Seq(
        UdpRaw(date = Date.valueOf("2020-01-01"),
          useragent = Some("String"),
          ifa = Array[Byte](192.toByte, 168.toByte, 1, 9),
          carrier = Some("String"),
          newcarrier = Some(""),
          latitude = Some(10F),
          longitude = Some(11F),
          locationtype = None,
          suspiciouslocation = None,
          devicetype = Some(""),
          make = Some("Iphone"),
          model = Some("Apple"),
          connectiontype = Some(""),
          ip = Option(Array[Byte](192.toByte, 168.toByte, 1, 9)),
          datetime = Timestamp.valueOf("2020-01-01 12:00:01"),
          appid = Some(""),
          appname = Some(""),
          localdatetime = Some(Timestamp.valueOf("2020-01-01 12:00:01")),
          os = None,
          accuracy = None,
          gps_speed = None,
          place_id = None,
          place_name = None,
          category = None,
          country_iso3 = None),
        UdpRaw(date = Date.valueOf("2020-01-01"),
          useragent = Some("String"),
          ifa = Array[Byte](192.toByte, 168.toByte, 1, 8),
          carrier = Some("String"),
          newcarrier = Some(""),
          latitude = Some(10F),
          longitude = Some(11F),
          locationtype = None,
          suspiciouslocation = None,
          devicetype = Some(""),
          make = Some("Iphone"),
          model = Some("Apple"),
          connectiontype = Some(""),
          ip = Option(Array[Byte](192.toByte, 168.toByte, 1, 9)),
          datetime = Timestamp.valueOf("2020-01-01 12:00:01"),
          appid = Some(""),
          appname = Some(""),
          localdatetime = Some(Timestamp.valueOf("2020-01-01 12:00:01")),
          os = None,
          accuracy = None,
          gps_speed = None,
          place_id = None,
          place_name = None,
          category = None,
          country_iso3 = None),
        UdpRaw(date = Date.valueOf("2020-01-02"),
          useragent = Some("String"),
          ifa = Array[Byte](192.toByte, 168.toByte, 1, 8),
          carrier = Some("String"),
          newcarrier = Some(""),
          latitude = Some(10F),
          longitude = Some(11F),
          locationtype = None,
          suspiciouslocation = None,
          devicetype = Some(""),
          make = None,
          model = Some("Apple"),
          connectiontype = Some(""),
          ip = Option(Array[Byte](192.toByte, 168.toByte, 1, 9)),
          datetime = Timestamp.valueOf("2020-01-01 12:00:01"),
          appid = Some(""),
          appname = Some(""),
          localdatetime = Some(Timestamp.valueOf("2020-01-01 12:00:01")),
          os = None,
          accuracy = None,
          gps_speed = None,
          place_id = None,
          place_name = None,
          category = None,
          country_iso3 = None),
        UdpRaw(date = Date.valueOf("2020-01-01"),
          useragent = Some("String"),
          ifa = Array[Byte](192.toByte, 168.toByte, 1, 9),
          carrier = Some("String"),
          newcarrier = Some(""),
          latitude = Some(10F),
          longitude = Some(11F),
          locationtype = None,
          suspiciouslocation = None,
          devicetype = Some(""),
          make = Some("Galaxy"),
          model = Some("Not-Apple"),
          connectiontype = Some(""),
          ip = Option(Array[Byte](192.toByte, 168.toByte, 1, 9)),
          datetime = Timestamp.valueOf("2020-01-01 12:00:01"),
          appid = Some(""),
          appname = Some(""),
          localdatetime = Some(Timestamp.valueOf("2020-01-01 12:00:01")),
          os = None,
          accuracy = None,
          gps_speed = None,
          place_id = None,
          place_name = None,
          category = None,
          country_iso3 = None),
        UdpRaw(date = Date.valueOf("2020-01-01"),
          useragent = Some("String"),
          ifa = Array[Byte](192.toByte, 168.toByte, 1, 9),
          carrier = Some("String"),
          newcarrier = Some(""),
          latitude = Some(10F),
          longitude = Some(11F),
          locationtype = None,
          suspiciouslocation = None,
          devicetype = Some(""),
          make = Some("Iphone"),
          model = None,
          connectiontype = Some(""),
          ip = Option(Array[Byte](192.toByte, 168.toByte, 1, 9)),
          datetime = Timestamp.valueOf("2020-01-01 12:00:01"),
          appid = Some(""),
          appname = Some(""),
          localdatetime = Some(Timestamp.valueOf("2020-01-01 12:00:01")),
          os = None,
          accuracy = None,
          gps_speed = None,
          place_id = None,
          place_name = None,
          category = None,
          country_iso3 = None)
      )

    it("correctly meters 'model' field") {
      import spark.implicits._

      val ds_expected = expect_ds(
        """
          +--------------+-------------------+-----------------+-------+-------------+------------+---+----+-----+---+
          |partition_date|segment            |metric           |records|distinct_ifas|distinct_ips|ds |year|month|day|
          +--------------+-------------------+-----------------+-------+-------------+------------+---+----+-----+---+
          |2020-01-01    |appname||appid     |||               |4      |2            |1           |mw |2020|01   |01 |
          |2020-01-01    |carrier||newcarrier|String||         |4      |2            |1           |mw |2020|01   |01 |
          |2020-01-01    |carrier||newcarrier|||               |4      |2            |1           |mw |2020|01   |01 |
          |2020-01-01    |connectiontype     |null             |4      |2            |null        |mw |2020|01   |01 |
          |2020-01-01    |connectiontype     |                 |4      |2            |null        |mw |2020|01   |01 |
          |2020-01-01    |locationtype       |null             |4      |2            |null        |mw |2020|01   |01 |
          |2020-01-01    |make||model        |Galaxy||         |1      |1            |1           |mw |2020|01   |01 |
          |2020-01-01    |make||model        |Galaxy||Not-Apple|1      |1            |1           |mw |2020|01   |01 |
          |2020-01-01    |make||model        |Iphone||         |1      |1            |1           |mw |2020|01   |01 |
          |2020-01-01    |make||model        |Iphone||         |3      |2            |1           |mw |2020|01   |01 |
          |2020-01-01    |make||model        |Iphone||Apple    |2      |2            |1           |mw |2020|01   |01 |
          |2020-01-01    |make||model        |||               |4      |2            |1           |mw |2020|01   |01 |
          |2020-01-01    |os                 |null             |4      |2            |null        |mw |2020|01   |01 |
          |2020-01-01    |os||locationtype   |||               |4      |2            |1           |mw |2020|01   |01 |
          |2020-01-02    |appname||appid     |||               |1      |1            |1           |mw |2020|01   |02 |
          |2020-01-02    |carrier||newcarrier|String||         |1      |1            |1           |mw |2020|01   |02 |
          |2020-01-02    |carrier||newcarrier|||               |1      |1            |1           |mw |2020|01   |02 |
          |2020-01-02    |connectiontype     |null             |1      |1            |null        |mw |2020|01   |02 |
          |2020-01-02    |connectiontype     |                 |1      |1            |null        |mw |2020|01   |02 |
          |2020-01-02    |locationtype       |null             |1      |1            |null        |mw |2020|01   |02 |
          |2020-01-02    |make||model        |||               |1      |1            |1           |mw |2020|01   |02 |
          |2020-01-02    |make||model        |||Apple          |1      |1            |1           |mw |2020|01   |02 |
          |2020-01-02    |os                 |null             |1      |1            |null        |mw |2020|01   |02 |
          |2020-01-02    |os||locationtype   |||               |1      |1            |1           |mw |2020|01   |02 |
          +--------------+-------------------+-----------------+-------+-------------+------------+---+----+-----+---+
        """)

      val ds_actual = UdpSegmentReportRunner.createReport(ComlinkdataDatasource.mw, udpDs.toDS)
        .reduce(_.unionByName(_))
        .sort("partition_date", "segment", "metric", "records")

      assertDatasetEquals(ds_expected, ds_actual)
    }

    it("can create a segments set from reveal with 1k sample file from createReport") {
      val ds_expected = expect_ds(
        """
          +--------------+-------------------+----------------------------------+-------+-------------+------------+------+----+-----+---+
          |partition_date|segment            |metric                            |records|distinct_ifas|distinct_ips|ds    |year|month|day|
          +--------------+-------------------+----------------------------------+-------+-------------+------------+------+----+-----+---+
          |2020-06-06    |appname||appid     |||                                |1000   |49           |54          |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||1419975b738da8d56cf93b36ed39375b|17     |10           |5           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||1b47d7001f88b258a66637cb4a878bf2|18     |2            |4           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||228635762b0dfc81b53313954e1391a3|3      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||289249218f204b370b088c2db84fd9de|8      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||3098e03b6ba6fc3a8120ce6f9abb5656|3      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||370a934f862d20f142b2152f715e70cb|6      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||49e6c21437fedef0dd7b12dc4c70c8ce|604    |32           |38          |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||62b200c1e6753a7552c942eaa3d807c0|197    |5            |0           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||694feedf5a4535ea0199f592e670b3f7|1      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||7b888b3516dd200710b6996ce5357069|4      |3            |3           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||8c63c2ecdcf196f950ed38b22b57145a|12     |5            |8           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||8de770f59cc6d2993621064d763df1a1|1      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||9111daff59cc5460975f5f83e08a5345|6      |3            |4           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||a4f4438e9ff4ea10d5048a34c40aee51|15     |2            |2           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||a536cae436831b558b3a4c6c92696737|4      |2            |4           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||b3a8e5bc62ddf4df61fc8678b9bc15a9|3      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||ca71e0092b9172c904a94ea6bee12ed8|3      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||cfcb94b6d8fddc0054fc1e47efa1239f|27     |4            |2           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||d78ff2f41cbef879305640b157e5d03b|3      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||e9c8a48c2d23e7faf50d705cc7624c75|5      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |appname||appid     |||f85029a4b45d3a7d0c6a20d9d80aad09|60     |3            |3           |reveal|2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|||                                |652    |38           |37          |reveal|2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|||                                |1000   |49           |54          |reveal|2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|||311-480                         |35     |8            |9           |reveal|2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|||311-490                         |6      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|||311-870                         |38     |4            |5           |reveal|2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|||AT&T                            |8      |2            |2           |reveal|2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|||Sprint                          |157    |3            |3           |reveal|2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|||T-Mobile                        |104    |9            |17          |reveal|2020|06   |06 |
          |2020-06-06    |connectiontype     |null                              |1000   |49           |null        |reveal|2020|06   |06 |
          |2020-06-06    |connectiontype     |CARRIER                           |155    |14           |null        |reveal|2020|06   |06 |
          |2020-06-06    |connectiontype     |HSPA                              |5      |1            |null        |reveal|2020|06   |06 |
          |2020-06-06    |connectiontype     |HSPA+                             |2      |1            |null        |reveal|2020|06   |06 |
          |2020-06-06    |connectiontype     |LTE                               |262    |5            |null        |reveal|2020|06   |06 |
          |2020-06-06    |connectiontype     |NA                                |210    |37           |null        |reveal|2020|06   |06 |
          |2020-06-06    |connectiontype     |WIFI                              |366    |17           |null        |reveal|2020|06   |06 |
          |2020-06-06    |locationtype       |null                              |1000   |49           |null        |reveal|2020|06   |06 |
          |2020-06-06    |locationtype       |GPS                               |1000   |49           |null        |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |Apple||                           |61     |10           |11          |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |Apple||iPad                       |8      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |Apple||iPhone                     |53     |9            |10          |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |Google||                          |1      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |Google||Pixel 2 XL                |1      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |LGE||                             |20     |2            |3           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |LGE||LG-Q710AL                    |1      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |LGE||LM-Q720                      |19     |1            |2           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |TCL||                             |28     |2            |5           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |TCL||A501DL                       |15     |1            |3           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |TCL||A502DL                       |13     |1            |2           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |Umx||                             |6      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |Umx||U683CL                       |6      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |samsung||                         |232    |11           |16          |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |samsung||SM-A102U                 |11     |1            |3           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |samsung||SM-G930P                 |15     |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |samsung||SM-G960U                 |153    |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |samsung||SM-G973U                 |4      |2            |2           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |samsung||SM-G975U                 |1      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |samsung||SM-J260T1                |28     |1            |2           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |samsung||SM-J737T1                |7      |1            |2           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |samsung||SM-N970U                 |3      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |samsung||SM-S727VL                |7      |1            |2           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |samsung||SPH-L710T                |3      |1            |1           |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |||                                |652    |38           |37          |reveal|2020|06   |06 |
          |2020-06-06    |make||model        |||                                |1000   |49           |54          |reveal|2020|06   |06 |
          |2020-06-06    |os                 |android                           |894    |32           |null        |reveal|2020|06   |06 |
          |2020-06-06    |os                 |ios                               |106    |17           |null        |reveal|2020|06   |06 |
          |2020-06-06    |os||locationtype   |android||                         |894    |32           |33          |reveal|2020|06   |06 |
          |2020-06-06    |os||locationtype   |android||GPS                      |894    |32           |33          |reveal|2020|06   |06 |
          |2020-06-06    |os||locationtype   |ios||                             |106    |17           |21          |reveal|2020|06   |06 |
          |2020-06-06    |os||locationtype   |ios||GPS                          |106    |17           |21          |reveal|2020|06   |06 |
          |2020-06-06    |os||locationtype   |||                                |1000   |49           |54          |reveal|2020|06   |06 |
          +--------------+-------------------+----------------------------------+-------+-------------+------------+------+----+-----+---+
          """)

      val df = spark.read.parquet(reveal1ksampleURL.toString)
        .withColumn("date", lit(Date.valueOf("2020-06-06"))).toDF
      val df_1 = df.transform(adapterReveal.rawToUdpRawDataset(startDateLocalDate, endDateLocalDate, _))
      val ds_actual = UdpSegmentReportRunner.createReport(datasourceReveal, df_1).reduce(_.unionByName(_))
        .sort("partition_date", "segment", "metric", "records")

      assertDatasetEquals(ds_expected, ds_actual)
    }

    it("can create a daily device set from mw with 1k sample file") {
      val ds_expected = expect_ds(
        """
          +--------------+-------------------+---------------------------------------------------------------------------+-------+-------------+------------+---+----+-----+---+
          |partition_date|segment            |metric                                                                     |records|distinct_ifas|distinct_ips|ds |year|month|day|
          +--------------+-------------------+---------------------------------------------------------------------------+-------+-------------+------------+---+----+-----+---+
          |2020-06-06    |appname||appid     |Android_Wattpad||                                                          |303    |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Android_Wattpad||wp.wattpad                                                |303    |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Draw Joust! Android||                                                      |38     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Draw Joust! Android||ru.galya.drawjoust                                    |38     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |LINE WEBTOON - Free Comics||                                               |5      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |LINE WEBTOON - Free Comics||com.naver.linewebtoon                          |5      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |MFP iOS||                                                                  |33     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |MFP iOS||341232718                                                         |33     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Musi - Simple Music Streaming||                                            |192    |1            |3           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Musi - Simple Music Streaming||591560124                                   |192    |1            |3           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Paper.io 2 Android||                                                       |187    |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Paper.io 2 Android||io.voodoo.paper2                                       |187    |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |PicsArt Photo Studio Android||                                             |51     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |PicsArt Photo Studio Android||com.picsart.studio                           |51     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Quiz Game 2020||                                                           |1      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Quiz Game 2020||pt.wm.milhes                                               |1      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Roller Splat! iOS||                                                        |49     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Roller Splat! iOS||1448852425                                              |49     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Spiral Roll iOS||                                                          |26     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Spiral Roll iOS||1482766542                                                |26     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Woodturning 3D Android||                                                   |7      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Woodturning 3D Android||com.BallGames.Woodturning                          |7      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Worldstar Hip Hop (Official)||                                             |84     |2            |3           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Worldstar Hip Hop (Official)||525958087                                    |37     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |Worldstar Hip Hop (Official)||com.pt.wshhp                                 |47     |1            |2           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |iFunny Android||                                                           |17     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |iFunny Android||mobi.ifunny                                                |17     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |my KONAMI Slots - Free Vegas Casino Slot Machines||                        |3      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |my KONAMI Slots - Free Vegas Casino Slot Machines||com.playstudios.mykonami|3      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |myVEGAS Slots - Vegas Casino Slot Machine Games||                          |4      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |myVEGAS Slots - Vegas Casino Slot Machine Games||com.playstudios.myvegas   |4      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |appname||appid     |||                                                                         |1000   |12           |15          |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|AT&T U-verse||                                                             |50     |2            |2           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|AT&T U-verse||310-410                                                      |33     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|AT&T U-verse||311-480                                                      |17     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Brantley Telephone Company||                                               |225    |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Brantley Telephone Company||311-480                                        |225    |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Buckeye Cablevision||                                                      |49     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Comcast Cable||                                                            |1      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Optimum Online||                                                           |3      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Optimum Online||311-480                                                    |3      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Spectrum||                                                                 |7      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Spectrum||                                                                 |87     |5            |5           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Spectrum||310-260                                                          |7      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Spectrum||310-410                                                          |26     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Spectrum||311-480                                                          |47     |2            |2           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|T-Mobile USA||                                                             |396    |2            |2           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|T-Mobile USA||310-260                                                      |396    |2            |2           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Verizon Wireless||                                                         |189    |1            |2           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|Verizon Wireless||311-480                                                  |189    |1            |2           |mw |2020|06   |06 |
          |2020-06-06    |carrier||newcarrier|||                                                                         |1000   |12           |15          |mw |2020|06   |06 |
          |2020-06-06    |connectiontype     |null                                                                       |1000   |12           |null        |mw |2020|06   |06 |
          |2020-06-06    |connectiontype     |CELLULAR                                                                   |585    |3            |null        |mw |2020|06   |06 |
          |2020-06-06    |connectiontype     |WIFI                                                                       |415    |10           |null        |mw |2020|06   |06 |
          |2020-06-06    |locationtype       |null                                                                       |1000   |12           |null        |mw |2020|06   |06 |
          |2020-06-06    |locationtype       |GPS                                                                        |84     |3            |null        |mw |2020|06   |06 |
          |2020-06-06    |locationtype       |IP                                                                         |916    |10           |null        |mw |2020|06   |06 |
          |2020-06-06    |make||model        |Apple||                                                                    |337    |5            |7           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |Apple||iPad Pro (9.7-inch) 6th Gen                                         |49     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |Apple||iPhone 11 Pro Max                                                   |33     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |Apple||iPhone 6s                                                           |26     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |Apple||iPhone 8 Plus                                                       |37     |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |Apple||iPhone XS Max                                                       |192    |1            |3           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |Samsung||                                                                  |377    |5            |5           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |Samsung||SM-A102U                                                          |359    |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |Samsung||SM-G928F                                                          |1      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |Samsung||SM-G935V                                                          |3      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |Samsung||SM-G950U                                                          |7      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |Samsung||SM-T510                                                           |7      |1            |1           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |||                                                                         |286    |3            |3           |mw |2020|06   |06 |
          |2020-06-06    |make||model        |||                                                                         |1000   |12           |15          |mw |2020|06   |06 |
          |2020-06-06    |os                 |null                                                                       |1000   |12           |null        |mw |2020|06   |06 |
          |2020-06-06    |os||locationtype   |||                                                                         |1000   |12           |15          |mw |2020|06   |06 |
          |2020-06-06    |os||locationtype   |||GPS                                                                      |84     |3            |3           |mw |2020|06   |06 |
          |2020-06-06    |os||locationtype   |||IP                                                                       |916    |10           |13          |mw |2020|06   |06 |
          +--------------+-------------------+---------------------------------------------------------------------------+-------+-------------+------------+---+----+-----+---+
        """)

      val df = spark.read.parquet(mw1ksampleURL.toString)
        .withColumn("date", lit(Date.valueOf("2020-06-06"))).toDF
      val df_1 = df.transform(adapterMobileWalla.rawToUdpRawDataset(startDateLocalDate, endDateLocalDate, _))
      val ds_actual = UdpSegmentReportRunner
        .createReport(datasourceMobileWalla, df_1)
        .reduce(_.unionByName(_))
        .sort("partition_date", "segment", "metric", "records")

      assertDatasetEquals(ds_expected, ds_actual)
    }
  }

  private def expect_ds(show: String) = {
    import spark.implicits._
    parseShow[SegmentReportData](show).map {
      case s if s.distinct_ips == null || s.distinct_ips.isEmpty => s
      case s => s.copy(distinct_ips = Some(s.distinct_ips.get.toString.toLong))
    }.toDS // Cannot cast "string" to "long".. scala bug?
  }
}
