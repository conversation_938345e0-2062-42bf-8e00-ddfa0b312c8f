package com.comlinkdata.emrjobs.spark.udp.locationset

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.{lit, when}

import scala.collection.mutable

class DayHourSumUDAFSpec extends CldSparkBaseSpec with LazyLogging {
  import spark.implicits._

  describe("Testing CarrierUDAF") {
    val dayHourSum = new DayHourSumUDAF
    val items = for (
      dayHour <- (0 to 23);
      connectionType <- Seq("CELLULAR", "WIFI", null)
    ) yield(dayHour, connectionType)

    lazy val df = items.toDF("day_hour", "connection_type_maxmind").
      withColumn("lat", lit(1)).
      withColumn("lng", lit(1)).
      groupBy("lat", "lng").
      agg(dayHourSum($"day_hour", $"connection_type_maxmind") as "day_hour_struct").
      withColumn("day_hours", when($"day_hour_struct".isNotNull, $"day_hour_struct"("day_hours")).otherwise(Array.empty[Int])).
      withColumn("day_hours_cell", when($"day_hour_struct".isNotNull, $"day_hour_struct"("day_hours_cell")).otherwise(Array.empty[Int])).
      withColumn("day_hours_wifi", when($"day_hour_struct".isNotNull, $"day_hour_struct"("day_hours_wifi")).otherwise(Array.empty[Int])).
      collect()

    it("Should have day hours"){
      val dayHour = df(0).getAs[mutable.WrappedArray[Int]](3)
      dayHour == (0 to 23).map(i => 3)
    }

    it("Should have cell"){
      val cell = df(0).getAs[mutable.WrappedArray[Int]](4)
      cell == (0 to 23).map(i => 1)
    }

    it("Should have wifi"){
      val wifi = df(0).getAs[mutable.WrappedArray[Int]](5)
      wifi == (0 to 23).map(i => 1)
    }
  }
}
