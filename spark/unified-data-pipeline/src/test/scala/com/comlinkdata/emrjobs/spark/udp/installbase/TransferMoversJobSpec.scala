package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, UriDFTableRW}
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.comlinkdata.largescale.schema.udp.installbase.IPHighConfidence
import com.comlinkdata.largescale.schema.udp.location.Point
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions.{lit, udf}
import org.scalatest.BeforeAndAfterAll

import java.net.URI
import java.sql.Date
import java.time.LocalDate

case class IPHighConfidenceLatLng(
  ifa: Ifa,
  ip: Ip,
  household_id: Array[Byte],
  obs_count: Long,
  night_obs_count: Long,
  carrier: String,
  ip_min_date: Date,
  ip_max_date: Date,
  case_method: Int,
  census_block_id: String,
  census_group_id: String,
  percent_block: Float,
  discounted_count: Float,
  household_count: Long,
  lat: Float,
  lng: Float
)

class TransferMoversSpec extends CldSparkBaseSpec with BeforeAndAfterAll {
  import spark.implicits._

  private val highConfidence = URI.create("table://hcTransfer")
  private val carrierLookupPath: URI = new URI("table://carrierlookupCoTransfer/v=1.7.019")
  private val outTable: URI = new URI("table://transferFullRunOut")

  private val hcDay = LocalDate.of(2021, 11, 19)
  private val hcPrevDay = LocalDate.of(2021, 9, 1)

  private val hcDay2020 = LocalDate.of(2020, 11, 19)
  private val hcPrevDay2020 = LocalDate.of(2020, 9, 1)

  def toPoint : UserDefinedFunction =
    udf[Point.Float, Float, Float] ((lat: Float, lng: Float) => Point(lat, lng))

  override def beforeAll(): Unit = {
    super.beforeAll()

    val hcDf = dataset[IPHighConfidenceLatLng] {
      s"""
         |     ifa|      ip|household_id|obs_count|night_obs_count|carrier|ip_min_date|ip_max_date|case_method|census_block_id|census_group_id|percent_block|discounted_count|household_count|  lat|  lng|
         |AQIDBAU=|CgoKCg==|    AQIDBAU=|       10|              1|  fake1| 2021-12-10| 2021-12-03|          0|           1234|           1234|         100 |            99.9|             12|123.1|234.1|
         |TQIDBAU=|CgoDCg==|    AQIDBAU=|       10|              1|  fake2| 2021-12-10| 2021-12-03|          0|           1234|           1234|         100 |            99.9|             12|123.1|234.1|
         |KQIDBAU=|CgoTCg==|    AQIDBAU=|       10|              1|  fake3| 2021-12-10| 2021-12-03|          0|           1234|           1234|         100 |            99.9|             12|123.1|234.1|
         |DQIDBAU=|CgoXCg==|    AQIDBAU=|       10|              1|  fake1| 2021-12-10| 2021-12-03|          0|           1234|           1234|         001 |            99.9|             12|123.1|234.1|
         |AQIDBAT=|CgoKCT==|    AQIDBAT=|       10|              1|  fake1| 2021-12-10| 2021-12-03|          0| 08031015400123|           1234|         100 |            99.9|             12|123.1|234.1|
         """
    }.withColumn("best_point", toPoint($"lat", $"lng"))
      .withColumn("ifa_max_date", lit(Date.valueOf("2021-12-01")))
      .withColumn("home_hour_date_count", lit(1))
      .withColumn("global_ip_min_date", lit(Date.valueOf("2021-12-01")))
      .withColumn("year", lit(hcDay.getYearString))
      .withColumn("month", lit(hcDay.getMonthValueString))
      .withColumn("day", lit(hcDay.getDayOfMonthString))
      .as[IPHighConfidence]
      .drop("year", "month", "day")

    val tslHC = TimeSeriesLocation
      .ofYmdDatePartitions(highConfidence)
      .build

    UriDFTableRW.fromStr(tslHC.partition(hcDay)).write(hcDf.toDF())
    UriDFTableRW.fromStr(tslHC.partition(hcDay2020)).write(hcDf.toDF())

    val hcPrev = dataset[IPHighConfidenceLatLng] {
      s"""
         |     ifa|      ip|household_id|obs_count|night_obs_count|carrier|ip_min_date|ip_max_date|case_method|census_block_id|census_group_id|percent_block|discounted_count|household_count|    lat|  lng|
         |AQIDBAU=|CgoACg==|    AQIDBAU=|       10|              1|  fake1| 2021-12-01| 2021-12-03|          0|           1235|           1234|         100 |            99.9|             12|   79.1|234.1|
         |TQIDBAU=|CgoBCg==|    AQIDBAU=|       10|              1|  fake1| 2021-12-01| 2021-12-03|          0|           1235|           1234|         100 |            99.9|             12|123.101|234.1|
         |KQIDBAU=|CgoTCg==|    AQIDBAU=|       10|              1|  fake2| 2021-12-01| 2021-12-03|          0|           1234|           1234|         100 |            99.9|             12|  123.1|234.1|
         |DQIDBAU=|CgoXCg==|    AQIDBAU=|       10|              1|  fake3| 2021-12-01| 2021-12-03|          0|           1235|           1234|         001 |            99.9|             12|  123.1|234.1|
         |AQIDBAT=|CgoKCT==|    AQIDBAT=|       10|              1|  fake1| 2021-12-10| 2021-12-03|          0| 08031015400456|           1234|         100 |            99.9|             12|123.1|234.1|
         """
    }.withColumn("best_point", toPoint($"lat", $"lng"))
      .withColumn("ifa_max_date", lit(Date.valueOf("2021-12-01")))
      .withColumn("home_hour_date_count", lit(1))
      .withColumn("global_ip_min_date", lit(Date.valueOf("2021-12-01")))
      .withColumn("year", lit(hcDay.getYearString))
      .withColumn("month", lit(hcDay.getMonthValueString))
      .withColumn("day", lit(hcDay.getDayOfMonthString))
      .as[IPHighConfidence]
      .drop("year", "month", "day")

    UriDFTableRW.fromStr(tslHC.partition(hcPrevDay)).write(hcPrev.toDF())
    UriDFTableRW.fromStr(tslHC.partition(hcPrevDay2020)).write(hcPrev.toDF())

    // Make an old version of fake1 with a bad sp_platform equal to 123 before 2021; 2nd test asserts this is used
    val carrierLookup = dataset[CarrierLookup] {
      s"""
         |mw_carrier            |consolidated_carrier  |consolidated_id|service_territory_name  |sp  |sp_platform|min_date  |
         |fake1                 |fake1                 |10061          |AT&T Inc.               |123 |123        |2016-11-01|
         |fake1                 |fake1                 |10061          |AT&T Inc.               |390 |390        |2021-01-01|
         |cell2                 |fake2                 |10062          |fake Inc.               |391 |391        |2016-11-01|
         |cell4                 |fake3                 |10063          |CenturyLink, Inc.       |852 |852        |2016-11-01|
         |Cox Communications    |Cox Communications    |10031          |Cox Communications, Inc.|1170|1170       |2016-11-01|
         """
    }

    UriDFTableRW(carrierLookupPath).write(carrierLookup.toDF())
  }

  describe("Transfers Tests") {
    it("Full Run - 2022 Carrier Lookup Date") {
      val config = TransferMoversConfig(
        highConfPath = highConfidence,
        carrierLookupPath = carrierLookupPath,
        hcDate = hcDay,
        hcPrevDate = hcPrevDay,
        minMoveDist = None,
        outPath = outTable,
        repartition = None
      )

      TransferMoversRunner.runJob(config)

      val outTSL = TimeSeriesLocation
        .ofYmdDatePartitions(outTable)
        .build

      val outDf = UriDFTableRW.fromStr(outTSL.partition(hcDay)).read()

      outDf.count() shouldBe 1
      outDf.select("churn_date").take(1).head(0) shouldEqual LocalDate.of(2021, 12, 10).toDate
      outDf.select("winning_sp").take(1).head(0) shouldBe 390
    }

    it("Full Run - 2020 Carrier Lookup Date") {
      val config = TransferMoversConfig(
        highConfPath = highConfidence,
        carrierLookupPath = carrierLookupPath,
        hcDate = hcDay2020,
        hcPrevDate = hcPrevDay2020,
        minMoveDist = None,
        outPath = outTable,
        repartition = None
      )

      TransferMoversRunner.runJob(config)

      val outTSL = TimeSeriesLocation
        .ofYmdDatePartitions(outTable)
        .build

      val outDf = UriDFTableRW.fromStr(outTSL.partition(hcDay2020)).read()

      outDf.count() shouldBe 1
      outDf.select("winning_sp").take(1).head(0) shouldBe 123
    }
  }
}
