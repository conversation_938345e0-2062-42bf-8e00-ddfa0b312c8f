package com.comlinkdata.emrjobs.spark.udp.homes

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.homes.UdpHhStepDIfaIpLocationSchema.SkinnyUdpHhStepCAggWindowSummary
import com.comlinkdata.largescale.commons.Utils
import org.apache.spark.sql.Dataset

class UdpHhStepCAggWindowSummaryJobSpec extends CldSparkBaseSpec {

  import UdpHhStepCAggWindowSummarySchema._

  describe("schema") {
    describe("SkinnyUdpHhStepCAggWindowSummary") {
      it("contains a subset of original object") {
        Utils.reflection.classAccessorNames[UdpHhStepCAggWindowSummary] should contain allElementsOf SkinnyUdpHhStepCAggWindowSummary.colNames
      }

    }
  }
//  val sampleUri = getClass.getResource("step_a_result.parquet")
//
//  lazy val loadSampleDs: Dataset[GeocodedResult] = {
//    import spark.implicits._
//    spark.read
//      .parquet(sampleUri.toString)
//      .as[GeocodedResult]
//      .cache()
//  }

//  describe("transform") {
//
//    it("should execute without exceptions") {
//      val input: Dataset[GeocodedResult] = loadSampleDs
//      UdpHhStepGCldBroadbandHouseholdsRunner
//        .createCldDevicesWithHouseholdJob(input)
//        .show(5)
//    }

//  }
}
