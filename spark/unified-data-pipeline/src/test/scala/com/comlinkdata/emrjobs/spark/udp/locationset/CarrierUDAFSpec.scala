package com.comlinkdata.emrjobs.spark.udp.locationset

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.Row
import org.apache.spark.sql.catalyst.expressions.GenericRowWithSchema

import java.sql.Date

class CarrierUDAFSpec extends CldSparkBaseSpec with LazyLogging {
  import spark.implicits._


  describe("Testing CarrierUDAF") {
    val carrierUDAF = new CarrierUDAF
    lazy val df = Seq(
      (1, 1, "carrier", null, Date.valueOf("2021-12-21"), 1L),
      (1, 1, "carrier", "CELLULAR", Date.valueOf("2021-12-21"), 1L),
      (1, 1, "carrier", "CELLULAR", Date.valueOf("2021-12-21"), 1L),
      (1, 1, "carrier", "CELLULAR", Date.valueOf("2021-12-22"), 1L),
      (1, 1, "carrier", "WIFI", Date.valueOf("2021-12-21"), 1L),
      (1, 1, "carrier", "WIFI", Date.valueOf("2021-12-21"), 1L),
      (1, 1, "carrier", "WIFI", Date.valueOf("2021-12-21"), 1L),
      (1, 1, "carrier", "WIFI", Date.valueOf("2021-12-22"), 1L),
      (1, 1, "carrier", "WIFI", Date.valueOf("2021-12-22"), 2L)
    ).toDF("lat", "lng", "carrier", "connection_type_maxmind", "date", "location_hash").
      groupBy("lat", "lng").
      agg(carrierUDAF($"carrier", $"connection_type_maxmind", $"date", $"location_hash").as("carrier_map_struct"))

    def rowsToMap(rows: Array[Row]): Map[(String,Date), Map[Int, Int]] = {
      rows(0).getAs[Map[GenericRowWithSchema, Map[Int, Int]]](0).map{
        case(k,v) => Map((k.getAs[String](0), k.getAs[Date](1))->v)
      }.reduce(_ ++ _)
    }

    it("Should have cell"){
      val cell : Map[(String, Date), Map[Int, Int]] = rowsToMap(df.select($"carrier_map_struct"("cell")).collect())
      cell.size shouldBe 2
      cell(("carrier", Date.valueOf("2021-12-21"))) shouldBe Map(1 -> 2)
      cell(("carrier", Date.valueOf("2021-12-22"))) shouldBe Map(1 -> 1)
    }

    it("Should have wifi"){
      val wifi : Map[(String, Date), Map[Int, Int]] = rowsToMap(df.select($"carrier_map_struct"("wifi")).collect())
      wifi.size shouldBe 2
      wifi(("carrier", Date.valueOf("2021-12-21"))) shouldBe Map(1 -> 3)
      wifi(("carrier", Date.valueOf("2021-12-22"))) shouldBe Map(1 -> 1, 2->1)
    }
  }
}
