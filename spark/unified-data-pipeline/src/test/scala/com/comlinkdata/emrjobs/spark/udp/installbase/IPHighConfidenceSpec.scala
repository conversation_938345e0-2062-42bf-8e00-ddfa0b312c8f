package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.installbase.IPHighConfidenceSetRunner.{process, highConfidence}
import com.comlinkdata.emrjobs.spark.udp.installbase.prefilter.IpToLocationsRunner
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, UriDFTableRW, LocalDateRange}
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.comlinkdata.largescale.schema.udp.installbase.{IPHighConfidenceNoLoc, UdpHousehold, IpToLocCounts, IPHighConfidence}
import com.comlinkdata.largescale.schema.udp.location.Point
import com.typesafe.scalalogging.LazyLogging
import org.apache.sedona.sql.utils.SedonaSQLRegistrator
import org.apache.spark.ml.classification.RandomForestClassificationModel
import org.apache.spark.sql.Dataset
import org.junit.rules.TemporaryFolder
import org.scalatest.{BeforeAndAfterEach, BeforeAndAfterAll}

import java.net.URI
import java.sql.Date
import java.time.LocalDate

class IPHighConfidenceSpec extends CldSparkBaseSpec with LazyLogging with BeforeAndAfterEach with BeforeAndAfterAll {
  import spark.implicits._

  private val geoShapeJson = this.getClass().getResource("/com/comlinkdata/emrjobs/spark/udp/installbase/geoshape")
  private val ipLocs = URI.create("table://ipLocs2")

  private var basePath: URI = new URI("")
  private val beforeAllTempFolder = new TemporaryFolder()

  private val startDate = LocalDate.of(2021, 10, 1)

  private val ifas: Array[Ifa] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]
  private val ips: Array[Ip] = (32 to 62).map( x => Array(192.toByte, 168.toByte, 0.toByte, x.toByte)).toArray[Array[Byte]]

  private val DAYS_TO_LOOK_BACK = 30
  private val DAYS_TO_LOOK_AHEAD = 7

  override def beforeAll(): Unit = {
    super.beforeAll()
    SedonaSQLRegistrator.registerAll(spark)

    writeIpToLocs(ipLocs)
  }

  private def writeIpToLocs(ipTolocsPath: URI) = {
    for (day <- LocalDateRange.of(
      startDateInclusive = startDate.minusDays(DAYS_TO_LOOK_BACK),
      endDateInclusive = startDate.plusDays(DAYS_TO_LOOK_AHEAD))) {

      val df = spark.createDataFrame(
        Seq(
          IpToLocCounts(
            ip = ips(2),
            obs_count = 123,
            night_obs_count = 100,
            ifas = Seq(ifas(2)),
            ifa_count = 1,
            hours_seen = Seq(1, 2, 3),
            loc_counts = Seq((Point(34.001364f, -85.996636f), 10)).toMap
          ),
          IpToLocCounts(
            ip = ips(3),
            obs_count = 12,
            night_obs_count = 10,
            ifas = Seq(ifas(3)),
            ifa_count = 1,
            hours_seen = Seq(1, 2, 3),
            loc_counts = Seq((Point(34.00f, -85.99f), 10)).toMap
          )
        )
      )

      val dsOut = df.as[IpToLocCounts].transform(IpToLocationsRunner.explodeCounts)

      UriDFTableRW.fromStr(TimeSeriesLocation
        .ofYmdDatePartitions(ipTolocsPath)
        .build.partition(day)
      ).writeDs(dsOut)
    }
  }

  override def beforeEach(): Unit = {
    super.beforeAll()

    beforeAllTempFolder.create()
    val folder = beforeAllTempFolder.newFolder()
    folder.delete()
    basePath = folder.toURI
  }

  describe("full run") {
    val past = LocalDateRange.of(startDate.minusDays(DAYS_TO_LOOK_BACK), startDate.minusDays(1))
    val dataPast = for {
      d <- past
      n <- 1 to 3
    } yield
      IfaToIpYMD(
        ip = ips(n),
        ifa = ifas(n),
        carrier = "Comcast",
        consolidated_id = "Comcast",
        connection_type = "Cable/DSL",
        sp_platform =  "1005",
        organization = "Comcast",
        autonomous_system_number = "1",
        obs_count = 10,
        night_obs_count = 5,
        household_obs_count = 0,
        household_weighted_obs_count = 0,
        business_weighted_obs_count = 0,
        year = d.getYear.toString,
        month = d.getMonthValue.formatted("%02d"),
        day = d.getDayOfMonth.formatted("%02d")
      )

    val future = LocalDateRange.of(startDate, startDate.plusDays(DAYS_TO_LOOK_AHEAD))
    val dataFuture = for {
      d <- future
      n <- 2 to 4
    } yield
      IfaToIpYMD(
        ip = ips(n),
        ifa = ifas(n),
        carrier = "Comcast",
        consolidated_id = "Comcast",
        connection_type = "Cable/DSL",
        sp_platform = "1005",
        organization = "Comcast",
        autonomous_system_number = "1",
        obs_count = 10,
        night_obs_count = 5,
        household_obs_count = 0,
        household_weighted_obs_count = 0,
        business_weighted_obs_count = 10,
        year = d.getYear.toString,
        month = d.getMonthValue.formatted("%02d"),
        day = d.getDayOfMonth.formatted("%02d")
      )

    // hardcoded hash value of hash(ips(5), startDate.minus(daysToLookBack)
    // have to use hardcoded because ips(5) is a moving target
    val household_id_ip5: Array[Byte] = Array(0.toByte)
    val householdSeq = Seq(
      UdpHousehold(
        ifa = ifas(2),
        ip = ips(2),
        household_id = household_id_ip5,
        carrier = "carrier",
        is_hh_split = false,
        potential_switch_ip = null,
        global_ip_min_date = startDate.minusDays(DAYS_TO_LOOK_BACK).toDate,
        ip_min_date = startDate.minusDays(DAYS_TO_LOOK_BACK).toDate,
        ip_max_date = startDate.minusDays(1).toDate,
        days_seen_count = DAYS_TO_LOOK_BACK,
        ip_sequence_raw_history = Map.empty[String, Ip],
        ip_sequence_cleansed_map = Map.empty[String, Ip],
        ip_sequence = Array.empty[Ip],
        year = startDate.minusDays(1).getYearString,
        month = startDate.minusDays(1).getMonthValueString,
        day = startDate.minusDays(1).getDayOfMonthString
      )
    )
    val ipClassifierPath = this.getClass().getResource("/com/comlinkdata/emrjobs/spark/udp/installbase/bus_res_classifier.model").toURI

    ignore("with parquet") {
      import spark.implicits._

      val ifaToIpSeq = dataPast ++ dataFuture
      val ifaToIpDataRoot = basePath.resolve("ifaToIp")
      spark.createDataFrame(ifaToIpSeq).write
        .partitionBy("year", "month", "day")
        .parquet(ifaToIpDataRoot.toString)

      val hhPath = basePath.resolve("hh")
      spark.createDataFrame(householdSeq).write
        .partitionBy("year", "month", "day")
        .parquet(hhPath.toString)

      val outPath = basePath.resolve("hc_test_out")

      val config: IPHighConfidenceSetConfig = IPHighConfidenceSetConfig(
        ifaToIpPath = ifaToIpDataRoot,
        outputPath = outPath,
        startDate = startDate,
        daysToLookBack = DAYS_TO_LOOK_BACK,
        daysToLookAhead = DAYS_TO_LOOK_AHEAD,
        ipLocsPath = ipLocs,
        polygonsLocation = geoShapeJson.toURI,
        repartition = None
      )

      IPHighConfidenceSetRunner.runJob(config)

      val hcOutTSL = TimeSeriesLocation
        .ofYmdDatePartitions(outPath)
        .build

      val data: Dataset[IPHighConfidence] = IPHighConfidence.read(outPath, startDate).as[IPHighConfidence]
      
      val rows: Array[IPHighConfidence] = data.orderBy($"ip").collect()
      rows(0).ip shouldBe ips(2)
      rows.length shouldBe 2
    }

    it("without parquet") {

      val dsPast: Dataset[IfaToIpYMD] = dataPast.toDS()

      val dsFuture: Dataset[IfaToIpYMD] = dataFuture.toDS()

      val ipClassifierModel = RandomForestClassificationModel.load(ipClassifierPath.toString)

      assertTrue(ipClassifierModel != null)

      val data = process(dsPast, dsFuture, startDate)
      val rows: Array[IPHighConfidenceNoLoc] = data.orderBy($"ip").collect()

      rows(0).ip shouldBe ips(2)
      rows.length shouldBe 2
    }
  }

  describe("highConfidence method"){
    it("handles single day"){
      val candidates: Dataset[IfaWithIpCandidate] = Seq(
        IfaWithIpCandidate(
          ifa = ifas(0),
          ip = ips(0),
          date_count = 1,
          home_hour_date_count = 1,
          obs_count = 1,
          night_obs_count = 1,
          ip_min_date = Date.valueOf("2021-09-30"),
          ip_max_date = Date.valueOf("2021-09-30"),
          carrier = "carrier",
          ip_set = Array.empty[Ip],
          ifa_max_date = Date.valueOf("2021-09-30")
        )
      ).toDS()

      val future: Dataset[IfaToIpYMD] = Seq(
        IfaToIpYMD(
          ip = ips(0),
          ifa = ifas(0),
          carrier = "Comcast",
          consolidated_id = "Comcast",
          connection_type = "Cable/DSL",
          organization = "Comcast",
          sp_platform = "1005",
          autonomous_system_number = "1",
          obs_count = 10,
          night_obs_count = 1,
          household_obs_count = 0,
          household_weighted_obs_count = 0,
          business_weighted_obs_count = 10,
          year = "2021",
          month = "10",
          day = "01"
        )
      ).toDS()

      val result = candidates.transform(highConfidence(future)).collect()
      result.length shouldBe 1

    }
    it("handles multiple days"){
      val candidates: Dataset[IfaWithIpCandidate] = Seq(
        IfaWithIpCandidate(
          ifa = ifas(0),
          ip = ips(0),
          date_count = 1,
          home_hour_date_count = 1,
          obs_count = 1,
          night_obs_count = 1,
          ip_min_date = Date.valueOf("2021-09-30"),
          ip_max_date = Date.valueOf("2021-09-30"),
          carrier = "carrier",
          ip_set = Array.empty[Ip],
          ifa_max_date = Date.valueOf("2021-09-30")
        )
      ).toDS()

      val future: Dataset[IfaToIpYMD] = Seq(
        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005",  "1", 10, 1, 1, 1, 10, "2021", "10", "01"),
        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "Cable/DSL", "Comcast", "1005", "1", 10, 1, 1, 1, 10, "2021", "10", "02")
      ).toDS()

      val result = candidates.transform(highConfidence(future)).collect()
      result.length shouldBe 1

    }
    it("throws away data that isn't seen in future dataset"){
      val candidates: Dataset[IfaWithIpCandidate] = Seq(
        IfaWithIpCandidate(
          ifa = ifas(0),
          ip = ips(0),
          date_count = 1,
          home_hour_date_count = 1,
          obs_count = 1,
          night_obs_count = 1,
          ip_min_date = Date.valueOf("2021-09-30"),
          ip_max_date = Date.valueOf("2021-09-30"),
          carrier = "carrier",
          ip_set = Array.empty[Ip],
          ifa_max_date = Date.valueOf("2021-09-30")
        ),
        IfaWithIpCandidate(
          ifa = ifas(1),
          ip = ips(1),
          date_count = 1,
          home_hour_date_count = 1,
          obs_count = 1,
          night_obs_count = 1,
          ip_min_date = Date.valueOf("2021-09-30"),
          ip_max_date = Date.valueOf("2021-09-30"),
          carrier = "carrier",
          ip_set = Array.empty[Ip],
          ifa_max_date = Date.valueOf("2021-09-30")
        )
      ).toDS()

      val future: Dataset[IfaToIpYMD] = Seq(
        IfaToIpYMD(ips(0), ifas(0), "Comcast", "Comcast", "1005", "Cable/DSL", "Comcast", "1", 10, 1, 1, 1 , 10, "2021", "10", "01")
      ).toDS()

      val result = candidates.transform(highConfidence(future)).collect()
      result.length shouldBe 1
    }
  }
}
