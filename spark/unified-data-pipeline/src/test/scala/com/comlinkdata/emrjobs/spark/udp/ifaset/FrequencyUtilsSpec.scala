package com.comlinkdata.emrjobs.spark.udp.ifaset

import com.comlinkdata.commons.testing.CldBaseSpec
import com.comlinkdata.emrjobs.spark.udp.ifaset.FrequencyUtils.{mergeDayStats, filterRecentEntries}
import com.comlinkdata.largescale.schema.udp.tier2
import com.comlinkdata.largescale.schema.udp.tier2.DayStat

import java.sql.Date

class FrequencyUtilsSpec extends CldBaseSpec {

  describe("can merge day stat maps") {
    val day1 = Date.valueOf("2021-01-01")
    val day2 = Date.valueOf("2021-01-02")
    val day3 = Date.valueOf("2021-01-03")
    it("can recognize new days") {
      val yesterday = Map(
        "app1" -> tier2.DayStat(first = day1, last = day2, distinct_days = 1),
        "app2" -> tier2.DayStat(first = day1, last = day2, distinct_days = 1))
      val today = Map(
        "app2" -> tier2.DayStat(first = day3, last = day3, distinct_days = 1),
        "app3" -> tier2.DayStat(first = day3, last = day3, distinct_days = 1))
      mergeDayStats(yesterday, today) shouldBe Map(
        "app1" -> tier2.DayStat(first = day1, last = day2, distinct_days = 1),
        "app2" -> tier2.DayStat(first = day1, last = day3, distinct_days = 2),
        "app3" -> tier2.DayStat(first = day3, last = day3, distinct_days = 1)
      )
    }
    it("can filter most & least recent entries") {
      filterRecentEntries(Map(
        "app1" -> tier2.DayStat(first = day1, last = day1, distinct_days = 1),
        "app3" -> tier2.DayStat(first = day1, last = day2, distinct_days = 2),
        "app2" -> tier2.DayStat(first = day1, last = day2, distinct_days = 2),
        "app4" -> tier2.DayStat(first = day3, last = day3, distinct_days = 1)
      ), 3) should contain theSameElementsAs Map(
        "app2" -> tier2.DayStat(first = day1, last = day2, distinct_days = 2),
        "app4" -> tier2.DayStat(first = day3, last = day3, distinct_days = 1)
      )
      filterRecentEntries(Map(
        "app1" -> tier2.DayStat(first = day1, last = day2, distinct_days = 1),
        "app3" -> tier2.DayStat(first = day1, last = day2, distinct_days = 2),
        "app4" -> tier2.DayStat(first = day3, last = day3, distinct_days = 1)
      ), 3) should contain theSameElementsAs Map(
        "app1" -> tier2.DayStat(first = day1, last = day2, distinct_days = 1),
        "app3" -> tier2.DayStat(first = day1, last = day2, distinct_days = 2),
        "app4" -> tier2.DayStat(first = day3, last = day3, distinct_days = 1)
      )
      filterRecentEntries(Map(
        "app1" -> tier2.DayStat(first = day1, last = day2, distinct_days = 1),
        "app3" -> tier2.DayStat(first = day1, last = day2, distinct_days = 1),
        "app4" -> tier2.DayStat(first = day3, last = day3, distinct_days = 1)
      ), 3) should contain theSameElementsAs Map(
        "app1" -> tier2.DayStat(first = day1, last = day2, distinct_days = 1),
        "app4" -> tier2.DayStat(first = day3, last = day3, distinct_days = 1)
      )
    }
  }
}
