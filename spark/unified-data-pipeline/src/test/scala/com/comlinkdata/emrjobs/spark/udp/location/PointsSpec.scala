package com.comlinkdata.emrjobs.spark.udp.location

import com.comlinkdata.largescale.udp._
import com.comlinkdata.commons.testing.CldBaseSpec
import com.comlinkdata.largescale.schema.udp.location.{Point, Rectangle}


class PointsSpec extends CldBaseSpec {

  describe("variance sample") {

    it("calculates simple variance in lat/lng independently") {
      val points = (1 to 6) map (i => Point(i.toFloat, i.toFloat * -1))
      Points.varianceSample(points) shouldBe Point(3.5f, 3.5f)
    }

    it("maps the correct dimensions") {
      val points = (1 to 6) map (i => Point(i.toFloat, i.toFloat * 2))
      val result = Points.varianceSample(points)
      result.lat should be < result.lng
    }
  }

  describe("outlierRatio") {
    it("always returns -1") {
      val p =  Point(1f,2f)
      Points.outlierRatio(p :: Nil) shouldBe -1
    }
  }

  describe("roundedLocation4") {

    it("multiplies a whole number") {
      val p = Point(88.0F, 1.0F)
      Points.roundedLocation4(p) shouldBe Point(880000, 10000)
    }

    it("rounds up") {
      val p = Point(1.555455555F, 179.555455555F)
      Points.roundedLocation4(p) shouldBe Point(15555, 1795555)
    }

    it("rounds down") {
      val p = Point(1.555444444F, 179.555433333F)
      Points.roundedLocation4(p) shouldBe Point(15554, 1795554)
    }

    it("antimeridian results are always positive") {
      val p = Point(0f, -179.99999f)
      Points.roundedLocation4(p) shouldBe Point(0, 1800000)
    }

    describe("lat lng boundaries") {
      it("errors if longitude > 180") {
        an[IllegalArgumentException] should be thrownBy Points.roundedLocation4(Point(1f, 180.1f))
      }

      it("errors if longitude <= -180") {
        an[IllegalArgumentException] should be thrownBy Points.roundedLocation4(Point(1f, -180.1f))
      }

      it("does not error if longitude equals 180"){
        noException should be thrownBy Points.roundedLocation4(Point(1f, 180f))
      }

      it("errors if latitude > 90") {
        an[IllegalArgumentException] should be thrownBy Points.roundedLocation4(Point(90.1f, 0f))
      }

      it("errors if latitude < -90") {
        an[IllegalArgumentException] should be thrownBy Points.roundedLocation4(Point(-90.1f, 0f))
      }

      it("does not error if latitude is < 90") {
        noException should be thrownBy Points.roundedLocation4(Point(90f, 0f))
      }

      it("does not error if latitude is -90") {
        noException should be thrownBy Points.roundedLocation4(Point(-90f, 0f))
      }
    }
  }

  describe("centroid") {
    val p = Point(1F, 1)
    it("should have a centroid equal to itself for a single point") {
      Points.centroid(Seq(p)) shouldBe p
    }

    it("should average xs and ys") {
      val points = (0 to 10).map(i => Point(i.toFloat, i.toFloat * -1))
      Points.centroid(points) shouldBe Point(5.0f, -5.0f)
    }
  }



  describe("envelope") {

    describe("constructor float float float float") {
      it("should build points in correct order") {
        val expected = Rectangle(Point(1, 2), Point(3, 4))
        Rectangle(1, 2, 3, 4) shouldBe expected
      }
    }

    describe("longitude wrapping") {

      ignore("should append a large negative across the antimeridian") {
        def stubAppend(r: Rectangle.Float, p: Point.Float) = {
          Points.envelope(Seq(r.bl, r.tr, p))
        }

        val r = Rectangle(1F, -179.5f, 1, 178)
        val p = Point(1F, -179)
        stubAppend(r, p) shouldBe Rectangle(1, -179, 1, 178)
      }

      it("the envelope of a point is itself 1") {
        val p = Point(1F, -179)
        Points.envelope(p :: Nil) shouldBe Rectangle(p, p)
      }

      it("the envelope of a point is itself 2") {
        val p = Point(1F, 178)
        Points.envelope(p :: Nil) shouldBe Rectangle(p, p)
      }

      //        it("should add 180 to -180") {
      //          val r = Rectangle(1, 180, 10, 179)
      //          val p = Point(5, -180)
      //          r append1 p shouldBe r
      //        }
      //
      //        it("should append a small lng to a positive across the antimeridian") {
      //          val r = Rectangle(1,-179,-1,-178)
      //          val p = Point(0,179)
      //          r append1 p shouldBe Rectangle(1, 179, -1, -178)
      //        }
      //
      //        it("should append in order to create the smallest rectangle possible - in the western (negative) direction") {
      //          val r = Rectangle(0,-179,-1,-178)
      //          val p = Point(-1,-1)
      //          r append1 p shouldBe Rectangle(0, -179, -1, -1)
      //        }
      //
      //        it("should append in order to create the smallest rectangle possible - in the eastern (positive) direction") {
      //          val r = Rectangle(1,178,-1,179)
      //          val p = Point(0,3)
      //          r append1 p shouldBe Rectangle(1, 3, -1, 179)
      //        }
      //
      //        it("should append in order to create the smallest rectangle possible - smallest circumferance") {
      //          val r = Rectangle(1,-5,-1,10)
      //          val p = Point(0,180)
      //          r append1 p shouldBe Rectangle(1, -5, -1, 180)
      //        }
      //
      //        it("should append in order to create the smallest rectangle possible - smallest circumferance (opposite)") {
      //          val r = Rectangle(1,-20,-1,10)
      //          val p = Point(0,180)
      //          r append1 p shouldBe Rectangle(1, 180, -1, 10)
      //        }
      //
      //        it("should remain the same when point is in rectangle (positive)") {
      //          val r = Rectangle(1, 176, -1, -176)
      //          val p = Point(0,179)
      //          r append1 p shouldBe r
      //        }
      //
      //        it("should remain the same when point is in rectangle (negative)") {
      //          val r = Rectangle(1, 176, -1, -176)
      //          val p = Point(0,-179)
      //          r append1 p shouldBe r
      //        }
      //
    }


  }
  describe("geotools") {

    //    it("can create a geometry") {
    //      val crs = DefaultGeographicCRS.WGS84
    //      val env = ReferencedEnvelope.create(crs)
    //
    //      println(s"env.isNull = ${env.isNull}")
    //      println(s"env.getLowerCorner = ${env.getLowerCorner}")
    //      println(s"env.getUpperCorner = ${env.getUpperCorner}")
    //      env.expandToInclude(10, 10)
    //      println(s"env.isNull = ${env.isNull}")
    //      println(s"env.getLowerCorner = ${env.getLowerCorner}")
    //      println(s"env.getUpperCorner = ${env.getUpperCorner}")
    //    }
  }
}
