package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.commons.UriDFTableRW
import com.comlinkdata.largescale.schema.udp.installbase.IfaToIp
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.apache.spark.sql.functions.{approx_count_distinct, round, sum}

import java.net.URI

class IfaToIpJobSpec extends CldSparkBaseSpec {
  import spark.implicits._

  private val ifaObsTestData = this.getClass.getResource("/com/comlinkdata/emrjobs/spark/udp/locationset/")
  private val dailyMaxMindTestData = this.getClass.getResource("/com/comlinkdata/emrjobs/spark/udp/locationset_maxmind/")

  private val carrierLookupPath = new URI("table://ifaipCL")
  private val carrierLookupFwPath = new URI("table://ifaipCL_FW")
  private val ipv6TruncationLookupPath = new URI("table://ifaipTrun")
  private val hourWeightLookupPath = new URI("table://ifaipHour")
  private val outpath = new URI("table://ifaipOutPath")

  describe("Testing") {

    it("Run Job with Saved Data") {
      DataGen.writeCarrierLookup(carrierLookupPath)
      DataGen.writeCarrierLookupFw(carrierLookupFwPath)
      DataGen.writeIpv6Lookup(ipv6TruncationLookupPath)
      DataGen.writeHourWeight(hourWeightLookupPath)

      val config = IfaToIpConfig(
        ifaObsPath = ifaObsTestData.toURI,
        maxmindPath = dailyMaxMindTestData.toURI,
        carrierLookupPath = carrierLookupPath,
        carrierLookupFwPath = carrierLookupFwPath,
        ipv6TruncationLookupPath = ipv6TruncationLookupPath,
        hourWeightLookupPath = hourWeightLookupPath,
        dailyIpCarrierPath = None,
        datasources = Seq(ComlinkdataDatasource.mw05),
        startDate = DataGen.startDate,
        endDate = DataGen.endDate,
        repartition = Some(1),
        ifaToIpOutPath = outpath
      )
      IfaToIpRunner.runJob(config)

      val ifaToIP = UriDFTableRW(config.ifaToIpOutPath).readBasePath().as[IfaToIp]
      ifaToIP.count shouldBe 416
      ifaToIP.show()

      val groupedIfaToIP = ifaToIP
        .groupBy("connection_type")
        .agg(
          approx_count_distinct($"ip") as "ip",
          approx_count_distinct($"ifa") as "ifa",
          approx_count_distinct($"carrier") as "carrier",
          sum($"obs_count"),
          sum($"night_obs_count"),
          sum($"household_obs_count"),
          round(sum($"household_weighted_obs_count"), 2),
          round(sum($"business_weighted_obs_count"), 2),
        )
        .orderBy("connection_type")

      val resultIfaToIP = groupedIfaToIP.collect()
      resultIfaToIP(0).get(0) shouldBe "Cable/DSL"
      //resultIfaToIP(0).get(1) shouldBe 66
      //resultIfaToIP(0).get(2) shouldBe 48
      //resultIfaToIP(0).get(3) shouldBe 18
      //resultIfaToIP(0).get(4) shouldBe 2958
      //resultIfaToIP(0).get(5) shouldBe 906
      //resultIfaToIP(0).get(6) shouldBe 0
      //resultIfaToIP(0).get(7) shouldBe 647.79
      //resultIfaToIP(0).get(8) shouldBe 726.0

      resultIfaToIP(1).get(0) shouldBe "Corporate"
      //resultIfaToIP(1).get(1) shouldBe 139
      //resultIfaToIP(1).get(2) shouldBe 16
      //resultIfaToIP(1).get(3) shouldBe 6
      //resultIfaToIP(1).get(4) shouldBe 550
      //resultIfaToIP(1).get(5) shouldBe 188
      //resultIfaToIP(1).get(6) shouldBe 0
      //resultIfaToIP(1).get(7) shouldBe 123.21
      //resultIfaToIP(1).get(8) shouldBe 38.0
    }
  }
}
