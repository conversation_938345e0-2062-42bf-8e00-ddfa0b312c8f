package com.comlinkdata.emrjobs.spark.udp.tier1

import com.comlinkdata.commons.testing.CldBaseSpec
import com.comlinkdata.emrjobs.spark.udp.tier1.RangeMap.Entry
import java.math.BigInteger
import scala.language.implicitConversions

class RangeMapSpec extends CldBaseSpec {
  implicit def intToBigInt(i: Int): BigInteger = BigInt(i).bigInteger

  it("should look up values correctly") {
    val map = new RangeMap(Array(
      new Entry(5, 7, "Value 2"),
      new Entry(1, 3, "Value 1")))
    map.find(0) shouldBe null
    map.find(1) shouldBe "Value 1"
    map.find(2) shouldBe "Value 1"
    map.find(3) shouldBe "Value 1"
    map.find(4) shouldBe null
    map.find(5) shouldBe "Value 2"
    map.find(6) shouldBe "Value 2"
    map.find(7) shouldBe "Value 2"
    map.find(8) shouldBe null
  }
  it("should not allow intersecting ranges") {
    val badArray1 = Array(
      new Entry(3, 7, "Value 2"),
      new Entry(1, 5, "Value 1"))
    val badArray2 = Array(
      new Entry(1, 5, "Value 1"),
      new Entry(3, 7, "Value 2"))
    val badArray3 = Array(
      new Entry(5, 7, "Value 2"),
      new Entry(1, 5, "Value 1"))
    val badArray4 = Array(
      new Entry(5, 7, "Value 2"),
      new Entry(7, 8, "Value 1"))
    assertThrows[IllegalStateException](new RangeMap(badArray1))
    assertThrows[IllegalStateException](new RangeMap(badArray2))
    assertThrows[IllegalStateException](new RangeMap(badArray3))
    assertThrows[IllegalStateException](new RangeMap(badArray4))
  }
}
