package com.comlinkdata.emrjobs.spark.udp.installbase
import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.common.generate
import com.comlinkdata.commons.testing.generators.udp.genIPHighConfidence
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.udp.installbase._

import java.time.LocalDate
import java.sql.Date

class IFALevelSummaryJoinJobSpec extends CldSparkBaseSpec  {

  val ifa: Array[Byte] = "a".map(_.toByte).toArray
  describe ("getQuarterData") {
    it ("test join")  {
      import spark.implicits._
      val tempDate = LocalDate.parse("2021-07-02")
      val currentDate = Date.valueOf("2022-01-01")
      val hc = generate(genIPHighConfidence()).map(_.copy(
          ip_min_date = currentDate,
          ip_max_date = currentDate,
          ifa_max_date = currentDate,
          global_ip_min_date = currentDate,
          year = "2021",
          month = "08",
          day = "01")).toDS()

      val output = IFALevelSummaryJoinJobRunner.getQuarterData(hc,tempDate)
      output.count() shouldBe 0
    }
  }
  describe ("getSPCarrier") {
    it ("correct schema") {
      import spark.implicits._
      val currentDate = Date.valueOf("2022-07-02")
      val hc = generate(genIPHighConfidence()).map(_.copy(
        ip_min_date = currentDate,
        ip_max_date = currentDate,
        ifa_max_date = currentDate,
        global_ip_min_date = currentDate,
        year = "2021",
        month = "08",
        day = "01")).toDS()
      val tempLookup = Seq(CarrierLookup("a","a","a",Some("a"),"a","a","a")).toDF().as[CarrierLookup]
      val correctOutput = Seq(IfaLevelSummary("a".map(_.toByte).toArray,false,"a", 10, 10,"a","a","a",1)).toDF().as[IfaLevelSummary]
      val output = IFALevelSummaryJoinJobRunner.getSPCarrier(hc,tempLookup, currentDate.toLocalDate)

      output.dtypes shouldBe correctOutput.dtypes
    }
  }
}