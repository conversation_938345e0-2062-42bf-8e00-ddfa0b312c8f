package com.comlinkdata.emrjobs.spark.udp.tier0

import com.comlinkdata.commons.testing.CldSparkBaseSpec

class CountryNameToIso3Spec extends CldSparkBaseSpec {
  val sampleUri = this.getClass.getResource("country_to_iso3_sample.csv").toURI

  import CountryNameToIso3._
  import spark.implicits._

  describe("read") {

    it("reads correct rows") {
      val expectedRows = 2
      read(sampleUri).count shouldBe expectedRows
    }

    it("should have expected content") {
      val expected = List(
        CountryNameToIso3("URY", "Uruguay"),
        CountryNameToIso3("USA", "United States")
      ).toDS
      read(sampleUri) shouldBeDs expected
    }
  }
}
