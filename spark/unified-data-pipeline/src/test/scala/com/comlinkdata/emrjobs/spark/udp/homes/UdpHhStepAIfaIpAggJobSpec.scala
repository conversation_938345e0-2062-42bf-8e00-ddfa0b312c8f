package com.comlinkdata.emrjobs.spark.udp.homes

import java.net.URI
import com.comlinkdata.commons.testing.CldSparkBaseSpec

class UdpHhStepAIfaIpAggJobSpec extends CldSparkBaseSpec {

  describe("runner integration") {

    val src = URI create "/Users/<USER>/Downloads/homes/ifa-obs-1k/part-00000-6f7407b7-7029-470f-a786-2d7db3445695-c000.snappy.parquet"
    ignore("can execute") {
      val input = spark.read.parquet(src.toString)

      //      val result = input.select($"ifa", $"observations.ip" as "observatsions.ip", $"observations.t_local" as "observations.t_local")
      //      val result =  input.as[SkinnyIfa]
      //        result.explain()
      //
      //      result.printSchema()
      //      result.show()

      //      input.printSchema()
      //      input
      //        .select("ifa", "ip", "observations.ip", "observations.t_local")
      //        .as[SkinnyIfa]
      //
      //        .flatMap { r =>
      //          r.observations
      //            .filter(obs => obs.ip.isDefined && obs.t_local.isDefined)
      //            .map(obs =>
      //              FlatIfaObs(r.ifa, obs.ip.get, obs.t_local.get)
      //            )
      //        }

      //      input.show
    }
  }
}
