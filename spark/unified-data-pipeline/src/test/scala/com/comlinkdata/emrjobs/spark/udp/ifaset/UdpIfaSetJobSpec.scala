package com.comlinkdata.emrjobs.spark.udp.ifaset

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.common.generate
import com.comlinkdata.commons.testing.generators.udp.{genUdpDailyDevice, genIfaSet, genUdpDailyIfa}
import com.comlinkdata.largescale.commons.UriDFTableRW
import com.comlinkdata.largescale.schema.udp.lookup.{DeviceTypeLookup, MccMncLookupTable}
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.tier1.{UdpDailyDevice, UdpDailyIfa}
import com.comlinkdata.largescale.schema.udp.tier2.IfaSet
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.junit.rules.TemporaryFolder
import org.scalacheck.Gen

import java.sql.Date
import java.time.LocalDate

case class JustIfaData(ifaStr: String)

case class IfaStrBin(ifaStr: String, ifa: Ifa, prefix: Option[Array[Byte]])

class UdpIfaSetJobSpec extends CldSparkBaseSpec {

  import spark.implicits._

  val ifas: Array[Ifa] = (32 to 62).map(x => Array(x.toByte)).toArray[Array[Byte]]

  describe("full run") {
    val startDate = LocalDate.of(2023, 1, 2)
    val datasources = Seq("mw05", "mw03")
    val datasource = ComlinkdataDatasource.mw05

    val udpDailyDeviceLocationData: Seq[UdpDailyDevice] = datasources.flatMap { ds =>
      generate(1, genUdpDailyDevice(
        ifas = Gen.oneOf(Seq(ifas(5))),
        dates = Gen.oneOf(Seq(Date.valueOf(startDate))),
      )).map(_.copy(ds = ds))
    }

    val ifaObsData: Seq[UdpDailyIfa] = datasources.flatMap { ds =>
      generate(1, genUdpDailyIfa(
        ifas = Gen.oneOf(Seq(ifas(5))),
        dates = Gen.oneOf(Seq(Date.valueOf(startDate))),
      )).map(_.copy(ds = ds))
    }

    val ifaSetData: Seq[IfaSet] = datasources.flatMap { ds =>
      generate(1, genIfaSet(
        ifas = Gen.oneOf(Seq(ifas(5))),
        dates = Gen.oneOf(Seq(Date.valueOf(startDate.minusDays(1)))),
      )).map(_.copy(ds = ds))
    }

    val mccMncData = Seq(
      MccMncLookupTable("mcc_mnc", "carrier", None)
    )

    val deviceTypes = Seq(
      DeviceTypeLookup("a", "b")
    )

    it("with parquet") {
      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI

      val config: UdpIfaSetJobConfig = UdpIfaSetJobConfig(
        datasource = datasource.toString,
        dailyDeviceLoc = basePath.resolve("ifa_set_dailyDeviceLoc"),
        mccMncLookupPath = basePath.resolve("ifa_set_mccMncLookupPath"),
        ifaSetSourceLoc = basePath.resolve("ifa_set_ifaSetSourceLoc"),
        ifaSetDestLoc = basePath.resolve("ifa_set_ifaSetDestLoc"),
        deviceTypeLookupLoc = basePath.resolve("ifa_set_deviceTypeModalModelLookupLoc"),
        bootstrapDateOpt = None,
        maxDaysToRun = 1
      )

      UriDFTableRW(config.dailyDeviceLoc).writeDsPartitionBy(udpDailyDeviceLocationData.toDS(), Seq("ds", "year", "month", "day"))
      UriDFTableRW(config.ifaSetSourceLoc).writeDsPartitionBy(ifaSetData.toDS(), Seq("ds", "year", "month", "day"))
      UriDFTableRW(config.mccMncLookupPath).writeDs(mccMncData.toDS())
      UriDFTableRW(config.deviceTypeLookupLoc).writeDs(deviceTypes.toDS())

      UdpIfaSetRunner.runJob(config)
      val result = IfaSet.read(config.ifaSetDestLoc, startDate, Seq(ComlinkdataDatasource.mw05))
      folder.delete()
      result.count() shouldBe 1
    }
  }
  describe("Gamoshi filtering") {
    val startDate = Date.valueOf("2023-01-05")
    val datasource = ComlinkdataDatasource.gamoshi

    val gamoshiIfaSetData: Seq[IfaSet] = Seq(
      IfaSet(
        ds = "gamoshi",
        year = "2023",
        month = "01",
        day = "04",
        ifa = ifas(5),
        days = IndexedSeq(Date.valueOf("2022-10-22")), // Older than 70 days
        apps = Map.empty,
        device_type = None,
        final_carrier_name = None,
        latest_carrier_name = None,
        iphone_disambiguated_model = None,
        modal_model_code = None,
        carrier_freqs = Map.empty,
        mcc_mnc_freqs = Map.empty,
        model_code_freqs = Map.empty,
        user_agent_string_freqs = Map.empty
      ),
      IfaSet(
        ds = "gamoshi",
        year = "2023",
        month = "01",
        day = "04",
        ifa = ifas(6),
        days = IndexedSeq(Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")), // Within 70 days
        apps = Map.empty,
        device_type = None,
        final_carrier_name = None,
        latest_carrier_name = None,
        iphone_disambiguated_model = None,
        modal_model_code = None,
        carrier_freqs = Map.empty,
        mcc_mnc_freqs = Map.empty,
        model_code_freqs = Map.empty,
        user_agent_string_freqs = Map.empty
      ),
      IfaSet(
        ds = "gamoshi",
        year = "2023",
        month = "01",
        day = "04",
        ifa = ifas(4),
        days = IndexedSeq(Date.valueOf("2023-01-01")), // Within 70 days
        apps = Map.empty,
        device_type = None,
        final_carrier_name = None,
        latest_carrier_name = None,
        iphone_disambiguated_model = None,
        modal_model_code = None,
        carrier_freqs = Map.empty,
        mcc_mnc_freqs = Map.empty,
        model_code_freqs = Map.empty,
        user_agent_string_freqs = Map.empty
      ),
      IfaSet(
        ds = "gamoshi",
        year = "2023",
        month = "01",
        day = "04",
        ifa = ifas(3),
        days = IndexedSeq(Date.valueOf("2022-01-01"), Date.valueOf("2022-05-01")), // Within 70 days
        apps = Map.empty,
        device_type = None,
        final_carrier_name = None,
        latest_carrier_name = None,
        iphone_disambiguated_model = None,
        modal_model_code = None,
        carrier_freqs = Map.empty,
        mcc_mnc_freqs = Map.empty,
        model_code_freqs = Map.empty,
        user_agent_string_freqs = Map.empty
      )
    )
    val udpDailyDeviceLocationData: Seq[UdpDailyDevice] =
      generate(2, genUdpDailyDevice(
        ifas = Gen.oneOf(Seq(ifas(4))),
        dates = Gen.oneOf(Seq(Date.valueOf(startDate.toLocalDate))),
      )).map(_.copy(ds = datasource.toString))


    val mccMncData = Seq(
      MccMncLookupTable("mcc_mnc", "carrier", None)
    )

    val deviceTypes = Seq(
      DeviceTypeLookup("a", "b")
    )

    it("filters Gamoshi dataset correctly") {
      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI

      val config: UdpIfaSetJobConfig = UdpIfaSetJobConfig(
        datasource = datasource.toString,
        dailyDeviceLoc = basePath.resolve("ifa_set_dailyDeviceLoc"),
        mccMncLookupPath = basePath.resolve("ifa_set_mccMncLookupPath"),
        ifaSetSourceLoc = basePath.resolve("ifa_set_ifaSetSourceLoc"),
        ifaSetDestLoc = basePath.resolve("ifa_set_ifaSetDestLoc"),
        deviceTypeLookupLoc = basePath.resolve("ifa_set_deviceTypeModalModelLookupLoc"),
        bootstrapDateOpt = None,
        maxDaysToRun = 1
      )

      UriDFTableRW(config.dailyDeviceLoc).writeDsPartitionBy(udpDailyDeviceLocationData.toDS(), Seq("ds", "year", "month", "day"))
      UriDFTableRW(config.ifaSetSourceLoc).writeDsPartitionBy(gamoshiIfaSetData.toDS(), Seq("ds", "year", "month", "day"))
      UriDFTableRW(config.mccMncLookupPath).writeDs(mccMncData.toDS())
      UriDFTableRW(config.deviceTypeLookupLoc).writeDs(deviceTypes.toDS())

      UdpIfaSetRunner.runJob(config)
      val result = IfaSet.read(config.ifaSetDestLoc, startDate.toLocalDate, Seq(ComlinkdataDatasource.gamoshi))
      folder.delete()
      result.count() shouldBe 3

    }
  }
}
