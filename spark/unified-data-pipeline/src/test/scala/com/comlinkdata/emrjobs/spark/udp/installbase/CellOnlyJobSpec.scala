package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.common.generate
import com.comlinkdata.commons.testing.generators.udp.genIPHighConfidence
import com.comlinkdata.emrjobs.spark.udp.installbase.CellOnlyRunner.{antiJoinHc, cellOnlyIfas}
import com.comlinkdata.emrjobs.spark.udp.installbase.prefilter.{MaxMindOrProxy, IfaObsWithMM}
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, UriDFTableRW, LocalDateRange}
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.comlinkdata.largescale.schema.udp.installbase.IPHighConfidence
import com.comlinkdata.largescale.schema.udp.location.{LocationMap, Point}
import com.comlinkdata.largescale.schema.udp.location.LocationStat.LocationStatsMap
import com.comlinkdata.largescale.schema.udp.lookup.{ipv6TruncateLookup, IPv6Lookup}
import com.comlinkdata.largescale.schema.udp.tier1.{UdpDailyIfaNoYmd, UdpDailyMaxmindNoYmd, UdpIfaObservation}
import com.comlinkdata.largescale.udp._
import org.apache.sedona.sql.utils.SedonaSQLRegistrator
import org.scalatest.BeforeAndAfterAll
import org.apache.spark.sql.functions._
import org.junit.rules.TemporaryFolder

import java.net.URI
import java.sql.{Timestamp, Date}
import java.time.LocalDate

case class UdpIfaObservationWithIfas(
  partition_date: Date,
  ifa: Ifa,
  t_utc: Timestamp,
  t_local: Option[Timestamp],
  ip: Option[Ip],
  loc_lat: Float,
  loc_lng: Float,
  location_type: String,
  connection_type: Option[String],
  accuracy: Option[Float],
  gps_speed: Option[Float],
  place_id: Option[Long],
  observation_count: Int,
  unique_ip_count: Int
)

class CellOnlyJobSpec extends CldSparkBaseSpec with BeforeAndAfterAll {
  import spark.implicits._

  private val highConfidence = URI.create("table://hcCellOnly")
  private val ipv6TruncationLookupPath: URI = new URI("table://ipv6LutCo")
  private val ifaObsTestTable: URI = new URI("table://ifaObsCo")
  private val maxmindTestTable: URI = new URI("table://maxmindCo/")
  private val outTable: URI = new URI("table://coFullRunOut")

  private val polygons = this.getClass.getResource("/com/comlinkdata/emrjobs/spark/udp/polygons").toURI

  private val startDate = LocalDate.of(2023, 6, 24)
  private val hcDay = startDate.plusDays(14)
  private val hcPrevDay = LocalDate.of(2023, 6, 10)

  private val ifas: Array[Ifa] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]
  private val ips: Array[Ip] = (32 to 62).map( x => Array(192.toByte, 168.toByte, 0.toByte, x.toByte)).toArray[Array[Byte]]
  private val hhid: Array[Array[Byte]] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]

  override def beforeAll(): Unit = {
    super.beforeAll()
    SedonaSQLRegistrator.registerAll(spark)

    val hcDf = Seq(
      (ifas(1), ips(1), hhid(0), hcDay),
      (ifas(2), ips(2), hhid(0), hcDay),
      (ifas(3), ips(3), hhid(0), hcDay),
      (ifas(4), ips(4), hhid(0), hcDay),
      (ifas(5), ips(5), hhid(0), hcDay),
      (ifas(5), ips(5), hhid(0), hcDay),
    ).flatMap( i => generate(genIPHighConfidence()).map(_.copy(
      ifa = i._1,
      ip = i._2,
      household_id = i._3,
      ip_min_date = i._4.toDate,
      ip_max_date = i._4.plusDays(2).toDate,
      year = i._4.getYearString,
      month = i._4.getMonthValueString,
      day = i._4.getDayOfMonthString)))
      .toDS()
      .drop("year", "month", "day")

    UriDFTableRW.fromStr(TimeSeriesLocation
      .ofYmdDatePartitions(highConfidence)
      .build.partition(hcDay)).write(hcDf.toDF())

    val hcPrev = Seq(
      (ifas(6), ips(1), hhid(0), hcPrevDay),
      (ifas(2), ips(2), hhid(0), hcPrevDay),
    ).flatMap( i => generate(genIPHighConfidence()).map(_.copy(
      ifa = i._1,
      ip = i._2,
      household_id = i._3,
      ip_min_date = i._4.toDate,
      ip_max_date = i._4.plusDays(2).toDate,
      year = i._4.getYearString,
      month = i._4.getMonthValueString,
      day = i._4.getDayOfMonthString)))
      .toDS()
      .drop("year", "month", "day")

    UriDFTableRW.fromStr(TimeSeriesLocation
      .ofYmdDatePartitions(highConfidence)
      .build.partition(hcPrevDay)).write(hcPrev.toDF())

    val ipv6LUT = dataset[ipv6TruncateLookup]{
      s"""
         |raw_carrier_name      |consolidated_carrier  |min_right_zero_bytes_when_truncated|
         |Fake                  |Fake                  |10                                 |
         |AT&T Services         |AT&T Internet Services|10                                 |
         |CenturyLink           |CenturyLink           |10                                 |
         |Comcast Cable         |Comcast               |10                                 |
         |Cox Communications    |Cox Communications    |10                                 |
         |GCI Communications    |General Communications|10                                 |
         |Home Telephone Company|Home Telephone Company|10                                 |
         |Mediacom Cable        |Mediacom Cable        |10                                 |
         |Zona Communications   |Zona Communications   |10                                 |
         |Optimum Online        |Altice                |10                                 |
         |Giggle Fiber          |Giggle Fiber          |10                                 |
         |UBTANET               |UBTANET               |10                                 |
         """
    }
    UriDFTableRW(ipv6TruncationLookupPath).write(ipv6LUT.toDF())

    val toLocStat = udf[LocationStatsMap, Seq[UdpIfaObservation]](LocationMap.fromIfaObservations)
    spark.sqlContext.udf.register("Point", (x: Float, y: Float) => Point(x, y))

    for(day <- LocalDateRange.of(startDateInclusive = startDate, endDateInclusive = hcDay)) {
      val mm = dataset[UdpDailyMaxmindNoYmd] {
        s"""
           |ip      |carrier           |connection_type|organization      |autonomous_system_number|autonomous_system_organization|
           |rDgfAA==|Zenlayer          |Cellular       |cell1             |1                       |null                          |
           |rDpvnA==|Zenlayer          |Cellular       |cell2             |1                       |null                          |
           |rDge7w==|Spectrum          |Cellular       |cell4             |1                       |null                          |
           |rDptVQ==|Zenlayer          |Cellular       |cell1             |1                       |null                          |
           |prVTAA==|Cox Communications|Cable/DSL      |Cox Communications|1                       |null                          |
           |rDpt7g==|Zenlayer          |Cellular       |cell1             |1                       |null                          |
           |rDpt7Q==|Zenlayer          |Cellular       |cell2             |1                       |null                          |
           |a03fAA==|Zenlayer          |Cellular       |cell1             |1                       |null                          |
           |prVRWw==|Spectrum          |Cellular       |cell4             |1                       |null                          |
           |rDpt/Q==|Zenlayer          |Cellular       |cell1             |1                       |null                          |
           |prVTQQ==|Cox Communications|Cable/DSL      |Cox Communications|1                       |null                          |"""}

      UriDFTableRW.fromStr(TimeSeriesLocation
        .ofYmdDatePartitions(maxmindTestTable)
        .withPartition(ComlinkdataDatasource.mw05)
        .withPartition("ipversion", 4)
        .build.partition(day)).write(mm.toDF())
      if (day.isEqual(ComlinkdataDatasource.mw07.firstDate) || day.isAfter(ComlinkdataDatasource.mw07.firstDate)) {
        UriDFTableRW.fromStr(TimeSeriesLocation
          .ofYmdDatePartitions(maxmindTestTable)
          .withPartition(ComlinkdataDatasource.mw07)
          .withPartition("ipversion", 4)
          .build.partition(day)).write(mm.toDF())
      }

      val ifaObsExploded = dataset[UdpIfaObservationWithIfas] {
        s"""
           |partition_date|ifa                     |t_utc                   |t_local                 |ip      |loc_lat |loc_lng  |location_type|connection_type|accuracy|gps_speed|place_id|observation_count|unique_ip_count|
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T18:02:26.854Z|${day}T12:02:26.854Z|prVTQQ==|43.03820|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T16:10:04.648Z|${day}T10:10:04.648Z|prVRWw==|41.60210|-93.61240|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T18:18:12.336Z|${day}T12:18:12.336Z|prVTAA==|43.04000|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T18:25:59.006Z|${day}T12:25:59.006Z|prVTAA==|43.04000|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T18:26:32.154Z|${day}T12:26:32.154Z|prVTQQ==|43.03820|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T18:44:40.321Z|${day}T12:44:40.321Z|prVTQQ==|43.03820|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T07:07:02.257Z|${day}T01:07:02.257Z|prVTAA==|43.04000|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T07:07:06.814Z|${day}T01:07:06.814Z|prVTQQ==|43.03820|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T07:44:42.323Z|${day}T01:44:42.323Z|prVTAA==|43.04000|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T07:44:42.811Z|${day}T01:44:42.811Z|prVTQQ==|43.03820|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T08:00:03.048Z|${day}T02:00:03.048Z|prVTAA==|43.04000|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T11:50:18.461Z|${day}T05:50:18.461Z|prVTQQ==|43.03820|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T11:50:19.088Z|${day}T05:50:19.088Z|prVTAA==|43.04000|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T13:48:16.586Z|${day}T07:48:16.586Z|prZWAA==|35.01000|-97.09000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T18:02:26.854Z|${day}T12:02:26.854Z|prVTQQ==|43.03820|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T16:10:04.648Z|${day}T10:10:04.648Z|prVRWw==|41.60210|-93.61240|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T18:18:12.336Z|${day}T12:18:12.336Z|prVTAA==|43.04000|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T18:25:59.006Z|${day}T12:25:59.006Z|prVTAA==|43.04000|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T18:26:32.154Z|${day}T12:26:32.154Z|prVTQQ==|43.03820|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T18:44:40.321Z|${day}T12:44:40.321Z|prVTQQ==|43.03820|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T07:07:02.257Z|${day}T01:07:02.257Z|prVTAA==|43.04000|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T07:07:06.814Z|${day}T01:07:06.814Z|prVTQQ==|43.03820|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T07:44:42.323Z|${day}T01:44:42.323Z|prVTAA==|43.04000|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T07:44:42.811Z|${day}T01:44:42.811Z|prVTQQ==|43.03820|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T08:00:03.048Z|${day}T02:00:03.048Z|prVTAA==|43.04000|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T11:50:18.461Z|${day}T05:50:18.461Z|prVTQQ==|43.03820|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T11:50:19.088Z|${day}T05:50:19.088Z|prVTAA==|43.04000|-87.91000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T13:48:16.586Z|${day}T07:48:16.586Z|prZWAA==|35.01000|-97.09000|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T13:48:17.441Z|${day}T07:48:17.441Z|prZWYg==|37.75100|-97.82200|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AAdBO+1TRLySFW/xdjPcsw==|${day}T13:50:37.448Z|${day}T07:50:37.448Z|prZWYg==|41.87810|-87.62980|GPS          |CELLULAR       |null    |null     |null    |29               |5              |
           |${day}    |AA9NRqF1QLmmZVZ0s7WChQ==|${day}T05:21:33.453Z|${day}T03:21:33.453Z|prVTQQ==|40.71001|-74.01000|GPS          |CELLULAR       |null    |null     |null    |3                |1              |
           |${day}    |AA9NRqF1QLmmZVZ0s7WChQ==|${day}T05:21:33.453Z|${day}T06:21:33.453Z|a03fAA==|40.71005|-74.01000|GPS          |CELLULAR       |null    |null     |null    |3                |1              |
           |${day}    |AA9NRqF1QLmmZVZ0s7WChQ==|${day}T05:21:33.453Z|${day}T07:21:33.453Z|a03fAA==|40.71005|-74.01000|GPS          |CELLULAR       |null    |null     |null    |3                |1              |
           |${day}    |AA9NRqF1QLmmZVZ0s7WChQ==|${day}T05:21:33.453Z|${day}T08:21:33.453Z|a03fAA==|40.71005|-74.01000|GPS          |CELLULAR       |null    |null     |null    |3                |1              |
           |${day}    |AA9NRqF1QLmmZVZ0s7WChQ==|${day}T05:21:33.453Z|${day}T09:21:33.453Z|a03fAA==|40.71005|-74.01000|GPS          |CELLULAR       |null    |null     |null    |3                |1              |
           |${day}    |AA9NRqF1QLmmZVZ0s7WChQ==|${day}T06:03:01.319Z|${day}T02:03:01.319Z|a03fAA==|40.71002|-74.01000|GPS          |CELLULAR       |null    |null     |null    |3                |1              |
           |${day}    |AA9NRqF1QLmmZVZ0s7WChQ==|${day}T07:04:01.331Z|${day}T05:04:01.331Z|a03fAA==|40.71003|-74.01000|GPS          |CELLULAR       |null    |null     |null    |3                |1              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T18:28:30.294Z|${day}T12:28:30.294Z|rDptVQ==|32.77670|-96.80500|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T04:22:06.402Z|${day}T22:22:06.402Z|rDpvnA==|32.77670|-96.80500|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T05:01:59.971Z|${day}T23:01:59.971Z|rDpvAA==|32.78000|-96.80000|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T05:02:10.909Z|${day}T23:02:10.909Z|rDpvnA==|32.77670|-96.80500|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T05:56:05.092Z|${day}T23:56:05.092Z|rDpvnA==|32.77670|-96.80500|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T07:02:54.853Z|${day}T01:02:54.853Z|rDpvnA==|32.77670|-96.80500|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T08:07:19.693Z|${day}T02:07:19.693Z|rDpt7Q==|32.77670|-96.80500|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T09:58:36.104Z|${day}T03:58:36.104Z|rDpt/Q==|32.77670|-96.80500|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T10:02:37.460Z|${day}T04:02:37.460Z|rDpt/Q==|32.77670|-96.80500|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T14:57:05.331Z|${day}T08:57:05.331Z|rDpt7g==|32.77670|-96.80500|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T15:06:10.970Z|${day}T09:06:10.970Z|rDpt7g==|32.77670|-96.80500|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T15:06:10.970Z|${day}T09:06:10.970Z|rDpt7g==|32.77670|-96.80500|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ABsEj2U9RfaB8T3oEEvBAQ==|${day}T00:06:10.970Z|${day.plusDays(1)}T23:06:10.970Z|rDpt7g==|32.77670|-96.80500|GPS          |CELLULAR       |null    |null     |null    |29               |8              |
           |${day}    |ADUg6tK8STm1K3CLK/lI4w==|${day}T16:03:55.861Z|${day}T08:03:55.861Z|rDgfAA==|34.05000|-118.24000|GPS         |CELLULAR       |null    |null     |null    |8                |5              |
           |${day}    |ADUg6tK8STm1K3CLK/lI4w==|${day}T16:04:00.541Z|${day}T08:04:00.541Z|rDgfFg==|34.55698|-118.07717|GPS         |CELLULAR       |null    |null     |null    |8                |5              |
           |${day}    |ADUg6tK8STm1K3CLK/lI4w==|${day}T16:04:18.730Z|${day}T08:04:18.730Z|rDgfFg==|34.55691|-118.07719|GPS         |CELLULAR       |null    |null     |null    |8                |5              |
           |${day}    |ADUg6tK8STm1K3CLK/lI4w==|${day}T16:17:25.425Z|${day}T08:17:25.425Z|rDgfNw==|34.55689|-118.07726|GPS         |CELLULAR       |null    |null     |null    |8                |5              |
           |${day}    |ADUg6tK8STm1K3CLK/lI4w==|${day}T16:17:28.842Z|${day}T08:17:28.842Z|rDgfNw==|34.55695|-118.07719|GPS         |CABLE          |null    |null     |null    |8                |5              |
           |${day}    |ADUg6tK8STm1K3CLK/lI4w==|${day}T17:06:08.492Z|${day}T09:06:08.492Z|rDgeAA==|32.72000|-117.16000|GPS         |CABLE          |null    |null     |null    |8                |5              |
           |${day}    |ADUg6tK8STm1K3CLK/lI4w==|${day}T17:06:09.608Z|${day}T09:06:09.608Z|rDge7w==|34.55710|-118.07681|GPS         |CABLE          |null    |null     |null    |8                |5              |
           |${day}    |ADUg6tK8STm1K3CLK/lI4w==|${day}T17:06:27.205Z|${day}T09:06:27.205Z|rDge7w==|34.55765|-118.07673|GPS         |CABLE          |null    |null     |null    |8                |5              |"""
      }

      val ifaObs = ifaObsExploded.selectExpr(
        "partition_date",
        "ifa",
        "t_utc",
        "t_local",
        "ip",
        "Point(loc_lat, loc_lng) as location",
        "location_type",
        "connection_type",
        "accuracy",
        "gps_speed",
        "place_id",
        "observation_count",
        "unique_ip_count"
      ).groupBy(
        "partition_date",
        "ifa",
        "observation_count",
        "unique_ip_count"
      ).agg(
        collect_list(
          struct("t_utc",
            "t_local",
            "ip",
            "location",
            "location_type",
            "connection_type",
            "accuracy",
            "gps_speed",
            "place_id")) as "observations")
        .withColumn("location_stats", toLocStat($"observations"))
        .as[UdpDailyIfaNoYmd]

      UriDFTableRW.fromStr(
        TimeSeriesLocation
          .ofYmdDatePartitions(ifaObsTestTable)
          .withPartition(ComlinkdataDatasource.mw05)
          .build.partition(day)
      ).write(ifaObs.toDF())
      if (day.isEqual(ComlinkdataDatasource.mw07.firstDate) || day.isAfter(ComlinkdataDatasource.mw07.firstDate)) {
        UriDFTableRW.fromStr(
          TimeSeriesLocation
            .ofYmdDatePartitions(ifaObsTestTable)
            .withPartition(ComlinkdataDatasource.mw07)
            .build.partition(day)
        ).write(ifaObs.toDF())
      }
    }
  }

  describe("Cell Only Tests") {
    it("Test Loading") {
      val hcOut = CellOnlyRunner.readHC(highConfidence, hcDay)
      hcOut.count shouldBe 6
      hcOut.columns.length shouldBe (IPHighConfidence.cols.length - 3) // ignore year month day

      val lut = IPv6Lookup.read(ipv6TruncationLookupPath)
      lut.select($"index").collect()(0).get(0) shouldBe 10 //Get any of them

      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI
      val carrierLookupPath = basePath.resolve("cell_only_carrier_lookup")
      val carrierLookupFwPath = basePath.resolve("cell_only_carrier_lookup_fw")
      val carrierLookup = dataset[CarrierLookup] {
        s"""
           |mw_carrier            |consolidated_carrier  |consolidated_id|service_territory_name  |sp  |sp_platform|min_date  |
           |cell1                 |fake1                 |10061          |AT&T Inc.               |398 |390        |2016-11-01|
           |cell2                 |fake2                 |10062          |AT&T Inc.               |398 |391        |2016-11-01|
           |cell4                 |fake3                 |10063          |CenturyLink, Inc.       |858 |852        |2016-11-01|
           """
      }
      val carrierLookupFw = dataset[CarrierLookup] {
        s"""
           |mw_carrier            |consolidated_carrier  |consolidated_id|service_territory_name  |sp  |sp_platform|min_date  |
           |Cox Communications    |Cox Communications    |10031          |Cox Communications, Inc.|1170|1170       |2016-11-01|"""
      }
      UriDFTableRW(carrierLookupPath).write(carrierLookup.toDF())
      UriDFTableRW(carrierLookupFwPath).write(carrierLookupFw.toDF())

      val mmOrProxy = new MaxMindOrProxy.CellOnly(
        ComlinkdataDatasource.mw05,
        maxmindTestTable,
        carrierLookupPath,
        carrierLookupFwPath,
        None
      )

      val ifaObsWithMM = new IfaObsWithMM(ifaObsTestTable, ipv6TruncationLookupPath, mmOrProxy)

      val ifaObsWmm = ifaObsWithMM.read(startDate)

      val ifaRows = antiJoinHc(hcOut, ifaObsWmm)
      ifaRows.count shouldBe 29

      val cellOnly = cellOnlyIfas(ifaRows, hcDay, 0.85f, 3)
      cellOnly.count shouldNot be(0)
      folder.delete()
    }
    
    it("Full Run") {
      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI

      val config = CellOnlyConfig(
        ifaObsPath = ifaObsTestTable,
        maxmindPath = maxmindTestTable,
        highConfPath = highConfidence,
        carrierLookupPath = basePath.resolve("cell_only_carrier_lookup2"),
        carrierLookupFwPath = basePath.resolve("cell_only_carrier_lookup2_fw"),
        polygonsLocation = polygons,
        ipv6TruncationLookupPath = ipv6TruncationLookupPath,
        hcDate = hcDay,
        hcPrevDate = Some(hcPrevDay),
        datasources = Seq(ComlinkdataDatasource.mw05, ComlinkdataDatasource.mw07),
        outPath = outTable,
        dailyIpCarrierPath = None
      )

      val carrierLookup = dataset[CarrierLookup] {
        s"""
           |mw_carrier            |consolidated_carrier  |consolidated_id|service_territory_name  |sp  |sp_platform|min_date  |
           |cell1                 |fake1                 |10061          |AT&T Inc.               |398 |390        |2016-11-01|
           |cell2                 |fake2                 |10062          |AT&T Inc.               |398 |391        |2016-11-01|
           |cell4                 |fake3                 |10063          |CenturyLink, Inc.       |858 |852        |2016-11-01|
           """
      }
      val carrierLookupFw = dataset[CarrierLookup] {
        s"""
           |mw_carrier            |consolidated_carrier  |consolidated_id|service_territory_name  |sp  |sp_platform|min_date  |
           |Cox Communications    |Cox Communications    |10031          |Cox Communications, Inc.|1170|1170       |2016-11-01|"""
      }
      UriDFTableRW(config.carrierLookupPath).write(carrierLookup.toDF())
      UriDFTableRW(config.carrierLookupFwPath).write(carrierLookupFw.toDF())

      CellOnlyRunner.runJob(config)

      val outTSL = TimeSeriesLocation
        .ofYmdDatePartitions(outTable)
        .build

      val tableOut = UriDFTableRW.fromStr(outTSL.partition(hcDay)).read().orderBy($"ifa")

      // tableOut.show(false)
      tableOut.select("census_block_id").collect().take(1).toSeq.head.get(0) shouldBe "530019502003067"
    }
  }
}
