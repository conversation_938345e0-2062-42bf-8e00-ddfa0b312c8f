package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.common.generate
import com.comlinkdata.commons.testing.generators.udp.{genIfa<PERSON>gg, genUdpIfaIpAggY<PERSON>}
import com.comlinkdata.emrjobs.spark.udp.installbase.IfaWirelessCarrierJobRunner._
import com.comlinkdata.largescale.schema.udp.{tier2, Ifa, Ip}
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.Utils
import com.comlinkdata.largescale.commons.customFunctions.addDateColFromYMD
import com.comlinkdata.largescale.schema.udp.installbase.IfaWirelessCarrierYMD
import com.comlinkdata.largescale.schema.udp.lookup.{MccMncLookupTable, WirelessCarrierIpBlock, SingleNetworkBrand, AllowedNetworkBrandPair}
import com.comlinkdata.largescale.schema.udp.tier2.{Udp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>tat}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource.mw05
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.functions._
import org.junit.rules.TemporaryFolder
import org.scalacheck.Gen

import java.sql.Date
import java.time.LocalDate

class IfaWirelessCarrierJobSpec extends CldSparkBaseSpec  with LazyLogging {

  val ifas: Array[Ifa] = (32 to 62).map(x => Array(x.toByte)).toArray[Array[Byte]]
  val ips: Array[Ip] = (32 to 62).map(x => Array(174.toByte, 254.toByte, 0.toByte, x.toByte)).toArray[Array[Byte]]
  val hhids: Array[Array[Byte]] = (32 to 62).map(x => Array(x.toByte)).toArray[Array[Byte]]

  val ipBlocksLocation = this.getClass().getResource("ifa_wireless_carrier/ip_range/ip_range.csv").toURI

  val startDate: LocalDate = LocalDate.of(2022, 1, 1)
  val endDate: LocalDate = LocalDate.of(2022, 3, 31) // process date

  describe("Full run") {

    //quarterly
    val ifaIpAgg: Seq[UdpIfaIpAggYMD] = (startDate to endDate).flatMap { day =>
      generate(genUdpIfaIpAggYMD(
        ifas = Gen.oneOf(Seq(ifas(0))),
        dates = Gen.oneOf(Seq(day.toDate)),
        carriers = Gen.oneOf(Seq("Verizon Wireless")),
        ips = Gen.oneOf(ips)
      ))
    }.toSeq

    //Single snapshot
    val dataIfaAgg: Seq[IfaAgg] = generate(genIfaAgg(
        ifas = Gen.oneOf(Seq(ifas(0))),
        dates = Gen.oneOf(Seq(startDate.toDate)),
        carriers = Gen.oneOf(Seq("Verizon Wireless"))))

    //all
    val dataMccMncLookup: Seq[MccMncLookupTable] = Seq(
      MccMncLookupTable("Verizon Wireless", "Verizon Wireless", None) // Major brands will have empty / null values for brands
    )

    val dataSingleNetworkBrands: Seq[SingleNetworkBrand] = Seq(
      SingleNetworkBrand("Verizon Wireless", "Spectrum Wireless")
    )

    val dataAllowedNetworkBrandPair: Seq[AllowedNetworkBrandPair] = Seq(
      AllowedNetworkBrandPair("Verizon Wireless", "Probable Verizon MVNO", "Verizon Wireless")
    )

    it("with parquet") {
      import spark.implicits._
      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI

      val config = IfaWirelessCarrierConfig(
        ifaIpAggLocation = basePath.resolve("ifa_wireless_carrier_ifaIpAggLocation"),
        ifaAggLocation = basePath.resolve("ifa_wireless_carrier_ifaAggLocation"),
        mccMncLookupLocation = basePath.resolve("ifa_wireless_carrier_mccMncLookupLocation"),
        wirelessCarrierIpBlockLookupLocation = ipBlocksLocation, // this is a csv and easy to lookup
        singleNetworkBrandLocation = basePath.resolve("ifa_wireless_carrier_singleNetworkBrandLocation"),
        allowedNetworkBrandPairLocation = basePath.resolve("ifa_wireless_carrier_allowedNetworkBrandPairLocation"),
        startDate = startDate,
        endDate = endDate,
        datasources = Seq(mw05),
        outputLocation = basePath.resolve("ifa_wireless_carrier_outputLocation"),
        outputPartitions = Some(10)
      )

      ifaIpAgg.toDS()
        .withColumn("ds", lit("mw05"))
        .write
        .partitionBy("ds", "year", "month", "day")
        .parquet(config.ifaIpAggLocation.toString)

      dataIfaAgg.toDS()
        .transform(addDateColFromYMD())
        .write
        .partitionBy("date")
        .parquet(config.ifaAggLocation.toString)

      dataMccMncLookup.toDS().write.parquet(config.mccMncLookupLocation.toString)
      dataSingleNetworkBrands.toDS().write.parquet(config.singleNetworkBrandLocation.toString)
      dataAllowedNetworkBrandPair.toDS().write.parquet(config.allowedNetworkBrandPairLocation.toString)
      IfaWirelessCarrierJobRunner.runJob(config)

      val output = IfaWirelessCarrierYMD.read(config.outputLocation, config.endDate)
      output.count()
      folder.delete()
    }
  }

  describe("filtering ifa agg"){
    it("filter out ifaAgg not between processing days") {
      import spark.implicits._
      val dsIfaAgg: Dataset[IfaAgg] = generate(genIfaAgg(
        ifas = Gen.oneOf(Seq(ifas(0))),
        dates = Gen.oneOf(Seq(startDate.toDate)),
        carriers = Gen.oneOf(Seq("Verizon Wireless"))
      )).map(_.copy(
        days = IndexedSeq[Date](
          startDate.minusDays(1).toDate,
          endDate.plusDays(1).toDate)
      )).toDS()
      
      val out = dsIfaAgg.transform(filterIfaAgg(startDate, endDate))
      out.count() shouldBe 0
    }

    it("keep ifaAgg on startDate") {
      import spark.implicits._
      val dsIfaAgg: Dataset[IfaAgg] = generate(genIfaAgg(
        ifas = Gen.oneOf(Seq(ifas(0))),
        dates = Gen.oneOf(Seq(startDate.toDate)),
        carriers = Gen.oneOf(Seq("Verizon Wireless"))
      )).map(_.copy(
        days = IndexedSeq[Date](startDate.toDate)
      )).toDS()
      
      val out = dsIfaAgg.transform(filterIfaAgg(startDate, endDate))
      out.count() shouldBe 1
    }

    it("keep ifaAgg on endDate") {
      import spark.implicits._
      val dsIfaAgg: Dataset[IfaAgg] = generate(genIfaAgg(
        ifas = Gen.oneOf(Seq(ifas(0))),
        dates = Gen.oneOf(Seq(endDate.toDate)),
        carriers = Gen.oneOf(Seq("Verizon Wireless"))
      )).map(_.copy(
        days = IndexedSeq[Date](endDate.toDate)
      )).toDS()
      
      val out = dsIfaAgg.transform(filterIfaAgg(startDate, endDate))
      out.count() shouldBe 1
    }

    it("keep ifaAgg between start and end") {
      import spark.implicits._
      val d = startDate.plusDays(5).toDate
      val dsIfaAgg: Dataset[IfaAgg] = generate(genIfaAgg(
        ifas = Gen.oneOf(Seq(ifas(0))),
        dates = Gen.oneOf(Seq(d)),
        carriers = Gen.oneOf(Seq("Verizon Wireless"))
      )).map(_.copy(
        days = IndexedSeq[Date](d)
      )).toDS()
      
      val out = dsIfaAgg.transform(filterIfaAgg(startDate, endDate))
      out.count() shouldBe 1
    }

    ignore("filter out 0 sized day stats") {
      import spark.implicits._
      val dsIfaAgg = generate(genIfaAgg(
        dates = Gen.oneOf(Seq(startDate.toDate))
      )).map(_.copy(
        mcc_mnc_freqs = Map.empty
      )).toDS()

      val out = dsIfaAgg.transform(filterIfaAgg(startDate, endDate))
      out.count() shouldBe 0
    }

    it("keep 1 day stats") {
      import spark.implicits._
      val dsIfaAgg = generate(genIfaAgg(
        dates = Gen.oneOf(Seq(startDate.toDate))
      )).toDS()

      val out = dsIfaAgg.transform(filterIfaAgg(startDate, endDate))
      out.count() shouldBe 1
    }

    it("keep multi day stats") {
      import spark.implicits._
      val dsIfaAgg = generate(genIfaAgg(
        dates = Gen.oneOf(Seq(startDate.toDate))
      )).map(_.copy(
        mcc_mnc_freqs = Map(
          "carrier A" -> tier2.DayStat(startDate.toDate, startDate.toDate, 1),
          "carrier B" -> tier2.DayStat(startDate.toDate, startDate.toDate, 1))
      )).toDS()

      val out = dsIfaAgg.transform(filterIfaAgg(startDate, endDate))
      out.count() shouldBe 1
    }

    it("Has some days before, after, and within"){
      import spark.implicits._
      val dsIfaAgg = generate(genIfaAgg(
        dates = Gen.oneOf(Seq(startDate.toDate))
      )).map(_.copy(
        days = IndexedSeq(
          Date.valueOf("2021-05-03"),
          Date.valueOf("2021-05-25"),
          Date.valueOf("2022-01-13"),
          Date.valueOf("2022-01-22"),
          Date.valueOf("2022-05-22"),
        ),
        mcc_mnc_freqs = Map(
          "A" -> tier2.DayStat(Date.valueOf("2021-05-03"), Date.valueOf("2021-05-25"), 2),
          "B"    -> tier2.DayStat(Date.valueOf("2021-05-03"), Date.valueOf("2022-01-13"), 2))
      )).toDS()
      val out = dsIfaAgg.transform(filterIfaAgg(startDate, endDate))
      out.count() shouldBe 1
    }
  }

  describe("sim carrier"){
    
    it("has brand data"){
      import spark.implicits._
      val dsIfaAgg = generate(genIfaAgg(
        dates = Gen.oneOf(Seq(startDate.toDate)),
        carriers = Gen.oneOf(Seq("mcc_mnc"))
      )).toDS().transform(filterIfaAgg(startDate, endDate))

      val dsLookup = Seq(
        MccMncLookupTable("mcc_mnc", "carrier", Some("subbrand"))
      ).toDS()

      val out = dsIfaAgg.transform(simCarrier(dsLookup, startDate))
      out.count() shouldBe dsIfaAgg.count()

      val results = out.select($"d_lifetime_sim_carrier_nonmno", $"e_recent_sim_carrier_nonmno").collect()
      results(0).get(0) shouldBe "subbrand"
      results(0).get(1) shouldBe "subbrand"
    }

    it("has brand data with multiple records"){

    }

    it("has network data") {
      import spark.implicits._
      val dsIfaAgg = generate(genIfaAgg(
        dates = Gen.oneOf(Seq(startDate.toDate)),
        carriers = Gen.oneOf(Seq("mcc_mnc"))
      )).toDS().transform(filterIfaAgg(startDate, endDate))

      val dsLookup = Seq(
        MccMncLookupTable("mcc_mnc", "carrier", None)
      ).toDS()

      val out = dsIfaAgg.transform(simCarrier(dsLookup, startDate))
      out.count() shouldBe dsIfaAgg.count()

      val results = out.select($"b_lifetime_sim_carrier_mno", $"c_recent_sim_carrier_mno").collect()
      results(0).get(0) shouldBe "carrier"
      results(0).get(1) shouldBe "carrier"
    }

    it("has network data with multiple records") {

    }

    it("handles empty day stats") {
      import spark.implicits._
      val dsIfaAgg = generate(genIfaAgg(
        dates = Gen.oneOf(Seq(startDate.toDate)),
        carriers = Gen.oneOf(Seq("mcc_mnc"))
      )).map(_.copy(
        mcc_mnc_freqs = Map.empty
      )).toDS()
        .transform(filterIfaAgg(startDate, endDate))

      val dsLookup = Seq(
        MccMncLookupTable("mcc_mnc", "carrier", None)
      ).toDS()

      val out = dsIfaAgg.transform(simCarrier(dsLookup, startDate))
      out.count() shouldBe dsIfaAgg.count()

      val results = out.collect()
      results(0).b_lifetime_sim_carrier_mno shouldBe null
      results(0).c_recent_sim_carrier_mno shouldBe null
      results(0).d_lifetime_sim_carrier_nonmno shouldBe null
      results(0).e_recent_sim_carrier_nonmno shouldBe null
    }
  }

  describe("cellular carrier"){
    it("filters out non cellular connection types"){
      import spark.implicits._
      val dsIfaIpAgg: Dataset[UdpIfaIpAggYMD] = generate(genUdpIfaIpAggYMD())
        .map(_.copy(
          ifa = ifas(0),
          connection_type = "wifi",
          carrier = "Verizon Wireless"))
        .toDS()
      val dsSimCarrier = Seq(SimCarrier(ifas(0), "network", "network", null, null)).toDS()
      val out = dsIfaIpAgg.transform(cellularCarrier(dsSimCarrier))
      out.count() shouldBe 0
    }
    it("filters out non wireless carriers"){
      import spark.implicits._
      val dsIfaIpAgg: Dataset[UdpIfaIpAggYMD] = generate(genUdpIfaIpAggYMD())
        .map(_.copy(
          ifa = ifas(0),
          carrier = "Vexrizon Wireless"))
        .toDS()
      val dsSimCarrier = Seq(SimCarrier(ifas(0), "network", "network", null, null)).toDS()
      val out = dsIfaIpAgg.transform(cellularCarrier(dsSimCarrier))
      out.count() shouldBe 0
    }
    it("has atleast one record with a wireless carrier"){
      import spark.implicits._
      val dsIfaIpAgg: Dataset[UdpIfaIpAggYMD] = generate(genUdpIfaIpAggYMD())
        .map(_.copy(
          ifa = ifas(0),
          carrier = "Verizon Wireless"))
        .toDS()
      val dsSimCarrier = Seq(SimCarrier(ifas(0), "network", "network", null, null)).toDS()
      val out = dsIfaIpAgg.transform(cellularCarrier(dsSimCarrier))
      out.count() shouldBe 1
    }
  }

  describe("ip range carrier"){
    it("filters out non cellular connection types") {
      import spark.implicits._
      val dsIpBlocks = WirelessCarrierIpBlock.read(ipBlocksLocation)
      val dsIfaIpAgg: Dataset[UdpIfaIpAggYMD] = generate(genUdpIfaIpAggYMD())
        .map(_.copy(
          ifa = ifas(0),
          connection_type = "wifi",
          carrier = "Verizon Wireless"))
        .toDS()
      val dsSimCarrier = Seq(SimCarrier(ifas(0), "network", "network", null, null)).toDS()
      val out = dsIfaIpAgg.transform(ipRangeCarrier(dsIpBlocks, dsSimCarrier))
      out.count() shouldBe 0
    }

    it("handles an ip_block"){
      import spark.implicits._
      val dsIpBlocks = WirelessCarrierIpBlock.read(ipBlocksLocation)
      val rawData = Seq(
        //matches
        (ifas(1), "Verizon Wireless", "***********"),
        (ifas(2), "Verizon Wireless", "*************"),
        (ifas(3), "Verizon Wireless", "*************"),
        (ifas(4), "Verizon Wireless", "***************"),
        (ifas(5), "Verizon Wireless", "***************"),
        (ifas(6), "T-Mobile", "**********"),
        (ifas(7), "Sprint", "**********"),
        (ifas(8), "AT&T Wireless", "*************"),
        (ifas(9), "AT&T Wireless", "***************"),
        //unmatched
        (ifas(10), "Verizon Wireless", "*************"),
        (ifas(11), "AT&T Wireless", "***************"),
      )
      val dsIfaIpAgg: Dataset[UdpIfaIpAggYMD] = rawData.flatMap { case (ifa, carrier, ip) =>
        generate(genUdpIfaIpAggYMD(
          ifas = Gen.oneOf(Seq(ifa)),
          carriers = Gen.oneOf(Seq(carrier)),
          ips = Gen.oneOf(Seq(Utils.ipStringToBinary(ip)))
        ))
      }.toDS()

      val dsSimCarrier: Dataset[SimCarrier] = rawData.flatMap {
        case (ifa, carrier, ip) => Seq(SimCarrier(ifa, "network", "network", null, null))
      }.toDS()

      val result = dsIfaIpAgg.transform(ipRangeCarrier(dsIpBlocks, dsSimCarrier))
      val expected = Seq(
        (ifas(1),  1, 0, 0, 1),
        (ifas(2),  1, 0, 0, 1),
        (ifas(3),  1, 0, 0, 1),
        (ifas(4),  1, 0, 0, 1),
        (ifas(5),  1, 0, 0, 1),
        (ifas(6),  0, 0, 1, 1),
        (ifas(7),  0, 0, 1, 1),
        (ifas(8),  0, 1, 0, 1),
        (ifas(9),  0, 1, 0, 1),
        (ifas(10), 0, 0, 0, 1),
        (ifas(11), 0, 0, 0, 1)
      ).toDF(
        "ifa",
        "g_verizon_reserved_range_dates",
        "h_att_reserved_range_dates",
        "i_tmo_reserved_range_dates",
        "j_total_dates"
      )

      assertDataFrameDataEquals(expected, result)
    }
  }

  describe("cleanBrandField"){
    it("handles all edge cases"){
      import spark.implicits._
      val data = Seq(
        (ifas(0), "d brand", "e brand", 1, "vz"), // e brand
        (ifas(1), null, "e brand", 0, "vz"), // e brand
        (ifas(2), null, null, 1, "vz"), // Probable Verizon MVNO
        (ifas(3), "d brand", null, 0, "vz"), // d brand
        (ifas(4), null, "Spectrum Wireless", 0, "vz"), // spectrum
        (ifas(5), "Spectrum Wireless", null, 0, "vz"), // null
      ).toDF(
        "ifa",
        "d_lifetime_sim_carrier_nonmno",
        "e_recent_sim_carrier_nonmno",
        "g_verizon_reserved_range_dates",
        "network_short")

      val result = data.transform(cleanBrandField).select("ifa", "brand", "brand_source")

      val expected = Seq(
        (ifas(0), "e brand", "e_recent_sim_carrier_nonmno"),
        (ifas(1), "e brand", "e_recent_sim_carrier_nonmno"),
        (ifas(2), "Probable Verizon MVNO", "Probable Verizon MVNO"),
        (ifas(3), "d brand", "d_lifetime_sim_carrier_nonmno"),
        (ifas(4), "Spectrum Wireless", "e_recent_sim_carrier_nonmno"),
        (ifas(5), null, null)
      ).toDF("ifa", "brand", "brand_source")

      assertDataFrameDataEquals(expected, result)
    }
  }

  describe("allCarrierFields"){
    it("can combine all the sources"){
      import spark.implicits._
      val dsSimCarrier = Seq(SimCarrier(ifas(0), "network", "network", null, null)).toDS()
      val dfCellularCarrier = Seq(
        (ifas(0), "cell carrier")
      ).toDF("ifa", "a_recent_cellular_mno")
      val dfIpRangeCarrier = Seq(
        (ifas(0), 1, 2, 3, 4)
      ).toDF(
        "ifa",
        "g_verizon_reserved_range_dates",
        "h_att_reserved_range_dates",
        "i_tmo_reserved_range_dates",
        "j_total_dates"
      )
      allCarrierFields(dsSimCarrier, dfCellularCarrier, dfIpRangeCarrier).count()
    }
  }

  describe("cleanRemoveMismatches"){
    val dataAllowedPairs: Seq[AllowedNetworkBrandPair] = Seq(
      AllowedNetworkBrandPair("T-Mobile", "Assurance Wireless", "Assurance Wireless"),
      AllowedNetworkBrandPair("T-Mobile", "Ting Mobile", "Ting"),
      AllowedNetworkBrandPair("Verizon", "Ting Mobile", "Ting"),
      AllowedNetworkBrandPair("T-Mobile", "Probable T-Mobile MVNO", "T-Mobile Wireless"),
      AllowedNetworkBrandPair("Verizon", "Probable Verizon MVNO", "Verizon Wireless"),
      AllowedNetworkBrandPair("AT&T Wireless", "Probable AT&T MVNO", "AT&T Wireless")
    )

    it("handles cleanRemoveMismatches"){
      import spark.implicits._
      val allowedPairs: Dataset[AllowedNetworkBrandPair] = dataAllowedPairs.toDS()
      val data = Seq(
        (ifas(0), "Assurance Wireless", "T-Mobile Wireless"),
        (ifas(1), "Assurance Wireless", "discard"), // remove or null
        (ifas(2), "Ting Mobile", "T-Mobile Wireless"),
        (ifas(3), "Ting Mobile", "Verizon Wireless"),
        (ifas(4), "Ting Mobile", "discard"), // remove or null
        (ifas(5), "discard brand", "discard"), // remove or null
        (ifas(6), null, "Verizon Wireless"),
      ).toDF("ifa", "brand", "network")
      val result = data.transform(cleanRemoveMismatches(allowedPairs)).select("ifa", "brand", "network")
      result.show()
      val expected = Seq(
        (ifas(0), "Assurance Wireless", "T-Mobile Wireless"),
        (ifas(2), "Ting Mobile", "T-Mobile Wireless"),
        (ifas(3), "Ting Mobile", "Verizon Wireless"),
        (ifas(6), null, "Verizon Wireless"),
      ).toDF("ifa", "brand", "network")
      assertDataFrameDataEquals(expected, result)
    }
    
    it("handles Probable MVNO hardcoding"){
      import spark.implicits._
      val allowedPairs: Dataset[AllowedNetworkBrandPair] = dataAllowedPairs.toDS()
      val data = Seq(
        (ifas(0), "Probable Verizon MVNO", "Verizon Wireless"),
        (ifas(1), "Probable AT&T MVNO", "AT&T Wireless"),
        (ifas(2), "Probable T-Mobile MVNO", "T-Mobile"),
      ).toDF("ifa", "brand", "network")
      val result = data.transform(cleanRemoveMismatches(allowedPairs)).select("ifa", "brand", "network")
      val expected = Seq(
        (ifas(0), "Probable Verizon MVNO", "Verizon Wireless"),
        (ifas(1), "Probable AT&T MVNO", "AT&T Wireless"),
        (ifas(2), "Probable T-Mobile MVNO", "T-Mobile"),
      ).toDF("ifa", "brand", "network")
      assertDataFrameDataEquals(expected, result)
    }
  }

  describe("cleanSingleNetworkBrands"){
    it("handles use_brand"){
      import spark.implicits._
      val data = Seq(
        (ifas(0), "spectrum wireless", "Verizon Wireless", "e_recent_sim_carrier_nonmno", "b_lifetime_sim_carrier_mno"),
        (ifas(1), "spectrum wireless", "Verizon Wireless", "e_recent_sim_carrier_nonmno", "x"),
        (ifas(2), "spectrum wireless", "Verizon Wireless", "x", "b_lifetime_sim_carrier_mno"),
        (ifas(3), "spectrum wireless", "Verizon Wireless", "x", "x"),
      ).toDF("ifa", "brand", "network", "brand_source", "network_source")
      val lookup: Dataset[SingleNetworkBrand] = Seq(
        SingleNetworkBrand("Verizon Wireless", "Spectrum Wireless")
      ).toDS()
      val result = data.transform(cleanSingleNetworkBrands(lookup)).select("ifa", "network_source")
      val expected = Seq(
        (ifas(0), "network_implied_by_brand"),
        (ifas(1), "network_implied_by_brand"),
        (ifas(2), "network_implied_by_brand"),
        (ifas(3), "x"),
      ).toDF("ifa", "network_source")
      assertDataFrameDataEquals(expected, result)
    }

    it("handles equal brands"){
      import spark.implicits._
      val data = Seq(
        (ifas(0), "spectrum wireless", "replace me", "e_recent_sim_carrier_nonmno", "b_lifetime_sim_carrier_mno"),
        (ifas(1), "Spectrum Wireless", "replace me", "e_recent_sim_carrier_nonmno", "b_lifetime_sim_carrier_mno"),
        (ifas(2), "Wireless", "do not replace me", "e_recent_sim_carrier_nonmno", "b_lifetime_sim_carrier_mno"),
      ).toDF("ifa", "brand", "network", "brand_source", "network_source")
      val lookup: Dataset[SingleNetworkBrand] = Seq(
        SingleNetworkBrand("Verizon Wireless", "Spectrum Wireless")
      ).toDS()
      val result = data.transform(cleanSingleNetworkBrands(lookup)).select("ifa", "network")
      val expected = Seq(
        (ifas(0), "Verizon Wireless"),
        (ifas(1), "Verizon Wireless"),
        (ifas(2), "do not replace me"),
      ).toDF("ifa", "network")
      assertDataFrameDataEquals(expected, result)
    }
  }
}
