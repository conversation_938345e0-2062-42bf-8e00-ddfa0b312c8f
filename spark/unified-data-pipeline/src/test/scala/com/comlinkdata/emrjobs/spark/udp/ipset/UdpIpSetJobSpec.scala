package com.comlinkdata.emrjobs.spark.udp.ipset

import java.sql.Date
import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.ipset.UdpIpSetRunner.{runDay, runJob}
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.udp._
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, Utils, UriDFTableRW, LocalDateRange}
import com.comlinkdata.largescale.schema.udp.tier1.UdpDailyMaxmind
import com.comlinkdata.largescale.schema.udp.tier2.UdpIpSet
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.{lit, length => spark_len}

import java.net.URI
import java.time.LocalDate
import scala.util.Random

private case class UdpDailyMaxmindLiteTest(
  ip: String,
  connection_type: Option[String],
  carrier: Option[String]
)

private case class UdpDailyMaxmindXLiteTest(
  ip: String
)

class UdpIpSetJobSpec extends CldSparkBaseSpec with LazyLogging {
  import spark.implicits._

  private val ip0 = Utils.ipStringToBinary("**************")
  private val today = LocalDate.now()

  object Generator {
    Random.setSeed(1)

    private val ips = (1 to 10).map(i =>Utils.ipStringToBinary(s"1.2.3.$i"))
    
    def writeRandomMaxmind(basePath: URI, today: LocalDate, ds: ComlinkdataDatasource): Unit = {
      val maxMindRows4 = dataset[UdpDailyMaxmindLiteTest] {
        s"""
           |      ip|connection_type|   carrier|
           |******* |       Cellular| Carrier 2|
           |********|      Corporate| Carrier 2|
           |******* |       Cellular| Carrier 1|
           |******* |      Corporate| Carrier 3|
           |******* |      Cable/DSL| Carrier 1|
           |******* |      Cable/DSL| Carrier 3|
           |******* |      Cable/DSL|Sprint PCS|
           |**************|           Wifi|   Comcast|
           """
      }
        .withColumn("ip",  Utils.ipStringToBinaryColumn($"ip"))
        .withColumn("organization", lit("fake"))
        .withColumn("autonomous_system_number", lit(7922))
        .withColumn("autonomous_system_organization", lit("COMCAST-7922"))
        .withColumn("ds", lit(ds.toString))
        .withColumn("ipversion", lit(4))
        .withColumn("year", lit(today.getYearString))
        .withColumn("month", lit(today.getMonthValueString))
        .withColumn("day", lit(today.getDayOfMonthString))
        .as[UdpDailyMaxmind]

      val outPath4 = f"$basePath/ds=${ds.toString}/ipversion=4/year=${today.getYear.toString}/month=${today.getMonthValue}%02d/day=${today.getDayOfMonth}%02d"
      UriDFTableRW.fromStr(outPath4).write(maxMindRows4.toDF())

      val maxMindRows16 = dataset[UdpDailyMaxmindXLiteTest] {
        s"""
           |ip                                  |
           |aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:5|
           |aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:7|
           |aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:4|
           |aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:8|
           |aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:3|
           |aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:1|
           |aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:6|
           |aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:aaaa:9|
           """
      }
        .withColumn("ip",  Utils.ipStringToBinaryColumn($"ip"))
        .withColumn("connection_type", lit("Wifi"))
        .withColumn("carrier", lit("fake"))
        .withColumn("organization", lit("fake"))
        .withColumn("autonomous_system_number", lit(7922))
        .withColumn("autonomous_system_organization", lit("COMCAST-7922"))
        .withColumn("ds", lit(ds.toString))
        .withColumn("ipversion", lit(4))
        .withColumn("year", lit(today.getYearString))
        .withColumn("month", lit(today.getMonthValueString))
        .withColumn("day", lit(today.getDayOfMonthString))
        .as[UdpDailyMaxmind]

      val outPath16 = f"$basePath/ds=${ds.toString}/ipversion=6/year=${today.getYear.toString}/month=${today.getMonthValue}%02d/day=${today.getDayOfMonth}%02d"
      UriDFTableRW.fromStr(outPath16).write(maxMindRows16.toDF())
    }

    def writeRandomIpSet(basePath: URI, today: LocalDate, ds: ComlinkdataDatasource): Unit = {
      val days_since_first_seen = Random.nextInt(120)
      val days_since_first_seen_carrier = Random.nextInt(days_since_first_seen + 1)
      val days_since_last_seen = Random.nextInt(days_since_first_seen_carrier + 1)

      val IpSetRows = Random.shuffle(ips).map(ip => UdpIpSet(
        partition_date = Date valueOf today,
        ip = ip,
        date_first_observed_ip = Date valueOf today.minusDays(days_since_first_seen),
        date_first_observed_carrier = Date valueOf today.minusDays(days_since_first_seen_carrier),
        date_last_observed = Date valueOf today.minusDays(days_since_last_seen),
        carriers = List("fake"),
        connection_type = Some("Wifi")
      )
      ).union(Seq(
        UdpIpSet(
          partition_date = Date valueOf today,
          ip = ip0,
          date_first_observed_ip = Date valueOf today.minusDays(200) ,
          date_first_observed_carrier = Date valueOf today.minusDays(200),
          date_last_observed = Date valueOf today.minusDays(200),
          carriers = List("Level 3 Communications", "Fake1", "Fake2"),
          connection_type = Some("WIFI")
        )
      )).toDS()

      val outPath = f"$basePath/ds=${ds.toString}/year=${today.getYear.toString}/month=${today.getMonthValue}%02d/day=${today.getDayOfMonth}%02d"
      UriDFTableRW.fromStr(outPath).write(IpSetRows.toDF())
    }
  }

  override def afterAll() = {
    super.afterAll()
    UriDFTableRW.fromStr("table://mmPath1").dropAll()
    UriDFTableRW.fromStr("table://mmPath2").dropAll()
    UriDFTableRW.fromStr("table://mmPath3").dropAll()
    UriDFTableRW.fromStr("table://ipSetPath1").dropAll()
    UriDFTableRW.fromStr("table://ipSetPath2").dropAll()
    UriDFTableRW.fromStr("table://ipSetPath3").dropAll()
    UriDFTableRW.fromStr("table://output1").dropAll()
    UriDFTableRW.fromStr("table://output2").dropAll()
  }

  describe("Test With Generated data") {
    it("Test Single Day Catchup") {
      import spark.implicits._

      val mmPath = URI.create("table://mmPath1")
      val ipSetPath = URI.create("table://ipSetPath1")
      val output = URI.create("table://output1")
      Generator.writeRandomMaxmind(mmPath, today = today, ds = ComlinkdataDatasource.mw05)
      Generator.writeRandomIpSet(ipSetPath, today.minusDays(1), ComlinkdataDatasource.mw05)

      val ipSetConfig = UdpIpSetConfig(
        datasources = List(ComlinkdataDatasource.mw05),
        dailyMaxmindLocation = mmPath,
        ipSetLocation = ipSetPath,
        maxDaysToRun = -1,
        bootstrapDateOpt = None,
        ipSetOutLocation = Some(output)
      )

      runDay(ipSetConfig)(ComlinkdataDatasource.mw05, today)

      val outpath_tsl = TimeSeriesLocation
        .ofYmdDatePartitions(ipSetConfig.outputLocation())
        .withPartition(ComlinkdataDatasource.mw05)
        .build
        .partition(today)

      val joined = UriDFTableRW.fromStr(outpath_tsl).read().as[UdpIpSet]

      //joined.show()
      joined.count shouldEqual 19
      joined.where($"ip" === ip0).take(1).head.date_last_observed shouldEqual Date.valueOf(today)
      joined.where(spark_len($"ip") === 16).select("date_first_observed_carrier").distinct().take(1).head.get(0) shouldEqual Date.valueOf(today)
    }

    it("Test Multi-Day Catchup") {
      val mmPath = URI.create("table://mmPath2")
      val ipSetPath = URI.create("table://ipSetPath2")
      val output = URI.create("table://output2")

      Generator.writeRandomMaxmind(mmPath, today = today.minusDays(3), ds = ComlinkdataDatasource.mw05)
      Generator.writeRandomMaxmind(mmPath, today = today.minusDays(2), ds = ComlinkdataDatasource.mw05)
      Generator.writeRandomMaxmind(mmPath, today = today.minusDays(1), ds = ComlinkdataDatasource.mw05)
      Generator.writeRandomMaxmind(mmPath, today = today, ds = ComlinkdataDatasource.mw05)

      val startDate = today.minusDays(4)
      Generator.writeRandomIpSet(ipSetPath, startDate, ComlinkdataDatasource.mw05)

      val ipSetConfig = UdpIpSetConfig(
        datasources = List(ComlinkdataDatasource.mw05),
        dailyMaxmindLocation = mmPath,
        ipSetLocation = ipSetPath,
        maxDaysToRun = 4,
        bootstrapDateOpt = None,
        ipSetOutLocation = Some(output)
      )

      runJob(ipSetConfig)

      val datesThatShouldExist = LocalDateRange(startDate, today)

      val outputDate = TimeSeriesLocation
        .ofYmdDatePartitions(ipSetConfig.outputLocation())
        .withPartition(ComlinkdataDatasource.mw05)
        .build

      val foundDates = outputDate.partitionsExistingBetween(datesThatShouldExist).toList
      foundDates.size shouldEqual 4

      val allIpSets = UriDFTableRW(output).readBasePath().as[UdpIpSet]

      allIpSets.where($"date_first_observed_carrier" < $"date_first_observed_ip").count() shouldEqual 0
      allIpSets.where($"date_last_observed" < $"date_first_observed_ip").count() shouldEqual 0
      allIpSets.where($"date_first_observed_carrier" > $"date_last_observed").count() shouldEqual 0
    }

    it("Test Multi-Day Bootstrap") {

      val mmPath = URI.create("table://mmPath3")
      val ipSetPath = URI.create("table://ipSetPath3")

      Generator.writeRandomMaxmind(mmPath, today = today.minusDays(3), ds = ComlinkdataDatasource.mw05)
      Generator.writeRandomMaxmind(mmPath, today = today.minusDays(2), ds = ComlinkdataDatasource.mw05)
      Generator.writeRandomMaxmind(mmPath, today = today.minusDays(1), ds = ComlinkdataDatasource.mw05)
      Generator.writeRandomMaxmind(mmPath, today = today, ds = ComlinkdataDatasource.mw05)

      val startDate = today.minusDays(4)

      val ipSetConfig = UdpIpSetConfig(
        datasources = List(ComlinkdataDatasource.mw05),
        dailyMaxmindLocation = mmPath,
        ipSetLocation = ipSetPath,
        maxDaysToRun = 4,
        bootstrapDateOpt = Some(today.minusDays(3))
      )

      runJob(ipSetConfig)

      val datesThatShouldExist = LocalDateRange(startDate, today)

      val outputDate = TimeSeriesLocation
        .ofYmdDatePartitions(ipSetConfig.outputLocation())
        .withPartition(ComlinkdataDatasource.mw05)
        .build

      val foundDates = outputDate.partitionsExistingBetween(datesThatShouldExist).toList

      foundDates.size shouldEqual 4

      val partitions = foundDates.map(_._2)

      val allIpSets = UriDFTableRW(ipSetConfig.outputLocation()).readBasePath().as[UdpIpSet]

      allIpSets.where($"date_first_observed_carrier" < $"date_first_observed_ip").count() shouldEqual 0
      allIpSets.where($"date_last_observed" < $"date_first_observed_ip").count() shouldEqual 0
      allIpSets.where($"date_first_observed_carrier" > $"date_last_observed").count() shouldEqual 0

      val numberOldRows = UriDFTableRW.fromStr(partitions.last).read()
        .where($"date_first_observed_ip" =!= lit(Date.valueOf(today)))
        .count()

      // Some of the rows should come from previous days
      numberOldRows.toInt should be > 0
      allIpSets.select("carriers").collect().foreach(row => {
        row.getSeq(0).reduceLeft((a: String, b: String) => {
          a should not equal b
          b
        })
      })
      //allIpSets.select("ip", "carriers").show(false)
    }
  }
}
