package com.comlinkdata.emrjobs.spark.udp.homes
import com.comlinkdata.commons.testing.CldSparkBaseSpec
import org.apache.spark.sql.Dataset

class UdpHhStepGCldBroadbandHouseholdsJobSpec extends CldSparkBaseSpec {

  import UdpHhStepGCldBroadbandHouseholdsSchema._

  val sampleUri = getClass.getResource("geocoded_result.parquet")

  lazy val loadSampleDs: Dataset[GeocodedResult] = {
    import spark.implicits._
    spark.read
      .parquet(sampleUri.toString)
      .as[GeocodedResult]
      .cache()
  }

  describe("createThings") {

    ignore("should execute without exceptions - too sloooow") {
      val input: Dataset[GeocodedResult] = loadSampleDs
      UdpHhStepGCldBroadbandHouseholdsRunner
        .createCldDevicesWithHousehold(input)
        .show(5)
    }

    //      //TODO implement tests for Table 6
    //      it("should propose 1 location per household ID") {}
    //      it("should propose the correct location per household ID") {}

  }
}
