package com.comlinkdata.emrjobs.spark.udp.tier0

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.tier0.KochavaAdapter.KochavaJoined
import com.comlinkdata.largescale.commons.Utils
import java.net.{URI, URL}
import java.nio.file.Paths
import java.sql.{Date, Timestamp}
import java.time.{Instant, LocalDate}
import org.apache.spark.sql.functions._

class KochavaAdapterSpec extends CldSparkBaseSpec {

  val countryToIso3Uri = this.getClass.getResource("country_to_iso3_sample.csv").toURI

  def startDate00 = LocalDate.of(2021, 1, 26)
  def startDate01 = LocalDate.of(2021, 1, 29)
  def joined0 = KochavaJoined(
    device_id_value = "f9df4a32-6783-45c6-a5fd-fabf1c82874a",
    date = java.sql.Date.valueOf("2021-01-28"),
    device_id_type = "idfa",
    activity_timestamp = "2021-01-27 04:41:05 UTC",
    latitude = 38.65551f,
    longitude = -93.22337f,
    horizontal_accuracy = 29.0f,
    ip_address = "*************",
    country_name = "United States",
    consent = Some("1---"),
    hardware_type = None,
    user_agent = None,
    device_make = None,
    device_model = None,
    marketing_name = None,
    os = None,
    os_version = None,
    screen_width = None,
    screen_height = None,
    screen_ppi = None,
    activity_date = None,
    carrier_name = None
  )

  import KochavaAdapter._
  import spark.implicits._

  val txResource = this.getClass.getResource("kochava/transaction")
  val devAttrResource = this.getClass.getResource("kochava/device_attributes")
  val carrierResource = this.getClass.getResource("kochava/carrier")

  def p(url: URL) = Paths.get(url.toURI).toAbsolutePath.toString

  def dfTx = spark.read
    .schema(Utils.schema[RawTransaction])
    .csv(p(txResource))
    .as[RawTransaction]

  def dfDevAttr = spark.read
    .schema(Utils.schema[RawDeviceAttributes])
    .csv(p(devAttrResource))
    .as[RawDeviceAttributes]

  def dfCarrier = spark.read
    .schema(Utils.schema[RawCarrier])
    .csv(p(carrierResource))
    .as[RawCarrier]

  def countryToIso3 = CountryNameToIso3.read(countryToIso3Uri)(spark).cache()

  describe("resources") {
    ignore("has transaction") {
      txResource should not be null
    }
    ignore("has dev attr") {
      devAttrResource should not be null
    }
    ignore("has carrier") {
      carrierResource should not be null
    }
  }

  describe("configuration") {
    ignore("can jsonify") {
      val c = KochavaAdapter.Config(
        carrierLocation = URI create "s3://some-bucket/path/to/carrier",
        deviceAttributesLocation = URI create "s3://some-bucket/path/to/device/attributes",
        transactionLocation = URI create "s3://some-bucket/path/to/transaction",
        countryNameToIso3Location = URI create "s3a://d000-comlinkdata-com/prod/private/lookup-tables/udp/country_name_to_iso3/"
      )
      val (expected, actual) = configCheck(c, prettyPrintToScreen = false)
      expected shouldBe actual
    }
  }

  describe("schemas") {

    ignore("can parse transaction") {
      noException should be thrownBy dfTx.map(identity).showToString
      dfTx.count shouldBe 10000L
    }

    ignore("has date from hive partition") {
      dfTx.map(_.date).distinct.collect should contain theSameElementsAs Seq(Date valueOf LocalDate.of(2021, 1, 28))
    }

    describe("device attributes") {
      ignore("can parse device attributes") {
        noException should be thrownBy dfDevAttr.map(identity).showToString
      }
      ignore("never has empty strings") {
        dfDevAttr.filter('device_model === "").count shouldBe 0
      }
    }

    ignore("can parse carrier") {
      noException should be thrownBy dfCarrier.map(identity).showToString
    }
  }
  describe("buildDataFrameFromComponents") {

    val ifaWithDeviceAttributes = "651f7962-2a6b-46ef-a36c-158bc8bde54f"
    val ifaWithDeviceAtributesUserAgent = "Dalvik/2.1.0 (Linux; U; Android 10; SM-A516U Build/QP1A.190711.020)"


    ignore("completes without errors") {
      val result = buildDataFrameFromComponents(dfTx, dfDevAttr, dfCarrier)
        .as[KochavaJoined]
      noException should be thrownBy result.map(identity).showToString
    }


    ignore("adds device data when available") {
      val result = buildDataFrameFromComponents(dfTx, dfDevAttr, dfCarrier)
        .as[KochavaJoined]
        .filter($"device_id_value" === lit(ifaWithDeviceAttributes))
      result.map(_.user_agent).collect.flatten should contain theSameElementsAs
        Seq(ifaWithDeviceAtributesUserAgent)
    }
  }

  describe("integration v2") {
    ignore("runs") {
      val input = buildDataFrameFromComponents(dfTx, dfDevAttr, dfCarrier)

      noException should be thrownBy rawToUdpRaw2Dataset(countryToIso3, input)
        .transform(UdpDeduplicator2.filterDuplicateObservations)
        .showToString
    }
  }

  describe("rawToUdpRaw2Dataset") {
    ignore("runs without errors") {
      val input = buildDataFrameFromComponents(dfTx, dfDevAttr, dfCarrier)
      val result = rawToUdpRaw2Dataset(countryToIso3, input)
      noException should be thrownBy result.showToString

    }

    ignore("can write result of udpraw to parquet") {
      // the process converted appropriately, but writing to parquet caused issue due to there being a null column
      val dest = Utils.joinPaths(tempFolder.newFolder("test").toPath.toAbsolutePath.toUri, "parquet-data").toString
      val input = buildDataFrameFromComponents(dfTx, dfDevAttr, dfCarrier)
      val result = rawToUdpRaw2Dataset(countryToIso3, input)
      val expected = result.count
      result.write.parquet(dest)
      val actual = spark.read.parquet(dest).count
      actual shouldBe expected
    }

    describe("model") {
      ignore("changes unknown to None") {
        val expected = None
        val input = List(
          joined0.copy(device_model = Some("unknown"))
        ).toDS.toDF
        val result = rawToUdpRaw2Dataset(countryToIso3, input).collect.head
        result.model shouldBe expected
      }
      ignore("does not modify non unknown") {
        val expected = Some("not-unknown")
        val input = List(
          joined0.copy(device_model = expected)
        ).toDS.toDF
        val result = rawToUdpRaw2Dataset(countryToIso3, input).collect.head
        result.model shouldBe expected
      }
      ignore("does not modify None") {
        val expected = None
        val input = List(
          joined0.copy(device_model = expected)
        ).toDS.toDF
        val result = rawToUdpRaw2Dataset(countryToIso3, input).collect.head
        result.model shouldBe expected
      }
      ignore("trims spaces") {
        val expected = Some("mymodel")
        val input = List(
          joined0.copy(device_model = Some("  mymodel  "))
        ).toDS.toDF
        val result = rawToUdpRaw2Dataset(countryToIso3, input).collect.head
        result.model shouldBe expected

      }
    }
    describe("make") {
      ignore("changes unknown to None") {
        val expected = None
        val input = List(
          joined0.copy(device_make = Some("unknown"))
        ).toDS.toDF
        val result = rawToUdpRaw2Dataset(countryToIso3, input).collect.head
        result.make shouldBe expected
      }

      ignore("does not modify non unknown") {
        val expected = Some("not-unknown")
        val input = List(
          joined0.copy(device_make = expected)
        ).toDS.toDF
        val result = rawToUdpRaw2Dataset(countryToIso3, input).collect.head
        result.make shouldBe expected
      }

      ignore("does not modify None") {
        val expected = None
        val input = List(
          joined0.copy(device_make = expected)
        ).toDS.toDF
        val result = rawToUdpRaw2Dataset(countryToIso3, input).collect.head
        result.make shouldBe expected
      }

      ignore("trims spaces") {
        val expected = Some("packard bell")
        val input = List(
          joined0.copy(device_make = Some(" packard bell "))
        ).toDS.toDF
        val result = rawToUdpRaw2Dataset(countryToIso3, input).collect.head
        result.make shouldBe expected
      }
    }

    describe("iso3") {
      ignore("converts country to iso3") {
        val expected = Some("USA")
        val input = List(
          joined0.copy(country_name = "United States")
        ).toDS.toDF
        val result = rawToUdpRaw2Dataset(countryToIso3, input).collect.head
        result.country_iso3 shouldBe expected
      }
      ignore("errors if country name not in iso3 list") {
        val input = List(
          joined0.copy(country_name = "United States not a thing")
        ).toDS.toDF
        val results = rawToUdpRaw2Dataset(countryToIso3, input).collect
        results.isEmpty shouldBe true
      }
      ignore("errors if the country is not provided") {
        val input = List(
          joined0.copy(country_name = null)
        ).toDS.toDF
        val results = rawToUdpRaw2Dataset(countryToIso3, input).collect
        results.isEmpty shouldBe true
      }
    }

    describe("carrier") {
      ignore("trims leading spaces from carrier name") {
        val expected = Some("asdf")
        val input = List(
          joined0.copy(carrier_name = Some("  asdf"))
        ).toDS.toDF

        val result = rawToUdpRaw2Dataset(countryToIso3, input).collect.head
        result.newcarrier shouldBe expected
      }

      ignore("trims trailing spaces from carrier name") {
        val expected = Some("asdf")
        val input = List(
          joined0.copy(carrier_name = Some("asdf  "))
        ).toDS.toDF

        val result = rawToUdpRaw2Dataset(countryToIso3, input).collect.head
        result.newcarrier shouldBe expected
      }
      ignore("passes thru None carrier") {
        val expected = None
        val input = List(
          joined0.copy(carrier_name = None)
        ).toDS.toDF
        val result = rawToUdpRaw2Dataset(countryToIso3, input).collect.head
        result.newcarrier shouldBe expected
      }

    }

  }

  describe("timestampPattern") {

    ignore("can convert to sql timestamp from string") {
      val example = "2020-12-13 02:01:11 UTC"
      val expected = Instant.ofEpochSecond(1607824871) // https://www.epochconverter.com/
      val df = Seq(example).toDF("t")
      val result = df.withColumn("tt", to_timestamp($"t", timestampPattern))
      val ts = result.select("tt").as[Timestamp].collect.head
      ts.toInstant shouldBe expected
    }

    ignore("can convert military hours > 12") {
      val example = "2020-12-13 23:01:11 UTC"
      val expected = Instant.ofEpochSecond(1607900471)
      val df = Seq(example).toDF("t")
      val result = df.withColumn("tt", to_timestamp($"t", timestampPattern))
      val ts = result.select("tt").as[Timestamp].collect.head
      ts.toInstant shouldBe expected
    }
  }

  describe("rawToUdpRawDataset") {
    ignore("errors immediately, no longer implemented") {
      val input = buildDataFrameFromComponents(dfTx, dfDevAttr, dfCarrier)
      val date = LocalDate.of(2020, 1, 28)

      a[NotImplementedError] should be thrownBy {
        rawToUdpRawDataset(date.minusYears(1), date.plusYears(1), countryToIso3Uri, input)
      }
    }
  }

}
