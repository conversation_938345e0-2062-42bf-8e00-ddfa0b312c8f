package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.installbase.IPClassifierRunner.{classifierCHI, ipClassifier}
import com.comlinkdata.emrjobs.spark.udp.installbase.IPSequenceSetRunner._
import com.comlinkdata.largescale.schema.udp.{Ifa, Ip}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.ml.classification.RandomForestClassificationModel
import org.apache.spark.sql.Dataset

import java.time.LocalDate


class IPClassifierSpec extends CldSparkBaseSpec with LazyLogging {
  import spark.implicits._

  private val startDate = LocalDate.of(2021, 10, 1)
  private val endDate = LocalDate.of(2021, 10, 3)

  private val ifas: Array[Ifa] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]
  private val ips: Array[Ip] = (32 to 62).map( x => Array(192.toByte, 168.toByte, 0.toByte, x.toByte)).toArray[Array[Byte]]

  private val DAYS_TO_LOOK_BACK = 7

  describe("generate model") {

    it("without parquet") {

    }
  }

  describe("IP Classification"){
    def toIfaToIpYMD(item: Seq[(Ip, Ifa, Int, Double, String, String, String)]): Seq[IfaToIpYMD] = {
      item.map(i=> IfaToIpYMD(
        ip = i._1,
        ifa = i._2,
        carrier = "Carrier",
        consolidated_id = "1",
        connection_type = "WIFI",
        organization = "Carrier",
        sp_platform = "1005",
        autonomous_system_number = "1",
        obs_count = i._3,
        night_obs_count = i._3,
        household_obs_count = i._3,
        household_weighted_obs_count = 0,
        business_weighted_obs_count = i._4,
        year = i._5,
        month = i._6,
        day = i._7
      ))
    }
    val resi1 = Seq(
      // resi 1
      (ips(0), ifas(0), 1, 0.0, "2021", "11", "01"),
      (ips(0), ifas(0), 1, 0.0, "2021", "11", "02"),
      (ips(0), ifas(0), 0, 0.0, "2021", "11", "03"),
      (ips(0), ifas(1), 1, 0.0, "2021", "11", "01"),
      (ips(0), ifas(1), 1, 0.0, "2021", "11", "02"),
      (ips(0), ifas(2), 0, 0.0, "2021", "11", "03")
    )
    val resi2 = Seq(
      (ips(1), ifas(0), 1, 0.0, "2021", "11", "01"),
      (ips(1), ifas(0), 1, 0.0, "2021", "11", "02"),
      (ips(1), ifas(0), 0, 0.0, "2021", "11", "03"),
      (ips(1), ifas(1), 1, 1.0, "2021", "11", "01"),
      (ips(1), ifas(1), 1, 1.0, "2021", "11", "02"),
      (ips(1), ifas(2), 0, 1.0, "2021", "11", "03")
    )
    val resi3 = Seq(
      (ips(2), ifas(0), 0, 13.0, "2021", "11", "01"),
      (ips(2), ifas(0), 0, 0.5, "2021", "11", "02"),
    )
    val resi4 = Seq(
      (ips(3), ifas(0), 0, 0.0, "2021", "11", "01"),
      (ips(3), ifas(0), 0, 0.0, "2021", "11", "02")
    )
    val busi1 = Seq(
      (ips(4), ifas(1), 1, 0.0, "2021", "11", "01"),
      (ips(4), ifas(1), 0, 1154.0, "2021", "11", "02"),
      (ips(4), ifas(1), 0, 0.0, "2021", "11", "03"),
      (ips(4), ifas(2), 1, 0.0, "2021", "11", "01"),
      (ips(4), ifas(2), 0, 0.0, "2021", "11", "02"),
      (ips(4), ifas(2), 0, 0.0, "2021", "11", "03"),
      (ips(4), ifas(3), 1, 0.0, "2021", "11", "01"),
      (ips(4), ifas(3), 0, 0.0, "2021", "11", "02"),
      (ips(4), ifas(3), 0, 0.0, "2021", "11", "03"),
      (ips(4), ifas(4), 1, 0.0, "2021", "11", "01"),
      (ips(4), ifas(4), 0, 0.0, "2021", "11", "02"),
      (ips(4), ifas(5), 1, 0.0, "2021", "11", "01"),
      (ips(4), ifas(6), 0, 0.0, "2021", "11", "01"),
    )
    val busi2 = Seq(
      (ips(5), ifas(1), 1, 0.0, "2021", "11", "01"),
      (ips(5), ifas(1), 0, 5209.0, "2021", "11", "02"),
      (ips(5), ifas(1), 0, 0.0, "2021", "11", "03"),
      (ips(5), ifas(2), 1, 0.0, "2021", "11", "01"),
      (ips(5), ifas(2), 0, 0.0, "2021", "11", "02"),
      (ips(5), ifas(2), 0, 0.0, "2021", "11", "03"),
      (ips(5), ifas(3), 1, 0.0, "2021", "11", "01"),
      (ips(5), ifas(3), 0, 0.0, "2021", "11", "02"),
      (ips(5), ifas(3), 0, 0.0, "2021", "11", "03"),
      (ips(5), ifas(4), 1, 0.0, "2021", "11", "01"),
      (ips(5), ifas(4), 0, 0.0, "2021", "11", "02"),
      (ips(5), ifas(5), 1, 0.0, "2021", "11", "01"),
      (ips(5), ifas(6), 0, 0.0, "2021", "11", "01"),
    )
    val busi3 = Seq(
      (ips(6), ifas(1), 1, 0.0, "2021", "11", "01"),
      (ips(6), ifas(1), 1, 0.0, "2021", "11", "02"),
      (ips(6), ifas(1), 1, 0.0, "2021", "11", "03"),
      (ips(6), ifas(1), 1, 0.0, "2021", "11", "04"),
      (ips(6), ifas(1), 1, 0.0, "2021", "11", "05"),
      (ips(6), ifas(1), 1, 0.0, "2021", "11", "06"),
      (ips(6), ifas(2), 0, 0.0, "2021", "11", "01"),
      (ips(6), ifas(2), 0, 0.0, "2021", "11", "02"),
      (ips(6), ifas(2), 0, 0.0, "2021", "11", "03"),
      (ips(6), ifas(3), 0, 0.0, "2021", "11", "01"),
      (ips(6), ifas(3), 0, 0.0, "2021", "11", "02"),
      (ips(6), ifas(3), 0, 0.0, "2021", "11", "03"),
      (ips(6), ifas(4), 0, 0.0, "2021", "11", "01"),
      (ips(6), ifas(5), 0, 0.0, "2021", "11", "01"),
      (ips(6), ifas(6), 0, 0.0, "2021", "11", "01"),
      (ips(6), ifas(7), 0, 0.0, "2021", "11", "01"),
      (ips(6), ifas(8), 0, 0.0, "2021", "11", "01"),
      (ips(6), ifas(9), 1, 0.0, "2021", "11", "01"),
    )
    val busi4 = Seq(
      (ips(7), ifas(1), 1, 0.0, "2021", "11", "01"),
      (ips(7), ifas(1), 1, 0.0, "2021", "11", "02"),
      (ips(7), ifas(1), 1, 0.0, "2021", "11", "03"),
      (ips(7), ifas(1), 1, 0.0, "2021", "11", "04"),
      (ips(7), ifas(1), 1, 0.0, "2021", "11", "05"),
      (ips(7), ifas(1), 1, 0.0, "2021", "11", "06"),
      (ips(7), ifas(2), 0, 0.0, "2021", "11", "01"),
      (ips(7), ifas(2), 0, 0.0, "2021", "11", "02"),
      (ips(7), ifas(2), 0, 0.0, "2021", "11", "03"),
      (ips(7), ifas(3), 0, 0.0, "2021", "11", "01"),
      (ips(7), ifas(3), 0, 0.5, "2021", "11", "02"),
      (ips(7), ifas(3), 0, 1.0, "2021", "11", "03"),
      (ips(7), ifas(4), 0, 1.0, "2021", "11", "01"),
      (ips(7), ifas(5), 0, 1.0, "2021", "11", "01"),
      (ips(7), ifas(6), 0, 1.0, "2021", "11", "01"),
      (ips(7), ifas(7), 0, 1.0, "2021", "11", "01"),
      (ips(7), ifas(8), 0, 1.0, "2021", "11", "01"),
      (ips(7), ifas(9), 1, 1.0, "2021", "11", "01"),
    )
    val data = resi1 ++ resi2 ++ resi3 ++ resi4 ++ busi1 ++ busi2 ++ busi3 ++ busi4
    val data2 = toIfaToIpYMD(data)

    it("Residential 1"){
      val results = toIfaToIpYMD(resi1)
        .toDS()
        .transform(classifierCHI)

      val collect = results.collect()
      collect.length shouldBe 1

      val row = collect(0)
      row.getAs[Int](1) shouldBe 1
      row.getAs[Int](2) shouldBe 2
      row.getAs[Int](3) shouldBe 3
      row.getAs[Int](4) shouldBe 2
      row.getAs[Int](5) shouldBe 0.0
    }

    it("Residential 2"){
      val results = toIfaToIpYMD(resi2)
        .toDS()
        .transform(classifierCHI)

      val collect = results.collect()
      collect.length shouldBe 1

      val row = collect(0)
      row.getAs[Int](1) shouldBe 1
      row.getAs[Int](2) shouldBe 2
      row.getAs[Int](3) shouldBe 3
      row.getAs[Int](4) shouldBe 2
      row.getAs[Int](5) shouldBe 3.0
    }

    it("Residential 3"){
      val results = toIfaToIpYMD(resi3)
        .toDS()
        .transform(classifierCHI)

      val collect = results.collect()
      collect.length shouldBe 1

      val row = collect(0)
      row.getAs[Int](1) shouldBe 0
      row.getAs[Int](2) shouldBe 1
      row.getAs[Int](3) shouldBe 1
      row.getAs[Int](4) shouldBe 0
      row.getAs[Int](5) shouldBe 13.5
    }

    it("Residential 4"){
      val results = toIfaToIpYMD(resi4)
        .toDS()
        .transform(classifierCHI)

      val collect = results.collect()
      collect.length shouldBe 1

      val row = collect(0)
      row.getAs[Int](1) shouldBe 0
      row.getAs[Int](2) shouldBe 1
      row.getAs[Int](3) shouldBe 1
      row.getAs[Int](4) shouldBe 0
      row.getAs[Int](5) shouldBe 0.0
    }

    it("Business 1"){
      val results = toIfaToIpYMD(busi1)
        .toDS()
        .transform(classifierCHI)

      val collect = results.collect()
      collect.length shouldBe 1

      val row = collect(0)
      row.getAs[Int](1) shouldBe 3
      row.getAs[Int](2) shouldBe 4
      row.getAs[Int](3) shouldBe 6
      row.getAs[Int](4) shouldBe 1
      row.getAs[Int](5) shouldBe 1154.0
    }

    it("Business 2"){
      val results = toIfaToIpYMD(busi2)
        .toDS()
        .transform(classifierCHI)

      val collect = results.collect()
      collect.length shouldBe 1

      val row = collect(0)
      row.getAs[Int](1) shouldBe 3
      row.getAs[Int](2) shouldBe 4
      row.getAs[Int](3) shouldBe 6
      row.getAs[Int](4) shouldBe 1
      row.getAs[Int](5) shouldBe 5209.0
    }

    it("Business 3"){
      val results = toIfaToIpYMD(busi3)
        .toDS()
        .transform(classifierCHI)

      val collect = results.collect()
      collect.length shouldBe 1

      val row = collect(0)
      row.getAs[Int](1) shouldBe 3
      row.getAs[Int](2) shouldBe 3
      row.getAs[Int](3) shouldBe 9
      row.getAs[Int](4) shouldBe 6
      row.getAs[Int](5) shouldBe 0.0
    }

    it("Business 4"){
      val results = toIfaToIpYMD(busi4)
        .toDS()
        .transform(classifierCHI)

      val collect = results.collect()
      collect.length shouldBe 1

      val row = collect(0)
      row.getAs[Int](1) shouldBe 3
      row.getAs[Int](2) shouldBe 3
      row.getAs[Int](3) shouldBe 9
      row.getAs[Int](4) shouldBe 6
      row.getAs[Int](5) shouldBe 7.5
    }

    it("test model"){

      val dsIfa: Dataset[IfaToIpYMD] = data2.toDS()

      val ipClassifierPath = this.getClass().getResource("/com/comlinkdata/emrjobs/spark/udp/installbase/bus_res_classifier.model").toURI
      val ipClassifierModel = RandomForestClassificationModel.load(ipClassifierPath.toString)
      val dsClassifiedIP: Dataset[ClassifiedIP] = dsIfa
        .transform(ipClassifier(ipClassifierModel))
        .orderBy($"ip")

      val collect = dsClassifiedIP.collect()
      collect.length shouldBe 8
      collect(0).prediction shouldBe 0.0
      collect(1).prediction shouldBe 0.0
      collect(2).prediction shouldBe 0.0
      collect(3).prediction shouldBe 0.0
      collect(4).prediction shouldBe 1.0
      collect(5).prediction shouldBe 1.0
      collect(6).prediction shouldBe 1.0
      collect(7).prediction shouldBe 1.0
    }
  }
}
