package com.comlinkdata.emrjobs.spark.udp.iplist

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.iplist.IpListCommon.loadIpRangeLookup
import com.comlinkdata.emrjobs.spark.udp.iplist.MvnoIpListRunner.findMvnoCategories
import org.apache.spark.sql.functions.lower

class MvnoIpListSpec extends CldSparkBaseSpec {
  describe("MVNO Category") {
    it("should correctly assign categories") {
      import spark.implicits._
      val (vzIpList, vzIpLength) = loadIpRangeLookup(Seq("255,255", "254,254").toDS, "Verizon")
      val (attIpList, attIpLength) = loadIpRangeLookup(Seq("1,2,3", "4,5,6").toDS, "AT&T")
      val (tmoIpList, tmoIpLength) = loadIpRangeLookup(Seq("10,20", "10,30").toDS, "TMO Boost")
      val input = Seq(
        ("ffff0102", " Verizon ", "Cellular"),
        ("fefe0102", " Verizon ", "Cellular"),
        ("feff0102", " Verizon ", "Cellular"),
        ("ffff0103", " Verizon ", "Cable/DSL"),
        ("01020304", " AT&T ", "Cellular"),
        ("04050607", " AT&T ", "Cellular"),
        ("02030405", " AT&T ", "Cellular"),
        ("01020305", " AT&T ", "Cable/DSL"),
        ("0a140102", " T-Mobile ", "Cellular"),
        ("0a1e0102", " T-Mobile ", "Cellular"),
        ("0a151411", " T-Mobile ", "Cellular"),
        ("0a140103", " T-Mobile ", "Cable/DSL"),
        ("0a140102", " TMobile ", "Cellular"),
        ("0a1e0102", " TMobile ", "Cellular"),
        ("0a151411", " TMobile ", "Cellular"),
        ("0a140103", " TMobile ", "Cable/DSL"),
        ("0a140102", " Sprint ", "Cellular"),
        ("0a1e0102", " Sprint ", "Cellular"),
        ("0a151411", " Sprint ", "Cellular"),
        ("0a140103", " Sprint ", "Cable/DSL")
      ).toDF("ip", "carrier", "connection_type")
      findMvnoCategories(vzIpList, vzIpLength, attIpList, attIpLength, tmoIpList, tmoIpLength)(input)
        .map { row => row.getAs[String]("ip") -> row.getAs[String]("carrier") }.collect.toSeq shouldBe Seq(
        ("ffff0102", " Verizon "),
        ("fefe0102", " Verizon "),
        ("01020304", " AT&T "),
        ("04050607", " AT&T "),
        ("0a140102", " T-Mobile "),
        ("0a1e0102", " T-Mobile "),
        ("0a140102", " TMobile "),
        ("0a1e0102", " TMobile "),
        ("0a140102", " Sprint "),
        ("0a1e0102", " Sprint "))
    }
  }
}
