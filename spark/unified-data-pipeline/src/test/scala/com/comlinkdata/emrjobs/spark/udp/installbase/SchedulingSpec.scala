package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.commons.testing.CldBaseSpec

import java.time.LocalDate

class SchedulingSpec extends CldBaseSpec {

  describe("date tests") {
    it("nextQ") {
      Scheduling.nextQ(LocalDate.of(2021, 12, 15)) shouldBe LocalDate.of(2022,1,1)
      Scheduling.nextQ(LocalDate.of(2022, 3, 15)) shouldBe LocalDate.of(2022,4,1)
      Scheduling.nextQ(LocalDate.of(2022, 6, 15)) shouldBe LocalDate.of(2022,7,1)
      Scheduling.nextQ(LocalDate.of(2022, 9, 15)) shouldBe LocalDate.of(2022,10,1)
    }

  }

}
