package com.comlinkdata.emrjobs.spark.udp.maxmind

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.commons.WithResource

import java.net.{URI, URL}
import java.nio.file.Paths
object MaxmindIpLocationDaoSpec {
  val sampleLoc: URL = this.getClass.getResource("output_maxmind_location_ipv4_sample.csv")
}
class MaxmindIpLocationDaoSpec extends CldSparkBaseSpec {

//  private val sampleURL = this.getClass.getResource("output_maxmind_location_ipv4_sample.csv")

  //  def sampleLoc: URI = URI.create("file:/Users/<USER>/Downloads/output_maxmind_location_ipv4.csv")
  def sampleLoc: URI = MaxmindIpLocationDaoSpec.sampleLoc.toURI

  lazy val expectedSize = WithResource.readLines(sampleLoc).size

  lazy val raw = MaxmindIpLocationDao.load(sampleLoc).cache()

  it("has resource") {
    MaxmindIpLocationDaoSpec.sampleLoc should not be null
  }

  describe("load") {

    import spark.implicits._

    it("can read csv into schema") {
      noException should be thrownBy raw.showToString
    }

    it("doesnt have null sat provider") {
      // shows all records were read in without being malformed since it's the last column
      val result = raw.filter('is_satellite_provider.isNull)
      result.count shouldBe 0
    }
  }
}
