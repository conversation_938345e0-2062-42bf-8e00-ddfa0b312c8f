package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.installbase.prefilter.DataGen.ips
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, UriDFTableRW}
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.location.{LocationStat, Point}
import com.comlinkdata.largescale.schema.udp.tier1.{UdpIfaObservation, UdpDailyIfa}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource

import java.net.URI
import java.time.LocalDate

class VZWIfaToLocationsJobSpec extends CldSparkBaseSpec {
  import spark.implicits._

  private val startDate = LocalDate.of(2021, 10, 15)
  private val ifas: Array[Ifa] = (32 to 62).map(x => Array(x.toByte)).toArray[Array[Byte]]

  private val carrierLookupPath = new URI("table://vzwIfaLocCL")
  private val carrierLookupFwPath = new URI("table://vzwIfaLocCL_FW")
  private val ipv6TruncationLookupPath = new URI("table://vzwIfaTrun")
  private val hourWeightLookupPath = new URI("table://vzwIfaHour")
  private val verizonFwIpLocation = new URI("table://vzwIfaVZWips")

  private val vzwIfaObsPath = new URI("table://vzwIfaObs")
  private val vzwMaxMindPath = new URI("table://vzwMM")

  private val outpath = new URI("table://vzwIfaOutPath")

  private val vzwIfaToIpData = Seq(
    UdpDailyIfa(
      ds = "mw05",
      year = startDate.getYearString,
      month = startDate.getMonthValueString,
      day = startDate.getDayOfMonthString,
      partition_date = startDate.toDate,
      ifa = ifas(2),
      observations = Seq(
        UdpIfaObservation(
          t_utc = DataGen.toTs("2021-10-15 19:33:09.000"),
          t_local = Some(DataGen.toTs("2021-10-15 14:33:09.000")),
          ip = Some(ips(2)),
          location = Point(41.0743f, -73.4804f),
          location_type = "GPS",
          connection_type = Some("NA"),
          accuracy = Some(45.0f),
          gps_speed = None,
          place_id = Some(16224257))),
      observation_count = 1,
      unique_ip_count = 1,
      location_stats = Map.empty[String, LocationStat]
    ),
    UdpDailyIfa(
      ds = "mw05",
      year = startDate.getYearString,
      month = startDate.getMonthValueString,
      day = startDate.getDayOfMonthString,
      partition_date = startDate.toDate,
      ifa = ifas(3),
      observations = Seq(
        UdpIfaObservation(
          t_utc = DataGen.toTs("2021-10-15 19:33:09.000"),
          t_local = Some(DataGen.toTs("2021-10-15 14:33:09.000")),
          ip = Some(ips(3)),
          location = Point(41.0743f, -73.4804f),
          location_type = "GPS",
          connection_type = Some("NA"),
          accuracy = Some(45.0f),
          gps_speed = None,
          place_id = Some(16224257))),
      observation_count = 1,
      unique_ip_count = 1,
      location_stats = Map.empty[String, LocationStat]
    ))

  private val vzwMaxmindData = Seq(
    DataGen.Maxmind(ips(2), "Verizon Wireless", "Cable/DSL", "Verizon Wireless", "1", "mw05", "4", "2021", "10", "15"),
    DataGen.Maxmind(ips(3), "x Wireless", "Cable/DSL", "Verizon Wireless", "1", "mw05", "4", "2021", "10", "15"),
    DataGen.Maxmind(ips(3), "x Wireless", "Cable/DSL", "Verizon Wireless", "1", "mw05", "6", "2021", "10", "15")
  )

  describe("Testing") {
    it("Run Job with Verizon") {
      DataGen.writeCarrierLookup(carrierLookupPath)
      DataGen.writeCarrierLookupFw(carrierLookupFwPath)
      DataGen.writeIpv6Lookup(ipv6TruncationLookupPath)
      DataGen.writeHourWeight(hourWeightLookupPath)
      DataGen.writeVerizonFwIp(verizonFwIpLocation)

      val tslObs = TimeSeriesLocation
        .ofYmdDatePartitions(vzwIfaObsPath)
        .withPartition(ComlinkdataDatasource.mw05)
        .build.partition(startDate)

      UriDFTableRW.fromStr(tslObs).writeSeq(vzwIfaToIpData)

      val vzwMaxmindDataDf = spark.createDataFrame(vzwMaxmindData)

      for(v <- Seq(4, 6)) {
        val tslMM = TimeSeriesLocation
          .ofYmdDatePartitions(vzwMaxMindPath)
          .withPartition(ComlinkdataDatasource.mw05)
          .withPartition(s"ipversion=$v")
          .build.partition(startDate)
        UriDFTableRW.fromStr(tslMM).write(vzwMaxmindDataDf.where($"ipversion" === s"$v"))
      }

      val config = VZWIfaToLocationsConfig(
        ifaObsPath = vzwIfaObsPath,
        maxmindPath = vzwMaxMindPath,
        carrierLookupPath = carrierLookupPath,
        carrierLookupFwPath = carrierLookupFwPath,
        ipv6TruncationLookupPath = ipv6TruncationLookupPath,
        hourWeightLookupPath = hourWeightLookupPath,
        dailyIpCarrierPath = None,
        verizonFwIpLocation = verizonFwIpLocation,
        datasources = Seq(ComlinkdataDatasource.mw05),
        startDate = startDate,
        endDate = startDate,
        repartition = Some(1),
        ifaToLocationOutPath = outpath
      )

      VZWIfaToLocationsRunner.runJob(config)

      val ipLocation = UriDFTableRW(config.ifaToLocationOutPath).readBasePath()
      ipLocation.count shouldBe 1
      ipLocation.show
    }
  }
}
