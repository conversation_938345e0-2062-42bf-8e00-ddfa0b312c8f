package com.comlinkdata.emrjobs.spark.udp.tier1

import java.sql.{Timestamp, Date}
import java.time.LocalDate
import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.tier0.UdpComboJobDatasourceAdapter
import com.comlinkdata.emrjobs.spark.udp.tier0.UdpComboJobDatasourceAdapter.SingleSourceConfig
import com.comlinkdata.largescale.commons.parsing.JsonUtils
import com.comlinkdata.largescale.schema.udp.tier0.UdpRaw
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.Dataset

import java.net.URI

class UdpDailyDeviceJobSpec extends CldSparkBaseSpec {

  val singleSourceConfig = SingleSourceConfig(URI create "s3://some-bucket/path/")
  val configMap = JsonUtils.fromJson[Map[String, Any]](JsonUtils.toJson(singleSourceConfig))
  val mw1ksampleURL = getClass.getResource("mw-sample-1k.snappy.parquet")
  val reveal1ksampleURL = getClass.getResource("reveal-sample-1k.snappy.parquet")
  val startDateLocalDate: LocalDate = LocalDate.of(2020,6,6)
  val endDateLocalDate: LocalDate = LocalDate.of(2020,6,6)
  val startDate: Date = Date.valueOf(startDateLocalDate)
  val endDate: Date = startDate
  val datasourceReveal = ComlinkdataDatasource.reveal
  val datasourceMobileWalla = ComlinkdataDatasource.mw
  val adapterReveal = UdpComboJobDatasourceAdapter.forDatasource(datasourceReveal, configMap)
  val adapterMobileWalla = UdpComboJobDatasourceAdapter.forDatasource(datasourceMobileWalla, configMap)
  val udpRawAllSeq: Seq[UdpRaw] =
    Seq(
      UdpRaw(
        date = Date.valueOf("2020-06-06"), // java.sql.Date,
        ifa = Array[Byte](192.toByte, 168.toByte, 1, 9), // Ifa,
        ip = Some(Array[Byte](192.toByte, 168.toByte, 1, 9)), // Ip,
        useragent = Some("useragent"), // Option[String],
        carrier = Some("ATT"), // Option[String],
        newcarrier = Some("ATT"), // Option[String],
        latitude = Some(10f), // Float, // from Reveal lat
        longitude = Some(10f), // Float, // from Reveal lon
        locationtype = Some("String"), // Option[String], // from Reveal event_type
        suspiciouslocation = Some(false), // Option[Boolean],
        devicetype = Some("devicetype"), // Option[String],
        make = Some("Galaxy"),
        model = Some("Not-Apple"),
        connectiontype = Some("WIFI"), // Option[String], // from reveal connection_type
        datetime = Timestamp.valueOf("2020-06-06 12:00:01"), // java.sql.Timestamp, // from reveal utc_timestamp
        appid = Some("string"), // Option[String], // from reveal app_id
        appname = None, // Option[String],
        os = Some("os"), // Option[String],
        accuracy = Some(10f), // Option[Float],
        gps_speed = Some(10f), // Option[Float],
        place_name = Some("burgerking"), // Option[String],
        place_id = Some(1), // Option[Int],
        category = Some("string"), // Option[String],
        country_iso3 = Some("string"), // Option[String],
        localdatetime = Some(Timestamp.valueOf("2020-06-06 12:00:01"))),
      UdpRaw(
        date = Date.valueOf("2020-06-06"), // java.sql.Date,
        ifa = Array[Byte](192.toByte, 168.toByte, 1, 9), // Ifa,
        ip = Some(Array[Byte](192.toByte, 168.toByte, 1, 9)), // Ip,
        useragent = Some("useragent"), // Option[String],
        carrier = Some("ATT"), // Option[String],
        newcarrier = Some("ATT"), // Option[String],
        latitude = Some(10f), // Float, // from Reveal lat
        longitude = Some(10f), // Float, // from Reveal lon
        locationtype = Some("String"), // Option[String], // from Reveal event_type
        suspiciouslocation = Some(false), // Option[Boolean],
        devicetype = Some("devicetype"), // Option[String],
        make = Some("Galaxy"),
        model = Some("Not-Apple"),
        connectiontype = Some("WIFI"), // Option[String], // from reveal connection_type
        datetime = Timestamp.valueOf("2020-06-06 12:00:01"), // java.sql.Timestamp, // from reveal utc_timestamp
        appid = Some("string"), // Option[String], // from reveal app_id
        appname = None, // Option[String],
        os = Some("os"), // Option[String],
        accuracy = Some(10f), // Option[Float],
        gps_speed = Some(10f), // Option[Float],
        place_name = Some("burgerking"), // Option[String],
        place_id = Some(1), // Option[Int],
        category = Some("string"), // Option[String],
        country_iso3 = Some("string"), // Option[String],
        localdatetime = Some(Timestamp.valueOf("2020-06-06 12:00:01"))) // Option[java.sql.Timestamp]
    )


  it("can create a daily device set from udp raw all with 2 manual records") {
    import spark.implicits._
    val df_1: Dataset[UdpRaw] = udpRawAllSeq.toDS
    val result = df_1.transform(UdpDailyDeviceRunner.createDeviceDataset(datasourceReveal, _))
    result.show
  }

  it("can create a daily device set from reveal with 1k sample file") {
    import spark.implicits._
    val df = spark.read.parquet(reveal1ksampleURL.toString)
      .withColumn("date", lit(Date.valueOf("2020-06-06"))).toDF
    val df_1 = df.transform(adapterReveal.rawToUdpRawDataset(startDateLocalDate, endDateLocalDate, _)).filter($"useragent".isNotNull)
    val df_1_filtered = df_1
    val result = df_1_filtered.transform(UdpDailyDeviceRunner.createDeviceDataset(datasourceReveal, _))
    result.show
  }

  it("can create a daily device set from mw with 1k sample file") {
    val df = spark.read.parquet(mw1ksampleURL.toString)
      .withColumn("date", lit(Date.valueOf("2020-06-06"))).toDF
    val df_1 = df.transform(adapterMobileWalla.rawToUdpRawDataset(startDateLocalDate, endDateLocalDate, _))
    val result = df_1.transform(UdpDailyDeviceRunner.createDeviceDataset(datasourceMobileWalla, _))
    result.show
  }

  it("collapses unique devices when they match") {
    val mwRawDF = spark.read.parquet(mw1ksampleURL.toString)
      .withColumn("date", lit(Date.valueOf("2020-06-06"))).toDF
    val mwRawDs = mwRawDF.transform(adapterMobileWalla.rawToUdpRawDataset(startDateLocalDate, endDateLocalDate, _))
    val input: Dataset[UdpRaw] = mwRawDs union mwRawDs
    val result_expected = UdpDailyDeviceRunner.createDeviceDataset(ComlinkdataDatasource.mw, mwRawDs)(spark)
    val result_input = UdpDailyDeviceRunner.createDeviceDataset(ComlinkdataDatasource.mw, input)(spark)
    result_expected.count shouldBe result_input.count
  }
}
