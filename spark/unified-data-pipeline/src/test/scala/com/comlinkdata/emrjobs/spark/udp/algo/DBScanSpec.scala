package com.comlinkdata.emrjobs.spark.udp.algo

import com.comlinkdata.commons.testing.DataHelperFunctions
import com.comlinkdata.largescale.schema.udp.location.Point
import org.scalatest.{FunSpecLike, BeforeAndAfter, PrivateMethodTester, FunSpec, Matchers}

class DBScanSpec extends FunSpec
  with FunSpecLike
  with Matchers
  with PrivateMethodTester
  with DataHelperFunctions
  with BeforeAndAfter {

  val testPts = IndexedSeq(
    (190.47f, 261.84f),
    (192.56f, 199.05f),
    (190.47f, 138.35f),
    (205.12f, 111.14f),
    (196.74f, 171.84f),
    (211.4f,  234.63f),
    (190.47f, 305.79f),
    (188.37f, 347.65f),
    (221.86f, 347.65f),
    (265.81f, 347.65f),
    (297.21f, 316.26f),
    (297.21f, 263.93f),
    (288.84f, 207.42f),
    (282.56f, 161.37f),
    (249.07f, 113.23f),
    (567.21f, 132.07f),
    (567.21f, 165.56f),
    (567.21f, 217.88f),
    (567.21f, 238.81f),
    (567.21f, 278.58f),
    (558.84f, 318.35f),
    (556.74f, 362.3f),
    (569.3f,  136.26f),
    (602.79f, 134.16f),
    (640.47f, 134.16f),
    (644.65f, 137.3f),
    (671.86f, 180.21f),
    (665.58f, 203.23f),
    (638.37f, 217.88f),
    (621.63f, 217.88f),
    (609.07f, 222.07f),
    (590.23f, 226.26f),
    (1000.0f, 1000.0f)
  )

  val testBadGPS1 = IndexedSeq(
    Point(33.5879f, -86.3384f),
    Point(33.285187f, -85.282745f),
    Point(33.285187f, -85.282715f),
    Point(33.5879f, -86.3384f),
    Point(33.5879f, -86.3384f))

  val testBadGPS2= IndexedSeq(
    Point(21.4254f, -157.8138f),
    Point(26.176365f, -80.224594f),
    Point(21.4254f, -157.8138f),
    Point(26.176365f, -80.224594f),
    Point(35.6211f, -97.4757f),
    Point(26.176498f, -80.224625f),
    Point(26.17653f, -80.22482f),
    Point(26.176544f, -80.22481f),
    Point(26.176407f, -80.2246f),
    Point(26.17639f, -80.22463f),
    Point(26.17639f, -80.22463f),
    Point(26.176392f, -80.224625f),
    Point(26.176392f, -80.224625f),
    Point(26.1764f, -80.22461f),
    Point(26.176392f, -80.224625f),
    Point(26.176392f, -80.224625f),
    Point(26.17639f, -80.22463f),
    Point(26.17639f, -80.22463f),
    Point(26.17639f, -80.22462f),
    Point(26.176392f, -80.224625f),
    Point(26.17639f, -80.22462f),
    Point(26.17639f, -80.22463f),
    Point(26.176392f, -80.224625f),
    Point(26.17639f, -80.22463f),
    Point(26.176392f, -80.224625f),
    Point(26.176392f, -80.224625f),
    Point(26.176392f, -80.224625f),
    Point(26.176392f, -80.224625f),
    Point(26.17639f, -80.22463f),
    Point(26.176392f, -80.224625f),
    Point(26.17639f, -80.22463f),
    Point(26.17639f, -80.22462f),
    Point(26.17639f, -80.22462f),
    Point(26.176392f, -80.224625f),
    Point(26.17431f, -80.23236f))


    //IP 20 01 05 b0 47 c7 00 00 00 00 00 00 00 00 00 00
    //2021	10	02
    val testBadGPS3 = IndexedSeq(
      Point(32.945236f, -87.15225f),
      Point(34.19592f, -88.49848f),
      Point(32.94524f, -87.15227f),
      Point(32.94524f, -87.15226f),
      Point(33.04202f, -89.59194f),
      Point(33.04202f, -89.59194f),
      Point(33.011032f, -89.71921f),
      Point(33.01104f, -89.71921f),
      Point(33.011032f, -89.71921f))

  //43 8e 70 f3
  //2021	10	02
  val testBadGPS4 = IndexedSeq(
    Point(38.5774f, -90.6709f),
    Point(30.8244f, -90.77027f),
    Point(36.6437f, -93.2185f),
    Point(38.5774f, -90.6709f),
    Point(38.5774f, -90.6709f),
    Point(38.57739f, -90.6709f),
    Point(38.58171f, -90.66381f),
    Point(36.643692f, -93.21851f),
    Point(37.751f, -97.822f),
    Point(36.6437f, -93.2185f),
    Point(30.8244f, -90.77027f),
    Point(36.643692f, -93.21851f),
    Point(38.58171f, -90.66382f),
    Point(36.643692f, -93.21851f),
    Point(30.8244f, -90.77027f),
    Point(38.58171f, -90.66381f)
  )

  val testBadGPS5Repeats = IndexedSeq(
    Point(42.42887f, -83.08115f),
    Point(42.428818f, -83.08125f),
    Point(42.428818f, -83.08125f),
    Point(42.42881f, -83.08125f),
    Point(42.428818f, -83.08125f),
    Point(42.42881f, -83.08125f),
    Point(42.428818f, -83.08125f),
    Point(42.428818f, -83.08125f),
    Point(42.42882f, -83.08126f),
    Point(42.428818f, -83.08125f),
    Point(42.428818f, -83.08125f),
    Point(42.428818f, -83.08125f),
    Point(42.42881f, -83.08125f),
    Point(42.42882f, -83.08126f),
    Point(42.42882f, -83.08126f),
    Point(42.42882f, -83.08126f),
    Point(42.42881f, -83.08125f),
    Point(42.42881f, -83.08125f),
    Point(42.428818f, -83.08125f),
    Point(42.428818f, -83.08125f),
    Point(42.42882f, -83.08126f),
    Point(42.42881f, -83.08125f),
    Point(42.428818f, -83.08125f),
    Point(42.428818f, -83.08125f),
    Point(42.42882f, -83.08126f),
    Point(42.42882f, -83.08126f),
    Point(42.42882f, -83.08126f),
    Point(42.42888f, -83.08122f),
    Point(42.428814f, -83.08115f),
    Point(42.42881f, -83.08115f),
    Point(42.428814f, -83.08115f),
    Point(42.42882f, -83.08116f),
    Point(42.42882f, -83.08116f),
    Point(42.42882f, -83.08116f),
    Point(42.42882f, -83.08116f),
    Point(42.42882f, -83.08116f),
    Point(42.42882f, -83.08116f),
    Point(42.428814f, -83.08115f),
    Point(42.428814f, -83.08115f),
    Point(42.42881f, -83.08115f),
    Point(42.428814f, -83.08115f),
    Point(42.428814f, -83.08115f),
    Point(42.42881f, -83.08115f),
    Point(42.42882f, -83.08116f),
    Point(42.42882f, -83.08116f),
    Point(42.42882f, -83.08116f),
    Point(42.42887f, -83.08121f),
    Point(42.42887f, -83.08121f),
    Point(42.42887f, -83.08121f),
    Point(42.42887f, -83.08121f),
    Point(42.42888f, -83.08122f),
    Point(42.42888f, -83.08122f),
    Point(42.42888f, -83.08122f),
    Point(42.42888f, -83.08122f),
    Point(42.42888f, -83.08122f),
    Point(42.42888f, -83.08122f),
    Point(45.6789f, -89.1011f),
    Point(45.6789f, -89.1011f),
    Point(45.6789f, -89.1011f),
    Point(45.6789f, -89.1011f),
    Point(45.6789f, -89.1011f)
  )

  describe("DB Scan") {
    it("test non GPS") {
      val (labels, clusterStats) = DBScan.cluster2DPts(testPts, 100, 3)

      clusterStats.size shouldEqual 2
      for (i <- 0 to 14) {
        labels(i) shouldEqual 1
      }
      for (i <- 15 to 31) {
        labels(i) shouldEqual 2
      }
      labels(32) shouldEqual -1

      clusterStats.foreach(println)
      labels.indices.foreach(i => println(i, labels(i)))
    }

    it("test GPS1") {
      val (labels, clusterStats) = DBScan.clusterGPS(testBadGPS1, 1000, 3)
      clusterStats.foreach(println)
      labels.indices.foreach(i => println(i, labels(i)))
      clusterStats.size shouldEqual 1
    }

    it("test GPS2") {
      val (labels, clusterStats) = DBScan.clusterGPS(testBadGPS2, 1000, 3)
      clusterStats.foreach(println)
      labels.indices.foreach(i => println(i, labels(i)))
    }

    it("test GPS3") {
      val (labels, clusterStats) = DBScan.clusterGPS(testBadGPS3, 1000, 3)
      clusterStats.foreach(println)
      labels.indices.foreach(i => println(i, labels(i)))
      labels(0) = 1
      labels(1) = -1
      labels(2) = 1
      labels(3) = 1
      labels(4) = -1
      labels(5) = -1
      labels(6) = 2
      labels(7) = 2
      labels(8) = 2
    }

    it("test GPS4") {
      val (labels, clusterStats) = DBScan.clusterGPS(testBadGPS4, 1000, 3)
      clusterStats.foreach(println)
      if (System.getProperty("user.name").contains("parks")) {
        printKML(labels, testBadGPS4)
      }

      clusterStats.head._1 shouldEqual Point(38.579247f,-90.66786f)
      clusterStats(1)._1 shouldEqual Point(30.8244f,-90.77027f)
      clusterStats(2)._1 shouldEqual Point(36.643696f,-93.218506f)
    }

    it("test GPS5") {
      val (_, _, clusterCentroids) = DBScan.clusterGPSWithBinning(testBadGPS5Repeats, 1000, 3)
      clusterCentroids.size shouldBe 2


      val (topPoints, _, clusterCentroidsTopK) = DBScan.clusterGPSWithBinningTopK(testBadGPS5Repeats, 1000, 3, Some(1))
      clusterCentroidsTopK.size shouldBe 1
      topPoints.head._1 shouldBe 12
    }
  }

  private def printKML(labels: Seq[Int], pts: Seq[Point.Float]): Unit = {
    println(
      """
        |<?xml version="1.0" encoding="UTF-8"?>
        |<kml xmlns="http://www.google.com/earth/kml/2">
        |<Document>
        |  <name>kml_sample2.kml</name>
        |
        |<Style id="red">
        |  <IconStyle>
        |    <Icon>
        |      <href>http://www.google.com/intl/en_us/mapfiles/ms/icons/red-dot.png</href>
        |    </Icon>
        |  </IconStyle>
        |</Style>
        |
        |<Style id="green">
        |  <IconStyle>
        |    <Icon>
        |      <href>http://www.google.com/intl/en_us/mapfiles/ms/icons/green-dot.png</href>
        |    </Icon>
        |  </IconStyle>
        |</Style>
        |
        |<Style id="blue">
        |  <IconStyle>
        |    <Icon>
        |      <href>http://www.google.com/intl/en_us/mapfiles/ms/icons/blue-dot.png</href>
        |    </Icon>
        |  </IconStyle>
        |
        |</Style>
        |
        |  <Style id="yellow">
        |  <IconStyle>
        |    <Icon>
        |      <href>https://www.google.com/intl/en_us/mapfiles/ms/icons/yellow-dot.png</href>
        |    </Icon>
        |  </IconStyle>
        |  </Style>
        |""".stripMargin)
    labels.indices.foreach(i => {
      val name = s"{i}"
      val color = labels(i) match {
        case 1=> "blue"
        case 2=> "green"
        case -1=> "red"
        case 3=> "yellow"
      }
      val lat = pts(i).lat
      val lng = pts(i).lng
      println(s"""
                 |  <Placemark>
                 |    <name>$name</name>
                 |    <styleUrl>#$color</styleUrl>
                 |    <Point>
                 |      <coordinates>$lng,$lat</coordinates>
                 |    </Point>
                 |  </Placemark>
                 |""".stripMargin)
    })
    println(
      """
        |</Document>
        |</kml>
        |""".stripMargin)
  }
}
