package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.{UriDFTableRW, Utils}
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.broadband_market_share.lookup.VerizonFwIp
import com.comlinkdata.largescale.schema.udp.Ip
import com.comlinkdata.largescale.schema.udp.installbase.IfaToIpWithObs
import com.comlinkdata.largescale.schema.udp.location.Point
import com.comlinkdata.largescale.schema.udp.lookup.{HourWeightLookUp, ipv6TruncateLookup}
import com.comlinkdata.largescale.schema.udp.tier1.UdpIfaObservation
import org.apache.spark.sql.{Dataset, SparkSession}

import java.net.URI
import java.sql.Timestamp
import java.time.LocalDate

object DataGen {
  val startDate = LocalDate.of(2021, 11, 20)
  val endDate = startDate.plusDays(1)

  case class Maxmind(
    ip: Ip,
    carrier: String,
    connection_type: String,
    organization: String,
    autonomous_system_number: String,
    ds: String,
    ipversion: String,
    year: String,
    month: String,
    day: String
  )

  private val ip0 = Utils.ipStringToBinary("**************")

  def toTs(s: String): Timestamp = {
    Timestamp.valueOf(s)
  }

  def exampleIpToIfa()(implicit spark: SparkSession): Dataset[IfaToIpWithObs] = {
    import spark.implicits._
    spark.createDataFrame(
      Seq(IfaToIpWithObs(
        ip = ip0,
        ifa = ip0,
        carrier = "Fake",
        consolidated_id = "1234",
        sp_platform = "1234",
        organization = "Fake",
        autonomous_system_number = "1",
        obs_count = 25,
        night_obs_count = 4,
        household_obs_count = 4,
        household_weighted_obs_count = 4,
        business_weighted_obs_count = 0,
        connection_type = "Cable/DSL",
        observations = Seq(
          UdpIfaObservation(
            t_utc=toTs("2021-10-15 19:33:09.000"),
            t_local=Some(toTs("2021-10-15 14:33:09.000")),
            ip=Some(ip0),
            location=Point(41.0743f, -73.4804f),
            location_type="GPS",
            connection_type=Some("NA"),
            accuracy=Some(45.0f),
            gps_speed=None,
            place_id=Some(16224257)),
          UdpIfaObservation(
            t_utc=toTs("2021-10-15 19:33:09.000"),
            t_local=Some(toTs("2021-10-15 14:33:09.000")),
            ip=Some(ip0),
            location=Point(41.0743f, -73.4804f),
            location_type="GPS",
            connection_type=Some("NA"),
            accuracy=Some(45.0f),
            gps_speed=None,
            place_id=Some(16224257)),
          UdpIfaObservation(
            t_utc=toTs("2021-10-15 23:01:21.000"),
            t_local=Some(toTs("2021-10-15 18:01:21.000")),
            ip=Some(ip0),
            location=Point(41.0743f, -73.4804f),
            location_type="GPS",
            connection_type=Some("NA"),
            accuracy=Some(35.0f),
            gps_speed=None,
            place_id=Some(16224257)),
          UdpIfaObservation(
            t_utc=toTs("2021-10-15 23:01:21.000"),
            t_local=Some(toTs("2021-10-15 18:01:21.000")),
            ip=Some(ip0),
            location=Point(41.0743f, -73.4804f),
            location_type="GPS",
            connection_type=Some("NA"),
            accuracy=Some(35.0f),
            gps_speed=None,
            place_id=Some(16224257))
        )
      ))).as[IfaToIpWithObs]
  }

  val carrierLookupData = Seq (
    CarrierLookup("AT&T Services", "AT&T Internet Services", "10011", Some("AT&T Inc."), "123", "123", "2010-11-01"),
    CarrierLookup("AT&T Services", "AT&T Internet Services", "10011", Some("AT&T Inc."), "398", "398", "2016-11-01"),
    CarrierLookup("CenturyLink", "CenturyLink", "10023", Some("CenturyLink, Inc."), "567", "567", "2010-11-01"),
    CarrierLookup("CenturyLink", "CenturyLink", "10023", Some("CenturyLink, Inc."), "858", "858", "2016-11-01"),
    CarrierLookup("Comcast Cable", "Comcast", "10027", Some("Comcast Corporation"), "1009", "1009", "2016-11-01"),
    CarrierLookup("Cox Communications", "Cox Communications", "10031", Some("Cox Communications, Inc."), "8910", "8910", "2010-11-01"),
    CarrierLookup("Cox Communications", "Cox Communications", "10031", Some("Cox Communications, Inc."), "1170", "1170", "2016-11-01"),
    CarrierLookup("GCI Communications", "General Communications", "10043", null, "1829", "1829", "2016-11-01"),
    CarrierLookup("Home Telephone Company", "Home Telephone Company", "10051", null, "2118", "2118", "2016-11-01"),
    CarrierLookup("Mediacom Cable", "Mediacom Cable", "10059", null, "2724", "2724", "2016-11-01"),
    CarrierLookup("Zona Communications", "Zona Communications", "10632", null, "5245", "5245", "2016-11-01"),
    CarrierLookup("Optimum Online", "Altice", "10007", Some("Altice"), "6143", "6143", "2016-11-01"),
    CarrierLookup("Giggle Fiber", "Giggle Fiber", "10271", null, "6294", "6294", "2016-11-01"),
    CarrierLookup("UBTANET", "UBTANET", "10577", null, "6458", "6458", "2016-11-01"),
    CarrierLookup("Verizon Wireless", "UBTANET", "10577", null, "6458", "6458", "2016-11-01")
  )

  val carrierLookupFwData = Seq(
    CarrierLookup("T-Mobile USA", "T-Mobile USA", "88888", Some("T-Mobile USA"), "6713", "6713", "2016-11-01"),
    CarrierLookup("Verizon Wireless", "Verizon Wireless", "99999", Some("Verizon Communications Inc."), "6715", "6715", "2016-11-01")
  )

  val ipv6LookupData = Seq(
    ipv6TruncateLookup("Fake", "Fake", 10L),
    ipv6TruncateLookup("AT&T Services", "AT&T Internet Services", 10L),
    ipv6TruncateLookup("CenturyLink", "CenturyLink", 10L),
    ipv6TruncateLookup("Comcast Cable", "Comcast", 10L),
    ipv6TruncateLookup("Cox Communications", "Cox Communications", 10L),
    ipv6TruncateLookup("GCI Communications", "General Communications", 10L),
    ipv6TruncateLookup("Home Telephone Company", "Home Telephone Company", 10L),
    ipv6TruncateLookup("Mediacom Cable", "Mediacom Cable", 10L),
    ipv6TruncateLookup("Zona Communications", "Zona Communications", 10L),
    ipv6TruncateLookup("Optimum Online", "Altice", 10L),
    ipv6TruncateLookup("Giggle Fiber", "Giggle Fiber", 10L),
    ipv6TruncateLookup("UBTANET", "UBTANET", 10L)
  )

  val hourWeightData = Seq(
    HourWeightLookUp("0", 0.*********, 0.0),
    HourWeightLookUp("1", 1.0, 0.0),
    HourWeightLookUp("2", 0.*********, 0.0),
    HourWeightLookUp("3", 0.*********, 0.0),
    HourWeightLookUp("4", 0.*********, 0.0),
    HourWeightLookUp("5", 0.*********, 0.0),
    HourWeightLookUp("6", 0.*********, 0.0),
    HourWeightLookUp("7", 0.0, 0.0),
    HourWeightLookUp("8", 0.0, 0.0),
    HourWeightLookUp("9", 0.0, 0.25),
    HourWeightLookUp("10", 0.0, 0.5),
    HourWeightLookUp("11", 0.0, 0.75),
    HourWeightLookUp("12", 0.0, 1.0),
    HourWeightLookUp("13", 0.0, 0.75),
    HourWeightLookUp("14", 0.0, 0.5),
    HourWeightLookUp("15", 0.0, 0.25),
    HourWeightLookUp("16", 0.0, 0.0),
    HourWeightLookUp("17", 0.0, 0.0),
    HourWeightLookUp("18", 0.25, 0.0),
    HourWeightLookUp("19", 0.357142857, 0.0),
    HourWeightLookUp("20", 0.*********, 0.0),
    HourWeightLookUp("21", 0.*********, 0.0),
    HourWeightLookUp("22", 0.*********, 0.0),
    HourWeightLookUp("23", 0.*********, 0.0)
  )

  val ips: Array[Ip] = (32 to 62).map(x => Array(192.toByte, 168.toByte, 0.toByte, x.toByte)).toArray[Array[Byte]]

  val verizonFwIpData = Seq(VerizonFwIp(ip_2_octets = ips(2)(0) + "." + ips(2)(1), ssid_bssid_count = 1L, startDate.toDate))

  def writeCarrierLookup(path: URI)(implicit spark: SparkSession): Unit = {
    UriDFTableRW(path).writeSeq(carrierLookupData)
  }

  def writeCarrierLookupFw(path: URI)(implicit spark: SparkSession): Unit = {
    UriDFTableRW(path).writeSeq(carrierLookupFwData)
  }

  def writeIpv6Lookup(path: URI)(implicit spark: SparkSession): Unit = {
    UriDFTableRW(path).writeSeq(ipv6LookupData)
  }

  def writeHourWeight(path: URI)(implicit spark: SparkSession): Unit = {
    UriDFTableRW(path).writeSeq(hourWeightData)
  }

  def writeVerizonFwIp(path: URI)(implicit spark: SparkSession): Unit = {
    UriDFTableRW(path).writeSeq(verizonFwIpData)
  }
}
