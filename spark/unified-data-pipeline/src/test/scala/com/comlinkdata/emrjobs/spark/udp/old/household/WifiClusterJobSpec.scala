package com.comlinkdata.emrjobs.spark.udp.old.household

import com.comlinkdata.commons.testing.CldBaseSpec

class WifiClusterJobSpec extends CldBaseSpec {
 describe("matching null") {
   it("will find a null") {
     null match {
       case null => succeed
       case _ => fail("null wasn't caught by null case.")
     }
   }
   it("will find null in tuple") {
     (null, "asdf") match {
       case (null, str) => str shouldBe "asdf"
       case _ => fail("null wasn't caught by null case.")
     }
   }
 }

  describe("adding maps") {
    val map: Map[String, Seq[Int]] = Seq("a" -> Seq(1)).toMap
    val map2: Map[String, Seq[Int]] = Seq("b" -> Seq(2)).toMap

    it("will overwrite a key") {
      map ++ map shouldBe map
    }

    it("will combine 2 keys") {
        map ++ map2 shouldBe Seq("a" -> Seq(1), "b" -> Seq(2)).toMap
    }
  }
}
