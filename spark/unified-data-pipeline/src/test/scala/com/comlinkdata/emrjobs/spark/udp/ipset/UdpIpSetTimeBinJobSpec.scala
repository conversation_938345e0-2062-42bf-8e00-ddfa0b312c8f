package com.comlinkdata.emrjobs.spark.udp.ipset

import com.comlinkdata.commons.testing.CldSparkBaseSpec

import java.sql.Date
import com.comlinkdata.emrjobs.spark.udp.ipset.UdpIpSetTimeBinRunner.{runJob, getDaysToRun}
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, Utils}
import com.comlinkdata.largescale.schema.udp.location.LocationStat.LocationStatsMap
import com.comlinkdata.largescale.schema.udp.location.{Rectangle, LocationStat, Point}
import com.comlinkdata.largescale.schema.udp.tier1.UdpDailyIp
import com.comlinkdata.largescale.schema.udp.tier2.{UdpIpSetTimeBins, IPSetBinnedHistory}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.catalyst.expressions.GenericRowWithSchema
import org.apache.spark.sql.{SparkSession, Dataset}
import org.junit.rules.TemporaryFolder
import org.scalamock.scalatest.MockFactory
import org.scalatest.BeforeAndAfterAll

import java.net.URI
import java.time.{DayOfWeek, LocalDate}
import scala.util.Random

class UdpIpSetTimeBinJobSpec extends CldSparkBaseSpec with LazyLogging with BeforeAndAfterAll with MockFactory {
  import spark.implicits._

  private val today = LocalDate.now()
  private val todayM120 = today.minusDays(30)

  private val pt0 = Point(lat = 36.1234f, lng= -115.1234f)
  private val pt1 = Point(lat = 28.1234f, lng= -81.1234f)

  private val someLocation = Map("IP" -> LocationStat(envelope=Rectangle(bl = pt0, tr = pt1), centroid = pt0))

  private val pt2 = Point(lat = 12f, lng= -34f)
  private val pt3 = Point(lat = 56f, lng= -78f)

  private val someLocationLatest = Map("IP" -> LocationStat(envelope=Rectangle(bl = pt2, tr = pt3), centroid = pt2))

  private val beforeAllTempFolder = new TemporaryFolder()

  private var prevBinnedPath: URI = null
  private var dailyipPath: URI = null
  private var outPath: URI = null
  private var binnedPath: URI = null

  object Generator {
    Random.setSeed(1)

    private def random4IP() = {
      val i1 = Random.nextInt(256)
      val i2 = Random.nextInt(256)
      val i3 = Random.nextInt(256)
      val i4 = Random.nextInt(256)
      Utils.ipStringToBinary(s"$i1.$i2.$i3.$i4")
    }

    private val ifas = (1 to 10).map(i => {
      // Use fixed length array of alphanumeric byte's so it looks pretty
      val ifa = new Array[Byte](5)
      ifa(0) = i.toByte
      ifa(1) = i.toByte
      ifa(2) = i.toByte
      ifa(3) = i.toByte
      ifa(4) = i.toByte
      ifa
    })

    private val ips = (1 to 5).map(_ => random4IP())

    def writePrevBinnedIpSet(basePath: URI, startDate: LocalDate, endDate: LocalDate, ds: ComlinkdataDatasource)(implicit spark: SparkSession): URI = {
      val ipsOut = ips.map(ip => {
        val datesToBinnedHist = Utils.dateRange(startDate, endDate).map(
          date => {
            val nIffas = 3
            val ifasThisDate = ifas.take(nIffas)
            val obcCount = 50
            val placeIDds = Seq(3l)

            (Date.valueOf(date), IPSetBinnedHistory(ifasThisDate, obcCount, nIffas, placeIDds))
          }
        ).toMap
        (ip,
          ComlinkdataDatasource.mw05.toString,
          endDate.getYear.toString,
          endDate.getMonthValue.formatted("%02d"),
          endDate.getDayOfMonth.formatted("%02d"),
          datesToBinnedHist)
      })

      val columns = Seq("ip", "ds", "year", "month", "day", "history")
      val df = spark.createDataFrame(ipsOut).toDF(columns:_*)

      val result = basePath.resolve("PrevBinnedIpSet")

      df.write
        .partitionBy("ds", "year", "month", "day")
        .parquet(result.toString)

      result
    }

    def writeRandomDailyIP(basePath: URI, startDate: LocalDate, endDate: LocalDate, ds: ComlinkdataDatasource)(implicit spark: SparkSession): URI = {
      val dailyIpsDS : Dataset[UdpDailyIp] = Utils.dateRange(startDate, endDate).flatMap(
        date => {
          val n_ifas = Random.nextInt(4) + 1
          Random.shuffle(ips).take(Random.nextInt(4) + 1).map(ip =>
            UdpDailyIp(
              ds = ds.toString,
              year = date.getYear.toString,
              month = f"${date.getMonthValue}%02d",
              day = f"${date.getDayOfMonth}%02d",
              partition_date = Date.valueOf(date),
              ip = ip,
              ifas = ifas.take(n_ifas),
              observation_count = 10,
              unique_ifa_count = n_ifas,
              place_ids = Seq(3),
              location_stats = if(date.equals(endDate)) someLocationLatest else someLocation
            ))
        }
      ).toDS()

      val result = basePath.resolve("DailyIp")
      dailyIpsDS.write
        .partitionBy("ds", "year", "month", "day")
        .parquet(result.toString)
      result
    }
  }

  override def beforeAll(): Unit = {
    super.beforeAll()

    beforeAllTempFolder.create()
    val folder = beforeAllTempFolder.newFolder()
    folder.delete()

    val basePath = folder.toURI
    logger.info(f"Writing test datasets to basePath=$basePath")
    dailyipPath = Generator.writeRandomDailyIP(basePath, today.minusDays(6), today, ComlinkdataDatasource.mw05)
    prevBinnedPath = Generator.writePrevBinnedIpSet(basePath, todayM120, today.minusDays(7), ComlinkdataDatasource.mw05)

    binnedPath = new URI(f"${basePath.toString}/IpSetBinned")
    outPath = new URI(f"${basePath.toString}/IpSetBinnedOut")
  }

  override def afterAll(): Unit = {
    beforeAllTempFolder.delete()
  }

  ignore("Full Run ok?") {
    import spark.implicits._

    val config = UdpIpSetTimeBinConfig(
      datasources = List(ComlinkdataDatasource.mw05),
      dailyIpLocation = dailyipPath,
      ipSetTimeBinsLocation = prevBinnedPath,
      ipSetTimeBinsOutLocation = Some(outPath),
      bootstrapDate = today,
      optNumDaysToRun = None
    )

    runJob(config)

    val outpathTSL = TimeSeriesLocation
      .ofYmdDatePartitions(outPath)
      .withPartition(ComlinkdataDatasource.mw05)
      .build
      .partition(today)

    val gotTimeBinnedData = spark.read.option(ReadOpts.basePath, outPath.toString)
      .parquet(outpathTSL)
      .as[UdpIpSetTimeBins]
      .orderBy("ip", "unique_ifa_count").cache()

    val loc_stats = gotTimeBinnedData.select("location_stats").take(1).head.get(0)
    val pt1X = loc_stats.asInstanceOf[LocationStatsMap]("IP").asInstanceOf[GenericRowWithSchema].get(1).asInstanceOf[GenericRowWithSchema].get(0)
    pt1X shouldEqual someLocation("IP").centroid.lat

    val resultUniqueIFACount = gotTimeBinnedData.select("unique_ifa_count").take(1).head.get(0)
    resultUniqueIFACount shouldEqual 76

    val resultPlaceIDsSize = gotTimeBinnedData.select("place_ids").take(1).head.getList[Long](0).size()
    resultPlaceIDsSize shouldEqual 1
  }

  implicit val localDateOrdering: Ordering[LocalDate] = Ordering.by(_.toEpochDay)

  it("DaysToRun bootstrap ok?") {
    val lastDayOfIpSet = LocalDate.of(2022,1,1)

    val tslIpSet = mock[TimeSeriesLocation]
    (tslIpSet.latestDate _).expects()
      .returning(lastDayOfIpSet)

    val tslBinned = mock[TimeSeriesLocation]
    (tslBinned.exists _).expects()
      .returning(false)

    val bootStrapDate = LocalDate.of(2021,12,1)

    val result = getDaysToRun(tslBinned, tslIpSet, bootStrapDate, 7, None)
    result.head shouldBe bootStrapDate

    result.foreach(x => {
      x should be >= bootStrapDate
      x should be <= lastDayOfIpSet
      x.getDayOfWeek shouldBe DayOfWeek.WEDNESDAY
    })
  }

  it("DaysToRun continue ok?") {
    val lastDayOfIpSet = LocalDate.of(2022,1,5)

    val tslIpSet = mock[TimeSeriesLocation]
    (tslIpSet.latestDate _).expects()
      .returning(lastDayOfIpSet)

    val lastBinnedRun = LocalDate.of(2021,12,1)

    val tslBinned = mock[TimeSeriesLocation]
    (tslBinned.exists _).expects()
      .returning(true)
    (tslBinned.latestDate _).expects()
      .returning(lastBinnedRun)

    val result = getDaysToRun(tslBinned, tslIpSet, LocalDate.of(2021,11,1), 7, None)
    result.head shouldBe lastBinnedRun.plusDays(7)

    result.foreach(x => {
      x should be >= lastBinnedRun
      x should be <= lastDayOfIpSet
      x.getDayOfWeek shouldBe DayOfWeek.WEDNESDAY
    })
    result.last shouldBe LocalDate.of(2022, 1, 5)
  }
}
