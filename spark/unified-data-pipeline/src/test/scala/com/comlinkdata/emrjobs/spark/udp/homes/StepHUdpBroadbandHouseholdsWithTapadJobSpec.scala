package com.comlinkdata.emrjobs.spark.udp.homes

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.commons.Utils
import org.apache.spark.sql.{DataFrame, Dataset}


class UdpHhStepHBroadbandHouseholdsJobSpec extends CldSparkBaseSpec {

  import UdpHhStepHBroadbandHouseholdsSchema._
  import UdpHhStepGCldBroadbandHouseholdsSchema._

  val sampleUri = getClass.getResource("broadband_household_flat_sample.parquet")
  val tapadUri = getClass.getResource("tapad_sample.parquet")
  val deviceUri = getClass.getResource("ifa_master_table.parquet")

  describe("resources are readable") {
    it("has sample uri") { sampleUri should not be null }
    it("tapad uri") { tapadUri should not be null }
    it("device uri") { deviceUri should not be null }
  }

  lazy val loadSample: Dataset[UdpHhStepGCldBroadbandHouseholds] = {
    import spark.implicits._
    spark.read
      .parquet(sampleUri.toString)
      .withColumn("census_block_id", $"census_block")
      .as[UdpHhStepGCldBroadbandHouseholds]
      .cache()
  }

  lazy val loadTapad: Dataset[TapadInput] = {
    import spark.implicits._
    spark.read
      .parquet(tapadUri.toString)
      .withColumn("ip_binary", Utils.ipStringToBinaryColumn($"tp_ip"))
      .as[TapadInput]
      .cache()
  }

  lazy val loadDeviceInfo: Dataset[IfaMasterTableInput] = {
    import spark.implicits._
    spark.read
      .parquet(deviceUri.toString)
      .withColumn("ifa_binary", Utils.ifaHexStringToBinary("ifa"))
      .as[IfaMasterTableInput]
      .cache()
  }

  describe("createUnifiedHouseholdsFlat") {
    it("should execute without exceptions") {
      val input: Dataset[UdpHhStepGCldBroadbandHouseholds] = loadSample
      val tapad: Dataset[TapadInput] = loadTapad
      val ifaMaster: Dataset[IfaMasterTableInput] = loadDeviceInfo

      noException should be thrownBy
        UdpHouseholdStepHBroadbandHouseholdsRunner
          .createUnifiedHouseholdsFlat(
            input,
            tapad,
            ifaMaster
          )
          .showToString(2)
    }
  }

  describe("should join UdpHhStepGCldBroadbandHouseholds and IfaMasterTableInput to get a UdpHhStepHBroadbandHouseholds") {
    val lhs = UdpHhStepGCldBroadbandHouseholds.colNames
    val rhs = IfaMasterTableInput.colNames.filter(_ != "ifa")
    val inputCols = (lhs ++ rhs)
    val outputCols = UdpHhStepHBroadbandHouseholds.colNames

    it("doesnt contain col duplicates except ifa") {
      inputCols should contain theSameElementsAs (inputCols.distinct)
    }

    it("has all necessary columns") {
      inputCols should contain allElementsOf outputCols
    }
  }
}

