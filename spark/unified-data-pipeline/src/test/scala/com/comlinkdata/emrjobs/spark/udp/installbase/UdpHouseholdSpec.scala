package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.installbase.UdpHouseholdSetRunner._
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.TimeSeriesLocation
import com.comlinkdata.largescale.schema.udp.{Ifa, IpVec, Ip}
import com.comlinkdata.largescale.schema.udp.installbase.{UdpHousehold, IPSequence}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{Dataset, DataFrame}
import org.apache.spark.sql.functions._
import org.junit.rules.TemporaryFolder

import java.sql.Date
import java.time.LocalDate
import javax.xml.bind.DatatypeConverter
import scala.collection.mutable
case class maps(ifa: Ifa, map: Map[String, Ip])

class UdpHouseholdSpec extends CldSparkBaseSpec with LazyLogging {
  import spark.implicits._

  private val ifas: Array[Ifa] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]
  private val ips: Array[Ip] = (32 to 62).map( x => Array(192.toByte, 168.toByte, 0.toByte, x.toByte)).toArray[Array[Byte]]
  private val hhids: Array[Array[Byte]] = (32 to 62).map( x => Array(x.toByte)).toArray[Array[Byte]]

  describe("Full run") {
    val startDate = LocalDate.of(2021, 10, 1)
    val endDate = LocalDate.of(2021, 10, 4)
    val input = Seq(
      IPSequence(ifas(5), ips(5),1, 1, "carrier", Date.valueOf("2021-09-01"), Date.valueOf("2021-10-01"), 3, "2021", "10", "01"),
      IPSequence(ifas(5), ips(6),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-02"), 3, "2021", "10", "02"),
      IPSequence(ifas(5), ips(5),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-03"), 3, "2021", "10", "03"),
      IPSequence(ifas(6), ips(7),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-04"), 3, "2021", "10", "04"),
      // x a x a pattern
      IPSequence(ifas(0), ips(0),1, 1, "carrier", Date.valueOf("2021-09-01"), Date.valueOf("2021-10-01"), 3, "2021", "10", "01"),
      IPSequence(ifas(0), ips(1),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-02"), 3, "2021", "10", "02"),
      IPSequence(ifas(0), ips(0),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-03"), 3, "2021", "10", "03"),
      IPSequence(ifas(0), ips(1),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-04"), 3, "2021", "10", "04"),
      // ifa that skips days
      IPSequence(ifas(4), ips(2),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-04"), 3, "2021", "10", "01"),
      IPSequence(ifas(4), ips(2),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-04"), 3, "2021", "10", "04"),
      // random ifa / ip that comes later in the dataset
      IPSequence(ifas(1), ips(2),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-04"), 3, "2021", "10", "04"),
      // hh split stuff - 2 is college
      IPSequence(ifas(2), ips(3),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-04"), 3, "2021", "10", "01"),
      IPSequence(ifas(3), ips(3),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-04"), 3, "2021", "10", "01"),
      IPSequence(ifas(2), ips(4),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-04"), 3, "2021", "10", "02"),
      IPSequence(ifas(3), ips(3),1, 1, "carrier", Date.valueOf("2021-09-02"), Date.valueOf("2021-10-04"), 3, "2021", "10", "02"),
      // duplicate check
      IPSequence(ifas(10), ips(10),1, 1, "carrier", Date.valueOf("2021-09-01"), Date.valueOf("2021-10-01"), 3, "2021", "10", "01"),
      IPSequence(ifas(10), ips(10),1, 1, "carrier", Date.valueOf("2021-09-01"), Date.valueOf("2021-10-01"), 3, "2021", "10", "01"),
      IPSequence(ifas(10), ips(10),1, 1, "carrier", Date.valueOf("2021-09-01"), Date.valueOf("2021-10-01"), 3, "2021", "10", "02"),
      IPSequence(ifas(10), ips(10),1, 1, "carrier", Date.valueOf("2021-09-01"), Date.valueOf("2021-10-01"), 3, "2021", "10", "02"),
      IPSequence(ifas(10), ips(10),1, 1, "carrier", Date.valueOf("2021-09-01"), Date.valueOf("2021-10-01"), 3, "2021", "10", "03"),
      IPSequence(ifas(10), ips(10),1, 1, "carrier", Date.valueOf("2021-09-01"), Date.valueOf("2021-10-01"), 3, "2021", "10", "03"),
      IPSequence(ifas(10), ips(10),1, 1, "carrier", Date.valueOf("2021-09-01"), Date.valueOf("2021-10-01"), 3, "2021", "10", "04"),
      IPSequence(ifas(10), ips(10),1, 1, "carrier", Date.valueOf("2021-09-01"), Date.valueOf("2021-10-01"), 3, "2021", "10", "04")
    )

    ignore("with parquet") {

      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI

      val inputDataRoot = basePath.resolve("input")
      spark.createDataFrame(input).write
        .partitionBy("year", "month", "day")
        .parquet(inputDataRoot.toString)

      val hhPath = basePath.resolve("hhOut")
      val config: UdpHouseholdSetConfig = UdpHouseholdSetConfig(
        ipSequencePath = inputDataRoot,
        householdOutPath = hhPath,
        startDate = Some(startDate),
        endDate = Some(endDate),
        maxDaysToRun = None,
        repartition = None
      )

      UdpHouseholdSetRunner.runJob(config)

      val tslHHOutput = TimeSeriesLocation
        .ofYmdDatePartitions(hhPath)
        .build

      val results: DataFrame = spark.read
        .option(ReadOpts.basePath, hhPath.toString)
        .parquet(tslHHOutput.partitions(startDate, endDate): _*)
        .cache()

      results
        .orderBy("ifa", "year", "month", "day", "ifa")
        .show(50, false)

      folder.delete()

    }

    it("without parquet"){
      val dsIfa = input.toDS()
      var outputHH: Dataset[UdpHousehold] = spark.emptyDataset[UdpHousehold]

      for (day <- (startDate to endDate)){
        val dsCurrIPSequence = dsIfa
          .withColumn("d", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))
          .filter($"d" === to_date(lit(day)))
          .drop("d")
          .as[IPSequence]

        val dsHH = outputHH
          .withColumn("d", to_date(concat($"year", lit("-"), $"month", lit("-"), $"day")))
          .filter($"d" === to_date(lit(day.minusDays(1))))
          .drop("d")
          .as[UdpHousehold]

        val dsHHNew = processDay(dsCurrIPSequence,  dsHH, startDate, day)
        outputHH = outputHH.unionByName(dsHHNew)
        outputHH.cache()
      }
      outputHH
        .orderBy("day", "ifa")
        //.show(30)
    }
  }

  describe("static household ids"){

    it("sha2((ip, date), 512)"){
      val result = ips
        .toSeq.toDF("ip")
        .withColumn("date", lit(Date.valueOf("2021-10-01")))
        .withColumn("household_id", newHouseholdId($"ip", $"date"))
        .orderBy($"ip")
        .collect()

      result(0).getAs[Array[Byte]]("household_id") shouldBe DatatypeConverter.parseHexBinary("898bf548329750ada56ab1f46158838b1216cf630e9d1a66505095a178d7bcd201ea7ea4044b5a9c9a8fa1394b53d9c58d0a58e1a869a73afadb1c2b3d0928ec")
      result(1).getAs[Array[Byte]]("household_id") shouldBe DatatypeConverter.parseHexBinary("880a50c8fc192f9222b075ab3bae09ce45caa9effe170ffe252d5fb79625408f5c2eef5c469f6b913544b9299be6cdc172ce211439d73924a3367c7eb2c695db")
      result(2).getAs[Array[Byte]]("household_id") shouldBe DatatypeConverter.parseHexBinary("81554012afe1856b60880201add52aaf4c76456a294b0c84a2a84d4630ad7a7a38d0392e7e376c94904ceb3d0b20bb7c14ec1140c881aeb8f752eb1dbdaefed6")
    }
  }

  describe("udfCombineMap(x, y)"){

    val emptyMap: Seq[maps] = Seq(maps(ifas(0), Map.empty[String, Ip]))
    val xOneItem: Seq[maps] = Seq(maps(ifas(0), Map("20211001" -> ips(0))))
    val yOneItem: Seq[maps] = Seq(maps(ifas(0), Map("20211002" -> ips(0))))

    it("x is null"){
      val x = Seq(ifas(0))
        .toDF("ifa")
        .withColumn("map", lit(null))

      val y = yOneItem.toDS()
      val results = x.as("x")
        .join(y.as("y"), Seq("ifa"), "inner")
        .withColumn("combine", udfCombineMap($"x.map", $"y.map"))
        .select($"y.map" as "m", $"combine")
        .collect()
      val expected = results(0).getAs[Map[String, Ip]]("m").mapValues(ip => ip.toVector)
      val actual = results(0).getAs[Map[String, Ip]]("combine").mapValues(ip => ip.toVector)
      actual shouldBe expected

    }
    it("y is null"){
      val x = xOneItem.toDS()

      val y = Seq(ifas(0))
        .toDF("ifa")
        .withColumn("map", lit(null))

      val results = x.as("x")
        .join(y.as("y"), Seq("ifa"), "inner")
        .withColumn("combine", udfCombineMap($"x.map", $"y.map"))
        .select($"x.map" as "m", $"combine")
        .collect()
      val expected = results(0).getAs[Map[String, Ip]]("m").mapValues(ip => ip.toVector)
      val actual = results(0).getAs[Map[String, Ip]]("combine").mapValues(ip => ip.toVector)
      actual shouldBe expected
    }
    it("x is empty, y is empty"){
      val x = emptyMap.toDS()

      val y = emptyMap.toDS()

      val results = x.as("x")
        .join(y.as("y"), Seq("ifa"), "inner")
        .withColumn("combine", udfCombineMap($"x.map", $"y.map"))
        .select($"x.map" as "m", $"combine", $"y.map" as "m2")
        .collect()
      val expected = results(0).getAs[Map[String, Ip]]("m").mapValues(ip => ip.toVector)
      val expected2 = results(0).getAs[Map[String, Ip]]("m2").mapValues(ip => ip.toVector)
      val actual = results(0).getAs[Map[String, Ip]]("combine").mapValues(ip => ip.toVector)
      actual shouldBe expected
      actual shouldBe expected2
    }
    it("x is one item, y is empty"){
      val x = xOneItem.toDS()

      val y = emptyMap.toDS()

      val results = x.as("x")
        .join(y.as("y"), Seq("ifa"), "inner")
        .withColumn("combine", udfCombineMap($"x.map", $"y.map"))
        .select($"x.map" as "m", $"combine")
        .collect()
      val expected = results(0).getAs[Map[String, Ip]]("m").mapValues(ip => ip.toVector)
      val actual = results(0).getAs[Map[String, Ip]]("combine").mapValues(ip => ip.toVector)
      actual shouldBe expected

    }
    it("x is empty, y is one item"){
      val x = emptyMap.toDS()

      val y = yOneItem.toDS()

      val results = x.as("x")
        .join(y.as("y"), Seq("ifa"), "inner")
        .withColumn("combine", udfCombineMap($"x.map", $"y.map"))
        .select($"y.map" as "m", $"combine")
        .collect()
      val expected = results(0).getAs[Map[String, Ip]]("m").mapValues(ip => ip.toVector)
      val actual = results(0).getAs[Map[String, Ip]]("combine").mapValues(ip => ip.toVector)
      actual shouldBe expected
    }
    it("x is one item, y is one item"){
      val x = xOneItem.toDS()

      val y = yOneItem.toDS()

      val results = x.as("x")
        .join(y.as("y"), Seq("ifa"), "inner")
        .withColumn("combine", udfCombineMap($"x.map", $"y.map"))
        .select($"combine")
        .collect()
      val actual = results(0).getAs[Map[String, Ip]]("combine").mapValues(ip => ip.toVector)
      actual.keySet.size shouldBe 2
    }
    it("item a is shared between x and y"){
      val x: Dataset[maps] = Seq(
        maps(ifas(0), Map("20211001" -> ips(0)))
      ).toDS()
      val y: Dataset[maps] = Seq(
        maps(ifas(0), Map("20211001" -> ips(1)))
      ).toDS()

      val results = x.as("x")
        .join(y.as("y"), Seq("ifa"), "inner")
        .withColumn("combine", udfCombineMap($"x.map", $"y.map"))
        .select($"combine")
        .as[Map[String, Ip]]
        .collect()
      val expectedIP:IpVec = ips(1).toVector
      val actualIP:IpVec = results(0).getOrElse("20211001", Array[Byte](0)).toVector
      actualIP shouldBe expectedIP
    }
  }

  describe("cleanse sequence"){
    it("one day"){
      val startDate:LocalDate = LocalDate.of(2021, 10, 1)
      val endDate:Date = Date.valueOf(LocalDate.of(2021, 10, 1))
      val m: mutable.Map[String, Ip] = mutable.Map("2021-10-01" -> ips(0))
      val actual = cleanseSequence(startDate, endDate, m)
      actual.keySet.size shouldBe 1
      actual.keySet.head shouldBe "2021-10-01"
      actual.values.head.toVector shouldBe ips(0).toVector
    }
    it("multi null day"){
      val startDate:LocalDate = LocalDate.of(2021, 10, 1)
      val endDate:Date = Date.valueOf(LocalDate.of(2021, 10, 4))
      val m: mutable.Map[String, Ip] = mutable.Map("2021-10-01" -> ips(0))
      val actual = cleanseSequence(startDate, endDate, m)
      actual.keySet.size shouldBe 4
      actual.keySet.contains("2021-10-01") shouldBe true
      actual.keySet.contains("2021-10-02") shouldBe true
      actual.keySet.contains("2021-10-03") shouldBe true
      actual.keySet.contains("2021-10-04") shouldBe true
      actual.values.toVector shouldBe Vector(ips(0), null, null, null)
    }
    it("xaxa"){
      val startDate:LocalDate = LocalDate.of(2021, 10, 1)
      val endDate:Date = Date.valueOf(LocalDate.of(2021, 10, 4))
      val m: mutable.Map[String, Ip] = mutable.Map(
        "2021-10-01" -> ips(0),
        "2021-10-02" -> ips(1),
        "2021-10-03" -> ips(0),
        "2021-10-04" -> ips(1)
      )
      val actual = cleanseSequence(startDate, endDate, m)
      actual.keySet.size shouldBe 4
      actual.keySet.contains("2021-10-01") shouldBe true
      actual.keySet.contains("2021-10-02") shouldBe true
      actual.keySet.contains("2021-10-03") shouldBe true
      actual.keySet.contains("2021-10-04") shouldBe true
      actual.getOrElse("2021-10-01", ips(5)).toVector shouldBe ips(0).toVector
      actual.getOrElse("2021-10-02", ips(5)).toVector shouldBe ips(0).toVector
      actual.getOrElse("2021-10-03", ips(5)).toVector shouldBe ips(0).toVector
      actual.getOrElse("2021-10-04", ips(5)).toVector shouldBe ips(1).toVector
    }
    it("xaax"){
      val startDate:LocalDate = LocalDate.of(2021, 10, 1)
      val endDate:Date = Date.valueOf(LocalDate.of(2021, 10, 4))
      val m: mutable.Map[String, Ip] = mutable.Map(
        "2021-10-01" -> ips(0),
        "2021-10-02" -> ips(1),
        "2021-10-03" -> ips(1),
        "2021-10-04" -> ips(0)
      )
      val actual = cleanseSequence(startDate, endDate, m)
      actual.keySet.size shouldBe 4
      actual.keySet.contains("2021-10-01") shouldBe true
      actual.keySet.contains("2021-10-02") shouldBe true
      actual.keySet.contains("2021-10-03") shouldBe true
      actual.keySet.contains("2021-10-04") shouldBe true
      actual.getOrElse("2021-10-01", ips(5)).toVector shouldBe ips(0).toVector
      actual.getOrElse("2021-10-02", ips(5)).toVector shouldBe ips(0).toVector
      actual.getOrElse("2021-10-03", ips(5)).toVector shouldBe ips(0).toVector
      actual.getOrElse("2021-10-04", ips(5)).toVector shouldBe ips(0).toVector
    }
    it("xaaax"){
      val startDate:LocalDate = LocalDate.of(2021, 10, 1)
      val endDate:Date = Date.valueOf(LocalDate.of(2021, 10, 5))
      val m: mutable.Map[String, Ip] = mutable.Map(
        "2021-10-01" -> ips(0),
        "2021-10-02" -> ips(1),
        "2021-10-03" -> ips(1),
        "2021-10-04" -> ips(1),
        "2021-10-05" -> ips(0)
      )
      val actual = cleanseSequence(startDate, endDate, m)
      actual.keySet.size shouldBe 5
      actual.keySet.contains("2021-10-01") shouldBe true
      actual.keySet.contains("2021-10-02") shouldBe true
      actual.keySet.contains("2021-10-03") shouldBe true
      actual.keySet.contains("2021-10-04") shouldBe true
      actual.keySet.contains("2021-10-05") shouldBe true
      actual.getOrElse("2021-10-01", ips(5)).toVector shouldBe ips(0).toVector
      actual.getOrElse("2021-10-02", ips(5)).toVector shouldBe ips(0).toVector
      actual.getOrElse("2021-10-03", ips(5)).toVector shouldBe ips(0).toVector
      actual.getOrElse("2021-10-04", ips(5)).toVector shouldBe ips(0).toVector
      actual.getOrElse("2021-10-04", ips(5)).toVector shouldBe ips(0).toVector
    }
  }

  describe("udfSortedIpSequence"){
    it("handles one day"){
      val m: mutable.Map[String, Ip] = mutable.Map("2021-10-01" -> ips(0))
      val actual = sortedIpSequence(m).map(ip => ip.toVector)
      actual.size shouldBe 1
      actual(0) shouldBe ips(0).toVector
    }
    it("handles already sorted map"){
      val m: mutable.Map[String, Ip] = mutable.Map(
        "2021-10-01" -> ips(0),
        "2021-10-02" -> ips(1),
        "2021-10-03" -> ips(2)
      )
      val actual = sortedIpSequence(m).map(ip => ip.toVector)
      actual.size shouldBe 3
      actual(0) shouldBe ips(0).toVector
      actual(1) shouldBe ips(1).toVector
      actual(2) shouldBe ips(2).toVector
    }
    it("handles unsorted map"){
      val m: mutable.Map[String, Ip] = mutable.Map(
        "2021-10-01" -> ips(1),
        "2021-10-03" -> ips(3),
        "2021-10-05" -> ips(5),
        "2021-10-02" -> ips(2),
        "2021-10-04" -> ips(4)
      )
      val actual = sortedIpSequence(m).map(ip => ip.toVector)
      actual.size shouldBe 5
      actual(0) shouldBe ips(1).toVector
      actual(1) shouldBe ips(2).toVector
      actual(2) shouldBe ips(3).toVector
      actual(3) shouldBe ips(4).toVector
      actual(4) shouldBe ips(5).toVector
    }
  }

  describe("household split"){
    it("handles college / student example"){
      val df:DataFrame = Seq(
        (ifas(1), ips(1), hhids(1), ips(1), hhids(1), Date.valueOf("2021-10-01")), // parent using old IPs + households
        (ifas(2), ips(2), hhids(2), ips(1), hhids(1), Date.valueOf("2021-10-01"))  // college student moved IPs + households
      ).toDF(
        "ifa",
        "ip",
        "new_household_id",
        "prev_ip",
        "prev_household_id",
        "global_ip_min_date"
      )

      val df2 = df
        .transform(getHouseholdId($"prev_household_id", $"prev_ip", $"ip", $"global_ip_min_date"))
        .withColumn("hhid", newHouseholdId($"ip", $"global_ip_min_date"))
        .orderBy($"ifa")
      //df2.show()
      val actual = df2.collect()

      actual.size shouldBe 2
      actual(0).getAs[Array[Byte]]("household_id") shouldBe hhids(1)
      actual(1).getAs[Array[Byte]]("household_id") shouldBe actual(1).getAs[String]("hhid")
    }
  }

  describe("best ip"){
    it("Single IP"){
      val results = Seq(
        ("ifa", ips(0), Map("2021-01-01" -> ips(0)))
      )
        .toDF("ifa", "curr_ip", "ip_sequence_cleansed_map")
        .transform(bestIp)
        .collect()

      results.size shouldBe 1
      results(0).getAs[Ip](3) shouldBe ips(0)
    }
    it("Multiple IPs with out of order dates"){
      val results = Seq(
        ("ifa", ips(0), Map("2021-01-03" -> ips(1), "2021-01-02" -> ips(2), "2021-01-01" -> ips(3)))
      )
        .toDF("ifa", "curr_ip", "ip_sequence_cleansed_map")
        .transform(bestIp)
        .collect()

      results.size shouldBe 1
      results(0).getAs[Ip](3) shouldBe ips(1)
    }
    it("Multiple IPs with nulls"){
      val results = Seq(
        ("ifa", ips(0), Map("2021-01-04" -> null, "2021-01-03" -> ips(1), "2021-01-02" -> ips(2), "2021-01-01" -> ips(3)))
      )
        .toDF("ifa", "curr_ip", "ip_sequence_cleansed_map")
        .transform(bestIp)
        .collect()

      results.size shouldBe 1
      results(0).getAs[Ip](3) shouldBe ips(1)
    }
  }
}
