package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.common.generate
import com.comlinkdata.commons.testing.generators.udp.genIPHighConfidenceNoLoc
import com.comlinkdata.emrjobs.spark.udp.installbase.prefilter.IpToLocationsRunner
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, Utils, UriDFTableRW, LocalDateRange}
import com.comlinkdata.largescale.schema.udp.installbase.{IpToLocation, UdpHousehold, IpToLocCounts, UdpHouseholdLocations}
import com.comlinkdata.largescale.schema.udp.location.Point
import com.typesafe.scalalogging.LazyLogging
import org.apache.sedona.sql.utils.SedonaSQLRegistrator
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.functions._
import org.junit.rules.TemporaryFolder
import org.scalatest.BeforeAndAfterAll

import java.sql.Date
import java.time.LocalDate
import java.time.temporal.ChronoUnit

class HouseholdToLocsSpec extends CldSparkBaseSpec with LazyLogging with BeforeAndAfterAll {
  import spark.implicits._

  private val geoShapeJson = this.getClass().getResource("/com/comlinkdata/emrjobs/spark/udp/installbase/geoshape")

  private val today = LocalDate.now()
  private val ip0 = Utils.ipStringToBinary(s"*************")
  private val ip1 = Utils.ipStringToBinary("***********")

  private val ifa1 = {
    val ifa = new Array[Byte](5)
    ifa(0) = 1
    ifa(1) = 2
    ifa(2) = 3
    ifa(3) = 4
    ifa(4) = 5
    ifa
  }

  override def beforeAll(): Unit = {
    super.beforeAll()
    SedonaSQLRegistrator.registerAll(spark)
  }

  private def writeIpToLocs(): Dataset[IpToLocation] = {
    //todo: Make this a common function?
    def truncAt(p: Int)(x: Double): Float = {
      val s = math pow(10, p);
      (math floor x * s) / s
    }.toFloat

    def dailyLocCounts(day: LocalDate): IpToLocCounts = {
      val varyPrecision = 6 - ChronoUnit.DAYS.between(day, today).toInt
      val r = (x: Double) => truncAt(varyPrecision)(x)
      val mostFreqPt = Point(r(34.001364), r(-85.996636f))
      IpToLocCounts(
        ip = ip0,
        obs_count = 123,
        night_obs_count = 100,
        ifas = Seq(ifa1),
        ifa_count = 1,
        hours_seen = Seq(1, 2, 3),
        loc_counts = Seq((mostFreqPt, 10), (Point(12f, 9f), 1), (Point(56f, 78f), 1)).toMap
      )
    }
    val ldr = LocalDateRange.of(
      startDateInclusive = today.minusDays(3),
      endDateInclusive = today)

    ldr
      .map(
        day => {
          Seq(dailyLocCounts(day))
            .toDS()
            .as[IpToLocCounts]
            .transform(IpToLocationsRunner.explodeCounts)
            .withColumn("year", lit(day.getYearString))
            .withColumn("month", lit(day.getMonthValueString))
            .withColumn("day", lit(day.getDayOfMonthString))
            .as[IpToLocation]
        })
      .reduce(_ unionByName _)
  }

  describe("Household to Locs2") {
    val householdData = Seq(
      UdpHousehold(
        ifa = ifa1,
        ip = ip0,
        household_id = Array(1.toByte),
        carrier = "test",
        is_hh_split = false,
        potential_switch_ip = ip1,
        global_ip_min_date = Date.valueOf(today.minusDays(2)),
        ip_min_date = Date.valueOf(today.minusDays(2)),
        ip_max_date = Date.valueOf(today),
        days_seen_count = 5,
        ip_sequence_raw_history = Map.empty, //unused in this test
        ip_sequence_cleansed_map = Map.empty, //unused in this test
        ip_sequence = Seq(ip0, ip1).toArray,
        year = today.getYearString,
        month = today.getMonthValueString,
        day = today.getDayOfMonthString
      ))

    val highConfidenceData = generate(genIPHighConfidenceNoLoc()).map(_.copy(
      ifa = ifa1,
      ip = ip0,
      household_id = Array(160.toByte, 160.toByte),
      obs_count = 10,
      carrier = "test",
      night_obs_count = 1,
      ip_min_date = Date.valueOf(today.minusDays(2)),
      ip_max_date = Date.valueOf(today),
      case_method = 0,
      year = today.getYearString,
      month = today.getMonthValueString,
      day = today.getDayOfMonthString
    ))

    it("test with high confidence dataset"){
      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI

      val config = HouseholdToLocsConfig(
        householdPath = basePath.resolve("hh_to_loc_test_with_hc_high_confidence"),
        ipLocsPath = basePath.resolve("hh_to_loc_test_with_hc_ip_locs"),
        polygonsLocation = geoShapeJson.toURI,
        outputDate = Some(today),
        lookBackDays = 3,
        householdWithLocsOutPath = basePath.resolve("hh_to_loc_test_with_hc_output"),
        hcLookForwardDays = None
      )

      spark.createDataFrame(highConfidenceData).write
        .partitionBy("year", "month", "day")
        .parquet(config.householdPath.toString)

      writeIpToLocs().write
        .partitionBy("year", "month", "day")
        .parquet(config.ipLocsPath.toString)

      HouseholdToLocsJobRunner.runJob(config)

      val tslOut = TimeSeriesLocation
        .ofYmdDatePartitions(config.householdWithLocsOutPath)
        .build.partition(today)

      val uriRW = UriDFTableRW.fromStr(tslOut)

      val tableOut = uriRW.read()

   //   tableOut.show()

      tableOut.count() shouldBe 1

      folder.delete()
    }

    it("test geo resolve") {
      val beforeAllTempFolder = new TemporaryFolder()
      beforeAllTempFolder.create()

      val folder = beforeAllTempFolder.newFolder()
      folder.delete()

      val basePath = folder.toURI

      val config = HouseholdToLocsConfig(
        householdPath = basePath.resolve("hh_to_loc_test_with_geo_high_confidence"),
        ipLocsPath = basePath.resolve("hh_to_loc_test_with_geo_ip_locs"),
        polygonsLocation = geoShapeJson.toURI,
        outputDate = Some(today),
        lookBackDays = 3,
        householdWithLocsOutPath = basePath.resolve("hh_to_loc_test_with_geo_output"),
        hcLookForwardDays = None
      )

      spark.createDataFrame(householdData).write
        .partitionBy("year", "month", "day")
        .parquet(config.householdPath.toString)

      writeIpToLocs().write
        .partitionBy("year", "month", "day")
        .parquet(config.ipLocsPath.toString)

      HouseholdToLocsJobRunner.runJob(config)

      val tslOut = TimeSeriesLocation
        .ofYmdDatePartitions(config.householdWithLocsOutPath)
        .build.partition(today)

      val uriRW = UriDFTableRW.fromStr(tslOut)

      val tableOut = uriRW.read().as[UdpHouseholdLocations]

      //tableOut.show()

      tableOut.count() shouldBe 1
      val rowsOut = tableOut.collect()(0)
      rowsOut.census_block_id shouldBe "010550013003"
      rowsOut.household_count shouldBe 30
      rowsOut.best_point.lat shouldBe 34.001364f
    }
  }
}
