package com.comlinkdata.emrjobs.spark.udp.ifaset

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.Linkage.Fields
import com.comlinkdata.commons.testing.generators.common.{genIfa, generate}
import com.comlinkdata.commons.testing.generators.udp.{genUdpDailyDevice, genIfaSet}
import com.comlinkdata.largescale.commons.LocalDateRange
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.lookup.DeviceTypeLookup
import com.comlinkdata.largescale.schema.udp.tier2.IfaSet
import org.scalacheck.Gen

import java.sql.Date
import java.time.LocalDate

class UdpIfaSetSpec extends CldSparkBaseSpec {

  import spark.implicits._

  private val date = LocalDate.now()
  private val dateGen = Gen.const(Date.valueOf(date))


  describe("structural") {
    it("can create ifa set from daily devices only") {
      val dailyDevices = generate(5, genUdpDailyDevice(dates = dateGen))
      val ifaSet = spark.emptyDataset[IfaSet]
      val mccMncLookup = Map.empty[String, String]

      val udpIfaSet = UdpIfaSet()
      val result = udpIfaSet.runDay(
        date, ifaSet, dailyDevices.toDS(), mccMncLookup, spark.emptyDataset[DeviceTypeLookup]
      )
      assert(result.count() === 5)
    }

    it("updates existing ifa set with daily increment") {
      case class IfaWrapper(ifa: Ifa)

      val ifas = generate(5, genIfa).map(IfaWrapper.apply)
      val oldIfas = ifas.take(3)
      val newIfas = ifas.drop(2) // 2 not updated, 1 updated, 2 new
      val ifaSetDates = Gen.oneOf(LocalDateRange(date.minusDays(4), date.minusDays(1)).map(Date.valueOf))

      val dailyDevices = Fields
        .oneToOne("ifa")
        .link(newIfas, generate(3, genUdpDailyDevice(dates = dateGen)))

      val ifaSet = Fields
        .oneToOne("ifa")
        .link(oldIfas, generate(3, genIfaSet(dates = ifaSetDates)))
        .toDS()

      val mccMncLookup = Map.empty[String, String]

      val udpIfaSet = UdpIfaSet()
      val result = udpIfaSet.runDay(
        date, ifaSet, dailyDevices.toDS(), mccMncLookup, spark.emptyDataset[DeviceTypeLookup]
      )
      assert(result.count() === 5) // 2 preserved, 1 updated, 2 new
    }
  }


  describe("static device type stabilization") {
    lazy val deviceTypeLookup = dataset[DeviceTypeLookup] {
      """
        |modal_model_code|device_type|
        |ipad            |TABLET     |
        |iphone          |SMART PHONE|"""
    }

    it("device type is updated by static lookup table") {
      val ifaSet = (
        generate(4, genIfaSet())
          .map(c => c.copy(modal_model_code = Some("ipad"), device_type = Some("unknown"))) ++
          generate(genIfaSet())
            .map(c => c.copy(modal_model_code = Some("iphone"), device_type = Some("unknown"))) ++
          generate(genIfaSet())
            .map(c => c.copy(modal_model_code = Some("do_not_touch"), device_type = Some("unknown"))))
        .toDS()

      val udpIfaSet = UdpIfaSet()
      val result = ifaSet.transform(udpIfaSet.stabilizeDeviceType(deviceTypeLookup))
        .map(_.device_type)
        .collect()
        .flatten

      assert(Seq("SMART PHONE", "TABLET", "TABLET", "TABLET", "TABLET", "unknown") === result.sorted)
    }
  }
}

object UdpIfaSetSpec {

  def d(s: String): Date = d(ld(s))

  def ld(s: String): LocalDate = LocalDate.parse(s)

  def d(ld: LocalDate): Date = Date.valueOf(ld)
}