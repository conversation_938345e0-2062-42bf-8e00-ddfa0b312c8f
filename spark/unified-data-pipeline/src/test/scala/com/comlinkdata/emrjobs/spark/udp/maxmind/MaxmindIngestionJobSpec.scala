package com.comlinkdata.emrjobs.spark.udp.maxmind

import java.net.URI
import java.sql.Date
import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.schema.udp.tier1.{MaxmindIp2ConnectionType, MaxmindIp2Isp}

class MaxmindIngestionJobSpec extends CldSparkBaseSpec {

  describe("maxmind ip2connectiontype") {
    import spark.implicits._

    val sample: URI = this.getClass.getResource("ip2connectiontype").toURI

    it("can find the resource") {
      sample should not be null
    }

    it("can read the csv sample data") {
      noException should be thrownBy
        MaxmindIp2ConnectionType
          .readFromSrcs(sample, sample)
          .map(identity)
          .showToString
    }

    it("contains ipv4 and ipv6 values") {
      MaxmindIp2ConnectionType.readFromSrcs(sample, sample)
        .map(_.ipversion).distinct.collect.toVector should
        contain theSameElementsAs Vector(4, 6)
    }

  }


  describe("maxmind ip2isp") {
    val sample: URI = getClass.getResource("ip2isp").toURI

    it("can find the resource") {
      sample should not be null
    }

    it("can read as dataframe") {
      val df = spark.read
        .option(ReadOpts.csv.inferSchema, "true")
        .option(ReadOpts.csv.header, "true")
        .option(ReadOpts.basePath, sample.toString)
        .csv(sample.toString)

//      df.printSchema()
      val result = df.showToString

    }

    it("can read the csv sample data") {
      import spark.implicits._
      noException should be thrownBy
        MaxmindIp2Isp
          .readFromSrcs(sample, sample)
          .map(identity)
          .showToString
    }

    it("has date from date partition") {
      import spark.implicits._

      val expectedDate = Date.valueOf("2020-07-21")

      val dates = MaxmindIp2Isp.readFromSrcs(sample, sample)
        .select("date").distinct.as[Date]
        .collect.toVector

      dates shouldBe Vector(expectedDate)
    }

    it("has null isp") {
      MaxmindIp2Isp
        .readFromSrcs(sample, sample)
        .filter(_.isp.isEmpty)
        .count should be > 0L
    }

    it("has null organization") {
      MaxmindIp2Isp
        .readFromSrcs(sample, sample)
        .filter(_.organization.isEmpty)
        .count should be > 0L
    }

    it("doesn't contain literal null string or empty string in fields") {
      MaxmindIp2Isp.readFromSrcs(sample, sample)
        .filter(r =>
          r.isp.contains("null") || r.isp.contains("") ||
            r.organization.contains("null") || r.organization.contains("")
        ).count shouldBe 0L
    }
  }
}
