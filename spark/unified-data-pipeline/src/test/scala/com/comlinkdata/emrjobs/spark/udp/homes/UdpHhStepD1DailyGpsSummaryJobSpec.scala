package com.comlinkdata.emrjobs.spark.udp.homes

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.maxmind.{MaxmindIpLocationDao, MaxmindIpLocationDaoSpec}
import com.comlinkdata.largescale.commons.{WithResource, Utils, LocalDateRange}
import com.comlinkdata.largescale.schema.udp.location.{LocationMap, Point}
import com.comlinkdata.largescale.schema.udp.tier1.{UdpIfaObservation, UdpDailyIfa}
import com.comlinkdata.largescale.udp._
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.functions._

import java.net.URI
import java.sql.Timestamp
import java.time.{Instant, LocalDate}
import java.util.UUID

class UdpHhStepD1DailyGpsSummaryJobSpec extends CldSparkBaseSpec {

  import UdpHhStepD1DailyGpsSupersetSummarySchema._
  import com.comlinkdata.largescale.commons.RichDate._

  def loc(eps: Double = 0): Point.Float = Point(42.251416f + eps.toFloat, -71.107145f + eps.toFloat)

  val ip0 = Utils.ipStringToBinary("************")
  val ip1 = Utils.ipStringToBinary("************")

  describe("runner") {
    import UdpHhStepD1DailyGpsSupersetSummaryRunner._
    import spark.implicits._


    val mar2021 = Instant.ofEpochSecond(1614615168)

    val ts = Timestamp.from(mar2021)
    val obs0: UdpIfaObservation = UdpIfaObservation(
      t_utc = ts, //: Timestamp,
      t_local = None, //: Option[Timestamp],
      ip = Some(ip0), //: Option[Ip],
      location = loc(), // massachusetts //: Point.Float,
      location_type = "IP", //: String,
      connection_type = Some("WIFI"), //: Option[String],
      accuracy = None, //: Option[Float],
      gps_speed = None, //: Option[Float],
      place_id = None //: Option[Long]
    )

    val ifa = Utils.uuidString2Binary(UUID.randomUUID().toString)
    val date = LocalDate.of(2020, 1, 2)
    val difa0: UdpDailyIfa = UdpDailyIfa(
      ds = ComlinkdataDatasource.kochava.toString, //: String,
      year = date.getYearString, //: String,
      month = date.getMonthValueString, //: String,
      day = date.getDayOfMonthString, //: String,
      partition_date = date.toDate, //: Date,
      ifa = ifa, //: Ifa,
      observations = obs0 :: Nil, //: Seq[UdpIfaObservation],
      observation_count = 1, //: Int,
      unique_ip_count = 1, //: Int,
      location_stats = LocationMap.fromIfaObservations(obs0 :: Nil) //: LocationStatsMap
    )
    val exFlatObs = FlatObs(
      ComlinkdataDatasource.mw05.toString,
      ifa = ifa,
      ip = ip0,
      location = loc(),
      locationType = locationTypeIp,
      partition_date = date.toDate
    )


    describe("dailyIfaToIpLocation") {


      def ds(obs: UdpIfaObservation): Dataset[Seq[UdpIfaObservation]] = dss(obs :: Nil)

      def dss(obss: Seq[UdpIfaObservation]): Dataset[Seq[UdpIfaObservation]] = Seq(obss).toDS

      val keepSet = Seq(Seq(obs0, obs0.copy(location = loc(0.001))))


      it("keeps ip defined conn == wifi loc == ip") {
        val input: Dataset[Seq[UdpIfaObservation]] = keepSet.toDS
        dailyIfaToIpLocation(input).count shouldBe 2
      }

      it("has a valid obs0 type for the rest of the tests") {
        dailyIfaToIpLocation(ds(obs0)).count shouldBe 1
      }


      it("only keeps ip locations") {
        val in = ds(obs0.copy(ip = None))
        dailyIfaToIpLocation(in).count shouldBe 0
      }

      it("only keeps wifi connection type") {
        val in = ds(obs0.copy(connection_type = Some(connectionTypeCellular)))
        dailyIfaToIpLocation(in).count shouldBe 0
      }

      it("only keeps non-empty connection type") {
        val in = ds(obs0.copy(connection_type = None))
        dailyIfaToIpLocation(in).count shouldBe 0
      }

      it("only keeps non null ip") {
        val in1 = ds(obs0.copy(ip = None))
        dailyIfaToIpLocation(in1).count shouldBe 0
      }

      it("removes duplicate records") {
        val in = dss(obs0 :: obs0 :: obs0 :: Nil)
        dailyIfaToIpLocation(in).count shouldBe 1
      }

      lazy val obs0Result = dailyIfaToIpLocation(ds(obs0)).collect().head

      it("translates ip") {
        obs0Result.ip should contain theSameElementsInOrderAs obs0.ip.get
      }
      it("translates location") {
        obs0Result.location shouldBe obs0.location
      }

    }

    describe("dailyIfaToFlatObs") {

      val exampleObs1: UdpIfaObservation = obs0.copy(
        connection_type = Some(connectionTypeWifi),
        location_type = locationTypeGps
      )

      val example: UdpDailyIfa = difa0.copy(observations = Seq(exampleObs1))

      def ds(x: UdpIfaObservation): Dataset[UdpDailyIfa] = dss(example.copy(observations = Seq(x)))

      def dss(x: UdpDailyIfa): Dataset[UdpDailyIfa] = Seq(x).toDS

      it("returns a record provided `example`") {
        dailyIfaToFlatObs(dss(example)).count shouldBe 1
      }

      it("returns one record per observation") {
        val in: Dataset[UdpDailyIfa] = Seq(example, example).toDS
        dailyIfaToFlatObs(in).count shouldBe 2
      }

      it("filters null ips") {
        val in = ds(exampleObs1.copy(ip = None))
        dailyIfaToFlatObs(in).count shouldBe 0
      }

      it("can handle precision 6 as integer") {
        val largestAngleValue = 360.123456
        val stuff = (largestAngleValue * 1e6)
        stuff.toLong should be < Integer.MAX_VALUE.toLong
      }

      it("removes cellular connection type") {
        val in = ds(exampleObs1.copy(connection_type = Some(connectionTypeCellular)))
        dailyIfaToFlatObs(in).count shouldBe 0
      }

      it("does not remove empty connection type") {
        val in = ds(exampleObs1.copy(connection_type = None))
        dailyIfaToFlatObs(in).count shouldBe 1
      }

      it("removes location type ip only") {
        val in = ds(exampleObs1.copy(location_type = locationTypeIp))
        dailyIfaToFlatObs(in).count shouldBe 0
      }

      lazy val actual: FlatObs = dailyIfaToFlatObs(dss(example)).collect.head

      it("translates ifa") {
        actual.ifa should contain theSameElementsInOrderAs example.ifa
      }

      it("translates ip") {
        actual.ip should contain theSameElementsInOrderAs exampleObs1.ip.get
      }

      it("translates location type") {
        actual.locationType shouldBe exampleObs1.location_type
      }

      it("translates location") {
        actual.location shouldBe exampleObs1.location
      }

      it("translates partition date") {
        actual.partition_date shouldBe example.partition_date
      }
    }

    describe("flatObsToIpLocationObs") {

      val flatObs0 = FlatObs(
        ds = ComlinkdataDatasource.mw05.toString,
        ifa = ifa, //: Ifa,
        ip = ip0, //: Ip,
        location = loc(), //: Point.Float,
        locationType = locationTypeIp, //: String,
        partition_date = date.toDate //: Date
      )
      //      val flatObsLoc40 = FlatObsRoundedLocation()

      def ds: Dataset[FlatObs] = Seq(flatObs0).toDS

      it("runs without schema exception") {
        noException should be thrownBy
          flatObsToIpLocationObs(ds).map(identity)
      }

      it("returns a single item for example") {
        flatObsToIpLocationObs(ds).count shouldBe 1
      }

      it("calculates observation count") {
        val size = 10
        val in = Seq.fill(size)(flatObs0).toDS
        flatObsToIpLocationObs(in).head.observation_count shouldBe size
      }
    }

    describe("removeKansasSink") {


      it("removes rectangle") {
        val center = Points.centroid(Seq(kansasRectSink.bl, kansasRectSink.tr))

        val in = Seq(
          exFlatObs.copy(location = center),
          exFlatObs.copy(location = kansasRectSink.bl),
          exFlatObs.copy(location = kansasRectSink.tr)
        ).toDS

        removeKansasSink(in).count shouldBe 0
      }

      it("removes lat sinks") {
        val xys = kansasLatSink.map(lat => loc(0).copy(lat = lat))
        val in = xys.map(xy => exFlatObs.copy(location = xy)).toDS
        removeKansasSink(in).count shouldBe 0
      }

      it("removes lng sinks") {
        val xys = kansasLngSink.map(lng => loc(0).copy(lng = lng))
        val in = xys.map(xy => exFlatObs.copy(location = xy)).toDS
        removeKansasSink(in).count shouldBe 0
      }
    }

//    describe("removeMmIpLocations") {
//      val loc4 = Points.roundedLocation4(loc())
//      val mm0 = SkinnyMaxmindIpLoc(
//        network = "************/32",
////        network_start_ip = Utils.ipStringToBinary("************"),
////        network_end_ip = Utils.ipStringToBinary("************"),
//        lat4 = loc4.lat,
//        lng4 = loc4.lng
//      )
//
//      def shouldBeEqual(o1: FlatObs, o2: FlatObs) = {
//        o1.ifa should contain theSameElementsInOrderAs o2.ifa
//        o1.ip should contain theSameElementsInOrderAs o2.ip
//        o1.location shouldBe o2.location
//        o1.locationType shouldBe o2.locationType
//        o1.partition_date shouldBe o2.partition_date
//      }
//
//      it("removes records where ip location is in maxmind dataset") {
//        val mmds = Seq(mm0).toDS
//        val in = Seq(exFlatObs.copy(ip = ip0)).toDS
//        mmds.show(false)
////        in.show(false)
//        removeMmIpLocations(mmds)(in).count shouldBe 0
//      }
//
//      it("passes data that doesn't exist in maxmind dataset") {
//        val mmds = Seq(mm0).toDS
//        val item = exFlatObs.copy(ip = ip1)
//        val in = Seq(item).toDS
//
//        shouldBeEqual(removeMmIpLocations(mmds)(in).head, item)
//      }
//    }

//    describe("removeIpLocations") {
//      val ipLoc0 = IpLocation(ip = ip0, location = loc())
//      val ipLoc1 = IpLocation(ip = ip1, location = loc(1))
//
//      def ipLocs = Seq(ipLoc0, ipLoc1).toDS
//
//      it("doesnt filter matching location with different ips") {
//        val obs: Dataset[FlatObs] = Seq(
//          exFlatObs.copy(ip = ip0, location = loc()),
//          exFlatObs.copy(ip = ip0, location = loc(1))
//        ).toDS
//
//        removeIpLocations(ipLocs)(obs).count shouldBe 1
//      }
//
//      it("filters a location that matches ipLocations") {
//
//        val obs: Dataset[FlatObs] = Seq(
//          exFlatObs.copy(ip = ip0, location = loc()),
//          exFlatObs.copy(ip = ip1, location = loc(1))
//        ).toDS
//
//        removeIpLocations(ipLocs)(obs).count shouldBe 0
//      }
//
//      it("leaves locations that don't match") {
//        val obs: Dataset[FlatObs] = Seq(
//          exFlatObs.copy(location = loc(3)),
//          exFlatObs.copy(location = loc(4))
//        ).toDS
//
//        removeIpLocations(ipLocs)(obs).count shouldBe 2
//      }
//    }

    describe("onlyIpsFromStepC") {

      it("keeps ips present in stepC data") {
        val ips = Seq(ip0, ip1).map(IpData).toDS
        val in = Seq(
          exFlatObs.copy(ip = ip0),
          exFlatObs.copy(ip = ip0),
          exFlatObs.copy(ip = ip1),
          exFlatObs.copy(ip = ip1)
        ).toDS

        onlyIpsFromStepC(ips)(in).count shouldBe 4
      }

      it("it removes ips not present in stepC data") {
        val ips = Seq(ip0).map(IpData).toDS
        val in = Seq(
          exFlatObs.copy(ip = ip0),
          exFlatObs.copy(ip = ip0),
          exFlatObs.copy(ip = ip1),
          exFlatObs.copy(ip = ip1)
        ).toDS

        onlyIpsFromStepC(ips)(in).count shouldBe 2
      }
    }

    describe("chooseDistinctLocationPerIp") {
      val ipLocObs0 = IpLocationObs(
        ip = ip0,
        location = loc(),
        observation_count = 1,
        partition_date = date.toDate
      )

      // second distinct location that aggregates into rnd4 location
      val ipLocObs1 = ipLocObs0.copy(location = loc(0.2e-4))

      it("collects examples into single record") {
        val in = Seq(ipLocObs0, ipLocObs1).toDS

        chooseDistinctLocationPerIp(in).count shouldBe 1
      }

      it("calculates the correct dayCount") {
        val size = 8
        val dates = LocalDateRange.ofEndDateWithSize(LocalDate.of(2020, 1, 1), size)
        require(dates.size == size, "bad test")
        val in: Dataset[IpLocationObs] = dates.toList.flatMap(d => Seq(
          ipLocObs0.copy(partition_date = d.toDate),
          ipLocObs1.copy(partition_date = d.toDate)
        )).toDS

        chooseDistinctLocationPerIp(in).head.day_count shouldBe size
      }

      it("calculates the correct obs_count") {
        val n = 8
        val e0 = ipLocObs0.copy(observation_count = 2)
        val e1 = ipLocObs1.copy(observation_count = 3)
        val dates = LocalDateRange.ofEndDateWithSize(LocalDate.of(2020, 1, 1), n)
        require(dates.size == n, "bad test")

        val in: Dataset[IpLocationObs] = dates.toList.flatMap(d => Seq(
          e0.copy(partition_date = d.toDate),
          e1.copy(partition_date = d.toDate)
        )).toDS

        chooseDistinctLocationPerIp(in).head.obs_count shouldBe 2 * n + 3 * n
      }

      it("calculates the correct location_count") {
        val size = 8
        val dates = LocalDateRange.ofEndDateWithSize(LocalDate.of(2020, 1, 1), size)
        require(dates.size == size, "bad test")

        val in: Dataset[IpLocationObs] = dates.toList.flatMap(d => Seq(
          ipLocObs0.copy(partition_date = d.toDate),
          ipLocObs1.copy(partition_date = d.toDate)
        )).toDS

        chooseDistinctLocationPerIp(in).head.location_count shouldBe 2
      }

      it("converts location to roundedLocation4 format") {
        val in = Seq(ipLocObs0, ipLocObs1).toDS
        chooseDistinctLocationPerIp(in).head.loc4 shouldBe Points.roundedLocation4(loc())
      }

      it("removes ips with only one distinct location") {
        val dr = LocalDateRange.ofEndDateWithSize(LocalDate.of(1988, 1, 1), 10)
        val data = dr.toVector.map(d => ipLocObs0.copy(partition_date = d.toDate))

        chooseDistinctLocationPerIp(data.toDS).count shouldBe 0
      }
    }
  }

  describe("RankableLocation") {
    import RankableLocation._
    val ex = RankableLocation(
      ip = ip0,
      loc4 = Points.roundedLocation4(loc()),
      day_count = 1,
      obs_count = 1,
      location_count = 1
    )
    describe("preferredOf") {
      it("chooses max dayCount per ip") {
        val r0 = ex.copy(day_count = 10)
        val r1 = ex.copy(day_count = 11)
        preferredOf(r0, r1) shouldBe r1
        preferredOf(r0, r1.copy(day_count = 9)) shouldBe r0
      }

      it("chooses max obsCount when dayCounts are equal") {
        val r0 = ex.copy(obs_count = 10)
        val r1 = ex.copy(obs_count = 11)
        preferredOf(r0, r1) shouldBe r1
        preferredOf(r0, r1.copy(obs_count = 9)) shouldBe r0
      }

      it("chosses max distinct location when dayCount and obsCount are equal") {
        val r0 = ex.copy(location_count = 10)
        val r1 = ex.copy(location_count = 11)
        preferredOf(r0, r1) shouldBe r1
        preferredOf(r0, r1.copy(location_count = 9)) shouldBe r0
      }
    }
  }



  describe("schema") {

    import spark.implicits._

    describe("skinny mamxind ip loc") {

      def sampleLoc: URI = MaxmindIpLocationDaoSpec.sampleLoc.toURI

      lazy val expectedSize = WithResource.readLines(sampleLoc).size

      lazy val raw = MaxmindIpLocationDao.load(sampleLoc).cache()

      it("has resource") {
        MaxmindIpLocationDaoSpec.sampleLoc should not be null
      }

      it("has all the records") {
        raw.count shouldBe expectedSize
      }

      // this will help detect if there is a corrupt record b/c it is at the end,
      // so if an issue occurs, the last cols may null out.
      it("can convert raw table into skinny format") {
        val expected = raw.filter('latitude.isNotNull).count
        raw.transform(SkinnyMaxmindIpLoc.fromRawInput)
          .map(identity).count shouldBe expected
      }

      it("locations are coherent") {
        val (lat0, lat1, lng0, lng1) = raw
          .transform(SkinnyMaxmindIpLoc.fromRawInput)
          .agg(min('lat4), max('lat4), min('lng4), max('lng4))
          .as[(Int, Int, Int, Int)]
          .collect.head

        val scale = 10000
        lat0 should be >= -90 * scale
        lat1 should be <= 90 * scale
        lng0 should be >= -180 * scale
        lng1 should be <= 180 * scale
      }

    }
  }

}
