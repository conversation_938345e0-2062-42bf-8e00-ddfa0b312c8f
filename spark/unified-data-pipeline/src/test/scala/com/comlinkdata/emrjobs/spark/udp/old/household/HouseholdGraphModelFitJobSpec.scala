package com.comlinkdata.emrjobs.spark.udp.old.household

import java.util.UUID

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.commons.Utils
import org.apache.spark.ml.classification.LogisticRegression
import org.apache.spark.ml.feature.LabeledPoint
import org.apache.spark.ml.linalg.DenseVector
import org.apache.spark.sql.DataFrame

class HouseholdGraphModelFitJobSpec extends CldSparkBaseSpec {

  ignore("logistic regression") {
    val newData = {

      val ifa0 = Utils.uuidString2Binary(UUID.randomUUID().toString)
      val ifa1 = Utils.uuidString2Binary(UUID.randomUUID().toString)

      def nextIpBlock = math.abs(scala.util.Random.nextInt) % 254

      def nextIpStr = s"$nextIpBlock.$nextIpBlock.$nextIpBlock.$nextIpBlock"

      val ip0Str = nextIpStr
      val ip0 = Utils.ipStringToBinary(ip0Str)

      val ip1Str = nextIpStr
      val ip1 = Utils.ipStringToBinary(ip1Str)
      val x0 = ModelInputIpPortion(
        ifa = ifa0,
        mw_ip = ip0,
        rank = -1,
        tp_ip = ip0,
        matched = true,
        distinct_night_ifa_count = 2,
        distinct_weekend_ifa_count = 1,
        distinct_days_ip = 99, // these high when matched == true, zero otherwise
        distinct_nights_ip = 98,
        distinct_weekend_days_ip = 97
      )


      val matchedYess = (1 to 10) map { _ =>
        x0.copy(
          ifa = ifa0,
          matched = true,
          distinct_days_ip = 99, // these high when matched == true, zero otherwise
          distinct_nights_ip = 98,
          distinct_weekend_days_ip = 97
        )
      }

      val matchedNos = (1 to 10) map { _ =>
        x0.copy(
          ifa = ifa1,
          matched = false,
          distinct_days_ip = 0, // these high when matched == true, zero otherwise
          distinct_nights_ip = 0,
          distinct_weekend_days_ip = 0
        )
      }

      matchedNos ++ matchedYess


    }

    ignore("can predict its training data") {

      import spark.implicits._

      val data = newData.toDS()
      data.showToString

      val labeledPoints = data.map { row =>
        val label = if (row.matched) 1 else 0
        val features = new DenseVector(Array(row.distinct_weekend_days_ip,
          row.distinct_nights_ip,
          row.distinct_weekend_ifa_count,
          row.distinct_night_ifa_count,
          row.distinct_days_ip
        ))
        LabeledPoint(label, features)
      }

      val seed = 42
      val trainingSet = labeledPoints
      val testSet = labeledPoints

      val regression = new LogisticRegression()
      //        .setMaxIter(100)
      //        .setRegParam(0.3)
      //        .setElasticNetParam(0.8)
      //        .setFitIntercept(true)


      val model = regression.fit(trainingSet)
      println(s"model.binarySummary.accuracy = ${model.binarySummary.accuracy}")

      val predictions: DataFrame = model.transform(testSet)

//      predictions.printSchema()
//      predictions.show(100)

      predictions.filter($"label" <= 0.1).count shouldBe 10
      predictions.filter($"label" >= 0.9).count shouldBe 10
    }
  }


}
