package com.comlinkdata.emrjobs.spark.udp.tier1

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.commons.Utils
import UdpStaticIpFilter._

class UdpStaticIpFilterSpec extends CldSparkBaseSpec {
  val ip = Utils.ipStringToBinary _

  import spark.implicits._

  describe("filterIpsByCidr") {
    it("filters loopback") {
      val cidrs = List("127.0.0.1/32")
      val expected = List(
        ip("***************"),
        ip("*********")
      )

      val input = (ip("127.0.0.1") :: expected).toDF("ip")

      input.filter(filterIpsByCidr("ip", cidrs)) shouldBeDf
        expected.toDF("ip")
    }

    it("filters 192.168 16 bits") {
      val cidrs = List("***********/16")

      val expected = List(
        ip("***************"),
        ip("*********"),
        ip("***********"),
        ip("***************")
      )

      val filtered = List(
        ip("***********"),
        ip("***************"),
        ip("*************"),
        ip("***********")
      )

      val input = (filtered ++ expected).toDF("ip")

      input.filter(filterIpsByCidr("ip", cidrs)) shouldBeDf
        expected.toDF("ip")
    }
    it("works against multiple cidrs") {
      val cidrs = List(
        "127.0.0.1/32",
        "***********/16"
      )
      val expected192 = List(
        ip("***************"),
        ip("*********"),
        ip("***********"),
        ip("***************")
      )
      val filtered192 = List(
        ip("***********"),
        ip("***************"),
        ip("*************"),
        ip("***********")
      )

      val expected127 = List(
        ip("***************"),
        ip("*********")
      )
      val filtered127 = ip("127.0.0.1") :: Nil

      val input = (filtered127 ++ expected127 ++ filtered192 ++ expected192).toDF("ip")
      input.filter(filterIpsByCidr("ip", cidrs)) shouldBeDf
        (expected127 ++ expected192).toDF("ip")
    }
  }

}
