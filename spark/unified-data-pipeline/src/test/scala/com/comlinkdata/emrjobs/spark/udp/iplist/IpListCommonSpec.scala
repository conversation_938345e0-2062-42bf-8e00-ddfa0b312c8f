package com.comlinkdata.emrjobs.spark.udp.iplist

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.iplist.IpListCommon.{getRange, loadIpRangeLookup, nextQuarter}
import com.comlinkdata.largescale.commons.LocalDateRange
import java.time.LocalDate

class IpListCommonSpec extends CldSparkBaseSpec {

  import spark.implicits._

  describe("Common Lib") {
    it("should load IP ranges correctly") {
      loadIpRangeLookup(Seq("107,242,125", "107,77,253").toDS, "test") shouldBe ((List("6bf27d", "6b4dfd"), 6))
      assertThrows[IllegalArgumentException](loadIpRangeLookup(Seq("107,242,125", "107,242").toDS, "test"))
    }
    it("should calculate next quarter") {
      nextQuarter("2000q1") shouldBe "2000q2"
      nextQuarter("2000q2") shouldBe "2000q3"
      nextQuarter("2000q3") shouldBe "2000q4"
      nextQuarter("2000q4") shouldBe "2001q1"
    }
    it("should calculate quarter range") {
      getRange("2000q1") shouldBe LocalDateRange.of(LocalDate.of(2000, 1, 1), LocalDate.of(2000, 3, 31))
      getRange("2000q2") shouldBe LocalDateRange.of(LocalDate.of(2000, 4, 1), LocalDate.of(2000, 6, 30))
      getRange("2000q3") shouldBe LocalDateRange.of(LocalDate.of(2000, 7, 1), LocalDate.of(2000, 9, 30))
      getRange("2000q4") shouldBe LocalDateRange.of(LocalDate.of(2000, 10, 1), LocalDate.of(2000, 12, 31))
    }
  }
}
