package com.comlinkdata.emrjobs.spark.udp.homes

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import org.apache.spark.sql.{DataFrame, Dataset}

class UdpHhStepEResidentialSummaryJobSpec extends CldSparkBaseSpec {

  describe("runner") {
    val ifaIpLocSampleUri = getClass.getResource("udpifaiplocation_sample.parquet")
    val ipLocationSampleUri = getClass.getResource("udpiplocationcategory_sample.parquet")

    it("ifa ip loc sample uri is not null") {
      ifaIpLocSampleUri should not be null
    }

    lazy val loadIfaIpLocSampleDs: Dataset[UdpHhStepDIfaIpLocation] = {
      import spark.implicits._
      spark.read
        .parquet(ifaIpLocSampleUri.toString)
        .as[UdpHhStepDIfaIpLocation]
        .cache()
    }

    lazy val loadipLocationCategorySampleDs: Dataset[UdpHhStepEResidentialSummary] = {
      import spark.implicits._
      spark.read
        .parquet(ipLocationSampleUri.toString)
        .as[UdpHhStepEResidentialSummary]
        .cache()
    }

    describe("transformUdpIfaIpLocation2UdpIpLocationWithCategory") {
      it("should execute without exceptions") {
        val input: Dataset[UdpHhStepDIfaIpLocation] = loadIfaIpLocSampleDs
        noException should be thrownBy
          UdpHhStepEResidentialSummaryRunner
            .transformUdpIfaIpLocation2UdpIpLocationWithCategory(input)
            .showToString
      }

//      //TODO implement tests for Table 4.1.a
//      it("should be distinct on ip") {}
//      it("should correctly calculate IP-GPS type locations") {}
//      it("should correctly calculate IFA-GPS type locations") {}
//      it("should only have null locations_source or location_date_count when location is null") {}
//      it("should never have a null ip_category") {}
    }

    describe("transformIpLocationCategory2DistinctResidentialLocation") {
      ignore("should execute without exceptions") {
        val ifaIpInput: Dataset[UdpHhStepDIfaIpLocation] = loadIfaIpLocSampleDs
        val ipLocationCategory: Dataset[UdpHhStepEResidentialSummary] = loadipLocationCategorySampleDs

        noException should be thrownBy
          UdpHhStepEResidentialSummaryRunner
            .transformIpLocationCategory2DistinctResidentialLocation(ipLocationCategory, ifaIpInput)
            .showToString
      }

//      //TODO implement tests for table 5
//      it("should be distinct on ifa") {}
//      it("should contain exactly 1 IP address per IFA"){}
//      it("should only contain IP addresses that are labeled as residential in the IpLocationCategory table"){}
//      it("should contain no null locations"){}
    }

  }
}
