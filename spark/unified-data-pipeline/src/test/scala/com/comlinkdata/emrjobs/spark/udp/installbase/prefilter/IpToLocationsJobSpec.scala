package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.commons.UriDFTableRW
import com.comlinkdata.largescale.udp.ComlinkdataDatasource
import org.apache.spark.sql.functions.explode

import java.net.URI

class IpToLocationsJobSpec extends CldSparkBaseSpec {
  import spark.implicits._

  private val ifaObsTestData = this.getClass.getResource("/com/comlinkdata/emrjobs/spark/udp/locationset/")
  private val dailyMaxMindTestData = this.getClass.getResource("/com/comlinkdata/emrjobs/spark/udp/locationset_maxmind/")

  private val carrierLookupPath = new URI("table://ipLocCL")
  private val carrierLookupFwPath = new URI("table://ipLocCL_FW")
  private val ipv6TruncationLookupPath = new URI("table://ipLocTrun")
  private val hourWeightLookupPath = new URI("table://ipLocHour")
  private val outpath = new URI("table://ipLocOutPath")

  describe("Ip to Locations"){
    it("Test loc filtering") {
      val ds = DataGen.exampleIpToIfa()

      val dsOut = IpToLocationsRunner.computeLocationSingleDS(ds)
      dsOut.select(explode($"locations")).count() shouldBe 2
    }
  }

  it("Run Job with Saved Data") {
    DataGen.writeCarrierLookup(carrierLookupPath)
    DataGen.writeCarrierLookupFw(carrierLookupFwPath)
    DataGen.writeIpv6Lookup(ipv6TruncationLookupPath)
    DataGen.writeHourWeight(hourWeightLookupPath)

    val config = IpToLocationsConfig(
      ifaObsPath = ifaObsTestData.toURI,
      maxmindPath = dailyMaxMindTestData.toURI,
      carrierLookupPath = carrierLookupPath,
      carrierLookupFwPath = carrierLookupFwPath,
      ipv6TruncationLookupPath = ipv6TruncationLookupPath,
      hourWeightLookupPath = hourWeightLookupPath,
      dailyIpCarrierPath = None,
      datasources = Seq(ComlinkdataDatasource.mw05),
      startDate = DataGen.startDate,
      endDate = DataGen.endDate,
      repartition = Some(1),
      ipToLocationOutPath = outpath
    )

    IpToLocationsRunner.runJob(config)

    val ipLocation = UriDFTableRW(config.ipToLocationOutPath).readBasePath()
    ipLocation.count shouldBe 14
    ipLocation.show
  }
}
