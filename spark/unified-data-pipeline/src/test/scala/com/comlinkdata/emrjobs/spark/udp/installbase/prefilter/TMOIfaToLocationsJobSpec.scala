package com.comlinkdata.emrjobs.spark.udp.installbase.prefilter

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.commons.testing.generators.common.generate
import com.comlinkdata.commons.testing.generators.udp.genUdpDailyDevice
import com.comlinkdata.emrjobs.spark.udp.installbase.prefilter.DataGen.ips
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.commons.{TimeSeriesLocation, UriDFTableRW}
import com.comlinkdata.largescale.schema.udp.Ifa
import com.comlinkdata.largescale.schema.udp.location.{LocationStat, Point}
import com.comlinkdata.largescale.schema.udp.tier1.{UdpIfaObservation, UdpDailyIfa, UdpDailyDevice}
import com.comlinkdata.largescale.udp._
import org.scalacheck.Gen

import java.net.URI
import java.sql.Date
import java.time.LocalDate

class TMOIfaToLocationsJobSpec extends CldSparkBaseSpec {
  import spark.implicits._

  private val startDate = LocalDate.of(2021, 10, 15)
  private val ifas: Array[Ifa] = (32 to 62).map(x => Array(x.toByte)).toArray[Array[Byte]]

  private val carrierLookupPath = new URI("table://prefilterTmoIfaLocCL")
  private val carrierLookupFwPath = new URI("table://prefilterTmoIfaLocCL_FW")
  private val ipv6TruncationLookupPath = new URI("table://prefilterTmoIfaTrun")
  private val hourWeightLookupPath = new URI("table://prefilterTmoIfaHour")

  private val tmoIfaObsPath = new URI("table://prefilterTmoIfaObs")
  private val udpDailyDeviceLocation = new URI("table://prefilterUdpDailyDevice")
  private val tmoMaxMindPath = new URI("table://prefilterTmoMM")

  private val outpath = new URI("table://prefilterTmoIfaOutPath")

  private val tmoIfaToIpData = Seq(
    UdpDailyIfa(
      ds = "mw05",
      year = startDate.getYearString,
      month = startDate.getMonthValueString,
      day = startDate.getDayOfMonthString,
      partition_date = startDate.toDate,
      ifa = ifas(2),
      observations = Seq(
        UdpIfaObservation(
          t_utc = DataGen.toTs("2021-10-15 19:33:09.000"),
          t_local = Some(DataGen.toTs("2021-10-15 14:33:09.000")),
          ip = Some(ips(2)),
          location = Point(41.0743f, -73.4804f),
          location_type = "GPS",
          connection_type = Some("NA"),
          accuracy = Some(45.0f),
          gps_speed = None,
          place_id = Some(16224257))),
      observation_count = 1,
      unique_ip_count = 1,
      location_stats = Map.empty[String, LocationStat]
    ),
    UdpDailyIfa(
      ds = "mw05",
      year = startDate.getYearString,
      month = startDate.getMonthValueString,
      day = startDate.getDayOfMonthString,
      partition_date = startDate.toDate,
      ifa = ifas(3),
      observations = Seq(
        UdpIfaObservation(
          t_utc = DataGen.toTs("2021-10-15 19:33:09.000"),
          t_local = Some(DataGen.toTs("2021-10-15 14:33:09.000")),
          ip = Some(ips(3)),
          location = Point(41.0743f, -73.4804f),
          location_type = "GPS",
          connection_type = Some("NA"),
          accuracy = Some(45.0f),
          gps_speed = None,
          place_id = Some(16224257))),
      observation_count = 1,
      unique_ip_count = 1,
      location_stats = Map.empty[String, LocationStat]
    ))

  private val tmoMaxmindData = Seq(
    DataGen.Maxmind(ips(2), "T-Mobile USA", "Cable/DSL", "T-Mobile USA", "1", "mw05", "4", "2021", "10", "15"),
    DataGen.Maxmind(ips(3), "x Wireless", "Cable/DSL", "T-Mobile USA", "1", "mw05", "4", "2021", "10", "15"),
    DataGen.Maxmind(ips(3), "x Wireless", "Cable/DSL", "T-Mobile USA", "1", "mw05", "6", "2021", "10", "15")
  )

  private val udpDailyDeviceLocationData: Seq[UdpDailyDevice] = Seq("mw05", "mw03").flatMap { ds =>
    generate(1, genUdpDailyDevice(
      ifas = Gen.oneOf(Seq(ifas(2))),
      dates = Gen.oneOf(Seq(Date.valueOf(startDate))),
    )).map(_.copy(ds = ds))
  }

  describe("Testing") {

    it("Run Job with TMO") {
      DataGen.writeCarrierLookup(carrierLookupPath)
      DataGen.writeCarrierLookupFw(carrierLookupFwPath)
      DataGen.writeIpv6Lookup(ipv6TruncationLookupPath)
      DataGen.writeHourWeight(hourWeightLookupPath)
      UriDFTableRW(udpDailyDeviceLocation)
        .writeDsPartitionBy(udpDailyDeviceLocationData.toDS(), Seq("ds", "year", "month", "day"))

      val tslObs = TimeSeriesLocation
        .ofYmdDatePartitions(tmoIfaObsPath)
        .withPartition(ComlinkdataDatasource.mw05)
        .build.partition(startDate)

      UriDFTableRW.fromStr(tslObs).writeSeq(tmoIfaToIpData)

      val tmoMaxmindDataDf = spark.createDataFrame(tmoMaxmindData)

      for(v <- Seq(4, 6)) {
        val tslMM = TimeSeriesLocation
          .ofYmdDatePartitions(tmoMaxMindPath)
          .withPartition(ComlinkdataDatasource.mw05)
          .withPartition(s"ipversion=$v")
          .build.partition(startDate)
        UriDFTableRW.fromStr(tslMM).write(tmoMaxmindDataDf.where($"ipversion" === s"$v"))
      }

      val config = TMOIfaToLocationsConfig(
        ifaObsPath = tmoIfaObsPath,
        maxmindPath = tmoMaxMindPath,
        carrierLookupPath = carrierLookupPath,
        carrierLookupFwPath = carrierLookupFwPath,
        ipv6TruncationLookupPath = ipv6TruncationLookupPath,
        hourWeightLookupPath = hourWeightLookupPath,
        dailyIpCarrierPath = None,
        udpDailyDeviceLocation = udpDailyDeviceLocation,
        tmoFwDeviceList = List("games console", "desktop", "set top box", "tv", "tablet"),
        datasources = Seq(ComlinkdataDatasource.mw05),
        startDate = startDate,
        endDate = startDate,
        repartition = Some(1),
        ifaToLocationOutPath = outpath
      )

      TMOIfaToLocationsRunner.runJob(config)

      val ipLocation = UriDFTableRW(config.ifaToLocationOutPath).readBasePath()
      ipLocation.show
      ipLocation.count shouldBe 1
    }
  }
}
