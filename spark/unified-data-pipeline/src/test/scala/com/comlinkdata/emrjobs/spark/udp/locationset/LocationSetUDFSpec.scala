package com.comlinkdata.emrjobs.spark.udp.locationset

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.locationset.LocationSetUDFs._
import com.comlinkdata.largescale.schema.udp.tier2.{CarrierSeen, HourHistory}
import com.comlinkdata.largescale.schema.udp.tier2.HourHistory.{toRowOption, toRow}
import com.comlinkdata.largescale.schema.udp.{Ifa, IfaVec}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions.{struct, lit, collect_set}
import org.apache.spark.sql.catalyst.expressions.GenericRowWithSchema

import java.sql.{Timestamp, Date}
import java.time.LocalDate
import scala.collection.mutable

class LocationSetUDFSpec extends CldSparkBaseSpec with LazyLogging {
  import spark.implicits._

  describe("Testing toHourOfDay"){
    it("Should handle null input"){
      assert(_toHourOfDay(null) == -1)
      val df = spark.sql("select cast(null as timestamp) as t_local").
        toDF("t_local").
        withColumn("day_hour", toHourOfDay($"t_local")).
        collect()
      val results = df.map(row => row.get(1))
      assert(results(0) == -1)
    }
    it("Should handle edge case zero"){
      val ts = Timestamp.valueOf("2021-12-21 00:00:00")
      val scalaResult = _toHourOfDay(ts)
      scalaResult shouldBe 0
      val df = Seq(ts).
        toDF("t_local").
        withColumn("day_hour", toHourOfDay($"t_local")).
        collect()
      val results = df.map(row => row.getInt(1))
      results(0) shouldBe 0
    }
    it("Should handle edge case 23"){
      val ts = Timestamp.valueOf("2021-12-21 23:59:59")
      val scalaResult = _toHourOfDay(ts)
      scalaResult shouldBe 23
      val df = Seq(ts).
        toDF("t_local").
        withColumn("day_hour", toHourOfDay($"t_local")).
        collect()
      val results = df.map(row => row.getInt(1))
      results(0) shouldBe 23
    }
    it("Should handle mid day") {
      val ts = Timestamp.valueOf("2021-06-10 12:00:00")
      val scalaResult = _toHourOfDay(ts)
      scalaResult shouldBe 12
      val df = Seq(ts).
        toDF("t_local").
        withColumn("day_hour", toHourOfDay($"t_local")).
        collect()
      val results = df.map(row => row.getInt(1))
      results(0) shouldBe 12
    }
  }

  describe("Testing lastNWeek"){
    val today = LocalDate.of(2021, 11, 3)
    val hourHistory = HourHistory.empty()

    for (i <- 0 until 126) {
      for (j <- 0 until 24) {
        hourHistory.history(i)(j) = 1
      }
    }

    it("Last week sum") {
      for (weeks <- 1 to 18) {
        val countsOut = _lastNWeekSum(weeks, today, toRow(hourHistory))
        for (i <- 0 until 7) {
          for (j <- 0 until 24) {
            countsOut(i)(j) shouldBe weeks
          }
        }
      }

      val results = Seq(hourHistory).toDF.
        // Workaround to convert back to dataframe
        select(struct("i", "history").as("day_hours_history")).
        withColumn("week_hours_1", lastNWeekSum(1, today)($"day_hours_history")).
        withColumn("week_hours_4", lastNWeekSum(4, today)($"day_hours_history")).
        withColumn("week_hours_7", lastNWeekSum(7, today)($"day_hours_history")).
        withColumn("week_hours_18", lastNWeekSum(18, today)($"day_hours_history")).
        collect()

      Seq(1, 4, 7, 18).zip(1 to 4).foreach{ i =>
        val week = results(0).getAs[mutable.WrappedArray[mutable.WrappedArray[Float]]](i._2)
        val hours = (0 to 23).map(x => i._1.toFloat)
        val compare = Seq(hours, hours, hours, hours, hours, hours, hours)

        week shouldBe compare
      }
    }

    it("Last week normalized") {
      for (weeks <- 1 to 18) {
        val countsOut = _lastNWeekNorm(weeks, today, toRow(hourHistory))
        for (i <- 0 until 7) {
          for (j <- 0 until 24) {
            countsOut(i)(j) shouldBe 1.0
          }
        }
      }

      val results = Seq(hourHistory).toDF.
        // Workaround to convert back to dataframe
        select(struct("i", "history").as("day_hours_history")).
        withColumn("week_hours_1", lastNWeekNorm(1, today)($"day_hours_history")).
        withColumn("week_hours_4", lastNWeekNorm(4, today)($"day_hours_history")).
        withColumn("week_hours_7", lastNWeekNorm(7, today)($"day_hours_history")).
        withColumn("week_hours_18", lastNWeekNorm(18, today)($"day_hours_history")).
        collect()

      Seq(1, 1, 1, 1).zip(1 to 4).foreach{ i =>
        val week = results(0).getAs[mutable.WrappedArray[mutable.WrappedArray[Float]]](i._2)
        val hours = (0 to 23).map(x => i._1.toFloat)
        val compare = Seq(hours, hours, hours, hours, hours, hours, hours)

        week shouldBe compare
      }
    }
  }

  describe("Testing distinctCountRollingWindow"){
    val date = LocalDate.of(2021, 12, 21)
    it("Should handle a null ifa map"){
      val scalaResults = _distinctCountRollingWindow(1, date, null)
      scalaResults shouldBe -1

      val results = Seq("a").
        toDF("a").
        withColumn("last_seen_map", lit(null)).
        withColumn("ifas_n", distinctCountRollingWindow(7, date)($"last_seen_map")).
        drop("a").
        collect()

      results(0).getInt(1) shouldBe -1
    }
    it("Should handle a 0 count ifa map"){
      val ifaDate: Map[IfaVec, Date] = Map()
      val scalaResults = _distinctCountRollingWindow(1, date, ifaDate)
      scalaResults shouldBe 0

      val df = Seq(ifaDate).
        toDF("last_seen_map").
        withColumn("ifas_n", distinctCountRollingWindow(7, date)($"last_seen_map")).
        collect()
      val results = df.map(row => row.getInt(1))
      results(0) shouldBe 0
    }
    it("Should handle days after cut off"){
      val ifaDate: Map[IfaVec, Date] = Map(
        Vector(1.toByte) -> Date.valueOf("2021-12-21"), // should be counted
        Vector(2.toByte) -> Date.valueOf("2021-12-01")  // should not be counted
      )
      val scalaResults = _distinctCountRollingWindow(1, date, ifaDate)
      scalaResults shouldBe 1

      val df = Seq(ifaDate).
        toDF("last_seen_map").
        withColumn("ifas_n", distinctCountRollingWindow(7, date)($"last_seen_map")).
        collect()
      val results = df.map(row => row.getInt(1))
      results(0) shouldBe 1
    }
    it("Rolling Window Count") {
      val today = LocalDate.of(2021, 11, 3)
      val day1 = Date.valueOf("2020-11-01")
      val day2 = Date.valueOf("2021-11-02")
      val day3 = Date.valueOf("2021-11-03")

      val ifa1 = Vector[Byte](1)
      val ifa2 = Vector[Byte](2)
      val ifa3 = Vector[Byte](3)

      assert(_distinctCountRollingWindow(7, today, null) == -1)

      val datesMaps = Map[IfaVec, Date]( ifa1 -> day1, ifa2 -> day2, ifa3 -> day3)
      val out = _distinctCountRollingWindow(7, today, datesMaps)
      out shouldBe 2
    }
  }

  describe("Testing toVecMapWithDate") {
    val ifas : mutable.WrappedArray[Ifa] = Array(
      Array[Byte](1),
      Array[Byte](2),
      Array[Byte](3)
    )
    //val date = LocalDate.of(2021, 12, 21)
    val date = Date.valueOf("2021-12-21")
    val localDate = LocalDate.of(2021, 12, 21)

    it("Test with null date"){
      val testDf = Seq(
        (1, 1, date, null)
      ).toDF("lat", "lon", "date", "ifa").
        groupBy("lat", "lon").
        agg(collect_set($"ifa") as "ifas").
        withColumn("last_seen_map", toVecMapWithDate(null)($"ifas")).
        collect()

      val x = testDf.map(row => row.getMap[IfaVec, Date](3))
      x(0) shouldBe Map.empty[IfaVec, Date]
    }
    it("Test with null ifas"){
      val testDf = Seq(
        (1, 1, date, null)
      ).toDF("lat", "lon", "date", "ifa").
        groupBy("lat", "lon").
        agg(collect_set($"ifa") as "ifas").
        withColumn("last_seen_map", toVecMapWithDate(localDate)($"ifas")).
        collect()

      val x = testDf.map(row => row.getMap[IfaVec, Date](3))
      x(0) shouldBe Map.empty[IfaVec, Date]
    }
    it("Test with empty list of ifas"){
      val testDf = Seq(
        (1, 1, date, null),
        (1, 1, date, null)
      ).toDF("lat", "lon", "date", "ifa").
        groupBy("lat", "lon").
        agg(collect_set($"ifa") as "ifas").
        withColumn("last_seen_map", toVecMapWithDate(localDate)($"ifas")).
        collect()

      val x = testDf.map(row => row.getMap[IfaVec, Date](3))
      x(0) shouldBe Map.empty[IfaVec, Date]
    }
    it("Test with list of ifas"){
      val testDf = Seq(
        (1, 1, date, Array[Byte](1)),
        (1, 1, date, Array[Byte](2)),
        (1, 1, date, Array[Byte](3))
      ).toDF("lat", "lon", "date", "ifa").
        groupBy("lat", "lon").
        agg(collect_set($"ifa") as "ifas").
        withColumn("last_seen_map", toVecMapWithDate(localDate)($"ifas")).
        collect()

      val x = testDf.map(row => row.getMap[IfaVec, Date](3))

      x(0) shouldBe Map(
        Vector(1) -> date,
        Vector(2) -> date,
        Vector(3) -> date
      )

    }

  }

  describe("Testing lastSeenMapCombine"){
    // 30 day TTL, prod will have 120
    val ttlDate = Date.valueOf("2021-11-01")
    it("Should handle null value for both maps"){
      val curr : Map[Seq[Byte], Date] = null
      val prev : Map[Seq[Byte], Date] = null
      val df = Seq((curr, prev)).
        toDF("curr", "prev").
        withColumn("merge", lastSeenMapCombine(ttlDate)($"prev",$"curr")).
        collect()
      val results = df.map(row => row.getMap[Seq[Byte], Date](2))
      results(0) shouldBe Map.empty[Seq[Byte], Date]
    }
    it("Should handle null value for previous day map"){
      val curr : Map[Seq[Byte], Date] = Map(Seq(1.toByte) -> Date.valueOf("2021-12-01"))
      val prev : Map[Seq[Byte], Date] = null
      val df = Seq((curr, prev)).
        toDF("curr", "prev").
        withColumn("merge", lastSeenMapCombine(ttlDate)($"prev",$"curr")).
        collect()
      val results = df.map(row => row.getMap[Seq[Byte], Date](2))
      results(0) shouldBe curr
    }
    it("Should handle null value for current day map"){
      val curr : Map[Seq[Byte], Date] = null
      val prev : Map[Seq[Byte], Date] = Map(Seq(1.toByte) -> Date.valueOf("2021-12-01"))
      val df = Seq((curr, prev)).
        toDF("curr", "prev").
        withColumn("merge", lastSeenMapCombine(ttlDate)($"prev",$"curr")).
        collect()
      val results = df.map(row => row.getMap[Seq[Byte], Date](2))
      results(0) shouldBe prev
    }
    it("Should handle null value for current day map and expired previous map"){
      val curr : Map[Seq[Byte], Date] = null
      val prev : Map[Seq[Byte], Date] = Map(Seq(1.toByte) -> Date.valueOf("2021-10-01"))
      val df = Seq((curr, prev)).
        toDF("curr", "prev").
        withColumn("merge", lastSeenMapCombine(ttlDate)($"prev",$"curr")).
        collect()
      val results = df.map(row => row.getMap[Seq[Byte], Date](2))
      results(0) shouldBe Map()
    }
    it("Should handle a new item in current day map (add)") {
      val curr : Map[Seq[Byte], Date] = Map(Seq(2.toByte) -> Date.valueOf("2021-12-02"))
      val prev : Map[Seq[Byte], Date] = Map(Seq(1.toByte) -> Date.valueOf("2021-12-01"))
      val df = Seq((curr, prev)).
        toDF("curr", "prev").
        withColumn("merge", lastSeenMapCombine(ttlDate)($"prev",$"curr")).
        collect()
      val results = df.map(row => row.getMap[Seq[Byte], Date](2))
      results(0) shouldBe Map(
        Seq(1.toByte) -> Date.valueOf("2021-12-01"),
        Seq(2.toByte) -> Date.valueOf("2021-12-02")
      )
    }
    it("Should handle merge items that exists in current and previous day (Change / Touch)"){
      val curr : Map[Seq[Byte], Date] = Map(Seq(1.toByte) -> Date.valueOf("2021-12-02"))
      val prev : Map[Seq[Byte], Date] = Map(Seq(1.toByte) -> Date.valueOf("2021-12-01"))
      val df = Seq((curr, prev)).
        toDF("curr", "prev").
        withColumn("merge", lastSeenMapCombine(ttlDate)($"prev",$"curr")).
        collect()
      val results = df.map(row => row.getMap[Seq[Byte], Date](2))
      results(0) shouldBe Map(
        Seq(1.toByte) -> Date.valueOf("2021-12-02")
      )
    }
    it("Should handle a key that exists in previous day map only and should NOT expire (persist)"){
      val curr : Map[Seq[Byte], Date] = Map(Seq(2.toByte) -> Date.valueOf("2021-12-02"))
      val prev : Map[Seq[Byte], Date] = Map(Seq(1.toByte) -> Date.valueOf("2021-11-15"))
      val df = Seq((curr, prev)).
        toDF("curr", "prev").
        withColumn("merge", lastSeenMapCombine(ttlDate)($"prev",$"curr")).
        collect()
      val results = df.map(row => row.getMap[Seq[Byte], Date](2))
      results(0) shouldBe Map(
        Seq(2.toByte) -> Date.valueOf("2021-12-02"),
        Seq(1.toByte) -> Date.valueOf("2021-11-15")
      )
    }
    it("Should handle a key that exists in previous day map only and should expire (delete)"){
      val curr : Map[Seq[Byte], Date] = Map(Seq(2.toByte) -> Date.valueOf("2021-12-02"))
      val prev : Map[Seq[Byte], Date] = Map(Seq(1.toByte) -> Date.valueOf("2021-10-01"))
      val df = Seq((curr, prev)).
        toDF("curr", "prev").
        withColumn("merge", lastSeenMapCombine(ttlDate)($"prev",$"curr")).
        collect()
      val results = df.map(row => row.getMap[Seq[Byte], Date](2))
      results(0) shouldBe Map(
        Seq(2.toByte) -> Date.valueOf("2021-12-02")
      )
    }
    it("Last Seen Map Combine") {
      val dayTtl = Date.valueOf("2020-11-01")
      val day1 = Date.valueOf("2021-11-01")
      val day2 = Date.valueOf("2021-11-02")

      val ifa1 = Seq[Byte](1)
      val ifa2 = Seq[Byte](2)
      val ifa3 = Seq[Byte](3)
      val ifa4 = Seq[Byte](4)

      val prev = Map[Seq[Byte], Date]( ifa1 -> day1, ifa2 -> day1, ifa4 -> dayTtl)
      val next = Map[Seq[Byte], Date]( ifa1 -> day1, ifa2 -> day2, ifa3 -> day2)

      val out = _lastSeenMapCombine(Date.valueOf("2021-10-01"), prev, next)

      out.size shouldBe 3

      out(ifa1) shouldBe day1
      out(ifa2) shouldBe day2
      out(ifa3) shouldBe day2
    }
  }

  describe("Testing dayHourCombine"){
    val dayHours = Array.ofDim[Int](24)
    it("Null history") {
      val day0: HourHistory = _dayHourCombine(null, dayHours).get
      day0.i shouldBe 1

      val results = Seq(dayHours).toDF("day_hours")
        .withColumn("day_hours_history", lit(null))
        .withColumn("day_hours_history", dayHourCombine($"day_hours_history", $"day_hours"))
        .collect()

      (results(0).getAs[Row](1)).getAs[Int](0) shouldBe 1
    }

    it("First Day"){
      var history: Option[HourHistory] = Some(HourHistory.empty())
      history = _dayHourCombine(toRowOption(history), dayHours)
      history.getOrElse(HourHistory.empty()).i shouldBe 1

      val results = Seq(dayHours).toDF("day_hours").
        withColumn("day_hours_history", emptyHourHistory()).
        withColumn("day_hours_history", dayHourCombine($"day_hours_history", $"day_hours")).
        collect()

      (results(0).getAs[Row](1)).getAs[Int](0) shouldBe 1
    }

    it("Two consecutive days"){
      var history: Option[HourHistory] = Some(HourHistory.empty())
      history = _dayHourCombine(toRowOption(history), dayHours)
      history = _dayHourCombine(toRowOption(history), dayHours)
      history.getOrElse(HourHistory.empty()).i shouldBe 2

      val prev = Seq(dayHours).toDF("day_hours").
        withColumn("group", lit(1)).
        withColumn("day_hours_history", emptyHourHistory()).
        withColumn("day_hours_history", dayHourCombine($"day_hours_history", $"day_hours"))

      val results = Seq(dayHours).toDF("day_hours").
        withColumn("group", lit(1)).
        join(prev.select($"day_hours_history" as "prev_day_hours_history", $"group"), "group").
        withColumn("day_hours_history", dayHourCombine($"prev_day_hours_history", $"day_hours")).
        drop("prev_day_hours_history", "group", "day_hours").
        collect()

      (results(0).getAs[Row](0)).getAs[Int](0) shouldBe 2
    }

    it("Max + 1 days"){
      val hourHistory = HourHistory.empty()

      for (i <- 0 until 126) {
        for (j <- 0 until 24) {
          hourHistory.history(i)(j) = 1
        }
      }

      val hours = (0 until 24).map(x => 126).toArray
      val history = _dayHourCombine(toRowOption(Some(hourHistory)), hours)
      val results = history.getOrElse(HourHistory.empty())

      results.i shouldBe 1
      results.history(0) shouldBe (0 until 24).map(x => 126).toArray
      results.history(1) shouldBe (0 until 24).map(x => 1).toArray
    }

    it("Max + 1 days (Spark)"){
      val hourHistory = HourHistory.empty()

      for (i <- 0 until 126) {
        for (j <- 0 until 24) {
          hourHistory.history(i)(j) = 1
        }
      }

      val hours = (0 until 24).map(x => 126).toArray
      val results = Seq(hourHistory).toDF.
        // Workaround to convert back to dataframe
        select(struct("i", "history").as("prev_day_hours_history")).
        withColumn("day_hours", lit(hours)).
        withColumn("day_hours_history", dayHourCombine($"prev_day_hours_history", $"day_hours")).
        collect()

      val row = results(0).getAs[Row](2)
      val i = row.getAs[Int](0)
      val history = row.getAs[mutable.WrappedArray[mutable.WrappedArray[Int]]](1)

      i shouldBe 1
      history(0) shouldBe (0 until 24).map(x => 126).toArray
      history(1) shouldBe (0 until 24).map(x => 1).toArray
    }
  }

  describe("testing toCarrierCount"){

    it("Should handle ifa obs with 0 precise locs"){
      // (Carrier, date) -> (precise_loc -> ifa count) // does ifa count matter?
      val raw : Map[(String, Date), Map[Int, Int]] = Map(
        ("Carrier with 0 precise locs", Date.valueOf("2021-12-21")) -> Map()
      )
      val result = Seq(raw).toDF("carrier_map_wifi_history").
        withColumn("carrier_count_map_wifi", toCarrierCount($"carrier_map_wifi_history")).
        collect()
      result(0).getAs[Map[String, (Date, Date, Int)]](1).size shouldBe 0
    }
    it("Should handle ifa obs with 1 distinct precise locs"){
      // (Carrier, date) -> (precise_loc -> ifa count)
      val raw : Map[(String, Date), Map[Int, Int]] = Map(
        ("Carrier with 1 precise locs", Date.valueOf("2021-12-21")) -> Map(1->1)
      )
      val result = Seq(raw).toDF("carrier_map_wifi_history").
        withColumn("carrier_count_map_wifi", toCarrierCount($"carrier_map_wifi_history")).
        collect()
      result(0).getAs[Map[String, (Date, Date, Int)]](1).size shouldBe 0
    }
    it("Should handle ifa obs with more than 2 distinct precise locs"){
      // (Carrier, date) -> (precise_loc -> ifa count)
      val raw : Map[(String, Date), Map[Int, Int]] = Map(
        ("Carrier with 2 precise locs", Date.valueOf("2021-12-21")) -> Map(1->1, 2->1)
      )
      val result = Seq(raw).toDF("carrier_map_wifi_history").
        withColumn("carrier_count_map_wifi", toCarrierCount($"carrier_map_wifi_history")).
        collect()
      result(0).getAs[Map[String, (Date, Date, Int)]](1).size shouldBe 0
    }
    it("Should handle ifa obs with more than 3 distinct precise locs on a single day"){
      // (Carrier, date) -> (precise_loc -> ifa count)
      val raw : Map[(String, Date), Map[Int, Int]] = Map(
        ("Carrier with 3 precise locs", Date.valueOf("2021-12-21")) -> Map(1->1, 2->1, 3->1)
      )
      val result = Seq(raw).toDF("carrier_map_wifi_history").
        withColumn("carrier_count_map_wifi", toCarrierCount($"carrier_map_wifi_history")).
        collect()
      result(0).getAs[Map[String, (Date, Date, Int)]](1).size shouldBe 1
    }
    it("Should handle ifa obs with more than 3 distinct precise locs on multiple days"){
      // (Carrier, date) -> (precise_loc -> ifa count)
      val raw : Map[(String, Date), Map[Int, Int]] = Map(
        ("Carrier with 3 precise locs", Date.valueOf("2021-12-21")) -> Map(1->1, 2->1, 3->1),
        ("Carrier with 3 precise locs", Date.valueOf("2021-12-22")) -> Map(1->1, 2->1, 3->1),
        ("Carrier with 3 precise locs", Date.valueOf("2021-12-23")) -> Map(1->1, 2->1, 3->1)
      )

      val rows = Seq(raw).toDF("carrier_map_wifi_history").
        withColumn("carrier_count_map_wifi", toCarrierCount($"carrier_map_wifi_history")).
        collect()
      val result = rows(0).getAs[Map[String, GenericRowWithSchema]](1).map{
        case(k,v) => (k, CarrierSeen(v.getAs[Date](0), v.getAs[Date](1), v.getAs[Int](2)))
      }

      result shouldBe Map("Carrier with 3 precise locs" -> CarrierSeen(Date.valueOf("2021-12-21"),Date.valueOf("2021-12-23"),3))
    }
    it("Should handle multiple carriers"){
      // (Carrier, date) -> (precise_loc -> ifa count)
      val raw : Map[(String, Date), Map[Int, Int]] = Map(
        ("Carrier1", Date.valueOf("2021-12-21")) -> Map(1->1, 2->1, 3->1),
        ("Carrier1", Date.valueOf("2021-12-22")) -> Map(1->1, 2->1, 3->1),
        ("Carrier1", Date.valueOf("2021-12-23")) -> Map(1->1, 2->1, 3->1),
        ("Carrier2", Date.valueOf("2021-12-21")) -> Map(1->1, 2->1, 3->1),
        ("Carrier2", Date.valueOf("2021-12-22")) -> Map(1->1, 2->1, 3->1),
        ("Carrier2", Date.valueOf("2021-12-23")) -> Map(1->1, 2->1, 3->1)
      )

      val rows = Seq(raw).toDF("carrier_map_wifi_history").
        withColumn("carrier_count_map_wifi", toCarrierCount($"carrier_map_wifi_history")).
        collect()
      val result = rows(0).getAs[Map[String, GenericRowWithSchema]](1).map{
        case(k,v) => (k, CarrierSeen(v.getAs[Date](0), v.getAs[Date](1), v.getAs[Int](2)))
      }
      // order doesn't matter here!
      result shouldBe Map(
        "Carrier1" -> CarrierSeen(Date.valueOf("2021-12-21"),Date.valueOf("2021-12-23"),3),
        "Carrier2" -> CarrierSeen(Date.valueOf("2021-12-21"),Date.valueOf("2021-12-23"),3)
      )
    }
  }
}
