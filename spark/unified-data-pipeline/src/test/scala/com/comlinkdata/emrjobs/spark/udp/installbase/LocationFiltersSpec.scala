package com.comlinkdata.emrjobs.spark.udp.installbase

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.udp.installbase.LocationFilters._
import com.comlinkdata.largescale.commons.Utils
import com.comlinkdata.largescale.schema.udp.Ip
import com.comlinkdata.largescale.schema.udp.installbase.LocTs
import com.comlinkdata.largescale.schema.udp.location.Point
import com.comlinkdata.largescale.schema.udp.tier1.UdpIfaObservation
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.col
import org.scalatest.BeforeAndAfterEach

import java.sql.Timestamp
import java.time.LocalDateTime

case class SpecTestTLocal (
  t_local: Timestamp
)

case class SpecTestObs (
  obs: SpecTestTLocal
)

case class SpecTestIps (
  ip: Ip,
  index: Long
)


class LocationFiltersSpec extends CldSparkBaseSpec with LazyLogging with BeforeAndAfterEach {

  private val midnight = Timestamp.valueOf("1982-06-10 00:00:00")
  private val noon = Timestamp.valueOf("1982-06-10 12:00:00")
  private val onePastNoon = Timestamp.valueOf("1982-06-10 12:01:00")
  private val twoPastNoon = Timestamp.valueOf("1982-06-10 12:02:00")

  private val denver = Point(39.742043f, -104.991531f)
  private val winter_park = Point(39.8917f, -105.7631f)

  describe("Testing") {
    it("distance test") {
      val resultInM = distanceBetweenPts(denver.lat, denver.lng, winter_park.lat, winter_park.lng)
      Math.round(resultInM / 1000) shouldEqual 68
    }

    it("how many decimals") {
      howManyDecimals(12.1234f) shouldEqual 4
      howManyDecimals(12.12f) shouldEqual 2
      howManyDecimals(12.1f) shouldEqual 1
      howManyDecimals(12f) shouldEqual 1

      howManyDecimalsInPt(Point(12.1234f, 2.12f)) shouldEqual 6
    }

    it("is Night") {
      import spark.implicits._
      val testInstances = Seq(
        SpecTestObs(SpecTestTLocal(midnight)),
        SpecTestObs(SpecTestTLocal(noon)),
      )

      val result = spark.createDataFrame(testInstances).toDF("obs")
        .withColumn("isNight", isNightTime($"obs.t_local"))

      val collected = result.collect()

      collected(0).get(1) shouldBe true
      collected(1).get(1) shouldBe false
    }

    it("is home hours") {
      val testInstances = Seq(
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-02 01:00:00"))), //Mon Morning
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-02 12:00:00"))), //Mon Afternoon
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-02 20:00:00"))), //Mon Night
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-03 01:00:00"))), //Tue Morning
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-03 12:00:00"))), //Tue Afternoon
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-03 20:00:00"))), //Tue Night
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-04 01:00:00"))), //Wed Morning
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-04 12:00:00"))), //Wed Afternoon
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-04 20:00:00"))), //Wed Night
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-05 01:00:00"))), //Thr Morning
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-05 12:00:00"))), //Thr Afternoon
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-05 20:00:00"))), //Thr Night
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-06 01:00:00"))), //Fri Morning
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-06 12:00:00"))), //Fri Afternoon
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-06 20:00:00"))), //Fri Night
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-07 01:00:00"))), //Sat Morning
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-07 12:00:00"))), //Sat Afternoon
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-07 20:00:00"))), //Sat Night
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-08 01:00:00"))), //Sun Morning
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-08 12:00:00"))), //Sun Afternoon
        SpecTestObs(SpecTestTLocal(Timestamp.valueOf("2022-05-08 20:00:00"))), //Sun Night
      )

      val result = spark.createDataFrame(testInstances).toDF("obs")
        .withColumn("isHH", isHouseholdHour(col("obs.t_local")))
        .orderBy("obs.t_local")

      val collected = result.collect()

      collected(0).get(1) shouldBe true //Mon Morning
      collected(1).get(1) shouldBe false //Mon Afternoon
      collected(2).get(1) shouldBe true //Mon Night
      collected(3).get(1) shouldBe true //Tue Morning
      collected(4).get(1) shouldBe false //Tue Afternoon
      collected(5).get(1) shouldBe true //Tue Night
      collected(6).get(1) shouldBe true //Wed Morning
      collected(7).get(1) shouldBe false //Wed Afternoon
      collected(8).get(1) shouldBe true //Wed Night
      collected(9).get(1) shouldBe true //Thr Morning
      collected(10).get(1) shouldBe false //Thr Afternoon
      collected(11).get(1) shouldBe true //Thr Night
      collected(12).get(1) shouldBe true //Fri Morning
      collected(13).get(1) shouldBe false //Fri Afternoon
      collected(14).get(1) shouldBe false //Fri Night
      collected(15).get(1) shouldBe false //Sat Morning
      collected(16).get(1) shouldBe false //Sat Afternoon
      collected(17).get(1) shouldBe false //Sat Night
      collected(18).get(1) shouldBe false //Sun Morning
      collected(19).get(1) shouldBe false //Sun Afternoon
      collected(20).get(1) shouldBe true //Sun Night
    }

    it("ip truncated") {
      import spark.implicits._
      val testIps = Seq(
        SpecTestIps(Utils.ipStringToBinary(s"***************"), -1),
        SpecTestIps(Utils.ipStringToBinary(s"***************"), 4), //index shouldn't impact ipv4
        SpecTestIps(Utils.ipStringToBinary(s"*************"), -1),
        SpecTestIps(Utils.ipStringToBinary(s"*************"), 4), //index shouldn't impact ipv4
        SpecTestIps(Utils.ipStringToBinary(s"1337:1337:1337:1337:1337:1337:1337:1337"), -1),
        SpecTestIps(Utils.ipStringToBinary(s"1337:1337:1337:1337:1337:1337:1337:0000"), -1),
        SpecTestIps(Utils.ipStringToBinary(s"1337:1337:0000:0000:0000:0000:0000:0000"), -1),
        SpecTestIps(Utils.ipStringToBinary(s"123A:123A:123A:1000:0000:0000:0000:0000"), 10),
        SpecTestIps(Utils.ipStringToBinary(s"123A:123A:123A:0000:0000:0000:0000:0000"), 10),
      )

      val result:DataFrame = testIps.toDS()
        .withColumn("isTrunc", isTruncatedIP($"ip", $"index"))
        .orderBy($"ip".desc, $"index")

      val collected = result.collect()

      collected(0).get(2) shouldBe false
      collected(1).get(2) shouldBe false
      collected(2).get(2) shouldBe true
      collected(3).get(2) shouldBe true
      collected(4).get(2) shouldBe false
      collected(5).get(2) shouldBe false
      collected(6).get(2) shouldBe true
      collected(7).get(2) shouldBe false
      collected(8).get(2) shouldBe true
    }

    it("Time is a flat circle") {
      secondsBetweenTimeStamps(midnight, noon) shouldEqual 43200.0
      secondsFromMidNight(noon) shouldEqual 43200.0
      secondsFromMidNight(midnight) shouldEqual 0.0
      secondsFromMidNight(Timestamp.valueOf("1982-06-10 6:00:00")) shouldEqual 21600.0
      secondsFromMidNight(Timestamp.valueOf("1982-06-10 18:00:00")) shouldEqual 21600.0

      val obs1 = UdpIfaObservation(
        t_utc = midnight,
        t_local = Some(midnight),
        ip = None, location = Point(1f,1f),
        location_type = "Fake",
        connection_type = None,
        accuracy = None,
        gps_speed = None,
        place_id = None
      )

      val obs2 = UdpIfaObservation(
        t_utc = noon,
        t_local = Some(noon),
        ip = None, location = Point(1f,1f),
        location_type = "Fake",
        connection_type = None,
        accuracy = None,
        gps_speed = None,
        place_id = None
      )

      isPrevCloserToMidnight(obs1, obs2) shouldBe true
      isPrevCloserToMidnight(obs2, obs1) shouldBe false

      isPrevObsMoreAccOrCloserToMidnightThanPrev(obs1, obs2) shouldBe true
    }

    it("Point compressions") {
      val obs1 = UdpIfaObservation(
        t_utc = midnight,
        t_local = Some(midnight),
        ip = None, location = Point(1.123f,1.1234f),
        location_type = "Fake",
        connection_type = None,
        accuracy = None,
        gps_speed = None,
        place_id = None
      )

      val obs2 = UdpIfaObservation(
        t_utc = midnight,
        t_local = Some(midnight),
        ip = None, location = Point(1f,1f),
        location_type = "Fake",
        connection_type = None,
        accuracy = None,
        gps_speed = None,
        place_id = None
      )
      isPrevObsMoreAccOrCloserToMidnightThanPrev(obs1, obs2) shouldBe true
    }

    it("Night time centroid") {
      val pts = Seq(
        UdpIfaObservation(
          t_utc = midnight,
          t_local = Some(midnight),
          ip = None, location = Point(1f,1f),
          location_type = "Fake",
          connection_type = None,
          accuracy = None,
          gps_speed = None,
          place_id = None
        ),
        UdpIfaObservation(
          t_utc = midnight,
          t_local = Some(midnight),
          ip = None, location = Point(1f,1f),
          location_type = "Fake",
          connection_type = None,
          accuracy = None,
          gps_speed = None,
          place_id = None
        ),
        UdpIfaObservation(
          t_utc = noon,
          t_local = Some(noon),
          ip = None, location = Point(1000f,1000f),
          location_type = "Fake",
          connection_type = None,
          accuracy = None,
          gps_speed = None,
          place_id = None
        )

      )
      val nighttime_centroid = nightTimeCentroid(pts)
      nighttime_centroid shouldEqual Point(1f,1f)
    }

    it("Redundant Repeat Time Compression") {
      val obs = Seq(
        UdpIfaObservation(
          t_utc = noon,
          t_local = Some(noon),
          ip = None, location = Point(1f,1f),
          location_type = "Fake",
          connection_type = None,
          accuracy = None,
          gps_speed = None,
          place_id = None
        ),
        UdpIfaObservation(
          t_utc = onePastNoon,
          t_local = Some(onePastNoon),
          ip = None, location = Point(1f,1f),
          location_type = "Fake",
          connection_type = None,
          accuracy = None,
          gps_speed = None,
          place_id = None
        )
      )

      val result = filterWithinOneHour(obs)

      result.size shouldEqual 1
      result.head.t_local.get shouldEqual onePastNoon

      val resultReverse = filterWithinOneHour(obs.reverse)

      resultReverse.size shouldEqual 1
      resultReverse.head.t_local.get shouldEqual onePastNoon
    }

    it("We got fast movers") {
      val obs2Pts = Seq(
        UdpIfaObservation(
          t_utc = noon,
          t_local = Some(noon),
          ip = None, location = denver,
          location_type = "Fake",
          connection_type = None,
          accuracy = None,
          gps_speed = None,
          place_id = None
        ),
        UdpIfaObservation(
          t_utc = onePastNoon,
          t_local = Some(onePastNoon),
          ip = None, location = winter_park,
          location_type = "Fake",
          connection_type = None,
          accuracy = None,
          gps_speed = None,
          place_id = None
        )
      )

      val result = filterFastMovers(obs2Pts)
      result.size shouldEqual 1
      result.head.t_local.get shouldEqual onePastNoon

      val obs3Pts = Seq(
        UdpIfaObservation(
          t_utc = noon,
          t_local = Some(noon),
          ip = None, location = denver,
          location_type = "Fake",
          connection_type = None,
          accuracy = None,
          gps_speed = None,
          place_id = None
        ),
        UdpIfaObservation(
          t_utc = twoPastNoon,
          t_local = Some(twoPastNoon),
          ip = None, location = denver,
          location_type = "Fake",
          connection_type = None,
          accuracy = None,
          gps_speed = None,
          place_id = None
        ),
        UdpIfaObservation(
          t_utc = onePastNoon,
          t_local = Some(onePastNoon),
          ip = None, location = winter_park,
          location_type = "Fake",
          connection_type = None,
          accuracy = None,
          gps_speed = None,
          place_id = None
        )
      )

      val result3Pts = filterFastMovers(obs3Pts)
      result3Pts.size shouldEqual 2
      result3Pts.head.location shouldEqual denver

      println(result)
    }

    it("Loc Stats") {
      val ptsIn = Seq(
        Point(1f,1f),
        Point(1f,1f),
        Point(4f,4f)
      )

      val result = locStats(ptsIn)

      result.get.modal_loc.lat shouldEqual 1
      result.get.modal_loc.lng shouldEqual 1

      result.get.centroid.lat shouldEqual 2
      result.get.centroid.lng shouldEqual 2

      val result2 = locStats(Seq(denver, winter_park))

      val latAvg = (denver.lat + winter_park.lat)/2
      val lngAvg = (denver.lng + winter_park.lng)/2


      val distCentroid1 = distanceBetweenPts(denver.lat, denver.lng, latAvg, lngAvg)
      val distCentroid2 = distanceBetweenPts(winter_park.lat, winter_park.lng, latAvg, lngAvg)

      val diffComputed = (distCentroid1 + distCentroid2)/2

      (result2.get.avg_centroid_dist - diffComputed) should be < 0.1
    }

    it("MPS between points") {
      val obs1 = UdpIfaObservation(
        t_utc = twoPastNoon,
        t_local = Some(twoPastNoon),
        ip = None, location = denver,
        location_type = "Fake",
        connection_type = None,
        accuracy = None,
        gps_speed = None,
        place_id = None
      )

      val obs2 = UdpIfaObservation(
        t_utc = onePastNoon,
        t_local = Some(onePastNoon),
        ip = None, location = winter_park,
        location_type = "Fake",
        connection_type = None,
        accuracy = None,
        gps_speed = None,
        place_id = None
      )

      val mps = mpsBetweenPoints(obs1, obs2)

      mps should be < 1133.0
      mps should be > 1132.0
    }

    it("modal pts test") {
      val points = Seq(
        Point(41.8781f, -87.6298f),
        Point(41.8781f, -87.6298f),
        Point(41.8781f, -87.6298f),
        Point(21.3454f, -158.08421f),
        Point(21.3454f, -158.08421f),
        Point(21.3454f, -158.0842f),
        Point(21.3454f, -158.08421f)
      )
      val modalPoint = LocationFilters.modal4DigitPoint(points)

      modalPoint shouldEqual Point(21.3454f, -158.08421f)

      val modalPoints = LocationFilters.getThreeDigitModalPoints(points, -1)

      modalPoints.size shouldBe 2
      modalPoints(Point(21.3454f, -158.08421f)) shouldBe 4

      val modalPointsOnly1 = LocationFilters.getThreeDigitModalPoints(points, 1)

      modalPointsOnly1.size shouldBe 1
      modalPointsOnly1.head._2 shouldBe 4
    }

    val testBadGPS5Repeats = IndexedSeq(Point(42.42887f, -83.08115f),
      Point(42.4292f, -83.08411f),
      Point(42.4292f, -83.08411f),
      Point(42.4292f, -83.08411f),
      Point(42.4292f, -83.08412f),
      Point(42.4292f, -83.08412f),
      Point(42.4292f, -83.08412f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.428818f, -83.08125f),
      Point(42.42881f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.42881f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.42887f, -83.08121f),
      Point(42.42888f, -83.08122f),
      Point(42.42888f, -83.08122f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.428818f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.42881f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.42881f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.42882f, -83.08126f),
      Point(42.428818f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.42881f, -83.08125f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.42881f, -83.08125f),
      Point(42.42881f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.42882f, -83.08126f),
      Point(42.42881f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.428818f, -83.08125f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.42882f, -83.08126f),
      Point(42.42888f, -83.08122f),
      Point(42.428814f, -83.08115f),
      Point(42.42881f, -83.08115f),
      Point(42.428814f, -83.08115f),
      Point(42.42882f, -83.08116f),
      Point(42.42882f, -83.08116f),
      Point(42.42882f, -83.08116f),
      Point(42.42882f, -83.08116f),
      Point(42.42882f, -83.08116f),
      Point(42.42882f, -83.08116f),
      Point(42.428814f, -83.08115f),
      Point(42.428814f, -83.08115f),
      Point(42.42881f, -83.08115f),
      Point(42.428814f, -83.08115f),
      Point(42.428814f, -83.08115f),
      Point(42.42881f, -83.08115f),
      Point(42.42882f, -83.08116f),
      Point(42.42882f, -83.08116f),
      Point(42.42882f, -83.08116f),
      Point(42.42887f, -83.08121f),
      Point(42.42887f, -83.08121f),
      Point(42.42887f, -83.08121f),
      Point(42.42887f, -83.08121f),
      Point(42.42888f, -83.08122f),
      Point(42.42888f, -83.08122f),
      Point(42.42888f, -83.08122f),
      Point(42.42888f, -83.08122f),
      Point(42.42888f, -83.08122f),
      Point(42.42888f, -83.08122f)
    )

    it("test GPS5") {
      val ts = testBadGPS5Repeats.map(LocTs(_, Timestamp.valueOf(LocalDateTime.now())))
      val result = runDBScan(ts, LocationStats(Point[Float](0f, 0f), Point[Float](0f, 0f), 0f, 0f))

      result.db_count shouldEqual Some(68)
    }
  }
}
