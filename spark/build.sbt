import CldEnv.publishCldEnvResourceTask
import EMR._
import ProjectMatrixOps._

initialize := {
  val _ = initialize.value // run the previous initialization
  val required = "1.8"
  val current = sys.props("java.specification.version")
  assert(current == required, s"Unsupported JDK: java.specification.version $current != $required")
}

enablePlugins(GitVersioning)
val scala_2_11 = "2.11.12"
val scala_2_12 = "2.12.15"

Global / excludeLintKeys := Set(deployJarDryRun)

ThisBuild / organization := "com.comlinkdata"
ThisBuild / version := s"2.0-${git.gitCurrentBranch.value}-SNAPSHOT"
ThisBuild / scalaVersion := scala_2_12
ThisBuild / autoAPIMappings := true
ThisBuild / deployJarRepo := "emr-jobs"

ThisBuild / scalacOptions += "-deprecation"
ThisBuild / console / initialCommands +=
        """|import org.apache.spark.sql._
           |
           |implicit val spark: SparkSession = SparkSession.builder().appName("console runner").
           |  config("spark.master", "local").
           |  config("spark.hadoop.fs.s3a.aws.credentials.provider", "com.amazonaws.auth.DefaultAWSCredentialsProviderChain").
           |  getOrCreate()
           |
           |import spark.implicits._
           |""".stripMargin


def commonSettings(emr: EMR) = Seq(
  scalacOptions += "-target:jvm-1.8",
  javacOptions ++= Seq("-target", "1.8", "-Xlint"),
  resolvers ++= Seq(
    Resolvers.cldReleases, Resolver.mavenCentral, Resolvers.jitpack, Resolvers.mulesoft, Resolvers.osGeo
  ),
  publishTo := Some(if (isSnapshot.value) Resolvers.cldSnapshots else Resolvers.cldReleases),
  assembly / assemblyMergeStrategy := {
    case PathList("META-INF", "services",  _*) => MergeStrategy.concat
    case PathList("META-INF", _*) => MergeStrategy.discard
    case _ => MergeStrategy.first
  },
  libraryDependencies ++= emr.sparkLibs
          .flatMap(r => Seq(r % Provided, r % Test))
          ++ emr.jacksonModuleScalaLib.map(_.exclude("log4j", "*"))
          ++ Seq(
    Libs.scopt,
    Libs.ip4s,
    Libs.timeshape,
    Libs.scalaLogging,
    Libs.sparkRedshift % Provided,
    emr.nettyLib,
    emr.awsSdkLib % Provided,
    Libs.scalaTest % Test,
    Libs.scalamock % Test,
    Libs.scalacheck % Test,
    emr.sparkTestingBaseLib % Test
  )
)

// PROJECTS

lazy val commons = projectMatrix
        .crossCompile(EMR_5_17_0, scala_2_11, commonSettings)
        .crossCompile(EMR_6_4_0, scala_2_12, commonSettings)

// specific versions
lazy val commonsEMR_5_17_0 = commons.only(EMR_5_17_0, scala_2_11)
lazy val commonsEMR_6_4_0 = commons.only(EMR_6_4_0, scala_2_12)

lazy val `csv-converter` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/csv-converter",
          commonSettings(EMR_6_4_0)
        )

lazy val georesolver = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/georesolver",
          commonSettings(EMR_6_4_0),
          libraryDependencies ++= Libs.geoToolsWrapper +: EMR_6_4_0.sedonaLibs
        )

lazy val comcast = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/comcast",
          commonSettings(EMR_6_4_0),
          libraryDependencies += Libs.redshiftSDK
        )

lazy val `comcast-early-fiber` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/comcast-early-fiber",
          commonSettings(EMR_6_4_0)
        )

lazy val `high-confidence-svt` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/high-confidence-svt",
          commonSettings(EMR_6_4_0)
        )

lazy val b2bbroadband = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          commonSettings(EMR_6_4_0),
          deployJarPathInRepo := "spark/b2bbroadband"
        )

lazy val tapad = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/tapad",
          commonSettings(EMR_6_4_0),
          Compile / resourceGenerators += publishCldEnvResourceTask
        )

lazy val `jsonline-processor` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/jsonline-processor",
          commonSettings(EMR_6_4_0)
        )

lazy val broadband = projectMatrix
  .enablePlugins(DeployJarPlugin)
  .dependsOn(commons, commons % "test->test")
  .settings(
    deployJarPathInRepo := "spark/broadband",
    updateOptions := updateOptions.value.withLatestSnapshots(false),
    libraryDependencies ++= Seq(
      Libs.magellan,
      Libs.sparkRedshift,
      Libs.redshiftSDK % Test,
      EMR_5_17_0.hadoopAwsLib % Test
    ),
    Compile / run / mainClass := Some("com.comlinkdata.emrjobs.spark.broadband.Router"),
    Test / fork := true,
    Compile / resourceGenerators += publishCldEnvResourceTask
  )
  .crossCompile(EMR_5_17_0, scala_2_11, commonSettings)
  .only(EMR_5_17_0, scala_2_11)


lazy val `broadband-customer-base` = projectMatrix
  .enablePlugins(DeployJarPlugin)
  .dependsOn(commons, commons % "test->test")
  .settings(
    deployJarPathInRepo := "spark/broadband-customer-base",
    updateOptions := updateOptions.value.withLatestSnapshots(false),
    libraryDependencies ++= Seq(
      Libs.sparkRedshift,
      Libs.magellan
    ),
    Test / fork := true
  )
  .crossCompile(EMR_5_17_0, scala_2_11, commonSettings)
  .only(EMR_5_17_0, scala_2_11)

lazy val `broadband-market-share` = project
  .enablePlugins(DeployJarPlugin)
  .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test", georesolver, georesolver % "test->test")
  .settings(
    deployJarPathInRepo := "spark/broadband-market-share",
    commonSettings(EMR_6_4_0),
    libraryDependencies ++= Seq(
      Libs.scalamock % Test,
      EMR_6_4_0.postgresLib
    ),
    Compile / resourceGenerators += publishCldEnvResourceTask
  )

lazy val `device-master-table` = project
  .enablePlugins(DeployJarPlugin)
  .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
  .settings(
    deployJarPathInRepo := "spark/device-master-table",
    commonSettings(EMR_6_4_0),
    libraryDependencies ++= Seq(
      Libs.uapScala,
      Libs.scalamock % Test
    ),
    Compile / resourceGenerators += publishCldEnvResourceTask
  )

lazy val `udp-device-switching` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/udp-device-switching",
          commonSettings(EMR_6_4_0),
          libraryDependencies ++= Seq(Libs.uapScala)
        )

lazy val `cellular-fixed-wireless` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test", georesolver, georesolver % "test->test")
        .settings(
          deployJarPathInRepo := "spark/cellular-fixed-wireless",
          commonSettings(EMR_6_4_0),
          libraryDependencies ++= Seq(
            Libs.gtEpsgHsql,
            Libs.gtReferencing,
            Libs.geoToolsWrapper,
            Libs.rTree2
          ) ++ EMR_6_4_0.sedonaLibs,
          Compile / resourceGenerators += publishCldEnvResourceTask
        )

lazy val `custom-regions` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/custom-regions",
          commonSettings(EMR_6_4_0),
          libraryDependencies ++= Seq(Libs.uapScala)
        )

lazy val `csp-forecasting` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/csp-forecasting",
          commonSettings(EMR_6_4_0)
        )

lazy val liveramp = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0)
        .settings(
          deployJarPathInRepo := "spark/liveramp",
          commonSettings(EMR_6_4_0)
        )

lazy val `data-tests` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0)
        .settings(
          deployJarPathInRepo := "spark/data-tests",
          commonSettings(EMR_6_4_0),
          libraryDependencies ++= Seq(EMR_6_4_0.sqlServerConnectorLib("2.12"))
        )

lazy val `non-pii-lima` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0)
        .settings(
          deployJarPathInRepo := "spark/non-pii-lima",
          commonSettings(EMR_6_4_0)
        )

lazy val `sharetracker-b2b-bbca-udp` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          commonSettings(EMR_6_4_0),
          deployJarPathInRepo := "spark/sharetracker-b2b-bbca-udp",
          libraryDependencies ++= Seq(EMR_6_4_0.nettyLib)
        )

lazy val `sharetracker-tablet` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/sharetracker-tablet",
          commonSettings(EMR_6_4_0)
        )

lazy val `unified-data-pipeline` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test", georesolver, georesolver % "test->test")
        .settings(
          deployJarPathInRepo := "spark/unified-data-pipeline",
          commonSettings(EMR_6_4_0),
          libraryDependencies ++= Seq(EMR_6_4_0.sparkMlLib % Provided, EMR_6_4_0.nettyLib),
          libraryDependencies += Libs.rTree2,
          libraryDependencies += Libs.uapScala,
          Compile / resourceGenerators += publishCldEnvResourceTask
        )


lazy val mvno = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/mvno",
          commonSettings(EMR_6_4_0),
          libraryDependencies ++= Seq(EMR_6_4_0.sqlServerConnectorLib("2.12"), EMR_6_4_0.postgresLib)
        )

lazy val `oracle-zip-scoring` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/oracle-zip-scoring",
          commonSettings(EMR_6_4_0)
        )

lazy val `legacy-sharetracker-datasourcing` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/legacy-sharetracker-datasourcing",
          commonSettings(EMR_6_4_0)
        )

lazy val `data-transfer` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/data-transfer",
          commonSettings(EMR_6_4_0),
          libraryDependencies ++= Seq(EMR_6_4_0.sqlServerConnectorLib("2.12"), EMR_6_4_0.postgresLib)
        )

lazy val `wireless-market-share` = project
        .enablePlugins(DeployJarPlugin)
        .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
        .settings(
          deployJarPathInRepo := "spark/wireless-market-share",
          commonSettings(EMR_6_4_0),
          libraryDependencies ++= Seq(EMR_6_4_0.sqlServerConnectorLib("2.12"), EMR_6_4_0.postgresLib)
        )

lazy val `first-party-ingestion` = project
  .enablePlugins(DeployJarPlugin)
  .dependsOn(commonsEMR_6_4_0, commonsEMR_6_4_0 % "test->test")
  .settings(
    deployJarPathInRepo := "spark/first-party-ingestion",
    commonSettings(EMR_6_4_0)
  )

lazy val root: Project = (project in file("."))
        .enablePlugins(ScalaUnidocPlugin)
        .enablePlugins(DeployUnidocPlugin)
        .settings(
          ScalaUnidoc / unidoc / unidocProjectFilter := inAnyProject -- inProjects(
            commonsEMR_5_17_0, broadband, `broadband-customer-base`),
          addMappingsToSiteDir(ScalaUnidoc / packageDoc / mappings, ScalaUnidoc / siteSubdirName),
          Keys.`package` := file(""),
          ScalaUnidoc / siteSubdirName := "api",
          deployJarDryRun := true // don't publish root
        )

// API documentation for EMR 5.17 projects
lazy val api = projectMatrix
        .enablePlugins(ScalaUnidocPlugin)
        .enablePlugins(DeployUnidocPlugin)
        .settings(
          deployJarDryRun := true,
          ScalaUnidoc / siteSubdirName := "api",
          ScalaUnidoc / unidoc / unidocProjectFilter := inProjects(
            commonsEMR_5_17_0, broadband, `broadband-customer-base`),
          addMappingsToSiteDir(ScalaUnidoc / packageDoc / mappings, ScalaUnidoc / siteSubdirName),
          deployUnidocDestSubdir := "emr-5.17"
        )
        .crossCompile(EMR_5_17_0, scala_2_11)
        .only(EMR_5_17_0, scala_2_11)
