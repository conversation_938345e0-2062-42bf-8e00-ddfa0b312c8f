package com.comlinkdata.mvno.ca.customer.base

import com.comlinkdata.largescale.commons.RedshiftUtils.RedshiftConfig
import com.comlinkdata.largescale.commons.SqlServerUtils.SqlServerConfig
import com.comlinkdata.largescale.commons.Utils

import java.time.LocalDate

object CanadaCustomerBaseSchema {

  /**
    * Configuration Class for the Customer base
    *
    * @param jobUniqueId               : This is assigned the cluster id of the EMR Cluster
    * @param jobName                   : Job Name
    * @param clientName                : Client Name
    * @param dataVersion               : WCV Data Version
    * @param env                       : Environment
    * @param defaultMinDate            : Default Minimum Start Date
    * @param minDate                   : Start Date
    * @param maxDate                   : End Date
    * @param ffcTable                  : FFC Table
    * @param ocnTable                  : OCN Data
    * @param rcIdTable                 : RC Id Table
    * @param seqCombinedCountSpPairs   : Sequential Combined Count SP Pairs
    * @param redshiftCustomerbaseTable : Redshift Customer Base Table
    * @param programStage              : Program Stage
    * @param region                    : Region
    * @param sqlServerConfig           : SQL Server Configuration
    * @param redshiftConfig            : Redshift Configuration
    */
  case class CanadaCustomerBaseConfig(
    jobUniqueId: String,
    jobName: String,
    clientName: String,
    dataVersion: String,
    env: String,
    defaultMinDate: LocalDate,
    minDate: LocalDate,
    maxDate: LocalDate,
    ffcTable: String,
    ocnTable: String,
    rcIdTable: String,
    seqCombinedCountSpPairs: Integer,
    redshiftCustomerbaseTable: String,
    programStage: String,
    region: String,
    sqlServerConfig: SqlServerConfig,
    redshiftConfig: RedshiftConfig
  ) {
    @transient
    implicit lazy val rc: RedshiftConfig = redshiftConfig
    @transient
    implicit lazy val sc: SqlServerConfig = sqlServerConfig
  }


  /**
    * FFC Record
    *
    * @param tn                         : Telephone Number
    * @param churn_timestamp            : Churn Timestamp
    * @param churn_type                 : Churn Type
    * @param churn_winning_ocn          : Winning OCN
    * @param churn_losing_ocn           : Losing OCN
    * @param noncompetitive_ind         : Non Competitive Indicator
    * @param rc_id                      : Rate Center Identifier
    * @param govt_contract_ind          : Government Contract Indicator,
    * @param churn_losing_sp            : Churn Losing SP
    * @param churn_losing_mode          : Churn Losing Mode
    * @param churn_winning_sp           : Churn Winning SP
    * @param churn_winning_mode         : Churn Winning Mode
    * @param prev_churn_losing_sp       : Previous Churn Losing SP
    * @param source_ind                 : Source Indicator
    * @param merger_id                  : Merger Id
    * @param tn_intra_flows_only_flag   : Indicator if TN is just Intra or not
    * @param trans_lag                  : Trnas Lag, this is the period the subscriber spent with his previous provier
    * @param adjusted_wins              : Adjusted Wins
    */
  case class CanadaFFCRecord(
    churn_timestamp: String,
    tn: String,
    churn_type: String,
    churn_winning_ocn: String,
    churn_losing_ocn: String,
    noncompetitive_ind: Integer,
    rc_id: String,
    govt_contract_ind: Boolean,
    churn_losing_sp: Integer,
    churn_losing_mode: String,
    churn_winning_sp: Integer,
    churn_winning_mode: String,
    prev_churn_losing_sp: String,
    seqcombinedcount_sppairs: Integer,
    source_ind: String,
    merger_id: String,
    tn_intra_flows_only_flag: Boolean,
    trans_lag: Integer,
    adjusted_wins: Double
  )

  /**
    * Transition Data to fetch FFC Table for Customer base data geneation
    *
    * @param churn_timestamp            : Churn Timestamp
    * @param acquisition_date           : Acquisition Date is the Date Part of Churn Timestamp
    * @param tn                         : Telephone Number
    * @param churn_winning_ocn          : Winning OCN
    * @param churn_losing_ocn           : Losing OCN
    * @param noncompetitive_ind         : Non Competitive Indicator
    * @param rc_id                      : Rate Center Identifier
    * @param govt_contract_ind          : Government Contract Indicator,
    * @param churn_losing_sp            : Churn Losing SP
    * @param churn_losing_mode          : Churn Losing Mode
    * @param churn_winning_sp           : Churn Winning SP
    * @param churn_winning_mode         : Churn Winning Mode
    * @param prev_churn_losing_sp       : Previous Churn Losing SP
    * @param source_ind                 : Source Indicator
    * @param merger_id                  : Merger Id
    * @param tn_intra_flows_only_flag   : Indicator if TN is just Intra or not
    * @param trans_lag                  : Trnas Lag, this is the period the subscriber spent with his previous provier
    * @param adjusted_wins              : Adjusted Wins
    */
  case class CanadaFfcDataForCustomerBaseComputation(
    churn_timestamp: String,
    acquisition_date: LocalDate,
    tn: String,
    churn_winning_ocn: String,
    churn_losing_ocn: String,
    noncompetitive_ind: Integer,
    rc_id: String,
    govt_contract_ind: Boolean,
    churn_losing_sp: Integer,
    churn_losing_mode: String,
    churn_winning_sp: Integer,
    churn_winning_mode: String,
    prev_churn_losing_sp: String,
    source_ind: String,
    merger_id: String,
    tn_intra_flows_only_flag: Boolean,
    trans_lag: Integer,
    adjusted_wins: Double
  )
  /**
    * Class with computed/transformed FFC Data, this can store the final FFC structure that can be used for Customer base generation, this data is not on tn level
    *
    * @param acquisition_date          : Acquisition Date is the Date Part of Churn Timestamp
    * @param customer_base_date        : Customer base Date
    * @param current_holder_sp         : Current Churn Winning SP/ OCN level latest SP
    * @param previous_holder_sp        : Current Churn Losing SP/ OCN level latest SP
    * @param churn_winning_mode        : Churn Winning Mode
    * @param churn_losing_mode         : Churn Losing Mode
    * @param noncompetitive_ind        : Non Competitive Indicator
    * @param rc_id                      : Rate Center Identifier
    * @param govt_contract_ind          : Government Contract Indicator
    * @param prev_churn_losing_sp       : Previous Churn Losing SP
    * @param source_ind                 : Source Indicator
    * @param merger_id                  : Merger Id
    * @param tn_intra_flows_only_flag   : Indicator if TN is just Intra or not
    * @param current_tenure_months     : The number of months the subscribers retained his previous connection
    * @param tenancy_months            : Difference in months between Customer base date and Acquisition Date
    * @param adjusted_wins             : Adjusted Wins
    */
  case class CanadaConsolidatedFfcDataForCustomerBase(
    acquisition_date: LocalDate,
    customer_base_date: LocalDate,
    current_holder_sp: Integer,
    previous_holder_sp: Integer,
    churn_winning_mode: String,
    churn_losing_mode: String,
    noncompetitive_ind: Integer,
    rc_id: String,
    govt_contract_ind: Boolean,
    prev_churn_losing_sp: Integer,
    source_ind: String,
    merger_id: String,
    tn_intra_flows_only_flag: Boolean,
    current_tenure_months: Integer,
    tenancy_months: Integer,
    adjusted_wins: Double
  )

  /**
    * Customer Base Data Structure
    *
    * @param customer_base_date        : Customer Base  Date
    * @param current_holder_sp         : Current Churn Winning SP/ OCN level latest SP
    * @param previous_holder_sp        : Current Churn Losing SP/ OCN level latest SP
    * @param churn_winning_mode        : Churn Winning Mode
    * @param churn_losing_mode         : Churn Losing Mode
    * @param noncompetitive_ind        : Non Competitive Indicator
    * @param rc_id                      : Rate Center Identifier
    * @param govt_contract_ind          : Government Contract Indicator,
    * @param prev_churn_losing_sp       : Previous Churn Losing SP
    * @param source_ind                 : Source Indicator
    * @param merger_id                  : Merger Id
    * @param tn_intra_flows_only_flag   : Indicator if TN is just Intra or not
    * @param current_tenure_months     : The number of months the subscribers retained his previous connection
    * @param tenancy_months            : Difference in months between Customer base date and Acquisition Date
    * @param total_customers           : Total Number of Customers as of Customer base Date
    * @param total_wins                : Total Wins for the Customer base date
    * @param total_losses              : Total Losses for the Customer base date
    */
  case class CanadaCustomerBaseData(
    customer_base_date: LocalDate,
    current_holder_sp: Integer,
    previous_holder_sp: Integer,
    churn_winning_mode: String,
    churn_losing_mode: String,
    noncompetitive_ind: Integer,
    rc_id: String,
    govt_contract_ind: Boolean,
    prev_churn_losing_sp: Integer,
    source_ind: String,
    merger_id: String,
    tn_intra_flows_only_flag: Boolean,
    current_tenure_months: Integer,
    tenancy_months: Integer,
    total_customers: Double,
    total_wins: Double,
    total_losses: Double
  )

  object CanadaFFCRecord extends Utils.reflection.ColumnNames[CanadaFFCRecord]

  object CanadaFfcDataForCustomerBaseComputation extends Utils.reflection.ColumnNames[CanadaFfcDataForCustomerBaseComputation]

  object CanadaConsolidatedFfcDataForCustomerBase extends Utils.reflection.ColumnNames[CanadaConsolidatedFfcDataForCustomerBase]

  object CanadaCustomerBaseData extends Utils.reflection.ColumnNames[CanadaCustomerBaseData]


}
