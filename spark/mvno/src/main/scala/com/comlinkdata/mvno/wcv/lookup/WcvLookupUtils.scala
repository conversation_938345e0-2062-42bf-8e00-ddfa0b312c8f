package com.comlinkdata.mvno.wcv.lookup

import com.comlinkdata.largescale.commons.SqlServerUtils.{SqlServerConfig, loadDataFromSQLServer}
import com.comlinkdata.largescale.commons.{LocalDateRange, TimeSeriesLocation}
import com.comlinkdata.mvno.wcv.lookup.WcvLookupJobRunner.logger
import com.comlinkdata.mvno.wcv.lookup.WcvLookupSchema._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Column, DataFrame, Dataset, SaveMode, SparkSession}
import org.apache.spark.sql.expressions.{Window, WindowSpec}

import java.net.URI
import java.sql.Date
import java.time.LocalDate
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.types.{DoubleType, StringType}


class WcvLookupUtils(implicit spark: SparkSession) {

  val SP_LIST=Map("spectrum" -> 6105,
    "xfinity" -> 6052,
    "otherMvno" -> 66666)

  val THREE_PREV_WEEKS_IN_SEC: Int = -86400 * 7 * 3 - 100

  val CUT_OVER_YEAR=2017


  /**
    * This function outputs the specific day of the week between start date and end date
    * @param startDate: Start Date
    * @param endDate: End Date
    * @param dayOfWeekExpected: Day of the week, 7 for Sunday
    * @param spark: Spark Session
    * @return: Dates which satisfy the day of week condition
    */
  def getSpecificDayOfWeekInDateRange(startDate:LocalDate, endDate:LocalDate, dayOfWeekExpected:Int)(implicit spark: SparkSession):DataFrame = {
   import spark.implicits._

    val start = startDate.toEpochDay
    val end = endDate.toEpochDay
    val dates = (start to end).map(LocalDate.ofEpochDay(_)).toDF("days")
    dates.withColumn("sundayornot",dayofweek(col("days"))).filter($"sundayornot" === dayOfWeekExpected).select("days")
  }

/**
    * Generates the Start Date for Lookup Generation: If min date is present then that takes precedence, else if min date is null then take the latest date folder
    * @param timeSeriesDatePartitionLocation: Folder where observed data lookup are generated
    * @param configMinDate: Configuration Minimum Date if provided
    * @return : Date
    */
  def getLookupGenStartDate(timeSeriesDatePartitionLocation: TimeSeriesLocation, configMinDate: Option[LocalDate]): LocalDate = {
    configMinDate
      .getOrElse(timeSeriesDatePartitionLocation.latestDate.plusDays(1))
  }

  /**
    * Load IM data
    * @param sqlServerURL : Database URL
    * @param industryModelTableName: IM table name
    * @param userName : DB user name
    * @param paramStoreKey : AWS parameter store key
    * @param startDate : Start Date for the Lookup
    * @param debugFlag Debug Flag
    * @param debugYear Debug Year
    * @param debugMonth Debug Month
    * @param debugChurnLosingSp Churn Losing SP to be verified
    * @param debugChurnWinningSp Churn Winning SP to be verified
    * @return : IM dataset
    */
  def loadIndustryModel(
    industryModelTableName:String,
    startDate: LocalDate)(implicit sc: SqlServerConfig,
    spark: SparkSession): Dataset[IndustryModel] = {
    import spark.implicits._

    val rawIndustryModel = loadDataFromSQLServer(
      industryModelTableName)

    rawIndustryModel
      .select(
        $"churn_year",
        $"churn_month",
        $"begin_plan_type_id",
        $"begin_mno_sp",
        $"begin_reference_sp",
        $"begin_observed_sp",
        $"end_plan_type_id",
        $"end_mno_sp",
        $"end_reference_sp",
        $"end_observed_sp",
        $"est_begin_brand_sp",
        $"est_end_brand_sp",
        $"est".cast(DoubleType).as("est"),
        $"port_est".cast(DoubleType).as("port_est"),
        $"losing_mno_winning_mno_prop".cast(DoubleType).as("losing_mno_winning_mno_prop"),
        $"losing_mno_prop".cast(DoubleType).as("losing_mno_prop"))
      .as[IndustryModel]
      .transform(filterIndustryModelRecordsPreviousToYear(CUT_OVER_YEAR))
      .transform(filterIndustryModelForStartDate(startDate))
  }

  /**
    * Filter prehistoric data, retain the data for December from the 1 year previous to Start Year
    * @param year: Start Year wrt IM
    * @param industryModelData Industry Model Dataset
    * @return : Industry Model Dataset
    */
  def filterIndustryModelRecordsPreviousToYear(year: Int)(industryModelData: Dataset[IndustryModel])(implicit spark: SparkSession): Dataset[IndustryModel] = {

    import spark.implicits._

    val prevYear = year - 1
    val decMonth = 12

    industryModelData
      .filter(
        ($"churn_year" === lit(prevYear)
          && $"churn_month" === lit(decMonth))
          || $"churn_year" >= lit(year))
  }

  /**
    * Delete all the data from IM which is earlier than 2 months from lookup start date
    * @param startDate : Lookup Start Date
    * @param industryModelData : IM dataset
    * @return : IM dataset
    */
  def filterIndustryModelForStartDate(startDate: LocalDate)(industryModelData: Dataset[IndustryModel])(implicit spark: SparkSession): Dataset[IndustryModel] = {

    import spark.implicits._

    val lookBackStartDate = startDate
      .minusMonths(2)

    industryModelData
      .filter(
        ($"churn_year" >= lookBackStartDate.getYear
          && $"churn_month" >= lookBackStartDate.getMonthValue)
          || $"churn_year" > lookBackStartDate.getYear)
  }


  /**
    * Load Lookup Configuration Data
    * @param configurationTableName: Lookup Configuration table name
    * @return Lookup Configuration Dataset
    */
  def loadLookupConfig(configurationTableName: String)(implicit sc: SqlServerConfig,
    spark: SparkSession): Dataset[LookupConfiguration] = {
    import spark.implicits._

    loadDataFromSQLServer(
      configurationTableName)
      .select(
        $"churn_type",
        $"begin_mno",
        $"begin_mno_sp",
        $"begin_brand",
        $"begin_brand_sp",
        $"begin_reference_sp",
        $"begin_observed_sp",
        $"begin_plan_type_id",
        $"begin_observation_type_id",
        $"begin_allocation_order",
        $"end_mno",
        $"end_mno_sp",
        $"end_brand",
        $"end_brand_sp",
        $"end_reference_sp",
        $"end_observed_sp",
        $"end_plan_type_id",
        $"end_observation_type_id",
        $"end_allocation_order",
        $"combined_observation_type_id",
        $"theoretical_is_observed".cast("boolean").as("theoretical_is_observed"),
        $"is_observed".cast("boolean").as("is_observed"),
        $"is_activation".cast("boolean").as("is_activation"),
        $"is_disconnect".cast("boolean").as("is_disconnect"),
        $"is_self_flow".cast("boolean").as("is_self_flow"),
        $"is_migration".cast("boolean").as("is_migration"),
        $"launch_date",
        coalesce(col("end_date"), current_date()).as("end_date"))
      .as[LookupConfiguration]
  }

  def extractNpaMnoMvnoCorrectionFactor(
    clientName : String,
    npaMnoMvnoCorrectionFactorTableName : String
  )(implicit sc: SqlServerConfig,
    spark: SparkSession): Dataset[NpaMnoMvnoCorrectionFactor] = {
    import spark.implicits._

    loadDataFromSQLServer(npaMnoMvnoCorrectionFactorTableName)
      .select(
        lit(clientName).as("clientName"),
        $"cld_network_operator",
        $"cld_brand_name",
        $"final_shapefile_complex",
        $"correction_factor")
      .withColumn(
        "correction_factor",
        when(
          $"clientName" === lit("cox")
            && $"cld_brand_name" === lit("Cox Wireless"),
          lit(1).cast("double")
        )
          .otherwise(
            $"correction_factor".cast("double")
          )
      )
      .as[NpaMnoMvnoCorrectionFactor]
  }


  /**
    *  Load Tutela NPA  level Aggregate Data
    * @param sqlServerURL : Database URL
    * @param userName : DB user name
    * @param paramStoreKey : AWS parameter store key
    * @param npaLookupTableName : NPA Lookup Table Name
    * @param npaMnoMvnoCorrectionFactorTableName: NPA MVNO Correction Factor Table Name
    * @param lookupGenMinDate: Start Date of the Lookup Generation
    * @param lookupGenMaxDate: End Date of the Lookup Generation
    * @return
    */
  def loadTutelaAgg(clientName:String, npaLookupTableName:String, npaMnoMvnoCorrectionFactorTableName:String, lookupGenMinDate:LocalDate, lookupGenMaxDate:LocalDate)(implicit sc: SqlServerConfig,
    spark: SparkSession): Dataset[TutelaAggData] ={
    import spark.implicits._

    val dateRangeData = getSpecificDayOfWeekInDateRange(
      lookupGenMinDate,
      lookupGenMaxDate,
      1)

    val npa_cf = extractNpaMnoMvnoCorrectionFactor(
      clientName,
      npaMnoMvnoCorrectionFactorTableName)

    val npa_lookup = loadDataFromSQLServer(
      npaLookupTableName)
      .as[NpaLookup]

    val tutelaData = npa_cf
      .join(
        npa_lookup,
        Seq("final_shapefile_complex"),
        "inner")
      .select(
        $"npa",
        $"cld_network_operator",
        $"cld_brand_name",
        $"correction_factor")
      .crossJoin(dateRangeData)
      .select(
        $"npa",
        $"cld_network_operator",
        $"cld_brand_name",
        $"correction_factor",
        $"days".alias("date"),
        year($"days").alias("year"),
        month($"days").alias("month")).as[TutelaAggData]

    tutelaData

  }

  /**
    * Load NPA level blcklist data
    * @param sqlServerURL : DB URL
    * @param blacklistDataTableName : Blacklist table name
    * @param userName : User name
    * @param paramStoreKey : AWS Parameter Store Key
    * @return : Blacklist Dataset
    */
  def loadBlacklistData(blacklistDataTableName: String)(implicit sc: SqlServerConfig,
    spark: SparkSession): Dataset[Blacklist] = {

    import spark.implicits._

    loadDataFromSQLServer(
      blacklistDataTableName)
      .select(
        $"npa",
        $"comcast_blocks_present".cast("boolean").as("comcast"),
        $"spectrum_blocks_present".cast("boolean").as("spectrum")
      )
      .as[Blacklist]
  }


  /**
    * Load Intra Flow Adjustment data
    * @param intraFlowAdjustementTableName : Intra Flow Adjustment data
    * @return : Intra Flow Adjustment Dataset
    */
  def loadIntraFlowAdjustementData(intraFlowAdjustementTableName: String)(implicit sc: SqlServerConfig,
    spark: SparkSession): Dataset[IntraFlowAdjustmentData] = {

    import spark.implicits._

    loadDataFromSQLServer(
      intraFlowAdjustementTableName)
      .select(
        $"date",
        $"begin_reference_sp",
        $"begin_brand_sp",
        $"begin_plan_type_id",
        $"end_reference_sp",
        $"end_brand_sp",
        $"end_plan_type_id",
        $"multiplier"
      )
      .as[IntraFlowAdjustmentData]
  }

  /**
    * Load Sub Monthly Flow Adjustment data
    * @param subMonthlyAdjustmentTableName : Sub Monthly Flow Adjustment data
    * @return : Inter Flow Adjustment Dataset
    */
  def loadSubMonthlyAdjustmentData(subMonthlyAdjustmentTableName: String)(implicit sc: SqlServerConfig,
    spark: SparkSession): Dataset[SubMonthlyAdjustmentData] = {

    import spark.implicits._

    loadDataFromSQLServer(
      subMonthlyAdjustmentTableName)
      .select(
        $"start_date",
        $"end_date",
        $"begin_plan_type_id",
        $"begin_mno_sp",
        $"begin_reference_sp",
        $"begin_observed_sp",
        $"end_plan_type_id",
        $"end_mno_sp",
        $"end_reference_sp",
        $"end_observed_sp",
        $"est_begin_brand_sp",
        $"est_end_brand_sp",
        $"est".cast(DoubleType).as("est"),
        $"port_est".cast(DoubleType).as("port_est"),
        $"losing_mno_winning_mno_prop".cast(DoubleType).as("losing_mno_winning_mno_prop"),
        $"losing_mno_prop".cast(DoubleType).as("losing_mno_prop")
      )
      .as[SubMonthlyAdjustmentData]
  }


  /**
    * Generate Normalized Values
    * @param columnName : Column Value to be Normalized
    * @param window : Normalization Window
    * @param normalizedColumnName : Resultant column
    * @param dataFrame: DataFrame
    * @return : DataFrame
    */
  def generateNormalizedValues(columnName:String,window:WindowSpec,normalizedColumnName:String)(dataFrame:DataFrame): DataFrame = {

    import spark.implicits._

    dataFrame
      .withColumn("total_sum",sum(col(columnName)).over(window))
      .withColumn(normalizedColumnName,when($"total_sum".isNull || $"total_sum" === lit( 0), 0)
        .otherwise(col(columnName)/col("total_sum")))

  }

  /**
    * Apply Geo Correction in which if Spectrum or Xfinity is not present in an area the IM Probability is adjusted to 0
    * @param spList : SP List
    * @param percentageColumn : Correction Factor Column
    * @param resultColumnName: Result Column Name
    * @param dataFrame : DataFrame
    * @return : DataFrame
    */
  def applyGeoCorrection(spList:Map[String,Int],percentageColumn:Column, resultColumnName:String)(dataFrame:DataFrame): DataFrame = {
    import spark.implicits._
    dataFrame
      .withColumn("geoCorrectionForNPAswithNoCoverage",  when(($"comcast" === false && ($"begin_brand_sp" === spList("xfinity")
        || $"end_brand_sp" === spList("xfinity")))
        ||( $"spectrum" === false
        && ($"end_brand_sp" === spList("spectrum")
        || $"begin_brand_sp" === spList("spectrum"))),0)
        .otherwise(1))
      .withColumn(resultColumnName,$"geoCorrectionForNPAswithNoCoverage"*percentageColumn)
  }

  /**
    * Apply the correction factors from winner and loser to the IM probability
    * @param probabilityColumn : Blacklist adjusted IM probability
    * @param winningCorrectionFactorColumn : Winning SP Tutela Correction Factor, If null set to 1
    * @param losingCorrectionFactorColumn : Losing SP Tutela Correction Factor, If null set to 1
    * @param resultColumnName : Result Column
    * @param dataFrame : Source DataFrame
    * @return : Result Dataframe
    */
  def applyTutelaCorrectionFactors(probabilityColumn:Column, winningCorrectionFactorColumn:Column,losingCorrectionFactorColumn:Column,resultColumnName:String)(dataFrame:DataFrame): DataFrame = {

    dataFrame
      .withColumn(resultColumnName,probabilityColumn*winningCorrectionFactorColumn*losingCorrectionFactorColumn)
  }

  /**
    * Generate date level records using the Week Level Data. The data will stay same across the whole week starting from Sunday
    * @param dataFrame : Weekly Data Dataframe
    * @param dateRange : Lookup Generation Date Range
    * @return
    */
  def createDailyRecords(dataFrame:DataFrame, dateRange: LocalDateRange): DataFrame = {

    import spark.implicits._

    val dateRangeAsDF = dateRange
      .map(Date.valueOf(_))
      .toDF
      .withColumnRenamed("value", "output_date")

    //dateRangeAsDF.show()

    dataFrame.createOrReplaceTempView("dataFrameTable")

    val dataFrameWithTimeFrame = spark
      .sql("select *, " +
        "explode(sequence(to_date(date), date_add(to_date(date),6), interval 1 day)) output_date " +
        "from dataFrameTable")

    //dataFrameWithTimeFrame.show()

    dateRangeAsDF
      .join(dataFrameWithTimeFrame, Seq("output_date"), "inner")
  }

  /**
    * This function generates the lookup data
    * @param mvnoIndustryModel : Industry Model
    * @param mvnoIndustryModelConfigurations : Lookup Configuration
    * @param tutelaData : Tutela Aggregate Data
    * @param blacklistData : Blacklist Data
    * @param dateRange : Lookup Date Range
    * @param observedRecordsLookupPath : Observed SPs Lookup Generation Path
    * @param modelledRecordsLookupPath : Modelled flows Lookup Generation Path
    * @param logger : Logger
    * @param debugFlag : Debug flag
    * @param debugYear: Debug Year
    * @param debugMonth: Debug Month
    * @param debugNpa : NPA to be checked
    * @param debugChurnLosingSp : Churn Losing SP for debugging
    * @param debugChurnWinningSp: Churn Winning SP for debugging
    */
  def generateLookup (mvnoIndustryModel:Dataset[IndustryModel],
    mvnoIndustryModelConfigurations:Dataset[LookupConfiguration],
    tutelaData:Dataset[TutelaAggData],
    blacklistData:Dataset[Blacklist],
    intraFlowAdjustmentData: Dataset[IntraFlowAdjustmentData],
    subMonthlyAdjustmentData: Dataset[SubMonthlyAdjustmentData],
    dateRange:LocalDateRange,
    observedRecordsLookupPath:URI,
    modelledRecordsLookupPath:URI,
    logger:Logger): Array[Long] ={
    /**
      * Following is the logic of the Lookup Generation
      * 1) Tutela Data provides the details regarding the NPAs which are in purview (Dataset: npas)
      * 2) Join the Industry Model with the NPA data on the basis of the time period (Dataset: joinIndustryModelWithDistinctNPAs)
      * 3) Join the Tutela Data with the Lookup Configuration data to be able to retrieve both Winning SP correction factor and Losing SP correction factor (Dataset: tutelaCorrectionFactorsForConfig)
      * 4) Join Blacklist to the resultant configuration data on the basis of NPA to be able to perform Geo Correction (joinTutelaCorrectionFactorsForConfigWithBlacklist)
      * 5) Combine Industry Model with the configuration data. In ideal scenarios, both should contain identical number and combination of records
      *    thus an inner join is performed (Dataset: combineIndustryModelWithAlgorithmConfig)
      * 6) Filter Observed Records using "is_observed"=true and apply geo correction as well as NPA level Tutela Correction Factor(observedDataRecords)
      * 7) Normalize the observed records (normalizedObservedDataRecords)
      * 8) Filter Modelled Flow using "is_observed"=false and apply geo correction as well as NPA level Tutela Correction Factor(modelledDataRecords),
      * no normalization done
      * 9) Smooth the data for both observed and modelled flows across a time period of 3 weeks to avoid any abrupt changes in the
      * numbers (smoothedNormalizedObservedDataRecords, smoothedModelledDataRecords)
      * 10) Generate Random Number Range Data for Daily and Weekly Lookups for Observed Records grouping the data on
      * NPA, churn_winning_sp, churn_losing_sp and date. (dailyObservedRecs)
      * 11) Generate Modelled Data lookup (dailyModelledRecs)
      */
    import spark.implicits._

    val npas = tutelaData
      .select(
        $"year",
        $"month",
        $"npa",
        $"date")
      .distinct()
      .as[NpasList]

    val joinIndustryModelToConfig=mvnoIndustryModel
      .select(
        $"churn_year",
        $"churn_month",
        $"begin_mno_sp",
        $"est_begin_brand_sp".as("begin_brand_sp"),
        coalesce($"begin_observed_sp",lit(0)).as("begin_observed_sp"),
        $"begin_plan_type_id",
        $"begin_reference_sp",
        $"end_mno_sp",
        $"est_end_brand_sp".as("end_brand_sp"),
        coalesce($"end_observed_sp",lit(0)).as("end_observed_sp"),
        $"end_plan_type_id",
        $"end_reference_sp",
        $"port_est",
        $"losing_mno_winning_mno_prop",
        $"losing_mno_prop")
      .join(mvnoIndustryModelConfigurations.select(
        $"churn_type",
        $"begin_mno",
        $"begin_mno_sp",
        $"begin_brand",
        $"begin_brand_sp",
        $"begin_reference_sp",
        coalesce($"begin_observed_sp",lit(0)).as("begin_observed_sp"),
        $"begin_plan_type_id",
        $"begin_observation_type_id",
        $"begin_allocation_order",
        $"end_mno",
        $"end_mno_sp",
        $"end_brand",
        $"end_brand_sp",
        $"end_reference_sp",
        coalesce($"end_observed_sp",lit(0)).as("end_observed_sp"),
        $"end_plan_type_id",
        $"end_observation_type_id",
        $"end_allocation_order",
        $"combined_observation_type_id",
        $"theoretical_is_observed",
        $"is_observed",
        $"is_activation",
        $"is_disconnect",
        $"is_self_flow",
        $"is_migration",
        $"launch_date",
        $"end_date"),
        Seq("begin_mno_sp",
          "begin_brand_sp",
          "begin_observed_sp",
          "begin_plan_type_id",
          "begin_reference_sp",
          "end_mno_sp",
          "end_brand_sp",
          "end_observed_sp",
          "end_plan_type_id",
          "end_reference_sp"),
        "inner")

    val joinIndustryModelToConfigWithDistinctNPAs = joinIndustryModelToConfig
      .withColumnRenamed("churn_year", "year")
      .withColumnRenamed("churn_month", "month")
      .join(npas, Seq("year", "month"), "left_outer")
      .filter(
        coalesce($"date", current_date()) between (joinIndustryModelToConfig("launch_date"), joinIndustryModelToConfig("end_date")))

    val tutelaCorrectionFactorsForIMWithConfig=joinIndustryModelToConfigWithDistinctNPAs
      .select(col("*"),
        expr("case when begin_brand = 'Other MVNO on TMO' then 'Other' " +
          "when begin_brand = 'Other MVNO on ATT' then 'Other' " +
          "when begin_brand = 'Other MVNO on SPR' then 'Other' " +
          "when begin_brand = 'Other MNVO on USC' then 'Other' " +
          "when begin_brand = 'Other MVNO on VZW' then 'Other' " +
          "else begin_brand end").alias("tutela_begin_brand"),
        expr("case when end_brand = 'Other MVNO on TMO' then 'Other' " +
          "when end_brand = 'Other MVNO on ATT' then 'Other' " +
          "when end_brand = 'Other MVNO on SPR' then 'Other' " +
          "when end_brand = 'Other MNVO on USC' then 'Other' " +
          "when end_brand = 'Other MVNO on VZW' then 'Other' " +
          "else end_brand end").alias("tutela_end_brand"))
      .join(tutelaData
      .select($"cld_network_operator".as("begin_mno"),
        $"cld_brand_name".as("tutela_begin_brand"),
        $"npa",
        $"correction_factor" as "losing_correction_factor",
        $"date"),
      Seq(
        "begin_mno",
        "tutela_begin_brand",
        "npa",
        "date"),
      "left_outer")
      .join(tutelaData
        .select(
          $"cld_network_operator".as("end_mno"),
          $"cld_brand_name".as("tutela_end_brand"),
          $"npa",
          $"correction_factor" as "winning_correction_factor",
          $"date"),
        Seq(
          "end_mno",
          "tutela_end_brand",
          "npa",
          "date"),
        "left_outer")

    val joinTutelaCorrectionFactorsForIMConfigWithBlacklist = tutelaCorrectionFactorsForIMWithConfig
      .join(blacklistData,
        Seq("npa"),
        "left_outer")
      .select(
        $"npa",
        $"date",
        $"begin_mno_sp",
        $"begin_brand_sp",
        $"begin_observed_sp",
        $"begin_plan_type_id",
        $"begin_reference_sp",
        $"end_mno_sp",
        $"end_brand_sp",
        $"end_observed_sp",
        $"end_plan_type_id",
        $"end_reference_sp",
        $"port_est",
        $"losing_mno_winning_mno_prop",
        $"losing_mno_prop",
        when($"end_brand_sp" === 66666, lit(1))
          .otherwise(coalesce($"winning_correction_factor", lit(1)))
          .as("winning_correction_factor"),
        when($"begin_brand_sp" === 66666, lit(1))
          .otherwise(coalesce($"losing_correction_factor", lit(1)))
          .as("losing_correction_factor"),
        $"begin_allocation_order",
        $"end_allocation_order",
        $"comcast",
        $"spectrum",
        $"is_observed")

    val observedDataRecords=joinTutelaCorrectionFactorsForIMConfigWithBlacklist
      .filter(
        $"theoretical_is_observed"
          && $"date".isNotNull
          && $"port_est".isNotNull
      )
      .drop(
        "year",
        "month")
      .transform(applyGeoCorrection(SP_LIST,$"losing_mno_winning_mno_prop","probablilityWithGeoCorrectionForNPAswithNoCoverage"))
      .transform(
        applyTutelaCorrectionFactors(
          $"probablilityWithGeoCorrectionForNPAswithNoCoverage",
          $"winning_correction_factor",
          $"losing_correction_factor",
          "adjustedProbablilityWithGeoWithNPALevelCorrectionFactor"))

    val normalizationWindow=Window
      .partitionBy(
        $"date",
        $"npa",
        $"end_observed_sp",
        $"begin_observed_sp")

    val normalizedObservedDataRecords = observedDataRecords
      .transform(
        generateNormalizedValues(
          "adjustedProbablilityWithGeoWithNPALevelCorrectionFactor",
          normalizationWindow,
          "percent_allocation"))
      .transform(
        generateNormalizedValues(
          "probablilityWithGeoCorrectionForNPAswithNoCoverage",
          normalizationWindow,
          "percent_allocation_geofix"))
      .drop("is_observed")

    val modelledDataRecords=joinTutelaCorrectionFactorsForIMConfigWithBlacklist
      .filter(
        !$"theoretical_is_observed"
          && $"date".isNotNull
          && $"port_est".isNotNull)
      .transform(
        applyGeoCorrection(
          SP_LIST,
          $"losing_mno_prop",
          "percent_allocation_geofix"))
      .transform(
        applyTutelaCorrectionFactors(
          $"percent_allocation_geofix",
          $"winning_correction_factor",
          $"losing_correction_factor",
          "percent_allocation"))
      .drop("is_observed")

    // Generate a window if 3 weeks for smoothening  the results

    val threeWeeksWindow = Window
      .partitionBy(
        $"npa",
        $"begin_observed_sp",
        $"begin_brand_sp",
        $"begin_plan_type_id",
        $"begin_reference_sp",
        $"end_observed_sp",
        $"end_brand_sp",
        $"end_plan_type_id",
        $"end_reference_sp")
      .orderBy(
        to_date(col("date"))
          .cast("timestamp")
          .cast("long"))
      .rangeBetween(THREE_PREV_WEEKS_IN_SEC, 0)

    val smoothedNormalizedObservedDataRecords = normalizedObservedDataRecords
      .withColumn("percent_allocation_avg", avg(col("percent_allocation")).over(threeWeeksWindow))
      .withColumn("percent_allocation_avg_geofix_smoothed", avg(col("percent_allocation_geofix")).over(threeWeeksWindow))
      .filter($"percent_allocation_avg">lit(0.00000))

    val smoothedModelledDataRecords = modelledDataRecords
      .withColumn("percent_allocation_avg", avg(col("percent_allocation")).over(threeWeeksWindow))
      .withColumn("percent_allocation_geofix_smoothed", avg(col("percent_allocation_geofix")).over(threeWeeksWindow))
      .withColumn("percent_allocation_geofix_unsmoothed", $"percent_allocation_geofix")
      .filter($"percent_allocation_avg">lit(0.00000))

    // Create a Spark SQL table on the sub-monthly probably Adjustments
    subMonthlyAdjustmentData
      .createOrReplaceTempView("subMonthlyFlowAdjustmentDataTable")

    val expandedSubMonthlyFlowAdjustmentData =spark.sql(
      "select explode(sequence(to_date(start_date), to_date(end_date), interval 1 day)) adjusted_the_date, " +
        "begin_plan_type_id as adjusted_begin_plan_type_id, " +
        "begin_mno_sp as adjusted_begin_mno_sp, " +
        "begin_reference_sp as adjusted_begin_reference_sp, " +
        "begin_observed_sp as adjusted_begin_observed_sp, " +
        "end_plan_type_id as adjusted_end_plan_type_id, " +
        "end_mno_sp as adjusted_end_mno_sp, " +
        "end_reference_sp as adjusted_end_reference_sp, " +
        "end_observed_sp as adjusted_end_observed_sp, " +
        "est_begin_brand_sp as adjusted_est_begin_brand_sp,  " +
        "est_end_brand_sp as adjusted_est_end_brand_sp, " +
        "est as adjusted_est, " +
        "port_est as adjusted_port_est,  " +
        "losing_mno_winning_mno_prop as adjusted_losing_mno_winning_mno_prop, " +
        "losing_mno_prop as adjusted_losing_mno_prop  " +
        "from subMonthlyFlowAdjustmentDataTable")
      .withColumn("week_end",next_day($"adjusted_the_date", "Sat"))
      .withColumn("week_start", date_sub($"week_end", 6))
      .drop("adjusted_the_date")
      .distinct()

    expandedSubMonthlyFlowAdjustmentData
      .filter($"adjusted_losing_mno_winning_mno_prop".isNotNull)
      .createOrReplaceTempView("expandedInterFlowAdjustmentDataTable")

    smoothedNormalizedObservedDataRecords
      .createOrReplaceTempView("normalizedObservedDataRecordsTable")

    //Adjustment data joins with the beginning saturday
    val interNetworkLookupAdjustments = spark
      .sql("select npa," +
      "date," +
      "begin_mno_sp," +
      "begin_brand_sp," +
      "begin_observed_sp," +
      "begin_plan_type_id," +
      "begin_reference_sp," +
      "end_mno_sp," +
      "end_brand_sp," +
      "end_observed_sp," +
      "end_plan_type_id," +
      "end_reference_sp ," +
      "port_est as port_est," +
      "losing_mno_winning_mno_prop as losing_mno_winning_mno_prop," +
      "losing_mno_prop as losing_mno_prop," +
      "winning_correction_factor," +
      "losing_correction_factor," +
      "begin_allocation_order," +
      "end_allocation_order," +
      "percent_allocation_avg_geofix_smoothed," +
      "percent_allocation," +
      "comcast," +
      "spectrum," +
      "geoCorrectionForNPAswithNoCoverage," +
      "probablilityWithGeoCorrectionForNPAswithNoCoverage," +
      "adjustedProbablilityWithGeoWithNPALevelCorrectionFactor," +
      "total_sum," +
      "coalesce(adjusted_losing_mno_winning_mno_prop,percent_allocation_avg) as percent_allocation_avg," +
      "percent_allocation_geofix, " +
      "percent_allocation_geofix, " +
      "adjusted_losing_mno_winning_mno_prop as sub_monthly_adjustment " +
      "from normalizedObservedDataRecordsTable normalizedObservedDataRecordsTable  " +
      "LEFT OUTER JOIN  " +
      "expandedInterFlowAdjustmentDataTable expandedInterFlowAdjustmentDataTable " +
      "ON (normalizedObservedDataRecordsTable.date=expandedInterFlowAdjustmentDataTable.week_start " +
      "and normalizedObservedDataRecordsTable.begin_plan_type_id=expandedInterFlowAdjustmentDataTable.adjusted_begin_plan_type_id " +
      "and normalizedObservedDataRecordsTable.begin_mno_sp=expandedInterFlowAdjustmentDataTable.adjusted_begin_mno_sp " +
      "and normalizedObservedDataRecordsTable.begin_reference_sp=expandedInterFlowAdjustmentDataTable.adjusted_begin_reference_sp " +
      "and coalesce(normalizedObservedDataRecordsTable.begin_observed_sp,0)=coalesce(expandedInterFlowAdjustmentDataTable.adjusted_begin_observed_sp,0) " +
      "and normalizedObservedDataRecordsTable.end_plan_type_id=expandedInterFlowAdjustmentDataTable.adjusted_end_plan_type_id " +
      "and normalizedObservedDataRecordsTable.end_mno_sp=expandedInterFlowAdjustmentDataTable.adjusted_end_mno_sp " +
      "and normalizedObservedDataRecordsTable.end_reference_sp=expandedInterFlowAdjustmentDataTable.adjusted_end_reference_sp " +
      "and coalesce(normalizedObservedDataRecordsTable.end_observed_sp,0)=coalesce(expandedInterFlowAdjustmentDataTable.adjusted_end_observed_sp,0) " +
      "and normalizedObservedDataRecordsTable.begin_brand_sp=expandedInterFlowAdjustmentDataTable.adjusted_est_begin_brand_sp " +
      "and normalizedObservedDataRecordsTable.end_brand_sp=expandedInterFlowAdjustmentDataTable.adjusted_est_end_brand_sp)")

    val interNetworkLookupAdjustmentsAfterSubMonthlyAdjustments = interNetworkLookupAdjustments.transform(
      generateNormalizedValues(
        "percent_allocation_avg",
        normalizationWindow,
        "percent_allocation_avg_after_sub_month_adjustments"))

    val windowPerDateNPAChurnWinningLosing = Window
      .partitionBy(
        $"date",
        $"npa",
        $"end_observed_sp",
        $"begin_observed_sp")
      .orderBy(
        $"end_observed_sp".desc,
        $"begin_observed_sp".desc,
        $"end_allocation_order".asc,
        $"end_plan_type_id".desc,
        $"begin_allocation_order".asc,
        $"begin_plan_type_id".desc)

    val computedPercSmoothedNormalizedObservedDataRecords = interNetworkLookupAdjustmentsAfterSubMonthlyAdjustments
      .withColumn("max", sum($"percent_allocation_avg_after_sub_month_adjustments").over(windowPerDateNPAChurnWinningLosing))
      .withColumn("min", $"max" - $"percent_allocation_avg_after_sub_month_adjustments")
      .withColumn("max_geofix", sum("percent_allocation_geofix").over(windowPerDateNPAChurnWinningLosing))
      .withColumn("min_geofix", $"max_geofix" - $"percent_allocation_geofix")
      .withColumn("max_geofix_smoothed", sum("percent_allocation_avg_geofix_smoothed").over(windowPerDateNPAChurnWinningLosing))
      .withColumn("min_geofix_smoothed", $"max_geofix_smoothed" - $"percent_allocation_avg_geofix_smoothed")
      .withColumn("max_geofix_unsmoothed", sum($"percent_allocation").over(windowPerDateNPAChurnWinningLosing))
      .withColumn("min_geofix_unsmoothed", $"max_geofix_unsmoothed" - $"percent_allocation")

    val dailyObservedRecs=createDailyRecords(
      computedPercSmoothedNormalizedObservedDataRecords,
      dateRange)
      .select(
        $"output_date".as("date"),
        $"npa".cast("String").as("npa"),
        $"begin_observed_sp".as("churn_losing_sp"),
        $"begin_brand_sp".as("losing_brand"),
        $"begin_plan_type_id".as("losing_plan_type"),
        $"end_observed_sp".as("churn_winning_sp"),
        $"end_brand_sp".as("winning_brand"),
        $"end_plan_type_id".as("winning_plan_type"),
        ($"min" * 100).as("min_random_number_value"),
        ($"max" * 100).as("max_random_number_value"),
        ($"min_geofix" * 100).as("min_random_number_value_geofix"),
        ($"max_geofix" * 100).as("max_random_number_value_geofix"),
        ($"min_geofix_smoothed" * 100).as("min_random_number_value_geofix_smoothed"),
        ($"max_geofix_smoothed" * 100).as("max_random_number_value_geofix_smoothed"),
        ($"min_geofix_unsmoothed" * 100).as("min_random_number_value_geofix_unsmoothed"),
        ($"max_geofix_unsmoothed" * 100).as("max_random_number_value_geofix_unsmoothed"),
         $"sub_monthly_adjustment")
      .as[ObservedMvnoLookupData]
      .filter(
        $"max".isNotNull
          && $"max">0.0
          && ($"min" != $"max"))

    // Apply Sub Month Adjustments to the modelled flows
    expandedSubMonthlyFlowAdjustmentData
      .filter($"adjusted_losing_mno_prop".isNotNull)
      .createOrReplaceTempView("expandedIntraFlowAdjustmentDataTable")

    smoothedModelledDataRecords
      .createOrReplaceTempView("smoothedModelledDataRecordsTable")

    val intraNetworkLookupAdjustmentsAfterSubMonthlyAdjustments = spark
      .sql("select npa," +
        "date," +
        "begin_mno_sp," +
        "begin_brand_sp," +
        "begin_observed_sp," +
        "begin_plan_type_id," +
        "begin_reference_sp," +
        "end_mno_sp," +
        "end_brand_sp," +
        "end_observed_sp," +
        "end_plan_type_id," +
        "end_reference_sp ," +
        "port_est as port_est," +
        "losing_mno_winning_mno_prop as losing_mno_winning_mno_prop," +
        "losing_mno_prop as losing_mno_prop," +
        "winning_correction_factor," +
        "losing_correction_factor," +
        "begin_allocation_order," +
        "end_allocation_order," +
        "comcast," +
        "spectrum," +
        "geoCorrectionForNPAswithNoCoverage," +
        "percent_allocation_geofix, " +
        "percent_allocation, " +
        "coalesce(adjusted_losing_mno_prop,percent_allocation_avg) as percent_allocation_avg," +
        "percent_allocation_geofix_smoothed, " +
        "percent_allocation_geofix_unsmoothed, " +
        "adjusted_losing_mno_prop as sub_monthly_adjustment " +
        "from smoothedModelledDataRecordsTable smoothedModelledDataRecordsTable  " +
        "LEFT OUTER JOIN  " +
        "expandedIntraFlowAdjustmentDataTable expandedIntraFlowAdjustmentDataTable " +
        "ON (smoothedModelledDataRecordsTable.date=expandedIntraFlowAdjustmentDataTable.week_start " +
        "and smoothedModelledDataRecordsTable.begin_plan_type_id=expandedIntraFlowAdjustmentDataTable.adjusted_begin_plan_type_id " +
        "and smoothedModelledDataRecordsTable.begin_mno_sp=expandedIntraFlowAdjustmentDataTable.adjusted_begin_mno_sp " +
        "and smoothedModelledDataRecordsTable.begin_reference_sp=expandedIntraFlowAdjustmentDataTable.adjusted_begin_reference_sp " +
        "and coalesce(smoothedModelledDataRecordsTable.begin_observed_sp,0)=coalesce(expandedIntraFlowAdjustmentDataTable.adjusted_begin_observed_sp,0) " +
        "and smoothedModelledDataRecordsTable.end_plan_type_id=expandedIntraFlowAdjustmentDataTable.adjusted_end_plan_type_id " +
        "and smoothedModelledDataRecordsTable.end_mno_sp=expandedIntraFlowAdjustmentDataTable.adjusted_end_mno_sp " +
        "and smoothedModelledDataRecordsTable.end_reference_sp=expandedIntraFlowAdjustmentDataTable.adjusted_end_reference_sp " +
        "and coalesce(smoothedModelledDataRecordsTable.end_observed_sp,0)=coalesce(expandedIntraFlowAdjustmentDataTable.adjusted_end_observed_sp,0) " +
        "and smoothedModelledDataRecordsTable.begin_brand_sp=expandedIntraFlowAdjustmentDataTable.adjusted_est_begin_brand_sp " +
        "and smoothedModelledDataRecordsTable.end_brand_sp=expandedIntraFlowAdjustmentDataTable.adjusted_est_end_brand_sp)")

    val dailyModelledRecs=createDailyRecords(
      intraNetworkLookupAdjustmentsAfterSubMonthlyAdjustments,
      dateRange)

    // Apply the Intra level adjustments
    val dailyModelledRecsAdjusted = dailyModelledRecs.join(intraFlowAdjustmentData,Seq("date","begin_reference_sp","begin_brand_sp","begin_plan_type_id","end_reference_sp","end_brand_sp","end_plan_type_id"), "left_outer")

    val  dailyModelledRecsAdjustedFinal = dailyModelledRecsAdjusted
      .select(
        $"output_date".as("date"),
        $"npa".cast("String").as("npa"),
        $"begin_reference_sp".as("churn_losing_sp"),
        $"begin_brand_sp".as("losing_brand"),
        $"begin_plan_type_id".as("losing_plan_type"),
        $"end_reference_sp".as("churn_winning_sp"),
        $"end_brand_sp".as("winning_brand"),
        $"end_plan_type_id".as("winning_plan_type"),
        ($"percent_allocation_avg"* coalesce($"multiplier",lit(1))).as("percent_allocation"),
        ($"percent_allocation_geofix"* coalesce($"multiplier",lit(1))).as("percent_allocation_geofix"),
        ($"percent_allocation_geofix_smoothed"* coalesce($"multiplier",lit(1))).as("percent_allocation_geofix_smoothed"),
        ($"percent_allocation_geofix_unsmoothed"* coalesce($"multiplier",lit(1))).as("percent_allocation_geofix_unsmoothed"),
        $"percent_allocation_avg".as("act_adjusted_percent_allocation"),
        $"sub_monthly_adjustment",
        coalesce($"multiplier",lit(1)).as("multiplier"))
      .as[ModelledMvnoLookupData]
      .filter($"percent_allocation_avg".isNotNull)
      .distinct()



    logger.info(s"Writing inter mno output to  {observedRecordsLookupPath}")

    dailyObservedRecs
      .repartition($"date")
      .write
      .mode(SaveMode.Append)
      .partitionBy("date")
      .parquet(observedRecordsLookupPath.toString)

    logger.info(s"Writing intra mno output to {modelledRecordsLookupPath}")

    dailyModelledRecsAdjustedFinal
      .repartition($"date")
      .write
      .mode(SaveMode.Append)
      .partitionBy("date")
      .parquet(modelledRecordsLookupPath.toString)

    Array(dailyObservedRecs.count(),dailyModelledRecsAdjustedFinal.count())
  }

}

object WcvLookupUtils{
  def apply()(implicit spark: SparkSession) = new WcvLookupUtils()
}
