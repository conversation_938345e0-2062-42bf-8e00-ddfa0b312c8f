package com.comlinkdata.mvno.wcv.lookup

import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner, SqlServerUtils, TimeSeriesLocation, Utils}
import com.comlinkdata.largescale.ops.tracker.{PostgresProps, Tracker}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.SparkSession

import java.net.URI
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.util.Date
import java.time.format.DateTimeFormatter

object WcvLookupJob extends SparkJob[WcvLookupSchema.WcvLookupConfig](WcvLookupJobRunner) with LazyLogging

object WcvLookupJobRunner extends SparkJobRunner[WcvLookupSchema.WcvLookupConfig] with LazyLogging {

  def runJob(config: WcvLookupSchema.WcvLookupConfig)(implicit spark: SparkSession): Unit = {

    import config._

    val STAGE="c200"
    val REGION="us"

    val wcvLookupUtils=WcvLookupUtils()
    val tracker: Tracker = Tracker.postgres(PostgresProps.default(config.env))

    val today=LocalDate.now
    val start_datetime = Utils.getDateTimeInEST()
    var end_datetime = Utils.getDateTimeInEST()

    tracker.publish("%s-%s-%s".format(config.jobName, config.dataVersion,config.jobUniqueId), REGION, STAGE, "start_tracker",null,start_datetime, end_datetime, 0 , "")

    try
      {
        //Folder where the lookup gets generated with folder names as the dates, format date="XXXX-XX-XX"
        val timeSeriesDatePartitionLocation=TimeSeriesLocation
          .ofDatePartitions(config.observedRecordsLookupPath)
          .build

        val startDate = wcvLookupUtils
          .getLookupGenStartDate(
            timeSeriesDatePartitionLocation,
            config.minDate)

        //Generate the date range between the start date and present date
        val configMaxDate= config.maxDate

        val dateRange = LocalDateRange
          .of(
            startDate,
            if(configMaxDate.getOrElse(today).isAfter(today)) today else configMaxDate.getOrElse(today)
          )

        // a) Error out if min date is provided and there is data
        // b) Error out if there is no data and min date is not provided
        timeSeriesDatePartitionLocation.validOutputPartitionsOrThrow(dateRange)

        logger.info(s"Processing dateRange $dateRange.")

        val mvnoIndustryModel = wcvLookupUtils
          .loadIndustryModel(
            config.industryModelTableName,
            startDate)

        val mvnoIndustryModelConfigurations=wcvLookupUtils
          .loadLookupConfig(
            config.configurationTableName)

        val tutelaData=wcvLookupUtils
          .loadTutelaAgg(
            config.clientName,
            config.npaLookupTableName,
            config.npaMnoMvnoCorrectionFactorTableName,
            startDate.minusMonths(2),
            if(configMaxDate.getOrElse(today).isAfter(today)) today else configMaxDate.getOrElse(today))

        val blacklistData=wcvLookupUtils
          .loadBlacklistData(
            config.blacklistDataTableName)

        val intraFlowAdjustmentData= wcvLookupUtils
          .loadIntraFlowAdjustementData( config.intraFlowAdjustmentTableName)

        // Adjustments now contain changes for both inter network and intra network adjustments which
        // override the monthly flow proportions and area level adjustments
        val subMonthlyAdjustmentData= wcvLookupUtils
          .loadSubMonthlyAdjustmentData(config.subMonthlyAdjustmentTableName)


        val timestamp = new SimpleDateFormat("yyyyMMddHHmm").format(new Date())
        val outputObservedRecordsLookupPath:URI=config.observedRecordsLookupPath

        val outputModelledRecordsLookupPath:URI= config.modelledRecordsLookupPath

        val result:Array[Long] = wcvLookupUtils
          .generateLookup(mvnoIndustryModel,
            mvnoIndustryModelConfigurations,
            tutelaData,
            blacklistData,
            intraFlowAdjustmentData,
            subMonthlyAdjustmentData,
            dateRange,
            outputObservedRecordsLookupPath,
            outputModelledRecordsLookupPath,
            logger)

        val new_job_name = "<span title = \"Start Date: %s&#13;End Date:%s&#13;Observed Records:%s&#13;Modelled Records:%s\" > %s %s </span>"
          .format(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
            (if(configMaxDate.getOrElse(today).isAfter(today)) today else configMaxDate.getOrElse(today)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
            result(0).toString,
            result(1).toString,
            config.jobName,
            config.dataVersion
            )
        end_datetime=Utils.getDateTimeInEST()
        tracker.publish("%s-%s-%s".format(config.jobName, config.dataVersion,config.jobUniqueId), REGION, STAGE,  "end_tracker",new_job_name,start_datetime, end_datetime, 0 , "")

      }
    catch {
      case ex: Exception => {
        logger.info(ex.toString)
        end_datetime=Utils.getDateTimeInEST()
        tracker.publish("%s-%s-%s".format(config.jobName, config.dataVersion,config.jobUniqueId), REGION, STAGE,  "error_tracker","%s-%s".format(config.jobName, config.dataVersion),start_datetime, end_datetime, 1, ex.toString)
        System.exit(1)
      }
    }


  }
}
