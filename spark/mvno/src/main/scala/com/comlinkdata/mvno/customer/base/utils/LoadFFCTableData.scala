package com.comlinkdata.mvno.customer.base.utils

import com.comlinkdata.largescale.commons.RedshiftUtils.RedshiftConfig
import com.comlinkdata.largescale.commons.SqlServerUtils
import com.comlinkdata.largescale.commons.SqlServerUtils.SqlServerConfig
import com.comlinkdata.mvno.customer.base.CustomerBaseSchema.{FFCRecord, FfcDataForCustomerBaseComputation}
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{DateType, DoubleType}
import org.apache.spark.sql.{Dataset, SparkSession}

import java.time.LocalDate
import scala.language.postfixOps

class LoadFFCTableData {

  /** *
    * This method is used to load FFC data to compute Customer base
    *
    * @param ffcTable                : FFC table
    * @param startDate               : Start Date
    * @param seqCombinedCountSpPairs : Number of subsequent records which migrated together
    */
  def loadFFCData(
    clientName: String,
    ffcTable: String,
    genericFfcTable: String,
    startDate: LocalDate,
    clientCutOffDate:LocalDate,
    seqCombinedCountSpPairs: Int
  )(implicit sc: SqlServerConfig, rc: RedshiftConfig, loggerData: Logger,
    spark: SparkSession): Dataset[FfcDataForCustomerBaseComputation] = {
    import spark.implicits._

    if (clientName == "generic" )
   {
      SqlServerUtils.loadDataFromSQLServer(
        ffcTable)
        .transform(filterFfcRecords(seqCombinedCountSpPairs, startDate, clientName))
    }
    else {
      val clientFFC = SqlServerUtils.loadDataFromSQLServer(
        ffcTable)
        .filter(trunc($"churn_timestamp"
          .cast(DateType),
          "month") >= clientCutOffDate)

      val genericFFC = SqlServerUtils.loadDataFromSQLServer(
        genericFfcTable)
        .filter(trunc($"churn_timestamp"
          .cast(DateType),
          "month") < clientCutOffDate)

      if (clientName == "xm") {
        clientFFC
          .select($"tn",
            $"churn_type",
            $"churn_timestamp",
            $"churn_winning_ocn",
            $"churn_losing_ocn",
            $"noncompetitive_ind",
            $"zip_rc_kblock",
            $"churn_winning_mode",
            $"churn_losing_mode",
            $"mvno_losing_plan_type_id",
            $"churn_losing_plan_type_id",
            $"mvno_winning_plan_type_id",
            $"churn_winning_plan_type_id",
            $"churn_losing_sp",
            $"mvno_losing_sp",
            $"churn_winning_sp",
            $"mvno_winning_sp",
            $"seqcombinedcount_sppairs",
            $"trans_lag",
            $"adjusted_wins",
            $"customer_type"
          )
          .unionAll(genericFFC.select($"tn",
            $"churn_type",
            $"churn_timestamp",
            $"churn_winning_ocn",
            $"churn_losing_ocn",
            $"noncompetitive_ind",
            $"zip_rc_kblock",
            $"churn_winning_mode",
            $"churn_losing_mode",
            $"mvno_losing_plan_type_id",
            $"churn_losing_plan_type_id",
            $"mvno_winning_plan_type_id",
            $"churn_winning_plan_type_id",
            $"churn_losing_sp",
            $"mvno_losing_sp",
            $"churn_winning_sp",
            $"mvno_winning_sp",
            $"seqcombinedcount_sppairs",
            $"trans_lag",
            $"adjusted_wins",
            lit(null).as("customer_type")
          ))
          .transform(filterFfcRecords(seqCombinedCountSpPairs, startDate, clientName))
      }
      else {
        clientFFC
          .select($"tn",
            $"churn_type",
            $"churn_timestamp",
            $"churn_winning_ocn",
            $"churn_losing_ocn",
            $"noncompetitive_ind",
            $"zip_rc_kblock",
            $"churn_winning_mode",
            $"churn_losing_mode",
            $"mvno_losing_plan_type_id",
            $"churn_losing_plan_type_id",
            $"mvno_winning_plan_type_id",
            $"churn_winning_plan_type_id",
            $"churn_losing_sp",
            $"mvno_losing_sp",
            $"churn_winning_sp",
            $"mvno_winning_sp",
            $"seqcombinedcount_sppairs",
            $"trans_lag",
            $"adjusted_wins"
          )
          .unionAll(genericFFC.select($"tn",
            $"churn_type",
            $"churn_timestamp",
            $"churn_winning_ocn",
            $"churn_losing_ocn",
            $"noncompetitive_ind",
            $"zip_rc_kblock",
            $"churn_winning_mode",
            $"churn_losing_mode",
            $"mvno_losing_plan_type_id",
            $"churn_losing_plan_type_id",
            $"mvno_winning_plan_type_id",
            $"churn_winning_plan_type_id",
            $"churn_losing_sp",
            $"mvno_losing_sp",
            $"churn_winning_sp",
            $"mvno_winning_sp",
            $"seqcombinedcount_sppairs",
            $"trans_lag",
            $"adjusted_wins"
          ))
          .transform(filterFfcRecords(seqCombinedCountSpPairs, startDate, clientName))

      }
    }

  }

  /** *
    * Filter eligible FFC Data
    *
    * @param seqCombinedCountSpPairs : Number of subsequent records which migrated together
    * @param customerBaseDate        : Custoner base Date
    * @param ffcData                 : FFC Data
    */
  def filterFfcRecords[T](seqCombinedCountSpPairs: Int, customerBaseDate: LocalDate, clientName: String)(ffcData: Dataset[T])(implicit spark: SparkSession): Dataset[FfcDataForCustomerBaseComputation] = {
    import spark.implicits._

    /*
    The TMO version ignores migration records (namely Boost -> TMO) unless they are Sprint -> TMO first time migrators. So the effect is
    Boost to TMO migrators stay as Boost instead of going to T-Mobile
    Sprint to TMO migrators go to TMO
    In either case, if they are a first time switcher (i.e. we used to not see them in the CB but the migration record adds them),
    the record gets ignored and they stay out of the CB. This was a special condition added to the Sprint -> TMO case so that
    we didn't see a wave of TMO customers just because they got added to the CB by the migration
    When you subset to noncomp=0, the only change is Boost is higher (0 -> 10% prepaid installed share) in the TMO version
    *. When you subset to noncomp in (0,1) which we now do, another change is TMO comes down (postpaid installed share down ~8%),
    due to removing those first time migrators.
     */
    ffcData
      .transform(getRecordsFromDatePeriod(customerBaseDate))
      .filter(coalesce($"noncompetitive_ind",lit(0)) =!= 1
        or
        ($"churn_losing_sp"=== 3
          and
          coalesce($"noncompetitive_ind",lit(0)) === 1
          and
          coalesce($"trans_lag",lit(0)) =!= 0)
      )
      .transform(getLatestRecordsPerTn(clientName))
      .select($"tn",
        $"churn_type",
        $"churn_timestamp",
        $"churn_winning_ocn",
        $"churn_losing_ocn",
        $"noncompetitive_ind",
        $"zip_rc_kblock",
        when(
          // This is to ensure mvno columns for pre mvno-stamping era gets populated only for wireless to wireless move
          (trunc($"churn_timestamp"
            .cast(DateType),
            "month") < LocalDate.of(2017, 4, 1))
            and
            $"churn_winning_mode" === "W"
            and
            $"churn_losing_mode" === "W",
          coalesce($"mvno_losing_plan_type_id", $"churn_losing_plan_type_id"))
          .otherwise($"mvno_losing_plan_type_id")
          .as("mvno_losing_plan_type_id"),
        when(
          trunc($"churn_timestamp"
            .cast(DateType),
            "month") < LocalDate.of(2017, 4, 1),
          coalesce($"mvno_winning_plan_type_id", $"churn_winning_plan_type_id"))
          .otherwise($"mvno_winning_plan_type_id")
          .as("mvno_winning_plan_type_id"),
        $"churn_losing_plan_type_id",
        $"churn_winning_plan_type_id",
        $"churn_losing_sp",
        when(
          (trunc($"churn_timestamp"
            .cast(DateType),
            "month") < LocalDate.of(2017, 4, 1))
            and
            $"churn_winning_mode" === "W"
            and
            $"churn_losing_mode" === "W",
          coalesce($"mvno_losing_sp", $"churn_losing_sp"))
          .otherwise($"mvno_losing_sp")
          .as("mvno_losing_sp"),
        $"churn_losing_mode",
        $"churn_winning_sp",
        when(
          (trunc($"churn_timestamp"
            .cast(DateType),
            "month") < LocalDate.of(2017, 4, 1))
            and
            $"churn_winning_mode" === "W"
            and
            $"churn_losing_mode" === "W",
          coalesce($"mvno_winning_sp", $"churn_winning_sp"))
          .otherwise($"mvno_winning_sp")
          .as("mvno_winning_sp"),
        $"churn_winning_mode",
        $"seqcombinedcount_sppairs",
        $"trans_lag",
        $"adjusted_wins".cast(DoubleType).as("adjusted_wins")
      )
      .as[FFCRecord]

      .transform(fetchWCVEligibleRecords(seqCombinedCountSpPairs))
      .select(
        $"churn_timestamp",
        $"churn_timestamp".cast(DateType).as("acquisition_date"),
        $"tn",
        trim($"churn_winning_ocn").as("churn_winning_ocn"),
        trim($"churn_losing_ocn").as("churn_losing_ocn"),
        $"noncompetitive_ind",
        $"zip_rc_kblock",
        substring($"tn", 1, 3).cast("Integer").as("npa"),
        coalesce($"mvno_losing_plan_type_id", $"churn_losing_plan_type_id").as("mvno_losing_plan_type_id"),
        coalesce($"mvno_winning_plan_type_id", $"churn_winning_plan_type_id").as("mvno_winning_plan_type_id"),
        $"churn_losing_plan_type_id",
        $"churn_winning_plan_type_id",
        $"churn_losing_sp",
        $"mvno_losing_sp",
        $"churn_losing_mode",
        $"churn_winning_sp",
        $"mvno_winning_sp",
        $"churn_winning_mode",
        $"trans_lag",
        $"adjusted_wins"
      )
      .as[FfcDataForCustomerBaseComputation]

  }


  /** *
    * Get ffc records between start date and end date
    *
    * @param customerBaseDate : Customer Base Date
    * @param ffcData          : FFC Data
    */
  def getRecordsFromDatePeriod[T](customerBaseDate: LocalDate)(ffcData: Dataset[T])(implicit spark: SparkSession): Dataset[T] = {
    import spark.implicits._

    ffcData
      .filter(trunc($"churn_timestamp".cast(DateType), "month") < customerBaseDate)

  }

  /** *
    * Get the latest records on TN level. The TN level latest records should be with the latest churn_timestamp
    *
    * @param ffcData : FFC Data
    */
  def getLatestRecordsPerTn[T](clientName: String)(ffcData: Dataset[T])(implicit spark: SparkSession) = {
    import org.apache.spark.sql.expressions.Window
    import spark.implicits._

    // Partition by tn order by churn_timestamp
    val tnLevelWindow = Window.partitionBy($"tn").orderBy($"churn_timestamp" desc)

    // Get the new DF applying window function
    val ffcDataRecord =  ffcData
      .withColumn("rank", rank().over(tnLevelWindow))
      .filter($"rank" === 1)
      .drop("rank")

    if(clientName == "xm")
    {
        ffcDataRecord
          .filter(!coalesce($"customer_type",lit("A")).like("B_%"))
    }
    else
    {
        ffcDataRecord
    }

  }

  /** *
    * This method filters out the eligible FFC Data which can be used for Customer base computation
    *
    * @param seqCombinedCountSpPairs : Number of subsequent records which migrated together
    * @param ffcData                 : FFC Data
    */
  def fetchWCVEligibleRecords(seqCombinedCountSpPairs: Int)(ffcData: Dataset[FFCRecord])(implicit spark: SparkSession): Dataset[FFCRecord] = {
    import spark.implicits._

    ffcData
      .filter(coalesce(substring($"churn_type", 1, 1), lit("B")) =!= lit("A"))
      .filter($"zip_rc_kblock" =!= "00000")
      .filter($"zip_rc_kblock".isNotNull)
      .filter($"churn_winning_mode" === "W")
      // Removing the filter on churn_losing_mode as the existing code allows non-wireless ports
      //.filter($"churn_losing_mode" === (lit("W")))
      .filter(coalesce($"seqcombinedcount_sppairs", lit(0)) <= seqCombinedCountSpPairs)
      .filter(
        ($"churn_losing_mode" === "W" and $"mvno_losing_sp".isNotNull and $"mvno_winning_sp".isNotNull)
          or
          $"churn_losing_mode" =!= "W"
      )
      .filter($"churn_winning_ocn".isNotNull)
      .filter($"churn_winning_ocn" =!= "000")
      .filter($"churn_winning_ocn" =!= "0")
      .filter($"churn_losing_ocn".isNotNull)
      .filter($"churn_losing_ocn" =!= "000")
      .filter($"churn_losing_ocn" =!= "0")
      .filter($"adjusted_wins" > 0)
  }


}

object LoadFFCTableData {
  def apply()(implicit spark: SparkSession) = new LoadFFCTableData()
}
