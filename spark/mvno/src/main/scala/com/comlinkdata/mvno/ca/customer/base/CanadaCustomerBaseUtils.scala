package com.comlinkdata.mvno.ca.customer.base


import com.comlinkdata.largescale.commons.RedshiftUtils.RedshiftConfig
import com.comlinkdata.largescale.commons.SqlServerUtils.SqlServerConfig
import com.comlinkdata.largescale.commons.{LocalDateRange, RedshiftUtils, SqlServerUtils}
import com.comlinkdata.mvno.ca.customer.base.CanadaCustomerBaseSchema._
import com.comlinkdata.mvno.ca.customer.base.utils.CanadaLoadFFCTableData
import com.comlinkdata.mvno.customer.base.CustomerBaseSchema.OcnData
import com.comlinkdata.mvno.customer.base.CustomerBaseUtils
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.DateType
import org.apache.spark.sql.{Dataset, Row, SparkSession}

import java.time.LocalDate
import scala.language.postfixOps
import com.comlinkdata.mvno.customer.base.utils.LoadFFCTableData

class CanadaCustomerBaseUtils(implicit spark: SparkSession) {

  val loadFFCTableData = LoadFFCTableData()
  val canadaLoadFFCTableData = CanadaLoadFFCTableData()
  val customerBaseUtils = CustomerBaseUtils()

  /**
    * Generate Customer Base Data
    * @param clientName : Client Code
    * @param customerBaseDefaultMinDate : Default Minimum Date for Customer base Generation
    * @param customerBaseMinDate : Customer Base generate Minimum Date
    * @param customerBaseMaxDate : Customer Base generate Maximum Date
    * @param ffcTable : Client FFC if Customer Base for Client FFC is being generated else Generic FFC Table Name
    * @param genericFfcTable: Generic FFC Table Name
    * @param seqCombinedCountSpPairs: Sequential Disconnect Count
    * @param ocnTable : OCN Table Name
    * @param redShiftCustomerbaseTable: Redshift Customer base Table Name
    */
  def computeCanadaCustomerBase(
    clientName:String,
    customerBaseDefaultMinDate: LocalDate,
    customerBaseMinDate: LocalDate,
    customerBaseMaxDate: LocalDate,
    ffcTable: String,
    rcIdTable: String,
    seqCombinedCountSpPairs: Int,
    ocnTable: String,
    redShiftCustomerbaseTable: String)(implicit sc: SqlServerConfig, rc: RedshiftConfig, loggerData: Logger): Unit = {

    import spark.implicits._

    // Generate Date Period
    val datePeriod = customerBaseUtils.getDatePeriodsBetweenStartAndEndDate(customerBaseDefaultMinDate, customerBaseMinDate, customerBaseMaxDate)
    println("Date Period:")
    print(datePeriod)

    var consolidatedCustomerBaseData = Seq.empty[CanadaConsolidatedFfcDataForCustomerBase].toDS()
    var consolidatedCustomerBaseDataForWinsLosses = Seq.empty[CanadaConsolidatedFfcDataForCustomerBase].toDS()
    var meanCustomerBaseData = Seq.empty[CanadaCustomerBaseData].toDS()
    var meanCustomerBaseDataForWinsLosses = Seq.empty[CanadaCustomerBaseData].toDS()

    for (dateRange <- datePeriod) {
      loggerData.info("Initiated Customer Base for the date: %s to %s".format(dateRange.prevCustomerBaseDate, dateRange.customerBaseDate))

      loggerData.info("Loading FFC Data")

      // Get the Eligible FFC Data
      val ffcData = canadaLoadFFCTableData.loadCanadaFFCData(
        clientName,
        ffcTable,
        rcIdTable,
        dateRange.customerBaseDate,
        seqCombinedCountSpPairs
      )

      loggerData.info("Completed Loading FFC Data")

      loggerData.info("Loading OCN Data")
      // Get the OCN Data
      val ocnMasterData = customerBaseUtils.loadOcnData(ocnTable, dateRange.customerBaseDate)

      loggerData.info("Completed Loading OCN Data")

      loggerData.info("Generate SPs and Tenure")
      // Get the OCN Data

      val fFCWithProperties = ffcData
        .transform(generateCanadaFFCProperties(ocnMasterData))

      fFCWithProperties.persist()

      loggerData.info("Completed Generating SPs and Tenure")

      loggerData.info("Generate Consolidated Customer Base Data")
      // Generate Customer base Data
      consolidatedCustomerBaseData = consolidatedCustomerBaseData
        .unionAll(
          fFCWithProperties
            .transform(customerBaseUtils.computeTenancy(lit(dateRange.customerBaseDate).cast(DateType)))
            .select(
              $"acquisition_date",
              lit(dateRange.customerBaseDate).cast(DateType).as("customer_base_date"),
              $"current_holder_sp",
              $"previous_holder_sp",
              $"churn_winning_mode",
              $"churn_losing_mode",
              $"noncompetitive_ind",
              $"rc_id",
              $"govt_contract_ind",
              $"prev_churn_losing_sp",
              $"source_ind",
              $"merger_id",
              $"tn_intra_flows_only_flag",
              $"current_tenure_months",
              $"tenancy".as("tenancy_months"),
              $"adjusted_wins")
            .repartition($"customer_base_date", $"current_holder_sp")
            .as[CanadaConsolidatedFfcDataForCustomerBase]
        )

      loggerData.info("Completed Consolidated Customer Base Data")

      loggerData.info("Generate Consolidated Customer Base Data for Next Month for Loss Computation")
      // Generate Customer base Data
      consolidatedCustomerBaseDataForWinsLosses = consolidatedCustomerBaseDataForWinsLosses
        .unionAll(
          fFCWithProperties
            .transform(customerBaseUtils.computeTenancy(lit(dateRange.customerBaseDate.plusMonths(-1)).cast(DateType)))
            .select(
              $"acquisition_date",
              lit(dateRange.customerBaseDate.plusMonths(-1)).cast(DateType).as("customer_base_date"),
              $"current_holder_sp",
              $"previous_holder_sp",
              $"churn_winning_mode",
              $"churn_losing_mode",
              $"noncompetitive_ind",
              $"rc_id",
              $"govt_contract_ind",
              $"prev_churn_losing_sp",
              $"source_ind",
              $"merger_id",
              $"tn_intra_flows_only_flag",
              $"current_tenure_months",
              $"tenancy".as("tenancy_months"),
              $"adjusted_wins")
            .repartition($"customer_base_date", $"current_holder_sp")
            .as[CanadaConsolidatedFfcDataForCustomerBase]
        )

      loggerData.info("Completed Consolidated Customer Base Data for Next Month for Loss Computation")


      loggerData.info("Completed Customer Base Data for the date period: %s to %s".format(dateRange.prevCustomerBaseDate, dateRange.customerBaseDate))
    }

    loggerData.info("Generate Total Customers")
    meanCustomerBaseData = computeCanadaTotalCustomers(consolidatedCustomerBaseData)

    loggerData.info("Generate Total Customers for Losses")
    meanCustomerBaseDataForWinsLosses = computeCanadaTotalCustomers(consolidatedCustomerBaseDataForWinsLosses)

    loggerData.info("Losses Applied")

    val meanCustomerBaseDataWithLosses = meanCustomerBaseData
      .transform(updateCanadaWinsAndLosses(meanCustomerBaseDataForWinsLosses))
      .filter($"customer_base_date" >= customerBaseMinDate)
      .filter($"customer_base_date" <= customerBaseMaxDate)
      .select($"customer_base_date",
        $"current_holder_sp",
        $"previous_holder_sp",
        when($"prev_churn_losing_sp" =!= -99999999, $"prev_churn_losing_sp").as("previous_previous_holder_sp"),
        $"noncompetitive_ind",
        $"rc_id",
        $"govt_contract_ind",
        $"source_ind",
        $"merger_id",
        $"tn_intra_flows_only_flag",
        $"current_tenure_months",
        $"tenancy_months",
        $"total_customers",
        $"total_wins",
        $"total_losses")
      .repartition($"customer_base_date",
        $"current_holder_sp")

    meanCustomerBaseDataWithLosses.persist()

    loggerData.info("Load Data into Redshift Table")

    RedshiftUtils.redshiftWriteDateRange(
      data = meanCustomerBaseDataWithLosses,
      tableName = redShiftCustomerbaseTable,
      Some(LocalDateRange(
        startDate = customerBaseMinDate,
        endDate = customerBaseMaxDate)),
      dateColumnName = "customer_base_date")


    loggerData.info("Load Data Completed")

  }

  def generateCanadaFFCProperties(
    ocnMasterData: Dataset[OcnData])
    (
      ffcDataForCustomerBaseComputation: Dataset[CanadaFfcDataForCustomerBaseComputation]
    ): Dataset[CanadaConsolidatedFfcDataForCustomerBase] = {

    import spark.implicits._

    //Generate Customer base data
    // Join the OCN table by ocn.
    // If mvno_sp is different from the churn_sp  then take the mvno_sp, if the latest record does not match then take the lookup value
    ffcDataForCustomerBaseComputation
      .transform(customerBaseUtils.computeTenure)
      .transform(customerBaseUtils.getCurrentOcnVsSPDataForFfcRecord("churn", ocnMasterData))
      .select(
        $"acquisition_date",
        lit(null).cast(DateType).as("customer_base_date"),
        $"ocn_churn_winning_sp".as("current_holder_sp"),
        $"ocn_churn_losing_sp".as("previous_holder_sp"),
        $"churn_winning_mode",
        $"churn_losing_mode",
        $"noncompetitive_ind",
        $"rc_id",
        $"govt_contract_ind",
        $"prev_churn_losing_sp",
        $"source_ind",
        $"merger_id",
        $"tn_intra_flows_only_flag",
        $"tenure".as("current_tenure_months"),
        lit(0).as("tenancy_months"),
        $"adjusted_wins"
      )
      .as[CanadaConsolidatedFfcDataForCustomerBase]

  }

  def computeCanadaTotalCustomers(customerBaseData: Dataset[CanadaConsolidatedFfcDataForCustomerBase]): Dataset[CanadaCustomerBaseData] = {
    import spark.implicits._

    customerBaseData
      .drop("total_wins")
      .groupBy(
        $"customer_base_date",
        $"current_holder_sp",
        $"previous_holder_sp",
        $"churn_winning_mode",
        $"churn_losing_mode",
        $"noncompetitive_ind",
        $"prev_churn_losing_sp",
        $"source_ind",
        $"merger_id",
        $"tn_intra_flows_only_flag",
        $"rc_id",
        $"govt_contract_ind",
        $"current_tenure_months",
        $"tenancy_months"
      )
      .agg(
        sum(when(trunc($"acquisition_date".cast(DateType), "month") < $"customer_base_date", $"adjusted_wins").otherwise(lit(0)))
          .as("total_customers"),
        sum(when(trunc($"acquisition_date", "MM") === $"customer_base_date", $"adjusted_wins").otherwise(lit(0)))
          .as("total_wins")
      )
      .select($"customer_base_date",
        $"current_holder_sp",
        $"previous_holder_sp",
        $"churn_winning_mode",
        $"churn_losing_mode",
        $"noncompetitive_ind",
        $"prev_churn_losing_sp",
        $"current_tenure_months",
        $"tenancy_months",
        $"total_customers",
        $"total_wins",
        $"rc_id",
        $"govt_contract_ind",
        $"source_ind",
        $"merger_id",
        $"tn_intra_flows_only_flag",
        lit(0.asInstanceOf[Double]).as("total_losses"))
      .repartition($"customer_base_date", $"current_holder_sp")
      .as[CanadaCustomerBaseData]
  }

  def updateCanadaWinsAndLosses(nextMeanCustomerBaseData: Dataset[CanadaCustomerBaseData])(customerBaseData: Dataset[CanadaCustomerBaseData]): Dataset[Row] = {
    import org.apache.spark.sql.functions._
    import spark.implicits._

    val maxCustomerBaseDate = customerBaseData
      .agg(max($"customer_base_date")
        .as("customer_base_date"))
      .select($"customer_base_date",
        lit("MAX_CUST_BASE_DATE").as("max_cust_base_date_ind"))

    val customerbaseLossData =customerBaseData
      .hint("broadcast")
      .join(maxCustomerBaseDate, Seq("customer_base_date"), "left_outer")
      .join(nextMeanCustomerBaseData
        .select($"customer_base_date",
          $"current_holder_sp",
          $"previous_holder_sp",
          $"churn_winning_mode",
          $"churn_losing_mode",
          $"noncompetitive_ind",
          $"prev_churn_losing_sp",
          $"rc_id",
          $"govt_contract_ind",
          $"source_ind",
          $"merger_id",
          $"tn_intra_flows_only_flag",
          $"current_tenure_months",
          $"tenancy_months",
          $"total_wins".as("next_total_wins"),
          $"total_customers".as("next_total_customers")),
        Seq("customer_base_date",
          "current_holder_sp",
          "previous_holder_sp",
          "churn_winning_mode",
          "churn_losing_mode",
          "noncompetitive_ind",
          "prev_churn_losing_sp",
          "rc_id",
          "govt_contract_ind",
          "source_ind",
          "merger_id",
          "tn_intra_flows_only_flag",
          "current_tenure_months",
          "tenancy_months"),
        "left_outer"
      )
      .select(
        $"customer_base_date",
        $"current_holder_sp",
        $"previous_holder_sp",
        $"churn_winning_mode",
        $"churn_losing_mode",
        $"noncompetitive_ind",
        $"prev_churn_losing_sp",
        $"rc_id",
        $"govt_contract_ind",
        $"source_ind",
        $"merger_id",
        $"tn_intra_flows_only_flag",
        $"current_tenure_months",
        $"tenancy_months",
        $"total_customers",
        coalesce($"next_total_wins", lit(0)).as("total_wins"),
        $"next_total_customers",
        $"max_cust_base_date_ind",
        $"next_total_wins",
        when($"tenancy_months".isNotNull and (coalesce($"max_cust_base_date_ind", lit("INTERMEDIATE")) =!= lit("MAX_CUST_BASE_DATE")), $"total_customers" - coalesce($"next_total_customers", lit(0.asInstanceOf[Double])))
          .otherwise($"total_losses").as("total_losses")
      )
      .unionAll(nextMeanCustomerBaseData
        .filter($"total_wins" > 0
          and $"tenancy_months".isNull)
        .select(
          $"customer_base_date",
          $"current_holder_sp",
          $"previous_holder_sp",
          $"churn_winning_mode",
          $"churn_losing_mode",
          $"noncompetitive_ind",
          $"prev_churn_losing_sp",
          $"rc_id",
          $"govt_contract_ind",
          $"source_ind",
          $"merger_id",
          $"tn_intra_flows_only_flag",
          $"current_tenure_months",
          $"tenancy_months",
          $"total_customers",
          $"total_wins",
          lit(0).as("next_total_customers"),
          lit("INTERMEDIATE").as("max_cust_base_date_ind"),
          lit(0).as("next_total_wins"),
          lit(0).as("total_losses")
        )
      )

    customerbaseLossData

  }

}


object CanadaCustomerBaseUtils {
  def apply()(implicit spark: SparkSession) = new CanadaCustomerBaseUtils()
}
