package com.comlinkdata.mvno.wcv.lookup

import com.comlinkdata.largescale.commons.RedshiftUtils.RedshiftConfig
import com.comlinkdata.largescale.commons.SqlServerUtils.SqlServerConfig
import com.comlinkdata.largescale.commons.Utils

import java.net.URI
import java.time.LocalDate

object WcvLookupSchema{

  case class WcvLookupConfig(
    jobUniqueId: String,
    jobName: String,
    dataVersion: String,
    env : String,
    clientName: String,
    industryModelTableName:String,
    configurationTableName: String,
    blacklistDataTableName:String,
    npaLookupTableName: String,
    npaMnoMvnoCorrectionFactorTableName: String,
    observedRecordsLookupPath:URI,
    modelledRecordsLookupPath:URI,
    minDate: Option[LocalDate],
    maxDate: Option[LocalDate],
    intraFlowAdjustmentTableName:String,
    subMonthlyAdjustmentTableName:String,
    sqlServerConfig: SqlServerConfig
  ){
    @transient
    implicit lazy val sc: SqlServerConfig = sqlServerConfig
  }

  // subMonthlyAdjustmentTableName contains lookup adjustments for both inter network and intra network flows
  object WcvLookupConfig {
    import com.comlinkdata.largescale.commons.fileutils.CldFileUtils.implicits._
    val dev: WcvLookupConfig = WcvLookupConfig(
      jobUniqueId = "999999",
      jobName= "Lookup Generation",
      dataVersion= "4.2.1",
      env= "dev",
      clientName= "cox",
      industryModelTableName="com_c220.dbo.mvno_industry_model",
      configurationTableName = "com_c220.dbo.mvno_industry_model_configurations",
      blacklistDataTableName="com_c220.dbo.d_npa_xmsm_blacklist",
      npaLookupTableName= "COM_C220.dbo.d_nanpa_lookup",
      npaMnoMvnoCorrectionFactorTableName =  "COM_C220.dbo.d_nanpa_mno_mvno_presence_correction_factor",
      observedRecordsLookupPath="s3://c200-dev-comlinkdata-com/wcv_4_2/lookup-tables/generic/mvno-algorithm-output/",
      modelledRecordsLookupPath="s3://c200-dev-comlinkdata-com/wcv_4_2/lookup-tables/generic/intra-mno-output/",
      minDate = Some(LocalDate.of(2016, 12, 15)),
      maxDate=Some(LocalDate.of(2022,1,1)),
      intraFlowAdjustmentTableName="COM_C220.dbo.mvno_im_intra_adjustment_date_sp_level",
      subMonthlyAdjustmentTableName="COM_C220.dbo.mvno_industry_model_v46_adjustments",
      sqlServerConfig=SqlServerConfig(sqlServerUserName = "raw_data_processor", sqlServerJdbcEndpoint = "*******************************************************************", sqlServerParameterStoreKey = "/c200/sql-server-toe-raw-data-processor")
    )
  }

  case class IndustryModel(
    churn_year: Integer,
    churn_month: Integer,
    begin_plan_type_id: Integer,
    begin_mno_sp: Integer,
    begin_reference_sp: Integer,
    begin_observed_sp: Integer,
    end_plan_type_id: Integer,
    end_mno_sp: Integer,
    end_reference_sp: Integer,
    end_observed_sp: Integer,
    est_begin_brand_sp:Integer,
    est_end_brand_sp:Integer,
    est: Double,
    port_est: Double,
    losing_mno_winning_mno_prop: Double,
    losing_mno_prop: Double
  )

  object IndustryModel extends Utils.reflection.ColumnNames[IndustryModel]

  case class LookupConfiguration(
    churn_type: String,
    begin_mno: String,
    begin_mno_sp: Integer,
    begin_brand: String,
    begin_brand_sp: Integer,
    begin_reference_sp: Integer,
    begin_observed_sp: Integer,
    begin_plan_type_id: Integer,
    begin_observation_type_id: Integer,
    begin_allocation_order: Integer,
    end_mno: String,
    end_mno_sp: Integer,
    end_brand: String,
    end_brand_sp: Integer,
    end_reference_sp: Integer,
    end_observed_sp: Integer,
    end_plan_type_id: Integer,
    end_observation_type_id: Integer,
    end_allocation_order: Integer,
    combined_observation_type_id: Integer,
    theoretical_is_observed: Boolean,
    is_observed: Boolean,
    is_activation: Boolean,
    is_disconnect: Boolean,
    is_self_flow: Boolean,
    is_migration: Boolean,
    launch_date:LocalDate,
    end_date:LocalDate
  )

  object LookupConfiguration extends Utils.reflection.ColumnNames[LookupConfiguration]

  case class NpaMnoMvnoCorrectionFactor(
    cld_network_operator: String,
    cld_brand_name: String,
    final_shapefile_complex: String,
    correction_factor:Double
  )

  object NpaMnoMvnoCorrectionFactor extends Utils.reflection.ColumnNames[NpaMnoMvnoCorrectionFactor]


  case class NpaLookup(
    npa: String,
    npa_complex_cld: String,
    state: String,
    state_name: String,
    final_shapefile_complex: String,
    nanpa_complex: String
  )

  object NpaLookup extends Utils.reflection.ColumnNames[NpaLookup]


  case class TutelaAggData(
    npa: String,
    cld_network_operator: String,
    cld_brand_name:String,
    correction_factor:Double,
    date: LocalDate,
    year: Int,
    month:Int
  )

  object TutelaAggData extends Utils.reflection.ColumnNames[TutelaAggData]

  case class Blacklist(
    npa:String,
    comcast:Boolean,
    spectrum:Boolean
  )

  object Blacklist extends Utils.reflection.ColumnNames[Blacklist]

  case class IntraFlowAdjustmentData(
    date: LocalDate,
    begin_reference_sp: Int,
    begin_brand_sp: Int,
    begin_plan_type_id: Int,
    end_reference_sp: Int,
    end_brand_sp: Int,
    end_plan_type_id : Int,
    multiplier: BigDecimal
  )

  object IntraFlowAdjustmentData extends Utils.reflection.ColumnNames[IntraFlowAdjustmentData]

  case class SubMonthlyAdjustmentData(
    start_date: LocalDate,
    end_date: LocalDate,
    begin_plan_type_id: Int,
    begin_mno_sp: Int,
    begin_reference_sp: Int,
    begin_observed_sp: Int,
    end_plan_type_id: Int,
    end_mno_sp: Int,
    end_reference_sp: Int,
    end_observed_sp: Int,
    est_begin_brand_sp: Int,
    est_end_brand_sp: Int,
    est: Double,
    port_est: Double,
    losing_mno_winning_mno_prop: Double,
    losing_mno_prop: Double
  )

  object SubMonthlyAdjustmentData extends Utils.reflection.ColumnNames[SubMonthlyAdjustmentData]

  case class ObservedMvnoLookupData(
    date:String,
    npa : String,
    churn_losing_sp : Int,
    losing_brand : Int,
    losing_plan_type : Int,
    churn_winning_sp : Int,
    winning_brand : Int,
    winning_plan_type : Int,
    min_random_number_value : Double,
    max_random_number_value : Double,
    min_random_number_value_geofix : Double,
    max_random_number_value_geofix : Double,
    min_random_number_value_geofix_smoothed : Double,
    max_random_number_value_geofix_smoothed : Double,
    min_random_number_value_geofix_unsmoothed : Double,
    max_random_number_value_geofix_unsmoothed : Double,
    sub_monthly_adjustment : Double
  )

  object ObservedMvnoLookupData extends Utils.reflection.ColumnNames[ObservedMvnoLookupData]


  case class ModelledMvnoLookupData(
    date:String,
    npa : String,
    churn_losing_sp : Int,
    losing_brand : Int,
    losing_plan_type : Int,
    churn_winning_sp : Int,
    winning_brand : Int,
    winning_plan_type : Int,
    percent_allocation : Double,
    percent_allocation_geofix : Double,
    percent_allocation_geofix_smoothed : Double,
    percent_allocation_geofix_unsmoothed : Double,
    act_adjusted_percent_allocation : Double,
    sub_monthly_adjustment : Double,
    multiplier : BigDecimal)

  object ModelledMvnoLookupData extends Utils.reflection.ColumnNames[ModelledMvnoLookupData]


  case class NpasList(
    year: Int,
    month:Int,
    npa: String,
    date: LocalDate
  )

}
