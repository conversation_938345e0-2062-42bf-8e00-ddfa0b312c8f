package com.comlinkdata.emrjobs.spark.sharetracker

import com.comlinkdata.largescale.commons.io.dataset.{dynamicOverwrite, overwrite}
import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON>, Spark<PERSON><PERSON><PERSON><PERSON><PERSON>, Utils}
import com.comlinkdata.largescale.schema.broadband.lookup.CarrierLookup
import com.comlinkdata.largescale.schema.udp.installbase.IPHighConfidence
import com.comlinkdata.emrjobs.spark.sharetracker.inputs.LegacyShareTrackerCustBase
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.{countDistinct, desc, lit, row_number}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

import java.net.URI
import java.time.LocalDate


case class LegacyShareTrackerCustBaseJobConfig(
  highConfidenceLocation: URI,
  broadbandCarrierLookupLocation: URI,
  legacyShareTrackerCustBaseOutputLocation: URI,
  outputPartitions: Int
)


object LegacyShareTrackerCustBaseJobConfig {

  val sample: LegacyShareTrackerCustBaseJobConfig = LegacyShareTrackerCustBaseJobConfig(
    highConfidenceLocation = URI create "s3://e000-comlinkdata-com/staging/broadband/installbase/high_confidence/v=0.4/",
    broadbandCarrierLookupLocation = URI create "s3://d000-comlinkdata-com/prod/private/lookup-tables/broadband/carrier-lookup/v=1.7.051/",
    legacyShareTrackerCustBaseOutputLocation = URI create "s3://e000-comlinkdata-com/dev/developers-sandbox/kchen/legacy-sharetracker-datasourcing/",
    outputPartitions = 1
  )
}


object LegacyShareTrackerCustBaseJob extends SparkJob[LegacyShareTrackerCustBaseJobConfig](LegacyShareTrackerCustBaseJobRunner)

object LegacyShareTrackerCustBaseJobRunner extends SparkJobRunner[LegacyShareTrackerCustBaseJobConfig] with LazyLogging {

  def runJob(config: LegacyShareTrackerCustBaseJobConfig)(implicit spark: SparkSession): Unit  = {
    import spark.implicits._

    val processingDate = Utils.calculateCurrQuarterDate(LocalDate.now())

    val tsl = IPHighConfidence.tsl(config.highConfidenceLocation)
    val latestDate = tsl.latestDate
    val highConfidence = IPHighConfidence.read(config.highConfidenceLocation, latestDate)
    val broadbandCarrierLookup = CarrierLookup.read(config.broadbandCarrierLookupLocation)

    val broadbandCarrier = getCarrierLookup(broadbandCarrierLookup)

    val joinResult =
    highConfidence
      .join(
        broadbandCarrier, $"carrier" === $"mw_carrier", "LEFT"
      )
    val finalOutputData = joinResult
      .groupBy($"consolidated_carrier", $"sp_platform", $"census_block_id")
      .agg(
        countDistinct($"ip") as "customer_base"
      )
      .select(
        $"consolidated_carrier",
        $"sp_platform" as "sp_id",
        $"sp_platform",
        $"census_block_id" as "census_block",
        lit("": String) as "wireless_carrier_name",
        lit("": String) as "oem",
        lit("": String) as "model_name",
        $"customer_base",
        lit(processingDate) as "date"
      )
      .as[LegacyShareTrackerCustBase]

    dynamicOverwrite(
      finalOutputData,
      config.legacyShareTrackerCustBaseOutputLocation,
      config.outputPartitions,
      "date"
    )
  }

  private def getCarrierLookup(broadbandCarrierLookup: Dataset[CarrierLookup])(implicit spark: SparkSession): DataFrame = {
    import spark.implicits._

    val window = Window.partitionBy("consolidated_id").orderBy(desc("min_date"))
    broadbandCarrierLookup
      .select(
        $"mw_carrier",
        $"consolidated_carrier",
        $"sp_platform",
        row_number.over(window) as "rank"
      )
      .where($"rank" === 1)
      .select(
        $"mw_carrier",
        $"consolidated_carrier",
        $"sp_platform"
      )
  }
}