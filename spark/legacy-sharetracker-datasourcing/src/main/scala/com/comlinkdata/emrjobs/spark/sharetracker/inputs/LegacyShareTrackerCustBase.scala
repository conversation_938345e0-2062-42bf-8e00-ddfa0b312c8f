package com.comlinkdata.emrjobs.spark.sharetracker.inputs

import com.comlinkdata.largescale.commons.TimeSeriesLocation
import org.apache.spark.sql.SparkSession

import java.net.URI
import java.sql.Date


case class LegacyShareTrackerCustBase(
  consolidated_carrier: String,
  sp_id: String,
  sp_platform: String,
  census_block: String,
  wireless_carrier_name: String,
  oem: String,
  model_name: String,
  customer_base: Double,
  date: Date
)

object LegacyShareTrackerCustBase {

  def tsl(path: URI)(implicit spark: SparkSession): TimeSeriesLocation  = {
    TimeSeriesLocation.ofDatePartitions(path, datePartitionName = "processing_date").build
  }
}