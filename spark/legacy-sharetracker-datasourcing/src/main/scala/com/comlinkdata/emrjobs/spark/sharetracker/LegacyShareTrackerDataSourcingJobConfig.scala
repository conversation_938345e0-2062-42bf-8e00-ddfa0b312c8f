package com.comlinkdata.emrjobs.spark.sharetracker

import java.net.URI
import java.time.LocalDate

/** Used as a predefined schema of the input configuration parameters */
case class LegacyShareTrackerDataSourcingJobConfig (
  // Inputs
  broadbandCustomerBaseLoc: URI,  // broadband installed base loc
  finalIfaLevelBaseSummaryBaseLoc: URI,  // IFA level base summary for the quarter
  currQuarterDate: Option[LocalDate], // current broadband installed base processed date
  prevQuarterDate: Option[LocalDate], // previous broadband installed base processed date

  // Outputs
  telcoBBCustomerBaseLoc: URI, // Telco BB customer base S3 location
  centrisBBCustomerBaseLoc: URI  // centris BB customer base S3 location
)

/** Stores a sample object for configuration parameters of type LegacyShareTrackerDataSourcingJobConfig */
object LegacyShareTrackerDataSourcingJobConfig {
  val sample: LegacyShareTrackerDataSourcingJobConfig = LegacyShareTrackerDataSourcingJobConfig (
    broadbandCustomerBaseLoc = URI create "s3://d000-comlinkdata-com/prod/private/broadband-customer-base/i-final-customer-base-aggregate/",
    finalIfaLevelBaseSummaryBaseLoc = URI create "s3://d000-comlinkdata-com/prod/private/broadband-customer-base/h-final-ifa-level-base-summary/",
    currQuarterDate = Option(LocalDate.of(2021,10,1)),
    prevQuarterDate = Option(LocalDate.of(2021,7,1)),
    // Dev output locations just for testing
    telcoBBCustomerBaseLoc = URI create "s3://d000-comlinkdata-com/dev/private/sharetracker/quarterly_datasourcing/telco_bb_customer_base/",
    centrisBBCustomerBaseLoc = URI create "s3://d000-comlinkdata-com/dev/private/sharetracker/quarterly_datasourcing/centris_bb_customer_base/"
  )
}
