package com.comlinkdata.emrjobs.spark.sharetracker

import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJob<PERSON><PERSON><PERSON>, Utils}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{DataFrame, Dataset, SaveMode, SparkSession}
import org.apache.spark.sql.functions._
import java.time.LocalDate


/** Used as a predefined schema of the the broadband customer base data as a dataset */
case class bbCustomerBase(
  consolidated_carrier: String,
  sp_id: String,
  sp_platform: String,
  census_block: String,
  wireless_carrier_name: String,
  oem: String,
  model_name: String,
  customer_base: Double
)

/** Used as a predefined schema of the IFA level summary data as a dataset */
case class ifaLevelBaseSummary(
  ifa: Array[Byte],
  is_ip_refresh: Boolean,
  consolidated_carrier: String,
  sp_id: String,
  sp_platform: String,
  census_block_group: String,
  home_source_flag: String,
  census_block: String,
  census_block_share: Double
)

/** Used as a predefined schema of quarterly aggregated IFA level base view as a dataset */
case class quarterlyIfaLevelBaseAggView (
  ifa: Array[Byte],
  census_block_group: String,
  sp_id: String,
  consolidated_carrier: String,
  blk_shr: Double
)

/** Used as a predefined schema of Telco broadband customer base output data dataset */
case class telcoBBCustomerBase(
  customer_base: Double,
  sp_platform: String,
  consolidated_carrier: String,
  census_block: String
)

/** Used as a predefined schema of Centris broadband customer base output data dataset */
case class centrisBBCustomerBase(
  cbg_121: String,
  spid_121: String,
  carrier_121: String,
  cbg_221: String,
  spid_221: String,
  carrier_221: String,
  cnt_dev: Long
)

/** Main object as a starting point for processing the algorithm */
object LegacyShareTrackerDataSourcingJob extends SparkJob(LegacyShareTrackerDataSourcingJobRunner)

/** Runner object which has the main runJob method to execute the whole algorithm steps  */
object LegacyShareTrackerDataSourcingJobRunner extends SparkJobRunner[LegacyShareTrackerDataSourcingJobConfig] with LazyLogging {

  /**
    * Main method which will read inputs and generate the output, and write it to the S3 locations
    * @param config: Object of input configuration parameters
    * */
  def runJob(config: LegacyShareTrackerDataSourcingJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    // Read input dates dynamically if not passed or passed as null
    // broadband customer base data processed dates for the current and previous quarter
    val currQuarterDate = config.currQuarterDate.getOrElse(Utils.calculateCurrQuarterDate(LocalDate.now()))
    val prevQuarterDate = config.prevQuarterDate.getOrElse(Utils.calculatePrevQuarterDate(LocalDate.now()))

    // current and previous quarter date validation
    val dateRange = LocalDateRange.of(prevQuarterDate, currQuarterDate)
    if (dateRange.endDate.isBefore(dateRange.startDate)) {
      logger.warn(s"Previous quarter date is not small with current quarter date: " +
        s"$dateRange, exiting job")
      return
    }
    logger.info(s"Running job with current quarter date: ${dateRange.endDate} and " +
      s"previous quarter date: ${dateRange.startDate}")

    // combined paths of the inputs locations
    val broadbandCustomerLoc = config.broadbandCustomerBaseLoc + "date=" + currQuarterDate.toString
    val prevQuarterIfaLevelBaseSummaryLoc = config.finalIfaLevelBaseSummaryBaseLoc + "date=" + prevQuarterDate.toString
    val currQuarterIfaLevelBaseSummaryLoc = config.finalIfaLevelBaseSummaryBaseLoc + "date=" + currQuarterDate.toString

    // Read current quarter broadband customer base data
    logger.info(s"Loading broadband customer base data from: ${broadbandCustomerLoc}")
    val bbCustomerBaseData = spark.read.parquet(broadbandCustomerLoc).as[bbCustomerBase]

    // Prepare Telco BB customer base data which is a summary of device volume by carrier & Census Block
    logger.info("Preparing the Telco Broadband customer base data table")
    val telcoBroadBandCBData: Dataset[telcoBBCustomerBase] = createTelcoBroadBandCB(bbCustomerBaseData)
    logger.info("Finished preparing the Telco Broadband customer base aggregated data table")

    // Read ifa level base summary data for the current and previous quarter
    logger.info(s"Reading current quarter Ifa level base summary data from: ${currQuarterIfaLevelBaseSummaryLoc}")
    val currQuarterIfaLevelBaseSummaryData = spark.read.parquet(currQuarterIfaLevelBaseSummaryLoc).as[ifaLevelBaseSummary]
    logger.info(s"Reading previous quarter Ifa level base summary data from: ${prevQuarterIfaLevelBaseSummaryLoc}")
    val prevQuarterIfaLevelBaseSummaryData = spark.read.parquet(prevQuarterIfaLevelBaseSummaryLoc).as[ifaLevelBaseSummary]

    // Prepare Centris BB customer base data which is a two quarter summary including current and previous
    // device counts by carrier & Census Block Group
    logger.info("Preparing the Centris Broadband customer base data table")
    val centrisBroadBandCBData: Dataset[centrisBBCustomerBase] = createCentrisBroadBandCB(
      currQuarterIfaLevelBaseSummaryData,
      prevQuarterIfaLevelBaseSummaryData
    )
    logger.info("Finished preparing the Centris Broadband customer base data table")

    // Write final data to S3 output locations
    // Telco BB Customer base data
    logger.info(s"Writing Telco broadband CB data to: ${config.telcoBBCustomerBaseLoc.toString}")
    telcoBroadBandCBData
      .withColumn("date", lit(currQuarterDate.toString))
      .repartition(1)
      .write
      .partitionBy("date")
      .mode(SaveMode.Append)
      .option("header", "true")
      .csv(config.telcoBBCustomerBaseLoc.toString)
    logger.info(s"Finished writing Telco broadband CB data to: ${config.telcoBBCustomerBaseLoc.toString}")

    // Centris BB Customer base data
    logger.info(s"Writing Centris broadband CB data to: ${config.centrisBBCustomerBaseLoc.toString}")
    centrisBroadBandCBData
      .withColumn("date", lit(currQuarterDate.toString))
      .repartition(1)
      .write
      .partitionBy("date")
      .mode(SaveMode.Append)
      .option("header", "true")
      .csv(config.centrisBBCustomerBaseLoc.toString)
    logger.info(s"Finished writing Centris broadband CB data to: ${config.centrisBBCustomerBaseLoc.toString}")

  } // end of runJob method

  /**
    * The method prepares the Telco staging table of a one quarter snapshot summary of device volume
    * by carrier & Census Block from broadband customer base data
    *
    * @param bbCustomerBaseData: broadband 1.7 installed base data for the current quarter
    * @return It returns the dataset of aggregated data of Telco staging table in the form of
    *         telcoBBCustomerBase
    * */
  def createTelcoBroadBandCB(bbCustomerBaseData: Dataset[bbCustomerBase])(implicit spark: SparkSession): Dataset[telcoBBCustomerBase] = {
    import spark.implicits._

    val telcoBBData: DataFrame = bbCustomerBaseData
      .groupBy("sp_platform", "consolidated_carrier", "census_block")
      .agg(
        (sum($"customer_base").as("customer_base"))
      )
      .select("customer_base",
        "sp_platform",
        "consolidated_carrier",
        "census_block"
      )

    telcoBBData.as[telcoBBCustomerBase]
  }

  /**
    * This method prepares the Centris staging table of a two quarter summary including current and previous
    * device counts by carrier & Census Block Group
    *
    * @param currQuarterIfaLevelBaseSummaryData: Current quarter ifa level base summary data
    * @param prevQuarterIfaLevelBaseSummaryData: Previous quarter ifa level base summary data
    * @return It returns the dataset of two quarter aggregated data of Centris staging table in the form of
    * centrisBBCustomerBase
    * */
  def createCentrisBroadBandCB(currQuarterIfaLevelBaseSummaryData: Dataset[ifaLevelBaseSummary],
    prevQuarterIfaLevelBaseSummaryData: Dataset[ifaLevelBaseSummary]
  )(implicit spark: SparkSession): Dataset[centrisBBCustomerBase] = {

    import spark.implicits._

    // Create agg view of ifa level base summary for current quarter
    logger.info("Creating a current quarter ifa level base aggregation view data")
    val currQuarterIfaLevelBaseAggView: Dataset[quarterlyIfaLevelBaseAggView] =
      createIfaLevelBaseAggView(currQuarterIfaLevelBaseSummaryData)
    logger.info("Finished creating a current quarter ifa level base aggregation view data")
    // Create agg view of ifa level base summary for previous quarter
    logger.info("Creating a previous quarter ifa level base aggregation view data")
    val prevQuarterIfaLevelBaseAggView: Dataset[quarterlyIfaLevelBaseAggView] =
      createIfaLevelBaseAggView(prevQuarterIfaLevelBaseSummaryData)
    logger.info("Finished creating a previous quarter ifa level base aggregation view data")

    // Join prev and current quarter agg view
    logger.info("Creating two quarter aggregated view based on IFA")
    val joinedDF: DataFrame = prevQuarterIfaLevelBaseAggView.as("a")
      .join(currQuarterIfaLevelBaseAggView.as("b"),
        col("a.ifa") === col("b.ifa"),
        "full_outer")

    // Prepare final aggregation data of Centris BB CB table
      val finalDf = joinedDF
      .groupBy(
        "a.census_block_group",
        "a.sp_id", "a.consolidated_carrier", "b.census_block_group", "b.sp_id",
        "b.consolidated_carrier"
      )
      .agg(
        (count("*").as("cnt_dev"))
      )
      .select(
        $"a.census_block_group".as("cbg_121"),
        $"a.sp_id".as("spid_121"),
        $"a.consolidated_carrier".as("carrier_121"),
        $"b.census_block_group".as("cbg_221"),
        $"b.sp_id".as("spid_221"),
        $"b.consolidated_carrier".as("carrier_221"),
        $"cnt_dev"
      ).distinct()

    finalDf.as[centrisBBCustomerBase]
  }

  /**
    * The method creates ifa level base aggregation view summing up census_block_share
    *
    * @param currQuarterIfaLevelBaseSummaryData: quarterly ifa level base summary data
    * @return It returns a quarterly ifa based aggregated view in the form of quarterlyIfaLevelBaseAggView
    * */
  def createIfaLevelBaseAggView(currQuarterIfaLevelBaseSummaryData: Dataset[ifaLevelBaseSummary])
    (implicit spark: SparkSession): Dataset[quarterlyIfaLevelBaseAggView] = {
    import spark.implicits._

    val quarterlyIfaLevelBaseAggView: DataFrame = currQuarterIfaLevelBaseSummaryData
      .groupBy("ifa", "census_block_group", "sp_id", "consolidated_carrier")
      .agg(
        (sum($"census_block_share").as("blk_shr"))
      )
      .select("ifa", "census_block_group", "sp_id", "consolidated_carrier", "blk_shr")
      .distinct()

    quarterlyIfaLevelBaseAggView.as[quarterlyIfaLevelBaseAggView]
  }
}
