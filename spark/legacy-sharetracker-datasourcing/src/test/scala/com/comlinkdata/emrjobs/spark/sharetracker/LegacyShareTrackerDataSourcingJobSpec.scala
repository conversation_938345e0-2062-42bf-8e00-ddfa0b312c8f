package com.comlinkdata.emrjobs.spark.sharetracker

import java.time.LocalDate
import com.comlinkdata.largescale.commons.Utils
import com.comlinkdata.commons.testing.CldSparkBaseSpec
import org.apache.spark.sql.functions._

import java.net.InetAddress


class LegacyShareTrackerDataSourcingJobSpec extends CldSparkBaseSpec {
  import spark.implicits._

  describe("createTelcoBroadBandCB") {
    it("test Telco BroadBand CB creation") {

      val bbCustomerBaseTestData = dataset[bbCustomerBase](
        """
        consolidated_carrier|sp_id|sp_platform|census_block|wireless_carrier_name|     oem|   model_name|customer_base|
        |                CC1|   10|         10|         121|     Verizon Wireless|      LG|      Reflect|          0.1|
        |                CC3|   11|         11|         122|        Other/Unknown|Motorola|      Moto G7|         0.02|
        |                CC2|   12|         12|         123|    T-Mobile Wireless|   Apple|     iPhone 8|       0.2345|
        |                CC2|   15|         12|         123|     Verizon Wireless|   Apple|     iPhone 8|          0.3|
        |                CC1|   10|         10|         121|    T-Mobile Wireless|      LG|      Reflect|          0.5|
        """
      )

      val expectedData = dataset[telcoBBCustomerBase](
        """
          |customer_base|sp_platform|consolidated_carrier|census_block|
          |         0.02|         11|                 CC3|         122|
          |       0.5345|         12|                 CC2|         123|
          |          0.6|         10|                 CC1|         121|
        """
      )

      val actualData = LegacyShareTrackerDataSourcingJobRunner.createTelcoBroadBandCB(bbCustomerBaseTestData).orderBy("customer_base")

      // count rows
      actualData.count() shouldEqual(expectedData.count())

      if (InetAddress.getLocalHost.toString.startsWith("CLD"))
        actualData.show()

      // Dataset equality
      assertDatasetEquals(expectedData, actualData)

      // Schema equality
      actualData.dtypes shouldBe expectedData.dtypes

    }
  }

  describe("createIfaLevelBaseAggView") {
    it("test ifa level base aggregation view creation") {

      val currQuarterIfaLevelBaseSummaryTestData = dataset[ifaLevelBaseSummary](
        """
          |ifa                             |is_ip_refresh|consolidated_carrier|sp_id|sp_platform|census_block_group|home_source_flag|census_block   |census_block_share  |
          |10060FF96B3942F994AE5A78FD51D4D2|null         |AT&T U-verse        |3985 |3982       |360310016011      |daily_homes     |060310016011716|0.0025947895022     |
          |10060FF96B3942F994AE5A78FD51D4D2|null         |Comcast             |3982 |3982       |260310016011      |daily_homes     |260310016011592|0.00384413          |
          |10060FF96B3942F994AB5A78FD51D4D3|null         |Comcast             |3988 |3981       |160310016010      |daily_homes     |160310016011602|0.013454481333272202|
          |10060FF96B3942F994AE5A78FD51D4D2|null         |AT&T U-verse        |3985 |3982       |360310016011      |daily_homes     |060310016011716|0.02576616436875    |
          |10060FF96B3942F994AB5A78FD51D4D3|null         |Comcast             |3983 |3983       |460310016011      |daily_homes     |360310016011607|0.0819265435539208  |
        """
      )

      val expectedData = dataset[quarterlyIfaLevelBaseAggView] (
        """
          |ifa                             |census_block_group|sp_id|consolidated_carrier|blk_shr             |
          |10060FF96B3942F994AE5A78FD51D4D2|360310016011      |3985 |AT&T U-verse        |0.02836095387095    |
          |10060FF96B3942F994AE5A78FD51D4D2|260310016011      |3982 |Comcast             |0.00384413          |
          |10060FF96B3942F994AB5A78FD51D4D3|160310016010      |3988 |Comcast             |0.013454481333272202|
          |10060FF96B3942F994AB5A78FD51D4D3|460310016011      |3983 |Comcast             |0.0819265435539208  |
        """
      )

      val actualData = LegacyShareTrackerDataSourcingJobRunner.createIfaLevelBaseAggView(
        currQuarterIfaLevelBaseSummaryTestData
      )

      // count rows
      actualData.count() shouldEqual(expectedData.count())

      // Schema equality
      actualData.dtypes shouldBe expectedData.dtypes

      // blk_shr total count
      val expectedSum: Double = 0.12758610875814302
      val actualSum = actualData.select($"blk_shr").agg(sum($"blk_shr")).first.get(0)

      actualSum shouldBe expectedSum
    }
  }

  describe("createCentrisBroadBandCB") {
    it("test Centris broadband CB creation") {

      val currQuarterIfaLevelBaseSummaryTestData = dataset[ifaLevelBaseSummary](
        """
          |ifa                             |is_ip_refresh|consolidated_carrier|sp_id|sp_platform|census_block_group|home_source_flag|census_block   |census_block_share  |
          |10060FF96B3942F994AE5A78FD51D4D2|null         |AT&T U-verse        |3985 |3982       |360310016011      |daily_homes     |060310016011716|0.0025947895022     |
          |10060FF96B3942F994AE5A78FD51D4D2|null         |Comcast             |3982 |3982       |260310016011      |daily_homes     |260310016011592|0.00384413          |
          |10060FF96B3942F994AB5A78FD51D4D3|null         |Comcast             |3988 |3981       |160310016010      |daily_homes     |160310016011602|0.013454481333272202|
          |10060FF96B3942F994AE5A78FD51D4D2|null         |AT&T U-verse        |3985 |3982       |360310016011      |daily_homes     |060310016011716|0.02576616436875    |
          |10060FF96B3942F994AB5A78FD51D4D3|null         |Comcast             |3983 |3983       |460310016011      |daily_homes     |360310016011607|0.0819265435539208  |
        """
      )

      val prevQuarterIfaLevelBaseSummaryTestData = dataset[ifaLevelBaseSummary](
        """
          |ifa                             |is_ip_refresh|consolidated_carrier|sp_id|sp_platform|census_block_group|home_source_flag|census_block   |census_block_share  |
          |00060FF96B3942F994AE5A78FD51D4D4|null         |AT&T U-verse        |3987 |3989       |560310016014      |daily_homes     |070310016011713|0.0225947895022     |
          |10060FF96B3942F994AE5A78FD51D4D2|null         |Comcast             |3982 |3982       |260310016011      |daily_homes     |260310016011592|0.00384413          |
          |10060FF96B3942F994AB5A78FD51D4D3|null         |Comcast             |3988 |3981       |160310016010      |daily_homes     |160310016011602|0.0134544813332     |
          |10060FF96B3942F994AE5A78FD51D4D2|null         |AT&T U-verse        |3985 |3982       |360310016011      |daily_homes     |060310016011716|0.02576616436875    |
          |10060FF96B3942F994AB5A78FD51D4D3|null         |Comcast             |3983 |3983       |460310016011      |daily_homes     |360310016011607|0.0819265435539208  |
        """
      )

      val expectedData = dataset[centrisBBCustomerBase] (
        """
          |cbg_121     |spid_121|carrier_121 |cbg_221     |spid_221|carrier_221 |cnt_dev|
          |360310016011|3985    |AT&T U-verse|360310016011|3985    |AT&T U-verse|1      |
          |160310016010|3988    |Comcast     |160310016010|3988    |Comcast     |1      |
          |460310016011|3983    |Comcast     |460310016011|3983    |Comcast     |1      |
          |160310016010|3988    |Comcast     |460310016011|3983    |Comcast     |1      |
          |260310016011|3982    |Comcast     |360310016011|3985    |AT&T U-verse|1      |
          |260310016011|3982    |Comcast     |260310016011|3982    |Comcast     |1      |
          |360310016011|3985    |AT&T U-verse|260310016011|3982    |Comcast     |1      |
          |460310016011|3983    |Comcast     |160310016010|3988    |Comcast     |1      |
          |560310016014|3987    |AT&T U-verse|null        |null    |null        |1      |
        """
      )

      val actualData = LegacyShareTrackerDataSourcingJobRunner.createCentrisBroadBandCB(
        currQuarterIfaLevelBaseSummaryTestData,
        prevQuarterIfaLevelBaseSummaryTestData
      )

      // count rows
      actualData.count() shouldEqual(expectedData.count())

      // Schema equality
      actualData.dtypes shouldBe expectedData.dtypes

      // total count of cnt_dev
      val expectedCount: Long = 9
      val actualCount = actualData.select($"cnt_dev").agg(sum($"cnt_dev")).first.get(0)

      actualCount shouldBe expectedCount
    }
  }

  describe("Verify input dates extraction for the quarter") {
    it("test calculateCurrQuarterDate") {
      val expected = "2021-10-01"
      val actual = Utils.calculateCurrQuarterDate(
        LocalDate.of(2021, 11, 15)
      ).toString

      actual should not be empty
      actual.shouldEqual(expected)
    }

    it("test calculatePrevQuarterDate") {
      val expected = "2021-01-01"
      val actual = Utils.calculatePrevQuarterDate(
        LocalDate.of(2021, 4, 10)
      ).toString

      actual should not be empty
      actual.shouldEqual(expected)
    }

    it("test calculatePrevQuarterDate for different year") {
      val expected = "2020-10-01"
      val actual = Utils.calculatePrevQuarterDate(
        LocalDate.of(2021, 1, 10)
      ).toString

      actual should not be empty
      actual.shouldEqual(expected)
    }
  }

//  describe("runJob") {
//    it("test configurations") {
//      LegacyShareTrackerDataSourcingJobRunner.runJob(LegacyShareTrackerDataSourcingJobConfig.sample)
//    }
//  }
}
