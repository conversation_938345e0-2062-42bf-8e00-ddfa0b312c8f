package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.emrjobs.spark.wireless_market_share.DemographicsUtilsDMA
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.{<PERSON>rk<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Utils}
import com.comlinkdata.largescale.commons.io.dataset.writeCsvOverwrite
import com.comlinkdata.largescale.schema.wireless_market_share.MonthlyOutputWithEthnicityDMA
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.SparkSession

import java.net.URI
import java.time.LocalDate
import scala.util.Try

/**
  * Configuration case class for the AlignBrandsAndDemographicsFieldsJob.
  *
  * @param monthToProcess         The specific month to process. Defaults to the previous month if not provided.
  * @param demographicsInputBasePath Base path for the demographics input data on S3.
  * @param outputBasePath         Base path for the output data on S3.
  * @param outputPartitions       Number of partitions for the output data.
  */

case class AlignBrandsAndDemographicsFieldsJobConfig(
  monthToProcess: Option[LocalDate],
  demographicsInputBasePath: URI,
  outputBasePath: URI,
  outputPartitions: Int
)

object AlignBrandsAndDemographicsFieldsJobConfig {

  val sample: AlignBrandsAndDemographicsFieldsJobConfig = AlignBrandsAndDemographicsFieldsJobConfig(
    monthToProcess = Option(LocalDate.of(2024, 12, 1)),
    demographicsInputBasePath = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/with_demographics/dma/",
    outputBasePath = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/inputs/packaging/",
    outputPartitions = 1
  )
}

object AlignBrandsAndDemographicsFieldsJob extends SparkJob[AlignBrandsAndDemographicsFieldsJobConfig](AlignBrandsAndDemographicsFieldsJobRunner)

object AlignBrandsAndDemographicsFieldsJobRunner extends SparkJobRunner[AlignBrandsAndDemographicsFieldsJobConfig] with LazyLogging {

  def runJob(config: AlignBrandsAndDemographicsFieldsJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val monthToProcess = config.monthToProcess.getOrElse(LocalDate.now().minusMonths(1).withDayOfMonth(1))

    // ** Read input data from S3 **

    val year = monthToProcess.getYear
    val month = monthToProcess.getMonthValue.formatted("%02d")

    val demographicsDataPath = Utils.joinPaths(
      URI create s"${config.demographicsInputBasePath.toString}", s"year=$year", s"month=$month"
    )
    val demographicsDataFinalPath = URI create generateNewROutputPath(demographicsDataPath)
    logger.info(s"Loading dma monthly demographics output data from the path: ${demographicsDataFinalPath}")
    val demographicsDMAData = MonthlyOutputWithEthnicityDMA.read_csv(demographicsDataFinalPath)

    // ** Perform calculations on the data **

    logger.info(s"Aligning Brands and Demographics fields the demographics output data...")
    val updatedDemographicsData = DemographicsUtilsDMA().updateBrandAndDemographicsFields(demographicsDMAData.toDF())

    // ** Write results to S3 **

    val outputBasePath = Utils.joinPaths(
      URI create s"${config.outputBasePath.toString}", s"year=$year", s"month=$month"
    )

    val futz = CldFileUtils.newBuilder.forUri(outputBasePath).build
    val maybeUriLatestR = Try(Utils.maxPartitionValue(outputBasePath, "r", futz, None)).toOption // latest revision
    val newR = maybeUriLatestR.map(ur => ur._2.toInt + 1).getOrElse(1) // latest revision increment by 1

    val outputPath = Utils.joinPaths(outputBasePath, s"r=$newR")
    logger.info(s"Writing data to the output path: $config.outputBasePath")
    writeCsvOverwrite(updatedDemographicsData, config.outputPartitions, outputPath.toString)
  }

  /**
    * Generates the path for the latest revision in the input data directory.
    *
    * @param basePath Base path for the input data on S3.
    * @param spark    The Spark session for the job.
    * @return The path of the latest revision as a string.
    */

  private def generateNewROutputPath(basePath: URI)(implicit spark: SparkSession): String = {
    val futz = CldFileUtils.newBuilder.forUri(basePath).build
    val (inputPath, latestR) = Utils.maxPartitionValue(basePath, "r", futz, None) // latest revision
    inputPath.toString
  }
}
