package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.largescale.commons.{RedshiftUtils, SparkJob, SparkJobRunner}
import com.comlinkdata.largescale.schema.wireless_market_share.redshift._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SaveMode, SparkSession}
import java.time.LocalDate
import java.net.URI


case class RedshiftUploadJobConfig(
  processingDate: Option[LocalDate],
  inputDatasetsBasePath: URI,
  redshiftTableSchema: String,
  redshiftJdbcEndpoint: String,
  redshiftUserName: String,
  redshiftParameterStoreKey: String,
  redshiftTempLocation: String
)

object RedshiftUploadJobConfig {

  val sample: RedshiftUploadJobConfig = RedshiftUploadJobConfig(
    processingDate = Option(LocalDate.of(2023, 1, 1)),
    inputDatasetsBasePath = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/redshift_upload_data/",
    redshiftTableSchema = "rpts",
    redshiftJdbcEndpoint = "******************************************************************************",
    redshiftUserName = "rpts_loader_dev",
    redshiftParameterStoreKey = "/c300/reporting_environment/rpts_loader_dev",
    redshiftTempLocation = "s3://d000-comlinkdata-com/redshift-uploads/wireless-market-share",
  )
}


object RedshiftUploadWMSJob extends SparkJob[RedshiftUploadJobConfig](RedshiftUploadWMSJobRunner)

  object RedshiftUploadWMSJobRunner extends SparkJobRunner[RedshiftUploadJobConfig] with LazyLogging {
    def runJob(config: RedshiftUploadJobConfig)(implicit spark: SparkSession): Unit = {

      val processingDate = config.processingDate.getOrElse(LocalDate.now().withDayOfMonth(1))
      val releaseMonthYear = getReleaseMonthYear(processingDate)
      val finalMonthYear = getFinalMonthYear(processingDate)

      // Read WMS National Gross Additions data
      val nationalGrossAdditionsFilename = s"wms_national_gross_additions_${finalMonthYear}_release_$releaseMonthYear.csv"
      val nationalGrossAdditionsPath = URI create s"${config.inputDatasetsBasePath}processing_date=$processingDate/$nationalGrossAdditionsFilename"
      logger.info(s"Reading national gross additions data from the path = $nationalGrossAdditionsPath")
      val nationalGrossAdditionsData = NationalGrossAdditionsOutput.read(nationalGrossAdditionsPath)

      // Read WMS National Gross Losses data
      val nationalGrossLossesFilename = s"wms_national_gross_losses_${finalMonthYear}_release_$releaseMonthYear.csv"
      val nationalGrossLossesPath = URI create s"${config.inputDatasetsBasePath}processing_date=$processingDate/$nationalGrossLossesFilename"
      logger.info(s"Reading national gross losses data from the path = $nationalGrossLossesPath")
      val nationalGrossLossesData = NationalGrossLossesOutput.read(nationalGrossLossesPath)

      // Read WMS National Subscribers data
      val nationalSubscribersFilename = s"wms_national_subscribers_${finalMonthYear}_release_$releaseMonthYear.csv"
      val nationalSubscribersPath = URI create s"${config.inputDatasetsBasePath}processing_date=$processingDate/$nationalSubscribersFilename"
      logger.info(s"Reading national subscribers data from the path = $nationalSubscribersPath")
      val nationalSubscribersData = NationalSubscribersOutput.read(nationalSubscribersPath)

      // Read WMS DMA data
      val dmaFilename = s"wms_dma_$finalMonthYear.csv"
      val dmaPath = URI create s"${config.inputDatasetsBasePath}processing_date=$processingDate/$dmaFilename"
      logger.info(s"Reading DMA data from the path = $dmaPath")
      val dmaData = DmaOutput.read(dmaPath)

      // Read WMS CMA data
      val cmaFilename = s"wms_cma_$finalMonthYear.csv"
      val cmaPath = URI create s"${config.inputDatasetsBasePath}processing_date=$processingDate/$cmaFilename"
      logger.info(s"Reading CMA data from the path = $dmaPath")
      val cmaData = CmaOutput.read(cmaPath)

      val rsConfig = RedshiftUtils.RedshiftConfig(
        rsTemporaryLocation = URI create config.redshiftTempLocation,
        rsJdbcEndpoint = config.redshiftJdbcEndpoint,
        rsUserName = config.redshiftUserName,
        rsParameterStoreKey = config.redshiftParameterStoreKey
      )

      val nationalGrossAdditionsRSTableName = s"${config.redshiftTableSchema}.${nationalGrossAdditionsFilename.split('.')(0)}"
      logger.info(s"Writing national gross additions data to the redshift table: $nationalGrossAdditionsRSTableName")
      RedshiftUtils.redshiftWrite(
        nationalGrossAdditionsData,
        nationalGrossAdditionsRSTableName,
        SaveMode.Overwrite
      )(rsConfig)

      val nationalGrossLossesRSTableName = s"${config.redshiftTableSchema}.${nationalGrossLossesFilename.split('.')(0)}"
      logger.info(s"Writing national gross losses data to the redshift table: $nationalGrossLossesRSTableName")
      RedshiftUtils.redshiftWrite(
        nationalGrossLossesData,
        nationalGrossLossesRSTableName,
        SaveMode.Overwrite
      )(rsConfig)

      val nationalSubscribersRSTableName = s"${config.redshiftTableSchema}.${nationalSubscribersFilename.split('.')(0)}"
      logger.info(s"Writing national subscribers data to the redshift table: $nationalSubscribersRSTableName")
      RedshiftUtils.redshiftWrite(
        nationalSubscribersData,
        nationalSubscribersRSTableName,
        SaveMode.Overwrite
      )(rsConfig)

      val dmaRSTableName = s"${config.redshiftTableSchema}.${dmaFilename.split('.')(0)}"
      logger.info(s"Writing DMA data to the redshift table: $dmaRSTableName")
      RedshiftUtils.redshiftWrite(
        dmaData,
        dmaRSTableName,
        SaveMode.Overwrite
      )(rsConfig)

      val cmaRSTableName = s"${config.redshiftTableSchema}.${cmaFilename.split('.')(0)}"
      logger.info(s"Writing CMA data to the redshift table: $cmaRSTableName")
      RedshiftUtils.redshiftWrite(
        cmaData,
        cmaRSTableName,
        SaveMode.Overwrite
      )(rsConfig)

    }  // end of runJob


    /**
      * Calculate release month year, for example: 2023_02 for Feb month processing
       * @param pDate: processing date parameter
      * @return Returns a string of year and month combined with underscore
      */
    def getReleaseMonthYear(pDate: LocalDate): String = {
      val month = pDate.getMonthValue.toString
      val formattedMonth = if (month.length == 1) "0".concat(month) else month
      s"${pDate.getYear.toString}_${formattedMonth}"  // eg. 2023-01
    }


    /**
      * Calculate Final month year, for example: 2023_01 for Feb month processing
      * The month and year for which the final data is available for WMS
      * @param pDate: processing date parameter
      * @return Returns a string of year and month combined with underscore
      */
    def getFinalMonthYear(pDate: LocalDate): String = {
      val lastAvailable = pDate.minusMonths(1)
      val lastMonth = lastAvailable.getMonthValue.toString
      val formattedLastMonth = if (lastMonth.length == 1) "0".concat(lastMonth) else lastMonth
      s"${lastAvailable.getYear}_${formattedLastMonth}"  // eg. 2022-12
    }

}
