package com.comlinkdata.emrjobs.spark.wireless_market_share.inputs

import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import org.apache.spark.sql.{Dataset, SparkSession}
import org.apache.spark.sql.functions.{sum, when}
import org.apache.spark.sql.types.DoubleType

class RegionalMNOUpscale(implicit spark: SparkSession) {

  import spark.implicits._

  /**
    * Aggregates output from rate_center_npa_total_subs to calculate total subs and losses
    * for ported and npa_complex_industry_model.
    * @param rateCenter: step 5.1 output
    * @return Dataset[NPAComplexTotalSubs]
    */
  def computeNPAComplexTotalSubs(rateCenter: Dataset[RateCenterNPATotalSubs]): Dataset[NPAComplexTotalSubs] = {
    rateCenter
      .groupBy(
        $"npa_complex_cld",
        $"customer_base_date",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"national_ported_tn_subscribers",
        $"national_industry_model_subscribers",
        $"national_total_subs_to_ported_subs_ratio",
        $"industry_model_sp_ind"
      )
      .agg(
        sum($"ported_tn_subscribers") as "ported_tn_subscribers",
        sum($"ported_tn_losses") as "ported_tn_losses",
        sum($"rate_center_industry_model_subscribers") as "npa_complex_industry_model_subscribers",
        sum($"rate_center_industry_model_losses") as "npa_complex_industry_model_losses"
      )
      .select(
        $"npa_complex_cld",
        $"customer_base_date",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"national_ported_tn_subscribers",
        $"national_industry_model_subscribers",
        $"national_total_subs_to_ported_subs_ratio",
        $"industry_model_sp_ind",
        $"ported_tn_subscribers",
        $"ported_tn_losses",
        $"npa_complex_industry_model_subscribers",
        $"npa_complex_industry_model_losses"
      )
      .as[NPAComplexTotalSubs]
  }

  /**
    * Aggregates output from rate_center_npa_total_adds to calculate total subs and losses
    * for ported and npa_complex_industry_model.
    * @param rateCenter 5.2 output
    * @return
    */
  def computeNPAComplexTotalAdds(rateCenter: Dataset[RateCenterNPATotalAdds]): Dataset[NPAComplexTotalAdds] = {
    rateCenter
      .groupBy(
        $"npa_complex_cld",
        $"customer_base_date",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"previous_holder_sp",
        $"previous_holder_plan_type_id",
        $"national_ported_tn_adds",
        $"national_industry_model_adds",
        $"national_total_adds_to_ported_adds_ratio",
        $"industry_model_sp_ind_current",
        $"industry_model_sp_ind_previous"
      )
      .agg(
        sum($"ported_tn_adds") as "ported_tn_adds",
        sum($"rate_center_industry_model_adds") as "npa_complex_industry_model_adds"
      )
      .select(
        $"npa_complex_cld",
        $"customer_base_date",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"previous_holder_sp",
        $"previous_holder_plan_type_id",
        $"national_ported_tn_adds",
        $"national_industry_model_adds",
        $"national_total_adds_to_ported_adds_ratio",
        $"industry_model_sp_ind_current",
        $"industry_model_sp_ind_previous",
        $"ported_tn_adds",
        $"npa_complex_industry_model_adds"
      )
      .as[NPAComplexTotalAdds]
  }

  /**
    * Aggregates output from step 6.1 by npa_complex_cld, date and current_holder_plan_type_id.
    * Calculates the total subs for ported_tn and npa_complex_industry_model, then finds the ratio
    * of npa_complex to ported_tn.
    * @param npaComplex 6.1 output
    * @return Dataset[NPAComplexPlanTypeMultipliers]
    */
  def computeNPAComplexPlanTypeMultipliers(npaComplex: Dataset[NPAComplexTotalSubs]): Dataset[NPAComplexPlanTypeMultipliers]  = {
    npaComplex
      .where($"industry_model_sp_ind" === 1)
      .groupBy(
        $"npa_complex_cld",
        $"customer_base_date",
        $"current_holder_plan_type_id"
      )
      .agg(
        sum($"ported_tn_subscribers") as "ported_tn_subscribers",
        sum($"npa_complex_industry_model_subscribers") as "npa_complex_industry_model_subscribers",
        sum($"npa_complex_industry_model_subscribers").cast(DoubleType) / sum("ported_tn_subscribers").cast(DoubleType) as "ported_to_total_subs_multiplier",
        sum($"ported_tn_losses") as "ported_tn_losses",
        sum($"npa_complex_industry_model_losses") as "npa_complex_industry_model_losses",
        sum("npa_complex_industry_model_losses").cast(DoubleType)/ sum("ported_tn_losses").cast(DoubleType) as "ported_to_total_loss_multiplier"
      )
      .select(
        $"npa_complex_cld",
        $"customer_base_date",
        $"current_holder_plan_type_id",
        $"ported_tn_subscribers",
        $"npa_complex_industry_model_subscribers",
        $"ported_to_total_subs_multiplier",
        $"ported_tn_losses",
        $"npa_complex_industry_model_losses",
        $"ported_to_total_loss_multiplier"
      )
      .as[NPAComplexPlanTypeMultipliers]
  }

  /**
    * Aggregates output from step 6.2 by npa_complex_cld, date, current_holder_plan_type_id and
    * previous_holder_plan_type_id. Calculates the total adds for ported_tn and npa_complex_industry_model,
    * then finds the ratio of npa_complex to ported_tn.
    * @param npaComplex: 6.2 output
    * @return Dataset[NPAComplexPlanTypeAddMultipliers]
    */
  def computeNPAComplexPlanTypeAddMultipliers(npaComplex: Dataset[NPAComplexTotalAdds]): Dataset[NPAComplexPlanTypeAddMultipliers]  = {
    npaComplex
      .where(
        $"industry_model_sp_ind_current" === 1 &&
          $"industry_model_sp_ind_previous" === 1
      )
      .groupBy(
        $"npa_complex_cld",
        $"customer_base_date",
        $"current_holder_plan_type_id",
        $"previous_holder_plan_type_id"
      )
      .agg(
        sum("ported_tn_adds") as "ported_tn_adds",
        sum("npa_complex_industry_model_adds") as "npa_complex_industry_model_adds",
        sum("npa_complex_industry_model_adds").cast(DoubleType) / sum("ported_tn_adds").cast(DoubleType) as "ported_to_total_adds_multiplier"
      )
      .select(
        $"npa_complex_cld",
        $"customer_base_date",
        $"current_holder_plan_type_id",
        $"previous_holder_plan_type_id",
        $"ported_tn_adds",
        $"npa_complex_industry_model_adds",
        $"ported_to_total_adds_multiplier"
      )
      .as[NPAComplexPlanTypeAddMultipliers]
  }

  /**
    * Calculates rate_center_npa_industry_model_subscribers and rate_center_npa_industry_model_losses
    * from the left join of steps 5.1 and 6.3
    * @param rateCenter: 5.1 output
    * @param npaComplex: 6.3 output
    * @return Dataset[RateCenterNPATotalSubsWithRegionalMNOs]
    */
  def computeRateCenterNPATotalSubsWithRegionalMNOs(rateCenter: Dataset[RateCenterNPATotalSubs], npaComplex: Dataset[NPAComplexPlanTypeMultipliers]): Dataset[RateCenterNPATotalSubsWithRegionalMNOs] = {
    val npaComplexRenamed = npaComplex
      .withColumnRenamed("ported_tn_subscribers","ported_tn_subscribers_complex")
      .withColumnRenamed("ported_tn_losses", "ported_tn_losses_complex")
      .withColumnRenamed("ported_to_total_subs_multiplier","ported_to_total_subs_multiplier_complex")
      .withColumnRenamed("ported_to_total_loss_multiplier", "ported_to_total_loss_multiplier_complex")
    rateCenter
      .join(
        npaComplexRenamed,
        Seq(
          "customer_base_date",
          "current_holder_plan_type_id",
          "npa_complex_cld"
        ),
        "LEFT"
      )
      .select(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"npa",
        $"npa_complex_cld",
        $"industry_model_sp_ind",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"national_ported_tn_subscribers",
        $"national_industry_model_subscribers",
        $"national_total_subs_to_ported_subs_ratio",
        $"national_ported_tn_losses",
        $"national_industry_model_losses",
        $"national_total_losses_to_ported_losses_ratio",
        $"ported_tn_subscribers",
        $"ported_tn_losses",
        when($"industry_model_sp_ind" === 1,$"rate_center_industry_model_subscribers")
          .when($"industry_model_sp_ind" === 0, $"ported_tn_subscribers" * $"ported_to_total_subs_multiplier_complex")
          .otherwise(0) as "rate_center_npa_industry_model_subscribers" ,
        when($"industry_model_sp_ind" === 1, $"rate_center_industry_model_losses")
          .when($"industry_model_sp_ind" === 0, $"ported_tn_losses" * $"ported_to_total_loss_multiplier_complex")
          .otherwise(0) as "rate_center_npa_industry_model_losses",
      )
      .as[RateCenterNPATotalSubsWithRegionalMNOs]
  }

  /**
    * Calculates rate_center_npa_industry_model_adds from the left join of steps 5.2 and 6.4
    * @param rateCenter: 5.2 output
    * @param npaComplex: 6.4 output
    * @return Dataset[RateCenterNPATotalAddsWithRegionalMNOs]
    */
  def computeRateCenterNPATotalAddsWithRegionalMNOs(rateCenter: Dataset[RateCenterNPATotalAdds], npaComplex: Dataset[NPAComplexPlanTypeAddMultipliers]): Dataset[RateCenterNPATotalAddsWithRegionalMNOs]  = {
    val npaComplexRenamed = npaComplex
      .withColumnRenamed("ported_tn_adds", "ported_tn_adds_complex")
      .withColumnRenamed("ported_to_total_adds_multiplier", "ported_to_total_adds_multiplier_complex")
    rateCenter
      .join(
        npaComplexRenamed,
        Seq(
          "customer_base_date",
          "current_holder_plan_type_id",
          "previous_holder_plan_type_id",
          "npa_complex_cld"
        ),
        "LEFT")
      .select(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"npa",
        $"npa_complex_cld",
        $"industry_model_sp_ind_current",
        $"industry_model_sp_ind_previous",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"previous_holder_sp",
        $"previous_holder_plan_type_id",
        $"national_ported_tn_adds",
        $"national_industry_model_adds",
        $"national_total_adds_to_ported_adds_ratio",
        $"ported_tn_adds",
        when($"industry_model_sp_ind_current" === 1 && $"industry_model_sp_ind_previous" === 1,$"rate_center_industry_model_adds" )
          .when($"industry_model_sp_ind_current" === 0 && $"industry_model_sp_ind_previous" === 1,$"ported_tn_adds".cast(DoubleType) * $"ported_to_total_adds_multiplier_complex")
          .when($"industry_model_sp_ind_current" === 1 && $"industry_model_sp_ind_previous" === 0,$"ported_tn_adds".cast(DoubleType) * $"ported_to_total_adds_multiplier_complex")
          .otherwise(0) as "rate_center_npa_industry_model_adds"
      )
      .as[RateCenterNPATotalAddsWithRegionalMNOs]
  }
}

object RegionalMNOUpscale {
  def apply()(implicit spark: SparkSession): RegionalMNOUpscale = new RegionalMNOUpscale()
}