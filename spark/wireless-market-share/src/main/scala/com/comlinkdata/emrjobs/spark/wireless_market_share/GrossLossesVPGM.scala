package com.comlinkdata.emrjobs.spark.wireless_market_share

import org.apache.spark.sql.{Dataset, SparkSession}
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.{ZeroOutBrandsVPGM, ZipToVPGM}
import com.comlinkdata.largescale.schema.wireless_market_share.{IndustryModel, PortedCarrierLossesVPGMInput, SubscribersOutputVPGM}
import org.apache.spark.sql.functions.{round, _}
import org.apache.spark.sql.types.IntegerType

import java.time.LocalDate


class GrossLossesVPGM(implicit spark: SparkSession)  {

  import spark.implicits._

  def calculateGrossLosses(
    processingDate: LocalDate,
    industryModel: Dataset[IndustryModel],
    portedCarrierLosses: Dataset[PortedCarrierLossesVPGM],
    subscribers: Dataset[SubscribersOutputVPGM]
  ): Dataset[EstimatedGrossLossesVPGM] = {

    // 1. GA_rate_avg
    val monthlyNationalAverageGrossLossRate = computeMonthlyNationalAverageGrossLossRate(processingDate, industryModel,portedCarrierLosses)

    // 2. GA_flat
    val flatGrossLosses = computeFlatGrossLosses(processingDate, subscribers, monthlyNationalAverageGrossLossRate)

    // 3. GA_flat_share
    val flatGrossLossesGeoShare = computeFlatGrossLossAverageFlatShare(flatGrossLosses)

    // 4. W_share
    val portedCarrierLossGeoShare = computePortedCarrierLossGeoShare(portedCarrierLosses)

    // 7. GA
    computeEstimatedGrossLosses(flatGrossLossesGeoShare,portedCarrierLossGeoShare, flatGrossLosses)

  }

  //GL_rate_avg[b,p,m] = GL[b,p,m] / S[b,p,m]
  def computeMonthlyNationalAverageGrossLossRate(
    processingDate: LocalDate,
    industryModel: Dataset[IndustryModel],
    portedCarrierLosses: Dataset[PortedCarrierLossesVPGM]
  ): Dataset[GrossLossesRateAverage] =  {
    val quarter = (processingDate.getMonthValue() +2) / 3
    val year = processingDate.getYear()
    val prevQuarter = if (quarter == 1) 4 else quarter - 1
    val prevYear = if (quarter == 1) year - 1 else year

    val industryModelPrev = industryModel
      .select($"brand", $"plan_type", $"year", $"quarter", $"gross_losses")
      .where($"quarter" === quarter && $"year" === year)
      .join(
        industryModel
          .where($"quarter" === prevQuarter && $"year" === prevYear)
          .select($"brand", $"plan_type", $"year", $"quarter", $"subscribers"),
        Seq("brand", "plan_type"),
        "LEFT"
      )
      .withColumn("gross_rate_average_industry_model", $"gross_Losses" / $"subscribers")

    val portedCarrierLossesQuarter = portedCarrierLosses
      .withColumn("quarter", date_format($"date_trunc", "q"))
      .select(
        $"loser" as "brand",
        $"secondary_plan_type_id" as "plan_type",
        $"date_trunc" as "customer_base_date",
        $"quarter",
        $"vpgm",
        $"switches"
      )
      .where(date_format($"customer_base_date", "yyyy").cast(IntegerType) === year)
      .groupBy($"brand", $"plan_type", $"customer_base_date", $"quarter")
      .agg(sum($"switches") as "switches_no_vpgm")
    val portedCarrierLossesAgg = portedCarrierLossesQuarter
      .groupBy($"brand", $"plan_type", $"quarter")
      .agg(sum($"switches_no_vpgm") as "switches_quarter")
    val portedCarrierLossesProcessed = portedCarrierLossesQuarter
      .join(portedCarrierLossesAgg, Seq("brand", "plan_type", "quarter"), "LEFT")
      .where($"customer_base_date" === processingDate)

    // current quarter GA / prev quarter subs
    industryModelPrev
      .join(portedCarrierLossesProcessed, Seq("brand", "plan_type", "quarter"), "LEFT")
      .withColumn("gross_rate_average", $"gross_rate_average_industry_model" * $"switches_no_vpgm" / $"switches_quarter") // divide by 3 for quarter to month
      .select($"brand", $"plan_type", $"customer_base_date",round($"gross_rate_average",4) as "gross_rate_average")
      .as[GrossLossesRateAverage]
  }

  //GL_flat[b,p,m,g] = S[b,p,m,g]*GL_rate_avg[b,p,m]
  def computeFlatGrossLosses(
    processingDate: LocalDate,
    subscribers: Dataset[SubscribersOutputVPGM],
    monthlyNationalAverageGrossLossRate: Dataset[GrossLossesRateAverage]
  ): Dataset[GrossLossesFlatVPGM] = {
    println(processingDate)
    val left = subscribers
      .select(
        $"brand",
        $"plan_type",
        add_months($"customer_base_date",1) as "customer_base_date",
        $"vpgm",
        $"subs"
      )
      .where($"customer_base_date" === processingDate)
    val right = monthlyNationalAverageGrossLossRate
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"gross_rate_average"
      )
      .where($"customer_base_date" === processingDate)
    left
      .join(right,Seq("brand","plan_type","customer_base_date"), "LEFT")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"vpgm",
        $"subs" * $"gross_rate_average" as "gross_losses_flat"
      )
      .as[GrossLossesFlatVPGM]
  }

  //GL_flat_share[b,p,m,g] = GL_flat[b,p,m,g] / sum{over b,p,m}(GL_flat[b,p,m,g] )
  def computeFlatGrossLossAverageFlatShare(
    flatGrossLosses: Dataset[GrossLossesFlatVPGM]
  ): Dataset[GrossLossesAverageFlatShareVPGM] = {
    val left = flatGrossLosses
      .groupBy($"brand", $"plan_type", $"customer_base_date", $"vpgm")
      .agg(sum($"gross_losses_flat") as "a_gross_losses_flat")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"vpgm",
        $"a_gross_losses_flat"
      )
    val right = flatGrossLosses
      .groupBy($"brand",$"plan_type",$"customer_base_date")
      .agg(sum($"gross_losses_flat") as "b_gross_losses_flat")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"b_gross_losses_flat"
      )
    left
      .join(right, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"vpgm",
        $"a_gross_losses_flat" / $"b_gross_losses_flat" as "gross_average_flat_share"
      )
      .as[GrossLossesAverageFlatShareVPGM]
  }

  //L_share[b,p,m,g] = L[b,p,g,m]/sum{over b,p,m}(L[b,p,g,m])
  def computePortedCarrierLossGeoShare(
    portedCarrierLosses: Dataset[PortedCarrierLossesVPGM]
  ): Dataset[PortedGeographicLossShareVPGM] = {
    val portedCarrierLossesRenamed = portedCarrierLosses
      .select(
        $"loser" as "brand",
        $"secondary_plan_type_id" as "plan_type",
        $"date_trunc" as "customer_base_date",
        $"vpgm",
        $"switches"
      )
    val left = portedCarrierLossesRenamed
      .groupBy($"brand", $"plan_type", $"customer_base_date", $"vpgm")
      .agg(sum($"switches") as "a_switches")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"vpgm",
        $"a_switches"
      )
    val right = portedCarrierLossesRenamed
      .groupBy($"brand", $"plan_type", $"customer_base_date")
      .agg(sum($"switches") as "b_switches")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"b_switches"
      )
    left
      .join(right, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"vpgm",
        $"a_switches" / $"b_switches" as "ported_loss_shares"
      )
      .as[PortedGeographicLossShareVPGM]
  }

  def computeEstimatedGrossLosses(
    flatGrossLossesGeoShare: Dataset[GrossLossesAverageFlatShareVPGM],
    portedCarrierLossGeoShare: Dataset[PortedGeographicLossShareVPGM],
    flatGrossLossesData: Dataset[GrossLossesFlatVPGM]
  ): Dataset[EstimatedGrossLossesVPGM] = {

    val A =
      flatGrossLossesGeoShare
        .join(portedCarrierLossGeoShare, Seq("brand", "plan_type", "customer_base_date", "vpgm"), "LEFT")
        .na.fill(0, Array("ported_loss_shares"))
        .withColumn("ga_share_variance", lit(0.5) * ($"ported_loss_shares" - $"gross_average_flat_share")) // something wrong here
        .withColumn("estimated_gross_loss_geo_share", $"gross_average_flat_share" + $"ga_share_variance") // something wrong here
        .select($"brand", $"plan_type", $"customer_base_date", $"vpgm", $"ga_share_variance", $"estimated_gross_loss_geo_share")

    flatGrossLossesData
      .groupBy($"brand", $"plan_type", $"customer_base_date")
      .agg(sum($"gross_losses_flat") as "gross_losses_flat_no_geo")
      .join(A, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
      .withColumn("estimated_gross_losses", $"estimated_gross_loss_geo_share" * $"gross_losses_flat_no_geo") // divide by 3 for quarter to month
      .select($"brand", $"plan_type", $"customer_base_date", $"vpgm", $"estimated_gross_losses")
      .na.fill(0)
      .as[EstimatedGrossLossesVPGM]
  }
}
object GrossLossesVPGM {
  def apply()(implicit spark: SparkSession): GrossLossesVPGM = new GrossLossesVPGM()

}
