package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import  com.comlinkdata.largescale.schema.wireless_market_share.{CmaFinalOutputPairwiseDemoCombinations, DmaFinalOutputPairwiseDemoCombinations, MsoIlecFootprintsFinalOutputPairwiseDemoCombinations, VpgmFromDmaFinalOutputPairwiseDemoCombinations, BoostMarketFinalOutputPairwiseDemoCombinations}
import com.comlinkdata.emrjobs.spark.wireless_market_share.Packaging
import com.comlinkdata.largescale.commons.io.dataset.writeCsvOverwrite
import com.comlinkdata.largescale.commons.{SparkJob, SparkJobRunner, Utils}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}
import org.apache.spark.sql.functions._

import java.time.LocalDate
import java.net.URI
import java.time.format.DateTimeFormatter

/**
  * Configurations for the PackagingFinalJob:
  *
  * @param creationDate                                            Optional creation date for output files; defaults to the current date.
  * @param dmaFinalOutputPairwiseDemoCombinationsLoc               URI location for DMA pairwise demo combinations data.
  * @param cmaFinalOutputPairwiseDemoCombinationsLoc               URI location for CMA pairwise demo combinations data.
  * @param vpgmFromDmaFinalOutputPairwiseDemoCombinationsLoc       URI location for VPGM pairwise demo combinations data.
  * @param msoIlecFootprintsFinalOutputPairwiseDemoCombinationsLoc URI location for MSO/ILEC footprints data.
  * @param boostMarketFinalOutputPairwiseDemoCombinationsLoc       URI location for Boost Market pairwise demo combinations data.
  * @param outputBasePath                                          Base URI for saving output files.
  * @param outputPartitions                                        Number of partitions for output files.
  */

case class PackagingFinalJobConfig(
  creationDate: Option[LocalDate],
  dmaFinalOutputPairwiseDemoCombinationsLoc: URI,
  cmaFinalOutputPairwiseDemoCombinationsLoc: URI,
  vpgmFromDmaFinalOutputPairwiseDemoCombinationsLoc: URI,
  msoIlecFootprintsFinalOutputPairwiseDemoCombinationsLoc: URI,
  boostMarketFinalOutputPairwiseDemoCombinationsLoc: URI,
  outputBasePath: URI,
  outputPartitions: Int
)

object PackagingFinalJobConfig {

  val sample: PackagingFinalJobConfig = PackagingFinalJobConfig(
    creationDate = Option(LocalDate.of(2024, 6, 10)),
    dmaFinalOutputPairwiseDemoCombinationsLoc = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging/dma_pairwise_final_output_all_demo_combinations/",
    cmaFinalOutputPairwiseDemoCombinationsLoc = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging/cma_pairwise_final_output_all_demo_combinations/",
    vpgmFromDmaFinalOutputPairwiseDemoCombinationsLoc = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging/vpgm_pairwise_final_output_all_demo_combinations/",
    msoIlecFootprintsFinalOutputPairwiseDemoCombinationsLoc = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging/mso_ilec_pairwise_final_output_all_demo_combinations/",
    boostMarketFinalOutputPairwiseDemoCombinationsLoc = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging/boost_market_final_output_pairwise_demo_combinations/",
    outputBasePath = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging_uat/",
    outputPartitions = 1
  )
}

object PackagingFinalJob extends SparkJob[PackagingFinalJobConfig](PackagingFinalJobRunner)

object PackagingFinalJobRunner extends SparkJobRunner[PackagingFinalJobConfig] with LazyLogging {

  def runJob(config: PackagingFinalJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    // ** Read input data from S3 **

    logger.info(s"Loading dma final output pairwise demo combinations data from the path: ${config.dmaFinalOutputPairwiseDemoCombinationsLoc}")
    val dmaFinalOutputPairwiseDemoCombinationsData = DmaFinalOutputPairwiseDemoCombinations.read(config.dmaFinalOutputPairwiseDemoCombinationsLoc)

    logger.info(s"Loading cma final output pairwise demo combinations data from the path: ${config.cmaFinalOutputPairwiseDemoCombinationsLoc}")
    val cmaFinalOutputPairwiseDemoCombinationsData = CmaFinalOutputPairwiseDemoCombinations.read(config.cmaFinalOutputPairwiseDemoCombinationsLoc)

    logger.info(s"Loading vpgm from dma final output pairwise demo combinations data from the path: ${config.vpgmFromDmaFinalOutputPairwiseDemoCombinationsLoc}")
    val vpgmFromDmaFinalOutputPairwiseDemoCombinationsData = VpgmFromDmaFinalOutputPairwiseDemoCombinations.read(config.vpgmFromDmaFinalOutputPairwiseDemoCombinationsLoc)

    logger.info(s"Loading mso ilec footprints final output pairwise demo combinations data from the path: ${config.msoIlecFootprintsFinalOutputPairwiseDemoCombinationsLoc}")
    val msoIlecFootprintsFinalOutputPairwiseDemoCombinationsData = MsoIlecFootprintsFinalOutputPairwiseDemoCombinations.read(config.msoIlecFootprintsFinalOutputPairwiseDemoCombinationsLoc)

    logger.info(s"Loading boost market final output pairwise demo combinations data from the path: ${config.boostMarketFinalOutputPairwiseDemoCombinationsLoc}")
    val boostMarketFinalOutputPairwiseDemoCombinationsData = BoostMarketFinalOutputPairwiseDemoCombinations.read(config.boostMarketFinalOutputPairwiseDemoCombinationsLoc)

    // Perform calculations on the data and set date parameters
    val creationDate = config.creationDate.getOrElse(LocalDate.now())

    // Define the creation date
    val yyyymmdd = creationDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"))
    println(s"yyyymmdd: $yyyymmdd")

    logger.info(s"Calculating the Dma Final Output Pairwise Demo Combinations...")
    val dmaFinalFinalOutput1 = Packaging().dmaFinalOutputPairwiseDemoCombinationsDF1(
      dmaFinalOutputPairwiseDemoCombinationsData, creationDate
    )

    val distinctMonth = dmaFinalFinalOutput1.select("month").distinct().orderBy(asc("month"))

    // Extract the first and last month values
    val firstMonth = distinctMonth.head()
    println(s"First Month: $firstMonth")
    val lastMonth = distinctMonth.tail(1).head
    println(s"Last Month: $lastMonth")

    val monthFormatter = DateTimeFormatter.ofPattern("MMMyy")

    // single month
    val firstMonthString = firstMonth.getAs[java.sql.Date]("month").toLocalDate()
    val monthSpecsSingle = firstMonthString.format(monthFormatter)
    println(s"Month Specs Single: $monthSpecsSingle")

    // range of months
    val startMonth = firstMonthString
    val endMonth = lastMonth.getAs[java.sql.Date]("month").toLocalDate()
    val monthSpecsRange = s"${startMonth.format(monthFormatter)}-${endMonth.format(monthFormatter)}"
    println(s"Month Specs Range: $monthSpecsRange")

    val monthSpecs = if (startMonth == endMonth) monthSpecsSingle else monthSpecsRange
    println(s"Final Month Specs: $monthSpecs")

    logger.info(s"Calculating the Dma Final Output Pairwise Demo Combinations...")
    val dmaFinalFinalOutput2 = Packaging().dmaFinalOutputPairwiseDemoCombinationsDF2(
      dmaFinalOutputPairwiseDemoCombinationsData, creationDate
    )

    logger.info(s"Calculating the Dma Final Output Pairwise Demo Combinations...")
    val dmaFinalFinalOutput3 = Packaging().dmaFinalOutputPairwiseDemoCombinationsDF3(
      dmaFinalOutputPairwiseDemoCombinationsData, creationDate
    )

    logger.info(s"Calculating the Cma Final Output Pairwise Demo Combinations...")
    val cmaFinalFinalOutput1 = Packaging().cmaFinalOutputPairwiseDemoCombinationsBaseDF1(
      cmaFinalOutputPairwiseDemoCombinationsData, creationDate
    )

    logger.info(s"Calculating the Cma Final Output Pairwise Demo Combinations...")
    val cmaFinalFinalOutput2 = Packaging().cmaFinalOutputPairwiseDemoCombinationsBaseDF2(
      cmaFinalOutputPairwiseDemoCombinationsData, creationDate
    )

    logger.info(s"Calculating the Cma Final Output Pairwise Demo Combinations...")
    val cmaFinalFinalOutput3 = Packaging().cmaFinalOutputPairwiseDemoCombinationsBaseDF3(
      cmaFinalOutputPairwiseDemoCombinationsData, creationDate
    )

    logger.info(s"Calculating the VPGM from DMA Final Output Pairwise Demo Combinations...")
    val vpgmFromDmaFinalOutput = Packaging().vpgmFromDmaFinalOutputPairwiseDemoCombinationsDF(
      vpgmFromDmaFinalOutputPairwiseDemoCombinationsData, creationDate
    )

    logger.info(s"Calculating the Mso Ilec Footprints Final Output Pairwise Demo Combinations...")
    val msoIlecFootprintsFinalOutput = Packaging().msoIlecFootprintsFinalOutputPairwiseDemoCombinationsDF(
      msoIlecFootprintsFinalOutputPairwiseDemoCombinationsData, creationDate
    )

    logger.info(s"Calculating the Boost Market Final Output Pairwise Demo Combinations...")
    val boostMarketFinalOutput = Packaging().boostMarketFinalOutputPairwiseDemoCombinationsDF(
      boostMarketFinalOutputPairwiseDemoCombinationsData, creationDate
    )

    // ** Write results to S3 **

    // Get the current date in the format "YYYY-MM-DD"
    val currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))

    // Base folder path with the date
    val baseFolderPath = Utils.joinPaths(config.outputBasePath.toString, s"update_$currentDate")

    val dmaPairwisePath1 = URI.create(Utils.joinPaths(baseFolderPath, s"DMA-$monthSpecs-PACKAGED-${yyyymmdd}_age_income")).toString
    logger.info(s"Writing dma final pairwise output data to the S3 location = $dmaPairwisePath1")
    writeCsvOverwrite(dmaFinalFinalOutput1, config.outputPartitions, dmaPairwisePath1)

    val dmaPairwisePath2 = URI.create(Utils.joinPaths(baseFolderPath, s"DMA-$monthSpecs-PACKAGED-${yyyymmdd}_ethnicity_income")).toString
    logger.info(s"Writing dma final pairwise output data to the S3 location = $dmaPairwisePath2")
    writeCsvOverwrite(dmaFinalFinalOutput2, config.outputPartitions, dmaPairwisePath2)

    val dmaPairwisePath3 = URI.create(Utils.joinPaths(baseFolderPath, s"DMA-$monthSpecs-PACKAGED-${yyyymmdd}_ethnicity_age")).toString
    logger.info(s"Writing dma final pairwise output data to the S3 location = $dmaPairwisePath3")
    writeCsvOverwrite(dmaFinalFinalOutput3, config.outputPartitions, dmaPairwisePath3)

    val cmaPairwisePath1 = URI.create(Utils.joinPaths(baseFolderPath, s"CMA-$monthSpecs-PACKAGED-${yyyymmdd}_age_income")).toString
    logger.info(s"Writing cma final pairwise output data to the S3 location = $cmaPairwisePath1")
    writeCsvOverwrite(cmaFinalFinalOutput1, config.outputPartitions, cmaPairwisePath1)

    val cmaPairwisePath2 = URI.create(Utils.joinPaths(baseFolderPath, s"CMA-$monthSpecs-PACKAGED-${yyyymmdd}_ethnicity_income")).toString
    logger.info(s"Writing cma final pairwise output data to the S3 location = $cmaPairwisePath2")
    writeCsvOverwrite(cmaFinalFinalOutput2, config.outputPartitions, cmaPairwisePath2)

    val cmaPairwisePath3 = URI.create(Utils.joinPaths(baseFolderPath, s"CMA-$monthSpecs-PACKAGED-${yyyymmdd}_ethnicity_age")).toString
    logger.info(s"Writing cma final pairwise output data to the S3 location = $cmaPairwisePath3")
    writeCsvOverwrite(cmaFinalFinalOutput3, config.outputPartitions, cmaPairwisePath3)

    val vpgmFromDmaPairwisePath = URI.create(Utils.joinPaths(baseFolderPath, s"VPGM-$monthSpecs-PACKAGED-$yyyymmdd")).toString
    logger.info(s"Writing vpgm from dma final pairwise output data to the S3 location = $vpgmFromDmaPairwisePath")
    writeCsvOverwrite(vpgmFromDmaFinalOutput, config.outputPartitions, vpgmFromDmaPairwisePath)

    val msoIlecFootprintsPairwisePath = URI.create(Utils.joinPaths(baseFolderPath, s"MSO-ILEC-SVT-$monthSpecs-PACKAGED-$yyyymmdd")).toString
    logger.info(s"Writing mso ilec footprints final pairwise output data to the S3 location = $msoIlecFootprintsPairwisePath")
    writeCsvOverwrite(msoIlecFootprintsFinalOutput, config.outputPartitions, msoIlecFootprintsPairwisePath)

    val boostMarketPairwisePath = URI.create(Utils.joinPaths(baseFolderPath, s"BOOST-$monthSpecs-PACKAGED-$yyyymmdd")).toString
    logger.info(s"Writing boost market final pairwise output data to the S3 location = $boostMarketPairwisePath")
    writeCsvOverwrite(boostMarketFinalOutput, config.outputPartitions, boostMarketPairwisePath)
  }
}

