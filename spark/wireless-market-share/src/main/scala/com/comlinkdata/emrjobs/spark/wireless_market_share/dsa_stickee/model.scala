package com.comlinkdata.emrjobs.spark.wireless_market_share.dsa_stickee

import java.sql.Date

object model {

  // Final Agg Schema
  case class dsaBroadbandStickeeAgg(
    the_date: Date,
    report_date: Date,
    isp_name: String,
    dma_name: String,
    technology_opensignal_st: Int,
    display_internet_plan_name: String,
    internet_plan_name: String,
    upload_speed_typical: Double,
    upload_speed_min: Double,
    upload_speed_max: Double,
    latency_speed_typical: Int,
    latency_speed_min: Int,
    latency_speed_max: Int,
    data_allotment: String,
    display_download_speed: Double,
    display_download_speed_without_rounding: Double,
    download_speed_typical: Double,
    download_speed_min: Double,
    download_speed_max: Double,
    rack_rate_price: Double,
    display_monthly_price: Double,
    contract_term: Int,
    intro_price: Double,
    intro_price_term: Int,
    secondary_price: Double,
    secondary_price_term: Int,
    autopay_discount: Double,
    //  wireless_bundle_discount: Double,
    wifi_equipment_intro_fee: Double,
    wifi_equipment_intro_term: Int,
    wifi_equipment_secondary_fee: Double,
    wifi_router_type: String,
    installation_included: Boolean,
    prof_installation_fee: Double,
    self_installation_fee: Double,
    late_payment_fee: Double,
    late_payment_fee_percentage: Double,
    total_cost_to_consumer_rack_rate: Double,
    total_cost_to_consumer_intro_rate: Double,
    sweetener_description: String,
    sweetener_value: Double,
    sweetener_value_type: String,
    sweetener_value_term: Int,
    sweetener_description_2: String,
    sweetener_value_2: Double,
    sweetener_value_type_2: String,
    sweetener_value_term_2: Int,
    sweetener_description_3: String,
    sweetener_value_3: Double,
    sweetener_value_type_3: String,
    sweetener_value_term_3: Int,
    offer_description: String,
    early_termination_fee: Double,
    non_return_equipment_fee: Double,
    current_customer_flag: Boolean,
    link: String,
    features_addons: String,
    features_addons_fee: Double,
    address_id: Int,
    dma_code: Int,
    city: String,
    state: String,
    zip_code: String,
    parent_sp: Int,
    serv_terr_id: String
  )

  // The ordered columns for the final Agg table to align the new column display_download_speed_without_rounding
  val dsaBroadbandStickeeAggOrderedCols: Seq[String] = Seq(
    "the_date", "report_date", "isp_name", "dma_name", "technology_opensignal_st",
    "display_internet_plan_name", "internet_plan_name", "upload_speed_typical",
    "upload_speed_min", "upload_speed_max", "latency_speed_typical", "latency_speed_min",
    "latency_speed_max", "data_allotment", "display_download_speed",
    "display_download_speed_without_rounding", "download_speed_typical", "download_speed_min",
    "download_speed_max", "rack_rate_price", "display_monthly_price", "contract_term", "intro_price",
    "intro_price_term", "secondary_price", "secondary_price_term", "autopay_discount",
    "wifi_equipment_intro_fee", "wifi_equipment_intro_term", "wifi_equipment_secondary_fee",
    "wifi_router_type", "installation_included", "prof_installation_fee", "self_installation_fee",
    "late_payment_fee", "late_payment_fee_percentage", "total_cost_to_consumer_rack_rate",
    "total_cost_to_consumer_intro_rate", "sweetener_description", "sweetener_value",
    "sweetener_value_type", "sweetener_value_term", "sweetener_description_2",
    "sweetener_value_2", "sweetener_value_type_2", "sweetener_value_term_2",
    "sweetener_description_3", "sweetener_value_3", "sweetener_value_type_3",
    "sweetener_value_term_3", "offer_description", "early_termination_fee",
    "non_return_equipment_fee", "current_customer_flag", "link", "features_addons", "features_addons_fee",
    "address_id", "dma_code", "city", "state", "zip_code", "parent_sp", "serv_terr_id"
  )

  // used for the test cases and for the intermediate datasets
  case class dsaBroadbandStickeeInputAgg(
    the_date: Date,
    report_date: Date,
    isp_name: String,
    dma_name: String,
    technology_opensignal_st: Int,
    display_internet_plan_name: String,
    internet_plan_name: String,
    upload_speed_typical: Double,
    upload_speed_min: Double,
    upload_speed_max: Double,
    latency_speed_typical: Int,
    latency_speed_min: Int,
    latency_speed_max: Int,
    data_allotment: String,
    display_download_speed: String,
    download_speed_typical: Double,
    download_speed_min: Double,
    download_speed_max: Double,
    rack_rate_price: Double,
    display_monthly_price: Double,
    contract_term: Int,
    intro_price: Double,
    intro_price_term: Int,
    secondary_price: Double,
    secondary_price_term: Int,
    autopay_discount: Double,
    wifi_equipment_intro_fee: Double,
    wifi_equipment_intro_term: Int,
    wifi_equipment_secondary_fee: Double,
    wifi_router_type: String,
    installation_included: Boolean,
    prof_installation_fee: Double,
    self_installation_fee: Double,
    late_payment_fee: Double,
    late_payment_fee_percentage: Double,
    total_cost_to_consumer_rack_rate: Double,
    total_cost_to_consumer_intro_rate: Double,
    sweetener_description: String,
    sweetener_value: Double,
    sweetener_value_type: String,
    sweetener_value_term: Int,
    sweetener_description_2: String,
    sweetener_value_2: Double,
    sweetener_value_type_2: String,
    sweetener_value_term_2: Int,
    sweetener_description_3: String,
    sweetener_value_3: Double,
    sweetener_value_type_3: String,
    sweetener_value_term_3: Int,
    offer_description: String,
    early_termination_fee: Double,
    non_return_equipment_fee: Double,
    current_customer_flag: Boolean,
    link: String,
    features_addons: String,
    features_addons_fee: Double,
    address_id: Int,
    dma_code: Int,
    city: String,
    state: String,
    zip_code: String,
    parent_sp: Int,
    serv_terr_id: String
  )

  val orderedCols: Seq[String] = Seq(
    "the_date", "report_date", "isp_name", "dma_name", "technology_opensignal_st",
    "display_internet_plan_name", "internet_plan_name", "upload_speed_typical",
    "upload_speed_min", "upload_speed_max", "latency_speed_typical", "latency_speed_min",
    "latency_speed_max", "data_allotment", "display_download_speed", "download_speed_typical",
    "download_speed_min", "download_speed_max", "rack_rate_price", "display_monthly_price",
    "contract_term", "intro_price", "intro_price_term", "secondary_price", "secondary_price_term",
    "autopay_discount", "wifi_equipment_intro_fee", "wifi_equipment_intro_term", "wifi_equipment_secondary_fee",
    "wifi_router_type", "installation_included", "prof_installation_fee", "self_installation_fee",
    "late_payment_fee", "late_payment_fee_percentage", "total_cost_to_consumer_rack_rate",
    "total_cost_to_consumer_intro_rate", "sweetener_description", "sweetener_value", "sweetener_value_type",
    "sweetener_value_term", "sweetener_description_2", "sweetener_value_2", "sweetener_value_type_2",
    "sweetener_value_term_2", "sweetener_description_3", "sweetener_value_3", "sweetener_value_type_3",
    "sweetener_value_term_3", "offer_description", "early_termination_fee", "non_return_equipment_fee",
    "current_customer_flag", "link", "features_addons", "features_addons_fee", "address_id",
    "dma_code", "city", "state", "zip_code", "parent_sp", "serv_terr_id"
  )

  val rawDataRSSchemaOrder: Seq[String] = Seq(
    "the_date",
    "actual_date",
    "isp_name",
    "dma_name",
    "technology_opensignal_st",
    "address_clean_geo",
    "display_internet_plan_name",
    "internet_plan_name",
    "upload_speed_typical",
    "upload_speed_min",
    "upload_speed_max",
    "latency_speed_typical",
    "latency_speed_min",
    "latency_speed_max",
    "data_allotment",
    "display_download_speed",
    "download_speed_typical",
    "download_speed_min",
    "download_speed_max",
    "rack_rate_price",
    "display_monthly_price",
    "display_price_is_intro",
    "contract_term",
    "intro_price",
    "intro_price_term",
    "secondary_price",
    "secondary_price_term",
    "autopay_discount",
    "wireless_bundle_discount",
    "wifi_equipment_intro_fee",
    "wifi_equipment_intro_term",
    "wifi_equipment_secondary_fee",
    "wifi_router_type",
    "installation_included",
    "prof_installation_fee",
    "self_installation_fee",
    "late_payment_fee",
    "late_payment_fee_percentage",
    "total_cost_to_consumer_rack_rate",
    "total_cost_to_consumer_intro_rate",
    "sweetener_description",
    "sweetener_value",
    "sweetener_value_type",
    "sweetener_value_term",
    "sweetener_description_2",
    "sweetener_value_2",
    "sweetener_value_type_2",
    "sweetener_value_term_2",
    "sweetener_description_3",
    "sweetener_value_3",
    "sweetener_value_type_3",
    "sweetener_value_term_3",
    "offer_description",
    "early_termination_fee",
    "non_return_equipment_fee",
    "current_customer_flag",
    "link",
    "features_addons",
    "features_addons_fee",
    "address_id",
    "address_primary",
    "dma_code",
    "city",
    "state",
    "zip_code",
    "parent_sp",
    "serv_terr_id",
    "error_message"
  )

  // The final RAW data schema
  case class DsaBroadbandStickeeRedshiftSchema(
    the_date: Date,
    actual_date: Date,
    isp_name: String,
    dma_name: String,
    technology_opensignal_st: Int,
    address_clean_geo: String,
    display_internet_plan_name: String,
    internet_plan_name: String,
    upload_speed_typical: Double,
    upload_speed_min: Double,
    upload_speed_max: Double,
    latency_speed_typical: Int,
    latency_speed_min: Int,
    latency_speed_max: Int,
    data_allotment: String,
    display_download_speed: String,
    download_speed_typical: Double,
    download_speed_min: Double,
    download_speed_max: Double,
    rack_rate_price: Double,
    display_monthly_price: Double,
    display_price_is_intro: Boolean,
    contract_term: Int,
    intro_price: Double,
    intro_price_term: Int,
    secondary_price: Double,
    secondary_price_term: Int,
    autopay_discount: Double,
    wireless_bundle_discount: Double,
    wifi_equipment_intro_fee: Double,
    wifi_equipment_intro_term: Int,
    wifi_equipment_secondary_fee: Double,
    wifi_router_type: String,
    installation_included: Boolean,
    prof_installation_fee: Double,
    self_installation_fee: Double,
    late_payment_fee: Double,
    late_payment_fee_percentage: Double,
    total_cost_to_consumer_rack_rate: Double,
    total_cost_to_consumer_intro_rate: Double,
    sweetener_description: String,
    sweetener_value: Double,
    sweetener_value_type: String,
    sweetener_value_term: Int,
    sweetener_description_2: String,
    sweetener_value_2: Double,
    sweetener_value_type_2: String,
    sweetener_value_term_2: Int,
    sweetener_description_3: String,
    sweetener_value_3: Double,
    sweetener_value_type_3: String,
    sweetener_value_term_3: Int,
    offer_description: String,
    early_termination_fee: Double,
    non_return_equipment_fee: Double,
    current_customer_flag: Boolean,
    link: String,
    features_addons: String,
    features_addons_fee: Double,
    address_id: Int,
    address_primary: String,
    dma_code: Int,
    city: String,
    state: String,
    zip_code: String,
    parent_sp: Int,
    serv_terr_id: String,
    error_message: String
  )

  val createTempAggTableQuery: String =
    s"""
       |DROP TABLE IF EXISTS %s;
       |
       |CREATE TABLE IF NOT EXISTS %s (
       |  the_date DATE ENCODE AZ64,
       |  report_date DATE ENCODE AZ64,
       |  isp_name VARCHAR(65535) ENCODE LZO,
       |  dma_name VARCHAR(65535) ENCODE LZO,
       |  technology_opensignal_st BIGINT ENCODE AZ64,
       |  display_internet_plan_name VARCHAR(65535) ENCODE LZO,
       |  internet_plan_name VARCHAR(65535) ENCODE LZO,
       |  upload_speed_typical DOUBLE PRECISION,
       |  upload_speed_min DOUBLE PRECISION,
       |  upload_speed_max DOUBLE PRECISION,
       |  latency_speed_typical BIGINT ENCODE AZ64,
       |  latency_speed_min BIGINT ENCODE AZ64,
       |  latency_speed_max BIGINT ENCODE AZ64,
       |  data_allotment VARCHAR(65535) ENCODE LZO,
       |  display_download_speed DOUBLE PRECISION,
       |  display_download_speed_without_rounding DOUBLE PRECISION,
       |  download_speed_typical DOUBLE PRECISION,
       |  download_speed_min DOUBLE PRECISION,
       |  download_speed_max DOUBLE PRECISION,
       |  rack_rate_price DOUBLE PRECISION,
       |  display_monthly_price DOUBLE PRECISION,
       |  contract_term BIGINT ENCODE AZ64,
       |  intro_price DOUBLE PRECISION,
       |  intro_price_term BIGINT ENCODE AZ64,
       |  secondary_price DOUBLE PRECISION,
       |  secondary_price_term BIGINT ENCODE AZ64,
       |  autopay_discount DOUBLE PRECISION,
       |  wifi_equipment_intro_fee DOUBLE PRECISION,
       |  wifi_equipment_intro_term BIGINT ENCODE AZ64,
       |  wifi_equipment_secondary_fee DOUBLE PRECISION,
       |  wifi_router_type VARCHAR(65535) ENCODE LZO,
       |  installation_included BOOLEAN,
       |  prof_installation_fee DOUBLE PRECISION,
       |  self_installation_fee DOUBLE PRECISION,
       |  late_payment_fee DOUBLE PRECISION,
       |  late_payment_fee_percentage DOUBLE PRECISION,
       |  total_cost_to_consumer_rack_rate DOUBLE PRECISION,
       |  total_cost_to_consumer_intro_rate DOUBLE PRECISION,
       |  sweetener_description VARCHAR(65535) ENCODE LZO,
       |  sweetener_value DOUBLE PRECISION,
       |  sweetener_value_type VARCHAR(65535) ENCODE LZO,
       |  sweetener_value_term BIGINT ENCODE AZ64,
       |  sweetener_description_2 VARCHAR(65535) ENCODE LZO,
       |  sweetener_value_2 DOUBLE PRECISION,
       |  sweetener_value_type_2 VARCHAR(65535) ENCODE LZO,
       |  sweetener_value_term_2 BIGINT ENCODE AZ64,
       |  sweetener_description_3 VARCHAR(65535) ENCODE LZO,
       |  sweetener_value_3 DOUBLE PRECISION,
       |  sweetener_value_type_3 VARCHAR(65535) ENCODE LZO,
       |  sweetener_value_term_3 BIGINT ENCODE AZ64,
       |  offer_description VARCHAR(65535) ENCODE LZO,
       |  early_termination_fee DOUBLE PRECISION,
       |  non_return_equipment_fee DOUBLE PRECISION,
       |  current_customer_flag BOOLEAN,
       |  link VARCHAR(65535) ENCODE LZO,
       |  features_addons VARCHAR(65535) ENCODE LZO,
       |  features_addons_fee DOUBLE PRECISION,
       |  address_id BIGINT ENCODE AZ64,
       |  dma_code BIGINT ENCODE AZ64,
       |  city VARCHAR(65535) ENCODE LZO,
       |  state VARCHAR(65535) ENCODE LZO,
       |  zip_code VARCHAR(65535) ENCODE LZO,
       |  parent_sp BIGINT ENCODE AZ64,
       |  serv_terr_id VARCHAR(65535) ENCODE LZO
       |);
    """.stripMargin


}
