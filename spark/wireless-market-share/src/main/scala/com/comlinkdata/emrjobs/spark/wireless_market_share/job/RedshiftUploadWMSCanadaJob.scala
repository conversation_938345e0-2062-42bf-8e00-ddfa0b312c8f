package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.largescale.commons.{RedshiftUtils, SparkJob, SparkJob<PERSON>unner}
import com.comlinkdata.largescale.schema.wireless_market_share.redshift._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SaveMode, SparkSession}
import java.time.LocalDate
import java.net.URI

/**
  * Case class representing the configuration for the Redshift upload job for WMS Canada.
  *
  * @param processingDate The processing date, optional.
  * @param inputDatasetsBasePath The base path for the input datasets in S3.
  * @param redshiftTableSchema The schema of the Redshift table.
  * @param redshiftTableName The name of the Redshift table.
  * @param redshiftJdbcEndpoint The JDBC endpoint for the Redshift cluster.
  * @param redshiftUserName The username for accessing Redshift.
  * @param redshiftParameterStoreKey The key for accessing the Redshift parameter store.
  * @param redshiftTempLocation The temporary location in S3 for Redshift uploads.
  */
case class RedshiftUploadWmsCanadaJobConfig(
  processingDate: Option[LocalDate],
  inputDatasetsBasePath: URI,
  redshiftTableSchema: String,
  redshiftTableName: String,
  redshiftJdbcEndpoint: String,
  redshiftUserName: String,
  redshiftParameterStoreKey: String,
  redshiftTempLocation: String
)

/**
  * A sample configuration for the Redshift upload job for WMS Canada.
  */
object RedshiftUploadWmsCanadaJobConfig {

  val sample: RedshiftUploadWmsCanadaJobConfig = RedshiftUploadWmsCanadaJobConfig(
    processingDate = Option(LocalDate.of(2023, 1, 1)),
    inputDatasetsBasePath = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/ca_wms_redshift_upload_data/",
    redshiftTableSchema = "rptsca",
    redshiftTableName = "wireless_market_share",
    redshiftJdbcEndpoint = "*******************************************************************************",
    redshiftUserName = "rpts_loader",
    redshiftParameterStoreKey = "/c300/reporting_environment/rpts_loader",
    redshiftTempLocation = "s3://d000-comlinkdata-com/redshift-uploads/wireless-market-share",
  )
}

/**
  * The main object for the Redshift upload job for WMS Canada.
  */
object RedshiftUploadWMSCanadaJob extends SparkJob[RedshiftUploadWmsCanadaJobConfig](RedshiftUploadWMSCanadaJobRunner)

/**
  * The runner for the Redshift upload job for WMS Canada.
  */
object RedshiftUploadWMSCanadaJobRunner extends SparkJobRunner[RedshiftUploadWmsCanadaJobConfig] with LazyLogging {

  /**
    * Runs the job with the given configuration.
    *
    * @param config The configuration for the job.
    * @param spark Implicit SparkSession.
    */
  def runJob(config: RedshiftUploadWmsCanadaJobConfig)(implicit spark: SparkSession): Unit = {

    val processingDate = config.processingDate.getOrElse(LocalDate.now().withDayOfMonth(1))
    val finalMonthYear = getFinalMonthYear(processingDate)


    // Read WMS Canada data
    val canadaFilename = s"wms_canada_$finalMonthYear.csv"
    val canadaPath = URI create s"${config.inputDatasetsBasePath}processing_date=$processingDate/$canadaFilename"
    logger.info(s"Reading WMS Canada data from the path = $canadaPath")
    val canadaData = WMSCanadaData.read(canadaPath).withColumnRenamed("final_renamed", "final")  // renamed final column name after reading to align with the original schema

    val rsConfig = RedshiftUtils.RedshiftConfig(
      rsTemporaryLocation = URI create config.redshiftTempLocation,
      rsJdbcEndpoint = config.redshiftJdbcEndpoint,
      rsUserName = config.redshiftUserName,
      rsParameterStoreKey = config.redshiftParameterStoreKey
    )

    val canadaRSTableName = s"${config.redshiftTableSchema}.${config.redshiftTableName}"
    // pre-action query is used to delete the existing data based on the processing date (delivery month)
    val preActionQuery = s"DELETE FROM $canadaRSTableName where processing_date = '$processingDate'"
    logger.info(s"Writing WMS Canada data to the Redshift table: $canadaRSTableName")
    RedshiftUtils.redshiftWrite(
      canadaData,
      canadaRSTableName,
      SaveMode.Append,
      Option(preActionQuery)
    )(rsConfig)
  }

  /**
    * Calculate Final month year, for example: 2023_01 for Feb month processing
    * The month and year for which the final data is available for WMS
    * @param pDate: processing date parameter
    * @return Returns a string of year and month combined with underscore
    */
  def getFinalMonthYear(pDate: LocalDate): String = {
    val lastAvailable = pDate.minusMonths(1)
    val lastMonth = lastAvailable.getMonthValue.toString
    val formattedLastMonth = if (lastMonth.length == 1) "0".concat(lastMonth) else lastMonth
    s"${lastAvailable.getYear}_${formattedLastMonth}"  // eg. 2022-12
  }
}
