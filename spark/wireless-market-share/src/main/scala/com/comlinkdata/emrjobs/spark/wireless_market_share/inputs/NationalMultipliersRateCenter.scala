package com.comlinkdata.emrjobs.spark.wireless_market_share.inputs

import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import org.apache.spark.sql.{Dataset, SparkSession}
import org.apache.spark.sql.functions.{broadcast, lit, when}
import org.apache.spark.sql.types.DoubleType


class NationalMultipliersRateCenter(implicit spark: SparkSession) {

  import spark.implicits._

  /**
    * Calculates rate_center_industry_model_subscribers as rate_center_subs * multipliers,
    * rate_center_industry_model_losses as rate center losses * multiplier, adds an indicator
    * to note if SPs are in or out of the industry model.
    * @param rateCenterNPAPortedInputs 4.1 output
    * @param nationalSubscriberMultipliers 3.1 output
    * @param lossMultipliers 3.2 output
    * @param nationalSubscriberMultipliersIndustry 1.1 output
    * @return Dataset[RateCenterNPATotalSubs]
    */
  def computeRateCenterNPATotalSubs(
    rateCenterNPAPortedInputs: Dataset[RateCenterNPAPortedCustomerBase],
    nationalSubscriberMultipliers: Dataset[NationalSubscriberMultipliers],
    lossMultipliers: Dataset[NationalLossMultipliers],
    nationalSubscriberMultipliersIndustry: Dataset[NationalSubscriberMultipliersIM]):
    Dataset[RateCenterNPATotalSubs] = {

    val nationalSubscribersMultipliersRenamed = nationalSubscriberMultipliers
      .withColumnRenamed("ported_tn_subscribers","national_ported_tn_subscribers")
    val nationalLossMultipliersRenamed = lossMultipliers
      .withColumnRenamed("ported_tn_losses","national_ported_tn_losses")

    rateCenterNPAPortedInputs
      .join(
        broadcast(nationalSubscribersMultipliersRenamed),
        Seq(
          "customer_base_date",
          "current_holder_sp",
          "current_holder_plan_type_id"
        ),
        "LEFT"
      )
      .join(
        broadcast(nationalLossMultipliersRenamed),
        Seq(
          "customer_base_date",
          "current_holder_sp",
          "current_holder_plan_type_id"
        ),
        "LEFT"
      )
      .join(
        broadcast(
          nationalSubscriberMultipliersIndustry
            .select($"current_holder_sp" as "national_overlap")
            .distinct()
        ),
        $"current_holder_sp" === $"national_overlap",
        "LEFT"
      )
      .where(
        $"current_holder_plan_type_id"
        .isin(List[Integer](1, 2): _*)
      )
      .select(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"npa",
        $"npa_complex_cld",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"ported_tn_subscribers",
        $"ported_tn_losses",
        $"national_ported_tn_subscribers",
        $"industry_model_subscribers" as "national_industry_model_subscribers",
        $"total_subs_to_ported_subs_ratio" as "national_total_subs_to_ported_subs_ratio", // specify
        $"ported_tn_subscribers".cast(DoubleType) * $"total_subs_to_ported_subs_ratio" as "rate_center_industry_model_subscribers",
        $"national_ported_tn_losses",
        $"industry_model_losses" as "national_industry_model_losses",
        $"total_losses_to_ported_losses_ratio" as "national_total_losses_to_ported_losses_ratio",
        $"ported_tn_losses" * $"total_losses_to_ported_losses_ratio" as "rate_center_industry_model_losses",
        when($"national_overlap".isNull, lit(0))
          .otherwise(lit(1)) as "industry_model_sp_ind"
      ).as[RateCenterNPATotalSubs]
  }

  /**
    * Excludes intra-MNO switching and activations, calculates rate_center_industry_model_adds
    * as rate center adds * multiplier and adds an indicator to note if current and previous
    * SPs are in or out of the industry model.
    * @param rateCenter 4.2 output
    * @param nationalAdd 3.3 output
    * @param nationalSubscriberMultipliersIndustry 1.1 output
    * @return Dataset[RateCenterNPATotalAdds]
    */
  def computeRateCenterNPATotalAdds(
    rateCenter: Dataset[RateCenterNPAAddsPortedCustomerBase],
    nationalAdd: Dataset[NationalAddMultipliers],
    nationalSubscriberMultipliersIndustry: Dataset[NationalSubscriberMultipliersIM]):
    Dataset[RateCenterNPATotalAdds]  = {
    val nationalAddRenamed = nationalAdd
      .withColumnRenamed("ported_tn_adds", "national_ported_tn_adds")
      .withColumnRenamed("industry_model_adds", "national_industry_model_adds")
      .withColumnRenamed("total_adds_to_ported_adds_ratio", "national_total_adds_to_ported_adds_ratio")

    rateCenter
      .join(
        broadcast(nationalAddRenamed),
        Seq(
          "customer_base_date",
          "current_holder_sp",
          "current_holder_plan_type_id",
          "previous_holder_sp",
          "previous_holder_plan_type_id"),
        "LEFT"
      )
      .join(
        broadcast(nationalSubscriberMultipliersIndustry)
          .select($"current_holder_sp" as "current_national_overlap")
          .distinct(),
        $"current_holder_sp" === $"current_national_overlap",
        "LEFT"
      )
      .join(
        broadcast(nationalSubscriberMultipliersIndustry)
          .select($"current_holder_sp" as "previous_national_overlap")
          .distinct(),
        $"previous_holder_sp" === $"previous_national_overlap",
        "LEFT"
      )
      .filter($"current_holder_plan_type_id".isin(1, 2))
      .filter($"previous_holder_plan_type_id".isin(1, 2))
      .select(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"npa",
        $"npa_complex_cld",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"previous_holder_sp",
        $"previous_holder_plan_type_id",
        $"ported_tn_adds",
        $"national_ported_tn_adds",
        $"national_industry_model_adds",
        $"national_total_adds_to_ported_adds_ratio",
        $"ported_tn_adds" * $"national_total_adds_to_ported_adds_ratio" as "rate_center_industry_model_adds",
        when($"current_national_overlap".isNull, lit(0))
          .otherwise(lit(1)) as "industry_model_sp_ind_current",
        when($"previous_national_overlap".isNull, lit(0))
          .otherwise(lit(1)) as "industry_model_sp_ind_previous"

      ).as[RateCenterNPATotalAdds]
  }

  /**
    * Calculates rate_center_industry_model_adds as rate center adds * multiplier
    * and adds an indicator to note if current and previous SPs are in or out of
    * the industry model. For intra-MNO switching only, inverse of table can be
    * used for rate_center_total_intra_mno_adds.
    * Unused ATM
    * @param rateCenter 4.3 output
    * @param multipliers 2.4 output
    * @param nationalSubscriberMultipliersIndustry 1.1 output
    * @return Dataset[RateCenterTotalIntraMNOAdds]
    */
  def computeRateCenterTotalIntraMNOAdds(
    rateCenter: Dataset[RateCenterAddsIntraMNO],
    multipliers: Dataset[NationalAddMultipliersIntraMno],
    nationalSubscriberMultipliersIndustry: Dataset[NationalSubscriberMultipliersIM]):
    Dataset[RateCenterTotalIntraMNOAdds] = {
    val multipliersRenamed = multipliers
      .withColumnRenamed("ported_tn_adds", "national_ported_tn_adds")
      .withColumnRenamed("industry_model_adds", "national_industry_model_adds")
      .withColumnRenamed("total_adds_to_ported_adds_ratio", "national_total_adds_to_ported_adds_ratio")
    rateCenter
      .join(
        multipliersRenamed,
        Seq(
          "customer_base_date",
          "current_holder_sp",
          "current_holder_plan_type_id",
          "previous_holder_sp",
          "previous_holder_plan_type_id"),
        "LEFT"
      )
      .where(
        ($"current_holder_plan_type_id" === lit(1) || $"current_holder_plan_type_id" === lit(2)) &&
          ($"previous_holder_plan_type_id" === lit(1) || $"previous_holder_plan_type_id" === lit(2))
      )
      .join(
        nationalSubscriberMultipliersIndustry
          .select($"current_holder_sp" as "current_national_overlap")
          .distinct(),
        $"current_holder_sp" === $"current_national_overlap",
        "LEFT"
      )
      .join(
        nationalSubscriberMultipliersIndustry
          .select($"current_holder_sp" as "previous_national_overlap")
          .distinct(),
        $"previous_holder_sp" === $"previous_national_overlap",
        "LEFT"
      )
      .select(
        $"customer_base_date",
        $"zip_rc_kblock",
        when($"current_national_overlap".isNull, lit(0))
          .otherwise(lit(1)) as "industry_model_sp_ind_current",
        when($"previous_national_overlap".isNull, lit(0))
          .otherwise(lit(1)) as "industry_model_sp_ind_previous",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"previous_holder_sp",
        $"previous_holder_plan_type_id",
        $"national_ported_tn_adds",
        $"national_industry_model_adds",
        $"national_total_adds_to_ported_adds_ratio",
        $"ported_tn_adds",
        $"ported_tn_adds" * $"national_total_adds_to_ported_adds_ratio" as "rate_center_industry_model_adds"
      )
      .as[RateCenterTotalIntraMNOAdds]
  }
}

object NationalMultipliersRateCenter  {
  def apply()(implicit spark: SparkSession): NationalMultipliersRateCenter = new NationalMultipliersRateCenter()
}
