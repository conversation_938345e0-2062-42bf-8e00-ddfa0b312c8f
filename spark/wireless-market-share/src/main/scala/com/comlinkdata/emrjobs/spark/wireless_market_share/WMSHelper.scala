package com.comlinkdata.emrjobs.spark.wireless_market_share
import org.apache.spark.sql.{Dataset, SparkSession}
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.{ZeroOutBrandsDMA, ZeroOutBrandsVPGM, ZipToVPGM}
import com.comlinkdata.largescale.schema.wireless_market_share.{PortedCarrierLosses, PortedCarrierLossesVPGMInput, PortedCarrierWins, PortedCarrierWinsVPGMInput}
import org.apache.spark.sql.functions._


class WMSHelper(implicit spark:SparkSession) {

  import spark.implicits._

  /**
    * The method used to set wins/market to zero for brand names with specific geographic area (DMA)
    * @param portedCarrierWins: WCV Ported Carrier Wins data
    * @param zeroOutBrands: Lookup data to refer which brand we need to mark zero wins
    * @return Returns the ported wins data with zeroed out market
    */
  def zeroOutAddsDMA(
    portedCarrierWins: Dataset[PortedCarrierWins],
    zeroOutBrands: Dataset[ZeroOutBrandsDMA]
  ): Dataset[PortedCarrierWins] = {
    portedCarrierWins
      .join(zeroOutBrands, Seq("winner", "primary_plan_type_id", "dma"), "LEFT")
      .withColumn("zeroed_switches", when($"zero" === 1, lit(0)).otherwise($"switches"))
      .select($"date_trunc", $"winner", $"primary_plan_type_id", $"secondary_plan_type_id", $"dma", $"dma_name", $"zeroed_switches" as "switches")
      .as[PortedCarrierWins]
  }

  def zeroOutAddsVPGM(
    portedCarrierWins: Dataset[PortedCarrierWinsVPGM],
    zeroOutBrands: Dataset[ZeroOutBrandsVPGM]
  ): Dataset[PortedCarrierWinsVPGM] = {
    portedCarrierWins
      .join(zeroOutBrands, Seq("winner", "primary_plan_type_id", "vpgm"), "LEFT")
      .withColumn("zeroed_switches", when($"zero" === 1, lit(0)).otherwise($"switches"))
      .select($"date_trunc", $"winner", $"primary_plan_type_id", $"secondary_plan_type_id", $"vpgm", $"zeroed_switches" as "switches")
      .as[PortedCarrierWinsVPGM]
  }

  /**
    * The method used to set losses/market to zero for brand names with specific geographic area (DMA)
    * @param portedCarrierLosses: WCV Ported Carrier Losses data
    * @param zeroOutBrands: Lookup data to refer which brand we need to mark zero losses
    * @return Returns the ported losses data with zeroed out market
    */
  def zeroOutLossesDMA(
    portedCarrierLosses: Dataset[PortedCarrierLosses],
    zeroOutBrands: Dataset[ZeroOutBrandsDMA]
  ): Dataset[PortedCarrierLosses] = {
    portedCarrierLosses.alias("losses")
      .join(
        zeroOutBrands.alias("zero"),
        $"winner" === $"loser" && $"secondary_plan_type_id" === $"zero.primary_plan_type_id" && $"losses.dma" === $"zero.dma",
        "LEFT"
      )
      .withColumn("zeroed_switches", when($"zero" === 1, lit(0)).otherwise($"switches"))
      .select($"date_trunc", $"loser", $"losses.primary_plan_type_id", $"secondary_plan_type_id", $"losses.dma", $"dma_name", $"zeroed_switches" as "switches")
      .as[PortedCarrierLosses]
  }

  def zeroOutLossesVPGM(
    portedCarrierLosses: Dataset[PortedCarrierLossesVPGM],
    zeroOutBrands: Dataset[ZeroOutBrandsVPGM]
  ): Dataset[PortedCarrierLossesVPGM] = {
    portedCarrierLosses.alias("losses")
      .join(
        zeroOutBrands.alias("zero"),
        $"winner" === $"loser" && $"secondary_plan_type_id" === $"zero.primary_plan_type_id" && $"losses.vpgm" === $"zero.vpgm",
        "LEFT"
      )
      .withColumn("zeroed_switches", when($"zero" === 1, lit(0)).otherwise($"switches"))
      .select($"date_trunc", $"loser", $"losses.primary_plan_type_id", $"secondary_plan_type_id", $"losses.vpgm", $"zeroed_switches" as "switches")
      .as[PortedCarrierLossesVPGM]
  }

  def convertWinsZipToVPGM(
    portedCarrierWins: Dataset[PortedCarrierWinsVPGMInput],
    zipToVPGM: Dataset[ZipToVPGM]
  ): Dataset[PortedCarrierWinsVPGM] = {
    portedCarrierWins
      .join(zipToVPGM, $"zip_cd" === $"rc_zip", "LEFT")
      .select($"date_trunc", $"winner", $"primary_plan_type_id", $"secondary_plan_type_id", $"vpgm", $"switches")
      .as[PortedCarrierWinsVPGM]
  }

  def convertLossesZipToVPGM(
    portedCarrierWins: Dataset[PortedCarrierLossesVPGMInput],
    zipToVPGM: Dataset[ZipToVPGM]
  ): Dataset[PortedCarrierLossesVPGM] = {
    portedCarrierWins
      .join(zipToVPGM, $"zip_cd" === $"rc_zip", "LEFT")
      .select($"date_trunc", $"loser", $"primary_plan_type_id", $"secondary_plan_type_id", $"vpgm", $"switches")
      .as[PortedCarrierLossesVPGM]
  }
}

object WMSHelper  {
  def apply()(implicit spark:SparkSession): WMSHelper = new WMSHelper()
}
