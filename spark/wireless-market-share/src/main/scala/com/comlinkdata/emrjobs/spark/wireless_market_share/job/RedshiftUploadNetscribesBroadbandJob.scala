package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.largescale.commons.{RedshiftUtils, Spark<PERSON><PERSON>, SparkJ<PERSON><PERSON><PERSON><PERSON>, Utils}
import com.comlinkdata.largescale.schema.Netscribes._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SaveMode, SparkSession}
import java.time.LocalDate
import java.net.URI

/**
  * Case class representing the configuration for the Redshift upload job for Netscribes Broadband data.
  *
  * @param processingDate              Optional date of processing.
  * @param netscribesBroadbandDataPath URI of the Netscribes broadband data.
  * @param redshiftTableSchema         Schema of the Redshift table.
  * @param redshiftTableName           Name of the Redshift table.
  * @param redshiftJdbcEndpoint        JDBC endpoint for Redshift.
  * @param redshiftUserName            Username for Redshift.
  * @param redshiftParameterStoreKey   Key for Redshift parameter store.
  * @param redshiftTempLocation        Temporary location for Redshift uploads.
  */

case class RedshiftUploadNetscribesBroadbandJobConfig(
  processingDate: Option[LocalDate],
  netscribesBroadbandDataPath: URI,
  redshiftTableSchema: String,
  redshiftTableName: String,
  redshiftJdbcEndpoint: String,
  redshiftUserName: String,
  redshiftParameterStoreKey: String,
  redshiftTempLocation: String
)


object RedshiftUploadNetscribesBroadbandJobConfig {
  val sample: RedshiftUploadNetscribesBroadbandJobConfig = RedshiftUploadNetscribesBroadbandJobConfig(
    processingDate = Option(LocalDate.of(2024, 1, 1)),
    netscribesBroadbandDataPath = URI create "s3://externaldata-nabia-comlinkdata-com/to_opensignal/USA/staging/edits/broadband/netscribes/",
    redshiftTableSchema = "netscribes",
    redshiftTableName = "d_netscribes_broadband",
    redshiftJdbcEndpoint = "******************************************************************************",
    redshiftUserName = "agg_loader_dev",
    redshiftParameterStoreKey = "/c200/redshift_agg_loader_dev",
    redshiftTempLocation = "s3://d000-comlinkdata-com/redshift-uploads/netscribes"
  )
}


object RedshiftUploadNetscribesBroadbandJob extends SparkJob[RedshiftUploadNetscribesBroadbandJobConfig](RedshiftUploadNetscribesBroadbandJobRunner)

object RedshiftUploadNetscribesBroadbandJobRunner extends SparkJobRunner[RedshiftUploadNetscribesBroadbandJobConfig] with LazyLogging {

  def runJob(config: RedshiftUploadNetscribesBroadbandJobConfig)(implicit spark: SparkSession): Unit = {

    val processingDay = config.processingDate.getOrElse(LocalDate.now())
    val year = processingDay.getYear
    val month = processingDay.getMonthValue.formatted("%02d")
    val day = processingDay.getDayOfMonth.formatted("%02d")
    val finalPath = Utils.joinPaths(config.netscribesBroadbandDataPath, s"year=$year", s"month=$month", s"day=$day")

    logger.info(s"Reading broadband data from the path = $finalPath")
    val broadbandData = BroadbandData.read(finalPath)

    // Redshift Config
    val rsConfig = RedshiftUtils.RedshiftConfig(
      rsTemporaryLocation = URI create config.redshiftTempLocation,
      rsJdbcEndpoint = config.redshiftJdbcEndpoint,
      rsUserName = config.redshiftUserName,
      rsParameterStoreKey = config.redshiftParameterStoreKey
    )

    /**
      * Write Netscribes broadband data to the specified Redshift table.
      *
      * @note Constructs the Redshift table name from the schema and table name provided in the configuration.
      *       Logs the table name and writes the data with the specified save mode and pre-action query.
      */

    // Load Netscribes broadband data in redshift
    val broadbandRSTableName = s"${config.redshiftTableSchema}.${config.redshiftTableName}"
    logger.info(s"Writing broadband data to the redshift table: $broadbandRSTableName")
    // Define your custom pre-action query
    val preActionQueryBroadband = s"DELETE FROM $broadbandRSTableName WHERE date = $processingDay;"
    RedshiftUtils.redshiftWrite(
      broadbandData,
      broadbandRSTableName,
      SaveMode.Append,
      preActionQuery = Some(preActionQueryBroadband)
    )(rsConfig)
  }
}