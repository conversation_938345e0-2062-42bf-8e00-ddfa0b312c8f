package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.emrjobs.spark.wireless_market_share.{DemographicsBackwardsInsertDMA, DemographicsForwardsInsertDMA, DemographicsMonthlyCreateOutputDMA, DemographicsUtilsDMA}
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.io.dataset.writeCsvOverwrite
import com.comlinkdata.largescale.commons.{SparkJob, SparkJobRunner, TimeSeriesLocation, Utils}
import com.comlinkdata.largescale.schema.wireless_market_share._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{Dataset, SparkSession}

import java.time.LocalDate
import java.net.URI
import scala.util.Try


/**
  * WMS 3.0 Demographic process configurations
  * @param monthToProcess: Specific month of subs we need to generate
  * @param jobToProcess: The process (CREATE, FORWARD, BACKWARD) to execute
  * @param nationalWCVFlowsLoc: National WCV flows calculated from National Oracle data
  * @param subscribersWithEthnicityLoc: Jun 22 frozen demographics data. Used only for CREATE process
  * @param dmaMonthlyOutputEthnicityAgeIncomeLoc: Existing monthly demographics output location to read last month data
  * @param wcvFlowsDMALoc: DMA WCV flows of carrier wins and losses
  * @param wmsDMALoc: Subscribers output data location
  * @param outputBasePath: Output base path to store the demographic results
  * @param outputPartitions: Number of partitions
  */
case class AttDemographicsDmaJobConfig(
  monthToProcess: Option[LocalDate],
  jobToProcess: String,
  nationalWCVFlowsLoc: URI,
  subscribersWithEthnicityLoc: URI,
  dmaMonthlyOutputEthnicityAgeIncomeLoc: URI,
  wcvFlowsDMALoc: URI,
  wmsDMALoc: URI,
  outputBasePath: URI,
  outputPartitions: Int
)

// Sample config just for FYIe
object AttDemographicsDmaJobConfig {

  val sample: AttDemographicsDmaJobConfig = AttDemographicsDmaJobConfig(
    monthToProcess = Option(LocalDate.of(2022, 7, 1)),
    jobToProcess = "CREATE",
    nationalWCVFlowsLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/demographics/dma/national_wcv_flows_ethnicity_age_income/",
    subscribersWithEthnicityLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/demographics/dma/dma_subs_by_ethnicity_age_income_jun2022/",
    dmaMonthlyOutputEthnicityAgeIncomeLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/outputs/with_demographics/dma/",
    wcvFlowsDMALoc = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/inputs/commons/dma/wcv_ported_wins_losses/",
    wmsDMALoc = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/without_demographics/dma/final_subs_output/",
    outputBasePath = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/outputs/with_demographics/dma/",
    outputPartitions = 1
  )
}


object AttDemographicsDmaJob extends SparkJob[AttDemographicsDmaJobConfig](AttDemographicsDmaJobRunner)

object AttDemographicsDmaJobRunner extends SparkJobRunner[AttDemographicsDmaJobConfig] with LazyLogging {
  def runJob(config: AttDemographicsDmaJobConfig)(implicit spark: SparkSession): Unit  = {
    import spark.implicits._

    // Default processing date would be the latest month
    val processingDate = LocalDate.now().withDayOfMonth(1)

    // Specific month to process, the default month to process should be the previous month
    val monthToProcess = config.monthToProcess.getOrElse(processingDate.minusMonths(1))
    logger.info(s"Calculating the Subscribers for the month: $monthToProcess")

    // Specific Job (CREATE, FORWARD, BACKWARD) to process
    val jobToProcess = Option(config.jobToProcess).getOrElse("FORWARD")

    // Read common schema's
    val year = monthToProcess.getYear

    val nationalWCVFlowsPath = Utils.joinPaths(
      config.nationalWCVFlowsLoc, s"processing_date=$processingDate")
    logger.info(s"Loading National WCS flows data from the path: ${nationalWCVFlowsPath}")
    val nationalWCVFlowsData = NationalWCVFlows.read(nationalWCVFlowsPath)

    // frozen subs of June 2022 used only for CREATE process
    val subscribersWithEthnicityPath = config.subscribersWithEthnicityLoc
    logger.info(s"Loading DMA subscribers with demographics data from the path: $subscribersWithEthnicityPath")
    val subscribersWithEthnicityData = SubscribersByEthnicityAgeIncomeDMA.read(subscribersWithEthnicityPath)

    // This can be used for the forward and backward insert query only
    var monthlyOutputWithEthnicityData: Dataset[MonthlyOutputWithEthnicityDMA] = null
    if(jobToProcess.equals("FORWARD") || jobToProcess.equals("BACKWARD")) {

      var month: String = null
      var year: String = null

      if(jobToProcess.equals("BACKWARD")) {
        val nextMonth = monthToProcess.plusMonths(1)
        month = nextMonth.getMonthValue.formatted("%02d")
        year = nextMonth.getYear.toString
      }

      if(jobToProcess.equals("FORWARD")) {
        val prevMonth = monthToProcess.minusMonths(1)
        month = prevMonth.getMonthValue.formatted("%02d")
        year = prevMonth.getYear.toString
      }

      val dmaMonthlyOutputEthnicityAgeIncomePath = Utils.joinPaths(
        URI create s"${config.dmaMonthlyOutputEthnicityAgeIncomeLoc.toString}", s"year=$year", s"month=$month"
      )
      val futz = CldFileUtils.newBuilder.forUri(dmaMonthlyOutputEthnicityAgeIncomePath).build
      val (inputPath, latestR) = Utils.maxPartitionValue(dmaMonthlyOutputEthnicityAgeIncomePath, "r", futz, None)
      logger.info(s"Loading Monthly DMA subscribers with demographics data from the path: $inputPath")
      monthlyOutputWithEthnicityData = MonthlyOutputWithEthnicityDMA.read_csv(inputPath)
    }

    // Ported Carrier Wins and Losses
    val wcvFlowsDMAPath = Utils.joinPaths(
      config.wcvFlowsDMALoc, s"processing_date=$processingDate")
    logger.info(s"Loading DMA WCV flows (Ported wins and losses) data from the path: $wcvFlowsDMAPath")
    val wcvFlowsDMAData = WCVFlowsDMA.read(wcvFlowsDMAPath)

    // Read Subscribers output data processed for monthToProcess
    val processingMonth = monthToProcess.getMonthValue.formatted("%02d")
    val wmsDMAPath = Utils.joinPaths(
      URI create s"${config.wmsDMALoc.toString}", s"year=$year", s"month=$processingMonth"
    )
    val wmsDMAFinalPath = URI create generateNewROutputPath(wmsDMAPath)
    logger.info(s"Loading Subscriber's Output data from the path: $wmsDMAFinalPath")
    val wmsDMAData = WmsDma.read(wmsDMAFinalPath)

    // Reading last period subs if available
    var wmsDMALastPeriodData = spark.emptyDataset[WmsDmaTemp]

    val wmsDMALastPeriodPath = Utils.joinPaths(
      URI create s"${config.wmsDMALoc.toString}", s"year=${monthToProcess.minusMonths(1).getYear}",
      s"month=${monthToProcess.minusMonths(1).getMonthValue.formatted("%02d")}"
    )
    logger.info(s"Checking if last period data exists at: $wmsDMALastPeriodPath")
    val tsPrev = TimeSeriesLocation.ofYmdDatePartitions(wmsDMALastPeriodPath).build

    if (tsPrev.exists) {
      // Added an existence check for the last period subscriber data.
      // This prevents Step failures that were occurring due to missing subscriber data for the last period.
      logger.info(s"Loading Last Period Subscriber's data from: $wmsDMALastPeriodPath")
      val wmsDMALastPeriodFinalPath = URI.create(generateNewROutputPath(wmsDMALastPeriodPath))
      wmsDMALastPeriodData = WmsDma.read(wmsDMALastPeriodFinalPath)
    }
    else {
      logger.warn(s"The Last period subs data not found at path: $wmsDMALastPeriodPath; Skip reading the last period's subs for the month to process: $monthToProcess")
    }

    // Reading next period subs if available
    var wmsDMANextPeriodData = spark.emptyDataset[WmsDmaTemp]

    // Read next period subs if monthToProcess is behind 2 months of the current month
    if(processingDate.minusMonths(1).isAfter(monthToProcess)) {
      val wmsDMANextPeriodPath = Utils.joinPaths(
        URI create s"${config.wmsDMALoc.toString}", s"year=${monthToProcess.plusMonths(1).getYear}",
        s"month=${monthToProcess.plusMonths(1).getMonthValue.formatted("%02d")}"
      )
      logger.info(s"Checking if data exists at path: $wmsDMANextPeriodPath")
      val tsNext = TimeSeriesLocation.ofYmdDatePartitions(wmsDMANextPeriodPath).build

      if (tsNext.exists) {
        // Added an existence check for the next period subscriber data.
        // This prevents Step failures that were occurring due to missing subscriber data for the next period.
        logger.info(s"Loading Next Period Subscriber's Output data from the path: $wmsDMANextPeriodPath")
        val wmsDMANextPeriodFinalPath = URI create generateNewROutputPath(wmsDMANextPeriodPath)
        wmsDMANextPeriodData = WmsDma.read(wmsDMANextPeriodFinalPath)
      }
      else {
        logger.warn(s"The Next period subs data not found at path: $wmsDMANextPeriodPath. Skip reading the next period's subs for the month to process: $monthToProcess")
      }
    }

    // Add last and next period subs column
    val wmsDMAFinalData = DemographicsUtilsDMA().addLastAndNextPeriodSubs(
      wmsDMAData, wmsDMALastPeriodData, wmsDMANextPeriodData
    )

    // ** Calculations **

    // Declarations
    var demographicsInitialMonthOutput = spark.emptyDataset[MonthlyOutputWithEthnicityDMA]
    var demographicsForwardsInitialMonthOutput = spark.emptyDataset[MonthlyOutputWithEthnicityDMA]
    var demographicsBackwardsMonthOutput = spark.emptyDataset[MonthlyOutputWithEthnicityDMA]

    jobToProcess match {

      case "CREATE" =>
        // create table
        logger.info(s"Calculating the demographics for the initial month...")
        demographicsInitialMonthOutput = DemographicsMonthlyCreateOutputDMA().calculateEthnicityMonthlyOutput(
          monthToProcess, nationalWCVFlowsData, subscribersWithEthnicityData, wcvFlowsDMAData, wmsDMAFinalData
        )
        logger.info(s"Finished calculating the demographics for the initial month.")

      case "FORWARD" =>
        // forwards insert
        logger.info(s"Calculating the demographics for the forwards month...")
        demographicsForwardsInitialMonthOutput = DemographicsForwardsInsertDMA().calculateEthnicityMonthlyOutputForwardsInsert(
          monthToProcess, nationalWCVFlowsData, monthlyOutputWithEthnicityData, wcvFlowsDMAData, wmsDMAFinalData
        )

      case "BACKWARD" =>
        // Backwards Insert
        logger.info(s"Calculating the demographics for the backwards month...")
        demographicsBackwardsMonthOutput = DemographicsBackwardsInsertDMA().calculateEthnicityMonthlyOutputBackwardsInsert(
          monthToProcess, nationalWCVFlowsData, monthlyOutputWithEthnicityData, wcvFlowsDMAData, wmsDMAFinalData
        )
    }

    // ** Write data to S3 **
    val month = monthToProcess.getMonthValue.formatted("%02d")
    val outputBasePath = Utils.joinPaths(
      URI create s"${config.outputBasePath.toString}", s"year=$year", s"month=$month"
    )

    val futz = CldFileUtils.newBuilder.forUri(outputBasePath).build
    val maybeUriLatestR = Try(Utils.maxPartitionValue(outputBasePath, "r", futz, None)).toOption  // latest revision
    val newR = maybeUriLatestR.map(ur => ur._2.toInt + 1).getOrElse(1)   // latest revision increment by 1
    val outputPath = Utils.joinPaths(outputBasePath.toString, s"r=$newR")

    jobToProcess match {

      case "CREATE" =>

        // Demographics Initial Month
        logger.info(s"Writing demographics for the initial month to the S3 location = $outputPath")
        writeCsvOverwrite(demographicsInitialMonthOutput, config.outputPartitions, outputPath)

      case "FORWARD" =>
        // Demographics Forwards Initial Month
        logger.info(s"Writing demographics for the forwards month to the S3 location = $outputPath")
        writeCsvOverwrite(demographicsForwardsInitialMonthOutput, config.outputPartitions, outputPath)

      case "BACKWARD" =>
        // Demographics Backwards Initial Month
        logger.info(s"Writing demographics for the backwards month to the S3 location = $outputPath")
        writeCsvOverwrite(demographicsBackwardsMonthOutput, config.outputPartitions, outputPath)
    }
  }


  private def generateNewROutputPath(basePath: URI)(implicit spark: SparkSession): String = {
    val futz = CldFileUtils.newBuilder.forUri(basePath).build
    val (inputPath, latestR) = Utils.maxPartitionValue(basePath, "r", futz, None)  // latest revision
    inputPath.toString
  }
}
