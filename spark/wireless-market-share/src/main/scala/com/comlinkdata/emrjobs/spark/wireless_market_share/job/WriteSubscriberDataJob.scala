package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON>, Spark<PERSON><PERSON><PERSON><PERSON><PERSON>, Utils}
import com.comlinkdata.largescale.commons.io.dataset.writeCsvOverwrite
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._

import java.net.URI
import scala.util.Try

/**
  * Configuration case class for the WriteSubscriberDataJob.
  *
  * @param inputBasePath    The input base path URI where subscriber data is located.
  * @param outputBasePath   The output base path URI where the processed data will be written.
  * @param outputPartitions The number of output partitions for the resulting files.
  */
case class WriteSubscriberDataJobConfig(
  inputBasePath: URI,
  outputBasePath: URI,
  outputPartitions: Int
)

object WriteSubscriberDataJobConfig {

  val sample: WriteSubscriberDataJobConfig = WriteSubscriberDataJobConfig(
    inputBasePath = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/inputs/without_demographics/dma/combined_montly_output/",
    outputBasePath = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/without_demographics/dma/final_subs_output/",
    outputPartitions = 1
  )
}

object WriteSubscriberDataJob extends SparkJob[WriteSubscriberDataJobConfig](WriteSubscriberDataJobRunner)

object WriteSubscriberDataJobRunner extends SparkJobRunner[WriteSubscriberDataJobConfig] with LazyLogging {
  /**
    * Executes the job logic to read subscriber data, process it by extracting year and month,
    * and write the data partitioned by year and month to the specified output path.
    */
  def runJob(config: WriteSubscriberDataJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val inputDataDF = spark.read.option("header", "true").option("inferSchema", "true").csv(config.inputBasePath.toString)

    // Extract year and month
    val updatedDF = inputDataDF
      .withColumn("year", year(to_date(col("customer_base_date"), "yyyy-MM-dd")))
      .withColumn("month", month(to_date(col("customer_base_date"), "yyyy-MM-dd")))

    // Get distinct year and month
    val yearMonthPairs = updatedDF
      .select("year", "month")
      .distinct()
      .collect()
      .map(row => (row.getInt(0), row.getInt(1)))

    // Write data for each year and month
    yearMonthPairs.foreach { case (year, month) =>
      val monthData = updatedDF.filter($"year" === year && $"month" === month)

      // Define the output path
      val outputBasePath = Utils.joinPaths(
        URI create s"${config.outputBasePath.toString}",
        s"year=$year/month=$month/"
      )

      // Determine the next revision number for the output
      val futz = CldFileUtils.newBuilder.forUri(outputBasePath).build
      val maybeUriLatestR = Try(Utils.maxPartitionValue(outputBasePath, "r", futz, None)).toOption
      val newR = maybeUriLatestR.map(ur => ur._2.toInt + 1).getOrElse(1)
      val outputPath = Utils.joinPaths(outputBasePath.toString, s"r=$newR")

      // Log and write the data
      logger.info(s"Writing subscribers data for year=$year, month=$month to path=$outputPath")
      writeCsvOverwrite(monthData, config.outputPartitions, outputPath)
      println(s"Written subscribers data for year=$year, month=$month to $outputPath")
    }
  }
}