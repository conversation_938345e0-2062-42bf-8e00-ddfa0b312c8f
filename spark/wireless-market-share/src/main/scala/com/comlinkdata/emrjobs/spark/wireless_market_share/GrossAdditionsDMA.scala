package com.comlinkdata.emrjobs.spark.wireless_market_share

import org.apache.spark.sql.{Dataset, SparkSession}
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share.{IndustryModel, PortedCarrierWins, SubscribersOutput}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import com.comlinkdata.largescale.commons.RichDate._
import java.time.temporal.IsoFields.QUARTER_OF_YEAR
import java.time.{LocalDate, YearMonth}
import java.sql.Date

/** The class used to calculate the gross additions based on ported wins, previous quarter subscribers and
  * National model data */
class GrossAdditionsDMA(implicit spark: SparkSession)  {

  import spark.implicits._

  def calculateGrossAdditions(
    processingDate: LocalDate,
    industryModel: Dataset[IndustryModel],
    portedCarrierWins: Dataset[PortedCarrierWins],
    subscribers: Dataset[SubscribersOutput]
  ): Dataset[EstimatedGrossAddsDMA] = {
    // 1. GA_rate_avg
    val monthlyNationalAverageGrossAddRate = computeMonthlyNationalAverageGrossAddRate(processingDate, industryModel, portedCarrierWins)

    // 2. GA_flat
    val flatGrossAdds = computeFlatGrossAdds(processingDate, subscribers, monthlyNationalAverageGrossAddRate)

    // 3. GA_flat_share
    val flatGrossAddsGeoShare = computeFlatGrossAverageFlatShare(flatGrossAdds)

    // 4. W_share
    val portedCarrierWinGeoShare = computePortedCarrierWinGeoShare(portedCarrierWins)

    // 7. GA
    computeEstimatedGrossAdds(flatGrossAddsGeoShare,portedCarrierWinGeoShare, flatGrossAdds)
  }

  //GA_rate_avg[b,p,m] = GA[b,p,m] / S[b,p,m]
  // specific: GA_rate_avg[b,p,m] = (current quarter GA / previous quarter Subs) * (current month wins) / (current quarter wins)
  def computeMonthlyNationalAverageGrossAddRate(
    processingDate: LocalDate,
    industryModel: Dataset[IndustryModel],
    portedCarrierWins: Dataset[PortedCarrierWins]
  ): Dataset[GrossAddsRateAverage] =  {

    // Processing month date values
    val quarter = processingDate.getQuarter
    val year = processingDate.getYear
    val prevQuarter = if (quarter == 1) 4 else quarter-1
    val prevYear = if (quarter == 1) year-1 else year
    val startDate = YearMonth.of(year, (quarter-1) * 3 + 1).`with`(QUARTER_OF_YEAR, quarter).atDay(1)
    val endDate = startDate.plusMonths(2)

    // Current Date values
    val currentDate = LocalDate.now().withDayOfMonth(1)
//    val currentDate = LocalDate.of(2023, 9, 1)
    val currentQuarter = currentDate.getQuarter
    val currentYear = currentDate.getYear
    val startDateCurrentQuarter = YearMonth.of(currentYear, (currentQuarter-1) * 3 + 1).`with`(QUARTER_OF_YEAR, currentQuarter).atDay(1)
    val endDateCurrentQuarter = startDateCurrentQuarter.plusMonths(2)


    // current quarter gross adds / prev quarter subs
    val industryModelPrev = industryModel
      .select($"brand", $"plan_type", $"year", $"quarter", $"gross_additions")
      .where($"quarter" === quarter && $"year" === year)
      .join(
        industryModel
          .where($"quarter" === prevQuarter && $"year" === prevYear)
          .select($"brand", $"plan_type", $"year", $"quarter", $"subscribers"),
        Seq("brand", "plan_type"),
        "LEFT"
      )
      .withColumn("gross_rate_average_industry_model", $"gross_additions" / $"subscribers")

    val portedCarrierWinsQuarter = portedCarrierWins
      .withColumn("quarter", date_format($"date_trunc", "q"))
      .select(
        $"winner" as "brand",
        $"primary_plan_type_id" as "plan_type",
        $"date_trunc" as "customer_base_date",
        $"quarter",
        $"dma",
        $"switches"
      )
      .where(date_format($"customer_base_date", "yyyy").cast(IntegerType) === year)
      .groupBy($"brand", $"plan_type", $"customer_base_date", $"quarter")
      .agg(sum($"switches") as "switches_no_dma")

    // copy of portedCarrierWins to update them if processing date is the 2nd month of the quarter
    var portedCarrierWinsQuarterUpdated = portedCarrierWinsQuarter

    // If it's second month of the quarter, add third month wins by taking avg of first and second month of that quarter
    if(processingDate.equals(startDate.plusMonths(1)) && currentDate.equals(processingDate.plusMonths(1))) {
      println(s"Adding third month: $endDate subs taking average of 1st and 2nd month subs")
      portedCarrierWinsQuarterUpdated = portedCarrierWinsQuarter
        .union(
          portedCarrierWinsQuarter
            .groupBy($"brand", $"plan_type", $"quarter")
            .agg(avg($"switches_no_dma") as "switches_avg")
            .withColumn("customer_base_date", lit(Date.valueOf(processingDate.plusMonths(1))))
            .select(
              $"brand",
              $"plan_type",
              $"customer_base_date",
              $"quarter",
              $"switches_avg" as "switches_no_dma"
            )
        )
    }
    else {
      println(s"We can't/don't need to add 3rd months subs because,\n1) The processing month = $processingDate should be the second month of the quarter. \n2) The current date = $currentDate should exactly the 3rd month of the in progress quarter\n")
    }

    val portedCarrierWinsAgg = portedCarrierWinsQuarterUpdated
      .groupBy($"brand", $"plan_type", $"quarter")
      .agg(sum($"switches_no_dma") as "switches_quarter")

    val portedCarrierWinsProcessed = portedCarrierWinsQuarterUpdated
      .join(portedCarrierWinsAgg,Seq("brand","plan_type","quarter"),"LEFT")
      .where($"customer_base_date" === processingDate)

    // GA average rates calculation based on the month in the quarter
    var averageGrossAddRate = spark.emptyDataset[GrossAddsRateAverage]

    // Completed quarter: E.g. If we are in Apr 24 that means 1st quarter of 2024 or all quarters before Apr-24 are completed
    // Provided that we have nationally reported numbers ready by Apr 24
    if(processingDate.isBefore(currentDate) && startDateCurrentQuarter.isAfter(endDate)) {
      // Quarter - After it has completed and carriers reported
      println(s"We are processing subs for the completed quarter for the date = $processingDate...")
      averageGrossAddRate = industryModelPrev
        .join(portedCarrierWinsProcessed,Seq("brand","plan_type","quarter"),"LEFT")
        .withColumn("gross_rate_average",$"gross_rate_average_industry_model" * $"switches_no_dma" / $"switches_quarter")
        .select($"brand",$"plan_type",$"customer_base_date", $"gross_rate_average" as "gross_rate_average")
        .as[GrossAddsRateAverage]
    }
    // In-progress quarter
    else if(processingDate.isBefore(currentDate) && currentQuarter == quarter) {  // now it could be 1st or 2nd month of the quarter
      // Check if processing month is the 1st or 2nd month of the quarter
      if(processingDate.equals(startDate)) {   // If it's 1st month of the quarter
        println(s"We are processing subs for the In-Progress quarter for the first month date = $processingDate...")
        averageGrossAddRate = industryModelPrev
          .join(portedCarrierWinsProcessed,Seq("brand","plan_type","quarter"),"LEFT")
          .withColumn("gross_rate_average",$"gross_rate_average_industry_model" * $"switches_no_dma" / $"switches_quarter" / 3)
          .select($"brand",$"plan_type",$"customer_base_date", $"gross_rate_average" as "gross_rate_average")
          .as[GrossAddsRateAverage]
      }
      else if (processingDate.equals(startDate.plusMonths(1))){  // if it's second month of the quarter
        println(s"We are processing subs for the In-Progress quarter for the second month date = $processingDate...")
        averageGrossAddRate = industryModelPrev
          .join(portedCarrierWinsProcessed,Seq("brand","plan_type","quarter"),"LEFT")
          .withColumn("gross_rate_average",$"gross_rate_average_industry_model" * $"switches_no_dma" / $"switches_quarter")
          .select($"brand",$"plan_type",$"customer_base_date", $"gross_rate_average" as "gross_rate_average")
          .as[GrossAddsRateAverage]
      }
      else {
        println("ERROR: Invalid processing date - we can not calculate the subs for the current month (3rd month of the quarter) with in-progress quarter process")
      }
    }
    else {
      println("ERROR: Invalid processing date - we can not calculate the subs for the current or future months")
    }

    averageGrossAddRate
  }

  //GA_flat[b,p,m,g] = S[b,p,m,g]*GA_rate_avg[b,p,m]
  def computeFlatGrossAdds(
    processingDate: LocalDate,
    subscribers: Dataset[SubscribersOutput],
    monthlyNationalAverageGrossAddRate: Dataset[GrossAddsRateAverage]
  ): Dataset[GrossAddsFlatDMA] = {

    subscribers.alias("a").join(
      monthlyNationalAverageGrossAddRate.alias("b"), Seq("brand","plan_type"), "LEFT"
    ).select(
      $"a.brand",
      $"a.plan_type",
      $"b.customer_base_date",
      $"a.dma",
      $"a.subs" * $"b.gross_rate_average" as "gross_adds_flat"
    ).as[GrossAddsFlatDMA]
  }

  //GA_flat_share[b,p,m,g] = GA_flat[b,p,m,g] / sum{over b,p,m}(GA_flat[b,p,m,g] )
  def computeFlatGrossAverageFlatShare(
    flatGrossAdds: Dataset[GrossAddsFlatDMA]
  ): Dataset[GrossAddsAverageFlatShareDMA] = {
    val left = flatGrossAdds
      .groupBy($"brand", $"plan_type", $"customer_base_date",$"dma")
      .agg(sum($"gross_adds_flat") as "a_gross_adds_flat")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"dma",
        $"a_gross_adds_flat"
      )
    val right = flatGrossAdds
      .groupBy($"brand",$"plan_type",$"customer_base_date")
      .agg(sum($"gross_adds_flat") as "b_gross_adds_flat")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"b_gross_adds_flat"
      )
    left
      .join(right, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"dma",
        $"a_gross_adds_flat" / $"b_gross_adds_flat" as "gross_average_flat_share"
      )
      .as[GrossAddsAverageFlatShareDMA]
  }

  //W_share[b,p,m,g] = W[b,p,g,m]/sum{over b,p,m}(W[b,p,g,m])
  def computePortedCarrierWinGeoShare(
    portedCarrierWins: Dataset[PortedCarrierWins]
  ): Dataset[PortedGeographicWinShareDMA] = {
    val portedCarrierWinsRenamed = portedCarrierWins
      .select(
        $"winner" as "brand",
        $"primary_plan_type_id" as "plan_type",
        $"date_trunc" as "customer_base_date",
        $"dma",
        $"switches"
      )
    val left = portedCarrierWinsRenamed
      .groupBy($"brand", $"plan_type", $"customer_base_date", $"dma")
      .agg(sum($"switches") as "a_switches")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"dma",
        $"a_switches"
      )
    val right = portedCarrierWinsRenamed
      .groupBy($"brand", $"plan_type", $"customer_base_date")
      .agg(sum($"switches") as "b_switches")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"b_switches"
      )
    left
      .join(right, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"dma",
        $"a_switches" / $"b_switches" as "ported_win_shares"
      )
      .as[PortedGeographicWinShareDMA]
  }

  def computeEstimatedGrossAdds(
    flatGrossAddsGeoShare: Dataset[GrossAddsAverageFlatShareDMA],
    portedCarrierWinGeoShare: Dataset[PortedGeographicWinShareDMA],
    flatGrossAddsData: Dataset[GrossAddsFlatDMA]
  ): Dataset[EstimatedGrossAddsDMA] = {

    //GA[b,p,m,g] = GA_flat_share[b,p,m,g]*GA_share[b,p,m,g]
    val A =
    flatGrossAddsGeoShare
      .join(portedCarrierWinGeoShare, Seq("brand", "plan_type", "customer_base_date", "dma"), "LEFT")
      .na.fill(0,Array("ported_win_shares"))
      .withColumn("ga_share_variance", lit(0.5) * ($"ported_win_shares" - $"gross_average_flat_share"))
      //GA_share[b,p,m,g] = GA_flat_share[b,p,m,g] + GA_share_variance[b,p,m,g]
      .withColumn("estimated_gross_add_geo_share", $"gross_average_flat_share" + $"ga_share_variance")
      .select($"brand", $"plan_type", $"customer_base_date", $"dma", $"ga_share_variance", $"estimated_gross_add_geo_share")

    //println("flat gross join ported carrier output length: " + A.count())

    flatGrossAddsData
      .groupBy($"brand",$"plan_type",$"customer_base_date")
      .agg(sum($"gross_adds_flat") as "gross_adds_flat_no_geo")
      .join(A, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
      .withColumn("estimated_gross_adds", $"estimated_gross_add_geo_share" * $"gross_adds_flat_no_geo")
      .select($"brand", $"plan_type", $"customer_base_date", $"dma", $"estimated_gross_adds")
      .na.fill(0)
      .as[EstimatedGrossAddsDMA]
  }
}
object GrossAdditionsDMA {
  def apply()(implicit spark: SparkSession): GrossAdditionsDMA = new GrossAdditionsDMA()

}
