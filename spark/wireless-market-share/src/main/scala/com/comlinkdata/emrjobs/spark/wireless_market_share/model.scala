package com.comlinkdata.emrjobs.spark.wireless_market_share

import java.sql.Date

/** The object has all the common models used in subscribers and demographics process */
object model {

  // Step 1 models
  case class NationalSubscriberMultipliersIM(
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    industry_model_subscribers: Double
  )

  case class NationalLossMultipliersIM(
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    industry_model_losses: Double
  )

  case class NationalLossMultipliersDisconnectsIM(
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    total_industry_model_losses: Double,
    ported_industry_model_losses: Double,
    non_ported_industry_model_losses: Double,
    non_ported_switching_industry_model_losses: Double,
    disconnects: Double
  )

  case class NationalAddMultipliersIM(
    customer_base_date: Date,
    previous_holder_sp: Int,
    previous_holder_plan_type_id: Int,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    industry_model_adds: Double
  )


  case class NationalAddMultipliersActivationsIM(
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    total_industry_model_adds: Double,
    ported_industry_model_adds: Double,
    non_ported_industry_model_adds: Double,
    non_ported_switching_industry_model_adds: Double,
    activations: Double,
    activations_multiplier: Double
  )

  case class NationalAddMultipliersIntraMnoIM(
    customer_base_date: Date,
    previous_holder_sp: Int,
    previous_holder_plan_type_id: Int,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    industry_model_adds: Double
  )


  // Step 2 models
  case class XMSMTiebreaker(
    zip_rc_kblock: String,
    current_holder_mvno_sp: Int
  )

  case class NationalSubscriberMultipliersPortedCustomerBase(
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    ported_tn_subscribers: Double,
    losses: Double
  )

  case class NationalAddMultipliersPortedCustomerBase(
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    previous_holder_sp: Int,
    previous_holder_plan_type_id: Int,
    adds: Double
  )

  case class NationalAddMultipliersIntraMnoInputs(
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    previous_holder_sp: Int,
    previous_holder_plan_type_id: Int,
    adds: Double
  )

  case class WirelessMovement(
    zip_cd: String,
    primary_sp: Int,
    secondary_sp: Int,
    primary_plan_type_id: Int,
    secondary_plan_type_id: Int,
    adjusted_wins: Double,
    month: Date
  )

  // Step 3 Models
  case class NationalSubscriberMultipliers(
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    ported_tn_subscribers: Double,
    industry_model_subscribers: Double,
    total_subs_to_ported_subs_ratio: Double
  )

  case class NationalLossMultipliers(
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    ported_tn_losses: Double,
    industry_model_losses: Double,
    total_losses_to_ported_losses_ratio: Double
  )

  case class NationalAddMultipliers(
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    previous_holder_sp: Int,
    previous_holder_plan_type_id: Int,
    ported_tn_adds: Double,
    industry_model_adds: Double,
    total_adds_to_ported_adds_ratio: Double
  )

  case class NationalAddMultipliersIntraMno(
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    previous_holder_sp: Int,
    previous_holder_plan_type_id: Int,
    ported_tn_adds: Double,
    industry_model_adds: Double,
    total_adds_to_ported_adds_ratio: Double
  )


  case class MarketshareOutput(
    year: Int,
    month: Int,
    cld_observed_loser: String,
    cld_observed_winner: String,
    quarter: Int,
    begin_segment: String,
    begin_mno: String,
    begin_mvno: String,
    end_segment: String,
    end_mno: String,
    end_mvno: String,
    diagonal: Int,
    migration: Int,
    port_est: Float,
    est: Float,
    winning_losing_prop: Float,
    winning_prop: Float,
    losing_prop: Float,
    top_prop: Float,
    prop: Float,
    churn_losing_mno_sp: Int,
    churn_winning_mno_sp: Int,
    churn_losing_mvno_sp: Int,
    churn_winning_mvno_sp: Int,
    churn_losing_plan_type_id: Int,
    churn_winning_plan_type_id: Int
  )


  // step 4
  case class distinctSPDataTotalMktShare(
    churn_losing_mvno_sp: Int,
    churn_winning_mvno_sp: Int
  )

  case class RateCenterNPAPortedCustomerBase(
    customer_base_date: Date,
    zip_rc_kblock: String,
    npa: String,
    npa_complex_cld: String,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    ported_tn_subscribers: Double,
    ported_tn_losses: Double
  )

  case class RateCenterNPAAddsPortedCustomerBase(
    customer_base_date: Date,
    zip_rc_kblock: String,
    npa: String,
    npa_complex_cld: String,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    previous_holder_sp: Int,
    previous_holder_plan_type_id: Int,
    ported_tn_adds: Double
  )

  case class RateCenterAddsIntraMNO(
    customer_base_date: Date,
    zip_rc_kblock: String,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    previous_holder_sp: Int,
    previous_holder_plan_type_id: Int,
    ported_tn_adds: Double
  )


  // Step 5 Models: NationalMultipliersRateCenterSpec

  case class RateCenterNPATotalSubs(
    customer_base_date: Date,
    zip_rc_kblock: String,
    npa: String,
    npa_complex_cld: String,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    ported_tn_subscribers: Double,
    ported_tn_losses: Double,
    national_ported_tn_subscribers: Double,
    national_industry_model_subscribers: Double,
    national_total_subs_to_ported_subs_ratio: Double,
    rate_center_industry_model_subscribers: Double,
    national_ported_tn_losses: Double,
    national_industry_model_losses: Double,
    national_total_losses_to_ported_losses_ratio: Double,
    rate_center_industry_model_losses: Double,
    industry_model_sp_ind: Int
  )

  case class RateCenterNPATotalAdds(
    customer_base_date: Date,
    zip_rc_kblock: String,
    npa: String,
    npa_complex_cld: String,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    previous_holder_sp: Int,
    previous_holder_plan_type_id: Int,
    ported_tn_adds: Double,
    national_ported_tn_adds: Double,
    national_industry_model_adds: Double,
    national_total_adds_to_ported_adds_ratio: Double,
    rate_center_industry_model_adds: Double,
    industry_model_sp_ind_current: Int,
    industry_model_sp_ind_previous: Int
  )

  case class RateCenterTotalIntraMNOAdds(
    customer_base_date: Date,
    zip_rc_kblock: String,
    industry_model_sp_ind_current: Int,
    industry_model_sp_ind_previous: Int,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    previous_holder_sp: Int,
    previous_holder_plan_type_id: Int,
    national_ported_tn_adds: Double,
    national_industry_model_adds: Double,
    national_total_adds_to_ported_adds_ratio: Double,
    ported_tn_adds: Double,
    rate_center_industry_model_adds: Double
  )

  // Step 6 Models: RegionalMNOUpscale
  case class NPAComplexTotalSubs(
    npa_complex_cld: String,
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    national_ported_tn_subscribers: Double,
    national_industry_model_subscribers: Double,
    national_total_subs_to_ported_subs_ratio: Double,
    industry_model_sp_ind: Int,
    ported_tn_subscribers: Double,
    ported_tn_losses: Double,
    npa_complex_industry_model_subscribers: Double,
    npa_complex_industry_model_losses: Double,
  )

  case class NPAComplexTotalAdds(
    npa_complex_cld: String,
    customer_base_date: Date,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    previous_holder_sp: Int,
    previous_holder_plan_type_id: Int,
    national_ported_tn_adds: Double,
    national_industry_model_adds: Double,
    national_total_adds_to_ported_adds_ratio: Double,
    industry_model_sp_ind_current: Int,
    industry_model_sp_ind_previous: Int,
    ported_tn_adds: Double,
    npa_complex_industry_model_adds: Double
  )

  case class NPAComplexPlanTypeMultipliers(
    npa_complex_cld: String,
    customer_base_date: Date,
    current_holder_plan_type_id: Int,
    ported_tn_subscribers: Double,
    npa_complex_industry_model_subscribers: Double,
    ported_to_total_subs_multiplier: Double,
    ported_tn_losses: Double,
    npa_complex_industry_model_losses: Double,
    ported_to_total_loss_multiplier: Double
  )

  case class NPAComplexPlanTypeAddMultipliers(
    npa_complex_cld: String,
    customer_base_date: Date,
    current_holder_plan_type_id: Int,
    previous_holder_plan_type_id: Int,
    ported_tn_adds: Double,
    npa_complex_industry_model_adds: Double,
    ported_to_total_adds_multiplier: Double
  )

  case class RateCenterNPATotalSubsWithRegionalMNOs(
    customer_base_date: Date,
    zip_rc_kblock: String,
    npa: String,
    npa_complex_cld: String,
    industry_model_sp_ind: Int,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    national_ported_tn_subscribers: Double,
    national_industry_model_subscribers: Double,
    national_total_subs_to_ported_subs_ratio: Double,
    national_ported_tn_losses: Double,
    national_industry_model_losses: Double,
    national_total_losses_to_ported_losses_ratio: Double,
    ported_tn_subscribers: Double,
    ported_tn_losses: Double,
    rate_center_npa_industry_model_subscribers: Double,
    rate_center_npa_industry_model_losses: Double
  )

  case class RateCenterNPATotalAddsWithRegionalMNOs(
    customer_base_date: Date,
    zip_rc_kblock: String,
    npa: String,
    npa_complex_cld: String,
    industry_model_sp_ind_current: Int,
    industry_model_sp_ind_previous: Int,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    previous_holder_sp: Int,
    previous_holder_plan_type_id: Int,
    national_ported_tn_adds: Double,
    national_industry_model_adds: Double,
    national_total_adds_to_ported_adds_ratio: Double,
    ported_tn_adds: Double,
    rate_center_npa_industry_model_adds: Double
  )

  // step 7 Final Output
  case class FinalOutput(
    zip_rc_kblock: String,
    current_holder_sp: Int,
    current_holder_plan_type_id: Int,
    industry_model_sp_ind: Int,
    starting_customers: Double,
    ported_starting_customers: Double,
    losses_excl_intra_mno: Double,
    ported_losses_excl_intra_mno: Double,
    intra_mno_losses: Double,
    ported_intra_mno_losses: Double,
    total_losses: Double,
    ported_total_losses: Double,
    wins_excl_intra_mno: Double,
    ported_wins_excl_intra_mno: Double,
    intra_mno_wins: Double,
    ported_intra_mno_wins: Double,
    wins_excl_activations: Double,
    ported_wins_excl_activations: Double,
    activations_multiplier: Double,
    activations: Double,
    total_wins: Double,
    ending_customers: Double,
    ported_ending_customers: Double,
    customer_base_date: Date
  )

  case class geoShareIB(
    current_holder: String,
    customer_base_date: Date,
    mvno_winning_plan_type_id: Int,
    cma: Int,
    dma: Int,
    share: Double
  )

  case class geoShareStoreLoc(
    carrier: String,
    cma: Int,
    dma: Int,
    share: Double
  )

  // DMA
  case class PortedCarrierWinsQuarterDMA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    switches: Double,
    quarter: Int
  )

  case class GrossAddsRateAverage(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    gross_rate_average: Double
  )

  case class GrossAddsFlatDMA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    gross_adds_flat: Double
  )

  case class GrossAddsAverageFlatShareDMA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    gross_average_flat_share: Double
  )

  case class PortedGeographicWinShareDMA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    ported_win_shares: Double
  )

  case class EstimatedGrossAddsDMA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    estimated_gross_adds: Double
  )

  case class EndOfPeriodDMA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    end_of_period_subscribers: Double
  )

  case class PortedCarrierLossesDMA(
    date_trunc: Date,
    loser: String,
    primary_plan_type_id: String,
    secondary_plan_type_id: String,
    dma: Int,
    switches: Double
  )

  case class PortedCarrierLossesQuarterDMA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    switches: Double,
    quarter: Int
  )

  case class GrossLossesRateAverage(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    gross_rate_average: Double,
  )

  case class GrossLossesFlatDMA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    gross_losses_flat: Double
  )

  case class GrossLossesAverageFlatShareDMA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    gross_average_flat_share: Double
  )

  case class PortedGeographicLossShareDMA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    ported_loss_shares: Double
  )

  case class EstimatedGrossLossesDMA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    estimated_gross_losses: Double
  )

  // for tests
  case class ExpectedGrossAddsLossesDMA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    actual_gross_adds: Double,
    actual_gross_losses: Double
  )


  case class PortedCarrierLossesByQuarter(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    quarter: Int,
    dma: Int,
    switches: Double
  )

  case class MonthlyLossesByGeos(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    quarter: Int,
    switches_per_geo: Double
  )

  case class PortedCarrierLossesAggByQuarter(
    brand: String,
    plan_type: String,
    quarter: Int,
    switches_per_quarter: Double
  )

  case class PortedCarrierMonthlyLossesGeoShare(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    quarter: Int,
    dma: Int,
    monthly_loss_share: Double
  )

  // VPGM

  case class PortedCarrierWinsVPGM(
    date_trunc: Date,
    winner: String,
    primary_plan_type_id: String,
    secondary_plan_type_id: String,
    vpgm: String,
    switches: Double
  )

  case class PortedCarrierLossesVPGM(
    date_trunc: Date,
    loser: String,
    primary_plan_type_id: String,
    secondary_plan_type_id: String,
    vpgm: String,
    switches: Double
  )

  case class PortedCarrierWinsQuarterVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    switches: Double,
    quarter: Int
  )


  case class GrossAddsFlatVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    gross_adds_flat: Double
  )

  case class GrossAddsAverageFlatShareVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    gross_average_flat_share: Double
  )

  case class PortedGeographicWinShareVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    ported_win_shares: Double
  )

  case class EstimatedGrossAddsVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    estimated_gross_adds: Double
  )

  case class PortedCarrierLossesQuarterVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    switches: Double,
    quarter: Int
  )

  case class GrossLossesFlatVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    gross_losses_flat: Double
  )

  case class GrossLossesAverageFlatShareVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    gross_average_flat_share: Double
  )

  case class PortedGeographicLossShareVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    ported_loss_shares: Double
  )

  case class EstimatedGrossLossesVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    estimated_gross_losses: Double
  )

  // for tests
  case class ExpectedGrossAddsLossesVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    actual_gross_adds: Double,
    actual_gross_losses: Double
  )

  case class EndOfPeriodVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    end_of_period_subscribers: Double
  )

  case class EstimatedMonthlyBA(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    dma: Int,
    estimated_ba: Double
  )

  // BA VPGM

  case class EstimatedMonthlyBAVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    estimated_ba: Double
  )

  case class PortedCarrierLossesByQuarterVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    quarter: Int,
    vpgm: String,
    switches: Double
  )

  case class MonthlyLossesByGeosVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    vpgm: String,
    quarter: Int,
    switches_per_geo: Double
  )

  case class PortedCarrierLossesAggByQuarterVPGM(
    brand: String,
    plan_type: String,
    quarter: Int,
    switches_per_quarter: Double
  )

  case class PortedCarrierMonthlyLossesGeoShareVPGM(
    brand: String,
    plan_type: String,
    customer_base_date: Date,
    quarter: Int,
    vpgm: String,
    monthly_loss_share: Double
  )

  case class finalDatasetWithAllMetricsDMA(
    customer_base_date: Date,
    geography: String,
    geography_type: String,
    brand: String,
    plan_type: String,
    bpt: String,
    dma: Int,
    ga: Double,
    gl: Double,
    ba: Double,
    subs: Double
  )

  case class finalDatasetWithAllMetricsVPGM(
    customer_base_date: Date,
    geography: String,
    geography_type: String,
    brand: String,
    plan_type: String,
    bpt: String,
    vpgm: String,
    ga: Double,
    gl: Double,
    ba: Double,
    subs: Double
  )

  // ethnicity DMA
  // holy crap that's a lotta sub tables lmao

  case class demoVarianceInput(
    date: Date,
    winner_brand_plantype: String,
    ethnicity: String,
    age: String,
    income: String,
    wins: Double
  )


  // combined with demoVarianceFactor
  case class demoVarianceFactorCompressed(
    date: Date,
    winner_brand_plantype: String,
    ethnicity: String,
    age: String,
    income: String,
    wins_numerator: Double,
    wins_denominator: Double,
    wins_prop: Double,
    long_term_wins_numerator: Double,
    long_term_wins_denominator: Double,
    long_term_wins_prop: Double,
    variance_factor: Double,
    variance_factor_compressed: Double
  )


  case class subsEthnicityPercentOfCarrier(
    date: Date,
    dma: Int,
    winner_brand_plantype: String,
    ethnicity: String,
    age: String,
    income: String,
    subscribers: Double,
    total_subscribers: Double,
    ethnicity_pct_of_carrier: Double
  )

  case class totalLosses(
    date: Date,
    dma: Int,
    loser_brand_plantype: String,
    losses: Double
  )

  case class totalWins(
    date: Date,
    dma: Int,
    winner_brand_plantype: String,
    wins: Double
  )

  case class lossesEthnicityPercentOfCarrier(
    date: Date,
    dma: Int,
    ethnicity: String,
    age: String,
    income: String,
    loser_brand_plantype: String,
    losses_total: Double,
    loser_ethnicity_pct_of_carrier: Double,
    losses_by_ethnicity: Double
  )

  case class winsEthnicityPercentOfCarrier(
    date: Date,
    dma: Int,
    ethnicity: String,
    age: String,
    income: String,
    winner_brand_plantype: String,
    wins_total: Double,
    winner_ethnicity_pct_of_carrier: Double,
    wins_by_ethnicity: Double
  )

  case class winnerPercentOfLoser(
    date: Date,
    dma: Int,
    loser_brand_plantype: String,
    winner_brand_plantype: String,
    wins: Double,
    total_losses: Double,
    winner_pct_of_loser: Double
  )

  case class loserPercentOfWinner(
    date: Date,
    dma: Int,
    loser_brand_plantype: String,
    winner_brand_plantype: String,
    wins: Double,
    total_wins: Double,
    loser_pct_of_winner: Double
  )

  case class winsLossesByEthnicityInterim1(
    date: Date,
    dma: Int,
    loser_brand_plantype: String,
    winner_brand_plantype: String,
    ethnicity: String,
    age: String,
    income: String,
    wins: Double,
    total_losses: Double,
    total_wins: Double,
    winner_pct_of_loser: Double,
    loser_ethnicity_pct_of_carrier: Double,
    losses_by_ethnicity: Double,
    wins_by_ethnicity_calc: Double,
    winner_ethnicity_pct_of_carrier: Double,
    wins_by_ethnicity: Double,
    losses_by_ethnicity_recalc: Double,
    w_l_avg_ethnicity_pct_of_carrier: Double,
    loser_avg_churn_volume_by_ethnicity: Double,
    winner_avg_churn_volume_by_ethnicity: Double
  )

  case class winsLossesByEthnicityInterim2(
    date: Date,
    dma: Int,
    loser_brand_plantype: String,
    winner_brand_plantype: String,
    ethnicity: String,
    age: String,
    income: String,
    wins: Double,
    total_losses: Double,
    total_wins: Double,
    winner_pct_of_loser: Double,
    loser_ethnicity_pct_of_carrier_original: Double,
    losses_by_ethnicity: Double,
    winner_ethnicity_pct_of_carrier_original: Double,
    wins_by_ethnicity: Double,
    loser_avg_churn_volume_by_ethnicity_original: Double,
    winner_avg_churn_volume_by_ethnicity_original: Double,
    variance_factor_compressed_not_null_check: Double,
    variance_factor_compressed: Double
  )

  // combine with winsLossesByEthnicityInterim
  case class winsLossesByEthnicity(
    date: Date,
    dma: Int,
    loser_brand_plantype: String,
    winner_brand_plantype: String,
    ethnicity: String,
    age: String,
    income: String,
    wins: Double,
    total_losses: Double,
    total_wins: Double,
    winner_pct_of_loser: Double,
    loser_ethnicity_pct_of_carrier_original: Double,
    losses_by_ethnicity: Double,
    winner_ethnicity_pct_of_carrier_original: Double,
    wins_by_ethnicity: Double,
    loser_avg_churn_volume_by_ethnicity_original: Double,
    winner_avg_churn_volume_by_ethnicity_original: Double,
    variance_factor_compressed: Double,
    loser_ethnicity_pct_of_carrier_numerator: Double,
    winner_ethnicity_pct_of_carrier_numerator: Double,
    loser_ethnicity_pct_of_carrier_denominator: Double,
    winner_ethnicity_pct_of_carrier_denominator: Double,
    loser_ethnicity_pct_of_carrier: Double,
    winner_ethnicity_pct_of_carrier: Double,
    loser_avg_churn_volume_by_ethnicity: Double,
    winner_avg_churn_volume_by_ethnicity: Double,
  )

  case class winsFromAverageEthnicityPercentOfCarrier(
    date: Date,
    dma: Int,
    ethnicity: String,
    age: String,
    income: String,
    winner_brand_plantype: String,
    wins_total: Double,
    wins_from_average_by_ethnicity: Double,
    winner_from_average_ethnicity_pct_of_carrier: Double,
  )

  case class lossesFromAverageEthnicityPercentOfCarrier(
    date: Date,
    dma: Int,
    ethnicity: String,
    age: String,
    income: String,
    loser_brand_plantype: String,
    losses_total: Double,
    losses_from_average_by_ethnicity: Double,
    loser_from_average_ethnicity_pct_of_carrier: Double,
  )

  case class wmsWithEthnicityFromAverageBackwards(
    wms_month: Date,
    subs_join_month: Date,
    losses_join_month: Date,
    wins_join_month: Date,
    dma: Int,
    dma_name: String,
    brand_plantype: String,
    total_subscribers: Double,
    total_gross_adds: Double,
    total_gross_losses: Double,
    total_base_adjustment: Double,
    total_last_period_subs: Double,
    total_next_period_subs: Double,
    ethnicity: String,
    age: String,
    income: String,
    subs_by_ethnicity_check: Double,
    subs_ethnicity_pct_of_carrier: Double,
    ending_subscribers: Double,
    loser_ethnicity_pct_of_carrier: Double,
    gross_losses: Double,
    winner_from_average_ethnicity_pct_of_carrier: Double,
    gross_adds: Double,
    base_adjustment: Double,
  )

  case class wmsWithEthnicityWithStartingSubsFromAverageBackwards(
    wms_month: Date,
    subs_join_month: Date,
    losses_join_month: Date,
    wins_join_month: Date,
    dma: Int,
    dma_name: String,
    brand_plantype: String,
    total_subscribers: Double,
    total_gross_adds: Double,
    total_gross_losses: Double,
    total_base_adjustment: Double,
    total_last_period_subs: Double,
    total_next_period_subs: Double,
    ethnicity: String,
    age: String,
    income: String,
    subs_by_ethnicity_check: Double,
    subs_ethnicity_pct_of_carrier: Double,
    ending_subscribers: Double,
    loser_ethnicity_pct_of_carrier: Double,
    gross_losses: Double,
    winner_from_average_ethnicity_pct_of_carrier: Double,
    gross_adds: Double,
    base_adjustment: Double,
    starting_subscribers: Double,
    gross_add_rate: Double,
    churn_rate: Double,
    overall_gross_add_rate: Double,
    overall_churn_rate: Double
  )

  case class rateCenterWinnerLoserTemp(
    zip_cd: String,
    primary_sp: Int,
    secondary_sp: Int,
    primary_plan_type_id: Int,
    secondary_plan_type_id: Int,
    adjusted_wins: Double,
    date: Date,
    winner: String,
    loser: String
  )

  // ethnicity VPGM

  case class subsEthnicityPercentOfCarrierVPGM(
    date: Date,
    vpgm: String,
    winner_brand_plantype: String,
    ethnicity: String,
    age: String,
    income: String,
    subscribers: Double,
    total_subscribers: Double,
    ethnicity_pct_of_carrier: Double
  )

  case class totalLossesVPGM(
    date: Date,
    vpgm: String,
    loser_brand_plantype: String,
    losses: Double
  )

  case class totalWinsVPGM(
    date: Date,
    vpgm: String,
    winner_brand_plantype: String,
    wins: Double
  )

  case class lossesEthnicityPercentOfCarrierVPGM(
    date: Date,
    vpgm: String,
    ethnicity: String,
    age: String,
    income: String,
    loser_brand_plantype: String,
    losses_total: Double,
    loser_ethnicity_pct_of_carrier: Double,
    losses_by_ethnicity: Double
  )

  case class winsEthnicityPercentOfCarrierVPGM(
    date: Date,
    vpgm: String,
    ethnicity: String,
    age: String,
    income: String,
    winner_brand_plantype: String,
    wins_total: Double,
    winner_ethnicity_pct_of_carrier: Double,
    wins_by_ethnicity: Double
  )

  case class winnerPercentOfLoserVPGM(
    date: Date,
    vpgm: String,
    loser_brand_plantype: String,
    winner_brand_plantype: String,
    wins: Double,
    total_losses: Double,
    winner_pct_of_loser: Double
  )

  case class loserPercentOfWinnerVPGM(
    date: Date,
    vpgm: String,
    loser_brand_plantype: String,
    winner_brand_plantype: String,
    wins: Double,
    total_wins: Double,
    loser_pct_of_winner: Double
  )

  case class winsLossesByEthnicityInterim1VPGM(
    date: Date,
    vpgm: String,
    loser_brand_plantype: String,
    winner_brand_plantype: String,
    ethnicity: String,
    age: String,
    income: String,
    wins: Double,
    total_losses: Double,
    total_wins: Double,
    winner_pct_of_loser: Double,
    loser_ethnicity_pct_of_carrier: Double,
    losses_by_ethnicity: Double,
    wins_by_ethnicity_calc: Double,
    winner_ethnicity_pct_of_carrier: Double,
    wins_by_ethnicity: Double,
    losses_by_ethnicity_recalc: Double,
    w_l_avg_ethnicity_pct_of_carrier: Double,
    loser_avg_churn_volume_by_ethnicity: Double,
    winner_avg_churn_volume_by_ethnicity: Double
  )

  case class winsLossesByEthnicityInterim2VPGM(
    date: Date,
    vpgm: String,
    loser_brand_plantype: String,
    winner_brand_plantype: String,
    ethnicity: String,
    age: String,
    income: String,
    wins: Double,
    total_losses: Double,
    total_wins: Double,
    winner_pct_of_loser: Double,
    loser_ethnicity_pct_of_carrier_original: Double,
    losses_by_ethnicity: Double,
    winner_ethnicity_pct_of_carrier_original: Double,
    wins_by_ethnicity: Double,
    loser_avg_churn_volume_by_ethnicity_original: Double,
    winner_avg_churn_volume_by_ethnicity_original: Double,
    variance_factor_compressed_not_null_check: Double,
    variance_factor_compressed: Double
  )

  // combine with winsLossesByEthnicityInterim
  case class winsLossesByEthnicityVPGM(
    date: Date,
    vpgm: String,
    loser_brand_plantype: String,
    winner_brand_plantype: String,
    ethnicity: String,
    age: String,
    income: String,
    wins: Double,
    total_losses: Double,
    total_wins: Double,
    winner_pct_of_loser: Double,
    loser_ethnicity_pct_of_carrier_original: Double,
    losses_by_ethnicity: Double,
    winner_ethnicity_pct_of_carrier_original: Double,
    wins_by_ethnicity: Double,
    loser_avg_churn_volume_by_ethnicity_original: Double,
    winner_avg_churn_volume_by_ethnicity_original: Double,
    variance_factor_compressed: Double,
    loser_ethnicity_pct_of_carrier_numerator: Double,
    winner_ethnicity_pct_of_carrier_numerator: Double,
    loser_ethnicity_pct_of_carrier_denominator: Double,
    winner_ethnicity_pct_of_carrier_denominator: Double,
    loser_ethnicity_pct_of_carrier: Double,
    winner_ethnicity_pct_of_carrier: Double,
    loser_avg_churn_volume_by_ethnicity: Double,
    winner_avg_churn_volume_by_ethnicity: Double,
  )

  case class winsFromAverageEthnicityPercentOfCarrierVPGM(
    date: Date,
    vpgm: String,
    ethnicity: String,
    age: String,
    income: String,
    winner_brand_plantype: String,
    wins_total: Double,
    wins_from_average_by_ethnicity: Double,
    winner_from_average_ethnicity_pct_of_carrier: Double,
  )

  case class lossesFromAverageEthnicityPercentOfCarrierVPGM(
    date: Date,
    vpgm: String,
    ethnicity: String,
    age: String,
    income: String,
    loser_brand_plantype: String,
    losses_total: Double,
    losses_from_average_by_ethnicity: Double,
    loser_from_average_ethnicity_pct_of_carrier: Double,
  )

  case class wmsWithEthnicityFromAverageBackwardsVPGM(
    wms_month: Date,
    subs_join_month: Date,
    losses_join_month: Date,
    wins_join_month: Date,
    vpgm: String,
    brand_plantype: String,
    total_subscribers: Double,
    total_gross_adds: Double,
    total_gross_losses: Double,
    total_base_adjustment: Double,
    total_last_period_subs: Double,
    total_next_period_subs: Double,
    ethnicity: String,
    age: String,
    income: String,
    subs_by_ethnicity_check: Double,
    subs_ethnicity_pct_of_carrier: Double,
    ending_subscribers: Double,
    loser_ethnicity_pct_of_carrier: Double,
    gross_losses: Double,
    winner_from_average_ethnicity_pct_of_carrier: Double,
    gross_adds: Double,
    base_adjustment: Double,
  )

  case class wmsWithEthnicityWithStartingSubsFromAverageBackwardsVPGM(
    wms_month: Date,
    subs_join_month: Date,
    losses_join_month: Date,
    wins_join_month: Date,
    vpgm: String,
    brand_plantype: String,
    total_subscribers: Double,
    total_gross_adds: Double,
    total_gross_losses: Double,
    total_base_adjustment: Double,
    total_last_period_subs: Double,
    total_next_period_subs: Double,
    ethnicity: String,
    age: String,
    income: String,
    subs_by_ethnicity_check: Double,
    subs_ethnicity_pct_of_carrier: Double,
    ending_subscribers: Double,
    loser_ethnicity_pct_of_carrier: Double,
    gross_losses: Double,
    winner_from_average_ethnicity_pct_of_carrier: Double,
    gross_adds: Double,
    base_adjustment: Double,
    starting_subscribers: Double,
    gross_add_rate: Double,
    churn_rate: Double,
    overall_gross_add_rate: Double,
    overall_churn_rate: Double
  )

  case class wmsWithEthnicityFromAverageForwards(
    wms_month: Date,
    subs_join_month: Date,
    losses_join_month: Date,
    wins_join_month: Date,
    dma: Int,
    dma_name: String,
    brand_plantype: String,
    total_subscribers: Double,
    total_gross_adds: Double,
    total_gross_losses: Double,
    total_base_adjustment: Double,
    total_last_period_subs: Double,
    total_next_period_subs: Double,
    ethnicity: String,
    age: String,
    income: String,
    subs_by_ethnicity_check: Double,
    subs_ethnicity_pct_of_carrier: Double,
    starting_subscribers: Double,
    loser_ethnicity_pct_of_carrier: Double,
    gross_losses: Double,
    winner_from_average_ethnicity_pct_of_carrier: Double,
    gross_adds: Double,
    base_adjustment: Double,
  )

  case class wmsWithEthnicityWithStartingSubsFromAverageForwards(
    wms_month: Date,
    subs_join_month: Date,
    losses_join_month: Date,
    wins_join_month: Date,
    dma: Int,
    dma_name: String,
    brand_plantype: String,
    total_subscribers: Double,
    total_gross_adds: Double,
    total_gross_losses: Double,
    total_base_adjustment: Double,
    total_last_period_subs: Double,
    total_next_period_subs: Double,
    ethnicity: String,
    age: String,
    income: String,
    subs_by_ethnicity_check: Double,
    subs_ethnicity_pct_of_carrier: Double,
    ending_subscribers: Double,
    loser_ethnicity_pct_of_carrier: Double,
    gross_losses: Double,
    winner_from_average_ethnicity_pct_of_carrier: Double,
    gross_adds: Double,
    base_adjustment: Double,
    starting_subscribers: Double,
    gross_add_rate: Double,
    churn_rate: Double,
    overall_gross_add_rate: Double,
    overall_churn_rate: Double
  )

  case class wmsWithEthnicityFromAverageForwardsVPGM(
    wms_month: Date,
    subs_join_month: Date,
    losses_join_month: Date,
    wins_join_month: Date,
    vpgm: String,
    brand_plantype: String,
    total_subscribers: Double,
    total_gross_adds: Double,
    total_gross_losses: Double,
    total_base_adjustment: Double,
    total_last_period_subs: Double,
    total_next_period_subs: Double,
    ethnicity: String,
    age: String,
    income: String,
    subs_by_ethnicity_check: Double,
    subs_ethnicity_pct_of_carrier: Double,
    starting_subscribers: Double,
    loser_ethnicity_pct_of_carrier: Double,
    gross_losses: Double,
    winner_from_average_ethnicity_pct_of_carrier: Double,
    gross_adds: Double,
    base_adjustment: Double,
  )

  case class wmsWithEthnicityWithStartingSubsFromAverageForwardsVPGM(
    wms_month: Date,
    subs_join_month: Date,
    losses_join_month: Date,
    wins_join_month: Date,
    vpgm: String,
    brand_plantype: String,
    total_subscribers: Double,
    total_gross_adds: Double,
    total_gross_losses: Double,
    total_base_adjustment: Double,
    total_last_period_subs: Double,
    total_next_period_subs: Double,
    ethnicity: String,
    age: String,
    income: String,
    subs_by_ethnicity_check: Double,
    subs_ethnicity_pct_of_carrier: Double,
    ending_subscribers: Double,
    loser_ethnicity_pct_of_carrier: Double,
    gross_losses: Double,
    winner_from_average_ethnicity_pct_of_carrier: Double,
    gross_adds: Double,
    base_adjustment: Double,
    starting_subscribers: Double,
    gross_add_rate: Double,
    churn_rate: Double,
    overall_gross_add_rate: Double,
    overall_churn_rate: Double
  )

  // Extracts
  case class PortedCarrierWinsLossesExtractDMA(
    date_trunc: Date,
    winner: String,
    loser: String,
    primary_plan_type_id: Int,
    secondary_plan_type_id: Int,
    dma: Int,
    dma_name: String,
    switches: Double
  )

  case class PortedCarrierWinsLossesExtractVPGM(
    date_trunc: Date,
    winner: String,
    loser: String,
    primary_plan_type_id: Int,
    secondary_plan_type_id: Int,
    custom_region_name: String,
    switches: Double
  )

  case class CaMonthlyPortedWinsAndLossesOutput(
    date_trunc: Date,
    winner: String,
    loser: String,
    primary_plan_type_id: String,
    secondary_plan_type_id: String,
    dma: String,
    dma_name: String,
    switches: Double
  )

  case class DmaFinalOutputAllDemo(
    month: Date,
    geography: String,
    geography_type: String,
    brand: String,
    plan_type: String,
    ethnicity: String,
    age: String,
    income: String,
    subscribers: Int,
    gross_adds: Int,
    gross_losses: Int,
    base_adjustment: Int,
    creation_date: Date
  )

  case class VpgmFromDmaFinalOutputAllDemo(
    month: Date,
    geography: String,
    geography_type: String,
    brand: String,
    plan_type: String,
    ethnicity: String,
    age: String,
    income: String,
    subscribers: Int,
    gross_adds: Int,
    gross_losses: Int,
    base_adjustment: Int,
    creation_date: Date
  )

  case class MsoIlecFootprintsFinalOutputAllDemo(
    month: Date,
    geography: String,
    geography_type: String,
    brand: String,
    plan_type: String,
    ethnicity: String,
    age: String,
    income: String,
    subscribers: Int,
    gross_adds: Int,
    gross_losses: Int,
    base_adjustment: Int,
    creation_date: Date
  )

  case class CmaFinalOutputAllDemo(
    month: Date,
    geography: String,
    geography_type: String,
    brand: String,
    plan_type: String,
    ethnicity: String,
    age: String,
    income: String,
    subscribers: Int,
    gross_adds: Int,
    gross_losses: Int,
    base_adjustment: Int,
    creation_date: Date
  )

  case class DmaFinalOutputPairwiseDemo(
    month: Date,
    geography: String,
    geography_type: String,
    brand: String,
    plan_type: String,
    ethnicity: String,
    age: String,
    income: String,
    subscribers: Long,
    gross_adds: Long,
    gross_losses: Long,
    base_adjustment: Long,
    creation_date: Date
  )

  case class VpgmFromDmaFinalOutputPairwiseDemo(
    month: Date,
    geography: String,
    geography_type: String,
    brand: String,
    plan_type: String,
    ethnicity: String,
    age: String,
    income: String,
    subscribers: Long,
    gross_adds: Long,
    gross_losses: Long,
    base_adjustment: Long,
    creation_date: Date
  )

  case class MsoIlecFootprintsFinalOutputPairwiseDemo(
    month: Date,
    geography: String,
    geography_type: String,
    brand: String,
    plan_type: String,
    ethnicity: String,
    age: String,
    income: String,
    subscribers: Long,
    gross_adds: Long,
    gross_losses: Long,
    base_adjustment: Long,
    creation_date: Date
  )

  case class CmaFinalOutputPairwiseDemo(
    month: Date,
    geography: String,
    geography_type: String,
    brand: String,
    plan_type: String,
    ethnicity: String,
    age: String,
    income: String,
    subscribers: Long,
    gross_adds: Long,
    gross_losses: Long,
    base_adjustment: Long,
    creation_date: Date
  )

  case class BoostMarketOutputAllDemo(
    month: Date,
    geography: String,
    geography_type: String,
    brand: String,
    plan_type: String,
    ethnicity: String,
    age: String,
    income: String,
    subscribers: Long,
    gross_adds: Long,
    gross_losses: Long,
    base_adjustment: Long,
    creation_date: Date
  )

  case class BoostMarketOutputPairwiseDemo(
    month: Date,
    geography: String,
    geography_type: String,
    brand: String,
    plan_type: String,
    ethnicity: String,
    age: String,
    income: String,
    subscribers: Long,
    gross_adds: Long,
    gross_losses: Long,
    base_adjustment: Long,
    creation_date: Date
  )
}
