package com.comlinkdata.emrjobs.spark.wireless_market_share

import org.apache.spark.sql.{Dataset, SparkSession}
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import org.apache.spark.sql.functions.when


class CalculateNationalMultipliers(implicit spark: SparkSession) {

  import spark.implicits._

  /**
    * Calculate National Subscribers Multipliers
    * @param nationalSubscriberMultipliersPortedCBInputs: National Subscribers Multipliers Customer Base Inputs
    * @param nationalSubscriberMultipliersIMInputs: National Subscribers Multipliers Industry Model Inputs
    * @return Returns a dataset of National Subscribers Multipliers data
    */
  def calculateNationalSubscriberMultipliers(
    nationalSubscriberMultipliersPortedCBInputs: Dataset[NationalSubscriberMultipliersPortedCustomerBase],
    nationalSubscriberMultipliersIMInputs: Dataset[NationalSubscriberMultipliersIM]
  ):
  Dataset[NationalSubscriberMultipliers] = {
    nationalSubscriberMultipliersPortedCBInputs.as("a")
      .join(
      nationalSubscriberMultipliersIMInputs.as("b"),
        $"a.customer_base_date" === $"b.customer_base_date" &&
        $"a.current_holder_sp" === $"b.current_holder_sp" &&
        $"a.current_holder_plan_type_id" === $"b.current_holder_plan_type_id",
      "full_outer"
    ).select(
      when($"a.customer_base_date".isNull, $"b.customer_base_date").otherwise($"a.customer_base_date") as "customer_base_date",
      when($"a.current_holder_sp".isNull, $"b.current_holder_sp").otherwise($"a.current_holder_sp") as "current_holder_sp",
      when($"a.current_holder_plan_type_id".isNull, $"b.current_holder_plan_type_id").otherwise($"a.current_holder_plan_type_id") as "current_holder_plan_type_id",
      $"ported_tn_subscribers",
      $"industry_model_subscribers" as "industry_model_subscribers",
      $"industry_model_subscribers" / $"ported_tn_subscribers" as "total_subs_to_ported_subs_ratio"
    ).as[NationalSubscriberMultipliers]
  }

  /**
    * Calculate National Loss Multipliers
    * @param nationalSubscriberMultipliersPortedCBInputs: National Subscribers Multipliers Customer Base Inputs
    * @param nationalLossMultipliersIMInputs: National Loss Multipliers Industry Model Inputs
    * @return Returns a dataset of National Loss Multipliers data
    */
  def calculateNationalLossMultipliers(
    nationalSubscriberMultipliersPortedCBInputs: Dataset[NationalSubscriberMultipliersPortedCustomerBase],
    nationalLossMultipliersIMInputs: Dataset[NationalLossMultipliersIM]
  ):
  Dataset[NationalLossMultipliers] = {

    nationalSubscriberMultipliersPortedCBInputs.as("a")
      .join(
      nationalLossMultipliersIMInputs.as("b"),
        $"a.customer_base_date" === $"b.customer_base_date" &&
        $"a.current_holder_sp" === $"b.current_holder_sp" &&
        $"a.current_holder_plan_type_id" === $"b.current_holder_plan_type_id",
      "full_outer"
    ).select(
      when($"a.customer_base_date".isNull, $"b.customer_base_date").otherwise($"a.customer_base_date") as "customer_base_date",
      when($"a.current_holder_sp".isNull, $"b.current_holder_sp").otherwise($"a.current_holder_sp") as "current_holder_sp",
      when($"a.current_holder_plan_type_id".isNull, $"b.current_holder_plan_type_id").otherwise($"a.current_holder_plan_type_id") as "current_holder_plan_type_id",
      $"losses" as "ported_tn_losses",
      $"industry_model_losses" as "industry_model_losses",
      $"industry_model_losses" / $"losses" as "total_losses_to_ported_losses_ratio"
    ).as[NationalLossMultipliers]
  }

  /**
    * Calculate National Add Multipliers
    * @param nationalAddMultipliersPortedCBInputs: National Add Multipliers Customer Base Inputs
    * @param nationalAddMultipliersIMInputs: National Add Multipliers Industry Model Inputs
    * @return Returns a dataset of National Add Multipliers data
    */
  def calculateNationalAddMultipliers(
    nationalAddMultipliersPortedCBInputs: Dataset[NationalAddMultipliersPortedCustomerBase],
    nationalAddMultipliersIMInputs: Dataset[NationalAddMultipliersIM]
  ):
  Dataset[NationalAddMultipliers] = {

    nationalAddMultipliersPortedCBInputs.as("a")
      .join(
      nationalAddMultipliersIMInputs.as("b"),
      $"a.customer_base_date" === $"b.customer_base_date" &&
      $"a.current_holder_sp" === $"b.current_holder_sp" &&
      $"a.current_holder_plan_type_id" === $"b.current_holder_plan_type_id" &&
      $"a.previous_holder_sp" === $"b.previous_holder_sp" &&
      $"a.previous_holder_plan_type_id" === $"b.previous_holder_plan_type_id",
      "full_outer"
    ).select(
      when($"a.customer_base_date".isNull, $"b.customer_base_date").otherwise($"a.customer_base_date") as "customer_base_date",
      when($"a.current_holder_sp".isNull, $"b.current_holder_sp").otherwise($"a.current_holder_sp") as "current_holder_sp",
      when($"a.current_holder_plan_type_id".isNull, $"b.current_holder_plan_type_id").otherwise($"a.current_holder_plan_type_id") as "current_holder_plan_type_id",
      when($"a.previous_holder_sp".isNull, $"b.previous_holder_sp").otherwise($"a.previous_holder_sp") as "previous_holder_sp",
      when($"a.previous_holder_plan_type_id".isNull, $"b.previous_holder_plan_type_id").otherwise($"a.previous_holder_plan_type_id") as "previous_holder_plan_type_id",
      $"adds" as "ported_tn_adds",
      $"industry_model_adds" as "industry_model_adds",
      $"industry_model_adds" / $"adds" as "total_adds_to_ported_adds_ratio"
    ).as[NationalAddMultipliers]
  }

  /**
    * Calculate National Intra MNO Add Multipliers
    * @param nationalAddMultipliersIntraMnoInputs: National Add Multipliers Intra MNO Customer Base Inputs
    * @param nationalAddMultipliersIntraMnoIMInputs: National Add Multipliers Intra MNO Industry Model Inputs
    * @return Returns a dataset of National Add Multipliers Intra MNO data
    */
  def calculateNationalIntraMnoAddMultipliers(
    nationalAddMultipliersIntraMnoInputs: Dataset[NationalAddMultipliersIntraMnoInputs],
    nationalAddMultipliersIntraMnoIMInputs: Dataset[NationalAddMultipliersIntraMnoIM]
  ):
  Dataset[NationalAddMultipliersIntraMno] = {

    nationalAddMultipliersIntraMnoInputs.as("a")
      .join(
      nationalAddMultipliersIntraMnoIMInputs.as("b"),
      $"a.customer_base_date" === $"b.customer_base_date" &&
      $"a.current_holder_sp" === $"b.current_holder_sp" &&
      $"a.current_holder_plan_type_id" === $"b.current_holder_plan_type_id" &&
      $"a.previous_holder_sp" === $"b.previous_holder_sp" &&
      $"a.previous_holder_plan_type_id" === $"b.previous_holder_plan_type_id",
      "full_outer"
    ).select(
      when($"a.customer_base_date".isNull, $"b.customer_base_date").otherwise($"a.customer_base_date") as "customer_base_date",
      when($"a.current_holder_sp".isNull, $"b.current_holder_sp").otherwise($"a.current_holder_sp") as "current_holder_sp",
      when($"a.current_holder_plan_type_id".isNull, $"b.current_holder_plan_type_id").otherwise($"a.current_holder_plan_type_id") as "current_holder_plan_type_id",
      when($"a.previous_holder_sp".isNull, $"b.previous_holder_sp").otherwise($"a.previous_holder_sp") as "previous_holder_sp",
      when($"a.previous_holder_plan_type_id".isNull, $"b.previous_holder_plan_type_id").otherwise($"a.previous_holder_plan_type_id") as "previous_holder_plan_type_id",
      $"adds" as "ported_tn_adds",
      $"industry_model_adds" as "industry_model_adds",
      $"industry_model_adds" / $"adds" as "total_adds_to_ported_adds_ratio"
    ).as[NationalAddMultipliersIntraMno]
  }
}


object CalculateNationalMultipliers {

  def apply()(implicit spark: SparkSession): CalculateNationalMultipliers = new CalculateNationalMultipliers()

}
