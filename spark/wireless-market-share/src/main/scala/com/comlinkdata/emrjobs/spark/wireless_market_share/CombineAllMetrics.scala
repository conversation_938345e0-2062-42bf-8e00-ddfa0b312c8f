package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.ZipToDMA
import org.apache.spark.sql.functions.{concat_ws, concat, lit, split, trim}
import org.apache.spark.sql.{Dataset, SparkSession}


class CombineAllMetrics(implicit spark: SparkSession)  {

  import spark.implicits._

  /**
    * Method used to combine gross adds, gross losses, base adjustments, and subscribers in a single output
    * @param grossAdditionsData: Gross adds output
    * @param grossLossesData: Gross losses output
    * @param baseAdjustmentsData: Base adjustments output
    * @param subsEopData: Subscribers data calculated using EOP formula
    * @param ZipToDmaData: Zip to DMA lookup data
    * @return Returns the combined metrics dataset of type finalDatasetWithAllMetricsDMA
    */
  def combineAllMetricsDMA(
    grossAdditionsData: Dataset[EstimatedGrossAddsDMA],
    grossLossesData: Dataset[EstimatedGrossLossesDMA],
    baseAdjustmentsData: Dataset[EstimatedMonthlyBA],
    subsEopData: Dataset[EndOfPeriodDMA],
    ZipToDmaData: Dataset[ZipToDMA]
  ): Dataset[finalDatasetWithAllMetricsDMA] = {

    // Combine GA, GL, and BA along with other columns for DMA
    val combinedData = grossAdditionsData
      .join(grossLossesData, Seq("brand", "plan_type", "customer_base_date", "dma"), "full")
      .join(baseAdjustmentsData, Seq("brand", "plan_type", "customer_base_date", "dma"), "full")
      .join(subsEopData, Seq("brand", "plan_type", "customer_base_date", "dma"), "full")
      .withColumn("geography_type", lit("DMA"))
      .withColumn("bpt", $"brand")
//      .withColumn("brand_only", trim(split($"brand", "\\_").getItem(0)))
//      .withColumn("plan_type", trim(split($"brand", "\\_").getItem(1)))
//      .drop("brand")
//      .withColumnRenamed("brand_only", "brand")
      .na.fill(0)

    // Add geography column based on zip to dma data distinct records
    ZipToDmaData.select($"dma", $"dma_name").distinct()
      .join(combinedData, Seq("dma"), "RIGHT")
      .withColumn("geography", concat($"dma_name", lit(" ("), $"dma", lit(")")))
      .select(
        $"customer_base_date",
        $"geography",
        $"geography_type",
        $"brand",
        $"plan_type",
        $"bpt",
        $"dma",
        $"estimated_gross_adds" as "ga",
        $"estimated_gross_losses" as "gl",
        $"estimated_ba" as "ba",
        $"end_of_period_subscribers" as "subs"
      )
      .as[finalDatasetWithAllMetricsDMA]
  }

  def combineAllMetricsVPGM(
    grossAdditionsData: Dataset[EstimatedGrossAddsVPGM],
    grossLossesData: Dataset[EstimatedGrossLossesVPGM],
    baseAdjustmentsData: Dataset[EstimatedMonthlyBAVPGM],
    subsEopData: Dataset[EndOfPeriodVPGM]
  ): Dataset[finalDatasetWithAllMetricsVPGM] = {

    // Combine GA, GL, and BA along with other columns for VPGM
    grossAdditionsData
      .join(grossLossesData, Seq("brand", "plan_type", "customer_base_date", "vpgm"), "full")
      .join(baseAdjustmentsData, Seq("brand", "plan_type", "customer_base_date", "vpgm"), "full")
      .join(subsEopData, Seq("brand", "plan_type", "customer_base_date", "vpgm"), "full")
      .withColumn("geography", $"vpgm")
      .withColumn("geography_type", lit("VPGM"))
      .withColumn("bpt", concat_ws("_", $"brand", $"plan_type"))
      .na.fill(0)
      .select(
        $"customer_base_date",
        $"geography",
        $"geography_type",
        $"brand",
        $"plan_type",
        $"bpt",
        $"vpgm",
        $"estimated_gross_adds" as "ga",
        $"estimated_gross_losses" as "gl",
        $"estimated_ba" as "ba",
        $"end_of_period_subscribers" as "subs"
      )
      .as[finalDatasetWithAllMetricsVPGM]
  }
}

object CombineAllMetrics {
  def apply()(implicit spark: SparkSession): CombineAllMetrics = new CombineAllMetrics()
}
