package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.commons.RichDate._
import com.comlinkdata.largescale.schema.wireless_market_share.lookup._
import com.comlinkdata.largescale.schema.wireless_market_share._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import org.apache.spark.sql.{Dataset, SparkSession}

import java.time.LocalDate


class BaseAdjustmentsVPGM(implicit spark: SparkSession)  {

  import spark.implicits._

  def calculateBaseAdjustments(
    processingDate: LocalDate,
    industryModel: Dataset[IndustryModel],
    portedCarrierLosses: Dataset[PortedCarrierLossesVPGM]
  ): Dataset[EstimatedMonthlyBAVPGM] = {

    val quarter = processingDate.getQuarter
    val year = processingDate.getYear

    // Filter ported losses by specific year and add quarter column to it
    val portedCarrierLossesByQuarter = portedCarrierLosses
      .filter(date_format($"date_trunc", "yyyy").cast(IntegerType) === year)
      .withColumn("quarter", date_format($"date_trunc", "q"))
      .select(
        $"loser" as "brand",
        $"secondary_plan_type_id" as "plan_type",
        $"date_trunc" as "customer_base_date",
        $"quarter".cast(IntegerType),
        $"vpgm",
        $"switches"
      ).as[PortedCarrierLossesByQuarterVPGM]

    // Monthly Losses (by Month/Brand/Plan Type/Geo)
    val portedCarrierMonthlyLossesByGeos = calculateMonthlyLossesByGeos(portedCarrierLossesByQuarter)

    //  Quarterly Losses (by Brand/Plan Type)
    val portedCarrierLossesAggByQuarter = calculatePortedCarrierLossesAggByQuarter(portedCarrierLossesByQuarter)

    // Monthly Losses (by Month/Brand/Plan Type/Geo)/Quarterly Losses (by Brand/Plan Type)
    val portedCarrierMonthlyLossesGeoShare = calculatePortedCarrierMonthlyLossesGeoShare(
      processingDate, portedCarrierMonthlyLossesByGeos, portedCarrierLossesAggByQuarter
    )

    // National Quarterly Reported BA (by Brand/ Plan Type)
    // * Monthly Losses (by Month/Brand/Plan Type/Geo)/Quarterly Losses (by Brand/Plan Type)
    calculateFinalBA(quarter, year, industryModel, portedCarrierMonthlyLossesGeoShare)
  }

  def calculateMonthlyLossesByGeos(portedCarrierLossesByQuarter: Dataset[PortedCarrierLossesByQuarterVPGM]
  ): Dataset[MonthlyLossesByGeosVPGM]  = {
    portedCarrierLossesByQuarter
      .groupBy($"brand", $"plan_type", $"customer_base_date", $"vpgm", $"quarter")
      .agg(sum($"switches") as "switches_per_geo")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"vpgm",
        $"quarter",
        $"switches_per_geo"
      )
      .as[MonthlyLossesByGeosVPGM]
  }

  def calculatePortedCarrierLossesAggByQuarter(portedCarrierLossesByQuarter: Dataset[PortedCarrierLossesByQuarterVPGM]
  ): Dataset[PortedCarrierLossesAggByQuarterVPGM] = {
    portedCarrierLossesByQuarter
      .groupBy($"brand", $"plan_type", $"quarter")
      .agg(sum($"switches") as "switches_per_quarter")
      .select(
        $"brand",
        $"plan_type",
        $"quarter",
        $"switches_per_quarter"
      )
      .as[PortedCarrierLossesAggByQuarterVPGM]
  }

  def calculatePortedCarrierMonthlyLossesGeoShare(
    processingDate:LocalDate, portedCarrierMonthlyLossesByGeos: Dataset[MonthlyLossesByGeosVPGM],
    portedCarrierLossesAggByQuarter: Dataset[PortedCarrierLossesAggByQuarterVPGM]
  ): Dataset[PortedCarrierMonthlyLossesGeoShareVPGM] = {
    portedCarrierMonthlyLossesByGeos
      .join(portedCarrierLossesAggByQuarter, Seq("brand", "plan_type", "quarter"), "LEFT")
      .withColumn("monthly_loss_share", $"switches_per_geo" / $"switches_per_quarter")
      .where($"customer_base_date" === processingDate)
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"quarter",
        $"vpgm",
        $"monthly_loss_share"
      )
      .as[PortedCarrierMonthlyLossesGeoShareVPGM]
  }

  private def calculateFinalBA(
    quarter: Int, year:Int, industryModel: Dataset[IndustryModel],
    portedCarrierMonthlyLossesGeoShare: Dataset[PortedCarrierMonthlyLossesGeoShareVPGM]
  ): Dataset[EstimatedMonthlyBAVPGM] = {

    industryModel
      .select($"brand", $"plan_type", $"year", $"quarter", $"ba")
      .where($"quarter" === quarter && $"year" === year)
      .join(portedCarrierMonthlyLossesGeoShare, Seq("brand", "plan_type", "quarter"), "LEFT")
      .withColumn("estimated_ba", $"ba" * $"monthly_loss_share")
      .groupBy("brand", "plan_type", "customer_base_date", "vpgm")
      .agg(sum($"estimated_ba") as "estimated_ba")
      .na.fill(0)
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"vpgm",
        $"estimated_ba"
      )
      .as[EstimatedMonthlyBAVPGM]
  }
}

object BaseAdjustmentsVPGM {
  def apply()(implicit spark: SparkSession): BaseAdjustmentsVPGM = new BaseAdjustmentsVPGM()

}
