package com.comlinkdata.emrjobs.spark.wireless_market_share

import org.apache.spark.sql.{Dataset, SparkSession}
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share.{IndustryModel, PortedCarrierLosses, SubscribersOutput}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import com.comlinkdata.largescale.commons.RichDate._

import java.sql.Date
import java.time.temporal.IsoFields.QUARTER_OF_YEAR
import java.time.{LocalDate, YearMonth}


/** The class used to calculate the gross losses based on ported losses, previous quarter subscribers and
  * National model data */
class GrossLossesDMA(implicit spark: SparkSession)  {

  import spark.implicits._

  def calculateGrossLosses(
    processingDate: LocalDate,
    industryModel: Dataset[IndustryModel],
    portedCarrierLosses: Dataset[PortedCarrierLosses],
    subscribers: Dataset[SubscribersOutput]
  ): Dataset[EstimatedGrossLossesDMA] = {
    // 1. GA_rate_avg
    val monthlyNationalAverageGrossLossRate = computeMonthlyNationalAverageGrossLossRate(processingDate, industryModel,portedCarrierLosses)

    // 2. GA_flat
    val flatGrossLosses = computeFlatGrossLosses(processingDate, subscribers, monthlyNationalAverageGrossLossRate)

    // 3. GA_flat_share
    val flatGrossLossesGeoShare = computeFlatGrossLossAverageFlatShare(flatGrossLosses)

    // 4. W_share
    val portedCarrierLossGeoShare = computePortedCarrierLossGeoShare(portedCarrierLosses)

    // 7. GA
    computeEstimatedGrossLosses(flatGrossLossesGeoShare,portedCarrierLossGeoShare, flatGrossLosses)

  }

  //GL_rate_avg[b,p,m] = GL[b,p,m] / S[b,p,m]
  // specific: GA_rate_avg[b,p,m] = (current quarter GA / previous quarter Subs) * (current month losses) / (current quarter losses)
  def computeMonthlyNationalAverageGrossLossRate(
    processingDate: LocalDate,
    industryModel: Dataset[IndustryModel],
    portedCarrierLosses: Dataset[PortedCarrierLosses]
  ): Dataset[GrossLossesRateAverage] =  {
    val quarter = processingDate.getQuarter
    val year = processingDate.getYear
    val prevQuarter = if (quarter == 1) 4 else quarter-1
    val prevYear = if (quarter == 1) year-1 else year
    val startDate = YearMonth.of(year, (quarter-1) * 3 + 1).`with`(QUARTER_OF_YEAR, quarter).atDay(1)
    val endDate = startDate.plusMonths(2)

    // Current Date values
    val currentDate = LocalDate.now().withDayOfMonth(1)
//    val currentDate = LocalDate.of(2023, 9, 1)
    val currentQuarter = currentDate.getQuarter
    val currentYear = currentDate.getYear
    val startDateCurrentQuarter = YearMonth.of(currentYear, (currentQuarter-1) * 3 + 1).`with`(QUARTER_OF_YEAR, currentQuarter).atDay(1)
    val endDateCurrentQuarter = startDateCurrentQuarter.plusMonths(2)

    // current quarter gross losses / prev quarter subs
    val industryModelPrev = industryModel
      .select($"brand", $"plan_type", $"year", $"quarter", $"gross_losses")
      .where($"quarter" === quarter && $"year" === year)
      .join(
        industryModel
          .where($"quarter" === prevQuarter && $"year" === prevYear)
          .select($"brand", $"plan_type", $"year", $"quarter", $"subscribers"),
        Seq("brand", "plan_type"),
        "LEFT"
      )
      .withColumn("gross_rate_average_industry_model", $"gross_Losses" / $"subscribers")

    val portedCarrierLossesQuarter = portedCarrierLosses
      .withColumn("quarter", date_format($"date_trunc", "q"))
      .select(
        $"loser" as "brand",
        $"secondary_plan_type_id" as "plan_type",
        $"date_trunc" as "customer_base_date",
        $"quarter",
        $"dma",
        $"switches"
      )
      .where(date_format($"customer_base_date", "yyyy").cast(IntegerType) === year)
      .groupBy($"brand", $"plan_type", $"customer_base_date", $"quarter")
      .agg(sum($"switches") as "switches_no_dma")

    // copy of portedCarrierLosses to update them if processing date is the 2nd month of the quarter
    var portedCarrierLossesQuarterUpdated = portedCarrierLossesQuarter

    // If it's second month of the quarter, add third month losses by taking avg of first and second month of that quarter
    if(processingDate.equals(startDate.plusMonths(1)) && currentDate.equals(processingDate.plusMonths(1))) {
      println(s"Adding third month: $endDate subs taking average of 1st and 2nd month subs")
      portedCarrierLossesQuarterUpdated = portedCarrierLossesQuarter
        .union(
          portedCarrierLossesQuarter
            .groupBy($"brand", $"plan_type", $"quarter")
            .agg(avg($"switches_no_dma") as "switches_avg")
            .withColumn("customer_base_date", lit(Date.valueOf(processingDate.plusMonths(1))))
            .select(
              $"brand",
              $"plan_type",
              $"customer_base_date",
              $"quarter",
              $"switches_avg" as "switches_no_dma"
            )
        )
    }
    else {
      println(s"We can't/don't need to add 3rd months subs because,\n1) The processing month = $processingDate should be the second month of the quarter. \n2) The current date = $currentDate should exactly the 3rd month of the in progress quarter\n")
    }

    val portedCarrierLossesAgg = portedCarrierLossesQuarterUpdated
      .groupBy($"brand", $"plan_type", $"quarter")
      .agg(sum($"switches_no_dma") as "switches_quarter")

    val portedCarrierLossesProcessed = portedCarrierLossesQuarter
      .join(portedCarrierLossesAgg, Seq("brand", "plan_type", "quarter"), "LEFT")
      .where($"customer_base_date" === processingDate)

    // GA average rates calculation based on the month in the quarter
    var averageGrossLossRate = spark.emptyDataset[GrossLossesRateAverage]

    // Completed quarter: E.g. If we are in Apr 24 that means 1st quarter of 2024 or all quarters before Apr-24 are completed
    // Provided that we have nationally reported numbers ready by Apr 24
    if(processingDate.isBefore(currentDate) && startDateCurrentQuarter.isAfter(endDate)) {
      // Quarter - After it has completed and carriers reported
      println(s"We are processing subs for the completed quarter for the date = $processingDate...")
      averageGrossLossRate = industryModelPrev
        .join(portedCarrierLossesProcessed,Seq("brand","plan_type","quarter"),"LEFT")
        .withColumn("gross_rate_average",$"gross_rate_average_industry_model" * $"switches_no_dma" / $"switches_quarter")
        .select($"brand",$"plan_type",$"customer_base_date", $"gross_rate_average" as "gross_rate_average")
        .as[GrossLossesRateAverage]
    }
    // In-progress quarter
    else if(processingDate.isBefore(currentDate) && currentQuarter == quarter) {  // now it could be 1st or 2nd month of the quarter
      // Check if processing month is the 1st or 2nd month of the quarter
      if(processingDate.equals(startDate)) {   // If it's 1st month of the quarter
        println(s"We are processing subs for the In-Progress quarter for the first month date = $processingDate...")
        averageGrossLossRate = industryModelPrev
          .join(portedCarrierLossesProcessed,Seq("brand","plan_type","quarter"),"LEFT")
          .withColumn("gross_rate_average",$"gross_rate_average_industry_model" * $"switches_no_dma" / $"switches_quarter" / 3)
          .select($"brand",$"plan_type",$"customer_base_date", $"gross_rate_average" as "gross_rate_average")
          .as[GrossLossesRateAverage]
      }
      else if (processingDate.equals(startDate.plusMonths(1))){  // if it's second month of the quarter
        println(s"We are processing subs for the In-Progress quarter for the second month date = $processingDate...")
        averageGrossLossRate = industryModelPrev
          .join(portedCarrierLossesProcessed,Seq("brand","plan_type","quarter"),"LEFT")
          .withColumn("gross_rate_average",$"gross_rate_average_industry_model" * $"switches_no_dma" / $"switches_quarter")
          .select($"brand",$"plan_type",$"customer_base_date", $"gross_rate_average" as "gross_rate_average")
          .as[GrossLossesRateAverage]
      }
      else {
        println("ERROR: Invalid processing date - we can not calculate the subs for the current month (3rd month of the quarter) with in-progress quarter process")
      }
    }
    else {
      println("ERROR: Invalid processing date - we can not calculate the subs for the current or future months")
    }

    averageGrossLossRate

  }

  //GL_flat[b,p,m,g] = S[b,p,m,g]*GL_rate_avg[b,p,m]
  def computeFlatGrossLosses(
    processingDate: LocalDate,
    subscribers: Dataset[SubscribersOutput],
    monthlyNationalAverageGrossLossRate: Dataset[GrossLossesRateAverage]
  ): Dataset[GrossLossesFlatDMA] = {

    subscribers.alias("a").join(
      monthlyNationalAverageGrossLossRate.alias("b"), Seq("brand","plan_type"), "LEFT"
    ).select(
      $"a.brand",
      $"a.plan_type",
      $"b.customer_base_date",
      $"a.dma",
      $"a.subs" * $"b.gross_rate_average" as "gross_losses_flat"
    ).as[GrossLossesFlatDMA]

  }

  //GL_flat_share[b,p,m,g] = GL_flat[b,p,m,g] / sum{over b,p,m}(GL_flat[b,p,m,g] )
  def computeFlatGrossLossAverageFlatShare(
    flatGrossLosses: Dataset[GrossLossesFlatDMA]
  ): Dataset[GrossLossesAverageFlatShareDMA] = {
    val left = flatGrossLosses
      .groupBy($"brand", $"plan_type", $"customer_base_date", $"dma")
      .agg(sum($"gross_losses_flat") as "a_gross_losses_flat")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"dma",
        $"a_gross_losses_flat"
      )
    val right = flatGrossLosses
      .groupBy($"brand",$"plan_type",$"customer_base_date")
      .agg(sum($"gross_losses_flat") as "b_gross_losses_flat")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"b_gross_losses_flat"
      )
    left
      .join(right, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"dma",
        $"a_gross_losses_flat" / $"b_gross_losses_flat" as "gross_average_flat_share"
      )
      .as[GrossLossesAverageFlatShareDMA]
  }

  //L_share[b,p,m,g] = L[b,p,g,m]/sum{over b,p,m}(L[b,p,g,m])
  def computePortedCarrierLossGeoShare(
    portedCarrierLosses: Dataset[PortedCarrierLosses]
  ): Dataset[PortedGeographicLossShareDMA] = {
    val portedCarrierLossesRenamed = portedCarrierLosses
      .select(
        $"loser" as "brand",
        $"secondary_plan_type_id" as "plan_type",
        $"date_trunc" as "customer_base_date",
        $"dma",
        $"switches"
      )
    val left = portedCarrierLossesRenamed
      .groupBy($"brand", $"plan_type", $"customer_base_date", $"dma")
      .agg(sum($"switches") as "a_switches")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"dma",
        $"a_switches"
      )
    val right = portedCarrierLossesRenamed
      .groupBy($"brand", $"plan_type", $"customer_base_date")
      .agg(sum($"switches") as "b_switches")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"b_switches"
      )
    left
      .join(right, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"dma",
        $"a_switches" / $"b_switches" as "ported_loss_shares"
      )
      .as[PortedGeographicLossShareDMA]
  }

  def computeEstimatedGrossLosses(
    flatGrossLossesGeoShare: Dataset[GrossLossesAverageFlatShareDMA],
    portedCarrierLossGeoShare: Dataset[PortedGeographicLossShareDMA],
    flatGrossLossesData: Dataset[GrossLossesFlatDMA]
  ): Dataset[EstimatedGrossLossesDMA] = {

    val A =
    flatGrossLossesGeoShare
      .join(portedCarrierLossGeoShare, Seq("brand", "plan_type", "customer_base_date", "dma"), "LEFT")
      .na.fill(0, Array("ported_loss_shares"))
      .withColumn("ga_share_variance", lit(0.5) * ($"ported_loss_shares" - $"gross_average_flat_share"))
      .withColumn("estimated_gross_loss_geo_share", $"gross_average_flat_share" + $"ga_share_variance")
      .select($"brand", $"plan_type", $"customer_base_date", $"dma", $"ga_share_variance", $"estimated_gross_loss_geo_share")

    flatGrossLossesData
      .groupBy($"brand", $"plan_type", $"customer_base_date")
      .agg(sum($"gross_losses_flat") as "gross_losses_flat_no_geo")
      .join(A, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
      .withColumn("estimated_gross_losses", $"estimated_gross_loss_geo_share" * $"gross_losses_flat_no_geo") // divide by 3 for quarter to month
      .select($"brand", $"plan_type", $"customer_base_date", $"dma", $"estimated_gross_losses")
      .na.fill(0)
      .as[EstimatedGrossLossesDMA]
  }
}
object GrossLossesDMA {
  def apply()(implicit spark: SparkSession): GrossLossesDMA = new GrossLossesDMA()

}
