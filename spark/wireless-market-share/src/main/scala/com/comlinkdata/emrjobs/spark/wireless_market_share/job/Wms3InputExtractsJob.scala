package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.emrjobs.spark.wireless_market_share.inputs.{MonthlyPortedWinsAndLossesWCV, NationalOracleWCVFlows}
import com.comlinkdata.largescale.commons.io.dataset.writeCsvOverwrite
import com.comlinkdata.largescale.commons.{RedshiftUtils, SparkJob, SparkJob<PERSON>unner, Utils}
import com.comlinkdata.largescale.schema.wireless_market_share._
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.{CustomRegionsPreProdLookup, FZipLookup}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.SparkSession
import java.time.LocalDate
import java.net.URI
import java.sql.Date


/**
  * WMS 3.0 process inputs preparation job
  * @param monthToProcess: Specific month of subs we need to generate
  * @param wirelessMovementWideGenericLoc: WCV churn data from Redshift table
  * @param nationalOracleDemoDataLoc: National Oracle Demographics data from Redshift
  * @param fZipDataRedshiftTableName: Zip to DMA lookup Redshift table
  * @param customRegionsPreprodRedshiftTableName: Custom Regions lookup Redshift table
  * @param redshiftJdbcEndpoint: Redshift database server URL
  * @param redshiftUserName: Redshift user name
  * @param redshiftParameterStoreKey: Redshift SSM parameter story key to get the password
  * @param redshiftTempLocation: Temporary S3 location for Redshift copy
  * @param outputBasePath: Output base path to store the extracted data
  * @param outputPartitions: Number of partitions
  */
case class Wms3InputExtractsJobConfig(
  monthToProcess: Option[LocalDate],
  wirelessMovementWideGenericLoc: URI,
  nationalOracleDemoDataLoc: URI,
  fZipDataRedshiftTableName: String,
  customRegionsPreprodRedshiftTableName: String,
  redshiftJdbcEndpoint: String,
  redshiftUserName: String,
  redshiftParameterStoreKey: String,
  redshiftTempLocation: String,
  outputBasePath: URI,
  outputPartitions: Int
)


// Sample config just for FYIe
object Wms3InputExtractsJobConfig {

  val sample: Wms3InputExtractsJobConfig = Wms3InputExtractsJobConfig(
    monthToProcess = Option(LocalDate.of(2022, 9, 1)),
    wirelessMovementWideGenericLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/wireless_movement_wide_generic/",
    nationalOracleDemoDataLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/national_oracle_demographics_ffc_data/",
    fZipDataRedshiftTableName = "wireless.f_zip",
    customRegionsPreprodRedshiftTableName = "rptsatt.custom_regions_preprod",
    redshiftJdbcEndpoint = "******************************************************************************",
    redshiftUserName = "rpts_loader_dev",
    redshiftParameterStoreKey = "/c300/reporting_environment/rpts_loader_dev",
    redshiftTempLocation = "s3://d000-comlinkdata-com/redshift-uploads/wireless-market-share",
    outputBasePath = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/outputs/",
    outputPartitions = 1
  )
}


object Wms3InputExtractsJob extends SparkJob[Wms3InputExtractsJobConfig](Wms3InputExtractsJobRunner)

object Wms3InputExtractsJobRunner extends SparkJobRunner[Wms3InputExtractsJobConfig] with LazyLogging {
  def runJob(config: Wms3InputExtractsJobConfig)(implicit spark: SparkSession): Unit  = {
    import spark.implicits._

    // Default processing date would be latest month. The processing date will always get used to extracts the latest data
    val processingDate = LocalDate.now().withDayOfMonth(1)

    // Specific month to process, the default month to process should be the previous month
    // Todo: Keeping monthToProcess param as it's might required in the future to get the specific month extracts.
    val monthToProcess = config.monthToProcess.getOrElse(processingDate.minusMonths(1)).toString

    // Read common schema's
    val rsConfig = RedshiftUtils.RedshiftConfig(
      rsTemporaryLocation = URI create config.redshiftTempLocation,
      rsJdbcEndpoint = config.redshiftJdbcEndpoint,
      rsUserName = config.redshiftUserName,
      rsParameterStoreKey = config.redshiftParameterStoreKey
    )

    logger.info(s"Loading WCV ported wins and losses data from the path: ${config.wirelessMovementWideGenericLoc}")
    val wirelessMovementWideGenericData = WirelessMovementWideGeneric.read(config.wirelessMovementWideGenericLoc)

    logger.info(s"Loading National Oracle Demographics FFC data from the path: ${config.nationalOracleDemoDataLoc}")
    val nationalOracleDemographicsData = NationalOracleDemographicsFFC.read(config.nationalOracleDemoDataLoc)

    logger.info(s" Loading F_Zip lookup data from the Redshift table: ${config.fZipDataRedshiftTableName}")
    val fZipLookupData = FZipLookup.readFromRS(config.fZipDataRedshiftTableName)(rsConfig, spark)

    // ** Calculations **

    // Monthly Ported Wins and Losses from WCV
    logger.info(s"Calculating the DMA ported wins and losses from wcv...")
    val portedWinsLossesExtractDataDMA = MonthlyPortedWinsAndLossesWCV().calculateMonthlyPortedWinsAndLossesWcvExtractDma(
      Date.valueOf(processingDate), wirelessMovementWideGenericData, fZipLookupData
    )

    // National WCV flows data from National Oracle demographics FFC
    logger.info(s"Calculating the National WCV flows from National Oracle Data...")
    val nationalWCVFlowsData = NationalOracleWCVFlows().calculateNationalWCVFlowsData(
      Date.valueOf(processingDate), nationalOracleDemographicsData
    )

    // ** Write data to S3 **

    // Ported Wins and Losses DMA
    val outputDmaPortedWinsLossesPath = Utils.joinPaths(config.outputBasePath.toString, "/commons/dma/wcv_ported_wins_losses", s"processing_date=$processingDate")
    logger.info(s"Writing Ported Wins and Losses DMA data to the S3 location $outputDmaPortedWinsLossesPath")
    writeCsvOverwrite(portedWinsLossesExtractDataDMA, config.outputPartitions, outputDmaPortedWinsLossesPath)

    // National WCV Flows Data
    val nationalWCVFlowsPath = Utils.joinPaths(config.outputBasePath.toString, "/with_demographics/commons/national_wcv_flows_ethnicity_age_income", s"processing_date=$processingDate")
    logger.info(s"Writing National WCV flows data to the S3 location $nationalWCVFlowsPath")
    writeCsvOverwrite(nationalWCVFlowsData, config.outputPartitions, nationalWCVFlowsPath)
  }
}
