package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.commons.TimeSeriesLocation
import org.apache.spark.sql.{Dataset, SparkSession}
import org.apache.spark.sql.functions.{add_months, coalesce, col, lit, sum}
import java.net.URI


class GenerateFinalOutput (implicit spark: SparkSession) {

  import spark.implicits._

  /**
    * Generates final output.
    *
    * @param rateCenterNPATotalSubsWithRegionalMNOs 6.5 output
    * @param rateCenterTotalIntraMNOAdds 5.3 output
    * @param rateCenterNPATotalAddsWithRegionalMNOs 6.6 output
    * @param nationalAddMultipliersActivationsIM 1.6
    * @return
    */
  def calculateFinalOutput(
    rateCenterNPATotalSubsWithRegionalMNOs: Dataset[RateCenterNPATotalSubsWithRegionalMNOs],
    rateCenterTotalIntraMNOAdds: Dataset[RateCenterTotalIntraMNOAdds],
    rateCenterNPATotalAddsWithRegionalMNOs: Dataset[RateCenterNPATotalAddsWithRegionalMNOs],
    nationalAddMultipliersActivationsIM: Dataset[NationalAddMultipliersActivationsIM]
  ): Dataset[FinalOutput]  = {
    val a = rateCenterNPATotalSubsWithRegionalMNOs
      .where($"industry_model_sp_ind" === 1)
      .groupBy(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"industry_model_sp_ind")
      .agg(
        sum($"rate_center_npa_industry_model_subscribers") as "starting_customers",
        sum($"rate_center_npa_industry_model_losses") as "losses_excl_intra_mno",
        sum($"ported_tn_subscribers") as "ported_starting_customers",
        sum($"ported_tn_losses") as "ported_losses_excl_intra_mno"
      )
      .select(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"industry_model_sp_ind",
        $"starting_customers",
        $"losses_excl_intra_mno",
        $"ported_starting_customers",
        $"ported_losses_excl_intra_mno"
      )

    val b = rateCenterTotalIntraMNOAdds
      .where($"industry_model_sp_ind_current" === 1 && $"industry_model_sp_ind_previous" === 1)
      .groupBy(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"previous_holder_sp",
        $"previous_holder_plan_type_id"
      )
      .agg(
        sum($"rate_center_industry_model_adds") as "intra_mno_losses",
        sum($"ported_tn_adds") as "ported_intra_mno_losses"
      )
      .select(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"previous_holder_sp",
        $"previous_holder_plan_type_id",
        $"intra_mno_losses",
        $"ported_intra_mno_losses"
      )

    val c = rateCenterNPATotalAddsWithRegionalMNOs
      .where($"industry_model_sp_ind_current" === 1 && $"industry_model_sp_ind_previous" === 1)
      .groupBy(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"current_holder_sp",
        $"current_holder_plan_type_id"
      )
      .agg(
        sum($"rate_center_npa_industry_model_adds") as "wins_excl_intra_mno",
        sum($"ported_tn_adds") as "ported_wins_excl_intra_mno"
      )
      .select(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"wins_excl_intra_mno",
        $"ported_wins_excl_intra_mno"
      )

    val d = rateCenterTotalIntraMNOAdds
      .where($"industry_model_sp_ind_current" === 1 && $"industry_model_sp_ind_previous" === 1)
      .groupBy(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"current_holder_sp",
        $"current_holder_plan_type_id"
      )
      .agg(
        sum($"rate_center_industry_model_adds") as "intra_mno_wins",
        sum($"ported_tn_adds") as "ported_intra_mno_wins"
      )
      .select(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"intra_mno_wins",
        $"ported_intra_mno_wins"
      )

    val g = rateCenterNPATotalSubsWithRegionalMNOs
      .where($"industry_model_sp_ind" === 1)
      .groupBy(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"industry_model_sp_ind"
      )
      .agg(
        sum($"rate_center_npa_industry_model_subscribers") as "ending_customers",
        sum($"ported_tn_subscribers") as "ported_ending_customers"
      )
      .select(
        $"customer_base_date",
        $"zip_rc_kblock",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"industry_model_sp_ind",
        $"ending_customers",
        $"ported_ending_customers"
      )

    val bRenamed = b
      .withColumnRenamed("customer_base_date", "customer_base_date_b")
      .withColumnRenamed("zip_rc_kblock", "zip_rc_kblock_b")


    // a join b
    val ab = a
      .join(
        bRenamed,
        $"customer_base_date" === $"customer_base_date_b" &&
          $"zip_rc_kblock" === $"zip_rc_kblock_b" &&
          $"current_holder_sp" === $"previous_holder_sp" &&
          $"current_holder_plan_type_id" === $"previous_holder_plan_type_id",
        "LEFT"
      )
      .drop("customer_base_date_b")
      .drop("zip_rc_kblock_b")

    val abc = ab
      .join(
        c,
        Seq("customer_base_date", "zip_rc_kblock", "current_holder_sp", "current_holder_plan_type_id"),
        "LEFT"
      )

    val abcd = abc
      .join(
        d,
        Seq("customer_base_date", "zip_rc_kblock", "current_holder_sp", "current_holder_plan_type_id"),
        "LEFT"
      )

    val abcdf = abcd
      .join(
        nationalAddMultipliersActivationsIM,
        Seq("customer_base_date", "current_holder_sp", "current_holder_plan_type_id")
        , "LEFT"
      )

    val gRenamed = g
      .withColumnRenamed("customer_base_date", "customer_base_date_g")
      .withColumnRenamed("zip_rc_kblock", "zip_rc_kblock_g")
      .withColumnRenamed("current_holder_sp", "current_holder_sp_g")
      .withColumnRenamed("current_holder_plan_type_id", "current_holder_plan_type_id_g")
      .withColumnRenamed("industry_model_sp_ind", "industry_model_sp_ind_g")

    val finalOutput = abcdf
      .join(
        gRenamed,
        $"customer_base_date" === add_months(col("customer_base_date_g"), -1) &&
          $"zip_rc_kblock" === $"zip_rc_kblock_g" &&
          $"current_holder_sp" === $"current_holder_sp_g" &&
          $"current_holder_plan_type_id" === $"current_holder_plan_type_id_g",
        "LEFT"
      )
      .drop("customer_base_date_g")
      .drop("zip_rc_kblock_g")
      .drop("current_holder_sp_g")
      .drop("current_holder_plan_type_id_g")
      .drop("industry_model_sp_ind_g")

    finalOutput
      .select(
        $"zip_rc_kblock",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"industry_model_sp_ind",
        $"starting_customers",
        $"ported_starting_customers",
        $"losses_excl_intra_mno",
        $"ported_losses_excl_intra_mno",
        $"intra_mno_losses",
        $"ported_intra_mno_losses",
        (coalesce($"losses_excl_intra_mno", lit(0)) + coalesce($"intra_mno_losses", lit(0))) as "total_losses",
        (coalesce($"ported_losses_excl_intra_mno", lit(0)) + coalesce($"ported_intra_mno_losses", lit(0))) as "ported_total_losses",
        $"wins_excl_intra_mno",
        $"ported_wins_excl_intra_mno",
        $"intra_mno_wins",
        $"ported_intra_mno_wins",
        (coalesce($"wins_excl_intra_mno", lit(0)) + coalesce($"intra_mno_wins", lit(0))) as "wins_excl_activations",
        (coalesce($"ported_wins_excl_intra_mno", lit(0)) + coalesce($"ported_intra_mno_wins", lit(0))) as "ported_wins_excl_activations",
        $"activations_multiplier",
        $"activations_multiplier" * (coalesce($"wins_excl_intra_mno", lit(0)) + coalesce($"intra_mno_wins", lit(0))) as "activations",
        (coalesce($"wins_excl_intra_mno", lit(0)) + coalesce($"intra_mno_wins", lit(0))) + ($"activations_multiplier" * (coalesce($"wins_excl_intra_mno", lit(0)) + coalesce($"intra_mno_wins", lit(0)))) as "total_wins",
        $"ending_customers",
        $"ported_ending_customers",
        $"customer_base_date"
      ).as[FinalOutput]
  }

}

object GenerateFinalOutput  {
  def tsl(path: URI)(implicit spark: SparkSession): TimeSeriesLocation = {
    TimeSeriesLocation.ofDatePartitions(path, datePartitionName = "processing_date").build
  }
  def apply()(implicit spark: SparkSession): GenerateFinalOutput = new GenerateFinalOutput()
}
