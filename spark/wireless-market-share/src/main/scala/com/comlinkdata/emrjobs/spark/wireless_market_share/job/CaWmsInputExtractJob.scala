package com.comlinkdata.emrjobs.spark.wireless_market_share.job


import com.comlinkdata.emrjobs.spark.wireless_market_share.inputs.CaMonthlyPortedWinsAndLossesWMS
import com.comlinkdata.largescale.commons.io.dataset.writeCsvOverwrite
import com.comlinkdata.largescale.commons.{RedshiftUtils, Spark<PERSON>ob, SparkJob<PERSON><PERSON>ner, Utils}
import com.comlinkdata.largescale.schema.wireless_market_share.redshift.{CaWirelessMovementWide, DDa, ShawMigrations, Wms10ModelWithPlanType2023Dec, DSpIdCatFlanker,Wms10SinglePeriodSubsBaseline2020Dec}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.SparkSession
import java.time.LocalDate
import java.net.URI
import java.sql.Date



case class CaWmsInputExtractJobConfig(
  monthToProcess: Option[LocalDate],
  caWirelessMovementWideTableName: String,
  dDaTableName: String,
  dSpIdCatFlankerTableName: String,
  shawMigrationsTableName: String,
  wms10ModelWithPlanType2023DecTableName: String,
  wms10SinglePeriodSubsBaseline2020DecTableName: String,
  redshiftJdbcEndpoint: String,
  redshiftUserName: String,
  redshiftParameterStoreKey: String,
  redshiftTempLocation: String,
  outputBasePath: URI,
  outputPartitions: Int
)


/** Sample config to understand the configuration params values */
object CaWmsInputExtractJobConfig {

  val sample: CaWmsInputExtractJobConfig = CaWmsInputExtractJobConfig(
    monthToProcess = Option(LocalDate.of(2022, 9, 1)),
    caWirelessMovementWideTableName = "canada_w.a_wireless_movement_wide",
    dDaTableName = "canada_w.d_da",
    dSpIdCatFlankerTableName = "canada_w.d_sp_id_cat_flanker",
    shawMigrationsTableName = "canada_w.shaw_migrations",
    wms10ModelWithPlanType2023DecTableName = "canada_w.wms_1_0_model_with_plantype_2023dec",
    wms10SinglePeriodSubsBaseline2020DecTableName = "canada_w.wms_1_0_single_period_subs_baseline_2020dec",
    redshiftJdbcEndpoint = "******************************************************************************",
    redshiftUserName = "rpts_loader_dev",
    redshiftParameterStoreKey = "/c300/reporting_environment/rpts_loader_dev",
    redshiftTempLocation = "s3://d000-comlinkdata-com/redshift-uploads/wireless-market-share",
    outputBasePath = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/outputs/",
    outputPartitions = 1
  )
}


object CaWmsInputExtractJob extends SparkJob[CaWmsInputExtractJobConfig](CaWmsInputExtractJobRunner)

object CaWmsInputExtractJobRunner extends SparkJobRunner[CaWmsInputExtractJobConfig] with LazyLogging {
  def runJob(config: CaWmsInputExtractJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    // Default processing date would be latest month. The processing date will always get used to extracts the latest data
    val processingDate = LocalDate.now().withDayOfMonth(1)

    // Read common schema's
    val rsConfig = RedshiftUtils.RedshiftConfig(
      rsTemporaryLocation = URI create config.redshiftTempLocation,
      rsJdbcEndpoint = config.redshiftJdbcEndpoint,
      rsUserName = config.redshiftUserName,
      rsParameterStoreKey = config.redshiftParameterStoreKey
    )

    logger.info(s"Loading wireless movement wide data from Redshift table: ${config.caWirelessMovementWideTableName}")
    val caWirelessMovementWideData = CaWirelessMovementWide.readFromRS(config.caWirelessMovementWideTableName)(rsConfig, spark)

    logger.info(s"Loading data from Redshift table: ${config.dDaTableName}")
    val dDaData = DDa.readFromRS(config.dDaTableName)(rsConfig, spark)

    logger.info(s"Loading DSpIdCatFlanker data from the Redshift table: ${config.dSpIdCatFlankerTableName}")
    val dSpIdCatFlankerData = DSpIdCatFlanker.readFromRS(config.dSpIdCatFlankerTableName)(rsConfig, spark)

    logger.info(s"Loading Shaw migrations data from Redshift table: ${config.shawMigrationsTableName}")
    val shawMigrationsData = ShawMigrations.readFromRS(config.shawMigrationsTableName)(rsConfig, spark)

    logger.info(s"Loading Wms10ModelWithPlanType2020Dec data from the Redshift table: ${config.wms10ModelWithPlanType2023DecTableName}")
    val wms10ModelWithPlanType2020DecData = Wms10ModelWithPlanType2023Dec.readFromRS(config.wms10ModelWithPlanType2023DecTableName)(rsConfig, spark)

    logger.info(s"Loading Wms10ModelWithPlanType2020Dec data from the Redshift table: ${config.wms10SinglePeriodSubsBaseline2020DecTableName}")
    val wms10SinglePeriodSubsBaseline2020DecData = Wms10SinglePeriodSubsBaseline2020Dec.readFromRS(config.wms10SinglePeriodSubsBaseline2020DecTableName)(rsConfig, spark)


    // ** Calculations **

    // Generate Monthly Ported Wins and Losses data
    logger.info(s"Calculating the WMS ported wins and losses data...")
    val caPortedWinsLossesExtractDataWMS = CaMonthlyPortedWinsAndLossesWMS().calculateMonthlyPortedWinsAndLossesWMSExtractCa(
      caWirelessMovementWideData, dDaData, dSpIdCatFlankerData, shawMigrationsData, wms10ModelWithPlanType2020DecData, wms10SinglePeriodSubsBaseline2020DecData
    )


    // ** Write data to S3 **

    // Write Ported Wins and Losses DMA
    val outputDmaPortedWinsLossesPath = Utils.joinPaths(config.outputBasePath.toString, "/commons/dma/wcv_ported_wins_losses", s"processing_date=$processingDate")
    logger.info(s"Writing Ported Wins and Losses WMS data to the S3 location $outputDmaPortedWinsLossesPath")
    writeCsvOverwrite(caPortedWinsLossesExtractDataWMS, config.outputPartitions, outputDmaPortedWinsLossesPath)
  }
}







