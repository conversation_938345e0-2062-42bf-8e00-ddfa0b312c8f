package com.comlinkdata.emrjobs.spark.wireless_market_share

import org.apache.spark.sql.{Dataset, SparkSession}
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.commons.Utils
import com.comlinkdata.largescale.schema.wireless_market_share.{IndustryModelTotalOutput, MvnoCustomerBase, WirelessMovementWideMonthlyAgg}
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.{DCommonOcnLookup, NpaToNpaComplexLookup}
import org.apache.spark.sql.functions._
import java.sql.Date


class RateCenterPortedTNCustBaseAgg(implicit spark: SparkSession) {

  import spark.implicits._

  /**
    * Compute rate center npa ported customer base inputs
    * @param wirelessMvnoCustBase: MVNO wireless customer base data
    * @param xmsmTiebreaker: xm sm tiebreaker input
    * @param dCommonOCN: OCN to SP mapping lookup table
    * @param npaNpaComplexLookup: NPA to NPA Complex mapping lookup table
    * @param startDate: Start Date
    * @param endDate: End Date
    * @return Returns a dataset of rate center npa ported customer base inputs
    */
  def computeRateCenterNpaPortedCBInputs(
    wirelessMvnoCustBase: Dataset[MvnoCustomerBase],
    xmsmTiebreaker: Dataset[XMSMTiebreaker],
    dCommonOCN: Dataset[DCommonOcnLookup],
    npaNpaComplexLookup: Dataset[NpaToNpaComplexLookup],
    mvnoSPList1: List[Int],
    mvnoSPList2: List[Int],
    startDate: Date,
    endDate: Date
  ): Dataset[RateCenterNPAPortedCustomerBase] = {

     wirelessMvnoCustBase
      .join(xmsmTiebreaker.withColumnRenamed(
        "current_holder_mvno_sp", "current_holder_mvno_sp_xmsm"),
        Seq("zip_rc_kblock"), "left")
      .join(broadcast(dCommonOCN),
        wirelessMvnoCustBase("previous_holder_sp") === dCommonOCN("ocn_common_dim_id"),
        "left")
      .join(broadcast(npaNpaComplexLookup), Seq("npa"), "left")
      .filter(Utils.and(
        $"noncompetitive_ind" === lit(0),
        $"customer_base_date".between(startDate, endDate),
        Utils.or(
          $"common_name_mode" === lit("W"),
          $"common_name_mode".isNull
        ),
        $"current_holder_mvno_sp" =!= lit(5932),
        $"previous_holder_mvno_sp" =!= lit(5932),
        $"total_customers" > lit(0.0000),
        $"total_customers" > lit(0.0000),
        Utils.or(
          $"current_holder_mvno_sp".isin(mvnoSPList1: _*),
          $"current_holder_mvno_sp".isin(mvnoSPList2: _*),
          $"mvno_winning_plan_type_id".isin(List(1, 2): _*)
        ))).withColumn("current_holder_sp",
      GeoAllocationFunctions.generateHolderSP($"current_holder_mvno_sp", $"current_holder_mvno_sp_xmsm")
    ).withColumn("current_holder_plan_type_id",
      GeoAllocationFunctions.generateHolderPlanTypeID($"current_holder_mvno_sp", $"mvno_winning_plan_type_id", mvnoSPList1, mvnoSPList2)
    ).groupBy(
      $"customer_base_date",
      $"zip_rc_kblock",
      $"npa",
      $"npa_complex_cld",
      $"current_holder_sp",
      $"current_holder_plan_type_id",
    ).agg(
      sum("total_customers").as("ported_tn_subscribers"),
      sum("total_losses").as("ported_tn_losses"),
    ).select(
      "customer_base_date",
      "zip_rc_kblock",
      "npa",
      "npa_complex_cld",
      "current_holder_sp",
      "current_holder_plan_type_id",
      "ported_tn_subscribers",
      "ported_tn_losses"
    ).as[RateCenterNPAPortedCustomerBase]
  }

  /**
    * Compute rate center npa adds ported customer base inputs
    * @param wirelessMvnoCustBase: MVNO wireless customer base data
    * @param xmsmTiebreaker: xm sm tiebreaker input
    * @param dCommonOCN: OCN to SP mapping lookup table
    * @param npaNpaComplexLookup: NPA to NPA Complex mapping lookup table
    * @param startDate: Start Date
    * @param endDate: End Date
    * @return Returns a dataset of rate center npa adds ported customer base inputs
    */
  def computeRateCenterNpaAddsPortedCBInputs(
    wirelessMvnoCustBase: Dataset[MvnoCustomerBase],
    xmsmTiebreaker: Dataset[XMSMTiebreaker],
    dCommonOCN: Dataset[DCommonOcnLookup],
    npaNpaComplexLookup: Dataset[NpaToNpaComplexLookup],
    mvnoSPList1: List[Int],
    mvnoSPList2: List[Int],
    startDate: Date,
    endDate: Date
  ): Dataset[RateCenterNPAAddsPortedCustomerBase] = {
    wirelessMvnoCustBase.as("A")
      .join(xmsmTiebreaker.as("B"), Seq("zip_rc_kblock"), "left")
      .join(broadcast(dCommonOCN).as("C"), $"A.previous_holder_sp" === $"C.ocn_common_dim_id",
        "left")
      .join(broadcast(dCommonOCN).as("D"), $"A.current_holder_sp" === $"D.ocn_common_dim_id",
        "left")
      .join(broadcast(npaNpaComplexLookup), Seq("npa"), "left")
      .filter(Utils.and(
        $"noncompetitive_ind" === lit(0),
        $"customer_base_date".between(startDate, endDate),
        Utils.or(
          $"C.common_name_mode" === lit("W"),
          $"C.common_name_mode".isNull
        ),
        Utils.or(
          $"D.common_name_mode" === lit("W"),
          $"D.common_name_mode".isNull
        ),
        $"A.current_holder_mvno_sp" =!= lit(5932),
        $"A.previous_holder_mvno_sp" =!= lit(5932),
        $"total_wins" > lit(0.0000),
        Utils.or(
          $"A.current_holder_mvno_sp".isin(mvnoSPList1: _*),
          $"A.current_holder_mvno_sp".isin(mvnoSPList2: _*),
          $"mvno_winning_plan_type_id".isin(List(1, 2): _*)
        ),
        Utils.or(
          $"A.previous_holder_mvno_sp".isin(mvnoSPList1: _*),
          $"A.previous_holder_mvno_sp".isin(mvnoSPList2: _*),
          $"mvno_losing_plan_type_id".isin(List(1, 2): _*)
        )
      )).withColumn("current_holder_sp",
      GeoAllocationFunctions.generateHolderSP($"A.current_holder_mvno_sp", $"B.current_holder_mvno_sp")
    ).withColumn("current_holder_plan_type_id",
      GeoAllocationFunctions.generateHolderPlanTypeID($"A.current_holder_mvno_sp", $"mvno_winning_plan_type_id", mvnoSPList1, mvnoSPList2)
    ).withColumn("previous_holder_sp",
      GeoAllocationFunctions.generateHolderSP($"A.previous_holder_mvno_sp", $"B.current_holder_mvno_sp")
    ).withColumn("previous_holder_plan_type_id",
      GeoAllocationFunctions.generateHolderPlanTypeID($"A.previous_holder_mvno_sp", $"mvno_losing_plan_type_id", mvnoSPList1, mvnoSPList2)
    ).groupBy(
      $"customer_base_date",
      $"zip_rc_kblock",
      $"npa",
      $"npa_complex_cld",
      $"current_holder_sp",
      $"current_holder_plan_type_id",
      $"previous_holder_sp",
      $"previous_holder_plan_type_id",
    ).agg(
      sum("total_wins").as("ported_tn_adds"),
    ).select(
      "customer_base_date",
      "zip_rc_kblock",
      "npa",
      "npa_complex_cld",
      "current_holder_sp",
      "current_holder_plan_type_id",
      "previous_holder_sp",
      "previous_holder_plan_type_id",
      "ported_tn_adds",
    ).as[RateCenterNPAAddsPortedCustomerBase]
  }

  /**
    * Compute rate center adds Intra MNO's
    * @param wirelessMovementWide: Wireless Movement Wide generic dataset
    * @param industryModelTotalOutput: MVNO Industry Model total output
    * @param startDate: Start Date
    * @param endDate: End Date
    * @return Returns a dataset of rate center adds Intra MNO's
    */
  def computeRateCenterAddsIntraMNOs(
                                      wirelessMovementWide: Dataset[WirelessMovementWideMonthlyAgg],
                                      industryModelTotalOutput: Dataset[IndustryModelTotalOutput],
                                      startDate: Date,
                                      endDate: Date
  ): Dataset[RateCenterAddsIntraMNO] = {

    val distinctSPData: Dataset[distinctSPDataTotalMktShare] = industryModelTotalOutput
      .filter(Utils.and(
        $"churn_losing_mno_sp" === $"churn_winning_mno_sp",
        $"churn_losing_mvno_sp" =!= $"churn_winning_mvno_sp",
        $"churn_losing_mvno_sp".isNotNull,
        $"churn_winning_mvno_sp".isNotNull,
        $"churn_losing_mvno_sp" =!= lit(6),
        $"churn_winning_mvno_sp" =!= lit(6),
      )
      ).select(
      "churn_losing_mvno_sp",
      "churn_winning_mvno_sp"
    ).distinct().as[distinctSPDataTotalMktShare]

    wirelessMovementWide.join(broadcast(distinctSPData), Utils.and(
      wirelessMovementWide("primary_sp") === distinctSPData("churn_winning_mvno_sp"),
      wirelessMovementWide("secondary_sp") === distinctSPData("churn_losing_mvno_sp")
    ), "inner"
    ).filter(
      $"month".between(startDate, endDate),
    ).groupBy(
      $"zip_cd",
      $"month",
      $"primary_sp",
      $"primary_plan_type_id",
      $"secondary_sp",
      $"secondary_plan_type_id",
    ).agg(
      sum("adjusted_wins").as("ported_tn_adds")
    ).select(
      $"month".as("customer_base_date"),
      $"zip_cd".as("zip_rc_kblock"),
      $"primary_sp".as("current_holder_sp"),
      $"primary_plan_type_id".as("current_holder_plan_type_id"),
      $"secondary_sp".as("previous_holder_sp"),
      $"secondary_plan_type_id".as("previous_holder_plan_type_id"),
      $"ported_tn_adds"
    ).as[RateCenterAddsIntraMNO]
  }
}

object RateCenterPortedTNCustBaseAgg {

  def apply()(implicit spark: SparkSession): RateCenterPortedTNCustBaseAgg = new RateCenterPortedTNCustBaseAgg()

}
