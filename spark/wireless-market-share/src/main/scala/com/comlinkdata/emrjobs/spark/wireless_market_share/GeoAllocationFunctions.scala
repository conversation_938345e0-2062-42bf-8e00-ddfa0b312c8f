package com.comlinkdata.emrjobs.spark.wireless_market_share

import org.apache.spark.sql.{Column, SparkSession}
import org.apache.spark.sql.functions.when

object GeoAllocationFunctions {

  /**
    * Brand aggregation for mvno_sp values, IE 6650/6651 = Verizon prepaid, so 1 is assigned
    * @param holderMvnoSP holder_mvno_sp column
    * @param holderMvnoSPXMSM holder_mvno_sp column from xmsm_tiebreaker
    * @return carrier values (IE 1 for Verizon)
    */
  def generateHolderSP(holderMvnoSP: Column, holderMvnoSPXMSM: Column)(implicit spark: SparkSession): Column = {
    when(holderMvnoSP.isin(List(6650, 6651): _*), 1)
      .when(holderMvnoSP === 6649, holderMvnoSPXMSM) // current_holder_mvno_sp_xmsm = b
      .when(holderMvnoSP.isin(List(2620, 6050, 178): _*), 6576)
      .when(holderMvnoSP === 6043, 609)
      .when(holderMvnoSP === 6042, 6577)
      .when(holderMvnoSP.isin(List[Int](6711, 6712, 6722, 6723, 6724): _*), 7)
      .otherwise(holderMvnoSP)
  }

  /**
    * Brand aggregation for holder_plan_type_id values, IE 6650/6651 = Verizon prepaid, so 1 is assigned
    * @param holderMvnoSP holder_mvno_sp column
    * @param planTypeId plan_type_id column
    * @param mvnoSPList1 values for where plan_type_id = 1
    * @param mvnoSPList2 values for where plan_type_id = 1
    * @return plan type IDs
    */
  def generateHolderPlanTypeID(
    holderMvnoSP: Column,
    planTypeId: Column,
    mvnoSPList1: List[Int],
    mvnoSPList2: List[Int])(implicit spark: SparkSession): Column = {
    when(holderMvnoSP.isin(mvnoSPList2: _*), 2)
      .when(holderMvnoSP.isin(mvnoSPList1: _*), 1)
      .otherwise(planTypeId)
  }
}
