package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.largescale.commons.{RedshiftUtils, Spark<PERSON><PERSON>, SparkJ<PERSON><PERSON><PERSON><PERSON>, Utils}
import com.comlinkdata.largescale.schema.Netscribes._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SaveMode, SparkSession}
import java.time.LocalDate
import java.net.URI

/**
  * Case class representing the configuration for the Redshift Upload Job.
  *
  * @param processingDate                     Optional date for processing the data
  * @param netscribesWirelessPostpaidDataPath URI for the wireless postpaid data
  * @param netscribesWirelessPrepaidDataPath  URI for the wireless prepaid data
  * @param redshiftTableSchema                Schema of the Redshift table
  * @param redshiftTableNamePostpaid          Table name for the postpaid data in Redshift
  * @param redshiftTableNamePrepaid           Table name for the prepaid data in Redshift
  * @param redshiftJdbcEndpoint               JDBC endpoint for connecting to Redshift
  * @param redshiftUserName                   Username for connecting to Redshift
  * @param redshiftParameterStoreKey          AWS Parameter Store key for retrieving Redshift credentials
  * @param redshiftTempLocation               Temporary S3 location for Redshift uploads
  * @param maxDaysToRun                       Maximum number of days the job can run
  */

case class RedshiftUploadNetscribesJobConfig(
  processingDate: Option[LocalDate],
  netscribesWirelessPostpaidDataPath: URI,
  netscribesWirelessPrepaidDataPath: URI,
  redshiftTableSchema: String,
  redshiftTableNamePostpaid: String,
  redshiftTableNamePrepaid: String,
  redshiftJdbcEndpoint: String,
  redshiftUserName: String,
  redshiftParameterStoreKey: String,
  redshiftTempLocation: String,
  maxDaysToRun: Int
)

object RedshiftUploadNetscribesJobConfig {

  val sample: RedshiftUploadNetscribesJobConfig = RedshiftUploadNetscribesJobConfig(
    processingDate = Option(LocalDate.of(2024, 1, 1)),
    netscribesWirelessPostpaidDataPath = URI create "s3://externaldata-nabia-comlinkdata-com/to_opensignal/USA/staging/edits/wireless_postpaid/netscribes/",
    netscribesWirelessPrepaidDataPath = URI create "s3://externaldata-nabia-comlinkdata-com/to_opensignal/USA/staging/edits/wireless_prepaid/netscribes/",
    redshiftTableSchema = "netscribes",
    redshiftTableNamePostpaid = "d_netscribes_wireless_postpaid",
    redshiftTableNamePrepaid = "d_netscribes_wireless_prepaid",
    redshiftJdbcEndpoint = "******************************************************************************",
    redshiftUserName = "agg_loader_dev",
    redshiftParameterStoreKey = "/c200/redshift_agg_loader_dev",
    redshiftTempLocation = "s3://d000-comlinkdata-com/redshift-uploads/netscribes",
    maxDaysToRun = 4 // Maximum number of days to run
  )
}

object RedshiftUploadNetscribesJob extends SparkJob[RedshiftUploadNetscribesJobConfig](RedshiftUploadNetscribesJobRunner)

object RedshiftUploadNetscribesJobRunner extends SparkJobRunner[RedshiftUploadNetscribesJobConfig] with LazyLogging {
  def runJob(config: RedshiftUploadNetscribesJobConfig)(implicit spark: SparkSession): Unit = {

    val maxDays = config.maxDaysToRun
    var currentDay = config.processingDate.getOrElse(LocalDate.now())

    for (_ <- 0 until maxDays) {
      val year = currentDay.getYear
      val month = currentDay.getMonthValue.formatted("%02d")
      val day = currentDay.getDayOfMonth.formatted("%02d")
      val finalPath_postpaid = Utils.joinPaths(config.netscribesWirelessPostpaidDataPath, s"year=$year", s"month=$month", s"day=$day")
      val finalPath_prepaid = Utils.joinPaths(config.netscribesWirelessPrepaidDataPath, s"year=$year", s"month=$month", s"day=$day")

      if (dataExistsInRedshift(currentDay, config)) {
        logger.info(s"Data exists in Redshift for date: $currentDay. Skipping loading for this date.")
      } else {
        logger.info(s"Data missing in Redshift for date: $currentDay. Proceeding to load data.")
        loadDataIntoRedshift(finalPath_postpaid, finalPath_prepaid, currentDay, config)(spark)
      }
      currentDay = currentDay.minusDays(1)
    }
  }

  /**
    * Checks if data for the specified processing day exists in the Redshift tables.
    *
    * @param processingDay The date for processing the data.
    * @param config        Configuration object containing Redshift settings.
    * @return True if data exists in both postpaid and prepaid tables for the processing day, false otherwise.
    */

  private def dataExistsInRedshift(processingDay: LocalDate, config: RedshiftUploadNetscribesJobConfig): Boolean = {
    import java.sql.DriverManager
    import java.sql.Connection

    var connection: Connection = null
    try {
      Class.forName("com.amazon.redshift.jdbc42.Driver")
      connection = DriverManager.getConnection(config.redshiftJdbcEndpoint, config.redshiftUserName, config.redshiftParameterStoreKey)

      val postpaidTable = s"${config.redshiftTableSchema}.${config.redshiftTableNamePostpaid}"
      val prepaidTable = s"${config.redshiftTableSchema}.${config.redshiftTableNamePrepaid}"

      val query =
        s"""
        SELECT
          (SELECT COUNT(*) FROM $postpaidTable WHERE date = '$processingDay') AS postpaid_count,
          (SELECT COUNT(*) FROM $prepaidTable WHERE date = '$processingDay') AS prepaid_count
      """

      val statement = connection.createStatement()
      val resultSet = statement.executeQuery(query)

      if (resultSet.next()) {
        val postpaidCount = resultSet.getInt("postpaid_count")
        val prepaidCount = resultSet.getInt("prepaid_count")
        postpaidCount > 0 && prepaidCount > 0
      } else {
        false
      }
    } catch {
      case e: Exception =>
        logger.error("Error checking data existence in Redshift", e)
        false
    } finally {
      if (connection != null) connection.close()
    }
  }

  /**
    * Loads wireless postpaid and prepaid data into Redshift.
    *
    * @param postpaidPath  URI path to the wireless postpaid data.
    * @param prepaidPath   URI path to the wireless prepaid data.
    * @param processingDay The date for processing the data.
    * @param config        Configuration object containing Redshift settings.
    * @param spark         Implicit SparkSession for Spark operations.
    */

  private def loadDataIntoRedshift(postpaidPath: URI, prepaidPath: URI, processingDay: LocalDate, config: RedshiftUploadNetscribesJobConfig)(implicit spark: SparkSession): Unit = {
    logger.info(s"Reading Wireless Postpaid data from the path = $postpaidPath")
    val wirelessPostpaidData = WirelessPostpaidData.read(postpaidPath)

    logger.info(s"Reading Wireless Prepaid data from the path = $prepaidPath")
    val wirelessPrepaidData = WirelessPrepaidData.read(prepaidPath)

    val rsConfig = RedshiftUtils.RedshiftConfig(
      rsTemporaryLocation = URI create config.redshiftTempLocation,
      rsJdbcEndpoint = config.redshiftJdbcEndpoint,
      rsUserName = config.redshiftUserName,
      rsParameterStoreKey = config.redshiftParameterStoreKey
    )

    // Load Netscribes Wireless Postpaid data in redshift
    val wirelessPostpaidRSTableName = s"${config.redshiftTableSchema}.${config.redshiftTableNamePostpaid}"
    logger.info(s"Writing wirelessPostpaid data to the redshift table: $wirelessPostpaidRSTableName")
    val preActionQueryPostpaid = s"DELETE FROM $wirelessPostpaidRSTableName WHERE date = '$processingDay';"
    RedshiftUtils.redshiftWrite(
      wirelessPostpaidData,
      wirelessPostpaidRSTableName,
      SaveMode.Append,
      preActionQuery = Some(preActionQueryPostpaid)
    )(rsConfig)

    // Load Netscribes Wireless Prepaid data in redshift
    val wirelessPrepaidRSTableName = s"${config.redshiftTableSchema}.${config.redshiftTableNamePrepaid}"
    logger.info(s"Writing wirelessPrepaid data to the redshift table: $wirelessPrepaidRSTableName")
    val preActionQueryPrepaid = s"DELETE FROM $wirelessPrepaidRSTableName WHERE date = '$processingDay';"
    RedshiftUtils.redshiftWrite(
      wirelessPrepaidData,
      wirelessPrepaidRSTableName,
      SaveMode.Append,
      preActionQuery = Some(preActionQueryPrepaid)
    )(rsConfig)
  }
}
