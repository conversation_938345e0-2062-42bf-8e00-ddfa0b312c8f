package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.largescale.schema.wireless_market_share.{BrandPlantypeLookup, CbgPctOfDmaGeorollups, DmaFinalOutputAllDemo, MonthlyOutputWithEthnicityDMA}
import com.comlinkdata.emrjobs.spark.wireless_market_share.Packaging
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.io.dataset.dynamicAppend
import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON>, SparkJobRunner, Utils}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.max
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}
import org.apache.spark.sql.functions._

import java.time.LocalDate
import java.sql.Date
import java.net.URI

/**
  * Case class to store the configuration for Packaging Job.
  *
  * @param monthToProcess                   Specifies the month to process data for.
  * @param creationDate                     Date of data creation, defaults to max creation date if None.
  * @param monthlyOutputWithEthnicityDmaLoc Location URI for the monthly output data including ethnicity and DMA.
  * @param brandPlantypeLookupLoc           URI for brand plan type lookup data.
  * @param cbgPctOfDmaGeoRollupsLoc         URI for CBG percentage of DMA georollups data.
  * @param dmaFinalOutputAllDemoLoc         URI for the final DMA output across demographic combinations.
  * @param outputBasePath                   Base URI for output data.
  * @param outputPartitions                 Number of partitions for output data.
  */

case class PackagingJobConfig(
  monthToProcess: Option[LocalDate],
  creationDate: Option[LocalDate],
  monthlyOutputWithEthnicityDmaLoc: URI,
  brandPlantypeLookupLoc: URI,
  cbgPctOfDmaGeoRollupsLoc: URI,
  dmaFinalOutputAllDemoLoc: URI,
  outputBasePath: URI,
  outputPartitions: Int
)

object PackagingJobConfig {

  val sample: PackagingJobConfig = PackagingJobConfig(
    monthToProcess = Option(LocalDate.of(2024, 10, 1)),
    creationDate = Option(LocalDate.of(2024, 6, 10)),
    monthlyOutputWithEthnicityDmaLoc = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/inputs/packaging/",
    brandPlantypeLookupLoc = URI create "s3://c400-athena-dev-comlinkdata-com/wms_ethnicity_test/brand_plantype_lookup/",
    cbgPctOfDmaGeoRollupsLoc = URI create "s3://c400-athena-dev-comlinkdata-com/wms_ethnicity_test/cbg_pct_of_dma_v10_3_2_20240822_restatement/",
    dmaFinalOutputAllDemoLoc = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging/dma_final_output_all_demo_combinations/",
    outputBasePath = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging/",
    outputPartitions = 1
  )
}

/**
  * PackagingJobConfig object contains a sample configuration for running the job.
  * Provides default URIs and parameters for testing purposes.
  */

object PackagingJob extends SparkJob[PackagingJobConfig](PackagingJobRunner)

object PackagingJobRunner extends SparkJobRunner[PackagingJobConfig] with LazyLogging {

  def runJob(config: PackagingJobConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    val monthToProcess = config.monthToProcess.getOrElse(LocalDate.now().minusMonths(1).withDayOfMonth(1))

    // ** Read input data from S3 **

    val year = monthToProcess.getYear
    val month = monthToProcess.getMonthValue.formatted("%02d")
    val wmsDMAPath = Utils.joinPaths(
      URI create s"${config.monthlyOutputWithEthnicityDmaLoc.toString}", s"year=$year", s"month=$month"
    )
    val wmsDMAFinalPath = URI create generateNewROutputPath(wmsDMAPath)
    logger.info(s"Loading dma monthly demographics output data from the path: ${wmsDMAFinalPath}")
    val monthlyOutputWithEthnicityDMAData = MonthlyOutputWithEthnicityDMA.read_csv(wmsDMAFinalPath)

    logger.info(s"Loading brand plan type lookup data from the path: ${config.brandPlantypeLookupLoc}")
    val brandPlantypeLookupData = BrandPlantypeLookup.read(config.brandPlantypeLookupLoc)

    logger.info(s"Loading cbg pct of dma georollups data from the path: ${config.cbgPctOfDmaGeoRollupsLoc}")
    val cbgPctOfDmaGeorollupsData = CbgPctOfDmaGeorollups.read(config.cbgPctOfDmaGeoRollupsLoc)

    logger.info(s"Loading dma final output all demo combinations data from the path: ${config.dmaFinalOutputAllDemoLoc}")
    val dmaFinalOutputAllDemoCombinationsData = DmaFinalOutputAllDemo.read(config.dmaFinalOutputAllDemoLoc)

    // ** Perform calculations on the data **

    val creationDate = config.creationDate.getOrElse(getMaxCreationDate(dmaFinalOutputAllDemoCombinationsData.toDF(), monthToProcess)).toString

    logger.info(s"Calculating the DMA final output all demo combinations data...")
    val dmaFinalOutputAllDemoData = Packaging().dmaFinalOutputAllDemo(
      monthlyOutputWithEthnicityDMAData, brandPlantypeLookupData, monthToProcess
    )

    logger.info(s"Calculating the VPGM from DMA final output all demo combinations data...")
    val vpgmFromDmaFinalOutputAllDemoData = Packaging().vpgmFromDmaFinalOutputAllDemo(
      dmaFinalOutputAllDemoData, cbgPctOfDmaGeorollupsData, monthToProcess
    )

    logger.info(s"Calculating the MSO/ILEC Footprints final output all demo combinations data...")
    val msoIlecFootprintsFinalOutputAlldemoData = Packaging().msoIlecFootprintsFinalOutputAllDemo(
      dmaFinalOutputAllDemoData, cbgPctOfDmaGeorollupsData, creationDate
    )

    logger.info(s"Calculating the CMA final output all demo combinations data...")
    val cmaFinalOutputAllDemoData = Packaging().cmaFinalOutputAllDemo(
      dmaFinalOutputAllDemoData, cbgPctOfDmaGeorollupsData, creationDate
    )

    logger.info(s"Calculating the DMA final output pairwise demo combinations data...")
    val dmaFinalOutputDataPairwiseDemo = Packaging().dmaFinalOutputPairwiseDemo(
      monthlyOutputWithEthnicityDMAData, brandPlantypeLookupData, monthToProcess
    )

    logger.info(s"Calculating the VPGM from DMA final output pairwise demo combinations data...")
    val vpgmFromFinalOutputDataPairwiseDemo = Packaging().vpgmFromDmaFinalOutputPairwiseDemo(
      vpgmFromDmaFinalOutputAllDemoData
    )

    logger.info(s"Calculating the MSO/ILEC footprints final output pairwise demo combinations data...")
    val msoIlecFinalOutputDataPairwiseDemo = Packaging().msoIlecFootprintsFinalOutputPairwiseDemo(
      msoIlecFootprintsFinalOutputAlldemoData, creationDate
    )

    logger.info(s"Calculating the CMA final output pairwise demo combinations data...")
    val cmaFinalOutputDataPairwiseDemo = Packaging().cmaFinalOutputPairwiseDemo(
      cmaFinalOutputAllDemoData, creationDate
    )

    logger.info(s"Calculating the Boost Market final output all demo combinations data...")
    val boostMarketFinalOutputAllDemo = Packaging().boostMarketFinalOutputAllDemo(
      dmaFinalOutputAllDemoData, cbgPctOfDmaGeorollupsData, monthToProcess
    )

    logger.info(s"Calculating the Boost Market final output pairwise demo combinations data...")
    val boostMarketFinalOutputPairwiseDemo = Packaging().boostMarketFinalOutputPairwiseDemo(
      boostMarketFinalOutputAllDemo
    )

    // ** Write results to S3 **

    val dmaPath = URI.create(Utils.joinPaths(config.outputBasePath.toString, s"/dma_final_output_all_demo_combinations/"))
    logger.info(s"Writing dma final output all demo combinations data to the S3 location = $dmaPath")
    dynamicAppend(dmaFinalOutputAllDemoData, dmaPath, config.outputPartitions, "creation_date")

    val vpgmFromDmaPath = URI.create(Utils.joinPaths(config.outputBasePath.toString, s"/vpgm_final_output_all_demo_combinations/"))
    logger.info(s"Writing vpgm from dma final output all demo combinations data to the S3 location = $vpgmFromDmaPath")
    dynamicAppend(vpgmFromDmaFinalOutputAllDemoData, vpgmFromDmaPath, config.outputPartitions, "creation_date")

    val msoIlecFootprintsPath = URI.create(Utils.joinPaths(config.outputBasePath.toString, s"/mso_ilec_final_output_all_demo_combinations/"))
    logger.info(s"Writing mso ilec footprints final output all demo combinations data to the S3 location = $msoIlecFootprintsPath")
    dynamicAppend(msoIlecFootprintsFinalOutputAlldemoData, msoIlecFootprintsPath, config.outputPartitions, "creation_date")

    val cmaPath = URI.create(Utils.joinPaths(config.outputBasePath.toString, s"/cma_final_output_all_demo_combinations/"))
    logger.info(s"Writing cma final output all demo combinations data to the S3 location = $cmaPath")
    dynamicAppend(cmaFinalOutputAllDemoData, cmaPath, config.outputPartitions, "creation_date")

    val dmaPairwisePath = URI.create(Utils.joinPaths(config.outputBasePath.toString, s"/dma_pairwise_final_output_all_demo_combinations/"))
    logger.info(s"Writing dma final output pairwise demo combinations data to the S3 location = $dmaPairwisePath")
    dynamicAppend(dmaFinalOutputDataPairwiseDemo, dmaPairwisePath, config.outputPartitions, "creation_date")

    val vpgmFromDmaPairwisePath = URI.create(Utils.joinPaths(config.outputBasePath.toString, s"/vpgm_pairwise_final_output_all_demo_combinations/"))
    logger.info(s"Writing vpgm from dma final output pairwise demo combinations data to the S3 location = $vpgmFromDmaPairwisePath")
    dynamicAppend(vpgmFromFinalOutputDataPairwiseDemo, vpgmFromDmaPairwisePath, config.outputPartitions, "creation_date")

    val msoIlecFootprintsPairwisePath = URI.create(Utils.joinPaths(config.outputBasePath.toString, s"/mso_ilec_pairwise_final_output_all_demo_combinations/"))
    logger.info(s"Writing mso ilec footprint final output pairwise demo combinations data to the S3 location = $msoIlecFootprintsPairwisePath")
    dynamicAppend(msoIlecFinalOutputDataPairwiseDemo, msoIlecFootprintsPairwisePath, config.outputPartitions, "creation_date")

    val cmaPairwisePath = URI.create(Utils.joinPaths(config.outputBasePath.toString, s"/cma_pairwise_final_output_all_demo_combinations/"))
    logger.info(s"Writing cma final output pairwise demo combinations data to the S3 location = $cmaPairwisePath")
    dynamicAppend(cmaFinalOutputDataPairwiseDemo, cmaPairwisePath, config.outputPartitions, "creation_date")

    val boostMarketPath = URI.create(Utils.joinPaths(config.outputBasePath.toString, s"/boost_market_final_output_all_demo_combinations/"))
    logger.info(s"Writing boost market final output all demo combinations data to the S3 location = $boostMarketPath")
    dynamicAppend(boostMarketFinalOutputAllDemo, boostMarketPath, config.outputPartitions, "creation_date")

    val boostMarketPairwisePath = URI.create(Utils.joinPaths(config.outputBasePath.toString, s"/boost_market_final_output_pairwise_demo_combinations/"))
    logger.info(s"Writing boost market final output pairwise demo combinations data to the S3 location = $boostMarketPairwisePath")
    dynamicAppend(boostMarketFinalOutputPairwiseDemo, boostMarketPairwisePath, config.outputPartitions, "creation_date")
  }

  /**
    * Gets the maximum creation date for a specified month from the DataFrame.
    *
    * @param df             Input DataFrame containing "month" and "creation_date" columns.
    * @param monthToProcess The month to filter on and find max "creation_date" for.
    * @return               The maximum creation date as a java.sql.Date.
    */

  private def getMaxCreationDate(df: DataFrame, monthToProcess: LocalDate): java.sql.Date = {

    // Step 1: Filter DataFrame where "month" matches the monthToProcess
    val filteredDf = df.filter(col("month") === monthToProcess)

    // Step 2: Get the maximum "creation_date" for the filtered month
    val maxCreationDate = filteredDf
      .agg(max("creation_date").alias("max_creation_date"))
      .collect()
      .headOption
      .flatMap(row => Option(row.getAs[java.sql.Date]("max_creation_date")))

    maxCreationDate.getOrElse(Date.valueOf(LocalDate.now()))
  }

  private def generateNewROutputPath(basePath: URI)(implicit spark: SparkSession): String = {
    val futz = CldFileUtils.newBuilder.forUri(basePath).build
    val (inputPath, latestR) = Utils.maxPartitionValue(basePath, "r", futz, None)  // latest revision
    inputPath.toString
  }
}
