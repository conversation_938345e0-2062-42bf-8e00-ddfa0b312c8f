package com.comlinkdata.emrjobs.spark.wireless_market_share.inputs

import com.comlinkdata.emrjobs.spark.wireless_market_share.GeoAllocationFunctions
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import org.apache.spark.sql.{Column, Dataset, SparkSession}
import com.comlinkdata.largescale.schema.wireless_market_share._
import com.comlinkdata.largescale.schema.wireless_market_share.lookup._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions.{broadcast, lit, sum, when}
import org.apache.spark.sql.types.{DoubleType, IntegerType}

import java.sql.Date


class NationalCustomerBaseInputs(implicit spark: SparkSession) extends LazyLogging {

  import spark.implicits._

  /**
    * Find the more common of current_holder_mvno_sp between 6052 and 6105
    * to overwrite 6649 sps.
    *
    * @param custBase mvno.a_customer_base_wireless_monthly_mvno_v4_total5
    * @param custBaseDate date used with mvno custbase, 2021-12-01 in original run
    * @return Dataset[XMSMTiebreaker]
    */
  def computeXMSMTiebreaker(custBase: Dataset[MvnoCustomerBase], custBaseDate: Date):
  Dataset[XMSMTiebreaker] = {
    custBase
      .where($"current_holder_mvno_sp".isin(List[Int](6052, 6105): _*) && $"customer_base_date" === custBaseDate)
      .groupBy($"zip_rc_kblock")
      .agg(
        sum(when($"current_holder_mvno_sp" === 6052, $"total_customers")
          .otherwise(0)) as "customers_6052",
        sum(when($"current_holder_mvno_sp" === 6105, $"total_customers")
          .otherwise(0)) as "customers_6105"
      )
      .select($"zip_rc_kblock", when($"customers_6052" > $"customers_6105", 6052)
        .otherwise(6105) as "current_holder_mvno_sp")
      .as[XMSMTiebreaker]
  }

  /**
    * Get beginning of month subs and losses from among those starting subs that happened during the month
    * roll-up SPs to match industry model SPs and overwrite plan_types for deterministic plan-type SPs ando
    * overwrite any value of 6649 with the tiebreaker value of 6052/6105.
    *
    * @param custBase mvno.a_customer_base_wireless_monthly_mvno_v4_total5
    * @param xmsmTiebreaker 2.1 output
    * @param dCommonOCN cld.d_common_ocn
    * @param mvnoSPList1 list of mvno_sp's that evaluate to 1
    * @param mvnoSPList2 list of mvno_sp's that evaluate to 2
    * @param startDate start date for processing
    * @param endDate end date for processing
    * @return
    */
  def computeNationalSubscriberMultipliersPortedCustomerBase(
    custBase: Dataset[MvnoCustomerBase],
    xmsmTiebreaker: Dataset[XMSMTiebreaker],
    dCommonOCN: Dataset[DCommonOcnLookup],
    mvnoSPList1: List[Int],
    mvnoSPList2: List[Int],
    startDate: Date,
    endDate: Date): Dataset[NationalSubscriberMultipliersPortedCustomerBase] = {
    val custBaseFiltered = custBase
      .filter($"customer_base_date".between(startDate, endDate))
      .filter($"noncompetitive_ind" === 0)
      .filter($"current_holder_mvno_sp" =!= 5932)
      .filter($"previous_holder_mvno_sp" =!= 5932)
      .filter(
        $"current_holder_mvno_sp".isin(mvnoSPList1 ++ mvnoSPList2: _*) ||
          $"mvno_winning_plan_type_id".isin(List(1, 2): _*))
      .withColumn("ocn_common_dim_id", $"previous_holder_sp")

    val agg =
    custBaseFiltered
      .join(
        broadcast(xmsmTiebreaker.withColumnRenamed("current_holder_mvno_sp", "current_holder_mvno_sp_xmsm")),
        Seq("zip_rc_kblock"),
        "LEFT")
      .join(broadcast(dCommonOCN), Seq("ocn_common_dim_id"), "LEFT")
      .where($"common_name_mode" === "W" || $"common_name_mode".isNull)
      .withColumn("current_holder_sp",
        GeoAllocationFunctions.generateHolderSP($"current_holder_mvno_sp", $"current_holder_mvno_sp_xmsm")
      )
      .withColumn("current_holder_plan_type_id",
        GeoAllocationFunctions.generateHolderPlanTypeID($"current_holder_mvno_sp", $"mvno_winning_plan_type_id", mvnoSPList1, mvnoSPList2)
      )
      .groupBy(
        $"customer_base_date",
        $"current_holder_sp",
        $"current_holder_plan_type_id")
      .agg(
        sum($"total_customers") as "ported_tn_subscribers",
        sum($"total_losses") as "losses"
      )
      agg
      .select(
        $"customer_base_date",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"ported_tn_subscribers",
        $"losses"
      )
      .as[NationalSubscriberMultipliersPortedCustomerBase]
  }

  /**
    * Get ported TN wins during the month for each combination of winning/losing SP/plan_type
    * roll-up SPs to match industry model SPs and overwrite plan_types for deterministic plan-type
    * SPs overwrite any value of 6649 with the tiebreaker value of 6052/6105.
    *
    * @param custBase       mvno.a_customer_base_wireless_monthly_mvno_v4_total5
    * @param xmsmTiebreaker 2.1 output
    * @param dCommonOCN     cld.d_common_ocn
    * @param mvnoSPList1    list of mvno_sp's that evaluate to 1
    * @param mvnoSPList2    list of mvno_sp's that evaluate to 2
    * @param startDate      start date for processing
    * @param endDate        end date for processing
    * @return Dataset[NationalAddMultipliersPortedCustomerBase]
    */
  def computeNationalAddMultipliersPortedCustomerBase(
    custBase: Dataset[MvnoCustomerBase],
    xmsmTiebreaker: Dataset[XMSMTiebreaker],
    dCommonOCN: Dataset[DCommonOcnLookup],
    mvnoSPList1: List[Int],
    mvnoSPList2: List[Int],
    startDate: Date,
    endDate: Date):
    Dataset[NationalAddMultipliersPortedCustomerBase] = {
    val xmsmTiebreakerRenamed = xmsmTiebreaker
      .withColumnRenamed("current_holder_mvno_sp", "current_holder_mvno_sp_xmsm")

    custBase
      .join(broadcast(xmsmTiebreakerRenamed), Seq("zip_rc_kblock"), "LEFT")
      .filter($"customer_base_date".between(startDate, endDate))
      .filter($"noncompetitive_ind" === 0)
      .filter($"current_holder_mvno_sp" =!= 5932)
      .filter($"previous_holder_mvno_sp" =!= 5932)
      .filter($"total_wins" > 0.0)
      .filter(
          $"current_holder_mvno_sp".isin(mvnoSPList1 ++ mvnoSPList2: _*) ||
            $"mvno_winning_plan_type_id".isin(List(1, 2): _*))
      .filter(
          $"previous_holder_mvno_sp".isin(mvnoSPList1 ++ mvnoSPList2: _*) ||
            $"mvno_losing_plan_type_id".isin(List(1, 2): _*))
      .join(broadcast(dCommonOCN), $"previous_holder_sp" === $"ocn_common_dim_id", "LEFT")
      .join(
          broadcast(dCommonOCN
            .withColumnRenamed("ocn_common_dim_id", "ocn_common_dim_id_2")
            .withColumnRenamed("common_name_mode","common_name_mode_2")),
          $"current_holder_sp" === $"ocn_common_dim_id_2",
          "LEFT")
      .filter($"common_name_mode" === "W" || $"common_name_mode".isNull)
      .filter($"common_name_mode_2" === "W" || $"common_name_mode_2".isNull)
      .withColumn("current_holder_sp",
        GeoAllocationFunctions.generateHolderSP($"current_holder_mvno_sp",$"current_holder_mvno_sp_xmsm")
      )
      .withColumn("current_holder_plan_type_id",
        GeoAllocationFunctions.generateHolderPlanTypeID($"current_holder_mvno_sp",$"mvno_winning_plan_type_id", mvnoSPList1, mvnoSPList2)
      )
      .withColumn("previous_holder_sp",
        GeoAllocationFunctions.generateHolderSP($"previous_holder_mvno_sp",$"current_holder_mvno_sp_xmsm")
      )
      .withColumn("previous_holder_plan_type_id",
        GeoAllocationFunctions.generateHolderPlanTypeID($"previous_holder_mvno_sp",$"mvno_losing_plan_type_id", mvnoSPList1, mvnoSPList2)
      )
      .groupBy(
          $"customer_base_date",
          $"current_holder_sp",
          $"current_holder_plan_type_id",
          $"previous_holder_sp",
          $"previous_holder_plan_type_id"
        )
      .agg(
        sum($"total_wins") as "adds"
      )
      .select($"customer_base_date", $"current_holder_sp", $"current_holder_plan_type_id", $"previous_holder_sp", $"previous_holder_plan_type_id", $"adds")
      .as[NationalAddMultipliersPortedCustomerBase]
  }

  /**
    * Get intra-MNO aggregate wins during the month for each combination of winning/losing SP/plan_type
    * Metro (SP 6) treated as special case
    *
    * @param wirelessMovement nobbs_playpen.a_wireless_movement_wide_generic_v440_monthly_20220811
    * @param marketShareTotalOutput cld.mktshare_total_output7_oct2022
    * @param startDate start date for processing
    * @param endDate end date for processing
    * @return
    */
  def computeNationalAddMultipliersIntraMnoInputs(
                                                   wirelessMovement: Dataset[WirelessMovementWideMonthlyAgg],
                                                   marketShareTotalOutput: Dataset[IndustryModelTotalOutput],
                                                   startDate: Date,
                                                   endDate: Date
  ): Dataset[NationalAddMultipliersIntraMnoInputs] = {
      import spark.implicits._

    val a_trimmed = wirelessMovement.select(
      $"month".alias("customer_base_date"),
      $"primary_sp".cast("int").alias("current_holder_sp"),
      $"primary_plan_type_id".cast("int").alias("current_holder_plan_type_id"),
      $"secondary_sp".cast("int").alias("previous_holder_sp"),
      $"secondary_plan_type_id".cast("int").alias("previous_holder_plan_type_id"),
      $"adjusted_wins"
    )
    .groupBy(
      "customer_base_date",
      "current_holder_sp",
      "current_holder_plan_type_id",
      "previous_holder_sp",
      "previous_holder_plan_type_id")
      .agg(sum("adjusted_wins").alias("adds")
    )
    val b_trimmed = marketShareTotalOutput.select(
      $"churn_losing_mvno_sp".cast("int"),
      $"churn_winning_mvno_sp".cast("int"),
      $"churn_losing_mno_sp".cast("int"),
      $"churn_winning_mno_sp".cast("int")
    )
    .where($"churn_losing_mvno_sp".isNotNull)
    .where($"churn_winning_mvno_sp".isNotNull)
    .where($"churn_losing_mvno_sp" =!= lit(6))
    .where($"churn_winning_mvno_sp" =!= lit(6))
    .where($"churn_losing_mno_sp" === $"churn_winning_mno_sp")
    .where($"churn_losing_mvno_sp" =!= $"churn_winning_mvno_sp")
    .distinct()

    b_trimmed.join(
      a_trimmed,
      $"current_holder_sp" === $"churn_winning_mvno_sp" &&
      $"previous_holder_sp" === $"churn_losing_mvno_sp",
      "inner"
    )
      .select(
        $"customer_base_date",
        $"current_holder_sp",
        $"current_holder_plan_type_id",
        $"previous_holder_sp",
        $"previous_holder_plan_type_id",
        $"adds"
      )
      .as[NationalAddMultipliersIntraMnoInputs]
  }

}

object NationalCustomerBaseInputs {
  def apply()(implicit spark: SparkSession): NationalCustomerBaseInputs = new NationalCustomerBaseInputs()
}