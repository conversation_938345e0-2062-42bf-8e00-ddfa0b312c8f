package com.comlinkdata.emrjobs.spark.wireless_market_share

import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share.{IndustryModel, PortedCarrierLosses}
import com.comlinkdata.largescale.commons.RichDate._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import java.time.LocalDate


/** The class used to calculate the base adjustment based on wins and losses and National model data  */
class BaseAdjustmentsDMA(implicit spark: SparkSession)  {

  import spark.implicits._

  def calculateBaseAdjustments(
    processingDate: LocalDate,
    industryModel: Dataset[IndustryModel],
    portedCarrierLosses: Dataset[PortedCarrierLosses],
  ): Dataset[EstimatedMonthlyBA] = {

    val quarter = processingDate.getQuarter
    val year = processingDate.getYear

    // Filter ported losses by specific year and add quarter column to it
    val portedCarrierLossesByQuarter = portedCarrierLosses
      .filter(date_format($"date_trunc", "yyyy").cast(IntegerType) === year)
      .withColumn("quarter", date_format($"date_trunc", "q"))
      .select(
        $"loser" as "brand",
        $"secondary_plan_type_id" as "plan_type",
        $"date_trunc" as "customer_base_date",
        $"quarter".cast(IntegerType),
        $"dma",
        $"switches"
      ).as[PortedCarrierLossesByQuarter]

    // Monthly Losses (by Month/Brand/Plan Type/Geo)
    val portedCarrierMonthlyLossesByGeos = calculateMonthlyLossesByGeos(portedCarrierLossesByQuarter)

    //  Quarterly Losses (by Brand/Plan Type)
    val portedCarrierLossesAggByQuarter = calculatePortedCarrierLossesAggByQuarter(portedCarrierLossesByQuarter)

    // Monthly Losses (by Month/Brand/Plan Type/Geo)/Quarterly Losses (by Brand/Plan Type)
    val portedCarrierMonthlyLossesGeoShare = calculatePortedCarrierMonthlyLossesGeoShare(
      processingDate, portedCarrierMonthlyLossesByGeos, portedCarrierLossesAggByQuarter
    )

    // National Quarterly Reported BA (by Brand/ Plan Type)
    // * Monthly Losses (by Month/Brand/Plan Type/Geo)/Quarterly Losses (by Brand/Plan Type)
    calculateFinalBA(quarter, year, industryModel, portedCarrierMonthlyLossesGeoShare)
  }

  def calculateMonthlyLossesByGeos(portedCarrierLossesByQuarter: Dataset[PortedCarrierLossesByQuarter]
  ): Dataset[MonthlyLossesByGeos]  = {
    portedCarrierLossesByQuarter
      .groupBy($"brand", $"plan_type", $"customer_base_date", $"dma", $"quarter")
      .agg(sum($"switches") as "switches_per_geo")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"dma",
        $"quarter",
        $"switches_per_geo"
      )
      .as[MonthlyLossesByGeos]
  }

  def calculatePortedCarrierLossesAggByQuarter(portedCarrierLossesByQuarter: Dataset[PortedCarrierLossesByQuarter]
  ): Dataset[PortedCarrierLossesAggByQuarter] = {
    portedCarrierLossesByQuarter
      .groupBy($"brand", $"plan_type", $"quarter")
      .agg(sum($"switches") as "switches_per_quarter")
      .select(
        $"brand",
        $"plan_type",
        $"quarter",
        $"switches_per_quarter"
      )
      .as[PortedCarrierLossesAggByQuarter]
  }

  def calculatePortedCarrierMonthlyLossesGeoShare(
    processingDate:LocalDate, portedCarrierMonthlyLossesByGeos: Dataset[MonthlyLossesByGeos],
    portedCarrierLossesAggByQuarter: Dataset[PortedCarrierLossesAggByQuarter]
  ): Dataset[PortedCarrierMonthlyLossesGeoShare] = {
    portedCarrierMonthlyLossesByGeos
      .join(portedCarrierLossesAggByQuarter, Seq("brand", "plan_type", "quarter"), "LEFT")
      .withColumn("monthly_loss_share", $"switches_per_geo" / $"switches_per_quarter")
      .where($"customer_base_date" === processingDate)
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"quarter",
        $"dma",
        $"monthly_loss_share"
      )
      .as[PortedCarrierMonthlyLossesGeoShare]
  }

  private def calculateFinalBA(
    quarter: Int, year:Int, industryModel: Dataset[IndustryModel],
    portedCarrierMonthlyLossesGeoShare: Dataset[PortedCarrierMonthlyLossesGeoShare]
  ): Dataset[EstimatedMonthlyBA] = {

    industryModel
      .select($"brand", $"plan_type", $"year", $"quarter", $"ba")
      .where($"quarter" === quarter && $"year" === year)
      .join(portedCarrierMonthlyLossesGeoShare, Seq("brand", "plan_type", "quarter"), "LEFT")
      .withColumn("estimated_ba", $"ba" * $"monthly_loss_share")
      .na.fill(0)
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"dma",
        $"estimated_ba"
      )
      .as[EstimatedMonthlyBA]
  }

}

object BaseAdjustmentsDMA {
  def apply()(implicit spark: SparkSession): BaseAdjustmentsDMA = new BaseAdjustmentsDMA()

}
