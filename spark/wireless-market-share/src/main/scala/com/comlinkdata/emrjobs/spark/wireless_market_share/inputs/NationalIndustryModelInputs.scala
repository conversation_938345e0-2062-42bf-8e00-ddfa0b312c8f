package com.comlinkdata.emrjobs.spark.wireless_market_share.inputs

import org.apache.spark.sql.{Column, Dataset, SparkSession}
import com.comlinkdata.largescale.schema.wireless_market_share.IndustryModelTotalOutput
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.commons.Utils
import org.apache.spark.sql.functions._

import java.sql.Date


class NationalIndustryModelInputs(implicit spark: SparkSession) {

  import spark.implicits._

  /**
    * Compute National Subscribers Multipliers Inputs from the total output of Industry Model
    * @param industryModelTotalOutput: Industry Model Output data
    * @param startDate: Start Date
    * @param endDate: End Date
    * @return Returns a dataset of National Subscribers Multipliers Inputs
    */
  def computeNationalSubscriberMultipliers(
    industryModelTotalOutput: Dataset[IndustryModelTotalOutput],
    startDate: Date,
    endDate: Date
  ): Dataset[NationalSubscriberMultipliersIM] = {

    val df = industryModelTotalOutput
      .withColumn("temp_col", to_date(concat_ws("-", $"year", $"month", lit("01"))))
      .withColumn("customer_base_date", add_months($"temp_col", 1))
      .filter($"temp_col".between(startDate, endDate))
      .filter($"churn_winning_mvno_sp".isNotNull)
      // `not` is used for end = 0, when case..when returns 1 (true) then it will result 1 = 0 else 0 = 0
      .withColumn("case_statement", when(Utils.and(
          $"churn_losing_plan_type_id" === 1,
          $"churn_losing_mvno_sp".isin(6576, 6577, 6578, 6579, 6580),
          $"churn_losing_mno_sp" === $"churn_winning_mno_sp",
          $"churn_winning_mvno_sp" =!= 6), 1).otherwise(0))
      .filter($"case_statement" === 0)

    val agg = df
      .groupBy(
        $"customer_base_date",
        $"churn_winning_mvno_sp",
        $"churn_winning_plan_type_id")
      .agg(
        sum("est").as("industry_model_subscribers"),
        count(lit(1)).as("c"))

    agg.select(
      $"customer_base_date",
      $"churn_winning_mvno_sp".as("current_holder_sp"),
      $"churn_winning_plan_type_id".as("current_holder_plan_type_id"),
      $"industry_model_subscribers"
    ).as[NationalSubscriberMultipliersIM]
  }

  /**
    * Compute National Loss Multipliers Inputs from the total output of Industry Model
    * @param industryModelTotalOutput: Industry Model Output data
    * @param startDate: Start Date
    * @param endDate: End Date
    * @return Returns a dataset of National Loss Multipliers Inputs
    */
  def computeNationalLossMultipliers(
    industryModelTotalOutput: Dataset[IndustryModelTotalOutput],
    startDate: Date,
    endDate: Date
  ): Dataset[NationalLossMultipliersIM] = {

    industryModelTotalOutput
      .withColumn("customer_base_date", generateCustomerBaseDate($"year",$"month")
      ).filter(Utils.and(
      $"customer_base_date".between(startDate, endDate),
      Utils.or(
        $"churn_losing_mno_sp" =!= $"churn_winning_mno_sp",
        Utils.and($"churn_losing_mvno_sp" === 6 && $"churn_winning_mvno_sp".isin(4, 6526, 6545, 6578)),
        Utils.and($"churn_winning_mvno_sp" === 6 && $"churn_losing_mvno_sp".isin(4, 6526, 6545, 6578)),
        $"churn_winning_mno_sp".isNull
      ))
    ).groupBy(
        $"customer_base_date",
        $"churn_losing_mvno_sp",
        $"churn_losing_plan_type_id",
      ).agg(
      sum("est").as("industry_model_losses"),
    ).select($"customer_base_date",
      $"churn_losing_mvno_sp".as("current_holder_sp"),
      $"churn_losing_plan_type_id".as("current_holder_plan_type_id"),
      $"industry_model_losses"
    ).as[NationalLossMultipliersIM]
  }

  /**
    * Compute National Loss Multipliers Disconnects Inputs from the total output of Industry Model
    * @param industryModelTotalOutput: Industry Model Output data
    * @param startDate: Start Date
    * @param endDate: End Date
    * @return Returns a dataset of National Loss Multipliers Disconnects Inputs
    */
  def computeNationalLossMultipliersDisconnects(
    industryModelTotalOutput: Dataset[IndustryModelTotalOutput],
    startDate: Date,
    endDate: Date
  ): Dataset[NationalLossMultipliersDisconnectsIM] = {

    industryModelTotalOutput
      .withColumn("customer_base_date", generateCustomerBaseDate($"year",$"month")
      ).where(Utils.and(
      $"customer_base_date".between(startDate, endDate),
      $"churn_losing_mvno_sp".isNotNull,
      Utils.or(
        $"churn_winning_mno_sp".isNull,
        $"churn_winning_mvno_sp" =!= $"churn_losing_mvno_sp"
      ))
    ).groupBy(
        $"customer_base_date",
        $"churn_losing_mvno_sp",
        $"churn_losing_plan_type_id",
      ).agg(
      sum("est").as("total_industry_model_losses"),
      sum("port_est").as("ported_industry_model_losses"),
      sum($"est" - $"port_est").as("non_ported_industry_model_losses"),
      sum(
        when($"churn_winning_mvno_sp".isNotNull, $"est" - $"port_est")
          .otherwise(0)).as("non_ported_switching_industry_model_losses"),
      sum(
        when($"churn_winning_mvno_sp".isNull, $"est")
          .otherwise(0)).as("disconnects")
    ).select($"customer_base_date",
      $"churn_losing_mvno_sp".as("current_holder_sp"),
      $"churn_losing_plan_type_id".as("current_holder_plan_type_id"),
      $"total_industry_model_losses",
      $"ported_industry_model_losses",
      $"non_ported_industry_model_losses",
      $"non_ported_switching_industry_model_losses",
      $"disconnects"
    ).as[NationalLossMultipliersDisconnectsIM]
  }

  /**
    * Compute National Add Multipliers Inputs from the total output of Industry Model
    * @param industryModelTotalOutput: Industry Model Output data
    * @param startDate: Start Date
    * @param endDate: End Date
    * @return Returns a dataset of National Add Multipliers Inputs
    */
  def computeNationalAddMultipliers(
    industryModelTotalOutput: Dataset[IndustryModelTotalOutput],
    startDate: Date,
    endDate: Date
  ): Dataset[NationalAddMultipliersIM] = {
    industryModelTotalOutput
      .withColumn("customer_base_date", generateCustomerBaseDate($"year",$"month")
      ).filter(Utils.and(
        $"customer_base_date".between(startDate, endDate),
        Utils.or(
          $"churn_losing_mno_sp" =!= $"churn_winning_mno_sp",
          Utils.and($"churn_losing_mvno_sp" === 6 && $"churn_winning_mvno_sp".isin(4, 6526, 6545, 6578)),
          Utils.and($"churn_winning_mvno_sp" === 6 && $"churn_losing_mvno_sp".isin(4, 6526, 6545, 6578)),
        ),
        $"churn_losing_mno_sp".isNotNull,
        $"churn_winning_mno_sp".isNotNull
      )).groupBy(
      $"customer_base_date",
      $"churn_losing_mvno_sp",
      $"churn_losing_plan_type_id",
      $"churn_winning_mvno_sp",
      $"churn_winning_plan_type_id",
    ).agg(
      sum("est").as("industry_model_adds")
    )
      .select($"customer_base_date",
        $"churn_losing_mvno_sp".as("previous_holder_sp"),
        $"churn_losing_plan_type_id".as("previous_holder_plan_type_id"),
        $"churn_winning_mvno_sp".as("current_holder_sp"),
        $"churn_winning_plan_type_id".as("current_holder_plan_type_id"),
        $"industry_model_adds"
      ).as[NationalAddMultipliersIM]
  }

  /**
    *
    * Compute National Add Multipliers Intra MNO Inputs from the total output of Industry Model
    * @param industryModelTotalOutput: Industry Model Output data
    * @param startDate: Start Date
    * @param endDate: End Date
    * @return Returns a dataset of National Add Multipliers Intra MNO Inputs
    */
  def computeNationalAddMultipliersIntraMno(
    industryModelTotalOutput: Dataset[IndustryModelTotalOutput],
    startDate: Date,
    endDate: Date
  ): Dataset[NationalAddMultipliersIntraMnoIM] = {

    industryModelTotalOutput
      .withColumn("customer_base_date", generateCustomerBaseDate($"year",$"month")
      ).filter(Utils.and(
      $"customer_base_date".between(startDate, endDate),
      $"churn_losing_mno_sp" === $"churn_winning_mno_sp",
      $"churn_losing_mvno_sp" =!= $"churn_winning_mvno_sp",
      $"churn_losing_mvno_sp".isNotNull,
      $"churn_winning_mvno_sp".isNotNull,
      $"churn_losing_mvno_sp" =!= 6,
      $"churn_winning_mvno_sp" =!= 6,
      not(Utils.or(
        Utils.and($"churn_winning_plan_type_id" === 1,
        $"churn_winning_mvno_sp".isin(6576, 6577, 6578, 6579, 6580)
      ),
      Utils.and($"churn_losing_plan_type_id" === 1,
          $"churn_losing_mvno_sp".isin(6576, 6577, 6578, 6579, 6580)
      ))))
    ).groupBy(
        $"customer_base_date",
        $"churn_losing_mvno_sp",
        $"churn_losing_plan_type_id",
        $"churn_winning_mvno_sp",
        $"churn_winning_plan_type_id",
      ).agg(
      sum("est").as("industry_model_adds"),
    ).select($"customer_base_date",
      $"churn_losing_mvno_sp".as("previous_holder_sp"),
      $"churn_losing_plan_type_id".as("previous_holder_plan_type_id"),
      $"churn_winning_mvno_sp".as("current_holder_sp"),
      $"churn_winning_plan_type_id".as("current_holder_plan_type_id"),
      $"industry_model_adds"
    ).as[NationalAddMultipliersIntraMnoIM]
  }

  /**
    * Compute National Add Multipliers Activations Inputs from the total output of Industry Model
    * @param industryModelTotalOutput: Industry Model Output data
    * @param startDate: Start Date
    * @param endDate: End Date
    * @return Returns a dataset of National Add Multipliers Activations Inputs
    */
  def computeNationalAddMultipliersActivations(
    industryModelTotalOutput: Dataset[IndustryModelTotalOutput],
    startDate: Date,
    endDate: Date
  ): Dataset[NationalAddMultipliersActivationsIM] = {

    industryModelTotalOutput
      .na.fill(-1, Seq("churn_winning_mvno_sp"))
      .na.fill(-1,Seq("churn_losing_mvno_sp"))
      .withColumn("customer_base_date", generateCustomerBaseDate($"year",$"month")
      ).filter(Utils.and(
      $"customer_base_date".between(startDate, endDate),
      $"churn_winning_mvno_sp" =!= -1,
      Utils.or(
        $"churn_losing_mvno_sp" === -1,
        $"churn_winning_mvno_sp" =!= $"churn_losing_mvno_sp"
      ),
      not(Utils.and(   // `not` is used for end = 0, when case..when returns 1 (true) then it will result 1 = 0 else 0 = 0
        $"churn_losing_plan_type_id" === 1,
        $"churn_losing_mvno_sp".isin(6576, 6577, 6578, 6579, 6580)
      )))
    ).groupBy(
        $"customer_base_date",
        $"churn_winning_mvno_sp",
        $"churn_winning_plan_type_id",
      ).agg(
      sum("est").as("total_industry_model_adds"),
      sum("port_est").as("ported_industry_model_adds"),
      sum($"est" - $"port_est").as("non_ported_industry_model_adds"),
      sum(
        when($"churn_losing_mvno_sp" =!= -1, $"est" - $"port_est")
          .otherwise(0)).as("non_ported_switching_industry_model_adds"),
      sum(
        when($"churn_losing_mvno_sp" === -1, $"est")
          .otherwise(0)).as("activations"),
      (sum(
        when($"churn_losing_mvno_sp" === -1, $"est")
          .otherwise(0)) / (sum($"est") - sum(
        when($"churn_losing_mvno_sp" === -1, $"est")
          .otherwise(0)))).as("activations_multiplier")
    ).select($"customer_base_date",
      $"churn_winning_mvno_sp".as("current_holder_sp"),
      $"churn_winning_plan_type_id".as("current_holder_plan_type_id"),
      $"total_industry_model_adds",
      $"ported_industry_model_adds",
      $"non_ported_industry_model_adds",
      $"non_ported_switching_industry_model_adds",
      $"activations",
      $"activations_multiplier"
    ).as[NationalAddMultipliersActivationsIM]
  }

  /**
    * Returns date column in the format year-month-01
    * @param year
    * @param month
    * @return Returns column based on year and month inputs
    */
  def generateCustomerBaseDate(year: Column, month: Column)(implicit spark: SparkSession): Column = {
    to_date(concat_ws("-", year,
      date_format(concat_ws("-", year, month, lit("01")
      ), "LL"), lit("01")))
  }
}

object NationalIndustryModelInputs {

  def apply()(implicit spark: SparkSession): NationalIndustryModelInputs = new NationalIndustryModelInputs()

}
