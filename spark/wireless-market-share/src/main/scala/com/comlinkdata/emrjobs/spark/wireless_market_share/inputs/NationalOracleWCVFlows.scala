package com.comlinkdata.emrjobs.spark.wireless_market_share.inputs

import com.comlinkdata.largescale.schema.wireless_market_share.{NationalOracleDemographicsFFC, NationalWCVFlows}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Column, DataFrame, Dataset, SparkSession}
import java.sql.Date


class NationalOracleWCVFlows(implicit spark: SparkSession) {

  import spark.implicits._

  val start_date: Date = Date.valueOf("2019-01-01")

  /**
    * The method used to prepare the National WCV flows based on the National Oracle Demographics FFC data
    * @param monthToProcess: The processing month used as end date to generate national wcv flows data
    * @param nationalOracleDemoData: National Oracle Demographics FFC data
    * @return Returns the output dataframe of prepared national wcv flows of type Dataset[NationalWCVFlows]
    */
  def calculateNationalWCVFlowsData(
    monthToProcess: Date,
    nationalOracleDemoData: Dataset[NationalOracleDemographicsFFC],
  ):Dataset[NationalWCVFlows]  = {

    nationalOracleDemoData
      .withColumn("month_date", to_date(concat_ws("-", $"year", $"month", lit("01"))))
      .where($"month_date" >= start_date && $"month_date" <= monthToProcess)
      .transform(assignBrandNamesToSP($"mvno_winner", $"winner_plan_type", "winner_brand_plantype"))
      .transform(assignBrandNamesToSP($"mvno_loser", $"loser_plan_type","loser_brand_plantype"))
      .drop("age", "ethnicity")   // recreate in next transform
      .transform(assign_ethnic_code_to_ethnicity($"Ethnic_Code", "ethnicity"))
      .transform(assign_age_range_to_age($"Age_Range", "age"))
      .transform(assign_estimated_household_income_to_income($"Estimated_Household_Income", "income"))
      .where($"ethnicity" =!= "Other" &&  $"winner_brand_plantype" =!= "Other" && $"loser_brand_plantype" =!= "Other")
      .groupBy($"month_date",
        $"loser_brand_plantype",
        $"winner_brand_plantype",
        $"ethnicity",
        $"age",
        $"income")
      .agg(sum($"wins") as "wins")
      .na.drop(Seq("ethnicity", "age", "income"))  // drop null records for "ethnicity", "age", "income
      .select(
        $"month_date",
        $"loser_brand_plantype",
        $"winner_brand_plantype",
        $"ethnicity",
        $"age",
        $"income",
        $"wins"
      ).as[NationalWCVFlows]
  }


  /**
    * The method used to assign the proper format brand names to service providers (SP) and based on plan types
    * @param sp: Service Provide number
    * @param plan_type: Plan type column name
    * @param brandColName: Brand column name
    * @param df: Source data that we have to transform
    * @return Return the dataframe by replacing SP with brand names
    */
  def assignBrandNamesToSP(sp: Column, plan_type: Column, brandColName: String)(df: DataFrame): DataFrame = {
    df
      .withColumn(
        brandColName,
        when(sp.isin(Seq(2,178,6050,2620):_*) && plan_type === 1, "AT&T_Prepaid Phone")
          .when(sp.isin(Seq(8):_*) && plan_type === 1, "Cricket Wireless_Prepaid Phone")
          .when(sp.isin(Seq(2, 178, 6050, 2620):_*) && plan_type === 2, "AT&T_Postpaid Phone")
          .when(sp.isin(Seq(3,3147,6042,6043,609):_*) && plan_type === 1, "Boost Mobile_Prepaid Phone")
          .when(sp.isin(Seq(3,3147,6042,6043,609):_*) && plan_type === 2, "T-Mobile_Postpaid Phone")
          .when(sp.isin(Seq(6):_*) && plan_type === 1, "MetroPCS_Prepaid Phone")
          .when(sp.isin(Seq(4):_*) && plan_type === 1, "T-Mobile_Prepaid Phone")
          .when(sp.isin(Seq(4,6):_*) && plan_type === 2, "T-Mobile_Postpaid Phone")
          .when(sp.isin(Seq(1):_*) && plan_type === 1, "Verizon Wireless_Prepaid Phone")
          .when(sp.isin(Seq(1):_*) && plan_type === 2, "Verizon Wireless_Postpaid Phone")
          .when(sp.isin(Seq(7):_*) && plan_type === 1, "U.S. Cellular_Prepaid Phone")
          .when(sp.isin(Seq(7):_*) && plan_type === 2, "U.S. Cellular_Postpaid Phone")
          .when(sp.isin(Seq(6495):_*) && plan_type === 1, "Altice_Prepaid Phone")
          .when(sp.isin(Seq(6495):_*) && plan_type === 2, "Optimum Mobile_Postpaid Phone")
          .when(sp.isin(Seq(6052):_*), "Xfinity Mobile_Postpaid Phone")
          .when(sp.isin(Seq(6105):_*), "Spectrum Mobile_Postpaid Phone")
          .when(sp.isin(Seq(5,6544,6545,6546,6547):_*) && plan_type === 1, "Tracfone_Prepaid Phone")
          .when(sp.isin(Seq(1171):_*) && plan_type === 2, "Cox Mobile_Postpaid Phone")
          .when(plan_type === 1,"Other_Prepaid Phone")
          .when(plan_type === 2,"Other_Postpaid Phone")
          .otherwise("Other")
      )
  }

  /**
    * The method used to transform ethnic codes into ethnicity names
    * @param k_ethnic_code: Column name sof Ethnic code
    * @param race_col_name: Column name of race/ethnicity
    * @param df: Source dataframe
    * @return Returns the output dataframe with Ethnicity data
    */
  private def assign_ethnic_code_to_ethnicity(k_ethnic_code: Column, race_col_name: String)(df: DataFrame): DataFrame = {
    df.withColumn(race_col_name,
      when(k_ethnic_code === "A", "Black / African American")
        .when(k_ethnic_code === "B", "Hispanic / Latino")
        .when(k_ethnic_code === "C", "Asian")
        .when(k_ethnic_code === "D", "White")
        .when(k_ethnic_code.isin(Seq("E", "F", "G"):_*), "Other")
    )
  }

  /**
    * The method used to transform the age in alphabets into age number range
    * @param k_age_range: Age alphabets (A, B, C, D, E, F, G, H, I, J, K, L, M) column name
    * @param age_col_name: Age number range column name (18-34, 35-54, 55-74, 75+)
    * @param df: Source dataframe
    * @return Returns the output dataframe contains the age ranges
    */
  private def assign_age_range_to_age(k_age_range: Column, age_col_name: String)(df: DataFrame): DataFrame = {
    df.withColumn(age_col_name,
      when(k_age_range.isin(Seq("A", "B", "C", "D"):_*), "18-34")
        .when(k_age_range.isin(Seq("E", "F", "G", "H"):_*), "35-54")
        .when(k_age_range.isin(Seq("I", "J", "K", "L"):_*), "55-74")
        .when(k_age_range === "M", "75+")
    )
  }

  /**
    * The method used to transform income alphabets to income range
    * @param k_income: Income column of alphabets (A, B, C, D, E, F, G, H, I, J, K, L, M, N, O)
    * @param income_col_name: Income range column name (<$50K, $50K-$100K, $100K-$150K, $150K+)
    * @param df: Source dataframe
    * @return Returns the output dataframe contains the income range
    */
  private def assign_estimated_household_income_to_income(k_income: Column, income_col_name: String)(df: DataFrame): DataFrame = {
    df.withColumn(income_col_name,
      when(k_income.isin(Seq("A", "B", "C", "D", "E"):_*), "<$50K")
        .when(k_income.isin(Seq("F", "G", "H"):_*), "$50K-$100K")
        .when(k_income.isin(Seq("I", "J"):_*), "$100K-$150K")
        .when(k_income.isin(Seq("K", "L", "M", "N", "O"):_*), "$150K+")
    )
  }
}

object NationalOracleWCVFlows {
  def apply()(implicit spark: SparkSession): NationalOracleWCVFlows = new NationalOracleWCVFlows()
}
