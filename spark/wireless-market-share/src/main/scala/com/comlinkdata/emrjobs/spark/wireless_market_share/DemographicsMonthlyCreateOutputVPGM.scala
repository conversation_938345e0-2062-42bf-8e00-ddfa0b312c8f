package com.comlinkdata.emrjobs.spark.wireless_market_share

import org.apache.spark.sql.{Dataset, SparkSession}
import com.comlinkdata.largescale.schema.wireless_market_share.{MonthlyOutputWithEthnicityVPGM, NationalWCVFlows, SubscribersByEthnicityAgeIncomeVPGM, WCVFlowsVPGM, WmsVPGM}
import java.time.LocalDate


// create table wms_ethnicity_test_vpgm_monthly_output
class DemographicsMonthlyCreateOutputVPGM(implicit spark: SparkSession) {

  import spark.implicits._

  // Use this function to call all the small functions from DemographicsUtilsVPGM and generate final output
  def calculateEthnicityMonthlyOutput(
    newMonth: LocalDate,
    nationalWCVFlowsData: Dataset[NationalWCVFlows], // national_wcv_flows_ethnicity_age_income
    subscribersWithEthnicityData: Dataset[SubscribersByEthnicityAgeIncomeVPGM], // vpgm_subs_by_ethnicity_age_income_jun2022
    wcvFlowsVPGMData: Dataset[WCVFlowsVPGM], // vpgm_wcv_flows
    wmsVPGMData: Dataset[WmsVPGM] // WMS VPGM all metrics data
  ): Dataset[MonthlyOutputWithEthnicityVPGM]  = {

    // line 7-28 demovarianceinput3months
    val demoVarianceThreeMonths = DemographicsUtilsVPGM().calculateDemoVarianceInputThreeMonths(newMonth, nationalWCVFlowsData)

    // line 30-48 demovarianceinput20202022
    val demoVarianceFixedTwoYears = DemographicsUtilsVPGM().calculateDemoVarianceFixedTwoYears(newMonth, nationalWCVFlowsData)

    // line 50-78 demo variance factor and line 80-89 demovariancefactorcompressed are combined
    val demoVarianceFactorCompressed = DemographicsUtilsVPGM().calculateDemoVarianceFactorCompressed(
      newMonth, 1.1, 0.9, demoVarianceThreeMonths, demoVarianceFixedTwoYears)

    // line 92-114 subethnicitypctofcarrier
    val subEthnicityPercentOfCarrier = DemographicsUtilsVPGM().calculateSubEthnicityPercentOfCarrier(subscribersWithEthnicityData, newMonth)

    // line 118-130 totallosses
    val totalLossesData = DemographicsUtilsVPGM().calculateTotalLosses(wcvFlowsVPGMData)

    // line 134-145 totalwins
    val totalWinsData = DemographicsUtilsVPGM().calculateTotalWins(wcvFlowsVPGMData)

    // line 149-167 lossesthnicitypctofcarrier
    val lossesEthnicityPercentOfCarrierData = DemographicsUtilsVPGM().calculateLossesEthnicityPercentOfCarrier(
      subEthnicityPercentOfCarrier, totalLossesData
    )

    // line 171-187 winsethnicitypctofcarrier
    val winsEthnicityPercentOfCarrierData = DemographicsUtilsVPGM().calculateWinsEthnicityPercentOfCarrier(
      subEthnicityPercentOfCarrier, totalWinsData
    )

    // line 192-204 winnerpctofloser
    val winnerPercentOfLoserData = DemographicsUtilsVPGM().calculateWinnerPercentOfLoserCreate(wcvFlowsVPGMData)

    // line 208-219 loserpctofwinner
    val loserPercentOfWinnerData = DemographicsUtilsVPGM().calculateLoserPercentOfWinnerCreate(wcvFlowsVPGMData)

    // line 224-288 winslossesbyethnicityinterim
    val winsLossesByEthnicityInterimData = DemographicsUtilsVPGM().calculateWinsLossesByEthnicityInterim1(
      lossesEthnicityPercentOfCarrierData, winsEthnicityPercentOfCarrierData, winnerPercentOfLoserData, loserPercentOfWinnerData
    )

    // line 291-318 winslossesbyethnicityinterim2
    val winsLossesByEthnicityInterim2Data = DemographicsUtilsVPGM().calculateWinsLossesByEthnicityInterim2(
      winsLossesByEthnicityInterimData, demoVarianceFactorCompressed
    )

    // line 320-355 winslossesbyethnicityinterim3 combined with
    // line 357-388 winslossesbyethnicity
    val winsLossesByEthnicityData = DemographicsUtilsVPGM().calculateWinsLossesByEthnicity(
      winsLossesByEthnicityInterim2Data
    )

    // line 391-416 winsfromaverageethnicitypctofcarrier
    val winsFromAverageEthnicityPercentOfCarrierData = DemographicsUtilsVPGM().calculateWinsFromAverageEthnicityPercentOfCarrier(
      winsLossesByEthnicityData, totalWinsData
    )

    // line 420-444 lossesfromaverageethnicitypctofcarrier
    val lossesFromAverageEthnicityPercentOfCarrierData = DemographicsUtilsVPGM().calculateLossesFromAverageEthnicityPercentOfCarrier(
      winsLossesByEthnicityData, totalLossesData
    )

    // line 451-515 wmswithethnicityfromaveragebackwards
    val wmsWithEthnicityFromAverageBackwardsData = DemographicsUtilsVPGM().calculateWMSWithEthnicityFromAverageBackwards(
      wmsVPGMData, subEthnicityPercentOfCarrier, lossesFromAverageEthnicityPercentOfCarrierData,
      winsFromAverageEthnicityPercentOfCarrierData, totalLossesData, totalWinsData
    )

    // line 520-530 wmswithethnicitywstartingsubsfromaveragebackwards
    val wmsWithEthnicityWithStartingSubsFromAverageBackwardsData = DemographicsUtilsVPGM().calculateWMSWithEthnicityWithStartingSubsFromAverageBackwards(
      wmsWithEthnicityFromAverageBackwardsData
    )

    // line 532-534 packaging
    wmsWithEthnicityWithStartingSubsFromAverageBackwardsData
      .filter($"wms_month" === newMonth)
      .withColumn("month", $"wms_month")
      .select("*").na.fill(0).as[MonthlyOutputWithEthnicityVPGM]
  }
}


object DemographicsMonthlyCreateOutputVPGM {
  def apply()(implicit spark: SparkSession): DemographicsMonthlyCreateOutputVPGM = new DemographicsMonthlyCreateOutputVPGM()

}
