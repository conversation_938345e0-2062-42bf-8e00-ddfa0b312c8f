package com.comlinkdata.emrjobs.spark.wireless_market_share


import org.apache.spark.sql.{Dataset, SparkSession}
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share.{SubscribersOutput, SubscribersOutputVPGM}
import org.apache.spark.sql.functions.add_months

class EndOfPeriod(implicit spark: SparkSession) {

  import spark.implicits._

  /**
    * Method used to calculate the subscriber's data based on GA, GL, BA, and previous month subscribers (BOP)
    * @param subscribers: Beginning of period (BOP) subscribers
    * @param grossAdds: Gross additions output
    * @param grossLosses: Gross losses output
    * @param baseAdjustments: Base adjusments output
    * @return Return the end of period (EOP) subscribers using EOP formula
    */
  def calculateEndOfPeriodDMA(
    subscribers: Dataset[SubscribersOutput],
    grossAdds: Dataset[EstimatedGrossAddsDMA],
    grossLosses: Dataset[EstimatedGrossLossesDMA],
    baseAdjustments: Dataset[EstimatedMonthlyBA]
  ): Dataset[EndOfPeriodDMA] = {
    subscribers
      .select($"brand", add_months($"customer_base_date", 1) as "customer_base_date", $"plan_type", $"dma", $"subs")
      .join(grossAdds,Seq("brand","customer_base_date","plan_type","dma"),"LEFT")
      .join(grossLosses, Seq("brand", "customer_base_date", "plan_type", "dma"), "LEFT")
      .join(baseAdjustments, Seq("brand", "customer_base_date", "plan_type", "dma"), "LEFT")
      .na.fill(0)
      .withColumn("end_of_period_subscribers",$"subs" + $"estimated_gross_adds" - $"estimated_gross_losses" + $"estimated_ba")
      .select($"brand",$"customer_base_date",$"plan_type",$"dma",$"end_of_period_subscribers")
      .as[EndOfPeriodDMA]
  }

  def calculateEndOfPeriodVPGM(
    subscribers: Dataset[SubscribersOutputVPGM],
    grossAdds: Dataset[EstimatedGrossAddsVPGM],
    grossLosses: Dataset[EstimatedGrossLossesVPGM],
    baseAdjustments: Dataset[EstimatedMonthlyBAVPGM]
  ): Dataset[EndOfPeriodVPGM] = {
    subscribers
      .select($"brand",add_months($"customer_base_date",1) as "customer_base_date",$"plan_type",$"vpgm",$"subs")
      .join(grossAdds, Seq("brand", "customer_base_date", "plan_type", "vpgm"), "LEFT")
      .join(grossLosses, Seq("brand", "customer_base_date", "plan_type", "vpgm"), "LEFT")
      .join(baseAdjustments, Seq("brand", "customer_base_date", "plan_type", "vpgm"), "LEFT")
      .na.fill(0)
      .withColumn("end_of_period_subscribers", $"subs" + $"estimated_gross_adds" - $"estimated_gross_losses" + $"estimated_ba")
      .select($"brand", $"customer_base_date", $"plan_type", $"vpgm", $"end_of_period_subscribers")
      .as[EndOfPeriodVPGM]
  }
}

object EndOfPeriod  {
  def apply()(implicit spark: SparkSession): EndOfPeriod = new EndOfPeriod()
}
