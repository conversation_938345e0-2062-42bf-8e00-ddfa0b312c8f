package com.comlinkdata.emrjobs.spark.wireless_market_share

import org.apache.spark.sql.{Dataset, SparkSession}
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share.{IndustryModel, PortedCarrierWinsVPGMInput, SubscribersOutputVPGM}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import com.comlinkdata.largescale.commons.RichDate._
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.{ZeroOutBrandsVPGM, ZipToVPGM}

import java.time.LocalDate


class GrossAdditionsVPGM(implicit spark: SparkSession)  {

  import spark.implicits._

  def calculateGrossAdditions(
    processingDate: LocalDate,
    industryModel: Dataset[IndustryModel],
    portedCarrierWins: Dataset[PortedCarrierWinsVPGM],
    subscribers: Dataset[SubscribersOutputVPGM]
  ): Dataset[EstimatedGrossAddsVPGM] = {

    // 1. GA_rate_avg
    val monthlyNationalAverageGrossAddRate = computeMonthlyNationalAverageGrossAddRate(processingDate,industryModel,portedCarrierWins)

    // 2. GA_flat
    val flatGrossAdds = computeFlatGrossAdds(processingDate, subscribers, monthlyNationalAverageGrossAddRate)

    // 3. GA_flat_share
    val flatGrossAddsGeoShare = computeFlatGrossAverageFlatShare(flatGrossAdds)

    // 4. W_share
    val portedCarrierWinGeoShare = computePortedCarrierWinGeoShare(portedCarrierWins)

    // 7. GA
    computeEstimatedGrossAdds(flatGrossAddsGeoShare,portedCarrierWinGeoShare, flatGrossAdds)
  }

  //GA_rate_avg[b,p,m] = GA[b,p,m] / S[b,p,m]
  // specific: GA_rate_avg[b,p,m] = (current quarter GA / previous quarter Subs) * (current month wins) / (current quarter wins)
  def computeMonthlyNationalAverageGrossAddRate(
    processingDate: LocalDate,
    industryModel: Dataset[IndustryModel],
    portedCarrierWins: Dataset[PortedCarrierWinsVPGM]
  ): Dataset[GrossAddsRateAverage] =  {
    val quarter = processingDate.getQuarter
    val year = processingDate.getYear
    val prevQuarter = if (quarter == 1)  4 else quarter-1
    val prevYear = if (quarter == 1) year-1 else year

    val industryModelPrev = industryModel
      .select($"brand",$"plan_type",$"year",$"quarter",$"gross_additions")
      .where($"quarter" === quarter && $"year" === year)
      .join(
        industryModel
          .where($"quarter" === prevQuarter && $"year" === prevYear)
          .select($"brand", $"plan_type", $"year", $"quarter", $"subscribers"),
        Seq("brand","plan_type"),
        "LEFT"
      )
      .withColumn("gross_rate_average_industry_model",$"gross_additions" / $"subscribers")

    val portedCarrierWinsQuarter = portedCarrierWins
      .withColumn("quarter", date_format($"date_trunc", "q"))
      .select(
        $"winner" as "brand",
        $"primary_plan_type_id" as "plan_type",
        $"date_trunc" as "customer_base_date",
        $"quarter",
        $"vpgm",
        $"switches"
      )
      .where(date_format($"customer_base_date", "yyyy").cast(IntegerType) === year)
      .groupBy($"brand",$"plan_type",$"customer_base_date",$"quarter")
      .agg(sum($"switches") as "switches_no_vpgm")

    val portedCarrierWinsAgg = portedCarrierWinsQuarter
      .groupBy($"brand", $"plan_type", $"quarter")
      .agg(sum($"switches_no_vpgm") as "switches_quarter")

    val portedCarrierWinsProcessed = portedCarrierWinsQuarter
      .join(portedCarrierWinsAgg,Seq("brand","plan_type","quarter"),"LEFT")
      .where($"customer_base_date" === processingDate)

    // current quarter GA / prev quarter subs
    industryModelPrev
      .join(portedCarrierWinsProcessed,Seq("brand","plan_type","quarter"),"LEFT")
      .withColumn("gross_rate_average",$"gross_rate_average_industry_model" * $"switches_no_vpgm" / $"switches_quarter") // divide by 3 for quarter to month
      .select($"brand",$"plan_type",$"customer_base_date",round($"gross_rate_average",4) as "gross_rate_average")
      .as[GrossAddsRateAverage]
  }

  //GA_flat[b,p,m,g] = S[b,p,m,g]*GA_rate_avg[b,p,m]
  def computeFlatGrossAdds(
    processingDate: LocalDate,
    subscribers: Dataset[SubscribersOutputVPGM],
    monthlyNationalAverageGrossAddRate: Dataset[GrossAddsRateAverage]
  ): Dataset[GrossAddsFlatVPGM] = {
    val left = subscribers
      .select(
        $"brand",
        $"plan_type",
        add_months($"customer_base_date",1) as "customer_base_date",
        $"vpgm",
        $"subs"
      )
      .where($"customer_base_date" === processingDate)
    val right = monthlyNationalAverageGrossAddRate
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"gross_rate_average"
      )
      .where($"customer_base_date" === processingDate)
    left
      .join(right,Seq("brand","plan_type","customer_base_date"), "LEFT")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"vpgm",
        $"subs" * $"gross_rate_average" as "gross_adds_flat"
      )
      .as[GrossAddsFlatVPGM]
  }

  //GA_flat_share[b,p,m,g] = GA_flat[b,p,m,g] / sum{over b,p,m}(GA_flat[b,p,m,g] )
  def computeFlatGrossAverageFlatShare(
    flatGrossAdds: Dataset[GrossAddsFlatVPGM]
  ): Dataset[GrossAddsAverageFlatShareVPGM] = {
    val left = flatGrossAdds
      .groupBy($"brand", $"plan_type", $"customer_base_date",$"vpgm")
      .agg(sum($"gross_adds_flat") as "a_gross_adds_flat")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"vpgm",
        $"a_gross_adds_flat"
      )
    val right = flatGrossAdds
      .groupBy($"brand",$"plan_type",$"customer_base_date")
      .agg(sum($"gross_adds_flat") as "b_gross_adds_flat")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"b_gross_adds_flat"
      )
    left
      .join(right, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"vpgm",
        $"a_gross_adds_flat" / $"b_gross_adds_flat" as "gross_average_flat_share"
      )
      .as[GrossAddsAverageFlatShareVPGM]
  }

  //W_share[b,p,m,g] = W[b,p,g,m]/sum{over b,p,m}(W[b,p,g,m])
  def computePortedCarrierWinGeoShare(
    portedCarrierWins: Dataset[PortedCarrierWinsVPGM]
  ): Dataset[PortedGeographicWinShareVPGM] = {
    val portedCarrierWinsRenamed = portedCarrierWins
      .select(
        $"winner" as "brand",
        $"primary_plan_type_id" as "plan_type",
        $"date_trunc" as "customer_base_date",
        $"vpgm",
        $"switches"
      )
    val left = portedCarrierWinsRenamed
      .groupBy($"brand", $"plan_type", $"customer_base_date", $"vpgm")
      .agg(sum($"switches") as "a_switches")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"vpgm",
        $"a_switches"
      )
    val right = portedCarrierWinsRenamed
      .groupBy($"brand", $"plan_type", $"customer_base_date")
      .agg(sum($"switches") as "b_switches")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"b_switches"
      )
    left
      .join(right, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
      .select(
        $"brand",
        $"plan_type",
        $"customer_base_date",
        $"vpgm",
        $"a_switches" / $"b_switches" as "ported_win_shares"
      )
      .as[PortedGeographicWinShareVPGM]
  }

  def computeEstimatedGrossAdds(
    flatGrossAddsGeoShare: Dataset[GrossAddsAverageFlatShareVPGM],
    portedCarrierWinGeoShare: Dataset[PortedGeographicWinShareVPGM],
    flatGrossAddsData: Dataset[GrossAddsFlatVPGM]
  ): Dataset[EstimatedGrossAddsVPGM] = {

    //GA[b,p,m,g] = GA_flat_share[b,p,m,g]*GA_share[b,p,m,g]
    val A =
      flatGrossAddsGeoShare
        .join(portedCarrierWinGeoShare, Seq("brand", "plan_type", "customer_base_date", "vpgm"), "LEFT")
        .na.fill(0,Array("ported_win_shares"))
        .withColumn("ga_share_variance", lit(0.5) * ($"ported_win_shares" - $"gross_average_flat_share")) // something wrong here
        //GA_share[b,p,m,g] = GA_flat_share[b,p,m,g] + GA_share_variance[b,p,m,g]
        .withColumn("estimated_gross_add_geo_share", $"gross_average_flat_share" + $"ga_share_variance") // something wrong here
        .select($"brand", $"plan_type", $"customer_base_date", $"vpgm", $"ga_share_variance", $"estimated_gross_add_geo_share")

    //println("flat gross join ported carrier output length: " + A.count())

    flatGrossAddsData
      .groupBy($"brand",$"plan_type",$"customer_base_date")
      .agg(sum($"gross_adds_flat") as "gross_adds_flat_no_geo")
      .join(A, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
      .withColumn("estimated_gross_adds", $"estimated_gross_add_geo_share" * $"gross_adds_flat_no_geo")
      .select($"brand", $"plan_type", $"customer_base_date", $"vpgm", $"estimated_gross_adds")
      .na.fill(0)
      .as[EstimatedGrossAddsVPGM]
  }
}
object GrossAdditionsVPGM {
  def apply()(implicit spark: SparkSession): GrossAdditionsVPGM = new GrossAdditionsVPGM()

}
