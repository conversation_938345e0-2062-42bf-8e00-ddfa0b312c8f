package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.commons.Utils
import com.comlinkdata.largescale.schema.wireless_market_share.{MonthlyOutputWithEthnicityVPGM, NationalWCVFlows, SubscribersByEthnicityAgeIncomeVPGM, WCVFlowsVPGM, WmsVPGM}
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.{when, _}
import org.apache.spark.sql.types.DoubleType
import org.apache.spark.sql.{Column, DataFrame, Dataset, SparkSession}
import java.time.LocalDate


class DemographicsUtilsVPGM(implicit spark: SparkSession) {

  import spark.implicits._

  private val bigThreeCarriers = List("AT&T Postpaid","Verizon Postpaid","T-Mobile Postpaid")

  // Create all small functions here to use them for both Create and forward insert queries

  // line 12-33 demovarianceinput3months
  def calculateDemoVarianceInputThreeMonths(
    newMonth: LocalDate,
    nationalWCVFlowsData: Dataset[NationalWCVFlows]
  ): Dataset[demoVarianceInput] = {
    nationalWCVFlowsData
      .withColumn("newMonth", lit(newMonth))
      .withColumn("subtractMonths", when($"winner_brand_plantype".isin(bigThreeCarriers: _*), lit(-2)).otherwise(lit(-5)))
      .withColumn("startMonth", add_months($"newMonth", $"subtractMonths"))
      .filter(!$"winner_brand_plantype".like("Sprint%") && date_format($"month_date", "yyyy-MM-dd")
        .between($"startMonth", $"newMonth"))
      .groupBy($"winner_brand_plantype", $"ethnicity", $"age", $"income")
      .agg(lit(newMonth) as "date", $"winner_brand_plantype", $"ethnicity", $"age", $"income", sum($"wins").cast(DoubleType) as "wins")
      .select($"date",$"winner_brand_plantype", $"ethnicity", $"age", $"income", $"wins")
      .as[demoVarianceInput]
  }

  // line 35-53 demovarianceinput20202022
  def calculateDemoVarianceFixedTwoYears(
    newMonth: LocalDate,
    nationalWCVFlowsData: Dataset[NationalWCVFlows],
  ): Dataset[demoVarianceInput] = {
    nationalWCVFlowsData
      .where(!$"winner_brand_plantype".like("Sprint%") &&
        date_format($"month_date", "yyyy-MM-dd")
          .between("2020-01-01", "2022-12-01")) // TODO remove hardcoded dates
      .groupBy($"winner_brand_plantype", $"ethnicity", $"age", $"income")
      .agg(lit(newMonth) as "date", $"winner_brand_plantype", $"ethnicity", $"age", $"income", sum($"wins").cast(DoubleType) as "wins")
      .select($"date",$"winner_brand_plantype",$"ethnicity",$"age",$"income",$"wins")
      .as[demoVarianceInput]
  }

  // line 55-86 demo variance factor
  // line 86-95 demovariancefactorcompressed
  def calculateDemoVarianceFactorCompressed(
    newDate: LocalDate, //2019-01-01
    compressedMax: Double,
    compressedMin: Double,
    demoVarianceThreeMonths: Dataset[demoVarianceInput],
    demoVarianceFixedTwoYears: Dataset[demoVarianceInput],
  ): Dataset[demoVarianceFactorCompressed] = {

    val winnerWindow = Window.partitionBy("a.date", "a.winner_brand_plantype")

    demoVarianceThreeMonths.alias("a")
      .join(demoVarianceFixedTwoYears.alias("b"), Seq("winner_brand_plantype", "ethnicity", "age", "income"), "LEFT")
      .withColumn("wins_denominator", sum($"a.wins").over(winnerWindow).cast(DoubleType))
      .withColumn("wins_prop",$"a.wins" / sum($"a.wins").over(winnerWindow).cast(DoubleType))
      .withColumn("long_term_wins_numerator", $"b.wins")
      .withColumn("long_term_wins_denominator", sum($"b.wins").over(winnerWindow))
      .withColumn("long_term_wins_prop", $"b.wins" / sum($"b.wins").over(winnerWindow))
      .withColumn("variance_factor", ($"a.wins" / sum($"a.wins").over(winnerWindow)).cast(DoubleType) / ($"b.wins" / sum($"b.wins").over(winnerWindow)))
      .withColumn("variance_factor_compressed", when($"variance_factor" > compressedMax, compressedMax).when($"variance_factor" < compressedMin, compressedMin).otherwise($"variance_factor"))
      .select(
        $"a.date",
        $"winner_brand_plantype",
        $"ethnicity",
        $"age",
        $"income",
        $"a.wins" as "wins_numerator",
        $"wins_denominator",
        $"wins_prop",
        $"long_term_wins_numerator",
        $"long_term_wins_denominator",
        $"long_term_wins_prop",
        $"variance_factor",
        $"variance_factor_compressed",
      )
      .distinct()
      .as[demoVarianceFactorCompressed]
  }

  // line 92-114 subethnicitypctofcarrier
  def calculateSubEthnicityPercentOfCarrier(
    subscribersWithEthnicityData: Dataset[SubscribersByEthnicityAgeIncomeVPGM],
    newDate: LocalDate
  ): Dataset[subsEthnicityPercentOfCarrierVPGM] = {
    val brandWindow = Window.partitionBy("date", "vpgm", "winner_brand_plantype").orderBy("date")
    subscribersWithEthnicityData
      .withColumn("subs_over_window", sum($"subscribers").over(brandWindow))
      .select(
        lit(newDate) as "date",
        $"vpgm",
        $"winner_brand_plantype",
        $"ethnicity",
        $"age",
        $"income",
        $"subscribers",
        when($"subs_over_window" === lit(0), 0)
          .otherwise($"subs_over_window") as "total_subscribers",
        when($"subs_over_window" === lit(0), 0)
          .otherwise($"subscribers".cast(DoubleType) / $"subs_over_window") as "ethnicity_pct_of_carrier"
      )
      .as[subsEthnicityPercentOfCarrierVPGM]
  }

  def calculateSubEthnicityPercentOfCarrierForwardInsertMonthly(
    subscribersWithEthnicityData: Dataset[MonthlyOutputWithEthnicityVPGM],
    newDate: LocalDate
  ): Dataset[subsEthnicityPercentOfCarrierVPGM] = {
    val brandWindow = Window.partitionBy("date", "vpgm", "brand_plantype").orderBy("date")
    subscribersWithEthnicityData
      .withColumn("date", lit(newDate))
      .filter($"wms_month" === add_months($"date", -1))
      .withColumn("ending_subs_over_window", sum($"ending_subscribers").over(brandWindow))
      .select(
        $"date",
        $"vpgm",
        $"brand_plantype" as "winner_brand_plantype",
        $"ethnicity",
        $"age",
        $"income",
        $"ending_subscribers".cast(DoubleType) as "subscribers",
        when($"ending_subs_over_window" === lit(0), 0)
          .otherwise($"ending_subs_over_window") as "total_subscribers",
        when($"ending_subs_over_window" === lit(0), 0)
          .otherwise($"ending_subscribers".cast(DoubleType) / $"ending_subs_over_window") as "ethnicity_pct_of_carrier"
      )
      .as[subsEthnicityPercentOfCarrierVPGM]
  }

  def calculateSubEthnicityPercentOfCarrierBackwardInsertMonthly(
    subscribersWithEthnicityData: Dataset[MonthlyOutputWithEthnicityVPGM],
    newDate: LocalDate
  ): Dataset[subsEthnicityPercentOfCarrierVPGM] = {
    val brandWindow = Window.partitionBy("date", "vpgm", "brand_plantype").orderBy("date")
    subscribersWithEthnicityData
      .withColumn("date", lit(newDate))
      .filter($"wms_month" === add_months($"date", 1))
      .withColumn("starting_subs_over_window", sum($"starting_subscribers").over(brandWindow))
      .select(
        $"date",
        $"vpgm",
        $"brand_plantype" as "winner_brand_plantype",
        $"ethnicity",
        $"age",
        $"income",
        $"starting_subscribers".cast(DoubleType) as "subscribers",
        when($"starting_subs_over_window" === lit(0), 0)
          .otherwise($"starting_subs_over_window") as "total_subscribers",
        when($"starting_subs_over_window" === lit(0), 0)
          .otherwise($"starting_subscribers".cast(DoubleType) / $"starting_subs_over_window") as "ethnicity_pct_of_carrier"
      )
      .as[subsEthnicityPercentOfCarrierVPGM]
  }
  // line 118-130 totallosses
  def calculateTotalLosses(
    vpgmWCVFlowsData: Dataset[WCVFlowsVPGM]
  ): Dataset[totalLossesVPGM] = {
    vpgmWCVFlowsData
      .groupBy($"month_date", $"vpgm", $"loser_brand_plantype")
      .agg(
        $"month_date" as "date",
        $"vpgm",
        $"loser_brand_plantype",
        sum("wins").cast(DoubleType) as "losses"
      )
      .select($"date",$"vpgm",$"loser_brand_plantype",$"losses")
      .as[totalLossesVPGM]
  }

  // line 134-145 totalwins
  def calculateTotalWins(
    vpgmWCVFlowsData: Dataset[WCVFlowsVPGM]
  ): Dataset[totalWinsVPGM] = {
    vpgmWCVFlowsData
      .groupBy($"month_date", $"vpgm", $"winner_brand_plantype")
      .agg(
        $"month_date" as "date",
        $"vpgm",
        $"winner_brand_plantype",
        sum("wins").cast(DoubleType) as "wins"
      )
      .select($"date", $"vpgm", $"winner_brand_plantype", $"wins")
      .as[totalWinsVPGM]
  }

  // line 149-167 lossesthnicitypctofcarrier
  def calculateLossesEthnicityPercentOfCarrier(
    subsEthnicityPctOfCarrierData: Dataset[subsEthnicityPercentOfCarrierVPGM],
    totalLossesData: Dataset[totalLossesVPGM]
  ): Dataset[lossesEthnicityPercentOfCarrierVPGM] = {

    subsEthnicityPctOfCarrierData.alias("a")
      .join(totalLossesData.alias("b"),
        $"a.date" === $"b.date" && $"a.vpgm" === $"b.vpgm" && $"a.winner_brand_plantype" === $"b.loser_brand_plantype",
        "LEFT")
      .select(
        $"b.date",
        $"a.vpgm",
        $"a.ethnicity",
        $"a.age",
        $"a.income",
        $"a.winner_brand_plantype" as "loser_brand_plantype",
        $"b.losses" as "losses_total",
        $"a.ethnicity_pct_of_carrier" as "loser_ethnicity_pct_of_carrier",
        $"a.ethnicity_pct_of_carrier" * $"b.losses" as "losses_by_ethnicity"
      )
      .na.fill(0)
      .as[lossesEthnicityPercentOfCarrierVPGM]
  }

  // line 171-187 winsethnicitypctofcarrier
  def calculateWinsEthnicityPercentOfCarrier(
    subsEthnicityPctOfCarrierData: Dataset[subsEthnicityPercentOfCarrierVPGM],
    totalWinsData: Dataset[totalWinsVPGM]
  ): Dataset[winsEthnicityPercentOfCarrierVPGM] = {
    subsEthnicityPctOfCarrierData.alias("a")
      .join(totalWinsData.alias("b"), $"a.date" === $"b.date" && $"a.vpgm" === $"b.vpgm" && $"a.winner_brand_plantype" === $"b.winner_brand_plantype", "LEFT")
      .select(
        $"b.date",
        $"a.vpgm",
        $"a.ethnicity",
        $"a.age",
        $"a.income",
        $"a.winner_brand_plantype" as "winner_brand_plantype",
        $"b.wins" as "wins_total",
        $"a.ethnicity_pct_of_carrier" as "winner_ethnicity_pct_of_carrier",
        $"a.ethnicity_pct_of_carrier" * $"b.wins" as "wins_by_ethnicity"
      )
      .na.fill(0)
      .as[winsEthnicityPercentOfCarrierVPGM]
  }

  // line 192-204 winnerpctofloser
  def calculateWinnerPercentOfLoserCreate(
    wcvFlowsVPGMData: Dataset[WCVFlowsVPGM]
  ): Dataset[winnerPercentOfLoserVPGM] = {
    val winnerWindow = Window.partitionBy($"month_date",$"vpgm",$"loser_brand_plantype")
    wcvFlowsVPGMData
      .select(
        $"month_date" as "date",
        $"vpgm",
        $"loser_brand_plantype",
        $"winner_brand_plantype",
        $"wins".cast(DoubleType) as "wins",
        sum($"wins").over(winnerWindow) as "total_losses",
        $"wins" / sum($"wins").over(winnerWindow) as "winner_pct_of_loser"
      )
      .as[winnerPercentOfLoserVPGM]
  }

  // line 208-219 loserpctofwinner
  def calculateLoserPercentOfWinnerCreate(
    wcvFlowsVPGMData: Dataset[WCVFlowsVPGM]
  ): Dataset[loserPercentOfWinnerVPGM] = {
    val winnerWindow = Window.partitionBy($"month_date", $"vpgm", $"loser_brand_plantype")
    wcvFlowsVPGMData
      .select(
        $"month_date" as "date",
        $"vpgm",
        $"loser_brand_plantype",
        $"winner_brand_plantype",
        $"wins".cast(DoubleType) as "wins",
        sum($"wins").over(winnerWindow) as "total_wins",
        $"wins" / sum($"wins").over(winnerWindow) as "loser_pct_of_winner"
      )
      .as[loserPercentOfWinnerVPGM]
  }

  def calculateWinnerPercentOfLoserBackwards(
    wcvFlowsVPGMData: Dataset[WCVFlowsVPGM]
  ): Dataset[winnerPercentOfLoserVPGM] = {
    val winnerWindow = Window.partitionBy($"month_date", $"vpgm", $"loser_brand_plantype")
    wcvFlowsVPGMData
      .where($"vpgm" =!= "Pacific States (WA/OR/AK)" || // TODO pacific states
        ($"loser_brand_plantype" =!= "Altice Postpaid" && $"winner_brand_plantype" =!= "Altice Postpaid") ||
        date_trunc("yyyy-MM-dd", $"month_date") >= "2021-05-01") // TODO hard coded date
      .select(
        $"month_date" as "date",
        $"vpgm",
        $"loser_brand_plantype",
        $"winner_brand_plantype",
        $"wins".cast(DoubleType) as "wins",
        sum($"wins").over(winnerWindow) as "total_losses",
        $"wins" / sum($"wins").over(winnerWindow) as "winner_pct_of_loser"
      )
      .as[winnerPercentOfLoserVPGM]
  }

  // line 208-219 loserpctofwinner
  def calculateLoserPercentOfWinnerBackwards(
    wcvFlowsVPGMData: Dataset[WCVFlowsVPGM]
  ): Dataset[loserPercentOfWinnerVPGM] = {
    val winnerWindow = Window.partitionBy($"month_date", $"vpgm", $"loser_brand_plantype")
    wcvFlowsVPGMData
      .select(
        $"month_date" as "date",
        $"vpgm",
        $"loser_brand_plantype",
        $"winner_brand_plantype",
        $"wins".cast(DoubleType) as "wins",
        sum($"wins").over(winnerWindow) as "total_losses",
        $"wins" / sum($"wins").over(winnerWindow) as "winner_pct_of_loser"
      )
      .as[loserPercentOfWinnerVPGM]
  }

  // line 357-388 winslossesbyethnicity
  def calculateWinsLossesByEthnicityInterim1(
    lossesEthnicityPercentOfCarrierData: Dataset[lossesEthnicityPercentOfCarrierVPGM],
    winsEthnicityPercentOfCarrierData: Dataset[winsEthnicityPercentOfCarrierVPGM],
    winnerPercentOfLoserData: Dataset[winnerPercentOfLoserVPGM],
    loserPercentOfWinnerData: Dataset[loserPercentOfWinnerVPGM]
  ): Dataset[winsLossesByEthnicityInterim1VPGM] = {
    winnerPercentOfLoserData.alias("a")
      .join(lossesEthnicityPercentOfCarrierData.alias("b"),Seq("date","vpgm","loser_brand_plantype"),"LEFT")
      .join(winsEthnicityPercentOfCarrierData.alias("c"),Seq("date","vpgm","winner_brand_plantype","ethnicity","age","income"),"LEFT")
      .join(loserPercentOfWinnerData.alias("d"),Seq("date","vpgm","loser_brand_plantype","winner_brand_plantype"),"LEFT")
      .select(
        $"a.date",
        $"a.vpgm",
        $"a.loser_brand_plantype",
        $"a.winner_brand_plantype",
        $"b.ethnicity",
        $"b.age",
        $"b.income",
        $"a.wins",
        $"a.total_losses",
        $"d.total_wins",
        $"a.winner_pct_of_loser",
        $"b.loser_ethnicity_pct_of_carrier",
        $"b.losses_by_ethnicity",
        $"a.winner_pct_of_loser" * $"b.losses_by_ethnicity" as "wins_by_ethnicity_calc",
        $"c.winner_ethnicity_pct_of_carrier",
        $"c.wins_by_ethnicity",
        $"d.loser_pct_of_winner" * $"c.wins_by_ethnicity" as "losses_by_ethnicity_recalc",
        when($"b.loser_ethnicity_pct_of_carrier" === 0.00 ||
          $"c.winner_ethnicity_pct_of_carrier" === 0.00 ||
          isnull($"b.loser_ethnicity_pct_of_carrier") ||
          isnull($"c.winner_ethnicity_pct_of_carrier"),
          $"b.loser_ethnicity_pct_of_carrier" + $"c.winner_ethnicity_pct_of_carrier")
          .otherwise(($"b.loser_ethnicity_pct_of_carrier" + $"c.winner_ethnicity_pct_of_carrier")/2) as "w_l_avg_ethnicity_pct_of_carrier",
        when($"b.loser_ethnicity_pct_of_carrier" === 0.00 ||
          $"c.winner_ethnicity_pct_of_carrier" === 0.00 ||
          isnull($"b.loser_ethnicity_pct_of_carrier") ||
          isnull($"c.winner_ethnicity_pct_of_carrier"),
          $"b.loser_ethnicity_pct_of_carrier" + $"c.winner_ethnicity_pct_of_carrier")
          .otherwise(($"b.loser_ethnicity_pct_of_carrier" * 3 + $"c.winner_ethnicity_pct_of_carrier") / 4) * $"a.wins" as "loser_avg_churn_volume_by_ethnicity",
        when($"b.loser_ethnicity_pct_of_carrier" === 0.00 ||
          $"c.winner_ethnicity_pct_of_carrier" === 0.00 ||
          isnull($"b.loser_ethnicity_pct_of_carrier") ||
          isnull($"c.winner_ethnicity_pct_of_carrier"),
          $"b.loser_ethnicity_pct_of_carrier" + $"c.winner_ethnicity_pct_of_carrier")
          .otherwise(($"b.loser_ethnicity_pct_of_carrier" + $"c.winner_ethnicity_pct_of_carrier" * 3) / 4) * $"a.wins" as "winner_avg_churn_volume_by_ethnicity"
      )
      .as[winsLossesByEthnicityInterim1VPGM]
  }
  def calculateWinsLossesByEthnicityInterim2(
    winsLossesByEthnicityInterim1Data: Dataset[winsLossesByEthnicityInterim1VPGM],
    demoVarianceFactorCompressedData: Dataset[demoVarianceFactorCompressed]
  ): Dataset[winsLossesByEthnicityInterim2VPGM] = {
    winsLossesByEthnicityInterim1Data
      .join(demoVarianceFactorCompressedData,Seq("date","winner_brand_plantype","ethnicity","age","income"),"LEFT")
      .select(
        $"date",
        $"vpgm",
        $"loser_brand_plantype",
        $"winner_brand_plantype",
        $"ethnicity",
        $"age",
        $"income",
        $"wins",
        $"total_losses",
        $"total_wins",
        $"winner_pct_of_loser",
        $"loser_ethnicity_pct_of_carrier" as "loser_ethnicity_pct_of_carrier_original",
        $"losses_by_ethnicity",
        $"winner_ethnicity_pct_of_carrier" as "winner_ethnicity_pct_of_carrier_original",
        $"wins_by_ethnicity",
        $"loser_avg_churn_volume_by_ethnicity" as "loser_avg_churn_volume_by_ethnicity_original",
        $"winner_avg_churn_volume_by_ethnicity" as "winner_avg_churn_volume_by_ethnicity_original",
        when(isnull($"variance_factor_compressed"),0.0).otherwise(1) as "variance_factor_compressed_not_null_check",
        when(isnull($"variance_factor_compressed"), 1.0).otherwise($"variance_factor_compressed") as "variance_factor_compressed"
      )
      .as[winsLossesByEthnicityInterim2VPGM]
  }

  def calculateWinsLossesByEthnicity(
    winsLossesByEthnicityInterim2Data: Dataset[winsLossesByEthnicityInterim2VPGM]
  ): Dataset[winsLossesByEthnicityVPGM] = {
    val interimWindow = Window.partitionBy($"date",$"vpgm",$"loser_brand_plantype",$"winner_brand_plantype")
    // note: a.variance_factor_compressed_NOT_NULL_check is used in interim3 but not in the final

    val partA = winsLossesByEthnicityInterim2Data
      .select(
        $"date",
        $"vpgm",
        $"loser_brand_plantype",
        $"winner_brand_plantype",
        $"ethnicity",
        $"age",
        $"income",
        $"wins",
        $"total_losses",
        $"total_wins",
        $"winner_pct_of_loser",
        $"loser_ethnicity_pct_of_carrier_original",
        $"losses_by_ethnicity",
        $"winner_ethnicity_pct_of_carrier_original",
        $"wins_by_ethnicity",
        $"loser_avg_churn_volume_by_ethnicity_original",
        $"winner_avg_churn_volume_by_ethnicity_original",
        $"variance_factor_compressed",
        $"loser_ethnicity_pct_of_carrier_original" * $"variance_factor_compressed" as "loser_ethnicity_pct_of_carrier_numerator",
        $"winner_ethnicity_pct_of_carrier_original" * $"variance_factor_compressed" as "winner_ethnicity_pct_of_carrier_numerator",
        sum($"loser_ethnicity_pct_of_carrier_original" * $"variance_factor_compressed").over(interimWindow) as "loser_ethnicity_pct_of_carrier_denominator",
        sum($"winner_ethnicity_pct_of_carrier_original" * $"variance_factor_compressed").over(interimWindow) as "winner_ethnicity_pct_of_carrier_denominator",
      )

    partA
      .withColumn("loser_ethnicity_pct_of_carrier",$"loser_ethnicity_pct_of_carrier_original" * $"variance_factor_compressed" / $"loser_ethnicity_pct_of_carrier_denominator")
      .withColumn("winner_ethnicity_pct_of_carrier", $"winner_ethnicity_pct_of_carrier_original" * $"variance_factor_compressed" / $"winner_ethnicity_pct_of_carrier_denominator")
      .withColumn("loser_avg_churn_volume_by_ethnicity",$"wins" * $"loser_ethnicity_pct_of_carrier")
      .withColumn("winner_avg_churn_volume_by_ethnicity",$"wins" * $"winner_ethnicity_pct_of_carrier")
      .select("*").as[winsLossesByEthnicityVPGM]
  }

  // line 391-416 winsfromaverageethnicitypctofcarrier
  def calculateWinsFromAverageEthnicityPercentOfCarrier(
    winsLossesByEthnicityData: Dataset[winsLossesByEthnicityVPGM],
    totalWinsData: Dataset[totalWinsVPGM]
  ): Dataset[winsFromAverageEthnicityPercentOfCarrierVPGM] = {
    winsLossesByEthnicityData.alias("a")
      .join(totalWinsData.alias("b"), Seq("date","vpgm","winner_brand_plantype"),"LEFT")
      .groupBy($"a.date", $"a.vpgm", $"a.ethnicity", $"a.age", $"a.income", $"a.winner_brand_plantype", $"b.wins")
      .agg(sum($"a.winner_avg_churn_volume_by_ethnicity") as "wins_from_average_by_ethnicity",
        sum($"a.winner_avg_churn_volume_by_ethnicity") / $"b.wins" as "winner_from_average_ethnicity_pct_of_carrier")
      .select(
        $"a.date",
        $"a.vpgm",
        $"a.ethnicity",
        $"a.age",
        $"a.income",
        $"a.winner_brand_plantype",
        $"b.wins" as "wins_total",
        $"wins_from_average_by_ethnicity",
        $"winner_from_average_ethnicity_pct_of_carrier"
      )
      .as[winsFromAverageEthnicityPercentOfCarrierVPGM]
  }

  // line 420-444 lossesfromaverageethnicitypctofcarrier
  def calculateLossesFromAverageEthnicityPercentOfCarrier(
    winsLossesByEthnicityData: Dataset[winsLossesByEthnicityVPGM],
    totalLossesData: Dataset[totalLossesVPGM]
  ): Dataset[lossesFromAverageEthnicityPercentOfCarrierVPGM] = {
    winsLossesByEthnicityData.alias("a")
      .join(totalLossesData.alias("b"), Seq("date", "vpgm", "loser_brand_plantype"), "LEFT")
      .groupBy($"a.date", $"a.vpgm", $"a.ethnicity", $"a.age", $"a.income", $"a.loser_brand_plantype", $"b.losses")
      .agg(sum($"a.loser_avg_churn_volume_by_ethnicity") as "losses_from_average_by_ethnicity",
        sum($"a.loser_avg_churn_volume_by_ethnicity") / $"b.losses" as "loser_from_average_ethnicity_pct_of_carrier")
      .select(
        $"a.date",
        $"a.vpgm",
        $"a.ethnicity",
        $"a.age",
        $"a.income",
        $"a.loser_brand_plantype",
        $"b.losses" as "losses_total",
        $"losses_from_average_by_ethnicity",
        $"loser_from_average_ethnicity_pct_of_carrier"
      )
      .as[lossesFromAverageEthnicityPercentOfCarrierVPGM]

  }

  // line 451-515 wmswithethnicityfromaveragebackwards
  def calculateWMSWithEthnicityFromAverageBackwards(
    vpgmWmsOutput: Dataset[WmsVPGM],
    subsEthnicityPctOfCarrierData: Dataset[subsEthnicityPercentOfCarrierVPGM],
    lossesFromAverageEthnicityPercentOfCarrierData: Dataset[lossesFromAverageEthnicityPercentOfCarrierVPGM],
    winsFromAverageEthnicityPercentOfCarrierData: Dataset[winsFromAverageEthnicityPercentOfCarrierVPGM],
    totalLossesData: Dataset[totalLossesVPGM],
    totalWinsData: Dataset[totalWinsVPGM]
  ): Dataset[wmsWithEthnicityFromAverageBackwardsVPGM] = {
    val A = vpgmWmsOutput.alias("a")
      .join(subsEthnicityPctOfCarrierData.alias("b"),
        Utils.and(
          $"a.month_date" === add_months($"b.date", 0),
          $"a.vpgm" === $"b.vpgm",
          $"a.brand_plantype" === $"b.winner_brand_plantype"
        ), "LEFT")

    val B = A
      .join(lossesFromAverageEthnicityPercentOfCarrierData.alias("c"),
        Utils.and(
          $"a.month_date" === $"c.date",
          $"a.vpgm" === $"c.vpgm",
          $"a.brand_plantype" === $"c.loser_brand_plantype",
          $"b.ethnicity" === $"c.ethnicity",
          $"b.age" === $"c.age",
          $"b.income" === $"c.income"
        ), "LEFT")

    val C = B
      .join(winsFromAverageEthnicityPercentOfCarrierData.alias("d"),
        Utils.and(
          $"a.month_date" === $"d.date",
          $"a.vpgm" === $"d.vpgm",
          $"a.brand_plantype" === $"d.winner_brand_plantype",
          $"b.ethnicity" === $"d.ethnicity",
          $"b.age" === $"d.age",
          $"b.income" === $"d.income"
        ), "LEFT")

    val D = C
      .join(totalLossesData.alias("e"),
        Utils.and(
          $"a.month_date" === $"e.date",
          $"a.vpgm" === $"e.vpgm",
          $"a.brand_plantype" === $"e.loser_brand_plantype"
        ), "LEFT")

    val E = D
      .join(totalWinsData.alias("f"),
        Utils.and(
          $"a.month_date" === $"f.date",
          $"a.vpgm" === $"f.vpgm",
          $"a.brand_plantype" === $"f.winner_brand_plantype"
        ), "LEFT")
      .withColumn("wms_month", $"a.month_date")

    E
      .select(
        $"a.month_date" as "wms_month",
        $"b.date" as "subs_join_month",
        $"c.date" as "losses_join_month",
        $"d.date" as "wins_join_month",
        $"a.vpgm",
        $"a.brand_plantype",
        $"a.subscribers".cast(DoubleType) as "total_subscribers",
        $"a.gross_adds".cast(DoubleType) as "total_gross_adds",
        $"a.gross_losses".cast(DoubleType) as "total_gross_losses",
        $"a.base_adjustment".cast(DoubleType) as "total_base_adjustment",
        $"a.last_period_subs".cast(DoubleType) as "total_last_period_subs",
        $"a.next_period_subs".cast(DoubleType) as "total_next_period_subs",
        coalesce($"b.ethnicity", $"c.ethnicity", $"d.ethnicity") as "ethnicity",
        coalesce($"b.age", $"c.age", $"d.age") as "age",
        coalesce($"b.income", $"c.income", $"d.income") as "income",
        $"b.subscribers" as "subs_by_ethnicity_check",
        $"b.ethnicity_pct_of_carrier" as "subs_ethnicity_pct_of_carrier",
        $"b.ethnicity_pct_of_carrier" * $"a.subscribers".cast(DoubleType) as "ending_subscribers",
        $"c.loser_from_average_ethnicity_pct_of_carrier" as "loser_ethnicity_pct_of_carrier",
        $"a.gross_losses".cast(DoubleType) *
          when($"c.loser_from_average_ethnicity_pct_of_carrier".isNull || $"e.losses" === 0.0, $"b.ethnicity_pct_of_carrier")
            .otherwise($"c.loser_from_average_ethnicity_pct_of_carrier") as "gross_losses",
        $"d.winner_from_average_ethnicity_pct_of_carrier",
        $"a.gross_adds".cast(DoubleType) *
          when($"d.winner_from_average_ethnicity_pct_of_carrier".isNull || $"f.wins" === 0.0, $"b.ethnicity_pct_of_carrier")
            .otherwise($"d.winner_from_average_ethnicity_pct_of_carrier") as "gross_adds",
        $"a.base_adjustment".cast(DoubleType) *
          when($"d.winner_from_average_ethnicity_pct_of_carrier".isNull || $"f.wins" === 0.0, $"b.ethnicity_pct_of_carrier")
            .otherwise($"d.winner_from_average_ethnicity_pct_of_carrier") as "base_adjustment",
      ).na.fill(0).as[wmsWithEthnicityFromAverageBackwardsVPGM]
  }

  // line 520-530 wmswithethnicitywstartingsubsfromaveragebackwards
  def calculateWMSWithEthnicityWithStartingSubsFromAverageBackwards(
    wmsWithEthnicityFromAverageBackwards: Dataset[wmsWithEthnicityFromAverageBackwardsVPGM]
  ): Dataset[wmsWithEthnicityWithStartingSubsFromAverageBackwardsVPGM] = {

    wmsWithEthnicityFromAverageBackwards
      .withColumn("starting_subscribers", $"ending_subscribers" - $"gross_adds" + $"gross_losses" - $"base_adjustment")
      .withColumn("gross_add_rate", $"gross_adds" / ($"ending_subscribers" - $"gross_adds" + $"gross_losses" - $"base_adjustment"))
      .withColumn("churn_rate", $"gross_losses" / ($"ending_subscribers" - $"gross_adds" + $"gross_losses" - $"base_adjustment"))
      .withColumn("overall_gross_add_rate", $"total_gross_adds" / $"total_last_period_subs")
      .withColumn("overall_churn_rate", $"total_gross_losses" / $"total_last_period_subs")
      .select("*").as[wmsWithEthnicityWithStartingSubsFromAverageBackwardsVPGM]
  }

  def assignCarrierNameCaseStatement(sp: Column, plan_type: Column, brand_plantype_name: String)(df: DataFrame): DataFrame = {
    df
      .withColumn(
        brand_plantype_name,
        when(sp.isin(Seq(2,178,6050,2620):_*) && plan_type === 1, "AT&T Prepaid")
          .when(sp.isin(Seq(8):_*) && plan_type === 1, "Cricket Prepaid")
          .when(sp.isin(Seq(2, 178, 6050, 2620):_*) && plan_type === 2, "AT&T Postpaid")
          .when(sp.isin(Seq(3,3147,6042,6043,609):_*) && plan_type === 1, "Boost Prepaid")
          .when(sp.isin(Seq(3,3147,6042,6043,609):_*) && plan_type === 2, "T-Mobile Postpaid")
          .when(sp.isin(Seq(6):_*) && plan_type === 1, "Metro Prepaid")
          .when(sp.isin(Seq(4):_*) && plan_type === 1, "T-Mobile Prepaid")
          .when(sp.isin(Seq(4,6):_*) && plan_type === 2, "T-Mobile Postpaid")
          .when(sp.isin(Seq(1):_*) && plan_type === 1, "Verizon Prepaid")
          .when(sp.isin(Seq(1):_*) && plan_type === 2, "Verizon Postpaid")
          .when(sp.isin(Seq(7):_*) && plan_type === 1, "U.S. Cellular Prepaid")
          .when(sp.isin(Seq(7):_*) && plan_type === 2, "U.S. Cellular Postpaid")
          .when(sp.isin(Seq(6495):_*) && plan_type === 1, "Altice Prepaid")
          .when(sp.isin(Seq(6495):_*) && plan_type === 2, "Altice Postpaid")
          .when(sp.isin(Seq(6052):_*), "XFINITY Mobile Postpaid")
          .when(sp.isin(Seq(6105):_*), "Spectrum Mobile Postpaid")
          .when(sp.isin(Seq(5,6544,6545,6546,6547):_*) && plan_type === 1, "Tracfone Prepaid")
          .when(plan_type === 1,"Other Wireless Prepaid")
          .when(plan_type === 2,"Other Wireless Postpaid")
          .otherwise("Other")
      )
  }

  def calculateWMSWithEthnicityFromAverageForwards(
    vpgmWmsOutput: Dataset[WmsVPGM],
    subsEthnicityPctOfCarrierData: Dataset[subsEthnicityPercentOfCarrierVPGM],
    lossesFromAverageEthnicityPercentOfCarrierData: Dataset[lossesFromAverageEthnicityPercentOfCarrierVPGM],
    winsFromAverageEthnicityPercentOfCarrierData: Dataset[winsFromAverageEthnicityPercentOfCarrierVPGM],
    totalLossesData: Dataset[totalLossesVPGM],
    totalWinsData: Dataset[totalWinsVPGM]
  ): Dataset[wmsWithEthnicityFromAverageForwardsVPGM] = {
    val A = vpgmWmsOutput.alias("a")
      .join(subsEthnicityPctOfCarrierData.alias("b"),
        Utils.and(
          $"a.month_date" === add_months($"b.date", 0),
          $"a.vpgm" === $"b.vpgm",
          $"a.brand_plantype" === $"b.winner_brand_plantype"
        ), "LEFT")

    val B = A
      .join(lossesFromAverageEthnicityPercentOfCarrierData.alias("c"),
        Utils.and(
          $"a.month_date" === $"c.date",
          $"a.vpgm" === $"c.vpgm",
          $"a.brand_plantype" === $"c.loser_brand_plantype",
          $"b.ethnicity" === $"c.ethnicity",
          $"b.age" === $"c.age",
          $"b.income" === $"c.income"
        ), "LEFT")

    val C = B
      .join(winsFromAverageEthnicityPercentOfCarrierData.alias("d"),
        Utils.and(
          $"a.month_date" === $"d.date",
          $"a.vpgm" === $"d.vpgm",
          $"a.brand_plantype" === $"d.winner_brand_plantype",
          $"b.ethnicity" === $"d.ethnicity",
          $"b.age" === $"d.age",
          $"b.income" === $"d.income"
        ), "LEFT")

    val D = C
      .join(totalLossesData.alias("e"),
        Utils.and(
          $"a.month_date" === $"e.date",
          $"a.vpgm" === $"e.vpgm",
          $"a.brand_plantype" === $"e.loser_brand_plantype"
        ), "LEFT")

    val E = D
      .join(totalWinsData.alias("f"),
        Utils.and(
          $"a.month_date" === $"f.date",
          $"a.vpgm" === $"f.vpgm",
          $"a.brand_plantype" === $"f.winner_brand_plantype"
        ), "LEFT")
      .withColumn("wms_month", $"a.month_date")

    E
      .select(
        $"a.month_date" as "wms_month",
        $"b.date" as "subs_join_month",
        $"c.date" as "losses_join_month",
        $"d.date" as "wins_join_month",
        $"a.vpgm",
        $"a.brand_plantype",
        $"a.subscribers".cast(DoubleType) as "total_subscribers",
        $"a.gross_adds".cast(DoubleType) as "total_gross_adds",
        $"a.gross_losses".cast(DoubleType) as "total_gross_losses",
        $"a.base_adjustment".cast(DoubleType) as "total_base_adjustment",
        $"a.last_period_subs".cast(DoubleType) as "total_last_period_subs",
        $"a.next_period_subs".cast(DoubleType) as "total_next_period_subs",
        coalesce($"b.ethnicity", $"c.ethnicity", $"d.ethnicity") as "ethnicity",
        coalesce($"b.age", $"c.age", $"d.age") as "age",
        coalesce($"b.income", $"c.income", $"d.income") as "income",
        $"b.subscribers" as "subs_by_ethnicity_check",
        $"b.ethnicity_pct_of_carrier" as "subs_ethnicity_pct_of_carrier",
        $"b.ethnicity_pct_of_carrier" * $"a.last_period_subs".cast(DoubleType) as "starting_subscribers",
        $"c.loser_from_average_ethnicity_pct_of_carrier" as "loser_ethnicity_pct_of_carrier",
        $"a.gross_losses".cast(DoubleType) *
          when($"c.loser_from_average_ethnicity_pct_of_carrier".isNull || $"e.losses" === 0.0, $"b.ethnicity_pct_of_carrier")
            .otherwise($"c.loser_from_average_ethnicity_pct_of_carrier") as "gross_losses",
        $"d.winner_from_average_ethnicity_pct_of_carrier",
        $"a.gross_adds".cast(DoubleType) *
          when($"d.winner_from_average_ethnicity_pct_of_carrier".isNull || $"f.wins" === 0.0, $"b.ethnicity_pct_of_carrier")
            .otherwise($"d.winner_from_average_ethnicity_pct_of_carrier") as "gross_adds",
        $"a.base_adjustment".cast(DoubleType) *
          when($"d.winner_from_average_ethnicity_pct_of_carrier".isNull || $"f.wins" === 0.0, $"b.ethnicity_pct_of_carrier")
            .otherwise($"d.winner_from_average_ethnicity_pct_of_carrier") as "base_adjustment",
      ).na.fill(0).as[wmsWithEthnicityFromAverageForwardsVPGM]
  }

  def wmsWithEthnicityWithEndingSubsFromAverageForwards(
    wmsWithEthnicityFromAverageForwards: Dataset[wmsWithEthnicityFromAverageForwardsVPGM]
  ): Dataset[wmsWithEthnicityWithStartingSubsFromAverageForwardsVPGM] = {

    wmsWithEthnicityFromAverageForwards
      .withColumn("ending_subscribers", $"starting_subscribers" + $"gross_adds" - $"gross_losses" + $"base_adjustment")
      .withColumn("gross_add_rate", $"gross_adds" / ($"starting_subscribers"))
      .withColumn("churn_rate", $"gross_losses" / ($"starting_subscribers"))
      .withColumn("overall_gross_add_rate", $"total_gross_adds" / $"total_last_period_subs")
      .withColumn("overall_churn_rate", $"total_gross_losses" / $"total_last_period_subs")
      .select($"wms_month",
        $"subs_join_month",
        $"losses_join_month",
        $"wins_join_month",
        $"vpgm",
        $"brand_plantype",
        $"total_subscribers",
        $"total_gross_adds",
        $"total_gross_losses",
        $"total_base_adjustment",
        $"total_last_period_subs",
        $"total_next_period_subs",
        $"ethnicity",
        $"age",
        $"income",
        $"subs_by_ethnicity_check",
        $"subs_ethnicity_pct_of_carrier",
        $"ending_subscribers",
        $"loser_ethnicity_pct_of_carrier",
        $"gross_losses",
        $"winner_from_average_ethnicity_pct_of_carrier",
        $"gross_adds",
        $"base_adjustment",
        $"starting_subscribers",
        $"gross_add_rate",
        $"churn_rate",
        $"overall_gross_add_rate",
        $"overall_churn_rate")
      .as[wmsWithEthnicityWithStartingSubsFromAverageForwardsVPGM]
  }
}


object DemographicsUtilsVPGM {
  def apply()(implicit spark: SparkSession): DemographicsUtilsVPGM = new DemographicsUtilsVPGM()

}
