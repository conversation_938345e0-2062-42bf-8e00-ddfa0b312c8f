package com.comlinkdata.emrjobs.spark.wireless_market_share.dsa_stickee

import com.comlinkdata.emrjobs.spark.wireless_market_share.dsa_stickee.model._
import com.comlinkdata.largescale.commons.RedshiftUtils
import com.comlinkdata.largescale.commons.RedshiftUtils.RedshiftConfig
import com.opensignal.largescale.schema.broadband_stickee._
import org.apache.spark.sql.functions.{col, _}
import org.apache.spark.sql.types.{DateType, DoubleType}
import org.apache.spark.sql.{DataFrame, Dataset, SaveMode, SparkSession}

import java.sql.Date
import java.time.temporal.TemporalAdjusters
import java.time.{DayOfWeek, LocalDate}


class BroadbandDsa(implicit spark: SparkSession)  {

  import spark.implicits._

  /**
    * The method used to generate the aggregated data based on all the required transformations
    * on the raw DSA BB Stickee data
    * @param rawDsaBBData: Current week's Raw DSA BB data
    * @param rawDataLastWeek: Last week's raw DSA BB data
    * @param techTypeSpIdLookup: Tech Type to SP ID lookup data
    * @param timeSeriesLookUpData: Time series lookup data of all ISPs
    * @return The method will return the aggregated dataset by cleaning the PII info
    */
  def generateDsaBBAggData(
    rawDsaBBData: Dataset[DsaBroadbandStickee],
    rawDataLastWeek: Dataset[DsaBroadbandStickee],
    techTypeSpIdLookup: Dataset[TechTypeSpIdLookup],
    timeSeriesLookUpData: Dataset[TimeSeriesIspLookup]
  ): Dataset[dsaBroadbandStickeeAgg] = {

    // get the actual date of the current week's data
    val currentWeekReportDate = extractReportDate(rawDsaBBData.toDF())

    println("Last Best Scrape processing...")
    val LastBestScrapeData: Dataset[DsaBroadbandStickee] = updateDataForLastBestScrape(
      currentWeekReportDate, rawDataLastWeek, rawDsaBBData, timeSeriesLookUpData
    )

    println("Time Series filtering...")
    val filteredTimeSeriesData: Dataset[DsaBroadbandStickee] = filterDataBasedOnTimeSeries(
      LastBestScrapeData, timeSeriesLookUpData
    )

    println("Excluding and populating the fields...")
    val dataAfterExcludingAndPopulatingFields: Dataset[dsaBroadbandStickeeInputAgg] = excludeAndPopulateFields(
        filteredTimeSeriesData, techTypeSpIdLookup
    )

    println("Consumer rate calculation...")
    val dataAfterConsumerRatesCalculation: Dataset[dsaBroadbandStickeeInputAgg] = calculateConsumerRates(
        dataAfterExcludingAndPopulatingFields
    )

    println("Speed Tier Standardization...")
    val finalAggAfterSpeedTier: Dataset[dsaBroadbandStickeeAgg] = speedTierStandardization(
      dataAfterConsumerRatesCalculation
    )

    finalAggAfterSpeedTier

  }

  // PRD-6&7
  /**
    * Excludes invalid records from the raw broadband dataset and populate technology type and parent_sp
    * by joining with a tech type to SP ID lookup table
    * @param rawDsaBBData: The raw BB DSA data
    * @param techTypeSpIdLookup: The tech type to SP ID lookup table data
    * @return A cleaned and enriched dataset of type `dsaBroadbandStickeeInputAgg` ready for further processing.
    */
  def excludeAndPopulateFields(
    rawDsaBBData: Dataset[DsaBroadbandStickee],
    techTypeSpIdLookup: Dataset[TechTypeSpIdLookup]
  ): Dataset[dsaBroadbandStickeeInputAgg] = {

    val df1 = rawDsaBBData
      .filter(col("error_message").isNull || col("error_message") === "")  // Exclude rows where error_message is NOT null
      .filter(
        col("internet_plan_name").isNull || col("internet_plan_name") === "" || !lower(col("internet_plan_name")).contains("business")
      )
      .drop("display_price_is_intro", "wireless_bundle_discount")
      .distinct()

    // TODO: Pull parent sp from other source if the isp, address combination not found in the lookup table
    df1
      .drop("technology_opensignal_st", "parent_sp")  // drop the columns before populating it from the lookup
      .join(techTypeSpIdLookup, Seq("isp_name", "address_clean_geo"), "left")
      .withColumn("technology_opensignal_st",
        when(
          col("technology_opensignal_st").isNull,  // No match found in the lookup table
          when(
            lower(col("isp_name")).isin("verizon", "at&t", "t-mobile"),
            lit(8)
          ).otherwise(lit(0))
        ).otherwise(col("technology_opensignal_st"))  // Use value from lookup if present
      )
      .withColumn("parent_sp", coalesce(col("parent_sp"), lit(0)))
      .drop("address_clean_geo", "address_primary")  // drop address columns once used
      .select(orderedCols.map(col): _*).as[dsaBroadbandStickeeInputAgg]
  }

  /**
    * The method will calculate the total cost to consumer rates columns by setting up the 0 to null
    * values before the calculations
    * @param rawDF: The raw BB DSA dataset
    * @return This returns a dataset of type dsaBroadbandStickeeInputAgg that has the calculated columns
    *         total_cost_to_consumer_rack_rate and total_cost_to_consumer_intro_rate
    */
  def calculateConsumerRates(
    rawDF: Dataset[dsaBroadbandStickeeInputAgg]
  ): Dataset[dsaBroadbandStickeeInputAgg] = {

    // PRD-5: Calculated Fields using inline null/empty checks
    val rawDFWithCalcs = rawDF
      // calculate total cost to consumer rack rate
      .withColumn(
        "total_cost_to_consumer_rack_rate",
        when(
            trim(col("rack_rate_price").cast("string")) === "" || col("rack_rate_price").isNull, lit(0.0)
        ).otherwise(col("rack_rate_price").cast("double")) -
          when(
              trim(col("autopay_discount").cast("string")) === "" || col("autopay_discount").isNull, lit(0.0)
          ).otherwise(col("autopay_discount").cast("double")) +
          when(
            trim(col("wifi_equipment_secondary_fee").cast("string")) =!= "" &&
              col("wifi_equipment_secondary_fee").isNotNull,
            col("wifi_equipment_secondary_fee").cast("double")
          ).otherwise(
            when(
              trim(col("wifi_equipment_intro_fee").cast("string")) === "" || col("wifi_equipment_intro_fee").isNull,
              lit(0.0)
            ).otherwise(col("wifi_equipment_intro_fee").cast("double"))
          )
      )
      // Calculate total cost to consumer intro rate
      .withColumn(
        "total_cost_to_consumer_intro_rate",
        when(
          trim(col("display_monthly_price").cast("string")) === "" || col("display_monthly_price").isNull,
          lit(0.0)
        ).otherwise(col("display_monthly_price").cast("double")) +
          when(
            trim(col("wifi_equipment_intro_fee").cast("string")) === "" || col("wifi_equipment_intro_fee").isNull,
            lit(0.0)
          ).otherwise(col("wifi_equipment_intro_fee").cast("double"))
      )
      // Set negative values to null/empty
      .withColumn(
        "total_cost_to_consumer_rack_rate",
        when(col("total_cost_to_consumer_rack_rate") < 0, lit("").cast("double"))
          .otherwise(col("total_cost_to_consumer_rack_rate"))
      )
      .withColumn(
        "total_cost_to_consumer_intro_rate",
        when(col("total_cost_to_consumer_intro_rate") < 0, lit("").cast("double"))
          .otherwise(col("total_cost_to_consumer_intro_rate"))
      )

    rawDFWithCalcs.as[dsaBroadbandStickeeInputAgg]

  }

  /**
    * The method is used to standardize the display speeds based on the provided columns in the raw data
    * @param aggData: The BB DSA Raw dataset
    * @return This returns the dataset of type dsaBroadbandStickeeAgg that has the standardized
    *         speed column display_download_speed
    */
  def speedTierStandardization(aggData: Dataset[dsaBroadbandStickeeInputAgg]): Dataset[dsaBroadbandStickeeAgg] = {

    // UDP to extract the highest number from the string. Ex. Speed 250, 400 will return 400
    val extractMaxNumberFromColumn = udf[Double, String] { s =>
      if (s == null) {
        null.asInstanceOf[Double]
      } else {
        val regex = "\\d+(\\.\\d+)?".r
        val nums = regex.findAllIn(s).toList.map(_.toDouble)
        if (nums.nonEmpty) nums.max else null.asInstanceOf[Double]
      }
    }

    val df1 = aggData
      // Extract the largest number from column "display_download_speed"
      .withColumn(
        "display_download_speed_extract",
        extractMaxNumberFromColumn(col("display_download_speed"))
      )
      // If column "display_download_speed" contains "gig" or "gbps", then multiply number by 1000
      .withColumn(
        "display_download_speed_extract",
        when(
          upper(col("display_download_speed")).contains("GIG")
            .or(upper(col("display_download_speed")).contains("GBPS")),
          col("display_download_speed_extract") * 1000
        ).otherwise(col("display_download_speed_extract"))
      )
      // Repeat the steps above for column "display_internet_plan_name"
      .withColumn(
        "display_internet_plan_name_extract",
        extractMaxNumberFromColumn(col("display_internet_plan_name"))
      )
      .withColumn(
        "display_internet_plan_name_extract",
        when(
          upper(col("display_internet_plan_name")).contains("GIG") ||
            upper(col("display_internet_plan_name")).contains("GBPS"),
          col("display_internet_plan_name_extract") * 1000
        ).otherwise(col("display_internet_plan_name_extract"))
      )


    val df2 = df1
      // If column "display_download_speed" or "display_internet_plan_name" contain "gig" or "gbps" AND number
      // in column "download_speed_typical" is less than 100, then multiply number in "download_speed_typical"
      // by 1000
      .withColumn(
        "download_speed_typical",
        when(
          (
            (upper(col("display_download_speed")).contains("GIG") ||
              upper(col("display_download_speed")).contains("GBPS") ||
              upper(col("display_internet_plan_name")).contains("GIG") ||
              upper(col("display_internet_plan_name")).contains("GBPS"))
              &&
              col("download_speed_typical") < 100
            ),
          col("download_speed_typical") * 1000
        ).otherwise(col("download_speed_typical"))
      )
      // Select the largest number from "display_download_speed_extract" and "display_internet_plan_name_extract".
      // If both columns are NA, then select number from "download_speed_typical" instead.
      .withColumn(
        "display_download_speed_standardized",
        when(
          col("isp_name") === "Ezee Fiber",
          col("display_download_speed_extract")
        ).otherwise(
          greatest(
            col("display_download_speed_extract"),
            col("display_internet_plan_name_extract")
          )
        )
      )
      .withColumn(
        "display_download_speed_standardized",
        coalesce(
          col("display_download_speed_standardized"),
          col("download_speed_typical")
        )
      )

    val df3 = df2
      // Divide "display_download_speed_standardized" by "download_speed_typical". If result is <= 0.143 or >= 7,
      // then replace value in "display_download_speed_standardized" with "download_speed_typical"
      .withColumn(
        "display_download_speed_standardized",
        when(
          col("download_speed_typical").isNotNull &&
            (
              col("display_download_speed_standardized") / col("download_speed_typical") >= 7 ||
                col("display_download_speed_standardized") / col("download_speed_typical") <= 0.143
              ),
          col("download_speed_typical")
        ).otherwise(col("display_download_speed_standardized"))
      )
      .withColumn("display_download_speed_without_rounding",
        greatest(
          col("display_download_speed_standardized").cast(DoubleType),
          col("download_speed_max").cast(DoubleType)
        )
      )

    // Round "display_download_speed" to the nearest 10 after taking the maximum value between
    // display_download_speed_standardized and download_speed_max in display_download_speed_without_rounding
    df3
      .withColumn(
        "display_download_speed",
        when(
          round(col("display_download_speed_without_rounding") / 10) * 10 === 0,
          lit(10)
        ).otherwise(
          round(col("display_download_speed_without_rounding") / 10) * 10)
      )
      .drop("display_download_speed_standardized", "display_download_speed_extract", "display_internet_plan_name_extract")
      .select(dsaBroadbandStickeeAggOrderedCols.map(col): _*)
      .as[dsaBroadbandStickeeAgg]
  }

  /**
    * This method is used to populate the most recent deals for ISP/address combinations that are missing
    * from the weekly deliverable
    * @param currentWeekActualDate: The actual date/report date of the current week's (Friday) raw data
    * @param rawDataLastWeek: Last week's (Last Friday) raw data
    * @param rawDataCurrentWeek: Current week's (Friday) raw data
    * @param timeSeriesLookUpData: TimeSeries (ISP+Report date) lookup data
    * @return This returns the dataset of type DsaBroadbandStickee that has the best scraped data from the last week ignoring
    *         the best scrape for the ISP that are stated this week
    */
  def updateDataForLastBestScrape(
    currentWeekActualDate: Date,
    rawDataLastWeek: Dataset[DsaBroadbandStickee],
    rawDataCurrentWeek: Dataset[DsaBroadbandStickee],
    timeSeriesLookUpData: Dataset[TimeSeriesIspLookup]
  ): Dataset[DsaBroadbandStickee] = {

    import org.apache.spark.sql.functions._

    // Get distinct ISP and ADDR combination records from both weeks
    val uniqueKeysCurrentWeek = rawDataCurrentWeek.select("isp_name", "address_id").distinct()
    val uniqueKeysLastWeek = rawDataLastWeek.select("isp_name", "address_id").distinct()

    // Find ISPs that start this week — based on the time series lookup
    val newIspThisWeek = timeSeriesLookUpData
      .filter(col("report_date") === lit(currentWeekActualDate))
      .select("isp_name")

    // Find missing keys — those are present in last week but not in current
    val missingKeys = uniqueKeysLastWeek
      .join(uniqueKeysCurrentWeek, Seq("isp_name", "address_id"), "left_anti")

    val missingKeysNewIspSkip =  missingKeys
      .join(newIspThisWeek, Seq("isp_name"), "left_anti")  // skip best scrape for new ISPs started this week

    // Pull all columns data for the missing keys from the last week and update report_date to the current week
    val missingKeysFullData = rawDataLastWeek
      .join(missingKeysNewIspSkip, Seq("isp_name", "address_id"), "inner")
      .withColumn("report_date", lit(currentWeekActualDate).cast(DateType))
      .as[DsaBroadbandStickee]

    // Merge with current week's data
    rawDataCurrentWeek.unionByName(missingKeysFullData)
      .filter(!col("isp_name").isin("Cox Mobile", "Xfinity Mobile"))  // filtered incorrect ISP's after best scrape

  }

  /**
    * The method is used to write the partial delivery (Non-Friday's delivery) to the REDSHIFT
    * @param aggDataToWrite: Final aggregated dataset to write on RS
    * @param redshiftAggTable: RS agg table name
    * @param rsConfig: Redshift Config
    */
  def writePartialDeliveryDataToRedshift(
    aggDataToWrite: Dataset[dsaBroadbandStickeeAgg],
    redshiftAggTable: String
  )(implicit rsConfig: RedshiftConfig): Unit = {

    val reportDate = BroadbandDsa().extractReportDate(aggDataToWrite.toDF())
    val stagingTable = "broadband.broadband_dsa_events_weekly_stickee_agg_temp"
    // Take last friday's date based on the report date in the current data
    val lastFridayDate = getLastFridayDate(reportDate.toLocalDate)  // If the partial delivery happened after Friday and before the next Fridays standard delivery
    println(s"We have received the partial delivery on $reportDate for the last Friday's delivery $lastFridayDate")

    // Overwrite the report_date with last Friday in the agg data rather using the new actual date of the partial delivery
    val updatedData = aggDataToWrite
      .withColumn("report_date", lit(Date.valueOf(lastFridayDate)).cast(DateType))

    // Create a staging table using the predefined schema to avoid column size issues.
    // For example, if the column size is 1000 in the schema and the data has 2000 characters, it will fail
    val createTableQuery = model.createTempAggTableQuery.format(stagingTable, stagingTable)
    println(s"Creating staging table: $stagingTable using the query: $createTableQuery")
    RedshiftUtils.redshiftExecute(createTableQuery)(rsConfig, spark)

    // Write the data to a staging table
    println(s"Writing data to staging table: $stagingTable")
    RedshiftUtils.redshiftWrite(
      updatedData,
      stagingTable,
      SaveMode.Append
    )(rsConfig)

    // Build the DELETE JOIN query
    val deleteQuery =
      s"""
         |DELETE FROM $redshiftAggTable
         |USING $stagingTable stg
         |WHERE $redshiftAggTable.isp_name = stg.isp_name
         |AND $redshiftAggTable.report_date = '$lastFridayDate';
     """.stripMargin

    // Step 3: Write data to agg table with pre-action query to delete the existing ISP data
    println(s"Writing data to Redshift table: $redshiftAggTable with pre-action query: $deleteQuery")
    RedshiftUtils.redshiftWrite(
      updatedData,
      redshiftAggTable,
      SaveMode.Append,
      preActionQuery = Some(deleteQuery)
    )(rsConfig)

    // Drop the staging table
    val dropQuery = s"DROP TABLE IF EXISTS $stagingTable;"
    println(s"Dropping staging table: $stagingTable using the query: $dropQuery")
    RedshiftUtils.redshiftExecute(dropQuery)(rsConfig, spark)
  }

  /**
    * This method is to filter out the ISPs based on the timeseries lookup data.
    * @param rawDsaBBData: The BB DSA Raw data
    * @param timeSeriesLookUpData: TimeSeries lookup data that has ISP and their Start date
    * @return This returns the dataset of type DsaBroadbandStickee that reflects the ISP's data based on the start date mentioned
    */
  def filterDataBasedOnTimeSeries(
    rawDsaBBData: Dataset[DsaBroadbandStickee], timeSeriesLookUpData: Dataset[TimeSeriesIspLookup]
  ): Dataset[DsaBroadbandStickee] = {

    rawDsaBBData.alias("raw")
      .join(timeSeriesLookUpData.alias("lookup"), Seq("isp_name"), "left")
      .filter($"raw.report_date" >= $"lookup.report_date")
      .select("raw.*")
      .select(DsaBroadbandStickee.cols.map(c => c.cast(DsaBroadbandStickee.schema(s"$c").dataType)):_*)
      .as[DsaBroadbandStickee]
  }

  /**
    * This method will return the most recent Friday's date based on the given date
    * @param today: The date param used to get the most recent Friday's date
    * @return This returns the most recent Friday's date of type LocalDate
    */
  def getLastFridayDate(today: LocalDate): LocalDate = {
    // Get the most recent Friday (including today if it's Friday)
    val lastFriday = today.`with`(TemporalAdjusters.previousOrSame(DayOfWeek.FRIDAY))
    lastFriday
  }

  /**
    * The method extracts the report date from the DSA BB Data
    * @param dsaBBData: The BB DSA dataset
    * @return This returns the report date of type "Date"
    */
  def extractReportDate(dsaBBData: DataFrame): Date = {
    dsaBBData.select("report_date").distinct().orderBy("report_date").collect().map(_.getDate(0)).head
  }

  /**
    * This method is used to nullify the PII columns before writing the raw data to REDSHIFT
    * @param rawData: The BB DSA Raw dataset
    * @return  This returns the dataset of type DsaBroadbandStickeeRedshiftSchema that has the nullified
    *          columns address_clean_geo & address_primary
    */
  def getRawDataWithoutPII(rawData: Dataset[DsaBroadbandStickee]):
  Dataset[DsaBroadbandStickeeRedshiftSchema] = {
    rawData
      .withColumn("address_clean_geo", lit(null).cast("String"))
      .withColumn("address_primary", lit(null).cast("String"))
      .withColumnRenamed("report_date", "actual_date")  // just revert report date to actual date for raw table
      .distinct()
      .select(rawDataRSSchemaOrder.map(col): _*).as[DsaBroadbandStickeeRedshiftSchema]
  }

}


object BroadbandDsa {
  def apply()(implicit spark: SparkSession): BroadbandDsa = new BroadbandDsa()

}
