package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON>, Spark<PERSON><PERSON><PERSON><PERSON><PERSON>, Utils}
import com.comlinkdata.largescale.commons.io.dataset.writeCsvOverwrite
import com.comlinkdata.largescale.schema.wireless_market_share._
import com.comlinkdata.emrjobs.spark.wireless_market_share.{BaseAdjustmentsVPGM, CombineAllMetrics, EndOfPeriod, GrossAdditionsVPGM, GrossLossesVPGM, WMSHelper}
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.{ZeroOutBrandsVPGM, ZipToVPGM}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.SparkSession

import java.time.LocalDate
import java.net.URI


case class AttProcessingJobVPGMConfig(
  monthToProcess: Option[LocalDate],
  industryModelLoc: URI,
  portedCarrierLossesLoc: URI,
  portedCarrierWinsLoc: URI,
  subscriberOutputLoc: URI,
  zeroBrandsLoc: URI,
  zipToVpgmLoc: URI,
  outputBasePath: URI,
  outputPartitions: Int
)

object AttProcessingJobVPGMConfig {

  val sample: AttProcessingJobVPGMConfig = AttProcessingJobVPGMConfig(
    monthToProcess = Option(LocalDate.of(2022, 7, 1)),
    industryModelLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/industry_model/",
    portedCarrierLossesLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/vpgm/ported_losses/",
    portedCarrierWinsLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/vpgm/ported_wins/",
    subscriberOutputLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/vpgm/subscribers/",
    zeroBrandsLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/vpgm/zero_brands/",
    zipToVpgmLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/lookups/zip_to_vpgm_mapping/",
    outputBasePath = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/outputs/vpgm/",
    outputPartitions = 1
  )
}


object AttProcessingJobVPGM extends SparkJob[AttProcessingJobVPGMConfig](AttProcessingJobVPGMRunner)

object AttProcessingJobVPGMRunner extends SparkJobRunner[AttProcessingJobVPGMConfig] with LazyLogging {
  def runJob(config: AttProcessingJobVPGMConfig)(implicit spark: SparkSession): Unit  = {
    import spark.implicits._

    // Default processing date would be latest month
    val processingDate = LocalDate.now().withDayOfMonth(1).toString

    // Specific month to process
    val monthToProcess = config.monthToProcess.getOrElse(processingDate).toString
    val lastMonthSubs = LocalDate.parse(monthToProcess).minusMonths(1)

    // Read common schema's
    val industryModelPath = Utils.joinPaths(
      config.industryModelLoc, s"processing_date=$monthToProcess")
    logger.info(s"Loading Industry Model input data from the path: ${industryModelPath}")
    val industryModelData = IndustryModel.read(industryModelPath)

    val portedCarrierLossesPath = Utils.joinPaths(
      config.portedCarrierLossesLoc, s"processing_date=$monthToProcess")
    logger.info(s"Loading Ported Carrier Losses input data from the path: ${portedCarrierLossesPath}")
    val portedCarrierLossesData = PortedCarrierLossesVPGMInput.read(portedCarrierLossesPath)

    val portedCarrierWinsPath = Utils.joinPaths(
      config.portedCarrierWinsLoc, s"processing_date=$monthToProcess")
    logger.info(s"Loading Ported Carrier Wins input data from the path: ${portedCarrierWinsPath}")
    val portedCarrierWinsData = PortedCarrierWinsVPGMInput.read(portedCarrierWinsPath)

    val subscriberOutputPath = Utils.joinPaths(
      config.subscriberOutputLoc, s"processing_date=$lastMonthSubs")
    logger.info(s"Loading Subscriber's Output input data from the path: ${subscriberOutputPath}")
    val subscribersData = SubscribersOutputVPGM.read(subscriberOutputPath)

    logger.info(s"Loading Zero Brands input data from the path: ${config.zeroBrandsLoc}")
    val zeroBrandsData = ZeroOutBrandsVPGM.read(config.zeroBrandsLoc)

    val zipToVPGMPath = Utils.joinPaths(
      config.zipToVpgmLoc, s"processing_date=${processingDate}")
    logger.info(s"Loading Zip to VPGM lookup data from the path: ${zipToVPGMPath}")
    val zipToVPGMData = ZipToVPGM.read(zipToVPGMPath)

    // convert data from zip to VPGM
    val portedCarrierWinsVPGMData = WMSHelper().convertWinsZipToVPGM(portedCarrierWinsData,zipToVPGMData)
    val portedCarrierLossesVPGMData = WMSHelper().convertLossesZipToVPGM(portedCarrierLossesData, zipToVPGMData)

    // Zeroed out brands in Ported Wins and Losses
    val portedCarrierWinsVPGMZeroed = WMSHelper().zeroOutAddsVPGM(portedCarrierWinsVPGMData, zeroBrandsData)
    val portedCarrierLossesVPGMZeroed = WMSHelper().zeroOutLossesVPGM(portedCarrierLossesVPGMData, zeroBrandsData)


    // ** Calculations **

    // 1. Estimated Gross Additions data
    logger.info(s"Calculating the estimated Gross Additions...")
    val grossAdditionsData = GrossAdditionsVPGM().calculateGrossAdditions(
      LocalDate.parse(monthToProcess), industryModelData, portedCarrierWinsVPGMZeroed, subscribersData
    )
    logger.info(s"Finished calculating the estimated gross additions.")

    // 2. Estimated Gross Losses data
    logger.info(s"Calculating the estimated Gross Losses...")
    val grossLossesData = GrossLossesVPGM().calculateGrossLosses(
      LocalDate.parse(monthToProcess), industryModelData, portedCarrierLossesVPGMZeroed, subscribersData
    )
    logger.info(s"Finished calculating the estimated gross losses.")

    // 3. Base Adjustments Data
    logger.info(s"Calculating the base adjustments...")
    val baseAdjustmentsData = BaseAdjustmentsVPGM().calculateBaseAdjustments(
      LocalDate.parse(monthToProcess), industryModelData, portedCarrierLossesVPGMZeroed
    )
    logger.info(s"Finished calculating the base adjustments.")

    // 4. End of Period Formula
    logger.info(s"Calculating the Subscribers using EOP formula...")
    val subsEopData = EndOfPeriod().calculateEndOfPeriodVPGM(
      subscribersData, grossAdditionsData, grossLossesData, baseAdjustmentsData
    )
    logger.info(s"Finished calculating the Subscribers using EOP formula.")

    // Combine all metrics
    val finalOutput = CombineAllMetrics().combineAllMetricsVPGM(
      grossAdditionsData, grossLossesData, baseAdjustmentsData, subsEopData
    )

    // Write data to S3
    // TODO: Remove functionality of writing the intermediate metrics once final dataset verified and accepted
    val outputBasePath = config.outputBasePath.toString

    // Gross Additions
    val grossAdditionsOutputPath = Utils.joinPaths(
      outputBasePath, "gross_additions", s"processing_date=$monthToProcess"
    )
    logger.info(s"Writing estimated gross additions to the S3 location = $grossAdditionsOutputPath")
    writeCsvOverwrite(grossAdditionsData, config.outputPartitions, grossAdditionsOutputPath)

    // Gross Losses
    val grossLossesOutputPath = Utils.joinPaths(
      outputBasePath, "gross_losses", s"processing_date=$monthToProcess"
    )
    logger.info(s"Writing estimated gross losses to the S3 location = $grossLossesOutputPath")
    writeCsvOverwrite(grossLossesData, config.outputPartitions, grossLossesOutputPath)

    // Base Adjustments
    val baseAdjustmentsOutputPath = Utils.joinPaths(
      outputBasePath, "base_adjustments", s"processing_date=$monthToProcess"
    )
    logger.info(s"Writing estimated base adjustments to the S3 location = $baseAdjustmentsOutputPath")
    writeCsvOverwrite(baseAdjustmentsData, config.outputPartitions, baseAdjustmentsOutputPath)

    // Subscribers using EOP
    val subsEopPath = Utils.joinPaths(
      outputBasePath, "subscribers", s"processing_date=$monthToProcess"
    )
    logger.info(s"Writing Subscribers data generated by EOP formula to the S3 location = $subsEopPath")
    writeCsvOverwrite(subsEopData, config.outputPartitions, subsEopPath)

    // Final Output
    val finalOutputPath = Utils.joinPaths(
      outputBasePath, "final_subs_output", s"processing_date=$monthToProcess"
    )
    logger.info(s"Writing estimated base adjustments to the S3 location = $finalOutputPath")
    writeCsvOverwrite(finalOutput, config.outputPartitions, finalOutputPath)

  }
}
