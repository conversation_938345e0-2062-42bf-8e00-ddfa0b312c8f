package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.largescale.commons.{RedshiftUtils, Spark<PERSON><PERSON>, SparkJob<PERSON>unner}
import com.comlinkdata.largescale.schema.wms_syndicated._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SaveMode, SparkSession, DataFrame}
import org.apache.spark.sql.functions._
import java.net.URI

/**
  * RedshiftUploadWmsSyndicatedJob is responsible for reading output data from specified paths,
  * processing it to keep rows with the maximum creation date per month, and uploading the
  * processed data to Redshift tables after truncating the existing data.
  *
  * Configuration:
  * - dmaFinalOutputPairwiseDemoPath: Path to the DMA output pairwise demo combinations data.
  * - cmaFinalOutputPairwiseDemoPath: Path to the CMA output pairwise demo combinations data.
  * - vpgmFinalOutputPairwiseDemoPath: Path to the VPGM from DMA output pairwise demo combinations data.
  * - msoIlecFootprintsFinalOutputPairwiseDemoPath: Path to the MSO ILEC footprints output pairwise demo combinations data.
  */

case class RedshiftUploadWmsSyndicatedJobConfig(
  dmaFinalOutputPairwiseDemoPath: URI,
  cmaFinalOutputPairwiseDemoPath: URI,
  vpgmFinalOutputPairwiseDemoPath: URI,
  msoIlecFootprintsFinalOutputPairwiseDemoPath: URI,
  boostFinalOutputPairwiseDemoPath: URI,
  redshiftTableSchema: String,
  redshiftTableVpgmSchema: String,
  redshiftTableBoostSchema: String,
  redshiftTableNameDma: String,
  redshiftTableNameCma: String,
  redshiftTableNameVpgmFromDma: String,
  redshiftTableNameMsoIlec: String,
  redshiftTableNameBoost: String,
  redshiftJdbcEndpoint: String,
  redshiftUserName: String,
  redshiftParameterStoreKey: String,
  redshiftTempLocation: String
)

object RedshiftUploadWmsSyndicatedJobConfig {
  val sample: RedshiftUploadWmsSyndicatedJobConfig = RedshiftUploadWmsSyndicatedJobConfig(
    dmaFinalOutputPairwiseDemoPath = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging/dma_pairwise_final_output_all_demo_combinations/",
    cmaFinalOutputPairwiseDemoPath = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging/cma_pairwise_final_output_all_demo_combinations/",
    vpgmFinalOutputPairwiseDemoPath = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging/vpgm_pairwise_final_output_all_demo_combinations/",
    msoIlecFootprintsFinalOutputPairwiseDemoPath = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging/mso_ilec_pairwise_final_output_all_demo_combinations/",
    boostFinalOutputPairwiseDemoPath = URI create "s3://e000-comlinkdata-com/prod/wireless/marketshare/US/outputs/packaging/boost_market_final_output_pairwise_demo_combinations/",
    redshiftTableSchema = "rpts",
    redshiftTableVpgmSchema = "rptsatt",
    redshiftTableBoostSchema = "rptsdsh",
    redshiftTableNameDma = "latest_dma_pairwise_demo_data",
    redshiftTableNameCma = "latest_cma_pairwise_demo_data",
    redshiftTableNameVpgmFromDma = "latest_vpgm_pairwise_demo_data",
    redshiftTableNameMsoIlec = "latest_mso_ilec_pairwise_demo_data",
    redshiftTableNameBoost = "latest_boost_pairwise_demo_data",
    redshiftJdbcEndpoint = "******************************************************************************",
    redshiftUserName = "agg_loader_dev",
    redshiftParameterStoreKey = "/c200/redshift_agg_loader_dev",
    redshiftTempLocation = "s3://d000-comlinkdata-com/redshift-uploads/wms_syndicated"
  )
}

object RedshiftUploadWmsSyndicatedJob extends SparkJob[RedshiftUploadWmsSyndicatedJobConfig](RedshiftUploadWmsSyndicatedJobRunner)

object RedshiftUploadWmsSyndicatedJobRunner extends SparkJobRunner[RedshiftUploadWmsSyndicatedJobConfig] with LazyLogging {

  def runJob(config: RedshiftUploadWmsSyndicatedJobConfig)(implicit spark: SparkSession): Unit = {
    val dmaFinalOutputPairwiseDemoPath = config.dmaFinalOutputPairwiseDemoPath
    val cmaFinalOutputPairwiseDemoPath = config.cmaFinalOutputPairwiseDemoPath
    val vpgmFinalOutputPairwiseDemoPath = config.vpgmFinalOutputPairwiseDemoPath
    val msoIlecFinalOutputPairwiseDemoPath = config.msoIlecFootprintsFinalOutputPairwiseDemoPath
    val boostFinalOutputPairwiseDemoPath = config.boostFinalOutputPairwiseDemoPath

    logger.info(s"Reading dma final output pairwise demo data from the path = $dmaFinalOutputPairwiseDemoPath")
    val dmaFinalOutputPairwiseDemoData = DmaOutput.read(dmaFinalOutputPairwiseDemoPath).toDF()

    logger.info(s"Reading cma final output pairwise demo data from the path = $cmaFinalOutputPairwiseDemoPath")
    val cmaFinalOutputPairwiseDemoData = CmaOutput.read(cmaFinalOutputPairwiseDemoPath).toDF()

    logger.info(s"Reading vpgm from dma final output pairwise demo data from the path = $vpgmFinalOutputPairwiseDemoPath")
    val vpgmFromDmaFinalOutputPairwiseDemoData = VpgmFromDmaOutput.read(vpgmFinalOutputPairwiseDemoPath).toDF()

    logger.info(s"Reading mso ilec footprints final output pairwise demo data from the path = $msoIlecFinalOutputPairwiseDemoPath")
    val msoIlecFootprintsFinalOutputPairwiseDemoData = MsoIlecFootprintsOutput.read(msoIlecFinalOutputPairwiseDemoPath).toDF()

    logger.info(s"Reading boost market final output pairwise demo data from the path = $boostFinalOutputPairwiseDemoPath")
    val boostMarketFinalOutputPairwiseDemoData = BoostOutput.read(boostFinalOutputPairwiseDemoPath).toDF()

    // Process each DataFrame to get rows with the max creation date per month
    val maxDmaFinalOutputPairwiseData = getLatestMonthByMaxCreationDate(dmaFinalOutputPairwiseDemoData)
    val maxCmaFinalOutputPairwiseData = getLatestMonthByMaxCreationDate(cmaFinalOutputPairwiseDemoData)
    val maxVpgmFromDmaFinalOutputPairwiseData = getLatestMonthByMaxCreationDate(vpgmFromDmaFinalOutputPairwiseDemoData)
    val maxMsoIlecFootprintsFinalOutputPairwiseData = getLatestMonthByMaxCreationDate(msoIlecFootprintsFinalOutputPairwiseDemoData)
    val maxBoostMarketFinalOutputPairwiseData = getLatestMonthByMaxCreationDate(boostMarketFinalOutputPairwiseDemoData)

    // Redshift Config
    val rsConfig = RedshiftUtils.RedshiftConfig(
      rsTemporaryLocation = URI create config.redshiftTempLocation,
      rsJdbcEndpoint = config.redshiftJdbcEndpoint,
      rsUserName = config.redshiftUserName,
      rsParameterStoreKey = config.redshiftParameterStoreKey
    )

    // Load Dma Output data in redshift
    val dmaRSTableName = s"${config.redshiftTableSchema}.${config.redshiftTableNameDma}"
    logger.info(s"Writing dma final output pairwise demo data to the redshift table: $dmaRSTableName")
    val preActionQueryDma = s"TRUNCATE TABLE $dmaRSTableName;"
    RedshiftUtils.redshiftWrite(
      maxDmaFinalOutputPairwiseData,
      dmaRSTableName,
      SaveMode.Overwrite,
      preActionQuery = Some(preActionQueryDma)
    )(rsConfig)

    // Load Cma Output data in redshift
    val cmaRSTableName = s"${config.redshiftTableSchema}.${config.redshiftTableNameCma}"
    logger.info(s"Writing cma final output pairwise demo data to the redshift table: $cmaRSTableName")
    val preActionQueryCma = s"TRUNCATE TABLE $cmaRSTableName;"
    RedshiftUtils.redshiftWrite(
      maxCmaFinalOutputPairwiseData,
      cmaRSTableName,
      SaveMode.Overwrite,
      preActionQuery = Some(preActionQueryCma)
    )(rsConfig)

    // Load Vpgm from Dma Output data in redshift
    val vpgmFromDmaRSTableName = s"${config.redshiftTableVpgmSchema}.${config.redshiftTableNameVpgmFromDma}"
    logger.info(s"Writing vpgm from dma final output pairwise demo data to the redshift table: $vpgmFromDmaRSTableName")
    val preActionQueryVpgmFromDma = s"TRUNCATE TABLE $vpgmFromDmaRSTableName;"
    RedshiftUtils.redshiftWrite(
      maxVpgmFromDmaFinalOutputPairwiseData,
      vpgmFromDmaRSTableName,
      SaveMode.Overwrite,
      preActionQuery = Some(preActionQueryVpgmFromDma)
    )(rsConfig)

    // Load Mso Ilec Footprints data in redshift
    val msoIlecRSTableName = s"${config.redshiftTableSchema}.${config.redshiftTableNameMsoIlec}"
    logger.info(s"Writing Mso Ilec final output pairwise demo data to the redshift table: $msoIlecRSTableName")
    val preActionQueryMsoIlec = s"TRUNCATE TABLE $msoIlecRSTableName;"
    RedshiftUtils.redshiftWrite(
      maxMsoIlecFootprintsFinalOutputPairwiseData,
      msoIlecRSTableName,
      SaveMode.Overwrite,
      preActionQuery = Some(preActionQueryMsoIlec)
    )(rsConfig)

    // Load Boost Market data in redshift
    val boostRSTableName = s"${config.redshiftTableBoostSchema}.${config.redshiftTableNameBoost}"
    logger.info(s"Writing Boost Market final output pairwise demo data to the redshift table: $boostRSTableName")
    val preActionQueryBoost = s"TRUNCATE TABLE $boostRSTableName;"
    RedshiftUtils.redshiftWrite(
      maxBoostMarketFinalOutputPairwiseData,
      boostRSTableName,
      SaveMode.Overwrite,
      preActionQuery = Some(preActionQueryBoost)
    )(rsConfig)
  }

/**
  * Returns a DataFrame containing rows with the latest creation date for each month.
  * @param df The input DataFrame containing at least the columns "month" and "creation_date".
  * @return DataFrame with rows filtered to only those with the maximum creation date per month.
  */

  def getLatestMonthByMaxCreationDate(df: DataFrame): DataFrame = {
    val dateSelect = df.groupBy("month")
      .agg(max("creation_date").alias("max_creation_date"))
      .orderBy(asc("month"))

    val result = df.as("output_data")
      .join(dateSelect, df("month") === dateSelect("month") && df("creation_date") === dateSelect("max_creation_date"))
      .select("output_data.*")
    result
  }
}
