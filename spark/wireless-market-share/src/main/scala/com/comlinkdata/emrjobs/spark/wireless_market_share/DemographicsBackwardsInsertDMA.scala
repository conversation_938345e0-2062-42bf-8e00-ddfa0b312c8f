package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.largescale.schema.wireless_market_share.{MonthlyOutputWithEthnicityDMA, NationalWCVFlows, WCVFlowsDMA, WmsDma}
import org.apache.spark.sql.{Dataset, SparkSession}
import java.time.LocalDate


// Backwards insert into table wms_ethnicity_test.dma_monthly_output_ethnicity_age_income_v8
class DemographicsBackwardsInsertDMA(implicit spark: SparkSession) {

  import spark.implicits._

  /**
    * Method used to generate previous month demographics output based on current month demographics and subscribers
    * @param newMonth: The month for which we have to generate the demographics
    * @param nationalWCVFlowsData: National WCV flows data
    * @param subscribersWithEthnicityData: Existing month demographics data
    * @param wcvFlowsDMAData: WCV flows data (Monthly ported wins and losses)
    * @param wmsDMAData: Subscribers output of the month for which we need to calculate the demographics
    * @return Returns the demographics output dataset of the month "newMonth"
    */
  def calculateEthnicityMonthlyOutputBackwardsInsert(
    newMonth: LocalDate,
    nationalWCVFlowsData: Dataset[NationalWCVFlows], // national_wcv_flows_ethnicity_age_income
    subscribersWithEthnicityData: Dataset[MonthlyOutputWithEthnicityDMA], // dma_monthly_output_ethnicity_age_income_v8
    wcvFlowsDMAData: Dataset[WCVFlowsDMA], // dma_wcv_flows
    wmsDMAData: Dataset[WmsDma] // WMS DMA all metrics data
  ): Dataset[MonthlyOutputWithEthnicityDMA]  = {

    val bigThreeCarriers = List("AT&T Postpaid", "Verizon Postpaid", "T-Mobile Postpaid")

    // line 7-28 demovarianceinput3months
    val demoVarianceThreeMonths = DemographicsUtilsDMA().calculateDemoVarianceInputThreeMonths(newMonth, nationalWCVFlowsData)

    // line 30-48 demovarianceinput20202022
    val demoVarianceFixedTwoYears = DemographicsUtilsDMA().calculateDemoVarianceFixedTwoYears(newMonth, nationalWCVFlowsData)

    // line 50-78 demo variance factor and line 80-89 demovariancefactorcompressed are combined
    val demoVarianceFactorCompressed = DemographicsUtilsDMA().calculateDemoVarianceFactorCompressed(
      newMonth, 1.1, 0.9, demoVarianceThreeMonths, demoVarianceFixedTwoYears)

    // line 92-114 subethnicitypctofcarrier
    val subEthnicityPercentOfCarrier = DemographicsUtilsDMA().calculateSubEthnicityPercentOfCarrierBackwardInsertMonthly(subscribersWithEthnicityData, newMonth)

    // line 118-130 totallosses
    val totalLossesData = DemographicsUtilsDMA().calculateTotalLosses(wcvFlowsDMAData)

    // line 134-145 totalwins
    val totalWinsData = DemographicsUtilsDMA().calculateTotalWins(wcvFlowsDMAData)

    // line 149-167 lossesthnicitypctofcarrier
    val lossesEthnicityPercentOfCarrierData = DemographicsUtilsDMA().calculateLossesEthnicityPercentOfCarrier(
      subEthnicityPercentOfCarrier, totalLossesData
    )

    // line 171-187 winsethnicitypctofcarrier
    val winsEthnicityPercentOfCarrierData = DemographicsUtilsDMA().calculateWinsEthnicityPercentOfCarrier(
      subEthnicityPercentOfCarrier, totalWinsData
    )

    // line 192-204 winnerpctofloser
    val winnerPercentOfLoserData = DemographicsUtilsDMA().calculateWinnerPercentOfLoserCreate(wcvFlowsDMAData)

    // line 208-219 loserpctofwinner
    val loserPercentOfWinnerData = DemographicsUtilsDMA().calculateLoserPercentOfWinnerCreate(wcvFlowsDMAData)

    // line 224-288 winslossesbyethnicityinterim
    val winsLossesByEthnicityInterimData = DemographicsUtilsDMA().calculateWinsLossesByEthnicityInterim1(
      lossesEthnicityPercentOfCarrierData, winsEthnicityPercentOfCarrierData, winnerPercentOfLoserData, loserPercentOfWinnerData
    )
    // line 291-318 winslossesbyethnicityinterim2
    val winsLossesByEthnicityInterim2Data = DemographicsUtilsDMA().calculateWinsLossesByEthnicityInterim2(
      winsLossesByEthnicityInterimData, demoVarianceFactorCompressed
    )
    // line 320-355 winslossesbyethnicityinterim3
    // line 357-388 winslossesbyethnicity
    val winsLossesByEthnicityData = DemographicsUtilsDMA().calculateWinsLossesByEthnicity(
      winsLossesByEthnicityInterim2Data
    )

    // line 391-416 winsfromaverageethnicitypctofcarrier
    val winsFromAverageEthnicityPercentOfCarrierData = DemographicsUtilsDMA().calculateWinsFromAverageEthnicityPercentOfCarrier(
      winsLossesByEthnicityData, totalWinsData
    )

    // line 420-444 lossesfromaverageethnicitypctofcarrier
    val lossesFromAverageEthnicityPercentOfCarrierData = DemographicsUtilsDMA().calculateLossesFromAverageEthnicityPercentOfCarrier(
      winsLossesByEthnicityData, totalLossesData
    )

    // line 451-515 wmswithethnicityfromaveragebackwards
    val wmsWithEthnicityFromAverageBackwardsData = DemographicsUtilsDMA().calculateWMSWithEthnicityFromAverageBackwards(
      wmsDMAData, subEthnicityPercentOfCarrier, lossesFromAverageEthnicityPercentOfCarrierData,
      winsFromAverageEthnicityPercentOfCarrierData, totalLossesData, totalWinsData
    )

    // line 520-530 wmswithethnicitywstartingsubsfromaveragebackwards
    val wmsWithEthnicityWithEndingSubsFromAverageBackwardsData = DemographicsUtilsDMA().calculateWMSWithEthnicityWithStartingSubsFromAverageBackwards(
      wmsWithEthnicityFromAverageBackwardsData
    )

    // line 526-528 packaging
    wmsWithEthnicityWithEndingSubsFromAverageBackwardsData
      .filter($"wms_month" === newMonth)
//      .withColumn("month", $"wms_month")   // commented because it was duplicate to versioning month column
      .select("*").na.fill(0).as[MonthlyOutputWithEthnicityDMA]
  }
}


object DemographicsBackwardsInsertDMA {
  def apply()(implicit spark: SparkSession): DemographicsBackwardsInsertDMA = new DemographicsBackwardsInsertDMA()

}
