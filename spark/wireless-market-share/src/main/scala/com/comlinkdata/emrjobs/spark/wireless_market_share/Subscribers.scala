//package com.comlinkdata.emrjobs.spark.wireless_market_share
//
//import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}
//import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
//import com.comlinkdata.emrjobs.spark.wireless_market_share.inputs.NationalIndustryModelInputs
//import com.comlinkdata.largescale.schema.wireless_market_share._
//import org.apache.spark.sql.functions._
//
//
//class Subscribers(implicit spark: SparkSession) {
//
//  import spark.implicits._
//
//  /**
//    * The function computes and returns the Subscribers data
//    * @param industryModel: Industry Model input dataset
//    * @param installedBase: Installed base input dataset
//    * @param storeLocations: Store Locations input dataset
//    * @return Returns the Subscribers dataset of type Dataset[subscribersOutput]
//    */
//  def computeSubscribers(
//    industryModel: Dataset[IndustryModel],
//    installedBase: Dataset[InstalledBase],
//    storeLocations: Dataset[StoreLocations],
//  ): Dataset[subscribersOutput] = {
//    val carriersIBGeoShareData = computeCarriersIBGeoShare(installedBase)
//    val storeLocGeoShareData = computeStoreLocGeoShare(storeLocations)
//
//    computeEstimatedSubs(
//      industryModel, carriersIBGeoShareData, storeLocGeoShareData
//    )
//  }
//
//  def computeInitialDistribution(
//    industryModelData: Dataset[IndustryModel],
//    installedBaseData: Dataset[InstalledBase],
//    portedCarrierWins: Dataset[PortedCarrierWins],
//    storeLocationsData: Dataset[StoreLocations],
//    populationData: Dataset[Population],
//    multiplierWeights: Dataset[SubscriberWeights]
//  ): Dataset[subscribersInitialDistributionsOutput]  = {
//
//    val portedCarrierWinsRenamed = portedCarrierWins
//      .select($"date_trunc" as "customer_base_date",$"winner" as "current_holder", $"primary_plan_type_id" as "plan_type_id",$"dma", $"switches")
//      .groupBy($"customer_base_date",$"current_holder",$"dma")
//      .agg(sum($"switches") as "subscribers_ported_wins")
//    val storeLocationsRenamed = storeLocationsData
//      .select($"store_count",$"store_type",$"carrier" as "current_holder",$"dma")
//      .groupBy($"current_holder",$"dma")
//      .agg(sum($"store_count") as "subscribers_stores")
//    val populationRenamed = populationData
//      .select($"dma", $"pop" as "subscribers_pop")
//
//    val temp =
//    installedBaseData
//      .withColumnRenamed("mvno_winning_plan_type_id","plan_type_id")
//      .groupBy($"customer_base_date",$"current_holder",$"dma",$"plan_type_id")
//      .agg(sum($"total_customers") as "total_customers_sum")
//      .select($"current_holder",$"customer_base_date",$"plan_type_id",$"dma",$"total_customers_sum" as "total_customers")
//      .join(portedCarrierWinsRenamed,Seq("current_holder","dma","customer_base_date"),"INNER")
//    val temp2 = temp
//      .join(storeLocationsRenamed,Seq("current_holder","dma"),"INNER")
//    val temp3 = temp2
//      .join(populationRenamed,Seq("dma"),"INNER")
//    val temp4 = temp3
//      .join(multiplierWeights,Seq("current_holder"),"INNER")
////    println("installedBase counts: " + installedBaseData.count()) // TODO get rid of this later
////    installedBaseData.select($"current_holder").distinct().show()
////    installedBaseData.show()
////    println("ported wins counts: " + portedCarrierWinsRenamed.count())
////    portedCarrierWinsRenamed.select($"current_holder").distinct().show()
////    portedCarrierWinsRenamed.show()
////    println("temp1 counts: " + temp.count())
////    temp.show()
////    temp.select($"current_holder").distinct().show()
////    println("temp2 counts: " + temp2.count())
////    temp2.show()
////    temp2.select($"current_holder").distinct().show()
////    println("temp3 counts: " + temp3.count())
////    temp3.show()
////    temp3.select($"current_holder").distinct().show()
////    println("temp4 counts: " + temp4.count())
////    temp4.show()
////    temp4.select($"current_holder").distinct().show()
//
//    val estimatedInitialDistribution = temp4
//        .select(
//          $"current_holder",
//          $"customer_base_date",
//          $"plan_type_id",
//          $"dma",
//          $"total_customers" * $"install_base_multiplier" +
//          $"subscribers_ported_wins" * $"ported_wins_multiplier" +
//          $"subscribers_stores" * $"store_data_multiplier" +
//          $"subscribers_pop" * $"population_multiplier" as "initial_distribution"
//        )
//        .as[subscribersInitialDistributionsOutput]
//
//    val industryModelCount = industryModelData
//      .withColumn("customer_base_date", NationalIndustryModelInputs().generateCustomerBaseDate($"year",$"month"*lit(3))) // TODO move generateCustomerBaseDate to commons maybe?
//      .withColumnRenamed("brand","current_holder")
//      .groupBy($"customer_base_date",$"current_holder")
//      .agg(sum("subscribers") as "industry_model_count")
//
//    val initialDistributionCount = estimatedInitialDistribution
//      .groupBy($"customer_base_date",$"current_holder")
//      .agg(sum("initial_distribution") as "initial_distribution_count")
//
//    println("estimated initial distribution")
//    estimatedInitialDistribution.show(100,false)
//    println("industry model counts")
//    industryModelCount.show(100,false)
//    println("initial distribution")
//    initialDistributionCount.show(100,false)
//
//    estimatedInitialDistribution
//      .join(industryModelCount,Seq("customer_base_date","current_holder"),"LEFT")
//      .join(initialDistributionCount, Seq("customer_base_date", "current_holder"), "LEFT")
//      .select(
//      $"current_holder",
//      $"customer_base_date",
//      $"plan_type_id",
//      $"dma",
//      $"initial_distribution" * $"industry_model_count" / $"initial_distribution_count" as "initial_distribution"
//    )
//      .as[subscribersInitialDistributionsOutput]
//
//  }
//  // old functions past here, delete
//  /**
//    * The function computes estimated subscribers based on the geographic shares of
//    * Installed base and store locations data
//    * @param industryModelData: Industry Model input dataset
//    * @param carriersIBGeoShareData: Geographic share of carriers installed base data
//    * @param storeLocGeoShareData: Geographic share of carriers store locations data
//    * @return Returns estimated subscribers dataset of type Dataset[subscribersOutput]
//    */
//  def computeEstimatedSubs(
//    industryModelData: Dataset[IndustryModel],
//    carriersIBGeoShareData: Dataset[geoShareIB],
//    storeLocGeoShareData: Dataset[geoShareStoreLoc]
//  ): Dataset[subscribersOutput] = {
//    val A = carriersIBGeoShareData.alias("ib").join(
//      storeLocGeoShareData.alias("sl"),
//      ($"current_holder" === $"carrier") &&
//        ($"ib.cma" === $"sl.cma") &&
//        ($"ib.dma" === $"sl.dma"),
//      "LEFT"
//    ).select(
//      $"current_holder" as "brand",
//      $"customer_base_date",
//      $"mvno_winning_plan_type_id" as "plan_type",
//      $"ib.cma",
//      $"ib.dma",
//      carriersIBGeoShareData("share")*0.8 + storeLocGeoShareData("share")*0.2 as "share"
//    )
//
//    industryModelData
//      .withColumn("customer_base_date", NationalIndustryModelInputs().generateCustomerBaseDate($"year",$"month"))
//      .join(A, Seq("brand", "plan_type", "customer_base_date"), "LEFT")
//      .select(
//        $"brand",
//        $"customer_base_date",
//        $"plan_type",
//        $"cma",
//        $"dma",
//        industryModelData("subscribers") * A("share") as "estimated_subs"
//      )
//      .na.fill(0)
//      .as[subscribersOutput]
//  }
//
//  /**
//    * The function computes the geographic share of carriers installed base data
//    * @param installedBase: Installed base input dataset
//    * @return Returns a dataset of type Dataset[geoShareIB]
//    */
//  def computeCarriersIBGeoShare(
//    installedBase: Dataset[InstalledBase]
//  ): Dataset[geoShareIB] = {
//
//      val A = installedBase.groupBy(
//        $"current_holder",
//        $"customer_base_date",
//        $"mvno_winning_plan_type_id",
//        $"cma",
//        $"dma"
//      ).agg(
//        sum($"total_customers") as "A_total_customers"
//      ).select(
//        $"current_holder",
//        $"customer_base_date",
//        $"mvno_winning_plan_type_id",
//        $"cma",
//        $"dma",
//        $"A_total_customers"
//      )
//
//    val B = installedBase.groupBy(
//      $"current_holder",
//      $"customer_base_date",
//      $"mvno_winning_plan_type_id"
//    ).agg(
//      sum($"total_customers") as "B_total_customers"
//    ).select(
//      $"current_holder",
//      $"customer_base_date",
//      $"mvno_winning_plan_type_id",
//      $"B_total_customers"
//    )
//
//    A
//      .join(B, Seq("current_holder", "customer_base_date", "mvno_winning_plan_type_id"), "LEFT")
//      .select(
//        $"current_holder",
//        $"customer_base_date",
//        $"mvno_winning_plan_type_id",
//        $"cma",
//        $"dma",
//        $"A_total_customers" / $"B_total_customers" as "share"
//      ).as[geoShareIB]
//  }
//
//  /**
//    * The function computes the geographic share of the carriers store locations data
//    * @param storeLocations: Store Locations input dataset
//    * @return Returns a dataset of type Dataset[geoShareStoreLoc]
//    */
//  def computeStoreLocGeoShare(
//    storeLocations: Dataset[StoreLocations]
//  ): Dataset[geoShareStoreLoc] = {
//
//    val A = storeLocations.groupBy(
//      $"carrier",
//      $"cma",
//      $"dma"
//    ).agg(
//      sum($"store_count") as "A_store_count"
//    ).select(
//      $"carrier",
//      $"cma",
//      $"dma",
//      $"A_store_count"
//    )
//
//    val B = storeLocations.groupBy(
//      $"carrier",
//    ).agg(
//      sum($"store_count") as "B_store_count"
//    ).select(
//      $"carrier",
//      $"B_store_count"
//    )
//
//    A
//      .join(B, Seq("carrier"), "LEFT")
//      .select(
//        $"carrier",
//        $"cma",
//        $"dma",
//        $"A_store_count" / $"B_store_count" as "share"
//      ).as[geoShareStoreLoc]
//  }
//}
//
//object Subscribers {
//
//  def apply()(implicit spark: SparkSession): Subscribers = new Subscribers()
//
//}
