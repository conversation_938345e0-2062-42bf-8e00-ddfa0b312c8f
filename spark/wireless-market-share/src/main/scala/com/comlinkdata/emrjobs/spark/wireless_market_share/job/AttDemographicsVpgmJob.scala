package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.emrjobs.spark.wireless_market_share.{DemographicsBackwardsInsertVPGM, DemographicsForwardsInsertVPGM, DemographicsMonthlyCreateOutputVPGM}
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.commons.io.dataset.writeCsvOverwrite
import com.comlinkdata.largescale.commons.{SparkJob, SparkJobRunner, Utils}
import com.comlinkdata.largescale.schema.wireless_market_share._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{Dataset, SparkSession}
import java.time.LocalDate
import java.net.URI
import scala.util.Try


case class AttDemographicsVpgmJobConfig(
  monthToProcess: Option[LocalDate],
  jobToProcess: String,
  nationalWCVFlowsLoc: URI,
  subscribersWithEthnicityLoc: URI,
  vpgmMonthlyOutputEthnicityAgeIncomeLoc: URI,
  wcvFlowsVPGMLoc: URI,
  wmsVPGMLoc: URI,
  outputBasePath: URI,
  outputPartitions: Int
)

object AttDemographicsVpgmJobConfig {

  val sample: AttDemographicsVpgmJobConfig = AttDemographicsVpgmJobConfig(
    monthToProcess = Option(LocalDate.of(2022, 7, 1)),
    jobToProcess = "CREATE",
    nationalWCVFlowsLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/demographics/vpgm/national_wcv_flows_ethnicity_age_income/",
    subscribersWithEthnicityLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/demographics/vpgm/vpgm_subs_by_ethnicity_age_income_jun2022/",
    vpgmMonthlyOutputEthnicityAgeIncomeLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/demographics/vpgm/vpgm_monthly_output_ethnicity_age_income_v8/",
    wcvFlowsVPGMLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/demographics/vpgm/vpgm_wcv_flows/",
    wmsVPGMLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/demographics/vpgm/vpgm_wms_input/",
    outputBasePath = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/outputs/with_demographics/vpgm/",
    outputPartitions = 1
  )
}


object AttDemographicsVpgmJob extends SparkJob[AttDemographicsVpgmJobConfig](AttDemographicsVpgmJobRunner)

object AttDemographicsVpgmJobRunner extends SparkJobRunner[AttDemographicsVpgmJobConfig] with LazyLogging {
  def runJob(config: AttDemographicsVpgmJobConfig)(implicit spark: SparkSession): Unit  = {
    import spark.implicits._

    // Default processing date would be latest month
    val processingDate = LocalDate.now().withDayOfMonth(1).toString

    // Specific month to process
    val monthToProcess = config.monthToProcess.getOrElse(LocalDate.now().withDayOfMonth(1))

    // Specific Job (CREATE, FORWARD, BACKWARD) to process
    val jobToProcess = config.jobToProcess

    // Read common schema's
    val year = monthToProcess.getYear

    val nationalWCVFlowsPath = config.nationalWCVFlowsLoc
    logger.info(s"Loading National WCV flows data from the path: ${nationalWCVFlowsPath}")
    val nationalWCVFlowsData = NationalWCVFlows.read(nationalWCVFlowsPath)

    val subscribersWithEthnicityPath = config.subscribersWithEthnicityLoc
    logger.info(s"Loading VPGM subscribers with demographics data from the path: ${subscribersWithEthnicityPath}")
    val subscribersWithEthnicityData = SubscribersByEthnicityAgeIncomeVPGM.read(subscribersWithEthnicityPath)

    // This can be used for the forward insert and backward insert query only
    var monthlyOutputWithEthnicityData: Dataset[MonthlyOutputWithEthnicityVPGM] = null
    if(jobToProcess.equals("FORWARD") || jobToProcess.equals("BACKWARD")){

      var month: String = null
      var year: String = null

      if(jobToProcess.equals("BACKWARD")) {
        val nextMonth = monthToProcess.plusMonths(1)
        month = nextMonth.getMonthValue.formatted("%02d")
        year = nextMonth.getYear.toString
      }

      if(jobToProcess.equals("FORWARD")) {
        val prevMonth = monthToProcess.minusMonths(1)
        month = prevMonth.getMonthValue.formatted("%02d")
        year = prevMonth.getYear.toString
      }

      val vpgmMonthlyOutputEthnicityAgeIncomePath = URI create s"${config.vpgmMonthlyOutputEthnicityAgeIncomeLoc.toString}year=$year/month=$month/"
      val futz = CldFileUtils.newBuilder.forUri(vpgmMonthlyOutputEthnicityAgeIncomePath).build
      val (inputPath, latestR) = Utils.maxPartitionValue(vpgmMonthlyOutputEthnicityAgeIncomePath, "r", futz, None)

      logger.info(s"Loading Monthly VPGM subscribers with demographics data from the path: ${inputPath}")
      monthlyOutputWithEthnicityData = MonthlyOutputWithEthnicityVPGM.read_csv(inputPath)
    }

    val wcvFlowsVPGMPath = Utils.joinPaths(
      config.wcvFlowsVPGMLoc, s"processing_date=${processingDate}")
    logger.info(s"Loading VPGM WCV flows data from the path: ${wcvFlowsVPGMPath}")
    val wcvFlowsVPGMData = WCVFlowsVPGM.read(wcvFlowsVPGMPath)

    val wmsVPGMPath = Utils.joinPaths(
      config.wmsVPGMLoc, s"processing_date=${processingDate}")
    logger.info(s"Loading Subscriber's Output data from the path: ${wmsVPGMPath}")
    val wmsVPGMData = WmsVPGM.read(wmsVPGMPath)


    // ** Calculations **

    // Declarations
    var demographicsInitialMonthOutput = spark.emptyDataset[MonthlyOutputWithEthnicityVPGM]
    var demographicsForwardsInitialMonthOutput = spark.emptyDataset[MonthlyOutputWithEthnicityVPGM]
    var demographicsBackwardsMonthOutput = spark.emptyDataset[MonthlyOutputWithEthnicityVPGM]

    jobToProcess match {

      case "CREATE" =>
        logger.info(s"Calculating the demographics for the initial month...")
        demographicsInitialMonthOutput = DemographicsMonthlyCreateOutputVPGM().calculateEthnicityMonthlyOutput(
          monthToProcess, nationalWCVFlowsData, subscribersWithEthnicityData, wcvFlowsVPGMData, wmsVPGMData
        )
        logger.info(s"Finished calculating the demographics for the initial month.")

      case "FORWARD" =>
        // forwards insert
        logger.info(s"Calculating the demographics for the forwards month...")
        demographicsForwardsInitialMonthOutput = DemographicsForwardsInsertVPGM().calculateEthnicityMonthlyOutputForwardsInsert(
          monthToProcess, nationalWCVFlowsData, monthlyOutputWithEthnicityData, wcvFlowsVPGMData, wmsVPGMData
        )

      case "BACKWARD" =>
        // Backwards Insert
        logger.info(s"Calculating the demographics for the backwards month...")
        demographicsBackwardsMonthOutput = DemographicsBackwardsInsertVPGM().calculateEthnicityMonthlyOutputBackwardsInsert(
          monthToProcess, nationalWCVFlowsData, monthlyOutputWithEthnicityData, wcvFlowsVPGMData, wmsVPGMData
        )
    }


    // ** Write data to S3 **
    val month = monthToProcess.getMonthValue.formatted("%02d")
    val outputBasePath = URI create s"${config.outputBasePath.toString}year=$year/month=$month/"
    val futz = CldFileUtils.newBuilder.forUri(outputBasePath).build
    val maybeUriLatestR = Try(Utils.maxPartitionValue(outputBasePath, "r", futz, None)).toOption  // latest revision
    val newR = maybeUriLatestR.map(ur => ur._2.toInt + 1).getOrElse(1)   // latest revision increment by 1
    val outputPath = Utils.joinPaths(outputBasePath.toString, s"r=$newR")

    jobToProcess match {

      case "CREATE" =>
        // Demographics Initial Month
        logger.info(s"Writing demographics for the initial month to the S3 location = $outputPath")
        writeCsvOverwrite(demographicsInitialMonthOutput, config.outputPartitions, outputPath)

      case "FORWARD" =>
        // Demographics Forwards Initial Month
        logger.info(s"Writing demographics for the forwards month to the S3 location = $outputPath")
        writeCsvOverwrite(demographicsForwardsInitialMonthOutput, config.outputPartitions, outputPath)

      case "BACKWARD" =>
        // Demographics Backwards Initial Month
        logger.info(s"Writing demographics for the backwards month to the S3 location = $outputPath")
        writeCsvOverwrite(demographicsBackwardsMonthOutput, config.outputPartitions, outputPath)
    }
  }
}
