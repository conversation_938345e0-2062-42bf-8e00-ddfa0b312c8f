package com.comlinkdata.emrjobs.spark.wireless_market_share.dsa_stickee

import com.comlinkdata.largescale.commons.{RedshiftUtils, SparkJob, SparkJobRunner}
import com.opensignal.largescale.schema.broadband_stickee.{DsaBroadbandStickee, TechTypeSpIdLookup, TimeSeriesIspLookup}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SaveMode, SparkSession}

import java.net.URI
import java.time.LocalDate


case class BroadbandDsaAggregateJobConfig(
  processingDate: Option[LocalDate],
  dsaBroadbandOldBasePath: URI,
  dsaBroadbandBasePath: URI,
  techTypeSpIdLookupPath: URI,
  timeSeriesLookUpPath: URI,
  redshiftTableSchema: String,
  redshiftTableName: String,
  redshiftJdbcEndpoint: String,
  redshiftUserName: String,
  redshiftParameterStoreKey: String,
  redshiftTempLocation: String,
  partialOverwrite: Boolean
)

object BroadbandDsaAggregateJobConfig {
  val sample: BroadbandDsaAggregateJobConfig = BroadbandDsaAggregateJobConfig(
    processingDate = Option(LocalDate.of(2025, 4, 4)),
    dsaBroadbandOldBasePath = URI create "s3://stickee.magpie.opensignal/broadband/USA/",
    dsaBroadbandBasePath = URI create "s3://stickee.magpie.opensignal/USA/production/DSA/stickee_bb01/weekly/",
    techTypeSpIdLookupPath = URI create "s3://e000-comlinkdata-com/dev/broadband_dsa_stickee/lookups/tech_type_spid_lookup/",
    timeSeriesLookUpPath = URI create "s3://e000-comlinkdata-com/dev/broadband_dsa_stickee/lookups/timeseries_lookup/",
    redshiftTableSchema = "broadband",
    redshiftTableName = "broadband_dsa_events_weekly_stickee_agg",
    redshiftJdbcEndpoint = "******************************************************************************",
    redshiftUserName = "rpts_loader_dev",
    redshiftParameterStoreKey = "/c300/reporting_environment/rpts_loader_dev",
    redshiftTempLocation = "s3://e000-comlinkdata-com/dev/redshift-uploads/dsa_broadband_stickee/",
    partialOverwrite = false  // by default
  )
}


object BroadbandDsaAggregateJob extends SparkJob[BroadbandDsaAggregateJobConfig](BroadbandDsaAggregateJobRunner)

object BroadbandDsaAggregateJobRunner extends SparkJobRunner[BroadbandDsaAggregateJobConfig] with LazyLogging {

  def runJob(config: BroadbandDsaAggregateJobConfig)(implicit spark: SparkSession): Unit = {

    val latestFridaysDate = BroadbandDsa().getLastFridayDate(LocalDate.now)
    val processingDate = config.processingDate.getOrElse(latestFridaysDate)
    val year = processingDate.getYear
    val month = processingDate.getMonthValue.formatted("%02d")
    val day = processingDate.getDayOfMonth.formatted("%02d")
    val cutOffDate = LocalDate.of(2025, 8, 15)

    // Read raw data based on the old or new path based on the processing date
    val dsaBroadbandStickeePath  = if (processingDate.isBefore(cutOffDate)) {
      URI create s"${config.dsaBroadbandOldBasePath}/$year/$month/$day"
    } else {
      URI create s"${config.dsaBroadbandBasePath}/year=$year/month=$month/day=$day"
    }
    logger.info(s"Reading DSA Broadband Data for the Current week from the path = $dsaBroadbandStickeePath")
    val rawDsaBBData = DsaBroadbandStickee.read(dsaBroadbandStickeePath)

    // Read Raw data for last week last best scrape
    // In case of partial delivery get the latest Friday's date first and then based on that get the last friday date
    val latestFridayForPartialDelivery = BroadbandDsa().getLastFridayDate(processingDate)
    val lastFridaysDate = BroadbandDsa().getLastFridayDate(latestFridayForPartialDelivery.minusDays(1))  // calculate the last Friday's date based on the latest Friday's date
    val yearL = lastFridaysDate.getYear
    val monthL = lastFridaysDate.getMonthValue.formatted("%02d")
    val dayL = lastFridaysDate.getDayOfMonth.formatted("%02d")

    // Read raw data based on the old or new path based on the lastFridaysDate
    val dsaBroadbandStickeeLastWeekPath  = if (lastFridaysDate.isBefore(cutOffDate)) {
      URI create s"${config.dsaBroadbandOldBasePath}/$yearL/$monthL/$dayL"
    } else {
      URI create s"${config.dsaBroadbandBasePath}/year=$yearL/month=$monthL/day=$dayL"
    }
    logger.info(s"Reading DSA Broadband Data for the last week from the path = $dsaBroadbandStickeeLastWeekPath")
    val rawDsaBBLastWeekData = DsaBroadbandStickee.read(dsaBroadbandStickeeLastWeekPath)

    // Read lookup data for tech type and parent sp id
    logger.info(s"Reading Tech Type to SP ID lookup data from the path = ${config.techTypeSpIdLookupPath}")
    val techTypeSpIdLookup = TechTypeSpIdLookup.read(config.techTypeSpIdLookupPath)

    // Read lookup data for tech type and parent sp id
    logger.info(s"Reading Time Series lookup data from the path = ${config.timeSeriesLookUpPath}")
    val timeSeriesLookUpData = TimeSeriesIspLookup.read(config.timeSeriesLookUpPath)

    // Generate the Agg
    println("Generating the aggregation data...")
    val dsaBBAggData = BroadbandDsa().generateDsaBBAggData(
      rawDsaBBData, rawDsaBBLastWeekData, techTypeSpIdLookup, timeSeriesLookUpData
    )

    // Redshift config
    val rsConfig = RedshiftUtils.RedshiftConfig(
      rsTemporaryLocation = URI create config.redshiftTempLocation,
      rsJdbcEndpoint = config.redshiftJdbcEndpoint,
      rsUserName = config.redshiftUserName,
      rsParameterStoreKey = config.redshiftParameterStoreKey
    )

    val dsaBroadbandAggTableName = s"${config.redshiftTableSchema}.${config.redshiftTableName}"

    // Rewrite/Overwrite the partial re-delivery data
    if(config.partialOverwrite) {
      // Rewrite/Overwrite to Redshift
      logger.info(s"Rewriting/Overwriting the partial (non-fridays) delivered data to Redshift table - ${dsaBroadbandAggTableName}")
      BroadbandDsa().writePartialDeliveryDataToRedshift(
        dsaBBAggData,
        dsaBroadbandAggTableName
      )(rsConfig)
    }
    else {  // Write the usual Friday's delivery data.

      logger.info(s"Writing usual delivery of the DSA Broadband aggregated data to the redshift table: $dsaBroadbandAggTableName")

      if (dsaBBAggData != null && !dsaBBAggData.isEmpty) {
        // Get the report date (Friday's date) to delete existing data before writing
        val reportDate = BroadbandDsa().extractReportDate(dsaBBAggData.toDF())

        // Pre-action query to delete the data based on the report_date to avoid appending the duplicate rows
        // The pre-action query will help if we have to reprocess the same Friday's delivery

        val preActionQueryDsaBroadband = s"DELETE FROM $dsaBroadbandAggTableName WHERE report_date = '$reportDate';"
        RedshiftUtils.redshiftWrite(
          dsaBBAggData,
          dsaBroadbandAggTableName,
          SaveMode.Append,
          preActionQuery = Some(preActionQueryDsaBroadband)
        )(rsConfig)

      } else {
        logger.warn("dsaBBAggData is null or empty, skipping Redshift write.")
      }

    }

  } // end of runJob

}

