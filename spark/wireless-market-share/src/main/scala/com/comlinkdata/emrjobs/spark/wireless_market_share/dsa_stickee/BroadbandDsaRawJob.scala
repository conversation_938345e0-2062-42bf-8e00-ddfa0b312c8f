package com.comlinkdata.emrjobs.spark.wireless_market_share.dsa_stickee

import com.comlinkdata.largescale.commons.{RedshiftUtils, SparkJob, SparkJobRunner}
import com.opensignal.largescale.schema.broadband_stickee.DsaBroadbandStickee
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{SaveMode, SparkSession}
import java.net.URI
import java.time.LocalDate


case class BroadbandDsaRawJobConfig(
  processingDate: Option[LocalDate],
  dsaBroadbandOldBasePath: URI,
  dsaBroadbandBasePath: URI,
  redshiftTableSchema: String,
  redshiftTableName: String,
  redshiftJdbcEndpoint: String,
  redshiftUserName: String,
  redshiftParameterStoreKey: String,
  redshiftTempLocation: String
)

object BroadbandDsaRawJobConfig {
  val sample: BroadbandDsaRawJobConfig = BroadbandDsaRawJobConfig(
    processingDate = Option(LocalDate.of(2025, 4, 4)),
    dsaBroadbandOldBasePath = URI create "s3://stickee.magpie.opensignal/broadband/USA/",
    dsaBroadbandBasePath = URI create "s3://stickee.magpie.opensignal/USA/production/DSA/stickee_bb01/weekly/",
    redshiftTableSchema = "broadband",
    redshiftTableName = "broadband_dsa_events_weekly_stickee",
    redshiftJdbcEndpoint = "******************************************************************************",
    redshiftUserName = "rpts_loader_dev",
    redshiftParameterStoreKey = "/c300/reporting_environment/rpts_loader_dev",
    redshiftTempLocation = "s3://e000-comlinkdata-com/prod/redshift-uploads/dsa_broadband_stickee"
  )
}


object BroadbandDsaRawJob extends SparkJob[BroadbandDsaRawJobConfig](BroadbandDsaRawJobRunner)

object BroadbandDsaRawJobRunner extends SparkJobRunner[BroadbandDsaRawJobConfig] with LazyLogging {

  def runJob(config: BroadbandDsaRawJobConfig)(implicit spark: SparkSession): Unit = {

    val latestFridaysDate = BroadbandDsa().getLastFridayDate(LocalDate.now)
    val processingDate = config.processingDate.getOrElse(latestFridaysDate)
    val year = processingDate.getYear
    val month = processingDate.getMonthValue.formatted("%02d")
    val day = processingDate.getDayOfMonth.formatted("%02d")
    val cutOffDate = LocalDate.of(2025, 8, 15)

    // Read raw data based on the old or new path based on the processing date
    val dsaBroadbandStickeePath  = if (processingDate.isBefore(cutOffDate)) {
      URI create s"${config.dsaBroadbandOldBasePath}/$year/$month/$day"
    } else {
      URI create s"${config.dsaBroadbandBasePath}/year=$year/month=$month/day=$day"
    }
    logger.info(s"Reading DSA Broadband Raw Data for the Current week from the path = $dsaBroadbandStickeePath")
    val rawDsaBBData = DsaBroadbandStickee.read(dsaBroadbandStickeePath)

    // Set address (PII) columns to null before RS upload and Remove duplicate rows
    val getCorrectRawData = BroadbandDsa().getRawDataWithoutPII(rawDsaBBData)

    // Get the actual date (Friday's date) to overwrite the data
    val actualDate = getCorrectRawData.select("actual_date").distinct().collect().map(_.getDate(0)).head

    // Redshift config
    val rsConfig = RedshiftUtils.RedshiftConfig(
      rsTemporaryLocation = URI create config.redshiftTempLocation,
      rsJdbcEndpoint = config.redshiftJdbcEndpoint,
      rsUserName = config.redshiftUserName,
      rsParameterStoreKey = config.redshiftParameterStoreKey
    )

    // Write to Redshift
    val dsaBroadbandRawTableName = s"${config.redshiftTableSchema}.${config.redshiftTableName}"
    logger.info(s"Writing DMA data to the redshift table: $dsaBroadbandRawTableName")
    // Define your custom pre-action query to delete the data based on the actual_date to avoid appending the duplicate rows
    val preActionQueryDsaBroadband = s"DELETE FROM $dsaBroadbandRawTableName WHERE actual_date = '$actualDate';"
    RedshiftUtils.redshiftWrite(
      getCorrectRawData,
      dsaBroadbandRawTableName,
      SaveMode.Append,
      preActionQuery = Some(preActionQueryDsaBroadband)
    )(rsConfig)

  } // end of runJob

}

