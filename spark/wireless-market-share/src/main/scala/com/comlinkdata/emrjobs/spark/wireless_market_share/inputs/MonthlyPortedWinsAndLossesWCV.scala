package com.comlinkdata.emrjobs.spark.wireless_market_share.inputs

import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share.WirelessMovementWideGeneric
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.{CustomRegionsPreProdLookup, FZipLookup}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Column, DataFrame, Dataset, SparkSession}
import java.sql.Date


class MonthlyPortedWinsAndLossesWCV(implicit spark: SparkSession) {

  import spark.implicits._

  val start_date: Date = Date.valueOf("2019-01-01")

  /**
    * Method used to generate the monthly ported wins and losses data based on wireless installed base
    * @param monthToProcess: The processing month used as end date to generate the wins and losses
    * @param wirelessMovementData: Installed base WCV data
    * @param fZipData: Zip to DMA lookup data
    * @return Returns the ported wins and losses data based on the start date and monthToProcess
    */
  def calculateMonthlyPortedWinsAndLossesWcvExtractDma(
    monthToProcess: Date,
    wirelessMovementData: Dataset[WirelessMovementWideGeneric],
    fZipData: Dataset[FZipLookup]
  ):Dataset[PortedCarrierWinsLossesExtractDMA]  = {

    val filteredData = wirelessMovementData
      .withColumnRenamed("customer_base_date", "the_date")
      .where($"the_date" >= start_date && $"the_date" < monthToProcess)
      .where(
        (
          !$"primary_sp".isin(Seq(99999):_*) && !$"secondary_sp".isin(Seq(99999):_*) &&
          (
            (
              (
                !$"primary_sp".isin(Seq(3):_*) ||
                $"primary_plan_type_id" =!= 2
              ) ||
              !$"secondary_sp".isin(Seq(4,6):_*)
            ) &&
              (!$"primary_sp".isin(Seq(4,6):_*) ||

                !$"secondary_sp".isin(Seq(3):_*) ||
                $"secondary_plan_type_id" =!= 2
              )
          ) &&
          (
            !$"primary_sp".isin(Seq(3,609):_*) ||
            !$"secondary_sp".isin(Seq(3,609):_*) ||
            (
              !$"primary_plan_type_id".isin(Seq(2):_*) ||
              !$"secondary_plan_type_id".isin(Seq(2):_*)
            )
          ) &&
          (
            !$"primary_sp".isin(Seq(5,6544,6545,6546,6547,1):_*) ||
            !$"secondary_sp".isin(Seq(5,6544,6545,6546,6547,1):_*)
          ) &&
          (
            !$"primary_sp".isin(Seq(2,8):_*) ||
            !$"secondary_sp".isin(Seq(2,8):_*)
          ) &&
          (
            !$"primary_sp".isin(Seq(4,6):_*) ||
            !$"secondary_sp".isin(Seq(4,6):_*)
          ) &&
          (
            !$"primary_sp".isin(Seq(1113,6526):_*) ||
            !$"secondary_sp".isin(Seq(1113,6526):_*)
          ) &&
          (
            !$"primary_sp".isin(Seq(6576,6577,6578,6579,6580):_*) ||
            !$"secondary_sp".isin(Seq(6576,6577,6578,6579,6580):_*)
          )
        ) &&
        $"primary_plan_type_id".isin(Seq(2,1):_*) &&
        $"secondary_plan_type_id".isin(Seq(2,1):_*) &&
        $"ustn_ind" === 1
      )
      .transform(assignBrandNamesToSP($"primary_sp", $"primary_plan_type_id", "winner"))
      .transform(assignBrandNamesToSP($"secondary_sp", $"secondary_plan_type_id","loser"))

    filteredData.alias("a")
      .join(fZipData.alias("b"), $"a.zip_cd" === $"b.zip_varchar", "LEFT")
      .groupBy($"the_date", $"winner", $"loser", $"primary_plan_type_id", $"secondary_plan_type_id", $"b.dma", $"b.dma_name")
      .agg(sum($"adjusted_wins") as "switches")
      .select($"the_date" as "date_trunc", $"winner", $"loser", $"primary_plan_type_id",
        $"secondary_plan_type_id", $"dma", $"dma_name", $"switches")
      .as[PortedCarrierWinsLossesExtractDMA]
  }

  def calculateMonthlyPortedWinsAndLossesWcvExtractVPGM(
    monthToProcess: Date,
    wirelessMovementData: Dataset[WirelessMovementWideGeneric],
    customRegionsPreprodData: Dataset[CustomRegionsPreProdLookup]
  ):Dataset[PortedCarrierWinsLossesExtractVPGM]  = {

    val filteredData = wirelessMovementData
      .withColumnRenamed("customer_base_date", "the_date")
      .where($"the_date" >= start_date && $"the_date" < monthToProcess)
      .where(
        (
          !$"primary_sp".isin(Seq(99999):_*) && !$"secondary_sp".isin(Seq(99999):_*) &&
            (
              (
                (
                  !$"primary_sp".isin(Seq(3):_*) ||
                    $"primary_plan_type_id" =!= 2
                  ) ||
                  !$"secondary_sp".isin(Seq(4,6):_*)
                ) &&
                (!$"primary_sp".isin(Seq(4,6):_*) ||

                  !$"secondary_sp".isin(Seq(3):_*) ||
                  $"secondary_plan_type_id" =!= 2
                  )
              ) &&
            (
              !$"primary_sp".isin(Seq(3,609):_*) ||
                !$"secondary_sp".isin(Seq(3,609):_*) ||
                (
                  !$"primary_plan_type_id".isin(Seq(2):_*) ||
                    !$"secondary_plan_type_id".isin(Seq(2):_*)
                  )
              ) &&
            (
              !$"primary_sp".isin(Seq(5,6544,6545,6546,6547,1):_*) ||
                !$"secondary_sp".isin(Seq(5,6544,6545,6546,6547,1):_*)
              ) &&
            (
              !$"primary_sp".isin(Seq(2,8):_*) ||
                !$"secondary_sp".isin(Seq(2,8):_*)
              ) &&
            (
              !$"primary_sp".isin(Seq(4,6):_*) ||
                !$"secondary_sp".isin(Seq(4,6):_*)
              ) &&
            (
              !$"primary_sp".isin(Seq(1113,6526):_*) ||
                !$"secondary_sp".isin(Seq(1113,6526):_*)
              ) &&
            (
              !$"primary_sp".isin(Seq(6576,6577,6578,6579,6580):_*) ||
                !$"secondary_sp".isin(Seq(6576,6577,6578,6579,6580):_*)
              )
          ) &&
          $"primary_plan_type_id".isin(Seq(2,1):_*) &&
          $"secondary_plan_type_id".isin(Seq(2,1):_*) &&
          $"ustn_ind" === 1
      )
      .transform(assignBrandNamesToSP($"primary_sp", $"primary_plan_type_id", "winner"))
      .transform(assignBrandNamesToSP($"secondary_sp", $"secondary_plan_type_id","loser"))

    filteredData.alias("a")
      .join(customRegionsPreprodData.alias("b"), $"a.zip_cd" === $"b.geo_code_value", "LEFT")
      .groupBy($"the_date", $"winner", $"loser", $"primary_plan_type_id", $"secondary_plan_type_id", $"custom_region_name")
      .agg(sum($"adjusted_wins") as "switches")
      .select($"the_date" as "date_trunc", $"winner", $"loser", $"primary_plan_type_id",
        $"secondary_plan_type_id", $"custom_region_name", $"switches")
      .as[PortedCarrierWinsLossesExtractVPGM]
  }


  /**
    * The method used to assign the proper format brand names to service providers (SP)
    * @param sp: Service Provide number
    * @param plan_type: Plan type column name
    * @param brandColName: Brand column name
    * @param df: Source data that we have to transform
    * @return Return the dataframe by replacing SP with brand names
    */
  def assignBrandNamesToSP(sp: Column, plan_type: Column, brandColName: String)(df: DataFrame): DataFrame = {
    df
      .withColumn(
        brandColName,
        when(sp.isin(Seq(2,178,6050,2620):_*) && plan_type === 1, "AT&T_Prepaid Phone")
          .when(sp.isin(Seq(8):_*) && plan_type === 1, "Cricket Wireless_Prepaid Phone")
          .when(sp.isin(Seq(2, 178, 6050, 2620):_*) && plan_type === 2, "AT&T_Postpaid Phone")
          .when(sp.isin(Seq(3,3147,6042,6043,609):_*) && plan_type === 1, "Boost Mobile_Prepaid Phone")
          .when(sp.isin(Seq(3,3147,6042,6043,609):_*) && plan_type === 2, "T-Mobile_Postpaid Phone")
          .when(sp.isin(Seq(6):_*) && plan_type === 1, "MetroPCS_Prepaid Phone")
          .when(sp.isin(Seq(4):_*) && plan_type === 1, "T-Mobile_Prepaid Phone")
          .when(sp.isin(Seq(4,6):_*) && plan_type === 2, "T-Mobile_Postpaid Phone")
          .when(sp.isin(Seq(1):_*) && plan_type === 1, "Verizon Wireless_Prepaid Phone")
          .when(sp.isin(Seq(1):_*) && plan_type === 2, "Verizon Wireless_Postpaid Phone")
          .when(sp.isin(Seq(7):_*) && plan_type === 1, "U.S. Cellular_Prepaid Phone")
          .when(sp.isin(Seq(7):_*) && plan_type === 2, "U.S. Cellular_Postpaid Phone")
          .when(sp.isin(Seq(6495):_*) && plan_type === 1, "Altice_Prepaid Phone")
          .when(sp.isin(Seq(6495):_*) && plan_type === 2, "Optimum Mobile_Postpaid Phone")
          .when(sp.isin(Seq(6052):_*), "Xfinity Mobile_Postpaid Phone")
          .when(sp.isin(Seq(6105):_*), "Spectrum Mobile_Postpaid Phone")
          .when(sp.isin(Seq(1171):_*), "Cox Mobile_Postpaid Phone")
          .when(sp.isin(Seq(5,6544,6545,6546,6547):_*) && plan_type === 1, "Tracfone_Prepaid Phone")
          .when(plan_type === 1,"Other_Prepaid Phone")
          .when(plan_type === 2,"Other_Postpaid Phone")
          .otherwise("Other")
      )
  }

}

object MonthlyPortedWinsAndLossesWCV {
  def apply()(implicit spark: SparkSession): MonthlyPortedWinsAndLossesWCV = new MonthlyPortedWinsAndLossesWCV()
}
