package com.comlinkdata.emrjobs.spark.wireless_market_share.inputs

import com.comlinkdata.largescale.schema.wireless_market_share.redshift.{CaWirelessMovementWide, DDa, DSpIdCatFlanker, ShawMigrations, Wms10ModelWithPlanType2023Dec, Wms10SinglePeriodSubsBaseline2020Dec}
import com.comlinkdata.emrjobs.spark.wireless_market_share.model.CaMonthlyPortedWinsAndLossesOutput
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{DoubleType, StringType}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession, SaveMode}
import com.comlinkdata.largescale.commons.io.dataset.writeCsvOverwrite

import java.sql.Date

/**
  * This class contains methods to calculate monthly ported wins and losses for WMS in Canada.
  *
  * @param spark Implicit SparkSession needed for running Spark operations.
  */
class CaMonthlyPortedWinsAndLossesWMS(implicit spark: SparkSession) {
  import spark.implicits._

  /**
    * This method calculates the monthly ported wins and losses for WMS in Canada.
    *
    * The method processes data from several datasets, applies necessary transformations,
    * and aggregates the data to produce a final dataset containing the monthly ported wins and losses.
    *
    * @param wirelessMovementWideData                Dataset containing wireless movement data.
    * @param dDaData                                 Dataset containing DDa data.
    * @param dSpIdCatFlankerData                     Dataset containing DSpIdCatFlanker data.
    * @param shawMigrationsData                      Dataset containing Shaw migrations data.
    * @param wms10ModelWithPlanType2023DecData       Dataset containing WMS 10 Model with Plan Type (December 2023) data.
    * @param Wms10SinglePeriodSubsBaseline2020DecData Dataset containing WMS 10 Single Period Subs Baseline (December 2020) data.
    * @return                                        Dataset containing the calculated monthly ported wins and losses.
    */
  def calculateMonthlyPortedWinsAndLossesWMSExtractCa(
    wirelessMovementWideData: Dataset[CaWirelessMovementWide],
    dDaData: Dataset[DDa],
    dSpIdCatFlankerData: Dataset[DSpIdCatFlanker],
    shawMigrationsData: Dataset[ShawMigrations],
    wms10ModelWithPlanType2023DecData: Dataset[Wms10ModelWithPlanType2023Dec],
    Wms10SinglePeriodSubsBaseline2020DecData: Dataset[Wms10SinglePeriodSubsBaseline2020Dec]

  ):Dataset[CaMonthlyPortedWinsAndLossesOutput]  = {

    // Convert each Dataset to DataFrame
    val wirelessMovementWideDF: DataFrame = wirelessMovementWideData.toDF()
    val dDaDF: DataFrame = dDaData.toDF()
    val dSpIdCatFlankerDF: DataFrame = dSpIdCatFlankerData.toDF()
    val shawMigrationsDF: DataFrame = shawMigrationsData.toDF()
    val wms10ModelWithPlanType2023DecDF: DataFrame = wms10ModelWithPlanType2023DecData.toDF()
    val Wms10SinglePeriodSubsBaseline2020DecDF: DataFrame = Wms10SinglePeriodSubsBaseline2020DecData.toDF()

    /**
      * Step 1: Aggregates daily ported wins and losses.
      *
      * Filters data based on date and primary/secondary service provider values,
      * applies specific transformations, and aggregates the adjusted wins.
      *
      * @return DataFrame containing the daily ported aggregated data.
      */
    // Step 1: daily_ported_agg
    val dailyPortedAggDF = wirelessMovementWideDF
      .filter($"the_date" >= "2021-01-01" && $"primary_sp" =!= 99999 && $"secondary_sp" =!= 99999)
      .withColumn("primary_sp", when($"primary_sp" === 6015, 526)
        .when($"primary_sp" === 4955 && $"pruid" === 35 && $"cduid" =!= 3506, 6188)
        .otherwise($"primary_sp"))
      .withColumn("secondary_sp", when($"secondary_sp" === 6015, 526)
        .when($"secondary_sp" === 4955 && $"pruid" === 35 && $"cduid" =!= 3506, 6188)
        .otherwise($"secondary_sp"))
      .groupBy($"the_date", $"pruid", $"primary_sp", $"secondary_sp")
      .agg(round(sum($"adjusted_wins")).alias("adjusted_wins"))
      .filter($"primary_sp" =!= $"secondary_sp")

    /**
      * Step 2: Aggregates ported data monthly.
      *
      * Joins daily ported aggregated data with Shaw migrations data,
      * and aggregates the switches by month.
      *
      * @return DataFrame containing the monthly ported aggregated data.
      */
    // Step 2: ported_agg
    val portedAggDF = dailyPortedAggDF.alias("main")
      .join(
        shawMigrationsDF.alias("adj"),
        $"main.the_date" === $"adj.the_date" &&
          $"main.pruid" === $"adj.pruid" &&
          $"main.primary_sp" === $"adj.primary_sp" &&
          $"main.secondary_sp" === $"adj.secondary_sp",
        "left_outer"
      )
      .groupBy(
        date_trunc("month", $"main.the_date").cast("date").alias("date_trunc"),
        $"main.pruid",
        $"main.primary_sp",
        $"main.secondary_sp"
      )
      .agg(
        round(sum($"main.adjusted_wins") - sum(coalesce($"adj.migrations", lit(0)))).alias("switches")
      )

    /**
      * Step 3: Split plan types based on subscription data.
      *
      * Calculates the plan type split for each brand and network based on subscription data.
      * This is done separately for the baseline and the model data.
      *
      * @return DataFrames containing the plan type split before and after merging Shaw data.
      */
    // Step 3: plan_type_split
    val networkWindowSpec = Window.partitionBy("pruid", "network")
    val brandWindowSpec = Window.partitionBy("pruid", "brand")

    val planTypeSplitPreShawMergeDF = Wms10SinglePeriodSubsBaseline2020DecDF
      .withColumn("brand", regexp_replace($"brand", "( mobile)$", ""))
      .withColumn("brand_temp", regexp_replace(when($"network" === "freedom mobile", "freedom mobile").otherwise($"brand"), "( mobile)$", ""))
      .withColumn("plan_type_split",
        when($"network" === "freedom mobile",
          coalesce(lit(1.00) * $"subs" / when(sum($"subs").over(networkWindowSpec) === 0, lit(null)).otherwise(sum($"subs").over(networkWindowSpec)), lit(0)))
          .otherwise(
            coalesce(lit(1.00) * $"subs" / when(sum($"subs").over(brandWindowSpec) === 0, lit(null)).otherwise(sum($"subs").over(brandWindowSpec)), lit(0))))
      .select("pruid", "brand", "brand_temp", "plan_type_id", "plan_type_split")

    val modelWindowSpec = Window.partitionBy("pruid", "brand")
    val planTypeSplitPostShawMergeDF = wms10ModelWithPlanType2023DecDF
      .withColumn("brand", regexp_replace($"brand", "( mobile)$", ""))
      .withColumn("plan_type_split",
        coalesce(lit(1.00) * $"subs" / when(sum($"subs").over(modelWindowSpec) === 0, lit(null)).otherwise(sum($"subs").over(modelWindowSpec)), lit(0)))
      .select("pruid", "brand", "plan_type_id", "plan_type_split")

    /**
      * Step 4: Aggregate the final data.
      *
      * Joins the aggregated ported wins and losses data with additional data from DDa and DSpIdCatFlanker dataframes.
      * Applies transformations to classify the winner and loser based on the service provider group.
      * Aggregates the data by date, region, and the classified winner and loser to get the final result.
      *
      * @return DataFrame containing the final aggregated data with winner, loser, and the number of switches.
      */
    // Step 4: main_agg
      val ddDF = dDaDF.select($"pruid", $"prname").distinct()

      val joinedDF = portedAggDF.alias("main")
        .join(ddDF.alias("dd"), $"main.pruid" === $"dd.pruid", "left")
        .join(dSpIdCatFlankerDF.alias("a"), $"main.primary_sp" === $"a.sp_dim_id", "left")
        .join(dSpIdCatFlankerDF.alias("b"), $"main.secondary_sp" === $"b.sp_dim_id", "left")
        .withColumn("winner",
          when(
            regexp_replace($"a.sp_reporting_name_group", "( Wireless| Mobile| Canada)$", "").isin("Bell", "Chatr", "Fido", "Fizz", "Freedom", "Koodo", "Lucky", "Public", "Rogers", "Shaw", "Telus", "Videotron", "Virgin", "SaskTel"),
            regexp_replace($"a.sp_reporting_name_group", "( Wireless| Mobile| Canada)$", "")
          ).when(
            regexp_replace($"a.sp_reporting_name_group", "( Wireless| Mobile| Canada)$", "") === "Bell MTS",
            "Bell"
          ).when(
            regexp_replace($"a.sp_reporting_name_group", "( Wireless| Mobile| Canada)$", "").isin("Eastlink", "Other", "Xplore"),
            "Other"
          ).otherwise(null))
        .withColumn("loser",
          when(
            regexp_replace($"b.sp_reporting_name_group", "( Wireless| Mobile| Canada)$", "").isin("Bell", "Chatr", "Fido", "Fizz", "Freedom", "Koodo", "Lucky", "Public", "Rogers", "Shaw", "Telus", "Videotron", "Virgin", "SaskTel"),
            regexp_replace($"b.sp_reporting_name_group", "( Wireless| Mobile| Canada)$", "")
          ).when(
            regexp_replace($"b.sp_reporting_name_group", "( Wireless| Mobile| Canada)$", "") === "Bell MTS",
            "Bell"
          ).when(
            regexp_replace($"b.sp_reporting_name_group", "( Wireless| Mobile| Canada)$", "").isin("Eastlink", "Other", "Xplore"),
            "Other"
          ).otherwise(null))
        .groupBy($"date_trunc", $"main.pruid", $"dd.prname", $"winner", $"loser")
        .agg(round(sum($"main.switches")).alias("switches"))

    val mainAggDF = joinedDF
      .withColumn("winner", when(
        $"date_trunc" >= lit("2023-11-01") && $"winner" === "Videotron" && $"pruid".isin(48, 59, 46),
        "Fizz"
      ).otherwise($"winner"))
      .withColumn("loser", when(
        $"date_trunc" >= lit("2023-11-01") && $"loser" === "Videotron" && $"pruid".isin(48, 59, 46),
        "Fizz"
      ).otherwise($"loser"))
      .select($"date_trunc",$"pruid",$"prname",$"winner",$"loser",$"switches")
      .filter($"winner" =!= $"loser")

    /**
      * Step 5: Pre-shaw-merge processing.
      *
      * Joins the aggregated data with pre-shaw-merge plan type split data.
      * Calculates minimum allocation and prepares the final DataFrame for pre-shaw-merge data.
      *
      * @return DataFrame containing the pre-shaw-merge results.
      */
    // Step 5: Pre-Shaw-Merge
    // Define window specification for calculating SUM(min_allocation) OVER partitioned by certain columns
    val windowSpec = Window.partitionBy("date_trunc", "pruid", "winner_temp", "loser_temp")

    // Pre-shaw-merge DataFrame
    val preShawMergeDF = mainAggDF.alias("main")
      .join(planTypeSplitPreShawMergeDF.alias("pt_winner"),
        lower($"main.winner") === $"pt_winner.brand_temp" && $"main.pruid" === $"pt_winner.pruid", "left")
      .join(planTypeSplitPreShawMergeDF.alias("pt_loser"),
        lower($"main.loser") === $"pt_loser.brand_temp" && $"main.pruid" === $"pt_loser.pruid", "left")
      .filter($"main.winner".isNotNull && $"main.loser".isNotNull && $"main.date_trunc" < lit("2023-04-01"))
      .select(
        $"main.date_trunc",
        $"main.pruid",
        $"main.prname",
        $"pt_winner.brand_temp".alias("winner_temp"),
        $"pt_loser.brand_temp".alias("loser_temp"),
        $"pt_winner.brand".alias("winner"),
        $"pt_loser.brand".alias("loser"),
        $"main.switches",
        $"pt_winner.plan_type_id".alias("winner_plan_type_id"),
        $"pt_loser.plan_type_id".alias("loser_plan_type_id"),
        least($"main.switches" * $"pt_winner.plan_type_split", $"main.switches" * $"pt_loser.plan_type_split").alias("min_allocation")
      )

    val finalPreShawMergeDF = preShawMergeDF.select(
      $"date_trunc",
      when(lower($"winner") === "sasktel" && $"winner_plan_type_id" === 1, concat(lit("SaskTel"), lit("_Prepaid Phone")))
        .when(lower($"winner") === "sasktel" && $"winner_plan_type_id" === 2, concat(lit("SaskTel"), lit("_Postpaid Phone")))
        .when($"winner_plan_type_id" === 1, concat(initcap($"winner"), lit("_Prepaid Phone")))
        .when($"winner_plan_type_id" === 2, concat(initcap($"winner"), lit("_Postpaid Phone")))
        .otherwise(lit(null)).as("winner"),
      when(lower($"loser") === "sasktel" && $"loser_plan_type_id" === 1, concat(lit("SaskTel"), lit("_Prepaid Phone")))
        .when(lower($"loser") === "sasktel" && $"loser_plan_type_id" === 2, concat(lit("SaskTel"), lit("_Postpaid Phone")))
        .when($"loser_plan_type_id" === 1, concat(initcap($"loser"), lit("_Prepaid Phone")))
        .when($"loser_plan_type_id" === 2, concat(initcap($"loser"), lit("_Postpaid Phone")))
        .otherwise(lit(null)).as("loser"),
      $"winner_plan_type_id".cast("string").as("primary_plan_type_id"),
      $"loser_plan_type_id".cast("string").as("secondary_plan_type_id"),
      $"pruid".as("dma"),
      $"prname".as("dma_name"),
      round($"min_allocation" * (lit(1.00) * $"switches" / coalesce(when(sum($"min_allocation").over(windowSpec) === 0, lit(0.0)).otherwise(sum($"min_allocation").over(windowSpec)), lit(0)))).cast(DoubleType).alias("switches")
    )

    /**
      * Step 6: Post-shaw-merge processing.
      *
      * Joins the aggregated data with post-shaw-merge plan type split data.
      * Calculates minimum allocation and prepares the final DataFrame for post-shaw-merge data.
      *
      * @return DataFrame containing the post-shaw-merge results.
      */
    // Define window specification for calculating SUM(min_allocation) OVER partitioned by certain columns
    val windowSpecific = Window.partitionBy("date_trunc", "pruid", "winner", "loser")

    // Post-shaw-merge DataFrame
    val postShawMergeDF = mainAggDF.alias("main")
      .join(planTypeSplitPostShawMergeDF.alias("pt_winner"),
        lower($"main.winner") === $"pt_winner.brand" && $"main.pruid" === $"pt_winner.pruid", "left")
      .join(planTypeSplitPostShawMergeDF.alias("pt_loser"),
        lower($"main.loser") === $"pt_loser.brand" && $"main.pruid" === $"pt_loser.pruid", "left")
      .filter($"main.winner".isNotNull && $"main.loser".isNotNull && $"main.date_trunc" >= lit("2023-04-01"))
      .select(
        $"main.date_trunc",
        $"main.pruid",
        $"main.prname",
        $"pt_winner.brand".alias("winner"),
        $"pt_loser.brand".alias("loser"),
        $"main.switches",
        $"pt_winner.plan_type_id".alias("winner_plan_type_id"),
        $"pt_loser.plan_type_id".alias("loser_plan_type_id"),
        least($"main.switches" * $"pt_winner.plan_type_split", $"main.switches" * $"pt_loser.plan_type_split").alias("min_allocation")
      )

    val finalPostShawMergeDF = postShawMergeDF.select(
      $"date_trunc",
      when(lower($"winner") === "sasktel" && $"winner_plan_type_id" === 1, concat(lit("SaskTel"), lit("_Prepaid Phone")))
        .when(lower($"winner") === "sasktel" && $"winner_plan_type_id" === 2, concat(lit("SaskTel"), lit("_Postpaid Phone")))
        .when($"winner_plan_type_id" === 1, concat(initcap($"winner"), lit("_Prepaid Phone")))
        .when($"winner_plan_type_id" === 2, concat(initcap($"winner"), lit("_Postpaid Phone")))
        .otherwise(lit(null)).as("winner"),
      when(lower($"loser") === "sasktel" && $"loser_plan_type_id" === 1, concat(lit("SaskTel"), lit("_Prepaid Phone")))
        .when(lower($"loser") === "sasktel" && $"loser_plan_type_id" === 2, concat(lit("SaskTel"), lit("_Postpaid Phone")))
        .when($"loser_plan_type_id" === 1, concat(initcap($"loser"), lit("_Prepaid Phone")))
        .when($"loser_plan_type_id" === 2, concat(initcap($"loser"), lit("_Postpaid Phone")))
        .otherwise(lit(null)).as("loser"),
      $"winner_plan_type_id".cast("string").as("primary_plan_type_id"),
      $"loser_plan_type_id".cast("string").as("secondary_plan_type_id"),
      $"pruid".as("dma"),
      $"prname".as("dma_name"),
      round($"min_allocation" * (lit(1.00) * $"switches" / coalesce(when(sum($"min_allocation").over(windowSpecific) === 0, lit(0.0)).otherwise(sum($"min_allocation").over(windowSpecific)), lit(0)))).cast(DoubleType).alias("switches")
    )

    /**
      * Combines the results from pre-shaw-merge and post-shaw-merge calculations.
      *
      * Unions the pre-shaw-merge and post-shaw-merge DataFrames, fills null values with zero,
      * and orders the final results by DMA, winner, and loser.
      *
      * @return DataFrame containing the combined ported wins and losses.
      */
    // Union the two DataFrames
    val resultDF = finalPreShawMergeDF.union(finalPostShawMergeDF)
    resultDF.orderBy($"dma", $"winner", $"loser").na.fill(0)
      .select(
        $"date_trunc",
        $"winner",
        $"loser",
        $"primary_plan_type_id",
        $"secondary_plan_type_id",
        $"dma".cast(StringType),
        $"dma_name",
        $"switches"
      )
      .as[CaMonthlyPortedWinsAndLossesOutput]
  }
}

object CaMonthlyPortedWinsAndLossesWMS {
  def apply()(implicit spark: SparkSession): CaMonthlyPortedWinsAndLossesWMS = new CaMonthlyPortedWinsAndLossesWMS()
}

