package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.largescale.schema.wireless_market_share._
import com.comlinkdata.largescale.schema.wireless_market_share.{MonthlyOutputWithEthnicityDMA, BrandPlantypeLookup, CmaFinalOutputPairwiseDemoCombinations, DmaFinalOutputPairwiseDemoCombinations, MsoIlecFootprintsFinalOutputPairwiseDemoCombinations, VpgmFromDmaFinalOutputPairwiseDemoCombinations}
import com.comlinkdata.emrjobs.spark.wireless_market_share.model.{DmaFinalOutputAllDemo, VpgmFromDmaFinalOutputAllDemo, MsoIlecFootprintsFinalOutputAllDemo, CmaFinalOutputAllDemo, DmaFinalOutputPairwiseDemo, VpgmFromDmaFinalOutputPairwiseDemo, MsoIlecFootprintsFinalOutputPairwiseDemo, CmaFinalOutputPairwiseDemo, BoostMarketOutputAllDemo, BoostMarketOutputPairwiseDemo}
import org.apache.spark.sql.functions.{current_date, _}
import org.apache.spark.sql.types.{DoubleType, IntegerType}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}
import java.time.LocalDate

class Packaging(implicit spark: SparkSession) {

  import spark.implicits._

  // Method to compute dma Final Output
  def dmaFinalOutputAllDemo(
    monthlyOutputWithEthnicityDMAData: Dataset[MonthlyOutputWithEthnicityDMA],
    brandPlantypeLookupData: Dataset[BrandPlantypeLookup],
    monthToProcess: LocalDate
  ): Dataset[DmaFinalOutputAllDemo] = {

    // Aliasing the input DataFrames
    val dmaMonthlyOutputAlias = monthlyOutputWithEthnicityDMAData.alias("a")
    val brandPlantypeLookupAlias = brandPlantypeLookupData.alias("b")

    // Apply the transformations and filters
    val dmaFinalOutputDF = dmaMonthlyOutputAlias
      .join(brandPlantypeLookupAlias, $"a.brand_plantype" === $"b.brand_plantype", "left")
      .filter($"a.wms_month" >= lit(monthToProcess) &&
        !isnan($"a.ending_subscribers") &&
        !(round(coalesce($"a.ending_subscribers", lit(0.00)), 0) === 0 && isnan($"a.gross_losses")) &&
        !(round(coalesce($"a.ending_subscribers", lit(0.00)), 0) === 0 && isnan($"a.gross_adds")) &&
        !$"a.ending_subscribers".leq(0.00) &&
        !$"a.gross_adds".lt(0.00) &&
        !$"a.gross_losses".lt(0.00) &&
        !($"a.ending_subscribers" === 0.00 && $"a.starting_subscribers".leq(0.00)) &&
        !$"a.base_adjustment".geq(999999999.00) &&
        !$"a.base_adjustment".leq(-999999999.00) &&
        !$"a.gross_adds".geq(999999999.00) &&
        !$"a.gross_losses".geq(999999999.00) &&
        !$"a.ending_subscribers".geq(999999999.00) &&
        !$"a.starting_subscribers".geq(999999999.00)
      )
      .select(
        $"a.wms_month".as("month"),
        $"a.dma_name".as("geography"),
        lit("DMA").as("geography_type"),
        $"b.brand".as("brand"),
        $"b.plantype".as("plan_type"),
        when($"a.ethnicity" === "White", "Caucasian").otherwise($"a.ethnicity").as("ethnicity"),
        $"a.age".as("age"),
        when($"a.income" === "GT 125K", "$125K+")
          .when($"a.income" === "100-125K", "$100K-$125K")
          .when($"a.income" === "LT $50K", "<$50K")
          .when($"a.income" === "$50-100K", "$50K-$100K")
          .otherwise(null).as("income"),
        round(coalesce($"a.ending_subscribers", lit(0.00)), 0).cast("integer").as("subscribers"),
        round(coalesce($"a.gross_adds", lit(0.00)), 0).cast("integer").as("gross_adds"),
        round(coalesce($"a.gross_losses", lit(0.00)), 0).cast("integer").as("gross_losses"),
        round(coalesce($"a.base_adjustment", lit(0.00)), 0).cast("integer").as("base_adjustment"),
      )

    val dmaFinalDF = dmaFinalOutputDF.withColumn("creation_date", current_date())
      .as[DmaFinalOutputAllDemo]

    dmaFinalDF
  }

  // Method to compute VPGM from dma Final Output
  def vpgmFromDmaFinalOutputAllDemo(
    dmaFinalOutputAllDemo: Dataset[DmaFinalOutputAllDemo],
    cbgPctOfDmaGeorollupsData: Dataset[CbgPctOfDmaGeorollups],
    monthToProcess: LocalDate
  ): Dataset[VpgmFromDmaFinalOutputAllDemo] = {

    // Calculate max_creation_date for every month
    val maxCreationDateDF = dmaFinalOutputAllDemo
      .groupBy("month")
      .agg(max("creation_date").as("max_creation_date"))
      .select(
        $"month",
        $"max_creation_date"
      )

    // Join the main table with the max creation date table
    val resultDF = dmaFinalOutputAllDemo.alias("a")
      .join(maxCreationDateDF.alias("c"), col("a.month") === col("c.month") && col("a.creation_date") === col("c.max_creation_date"), "inner")
      .join(cbgPctOfDmaGeorollupsData.alias("b"),
        col("a.geography") === col("b.geography") &&
          col("a.ethnicity") === col("b.ethnicity") &&
          col("a.age") === col("b.age") &&
          col("a.income") === col("b.income") &&
          col("a.brand") === col("b.brand") &&
          col("a.plan_type") === col("b.plan_type"), "left")
      .where(col("a.month") >= lit(monthToProcess))

    val geographyExpr = when(substring(col("b.cbg"), 1, 2) === "09", lit("Northeast States")).otherwise(col("b.vpgm"))

      val newDF = resultDF
        .groupBy(
          col("a.month"),
          geographyExpr.alias("geography"),
          col("a.brand"),
          col("a.plan_type"),
          col("a.ethnicity"),
          col("a.age"),
          col("a.income"))
      .agg(
        round(sum(coalesce(col("a.subscribers") * col("b.cbg_pct_dma_total"), lit(0.00))), 0).cast(IntegerType).alias("subscribers"),
        round(sum(coalesce(col("a.gross_adds") * col("b.cbg_pct_dma_total"), lit(0.00))), 0).cast(IntegerType).alias("gross_adds"),
        round(sum(coalesce(col("a.gross_losses") * col("b.cbg_pct_dma_total"), lit(0.00))), 0).cast(IntegerType).alias("gross_losses"),
        round(sum(coalesce(col("a.base_adjustment") * col("b.cbg_pct_dma_total"), lit(0.00))), 0).cast(IntegerType).alias("base_adjustment"),
        current_date().alias("creation_date")
      )
      .withColumn("geography_type", lit("VPGM"))
        .select(
          col("month"),
          col("geography"),
          col("geography_type"),
          col("brand"),
          col("plan_type"),
          col("ethnicity"),
          col("age"),
          col("income"),
          col("subscribers"),
          col("gross_adds"),
          col("gross_losses"),
          col("base_adjustment"),
          col("creation_date")
        ).as[VpgmFromDmaFinalOutputAllDemo]

    newDF
  }

  // Method to compute MSO/ILEC Footprints Final Output
  def msoIlecFootprintsFinalOutputAllDemo(
    dmaFinalOutputAllDemo: Dataset[DmaFinalOutputAllDemo],
    cbgPctOfDmaGeorollupsData: Dataset[CbgPctOfDmaGeorollups],
    creationDate: String
  ): Dataset[MsoIlecFootprintsFinalOutputAllDemo] = {

    // Helper function to perform union operation
    def msoUnionHelper(mso: String, geography: String): DataFrame = {

      // Alias the dataframes
      val dmaAlias = dmaFinalOutputAllDemo.alias("a")
      val cbgAlias = cbgPctOfDmaGeorollupsData.alias("b")

      dmaAlias
        .join(cbgAlias, Seq("geography", "ethnicity", "age", "income", "brand", "plan_type"), "left")
        .filter(cbgAlias(mso) === 1)
        .filter($"a.creation_date" >= lit(creationDate))
        .groupBy(
          col("a.month"), // Resolve the ambiguity by specifying the alias
          lit(geography).alias("geography"),
          lit("Broadband_footprint").alias("geography_type"),
          col("a.brand"),
          col("a.plan_type"),
          col("a.ethnicity"),
          col("a.age"),
          col("a.income"),
          col("a.creation_date")
        )
        .agg(
          round(sum(coalesce($"a.subscribers" * $"b.cbg_pct_dma_total", lit(0.00))), 0).cast(IntegerType).alias("subscribers"),
          round(sum(coalesce($"a.gross_adds" * $"b.cbg_pct_dma_total", lit(0.00))), 0).cast(IntegerType).alias("gross_adds"),
          round(sum(coalesce($"a.gross_losses" * $"b.cbg_pct_dma_total", lit(0.00))), 0).cast(IntegerType).alias("gross_losses"),
          round(sum(coalesce($"a.base_adjustment" * $"b.cbg_pct_dma_total", lit(0.00))), 0).cast(IntegerType).alias("base_adjustment")
        )
    }

    // Union all the dataframes produced by msoUnionHelper
    val msoIlecFootprintsDF =
      msoUnionHelper("comcast_mso", "Comcast_MSO")
      .union(msoUnionHelper("spectrum_mso", "Spectrum_MSO"))
      .union(msoUnionHelper("altice_mso", "Altice_MSO"))
      .union(msoUnionHelper("cox_mso", "Cox_MSO"))
      .union(msoUnionHelper("att_fiber", "AT&T_FTTH"))
      .union(msoUnionHelper("att_ilec", "AT&T_ILEC"))
      .union(msoUnionHelper("verizon_fiber", "Verizon_FTTH"))
      .union(msoUnionHelper("verizon_ilec", "Verizon_ILEC"))
      .union(msoUnionHelper("lumen_fiber", "Lumen_FTTH"))
      .union(msoUnionHelper("lumen_ilec", "Lumen_ILEC"))
      .union(msoUnionHelper("brightspeed_fiber", "Brightspeed_FTTH"))
      .union(msoUnionHelper("brightspeed_ilec", "Brightspeed_ILEC"))
      .union(msoUnionHelper("frontier_fiber", "Frontier_FTTH"))
      .union(msoUnionHelper("frontier_ilec", "Frontier_ILEC"))
      .union(msoUnionHelper("windstream_fiber", "Windstream_FTTH"))
      .union(msoUnionHelper("windstream_ilec", "Windstream_ILEC"))
      .select(
        "a.month",
        "geography",
        "geography_type",
        "a.brand",
        "a.plan_type",
        "a.ethnicity",
        "a.age",
        "a.income",
        "subscribers",
        "gross_adds",
        "gross_losses",
        "base_adjustment",
        "creation_date"
      )
      .as[MsoIlecFootprintsFinalOutputAllDemo]

    msoIlecFootprintsDF
  }

  // Method to compute CMA Final Output
  def cmaFinalOutputAllDemo(
    dmaFinalOutputAllDemo: Dataset[DmaFinalOutputAllDemo],
    cbgPctOfDmaGeorollupsData: Dataset[CbgPctOfDmaGeorollups],
    creationDate: String
  ): Dataset[CmaFinalOutputAllDemo] = {

    // Perform the join and aggregation
    val result = dmaFinalOutputAllDemo.alias("a")
      .join(cbgPctOfDmaGeorollupsData.alias("b"),
        col("a.geography") === col("b.geography") &&
          col("a.ethnicity") === col("b.ethnicity") &&
          col("a.age") === col("b.age") &&
          col("a.income") === col("b.income") &&
          col("a.brand") === col("b.brand") &&
          col("a.plan_type") === col("b.plan_type"),
        "left"
      )
      .filter(col("a.creation_date") >= lit(creationDate))
      .groupBy(
        col("a.month"),
        concat(col("b.cma_name"), lit(" ("), col("b.cma"), lit(")")).alias("geography"),
        lit("CMA").alias("geography_type"),
        col("a.brand"),
        col("a.plan_type"),
        col("a.ethnicity"),
        col("a.age"),
        col("a.income"),
        col("a.creation_date")
      )
      .agg(
        round(sum(coalesce(col("a.subscribers") * col("b.cbg_pct_dma_total"), lit(0.00))), 0).cast("integer").alias("subscribers"),
        round(sum(coalesce(col("a.gross_adds") * col("b.cbg_pct_dma_total"), lit(0.00))), 0).cast("integer").alias("gross_adds"),
        round(sum(coalesce(col("a.gross_losses") * col("b.cbg_pct_dma_total"), lit(0.00))), 0).cast("integer").alias("gross_losses"),
        round(sum(coalesce(col("a.base_adjustment") * col("b.cbg_pct_dma_total"), lit(0.00))), 0).cast("integer").alias("base_adjustment")
      )
      .select(
        "a.month",
        "geography",
        "geography_type",
        "a.brand",
        "a.plan_type",
        "a.ethnicity",
        "a.age",
        "a.income",
        "subscribers",
        "gross_adds",
        "gross_losses",
        "base_adjustment",
        "creation_date"
      )
      .as[CmaFinalOutputAllDemo]

    result
  }

  // Method to compute DMA Final Pairwise Output
  def dmaFinalOutputPairwiseDemo(
    monthlyOutputWithEthnicityDMAData: Dataset[MonthlyOutputWithEthnicityDMA],
    brandPlantypeLookupData: Dataset[BrandPlantypeLookup],
    monthToProcess: LocalDate
  ): Dataset[DmaFinalOutputPairwiseDemo] = {
    // Perform the LEFT JOIN operation
    val joinedDF = monthlyOutputWithEthnicityDMAData.alias("a")
      .join(brandPlantypeLookupData.alias("b"), col("a.brand_plantype") === col("b.brand_plantype"), "left")

    // Apply the transformations as per the SQL query
    val dmaOutputPairwiseDF = joinedDF
      .filter(col("a.wms_month") >= lit(monthToProcess) &&
        !isnan(col("a.ending_subscribers")) &&
        !(round(coalesce(col("a.ending_subscribers"), lit(0.00)), 0) === 0 && isnan(col("a.gross_losses"))) &&
        !(round(coalesce(col("a.ending_subscribers"), lit(0.00)), 0) === 0 && isnan(col("a.gross_adds"))) &&
        !(col("a.ending_subscribers") <= 0.00) &&
        !(col("a.gross_adds") < 0.00) &&
        !(col("a.gross_losses") < 0.00) &&
        !(col("a.ending_subscribers") === 0.00 && col("a.starting_subscribers") <= 0.00) &&
        !(col("a.base_adjustment") >= 999999999.00) &&
        !(col("a.base_adjustment") <= -999999999.00) &&
        !(col("a.gross_adds") >= 999999999.00) &&
        !(col("a.gross_losses") >= 999999999.00) &&
        !(col("a.ending_subscribers") >= 999999999.00) &&
        !(col("a.starting_subscribers") >= 999999999.00))
      .select(
        col("a.wms_month").as("month"),
        col("a.dma_name").as("geography"),
        lit("DMA").as("geography_Type"),
        col("b.brand").as("brand"),
        col("b.plantype").as("plan_type"),
        when(col("a.ethnicity") === "White", "Caucasian").otherwise(col("a.ethnicity")).as("ethnicity"),
        col("a.age").as("age"),
        when(col("a.income") === "GT 125K", "$125K+")
          .when(col("a.income") === "100-125K", "$100K-$125K")
          .when(col("a.income") === "LT $50K", "<$50K")
          .when(col("a.income") === "$50-100K", "$50K-$100K")
          .otherwise(null).as("income"),
        round(coalesce(col("a.ending_subscribers"), lit(0.00)), 0).cast(IntegerType).as("subscribers"),
        round(coalesce(col("a.gross_adds"), lit(0.00)), 0).cast(IntegerType).as("gross_adds"),
        round(coalesce(col("a.gross_losses"), lit(0.00)), 0).cast(IntegerType).as("gross_losses"),
        round(coalesce(col("a.base_adjustment"), lit(0.00)), 0).cast(IntegerType).as("base_adjustment")
      )

    // Ensure consistent column order for UNION ALL
    val cols = Seq("month", "geography", "geography_type", "brand", "plan_type", "ethnicity", "age", "income", "subscribers", "gross_adds", "gross_losses", "base_adjustment")

    // Aggregations for the final DataFrame
    val dmaOutputPairwiseDF1 = dmaOutputPairwiseDF
      .groupBy("month", "geography", "geography_type", "brand", "plan_type", "ethnicity", "age")
      .agg(
        lit("All").as("income"),
        sum(when(col("subscribers") < 0, 0).otherwise(col("subscribers"))).as("subscribers"),
        sum(when(col("gross_adds") < 0, 0).otherwise(col("gross_adds"))).as("gross_adds"),
        sum(when(col("gross_losses") < 0, 0).otherwise(col("gross_losses"))).as("gross_losses"),
        sum("base_adjustment").as("base_adjustment")
      )
      .select(cols.map(col): _*) // Ensure consistent column order

    val dmaOutputPairwiseDF2 = dmaOutputPairwiseDF
      .groupBy("month", "geography", "geography_type", "brand", "plan_type", "ethnicity", "income")
      .agg(
        lit("All").as("age"),
        sum(when(col("subscribers") < 0, 0).otherwise(col("subscribers"))).as("subscribers"),
        sum(when(col("gross_adds") < 0, 0).otherwise(col("gross_adds"))).as("gross_adds"),
        sum(when(col("gross_losses") < 0, 0).otherwise(col("gross_losses"))).as("gross_losses"),
        sum("base_adjustment").as("base_adjustment")
      )
      .select(cols.map(col): _*) // Ensure consistent column order

    val dmaOutputPairwiseDF3 = dmaOutputPairwiseDF
      .groupBy("month", "geography", "geography_type", "brand", "plan_type", "age", "income")
      .agg(
        lit("All").as("ethnicity"),
        sum(when(col("subscribers") < 0, 0).otherwise(col("subscribers"))).as("subscribers"),
        sum(when(col("gross_adds") < 0, 0).otherwise(col("gross_adds"))).as("gross_adds"),
        sum(when(col("gross_losses") < 0, 0).otherwise(col("gross_losses"))).as("gross_losses"),
        sum("base_adjustment").as("base_adjustment")
      )
      .select(cols.map(col): _*) // Ensure consistent column order

    // Combine all parts
    val dmaFinalOutputPairwiseDF = dmaOutputPairwiseDF1
      .union(dmaOutputPairwiseDF2)
      .union(dmaOutputPairwiseDF3)
      .withColumn("creation_date", current_date())

    dmaFinalOutputPairwiseDF.as[DmaFinalOutputPairwiseDemo]

  }

  // Method to compute VPGM from dma Final Output Pairwise
  def vpgmFromDmaFinalOutputPairwiseDemo(
    vpgmFromDmaFinalOutputAllDemoData: Dataset[VpgmFromDmaFinalOutputAllDemo]
  ): Dataset[VpgmFromDmaFinalOutputPairwiseDemo] = {
    // Ensure consistent column order for UNION ALL
    val cols = Seq("month", "geography", "geography_type", "brand", "plan_type", "ethnicity", "age", "income", "subscribers", "gross_adds", "gross_losses", "base_adjustment")

    // First Union: Group by Month, Geography, Geography_Type, Brand, Plan_Type, Ethnicity, Age
    val vpgmFromDmaFinalOutputPairwiseDF1 = vpgmFromDmaFinalOutputAllDemoData
      .groupBy("month", "geography", "geography_type", "brand", "plan_type", "ethnicity", "age")
      .agg(
        lit("All").as("income"),
        sum(when(col("subscribers") < 0, 0).otherwise(col("subscribers"))).alias("subscribers"),
        sum(when(col("gross_adds") < 0, 0).otherwise(col("gross_adds"))).alias("gross_adds"),
        sum(when(col("gross_losses") < 0, 0).otherwise(col("gross_losses"))).alias("gross_losses"),
        sum("base_adjustment").alias("base_adjustment")
      )
      .select(cols.map(col): _*) // Ensure consistent column order

    // Second Union: Group by Month, Geography, Geography_Type, Brand, Plan_Type, Ethnicity, Income
    val vpgmFromDmaFinalOutputPairwiseDF2 = vpgmFromDmaFinalOutputAllDemoData
      .groupBy("month", "geography", "geography_type", "brand", "plan_type", "ethnicity", "income")
      .agg(
        lit("All").as("age"),
        sum(when(col("subscribers") < 0, 0).otherwise(col("subscribers"))).alias("subscribers"),
        sum(when(col("gross_adds") < 0, 0).otherwise(col("gross_adds"))).alias("gross_adds"),
        sum(when(col("gross_losses") < 0, 0).otherwise(col("gross_losses"))).alias("gross_losses"),
        sum("base_adjustment").alias("base_adjustment")
      )
      .select(cols.map(col): _*) // Ensure consistent column order

    // Third Union: Group by Month, Geography, Geography_Type, Brand, Plan_Type, Age, Income
    val vpgmFromDmaFinalOutputPairwiseDF3 = vpgmFromDmaFinalOutputAllDemoData
      .groupBy("month", "geography", "geography_type", "brand", "plan_type", "age", "income")
      .agg(
        lit("All").as("ethnicity"),
        sum(when(col("subscribers") < 0, 0).otherwise(col("subscribers"))).alias("subscribers"),
        sum(when(col("gross_adds") < 0, 0).otherwise(col("gross_adds"))).alias("gross_adds"),
        sum(when(col("gross_losses") < 0, 0).otherwise(col("gross_losses"))).alias("gross_losses"),
        sum("base_adjustment").alias("base_adjustment")
      )
      .select(cols.map(col): _*) // Ensure consistent column order

    // Combine the results using unionAll
    val vpgmFromDmaFinalOutputPairwiseDF = vpgmFromDmaFinalOutputPairwiseDF1
      .union(vpgmFromDmaFinalOutputPairwiseDF2)
      .union(vpgmFromDmaFinalOutputPairwiseDF3)
      .withColumn("creation_date", current_date())

    vpgmFromDmaFinalOutputPairwiseDF.as[VpgmFromDmaFinalOutputPairwiseDemo]

  }

  // Method to compute MSO/ILEC Footprints Final Output Pairwise
  def msoIlecFootprintsFinalOutputPairwiseDemo(
    msoIlecFootprintsFinalOutputAllDemoData: Dataset[MsoIlecFootprintsFinalOutputAllDemo],
    creationDate: String
  ): Dataset[MsoIlecFootprintsFinalOutputPairwiseDemo] = {

    // Ensure consistent column order for UNION ALL
    val cols = Seq("month", "geography", "geography_type", "brand", "plan_type", "ethnicity", "age", "income", "subscribers", "gross_adds", "gross_losses", "base_adjustment", "creation_date")

    // First Union: Group by Month, Geography, Geography_Type, Brand, Plan_Type, Ethnicity, Age, creation_date
    val msoIlecFootprintsFinalOutputPairwiseDF1 = msoIlecFootprintsFinalOutputAllDemoData
      .filter(col("creation_date") >= lit(creationDate))
      .groupBy("month", "geography", "geography_type", "brand", "plan_type", "ethnicity", "age", "creation_date")
      .agg(
        lit("All").as("income"),
        sum(when(col("subscribers") < 0, 0).otherwise(col("subscribers"))).alias("subscribers"),
        sum(when(col("gross_adds") < 0, 0).otherwise(col("gross_adds"))).alias("gross_adds"),
        sum(when(col("gross_losses") < 0, 0).otherwise(col("gross_losses"))).alias("gross_losses"),
        sum("base_adjustment").alias("base_adjustment")
      )
      .select(cols.map(col): _*) // Ensure consistent column order

    // Second Union: Group by Month, Geography, Geography_Type, Brand, Plan_Type, Ethnicity, Income, creation_date
    val msoIlecFootprintsFinalOutputPairwiseDF2 = msoIlecFootprintsFinalOutputAllDemoData
      .filter(col("creation_date") >= lit(creationDate))
      .groupBy("month", "geography", "geography_type", "brand", "plan_type", "ethnicity", "income", "creation_date")
      .agg(
        lit("All").as("age"),
        sum(when(col("subscribers") < 0, 0).otherwise(col("subscribers"))).alias("subscribers"),
        sum(when(col("gross_adds") < 0, 0).otherwise(col("gross_adds"))).alias("gross_adds"),
        sum(when(col("gross_losses") < 0, 0).otherwise(col("gross_losses"))).alias("gross_losses"),
        sum("base_adjustment").alias("base_adjustment")
      )
      .select(cols.map(col): _*) // Ensure consistent column order

    // Third Union: Group by Month, Geography, Geography_Type, Brand, Plan_Type, Age, Income, creation_date
    val msoIlecFootprintsFinalOutputPairwiseDF3 = msoIlecFootprintsFinalOutputAllDemoData
      .filter(col("creation_date") >= lit(creationDate))
      .groupBy("month", "geography", "geography_type", "brand", "plan_type", "age", "income", "creation_date")
      .agg(
        lit("All").as("ethnicity"),
        sum(when(col("subscribers") < 0, 0).otherwise(col("subscribers"))).alias("subscribers"),
        sum(when(col("gross_adds") < 0, 0).otherwise(col("gross_adds"))).alias("gross_adds"),
        sum(when(col("gross_losses") < 0, 0).otherwise(col("gross_losses"))).alias("gross_losses"),
        sum("base_adjustment").alias("base_adjustment")
      )
      .select(cols.map(col): _*) // Ensure consistent column order

    // Combine all DataFrames using union
    val msoIlecFootprintsFinalOutputPairwiseDF = msoIlecFootprintsFinalOutputPairwiseDF1
      .union(msoIlecFootprintsFinalOutputPairwiseDF2)
      .union(msoIlecFootprintsFinalOutputPairwiseDF3)

    msoIlecFootprintsFinalOutputPairwiseDF.as[MsoIlecFootprintsFinalOutputPairwiseDemo]
  }

  // Method to compute CMA Final Output Pairwise
  def cmaFinalOutputPairwiseDemo(
    cmaFinalOutputAllDemoData: Dataset[CmaFinalOutputAllDemo],
    creationDate: String
  ): Dataset[CmaFinalOutputPairwiseDemo] = {

    // Ensure consistent column order for UNION ALL
    val cols = Seq("month", "geography", "geography_type", "brand", "plan_type", "ethnicity", "age", "income", "subscribers", "gross_adds", "gross_losses", "base_adjustment", "creation_date")

    // First part: Group by Month, Geography, Geography_Type, Brand, Plan_Type, Ethnicity, Age, creation_date
    val cmaFinalOutputPairwiseDF1 = cmaFinalOutputAllDemoData
      .groupBy("month", "geography", "geography_type", "brand", "plan_type", "ethnicity", "age", "creation_date")
      .agg(
        lit("All").as("income"),
        sum(when(col("subscribers") < 0, 0).otherwise(col("subscribers"))).alias("subscribers"),
        sum(when(col("gross_adds") < 0, 0).otherwise(col("gross_adds"))).alias("gross_adds"),
        sum(when(col("gross_losses") < 0, 0).otherwise(col("gross_losses"))).alias("gross_losses"),
        sum("base_adjustment").alias("base_adjustment")
      )
      .select(cols.map(col): _*) // Ensure consistent column order

    // Second part: Group by Month, Geography, Geography_Type, Brand, Plan_Type, Ethnicity, Income, creation_date
    val cmaFinalOutputPairwiseDF2 = cmaFinalOutputAllDemoData
      .groupBy("month", "geography", "geography_type", "brand", "plan_type", "ethnicity", "income", "creation_date")
      .agg(
        lit("All").as("age"),
        sum(when(col("subscribers") < 0, 0).otherwise(col("subscribers"))).alias("subscribers"),
        sum(when(col("gross_adds") < 0, 0).otherwise(col("gross_adds"))).alias("gross_adds"),
        sum(when(col("gross_losses") < 0, 0).otherwise(col("gross_losses"))).alias("gross_losses"),
        sum("base_adjustment").alias("base_adjustment")
      )
      .select(cols.map(col): _*) // Ensure consistent column order

    // Third part: Group by Month, Geography, Geography_Type, Brand, Plan_Type, Age, Income, creation_date
    val cmaFinalOutputPairwiseDF3 = cmaFinalOutputAllDemoData
      .groupBy("month", "geography", "geography_type", "brand", "plan_type", "age", "income", "creation_date")
      .agg(
        lit("All").as("ethnicity"),
        sum(when(col("subscribers") < 0, 0).otherwise(col("subscribers"))).alias("subscribers"),
        sum(when(col("gross_adds") < 0, 0).otherwise(col("gross_adds"))).alias("gross_adds"),
        sum(when(col("gross_losses") < 0, 0).otherwise(col("gross_losses"))).alias("gross_losses"),
        sum("base_adjustment").alias("base_adjustment")
      )
      .select(cols.map(col): _*) // Ensure consistent column order

    // Combine all DataFrames using union
    val cmaFinalOutputPairwiseDF = cmaFinalOutputPairwiseDF1
      .union(cmaFinalOutputPairwiseDF2)
      .union(cmaFinalOutputPairwiseDF3)
      .filter(col("creation_date") >= lit(creationDate))

    cmaFinalOutputPairwiseDF.as[CmaFinalOutputPairwiseDemo]
  }

  // Method to compute Boost Market Final Output All Demo
  /**
    * Computes the Boost Market Final Output for all demographic combinations.
    *
    * This method performs the following steps:
    * 1. Finds the maximum `creation_date` per `month` in the DMA final output dataset.
    * 2. Filters the DMA final output to only include rows with the latest `creation_date` for each month.
    * 3. Joins the filtered DMA final output with the CBG percentage data on multiple demographic dimensions.
    *
    * @param dmaFinalOutputAllDemo        Dataset containing the DMA-level final output for all demographic combinations.
    * @param cbgPctOfDmaGeorollupsData    Dataset containing the percentage of CBGs within DMAs for various geographic and demographic combinations.
    * @param monthToProcess               The month for which data is being processed.
    * @return                             Dataset of `BoostMarketOutputAllDemo` after filtering and joining.
    */
  def boostMarketFinalOutputAllDemo(
    dmaFinalOutputAllDemo: Dataset[DmaFinalOutputAllDemo],
    cbgPctOfDmaGeorollupsData: Dataset[CbgPctOfDmaGeorollups],
    monthToProcess: LocalDate
  ): Dataset[BoostMarketOutputAllDemo] = {

    val a = dmaFinalOutputAllDemo.alias("a")
    val b = cbgPctOfDmaGeorollupsData.alias("b")

    val maxCreationDateDF = a
      .groupBy(col("a.Month"))
      .agg(max(col("a.creation_date")).alias("max_creation_date"))

    val filteredDMAFinalDF = a
      .join(maxCreationDateDF.alias("c"), Seq("Month"))
      .filter(col("a.creation_date") === col("c.max_creation_date"))

    val joinedDF = filteredDMAFinalDF
      .join(b,
        col("a.geography") === col("b.geography") &&
          col("a.ethnicity") === col("b.ethnicity") &&
          col("a.age") === col("b.age") &&
          col("a.income") === col("b.income") &&
          col("a.brand") === col("b.brand") &&
          col("a.plan_type") === col("b.plan_type"),
        "left"
      )

    val filteredDF = joinedDF
      .filter(col("a.Month") >= lit(monthToProcess))

    val resultDF = filteredDF
      .groupBy(
        col("a.Month"),
        col("b.boost_market"),
        col("a.Brand"),
        col("a.Plan_Type"),
        col("a.Ethnicity"),
        col("a.Age"),
        col("a.Income")
      )
      .agg(
        round(sum(coalesce(col("a.Subscribers") * col("b.cbg_pct_dma_total"), lit(0.0))), 0).cast("int").alias("Subscribers"),
        round(sum(coalesce(col("a.Gross_Adds") * col("b.cbg_pct_dma_total"), lit(0.0))), 0).cast("int").alias("Gross_Adds"),
        round(sum(coalesce(col("a.Gross_Losses") * col("b.cbg_pct_dma_total"), lit(0.0))), 0).cast("int").alias("Gross_Losses"),
        round(sum(coalesce(col("a.Base_Adjustment") * col("b.cbg_pct_dma_total"), lit(0.0))), 0).cast("int").alias("Base_Adjustment")
      )
      .withColumn("Geography", col("b.boost_market"))
      .withColumn("Geography_Type", lit("Boost_Market"))
      .withColumn("creation_date", current_date())

    val finalDF = resultDF.select(
      col("Month"),
      col("Geography"),
      col("Geography_Type"),
      col("Brand"),
      col("Plan_Type"),
      col("Ethnicity"),
      col("Age"),
      col("Income"),
      col("Subscribers"),
      col("Gross_Adds"),
      col("Gross_Losses"),
      col("Base_Adjustment"),
      col("creation_date")
    )
    finalDF.as[BoostMarketOutputAllDemo]
  }

  // Method to compute Boost Market Final Output Pairwise Demo
  /**
    * Computes the Boost Market Final Output for pairwise demographic combinations.
    *
    * This method performs the following steps:
    * 1. Aggregates the fully disaggregated Boost Market output by collapsing one demographic dimension at a time:
    * 2. Replaces negative values in key metrics (`Subscribers`, `Gross_Adds`, `Gross_Losses`) with 0 to ensure valid aggregation.
    * 3. Unifies the three resulting datasets into one using `unionByName`.
    * 4. Adds the current system date as `creation_date`.
    * 5. Returns the result as a typed Dataset of `BoostMarketOutputPairwiseDemo`.
    *
    * @param boostMarketFinalOutputAllDemo Dataset containing the fully disaggregated Boost Market output for all demographics.
    * @return                              Dataset of `BoostMarketOutputPairwiseDemo` with pairwise demographic aggregations and current creation date.
    */
  def boostMarketFinalOutputPairwiseDemo(
    boostMarketFinalOutputAllDemo: Dataset[BoostMarketOutputAllDemo],
  ): Dataset[BoostMarketOutputPairwiseDemo] = {

    def zeroNegative(colName: String) = when(col(colName) < 0, lit(0)).otherwise(col(colName))

    val incomeAllDF = boostMarketFinalOutputAllDemo
      .groupBy("Month", "Geography", "Geography_Type", "Brand", "Plan_Type", "Ethnicity", "Age")
      .agg(
        sum(zeroNegative("Subscribers")).alias("Subscribers"),
        sum(zeroNegative("Gross_Adds")).alias("Gross_Adds"),
        sum(zeroNegative("Gross_Losses")).alias("Gross_Losses"),
        sum(col("Base_Adjustment")).alias("Base_Adjustment")
      )
      .withColumn("Income", lit("All"))

    val ageAllDF = boostMarketFinalOutputAllDemo
      .groupBy("Month", "Geography", "Geography_Type", "Brand", "Plan_Type", "Ethnicity", "Income")
      .agg(
        sum(zeroNegative("Subscribers")).alias("Subscribers"),
        sum(zeroNegative("Gross_Adds")).alias("Gross_Adds"),
        sum(zeroNegative("Gross_Losses")).alias("Gross_Losses"),
        sum(col("Base_Adjustment")).alias("Base_Adjustment")
      )
      .withColumn("Age", lit("All"))

    val ethnicityAllDF = boostMarketFinalOutputAllDemo
      .groupBy("Month", "Geography", "Geography_Type", "Brand", "Plan_Type", "Age", "Income")
      .agg(
        sum(zeroNegative("Subscribers")).alias("Subscribers"),
        sum(zeroNegative("Gross_Adds")).alias("Gross_Adds"),
        sum(zeroNegative("Gross_Losses")).alias("Gross_Losses"),
        sum(col("Base_Adjustment")).alias("Base_Adjustment")
      )
      .withColumn("Ethnicity", lit("All"))

    val unifiedDF = incomeAllDF
      .select("Month", "Geography", "Geography_Type", "Brand", "Plan_Type", "Ethnicity", "Age", "Income", "Subscribers", "Gross_Adds", "Gross_Losses", "Base_Adjustment")
      .unionByName(
        ageAllDF.select("Month", "Geography", "Geography_Type", "Brand", "Plan_Type", "Ethnicity", "Age", "Income", "Subscribers", "Gross_Adds", "Gross_Losses", "Base_Adjustment")
      )
      .unionByName(
        ethnicityAllDF.select("Month", "Geography", "Geography_Type", "Brand", "Plan_Type", "Ethnicity", "Age", "Income", "Subscribers", "Gross_Adds", "Gross_Losses", "Base_Adjustment")
      )

    val finalDF = unifiedDF.withColumn("creation_date", current_date())

    finalDF.as[BoostMarketOutputPairwiseDemo]
  }

  // ** Final Queries to separate the packaging output **

  // DMA File 1 (ethnicity)
  def dmaFinalOutputPairwiseDemoCombinationsDF1(
    dmaFinalOutputPairwiseDemoCombinationsData: Dataset[DmaFinalOutputPairwiseDemoCombinations],
    creationDate: LocalDate
  ): DataFrame = {
    val dmaDF1 = dmaFinalOutputPairwiseDemoCombinationsData
      .filter(col("creation_date") === lit(creationDate) && col("ethnicity") === "All")
      .select(
        col("Month"),
        col("Geography"),
        col("Geography_Type"),
        col("Brand"),
        col("Plan_Type"),
        col("Ethnicity"),
        col("Age"),
        col("Income"),
        col("Subscribers"),
        col("Gross_Adds"),
        col("Gross_Losses"),
        col("Base_Adjustment"))
    dmaDF1
  }

  // DMA File 2 (age)
  def dmaFinalOutputPairwiseDemoCombinationsDF2(
    dmaFinalOutputPairwiseDemoCombinationsData: Dataset[DmaFinalOutputPairwiseDemoCombinations],
    creationDate: LocalDate
  ): DataFrame = {
    val dmaDF2 = dmaFinalOutputPairwiseDemoCombinationsData
      .filter(col("creation_date") === lit(creationDate) && col("age") === "All")
      .select(
        col("Month"),
        col("Geography"),
        col("Geography_Type"),
        col("Brand"),
        col("Plan_Type"),
        col("Ethnicity"),
        col("Age"),
        col("Income"),
        col("Subscribers"),
        col("Gross_Adds"),
        col("Gross_Losses"),
        col("Base_Adjustment"))
    dmaDF2
  }

  // DMA File 3 (income)
  def dmaFinalOutputPairwiseDemoCombinationsDF3(
    dmaFinalOutputPairwiseDemoCombinationsData: Dataset[DmaFinalOutputPairwiseDemoCombinations],
    creationDate: LocalDate
  ): DataFrame = {
    val dmaDF3 = dmaFinalOutputPairwiseDemoCombinationsData
      .filter(col("creation_date") === lit(creationDate) && col("income") === "All")
      .select(
        col("Month"),
        col("Geography"),
        col("Geography_Type"),
        col("Brand"),
        col("Plan_Type"),
        col("Ethnicity"),
        col("Age"),
        col("Income"),
        col("Subscribers"),
        col("Gross_Adds"),
        col("Gross_Losses"),
        col("Base_Adjustment"))
    dmaDF3
  }

  // CMA File 1 (ethnicity)
  def cmaFinalOutputPairwiseDemoCombinationsBaseDF1(
    cmaFinalOutputPairwiseDemoCombinationsData: Dataset[CmaFinalOutputPairwiseDemoCombinations],
    creationDate: LocalDate
  ): DataFrame = {
    val cmaDF1 = cmaFinalOutputPairwiseDemoCombinationsData
      .filter(col("creation_date") === lit(creationDate) && col("ethnicity") === "All")
      .select(
        col("Month"),
        col("Geography"),
        col("Geography_Type"),
        col("Brand"),
        col("Plan_Type"),
        col("Ethnicity"),
        col("Age"),
        col("Income"),
        col("Subscribers"),
        col("Gross_Adds"),
        col("Gross_Losses"),
        col("Base_Adjustment")
      )
    cmaDF1
  }

  // CMA File 2 (age)
  def cmaFinalOutputPairwiseDemoCombinationsBaseDF2(
    cmaFinalOutputPairwiseDemoCombinationsData: Dataset[CmaFinalOutputPairwiseDemoCombinations],
    creationDate: LocalDate
  ): DataFrame = {
    val cmaDF2 = cmaFinalOutputPairwiseDemoCombinationsData
      .filter(col("creation_date") === lit(creationDate) && col("age") === "All")
      .select(
        col("Month"),
        col("Geography"),
        col("Geography_Type"),
        col("Brand"),
        col("Plan_Type"),
        col("Ethnicity"),
        col("Age"),
        col("Income"),
        col("Subscribers"),
        col("Gross_Adds"),
        col("Gross_Losses"),
        col("Base_Adjustment")
      )
    cmaDF2
  }

  // CMA File 3 (income)
  def cmaFinalOutputPairwiseDemoCombinationsBaseDF3(
    cmaFinalOutputPairwiseDemoCombinationsData: Dataset[CmaFinalOutputPairwiseDemoCombinations],
    creationDate: LocalDate
  ): DataFrame = {
    val cmaDF3 = cmaFinalOutputPairwiseDemoCombinationsData
      .filter(col("creation_date") === lit(creationDate) && col("income") === "All")
      .select(
        col("Month"),
        col("Geography"),
        col("Geography_Type"),
        col("Brand"),
        col("Plan_Type"),
        col("Ethnicity"),
        col("Age"),
        col("Income"),
        col("Subscribers"),
        col("Gross_Adds"),
        col("Gross_Losses"),
        col("Base_Adjustment")
      )
    cmaDF3
  }

  // VPGM from DMA Pairwise Demo Combinations
  def vpgmFromDmaFinalOutputPairwiseDemoCombinationsDF(
    vpgmFromDmaFinalOutputPairwiseDemoCombinationsData: Dataset[VpgmFromDmaFinalOutputPairwiseDemoCombinations],
    creationDate: LocalDate
  ): DataFrame = {
    val vpgmDF = vpgmFromDmaFinalOutputPairwiseDemoCombinationsData
      .filter(col("creation_date") === lit(creationDate))
      .select(
        col("Month"),
        col("Geography"),
        col("Geography_Type"),
        col("Brand"),
        col("Plan_Type"),
        col("Ethnicity"),
        col("Age"),
        col("Income"),
        col("Subscribers"),
        col("Gross_Adds"),
        col("Gross_Losses"),
        col("Base_Adjustment")
      )
    vpgmDF
  }

 // Mso Ilec Footprints Pairwise Demo Combinations
  def msoIlecFootprintsFinalOutputPairwiseDemoCombinationsDF(
    msoIlecFootprintsFinalOutputPairwiseDemoCombinationsData: Dataset[MsoIlecFootprintsFinalOutputPairwiseDemoCombinations],
    creationDate: LocalDate
  ): DataFrame = {
    val footprintDF = msoIlecFootprintsFinalOutputPairwiseDemoCombinationsData
      .filter(col("creation_date") === lit(creationDate))
      .select(
        col("Month"),
        col("Geography"),
        col("Geography_Type"),
        col("Brand"),
        col("Plan_Type"),
        col("Ethnicity"),
        col("Age"),
        col("Income"),
        col("Subscribers"),
        col("Gross_Adds"),
        col("Gross_Losses"),
        col("Base_Adjustment")
      )
    footprintDF
  }

  // Boost Market Pairwise Demo Combinations
  def boostMarketFinalOutputPairwiseDemoCombinationsDF(
    boostMarketFinalOutputPairwiseDemoCombinationsData: Dataset[BoostMarketFinalOutputPairwiseDemoCombinations],
    creationDate: LocalDate
  ): DataFrame = {
    val boostDF = boostMarketFinalOutputPairwiseDemoCombinationsData
      .filter(col("creation_date") === lit(creationDate))
      .filter(col("Geography").isNotNull && col("Geography") =!= "") // Exclude rows where Geography is null
      .select(
        col("Month"),
        col("Geography"),
        col("Geography_Type"),
        col("Brand"),
        col("Plan_Type"),
        col("Ethnicity"),
        col("Age"),
        col("Income"),
        col("Subscribers"),
        col("Gross_Adds"),
        col("Gross_Losses"),
        col("Base_Adjustment")
      )
    boostDF
  }
}

object Packaging {
  def apply()(implicit spark: SparkSession): Packaging = new Packaging()
}
