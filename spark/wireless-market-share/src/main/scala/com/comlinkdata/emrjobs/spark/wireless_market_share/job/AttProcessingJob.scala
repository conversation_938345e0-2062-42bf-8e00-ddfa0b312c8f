package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.largescale.commons.{<PERSON><PERSON><PERSON><PERSON>, Spark<PERSON><PERSON><PERSON><PERSON><PERSON>, Utils}
import com.comlinkdata.largescale.commons.io.dataset.writeCsvOverwrite
import com.comlinkdata.largescale.schema.wireless_market_share._
import com.comlinkdata.emrjobs.spark.wireless_market_share.{BaseAdjustmentsDMA, CombineAllMetrics, EndOfPeriod, GrossAdditionsDMA, GrossLossesDMA, WMSHelper}
import com.comlinkdata.largescale.commons.fileutils.CldFileUtils
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.{ZeroOutBrandsDMA, ZipToDMA}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.SparkSession
import com.comlinkdata.largescale.commons.RichDate._
import java.time.{LocalDate, YearMonth}
import java.time.temporal.IsoFields.QUARTER_OF_YEAR
import java.net.URI
import scala.util.Try


/**
  * Subscriber calculation process parameters
  * @param monthToProcess: Specific month of subs we need to generate
  * @param industryModelLoc: National Model Data of the total subscribers
  * @param portedCarrierLossesLoc: WCV Ported Carrier Losses
  * @param portedCarrierWinsLoc: WCV Ported Carrier Wins
  * @param subscriberOutputLoc: S3 location of the subscribers output to read last month subs
  * @param zeroBrandsLoc: S3 location of brand list to make the subs zero for the particular DMA region
  * @param zipToDmaLoc: Zip to DMA lookup
  * @param revision: revision
  * @param outputBasePath: Output base path of the subs
  * @param outputPartitions: Number of partitions
  */
case class AttProcessingJobConfig(
  monthToProcess: Option[LocalDate],
  industryModelLoc: URI,
  portedCarrierLossesLoc: URI,
  portedCarrierWinsLoc: URI,
  subscriberOutputLoc: URI,
  zeroBrandsLoc: URI,
  zipToDmaLoc: URI,
  revision: Int,
  outputBasePath: URI,
  outputPartitions: Int
)


// Sample config just for FYIe
object AttProcessingJobConfig {

  val sample: AttProcessingJobConfig = AttProcessingJobConfig(
    monthToProcess = Option(LocalDate.of(2022, 9, 1)),
    industryModelLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/industry_model/",
    portedCarrierLossesLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/ported_losses/",
    portedCarrierWinsLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/ported_wins/",
    subscriberOutputLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/outputs/without_demographics/final_output/",
    zeroBrandsLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/zero_brands/",
    zipToDmaLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/inputs/lookups/zip_to_dma_mapping/",
    revision = 1,
    outputBasePath = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/wms_att_3_0/outputs/without_demographics/",
    outputPartitions = 1
  )
}


object AttProcessingJob extends SparkJob[AttProcessingJobConfig](AttProcessingJobRunner)

object AttProcessingJobRunner extends SparkJobRunner[AttProcessingJobConfig] with LazyLogging {
  def runJob(config: AttProcessingJobConfig)(implicit spark: SparkSession): Unit  = {
    import spark.implicits._

    // Default processing date would be the latest month
    val processingDate = LocalDate.now().withDayOfMonth(1)
    val currentQuarter = processingDate.getQuarter
    val currentYear = processingDate.getYear
    val startDateCurrentQuarter = YearMonth.of(currentYear, (currentQuarter-1) * 3 + 1).`with`(QUARTER_OF_YEAR, currentQuarter).atDay(1)

    // Get current date and set to first day of the month
    val deliveryDate = LocalDate.now().withDayOfMonth(1)

    // Dynamic revision value
    val revision = config.revision

    // Specific month to process, the default month to process should be the previous month
    val monthToProcess = config.monthToProcess.getOrElse(processingDate.minusMonths(1))
    logger.info(s"Calculating the Subscribers for the month: $monthToProcess")

    // Get quarter, quarter's start date and end date
    val year = monthToProcess.getYear
    val quarter = monthToProcess.getQuarter
    val startDate = YearMonth.of(year, (quarter-1) * 3 + 1).`with`(QUARTER_OF_YEAR, quarter).atDay(1)
    val endDate = startDate.plusMonths(2)
    val prevQuarterEndDate = startDate.minusMonths(1)
    val processingMonth = monthToProcess.getMonthValue.formatted("%02d")

    // Read common schema's
    val industryModelPath = Utils.joinPaths(
      config.industryModelLoc, s"processing_date=$processingDate")
    logger.info(s"Loading Industry Model input data from the path: ${industryModelPath}")
    val industryModelData = IndustryModel.read(industryModelPath)

    val portedCarrierLossesPath = Utils.joinPaths(
      config.portedCarrierLossesLoc, s"processing_date=$processingDate")
    logger.info(s"Loading Ported Carrier Losses input data from the path: ${portedCarrierLossesPath}")

    // Read and filter ported losses data based on the in-progress quarter month processing.
    var portedCarrierLossesData = spark.emptyDataset[PortedCarrierLosses]

    // if monthToProcess is first month of the in-progress quarter then filter losses only for the first month
    if(monthToProcess.isBefore(processingDate) && currentQuarter == quarter && monthToProcess.equals(startDateCurrentQuarter)) {
      logger.info("Reading ported losses data for the first month of the in-progress quarter process")
      portedCarrierLossesData = PortedCarrierLosses.read(portedCarrierLossesPath).filter($"date_trunc" === startDate)
    }

    // if monthToProcess is the second month of the in-progress quarter then filter losses for the in-progress quarter
    else if(monthToProcess.isBefore(processingDate) && currentQuarter == quarter && monthToProcess.equals(startDateCurrentQuarter.plusMonths(1))) {
      logger.info("Reading ported losses data for the second month of the in-progress quarter process")
      portedCarrierLossesData = PortedCarrierLosses.read(portedCarrierLossesPath).filter($"date_trunc" >= startDate && $"date_trunc" < endDate)
    }

    // if it's a completed quarter then filter losses for the completed quarter process
    else {
      logger.info("Reading ported losses data for the full quarter of the completed quarter process")
      portedCarrierLossesData = PortedCarrierLosses.read(portedCarrierLossesPath).filter($"date_trunc" >= startDate && $"date_trunc" <= endDate)
    }

    val portedCarrierWinsPath = Utils.joinPaths(
      config.portedCarrierWinsLoc, s"processing_date=$processingDate")
    logger.info(s"Loading Ported Carrier Wins input data from the path: $portedCarrierWinsPath")

    // Read and filter ported wins data based on the in-progress quarter month processing.
    var portedCarrierWinsData = spark.emptyDataset[PortedCarrierWins]

    // if monthToProcess is first month of the in-progress quarter then filter wins only for the first month
    if(monthToProcess.isBefore(processingDate) && currentQuarter == quarter && monthToProcess.equals(startDateCurrentQuarter)) {
      logger.info("Reading ported wins data for the first month of the in-progress quarter process")
      portedCarrierWinsData = PortedCarrierWins.read(portedCarrierWinsPath).filter($"date_trunc" === startDate)
    }

    // if monthToProcess is the second month of the in-progress quarter then filter wins for the in-progress quarter
    else if(monthToProcess.isBefore(processingDate) && currentQuarter == quarter && monthToProcess.equals(startDateCurrentQuarter.plusMonths(1))) {
      logger.info("Reading ported wins data for the second month of the in-progress quarter process")
      portedCarrierWinsData = PortedCarrierWins.read(portedCarrierWinsPath).filter($"date_trunc" >= startDate && $"date_trunc" < endDate)
    }

    // if it's a completed quarter then filter wins for the completed quarter process
    else {
      logger.info("Reading ported wins data for the full quarter of the completed quarter process")
      portedCarrierWinsData = PortedCarrierWins.read(portedCarrierWinsPath).filter($"date_trunc" >= startDate && $"date_trunc" <= endDate)
    }

    // Last month Subs used for EOP formula
    val cbDateToFilter = monthToProcess.minusMonths(1)
    val yearOfLastMonth = cbDateToFilter.getYear
    val prevProcessingMonth = cbDateToFilter.getMonthValue.formatted("%02d")
    val subscriberOutputPath = Utils.joinPaths(
      URI create s"${config.subscriberOutputLoc.toString}", s"year=$yearOfLastMonth", s"month=$prevProcessingMonth", s"r=$revision"
    )
    val subscriberOutputPathFinal = generateLatestDeliveryDateInputPath(subscriberOutputPath)
    logger.info(s"Loading Subscriber's Output input data from the path: $subscriberOutputPathFinal")
    val subscribersData = SubscribersOutput.read(subscriberOutputPathFinal).filter($"customer_base_date" === cbDateToFilter)

    // Read previous quarters subscribers
    val yearOfPrevQuarterEndDate = prevQuarterEndDate.getYear
    val monthOfPrevQuarterEndDate = prevQuarterEndDate.getMonthValue.formatted("%02d")
    val prevQuarterSubsOutputPath = Utils.joinPaths(
      URI create s"${config.subscriberOutputLoc.toString}", s"year=$yearOfPrevQuarterEndDate", s"month=$monthOfPrevQuarterEndDate", s"r=$revision"
    )
    val prevQuarterSubsOutputFinalPath = generateLatestDeliveryDateInputPath(prevQuarterSubsOutputPath)
    logger.info(s"Loading Previous Quarter's Subscriber's Output input data from the path: $prevQuarterSubsOutputFinalPath")
    val prevQuarterSubsData = SubscribersOutput.read(prevQuarterSubsOutputFinalPath).filter($"customer_base_date" === prevQuarterEndDate)

    logger.info(s"Loading Zero Brands input data from the path: ${config.zeroBrandsLoc}")
    val zeroBrandsData = ZeroOutBrandsDMA.read(config.zeroBrandsLoc)

    logger.info(s"Loading Zip to DMA lookup data from the path: ${config.zipToDmaLoc}")
    val ZipToDmaData = ZipToDMA.read(config.zipToDmaLoc)

    logger.info("Zeroing out the ported wins and losses data to zero-out the brands for specific market")
    val portedCarrierWinsDMAZeroed = WMSHelper().zeroOutAddsDMA(portedCarrierWinsData,zeroBrandsData)
    val portedCarrierLossesDMAZeroed = WMSHelper().zeroOutLossesDMA(portedCarrierLossesData, zeroBrandsData)

    // ** Calculations **

    // 1. Estimated Gross Additions data
    logger.info(s"Calculating the estimated Gross Additions...")
    val grossAdditionsData = GrossAdditionsDMA().calculateGrossAdditions(
      monthToProcess, industryModelData, portedCarrierWinsDMAZeroed, prevQuarterSubsData
    )
    logger.info(s"Finished calculating the estimated gross additions.")

    // 2. Estimated Gross Losses data
    logger.info(s"Calculating the estimated Gross Losses...")
    val grossLossesData = GrossLossesDMA().calculateGrossLosses(
      monthToProcess, industryModelData, portedCarrierLossesDMAZeroed, prevQuarterSubsData
    )
    logger.info(s"Finished calculating the estimated gross losses.")

    // 3. Base Adjustments Data
    logger.info(s"Calculating the base adjustments...")
    val baseAdjustmentsData = BaseAdjustmentsDMA().calculateBaseAdjustments(
      monthToProcess, industryModelData, portedCarrierLossesDMAZeroed
    )
    logger.info(s"Finished calculating the base adjustments.")

    // 4. End of Period Formula
    logger.info(s"Calculating the Subscribers using EOP formula...")
    val subsEopData = EndOfPeriod().calculateEndOfPeriodDMA(
      subscribersData, grossAdditionsData, grossLossesData, baseAdjustmentsData
    )
    logger.info(s"Finished calculating the Subscribers using EOP formula.")

    // Combine all metrics
    val finalOutput = CombineAllMetrics().combineAllMetricsDMA(
      grossAdditionsData, grossLossesData, baseAdjustmentsData, subsEopData, ZipToDmaData
    )

    // Write data to S3
    // TODO: Remove functionality of writing the intermediate metrics once final dataset verified and accepted
    val outputBasePath = config.outputBasePath.toString

    // Gross Additions
    val grossAdditionsOutputPath = Utils.joinPaths(
      URI create s"$outputBasePath", "gross_additions", s"year=$year", s"month=$processingMonth", s"r=$revision", s"delivery_date=$deliveryDate"
    )
    logger.info(s"Writing estimated gross additions to the S3 location = $grossAdditionsOutputPath")
    writeCsvOverwrite(grossAdditionsData, config.outputPartitions, grossAdditionsOutputPath.toString)

    // Gross Losses
    val grossLossesOutputPath = Utils.joinPaths(
      URI create s"$outputBasePath", "gross_losses", s"year=$year", s"month=$processingMonth", s"r=$revision", s"delivery_date=$deliveryDate"
    )
    logger.info(s"Writing estimated gross losses to the S3 location = $grossLossesOutputPath")
    writeCsvOverwrite(grossLossesData, config.outputPartitions, grossLossesOutputPath.toString)

    // Base Adjustments
    val baseAdjustmentsOutputPath = Utils.joinPaths(
      URI create s"$outputBasePath", "base_adjustments", s"year=$year", s"month=$processingMonth", s"r=$revision", s"delivery_date=$deliveryDate"
    )
    logger.info(s"Writing estimated base adjustments to the S3 location = $baseAdjustmentsOutputPath")
    writeCsvOverwrite(baseAdjustmentsData, config.outputPartitions, baseAdjustmentsOutputPath.toString)

    // Subscribers using EOP
    val subsEopPath = Utils.joinPaths(
      URI create s"$outputBasePath", "subscribers", s"year=$year", s"month=$processingMonth", s"r=$revision", s"delivery_date=$deliveryDate"
    )
    logger.info(s"Writing Subscribers data generated by EOP formula to the S3 location = $subsEopPath")
    writeCsvOverwrite(subsEopData, config.outputPartitions, subsEopPath.toString)

    // Final Output
    val subsOutputPath = Utils.joinPaths(
      URI create s"$outputBasePath", "final_subs_output", s"year=$year", s"month=$processingMonth", s"r=$revision", s"delivery_date=$deliveryDate"
    )
    logger.info(s"Writing estimated base adjustments to the S3 location = $subsOutputPath")
    writeCsvOverwrite(finalOutput, config.outputPartitions, subsOutputPath.toString)
  }


  private def generateNewROutputPath(basePath: URI)(implicit spark: SparkSession): String = {
    val futz = CldFileUtils.newBuilder.forUri(basePath).build
    val maybeUriLatestR = Try(Utils.maxPartitionValue(basePath, "r", futz, None)).toOption // latest revision
    val newR = maybeUriLatestR.map(ur => ur._2.toInt + 1).getOrElse(1) // latest revision increment by 1
    Utils.joinPaths(basePath.toString, s"r=$newR")
  }

  private def generateNewRInputPath(basePath: URI)(implicit spark: SparkSession): URI = {
    val futz = CldFileUtils.newBuilder.forUri(basePath).build
    val (finalInputPath, latestR) = Utils.maxPartitionValue(basePath, "r", futz, None)  // Max revision and path
    finalInputPath
  }

  private def generateLatestDeliveryDateInputPath(basePath: URI)(implicit spark: SparkSession): URI = {
    val futz = CldFileUtils.newBuilder.forUri(basePath).build
    val (finalInputPath, latestDeliveryDate) = Utils.maxPartitionValue(basePath, "delivery_date", futz, None)
    finalInputPath
  }
}
