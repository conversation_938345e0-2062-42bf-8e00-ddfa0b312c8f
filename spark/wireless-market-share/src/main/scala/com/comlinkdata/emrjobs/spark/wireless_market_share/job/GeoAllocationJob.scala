package com.comlinkdata.emrjobs.spark.wireless_market_share.job

import com.comlinkdata.largescale.commons.{Spark<PERSON><PERSON>, Spark<PERSON><PERSON><PERSON><PERSON><PERSON>, Utils}
import com.comlinkdata.largescale.commons.io.dataset.dynamicOverwrite
import com.comlinkdata.largescale.schema.wireless_market_share._
import com.comlinkdata.emrjobs.spark.wireless_market_share.inputs._
import com.comlinkdata.emrjobs.spark.wireless_market_share.{CalculateNationalMultipliers, GenerateFinalOutput, RateCenterPortedTNCustBaseAgg}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SaveMode, SparkSession}
import java.sql.Date
import java.time.LocalDate
import java.net.URI


case class GeoAllocationJobConfig(
  startDate: Option[Date],
  endDate: Option[Date],
  customerBaseDate: Option[Date],
  industryModelTotalOutputLoc: URI,
  customerBaseWirelessMonthlyMvnoLoc: URI,
  wirelessMovementWideGenericLoc: URI,
  dCommonOcnLookupLoc: URI,
  npaToNpaComplexLookupLoc: URI,
  wirelessMarketShareByRateCenterOutputLoc: URI,
  outputPartitions: Int
)

object GeoAllocationJobConfig {

  val sample: GeoAllocationJobConfig = GeoAllocationJobConfig(
    startDate = Option(Date.valueOf("2017-07-01")),
    endDate = Option(Date.valueOf("2022-08-01")),
    customerBaseDate = Option(Date.valueOf("2021-12-01")),
    // Athena table - cld.mktshare_total_output7_oct2022 65k
    industryModelTotalOutputLoc = URI create "s3://e000-comlinkdata-com/dev/wireless/marketshare/marketshare_total_output_packaging_r/mvno/AGG-4826-WMS-QA/processes/mktshare/output/national/full-output/",
    // Athena table - mvno.a_customer_base_wireless_monthly_mvno_v4_total5 10,000,000
    customerBaseWirelessMonthlyMvnoLoc = URI create "s3://e000-comlinkdata-com/dev/products/wireless/marketshare/a_customer_base_wireless_monthly_mvno/",
    // Athena table - nobbs_playpen.a_wireless_movement_wide_generic_v440_monthly_20220811 37764535
    wirelessMovementWideGenericLoc = URI create "s3://c400-athena-dev-comlinkdata-com/nobbs_playpen/a_wireless_movement_wide_generic_v440_monthly_20220811/month=2022-07-01/",
    // Athena table - cld.d_common_ocn 6525
    dCommonOcnLookupLoc = URI create "s3://c400-athena-dev-comlinkdata-com/data_ops/data_depot/d_common_ocn",
    // Athena table - mvno.npa_npacomplex_lookup_20200723 343
    npaToNpaComplexLookupLoc = URI create "s3://c400-athena-dev-comlinkdata-com/mvno/npa_npacomplex_lookup_20200723",
    // Athena table - nobbs_playpen.wireless_market_share_by_rate_center_20170701_20220801_v2_with_ported_breakout
    wirelessMarketShareByRateCenterOutputLoc = URI create "s3://e000-comlinkdata-com/dev/products/wireless/marketshare/wireless_market_share_by_rate_center/",
    outputPartitions = 1
  )
}


object GeoAllocationJob extends SparkJob[GeoAllocationJobConfig](GeoAllocationJobRunner)

object GeoAllocationJobRunner extends SparkJobRunner[GeoAllocationJobConfig] with LazyLogging {
  def runJob(config: GeoAllocationJobConfig)(implicit spark: SparkSession): Unit  = {
    // Assigns value to current_holder_plan_type_id based on value of current_holder_mvno_sp, ID1: current_holder_plan_type_id = 1, ID1: current_holder_plan_type_id = 2
    val MVNOSPTOPLANTYPEID1 = List[Int](5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6650, 6711, 6580)
    val MVNOSPTOPLANTYPEID2 = List[Int](1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712)
    val START_DATE = Date.valueOf("2017-07-01")
    // This is the hard-coded date used in the step 2.1
    val CUSTOMER_BASE_DATE = Date.valueOf("2021-12-01")

    val startDate = config.startDate.getOrElse(START_DATE)
    val endDate = config.endDate getOrElse Date.valueOf(LocalDate.now().withDayOfMonth(1))

    val customerBaseDate = config.customerBaseDate.getOrElse(CUSTOMER_BASE_DATE)

    // Read common schema's
    val industryModelTotalOutputPath = Utils.joinPaths(
      config.industryModelTotalOutputLoc, s"processing_date=${LocalDate.now().toString}",
    )
    val industryModelOutputData = IndustryModelTotalOutput.read(industryModelTotalOutputPath)
    industryModelOutputData.cache()

    val mvnoCBWirelessData = MvnoCustomerBase.read(config.customerBaseWirelessMonthlyMvnoLoc)
    mvnoCBWirelessData.cache()

    val wirelessMovementWideData = WirelessMovementWideMonthlyAgg.read(config.wirelessMovementWideGenericLoc)
    wirelessMovementWideData.cache()

    // Read lookup data
    val dCommonOcnData = lookup.DCommonOcnLookup.read(config.dCommonOcnLookupLoc)
    dCommonOcnData.cache()

    val npaToNpaComplexData = lookup.NpaToNpaComplexLookup.read(config.npaToNpaComplexLookupLoc)
    npaToNpaComplexData.cache()


    // Step 1: Read National Industry Model Inputs
    val nationalSubscriberMultipliersIMData = NationalIndustryModelInputs().computeNationalSubscriberMultipliers(
      industryModelOutputData, startDate, endDate
    )
    nationalSubscriberMultipliersIMData.cache()

    val nationalLossMultipliersIMData = NationalIndustryModelInputs().computeNationalLossMultipliers(
      industryModelOutputData, startDate, endDate
    )

    val nationalLossMultipliersDisconnectsIMData = NationalIndustryModelInputs().computeNationalLossMultipliersDisconnects(
      industryModelOutputData, startDate, endDate
    )

    val nationalAddMultipliersIMData = NationalIndustryModelInputs().computeNationalAddMultipliers(
      industryModelOutputData, startDate, endDate
    )

    val nationalAddMultipliersIntraMnoIMData = NationalIndustryModelInputs().computeNationalAddMultipliersIntraMno(
      industryModelOutputData, startDate, endDate
    )

    val nationalAddMultipliersActivationsIMData = NationalIndustryModelInputs().computeNationalAddMultipliersActivations(
      industryModelOutputData, startDate, endDate
    )


    // Step 2: Read National ported TN customer base inputs
    val xmSMTiebreakerData = NationalCustomerBaseInputs().computeXMSMTiebreaker(
      mvnoCBWirelessData, customerBaseDate
    ).cache()


    val nationalSubscriberMultipliersPortedCBData = NationalCustomerBaseInputs().computeNationalSubscriberMultipliersPortedCustomerBase(
      mvnoCBWirelessData, xmSMTiebreakerData, dCommonOcnData, MVNOSPTOPLANTYPEID1, MVNOSPTOPLANTYPEID2, startDate, endDate
    ).cache()


    val nationalAddMultipliersPortedCBData = NationalCustomerBaseInputs().computeNationalAddMultipliersPortedCustomerBase(
      mvnoCBWirelessData, xmSMTiebreakerData, dCommonOcnData, MVNOSPTOPLANTYPEID1, MVNOSPTOPLANTYPEID2, startDate, endDate
    )


    val nationalAddMultipliersIntraMnoData = NationalCustomerBaseInputs().computeNationalAddMultipliersIntraMnoInputs(
      wirelessMovementWideData, industryModelOutputData, startDate, endDate
    )


    // Step 3: Read calculated National Multipliers
    val nationalSubscriberMultipliersData = CalculateNationalMultipliers().calculateNationalSubscriberMultipliers(
      nationalSubscriberMultipliersPortedCBData, nationalSubscriberMultipliersIMData
    )

    val nationalLossMultipliersData = CalculateNationalMultipliers().calculateNationalLossMultipliers(
      nationalSubscriberMultipliersPortedCBData, nationalLossMultipliersIMData
    )

    val nationalAddMultipliersData = CalculateNationalMultipliers().calculateNationalAddMultipliers(
      nationalAddMultipliersPortedCBData, nationalAddMultipliersIMData
    )

    val nationalIntraMnoAddMultipliersData = CalculateNationalMultipliers().calculateNationalIntraMnoAddMultipliers(
      nationalAddMultipliersIntraMnoData, nationalAddMultipliersIntraMnoIMData
    )


    // Step 4: Read Rate Center ported TN customer base inputs
    val rateCenterNpaPortedCBData = RateCenterPortedTNCustBaseAgg().computeRateCenterNpaPortedCBInputs(
      mvnoCBWirelessData, xmSMTiebreakerData, dCommonOcnData, npaToNpaComplexData, MVNOSPTOPLANTYPEID1, MVNOSPTOPLANTYPEID2, startDate, endDate
    )

    val rateCenterNpaAddsPortedCBData = RateCenterPortedTNCustBaseAgg().computeRateCenterNpaAddsPortedCBInputs(
      mvnoCBWirelessData, xmSMTiebreakerData, dCommonOcnData, npaToNpaComplexData, MVNOSPTOPLANTYPEID1, MVNOSPTOPLANTYPEID2, startDate, endDate
    )

    val rateCenterAddsIntraMnoData = RateCenterPortedTNCustBaseAgg().computeRateCenterAddsIntraMNOs(
      wirelessMovementWideData, industryModelOutputData, startDate, endDate
    )

    // Step 5: Apply national multipliers to Rate Center

    val rateCenterNPATotalSubsData = NationalMultipliersRateCenter().computeRateCenterNPATotalSubs(
      rateCenterNpaPortedCBData, nationalSubscriberMultipliersData, nationalLossMultipliersData, nationalSubscriberMultipliersIMData
    )
    rateCenterNPATotalSubsData.cache()


    val rateCenterNPATotalAddsData = NationalMultipliersRateCenter().computeRateCenterNPATotalAdds(
      rateCenterNpaAddsPortedCBData, nationalAddMultipliersData, nationalSubscriberMultipliersIMData
    )
    rateCenterNPATotalAddsData.cache()


    val rateCenterTotalIntraMNOAddsData = NationalMultipliersRateCenter().computeRateCenterTotalIntraMNOAdds(
      rateCenterAddsIntraMnoData, nationalIntraMnoAddMultipliersData, nationalSubscriberMultipliersIMData
    )


    // Step 6: Scale up small regional MNOs
    val npaComplexTotalSubsData = RegionalMNOUpscale().computeNPAComplexTotalSubs(rateCenterNPATotalSubsData)

    val npaComplexTotalAddsData = RegionalMNOUpscale().computeNPAComplexTotalAdds(rateCenterNPATotalAddsData)

    val npaComplexPlanTypeMultipliersData = RegionalMNOUpscale().computeNPAComplexPlanTypeMultipliers(npaComplexTotalSubsData)

    val NPAComplexPlanTypeAddMultipliersData = RegionalMNOUpscale().computeNPAComplexPlanTypeAddMultipliers(npaComplexTotalAddsData)

    val rateCenterNPATotalSubsWithRegionalMNOsData = RegionalMNOUpscale().computeRateCenterNPATotalSubsWithRegionalMNOs(
      rateCenterNPATotalSubsData, npaComplexPlanTypeMultipliersData
    )


    val rateCenterNPATotalAddsWithRegionalMNOsData = RegionalMNOUpscale().computeRateCenterNPATotalAddsWithRegionalMNOs(
      rateCenterNPATotalAddsData, NPAComplexPlanTypeAddMultipliersData
    )

    //    // Step 7: Final computation
    val finalOutputData = GenerateFinalOutput().calculateFinalOutput(
      rateCenterNPATotalSubsWithRegionalMNOsData, rateCenterTotalIntraMNOAddsData, rateCenterNPATotalAddsWithRegionalMNOsData, nationalAddMultipliersActivationsIMData
    )

    // Generate processing date partition
    val outputPartitionPath = URI create GenerateFinalOutput.tsl(config.wirelessMarketShareByRateCenterOutputLoc).partition(LocalDate.now())

    dynamicOverwrite(finalOutputData,
      outputPartitionPath,
      config.outputPartitions,
      "customer_base_date"
    )

    // Write data in CSV format for downstream R process
    val OutputCsvPath = s"${config.industryModelTotalOutputLoc.toString.split("output/")(0)}geo/processing_date=${LocalDate.now()}"
    finalOutputData.repartition(config.outputPartitions)
      .write
      .format("csv")
      .mode(SaveMode.Overwrite)
      .option("header", value = true)
      .option("basePath", OutputCsvPath)
      .option("delimiter", ",")
      .save(OutputCsvPath)
  }
}
