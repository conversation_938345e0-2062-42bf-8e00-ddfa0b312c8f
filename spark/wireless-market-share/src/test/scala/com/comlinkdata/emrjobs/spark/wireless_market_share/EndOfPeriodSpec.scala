package com.comlinkdata.emrjobs.spark.wireless_market_share
import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share._
import org.apache.spark.sql.SaveMode
import org.apache.spark.sql.types.{DateType, DoubleType, IntegerType}

import java.net.URI

class EndOfPeriodSpec extends CldSparkBaseSpec {
  describe("End of period") {
    import spark.implicits._
    it("test")  {
      val subs = dataset[SubscribersOutputVPGM] {
        """
          |brand         |plan_type|customer_base_date|vpgm|subs|
          |AT&T Wireless |1        |2022-07-01        |10 |10                |
          |AT&T Wireless |1        |2022-08-01        |20 |10                |
          |AT&T Wireless |1        |2022-09-01        |30 |10                |
          |Altice        |1        |2022-10-01        |10 |10                |
          |Altice        |1        |2022-11-01        |20 |10                |
          |Altice        |1        |2023-01-01        |30 |10                |
          |Boost         |1        |2022-10-01        |10 |10                |
          |Boost         |1        |2022-11-01        |20 |10                |
        """
      }.as[SubscribersOutputVPGM]

      val grossAdds = dataset[EstimatedGrossAddsVPGM] {
        """
          |brand        |plan_type|customer_base_date|vpgm|estimated_gross_adds|
          |AT&T Wireless |1        |2022-08-01        |10 |10                |
          |AT&T Wireless |1        |2022-09-01        |20 |10                |
          |AT&T Wireless |1        |2022-10-01        |30 |10                |
          |Altice        |1        |2022-11-01        |10 |10                |
          |Altice        |1        |2022-12-01        |20 |10                |
          |Altice        |1        |2023-01-01        |30 |10                |
          |Boost         |1        |2022-11-01        |10 |10                |
          |Boost         |1        |2022-12-01        |20 |10                |
        """
      }
      val grossLosses = dataset[EstimatedGrossLossesVPGM] {
        """
          |brand        |plan_type|customer_base_date|vpgm|estimated_gross_losses|
          |AT&T Wireless |1        |2022-08-01        |10 |10                |
          |AT&T Wireless |1        |2022-09-01        |20 |10                |
          |AT&T Wireless |1        |2022-10-01        |30 |10                |
          |Altice        |1        |2022-11-01        |10 |10                |
          |Altice        |1        |2022-12-01        |20 |10                |
          |Altice        |1        |2023-01-01        |30 |10                |
          |Boost         |1        |2022-11-01        |10 |10                |
          |Boost         |1        |2022-12-01        |20 |10                |
        """
      }
      val baseAdjustments = dataset[EstimatedMonthlyBAVPGM] {
        """
          |brand         |plan_type|customer_base_date|vpgm|estimated_ba|
          |AT&T Wireless |1        |2022-08-01        |10 |10                |
          |AT&T Wireless |1        |2022-09-01        |20 |10                |
          |AT&T Wireless |1        |2022-10-01        |30 |10                |
          |Altice        |1        |2022-11-01        |10 |10                |
          |Altice        |1        |2022-12-01        |20 |10                |
          |Altice        |1        |2023-01-01        |30 |10                |
          |Boost         |1        |2022-11-01        |10 |10                |
          |Boost         |1        |2022-12-01        |20 |10                |
        """
      }.as[EstimatedMonthlyBAVPGM]



      val actualOutput = EndOfPeriod().calculateEndOfPeriodVPGM(subs,grossAdds,grossLosses,baseAdjustments)

    }
  }
}