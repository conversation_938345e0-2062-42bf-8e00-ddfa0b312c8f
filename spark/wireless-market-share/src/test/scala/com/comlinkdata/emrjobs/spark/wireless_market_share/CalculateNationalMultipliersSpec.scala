package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import org.apache.spark.sql.Dataset


class CalculateNationalMultipliersSpec extends CldSparkBaseSpec {
  // For Step 3.1
  describe("National Subscriber Multipliers Tests") {

    import spark.implicits._

    it("National Subscriber Multipliers") {

      val nationalSubscriberMultipliersPortedCBData = dataset[NationalSubscriberMultipliersPortedCustomerBase] {
          """
            |customer_base_date|current_holder_sp|current_holder_plan_type_id|ported_tn_subscribers|losses|
            |        2017-07-01|                2|                          3|                  2.4|   1.3|
            |        2017-08-01|                1|                          4|                12.10|  8.32|
            |        2019-05-01|                4|                          2|                  3.2|   5.4|
            |        2018-08-01|                5|                          1|                  4.2|   5.4|
            |        2020-07-01|                6|                          7|                  1.2|   7.4|
          """
      }

      val NationalSubscriberMultipliersIMData = dataset[NationalSubscriberMultipliersIM] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id| industry_model_subscribers|
          |        2017-07-01|                2|                          6|                       12.4|
          |        2018-09-01|                6|                          4|                      22.10|
          |        2019-10-01|                4|                          2|                        8.2|
          |        2018-08-01|                5|                          1|                        8.2|
          |        2020-07-01|                6|                          7|                        9.2|
          """
      }

      nationalSubscriberMultipliersPortedCBData.createOrReplaceTempView("nationalSubscriberMultipliersPortedCBDataView")
      NationalSubscriberMultipliersIMData.createOrReplaceTempView("NationalSubscriberMultipliersIMDataView")

      val query =
        """
          SELECT
          case when a.customer_base_date IS NULL then b.customer_base_date else a.customer_base_date end as customer_base_date,
          case when a.current_holder_sp IS NULL then b.current_holder_sp else a.current_holder_sp end as current_holder_sp,
          case when a.current_holder_plan_type_id IS NULL then b.current_holder_plan_type_id else a.current_holder_plan_type_id end as current_holder_plan_type_id,
          cast(a.ported_tn_subscribers as double) as ported_tn_subscribers,
          cast(b.industry_model_subscribers as double) as industry_model_subscribers,
          (cast(b.industry_model_subscribers as double) / cast(a.ported_tn_subscribers as double)) as total_subs_to_ported_subs_ratio
          FROM nationalSubscriberMultipliersPortedCBDataView a
          FULL OUTER JOIN NationalSubscriberMultipliersIMDataView b
          ON a.customer_base_date = b.customer_base_date
          AND a.current_holder_sp = b.current_holder_sp
          AND a.current_holder_plan_type_id = b.current_holder_plan_type_id
        """

      val expectedData: Dataset[NationalSubscriberMultipliers] = spark.sql(query).as[NationalSubscriberMultipliers]

      val actualData = CalculateNationalMultipliers().calculateNationalSubscriberMultipliers(
        nationalSubscriberMultipliersPortedCBData,
        NationalSubscriberMultipliersIMData
      )

      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()
      // Sum validation
      expectedData.select("ported_tn_subscribers").collect() shouldEqual
        actualData.select("ported_tn_subscribers").collect()
      expectedData.select("industry_model_subscribers").collect() shouldEqual
        actualData.select("industry_model_subscribers").collect()
      expectedData.select("total_subs_to_ported_subs_ratio").collect() shouldEqual
        actualData.select("total_subs_to_ported_subs_ratio").collect()

      // Dataset equality
      assertDatasetUnsortedEquals(actualData, expectedData)
    }
  }

  // For Step 3.2
  describe("National Loss Multipliers Tests") {

    import spark.implicits._

    it("National Loss Multipliers") {

      val nationalSubscriberMultipliersPortedCBData = dataset[NationalSubscriberMultipliersPortedCustomerBase] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id|ported_tn_subscribers|losses|
          |        2017-07-01|                2|                          3|                  2.4|   1.3|
          |        2017-08-01|                1|                          4|                12.10|  8.32|
          |        2019-05-01|                4|                          2|                  3.2|   5.4|
          |        2018-08-01|                5|                          1|                  4.2|   5.4|
          |        2020-07-01|                6|                          7|                  1.2|   7.4|
          """
      }

      val NationalLossMultipliersIMData = dataset[NationalLossMultipliersIM] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id| industry_model_losses|
          |        2017-07-01|                2|                          6|                  12.4|
          |        2018-09-01|                6|                          4|                  2.10|
          |        2019-10-01|                4|                          2|                   8.2|
          |        2018-08-01|                5|                          1|                   8.2|
          |        2020-07-01|                6|                          7|                   9.2|
          """
      }

      nationalSubscriberMultipliersPortedCBData.createOrReplaceTempView("nationalSubscriberMultipliersPortedCBDataView")
      NationalLossMultipliersIMData.createOrReplaceTempView("NationalLossMultipliersIMDataView")

      val query =
        """
          SELECT
          case when a.customer_base_date IS NULL then b.customer_base_date else a.customer_base_date end as customer_base_date,
          case when a.current_holder_sp IS NULL then b.current_holder_sp else a.current_holder_sp end as current_holder_sp,
          case when a.current_holder_plan_type_id IS NULL then b.current_holder_plan_type_id else a.current_holder_plan_type_id end as current_holder_plan_type_id,
          cast(a.losses as double) as ported_tn_losses,
          cast(b.industry_model_losses as double) as industry_model_losses,
          (cast(b.industry_model_losses as double) / cast(a.losses as double)) as total_losses_to_ported_losses_ratio
          FROM nationalSubscriberMultipliersPortedCBDataView a
          FULL OUTER JOIN NationalLossMultipliersIMDataView b
          ON a.customer_base_date = b.customer_base_date
          AND a.current_holder_sp = b.current_holder_sp
          AND a.current_holder_plan_type_id = b.current_holder_plan_type_id
        """

      val expectedData: Dataset[NationalLossMultipliers] = spark.sql(query).as[NationalLossMultipliers]

      val actualData = CalculateNationalMultipliers().calculateNationalLossMultipliers(
        nationalSubscriberMultipliersPortedCBData,
        NationalLossMultipliersIMData
      )

      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()
      // Sum validation
      expectedData.select("ported_tn_losses").collect() shouldEqual
        actualData.select("ported_tn_losses").collect()
      expectedData.select("industry_model_losses").collect() shouldEqual
        actualData.select("industry_model_losses").collect()
      expectedData.select("total_losses_to_ported_losses_ratio").collect() shouldEqual
        actualData.select("total_losses_to_ported_losses_ratio").collect()

      // Dataset equality
      assertDatasetUnsortedEquals(actualData, expectedData)
    }
  }

  // For Step 3.3
  describe("National Add Multipliers Tests") {

    import spark.implicits._

    it("National Add Multipliers") {

      val nationalAddMultipliersPortedCBData = dataset[NationalAddMultipliersPortedCustomerBase] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id|previous_holder_sp| previous_holder_plan_type_id|  adds|
          |        2017-07-01|                2|                          3|                 1|                            2|   2.4|
          |        2017-08-01|                1|                          4|                 2|                            1|   7.4|
          |        2019-05-01|                4|                          2|                 7|                            5|   2.5|
          |        2018-08-01|                5|                          1|                 6|                            4|   3.8|
          |        2020-07-01|                6|                          7|                 4|                            8|   1.9|
        """
      }

      val NationalAddMultipliersIMData = dataset[NationalAddMultipliersIM] {
        """
         |customer_base_date|previous_holder_sp|previous_holder_plan_type_id|current_holder_sp|current_holder_plan_type_id|industry_model_adds|
         |        2017-07-01|                 2|                           3|                1|                          2|                2.4|
         |        2017-08-01|                 1|                           4|                2|                          1|                7.4|
         |        2019-05-01|                 4|                           2|                7|                          5|                2.5|
         |        2018-08-01|                 5|                           1|                6|                          4|                3.8|
         |        2020-07-01|                 6|                           7|                4|                          8|                1.9|
        """
      }

      nationalAddMultipliersPortedCBData.createOrReplaceTempView("nationalAddMultipliersPortedCBDataView")
      NationalAddMultipliersIMData.createOrReplaceTempView("NationalAddMultipliersIMDataView")

      val query =
        """
          SELECT
          case when a.customer_base_date IS NULL then b.customer_base_date else a.customer_base_date end as customer_base_date,
          case when a.current_holder_sp IS NULL then b.current_holder_sp else a.current_holder_sp end as current_holder_sp,
          case when a.current_holder_plan_type_id IS NULL then b.current_holder_plan_type_id else a.current_holder_plan_type_id end as current_holder_plan_type_id,
          case when a.previous_holder_sp IS NULL then b.previous_holder_sp else a.previous_holder_sp end as previous_holder_sp,
          case when a.previous_holder_plan_type_id IS NULL then b.previous_holder_plan_type_id else a.previous_holder_plan_type_id end as previous_holder_plan_type_id,
          cast(a.adds as double) as ported_tn_adds,
          cast(b.industry_model_adds as double) as industry_model_adds,
          (cast(b.industry_model_adds as double) / cast(a.adds as double)) as total_adds_to_ported_adds_ratio
          FROM nationalAddMultipliersPortedCBDataView a
          FULL OUTER JOIN NationalAddMultipliersIMDataView b
          ON a.customer_base_date = b.customer_base_date
          AND a.current_holder_sp = b.current_holder_sp
          AND a.current_holder_plan_type_id = b.current_holder_plan_type_id
          AND a.previous_holder_sp = b.previous_holder_sp
          AND a.previous_holder_plan_type_id = b.previous_holder_plan_type_id
        """

      val expectedData: Dataset[NationalAddMultipliers] = spark.sql(query).as[NationalAddMultipliers]

      val actualData = CalculateNationalMultipliers().calculateNationalAddMultipliers(
        nationalAddMultipliersPortedCBData,
        NationalAddMultipliersIMData
      )

      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()
      // Sum validation
      expectedData.select("ported_tn_adds").collect() shouldEqual
        actualData.select("ported_tn_adds").collect()
      expectedData.select("industry_model_adds").collect() shouldEqual
        actualData.select("industry_model_adds").collect()
      expectedData.select("total_adds_to_ported_adds_ratio").collect() shouldEqual
        actualData.select("total_adds_to_ported_adds_ratio").collect()

      // Dataset equality
      assertDatasetUnsortedEquals(actualData, expectedData)
    }
  }

  // For Step 3.4
  describe("National Add Multipliers Intra Mno Tests") {

    import spark.implicits._

    it("National Add Multipliers Intra Mno") {

      val nationalAddMultipliersIntraMnoCBData = dataset[NationalAddMultipliersIntraMnoInputs] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id|previous_holder_sp| previous_holder_plan_type_id|  adds|
          |        2017-07-01|                2|                          3|                 1|                            2|   2.4|
          |        2017-08-01|                1|                          4|                 2|                            1|   7.4|
          |        2019-05-01|                4|                          2|                 7|                            5|   2.5|
          |        2018-08-01|                5|                          1|                 6|                            4|   3.8|
          |        2020-07-01|                6|                          7|                 4|                            8|   1.9|
        """
      }

      val nationalAddMultipliersIntraMnoIMData = dataset[NationalAddMultipliersIntraMnoIM] {
        """
          |customer_base_date|previous_holder_sp|previous_holder_plan_type_id|current_holder_sp|current_holder_plan_type_id|industry_model_adds|
          |        2017-07-01|                 2|                           3|                1|                          2|                2.4|
          |        2017-08-01|                 1|                           4|                2|                          1|                7.4|
          |        2019-05-01|                 4|                           2|                7|                          5|                2.5|
          |        2018-08-01|                 5|                           1|                6|                          4|                3.8|
          |        2020-07-01|                 6|                           7|                4|                          8|                1.9|
        """
      }

      nationalAddMultipliersIntraMnoCBData.createOrReplaceTempView("nationalAddMultipliersIntraMnoCBDataView")
      nationalAddMultipliersIntraMnoIMData.createOrReplaceTempView("nationalAddMultipliersIntraMnoIMDataView")

      val query =
        """
          SELECT
          case when a.customer_base_date IS NULL then b.customer_base_date else a.customer_base_date end as customer_base_date,
          case when a.current_holder_sp IS NULL then b.current_holder_sp else a.current_holder_sp end as current_holder_sp,
          case when a.current_holder_plan_type_id IS NULL then b.current_holder_plan_type_id else a.current_holder_plan_type_id end as current_holder_plan_type_id,
          case when a.previous_holder_sp IS NULL then b.previous_holder_sp else a.previous_holder_sp end as previous_holder_sp,
          case when a.previous_holder_plan_type_id IS NULL then b.previous_holder_plan_type_id else a.previous_holder_plan_type_id end as previous_holder_plan_type_id,
          cast(a.adds as double) as ported_tn_adds,
          cast(b.industry_model_adds as double) as industry_model_adds,
          (cast(b.industry_model_adds as double) / cast(a.adds as double)) as total_adds_to_ported_adds_ratio
          FROM nationalAddMultipliersIntraMnoCBDataView a
          FULL OUTER JOIN nationalAddMultipliersIntraMnoIMDataView b
          ON a.customer_base_date = b.customer_base_date
          AND a.current_holder_sp = b.current_holder_sp
          AND a.current_holder_plan_type_id = b.current_holder_plan_type_id
          AND a.previous_holder_sp = b.previous_holder_sp
          AND a.previous_holder_plan_type_id = b.previous_holder_plan_type_id
        """

      val expectedData: Dataset[NationalAddMultipliersIntraMno] = spark.sql(query).as[NationalAddMultipliersIntraMno]

      val actualData = CalculateNationalMultipliers().calculateNationalIntraMnoAddMultipliers(
        nationalAddMultipliersIntraMnoCBData,
        nationalAddMultipliersIntraMnoIMData
      )

      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()
      // Sum validation
      expectedData.select("ported_tn_adds").collect() shouldEqual
        actualData.select("ported_tn_adds").collect()
      expectedData.select("industry_model_adds").collect() shouldEqual
        actualData.select("industry_model_adds").collect()
      expectedData.select("total_adds_to_ported_adds_ratio").collect() shouldEqual
        actualData.select("total_adds_to_ported_adds_ratio").collect()

      // Dataset equality
      assertDatasetUnsortedEquals(actualData, expectedData)
    }
  }

}
