package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share.{IndustryModelTotalOutput, MvnoCustomerBase, WirelessMovementWideMonthlyAgg}
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.{DCommonOcnLookup, NpaToNpaComplexLookup}
import org.apache.spark.sql.Dataset
import java.sql.Date


class RateCenterPortedTNCustBaseAggSpec extends CldSparkBaseSpec {

  val startDate: Date = Date.valueOf("2017-07-01")
  val endDate: Date = Date.valueOf("2022-08-01")

  val mvnoSPList1 = List[Int](5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6650, 6711, 6580)
  val mvnoSPList2 = List[Int](1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712)

  // For Step 4.1
  describe("Rate Center Npa Ported CB Inputs Tests") {

    import spark.implicits._

    it("Rate Center Npa Ported CB Inputs") {

      val monthlyCustomerBaseWirelessData = dataset[MvnoCustomerBase] {
        """
          |current_holder_sp|previous_holder_sp|current_holder_mvno_sp|previous_holder_mvno_sp|noncompetitive_ind|zip_rc_kblock|npa|mvno_losing_plan_type_id|mvno_winning_plan_type_id|current_tenure_months|tenancy_months|total_customers|total_wins|total_losses|customer_base_date|
          |1|5  |6105   |5 |0 |91702 |626|1 |2  |0  |10  |1.00 |0.00 |0.00 |2021-11-01|
          |1|2|6105|6576|0|43113|740|2|2|19|14|1.00|0.00|0.00|2020-12-01|
          |6105|3|6105|6577|0|91355|661|1|2|127|6|1.00|0.00|0.00|2018-12-01|
          |6105|6|6105|6|0|92704|657|1|2|0|0|0.00|1.00|0.00|2021-07-01|
          |6649|1113|6052|1113|0|37211|615|2|2|34|24|1.00|0.00|0.00|2021-12-01|
        """
      }

      val xmsmTiebreakerIMData = dataset[XMSMTiebreaker] {
        """
          |zip_rc_kblock|current_holder_mvno_sp|
          |        96797|                     2|
          |        91709|                  5932|
          |        52402|                  6052|
          |        20723|                     8|
          |        44306|                     5|
          """
      }

      val dCommonOcnLookupData = dataset[DCommonOcnLookup] {
        """
          |ocn_common_dim_id|     common_name|common_name_mode| id6|changed|sp_reporting_nm|
          |                1|Verizon Wireless|               W|   1|    YES|            VZW|
          |                2|     Cablevision|               L|   2|    YES|         TYPELL|
          |                3|   AT&T Wireless|               P|   3|       |            ATT|
          |                4| Sprint Wireless|               W|   4|    YES|            SPR|
          |                5|Verizon Wireless|               M|   5|       |            VZW|
          """
      }

      val npaToNpaComplexLookupData = dataset[NpaToNpaComplexLookup] {
        """
          |npa|npa_complex_cld|state|state_name|final_shapefile_complex|nanpa_complex|
          |626|        205_659|   AL|   Alabama|                    205|      205/659|
          |740|        205_659|   CA|California|                    217|            0|
          |657|            216|   CA|California|                    207|          659|
          |217|            209|   ME|     Maine|                    209|      213/323|
          |207|            225|   WA|Washington|                    210|            0|
          """
      }

      monthlyCustomerBaseWirelessData.createOrReplaceTempView("monthlyCustomerBaseWirelessDataView")
      xmsmTiebreakerIMData.createOrReplaceTempView("xmsmTiebreakerIMDataView")
      dCommonOcnLookupData.createOrReplaceTempView("dCommonOcnLookupDataView")
      npaToNpaComplexLookupData.createOrReplaceTempView("npaToNpaComplexLookupDataView")

      val query =
        """
          with start_date as (select date('2017-07-01') as date),
          end_date as (select date('2022-08-01') as date)

          SELECT
          customer_base_date,
          a.zip_rc_kblock,
          a.npa,
          d.npa_complex_cld,
          case
          when a.current_holder_mvno_sp IN (6650, 6651) then 1
          when a.current_holder_mvno_sp = 6649 then b.current_holder_mvno_sp
          when a.current_holder_mvno_sp IN (2620, 6050, 178) then 6576
          when a.current_holder_mvno_sp = 6043 then 609
          when a.current_holder_mvno_sp = 6042 then 6577
          when a.current_holder_mvno_sp IN (6711, 6712, 6722, 6723, 6724) then 7
          else a.current_holder_mvno_sp end as current_holder_sp,
          case
          when a.current_holder_mvno_sp IN (1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712) then 2
          when a.current_holder_mvno_sp IN (5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6580, 6650, 6711, 6580) then 1
          else mvno_winning_plan_type_id end as current_holder_plan_type_id,
          sum(total_customers) as ported_tn_subscribers,
          sum(total_losses) as ported_tn_losses
          FROM monthlyCustomerBaseWirelessDataView a
          LEFT JOIN xmsmTiebreakerIMDataView b
          ON a.zip_rc_kblock = b.zip_rc_kblock
          LEFT JOIN dCommonOcnLookupDataView c
          ON a.previous_holder_sp = c.ocn_common_dim_id
          LEFT JOIN npaToNpaComplexLookupDataView d
          ON a.npa = d.npa
          WHERE
          noncompetitive_ind = 0
          AND a.customer_base_date between (select date from start_date) and (select date from end_date)
          AND (c.common_name_mode = 'W' OR c.common_name_mode IS NULL) -- d_common_ocn is out of date here
          AND a.current_holder_mvno_sp <> 5932
          AND a.previous_holder_mvno_sp <> 5932
          AND a.total_customers > cast(0.0000 as double)
          AND case
          when a.current_holder_mvno_sp IN (1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712) then 2
          when a.current_holder_mvno_sp IN (5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6650, 6711, 6580) then 1
          else mvno_winning_plan_type_id end IN (1, 2)
          GROUP BY
          customer_base_date,
          a.zip_rc_kblock,
          a.npa,
          d.npa_complex_cld,
          case
          when a.current_holder_mvno_sp IN (6650, 6651) then 1
          when a.current_holder_mvno_sp = 6649 then b.current_holder_mvno_sp
          when a.current_holder_mvno_sp IN (2620, 6050, 178) then 6576
          when a.current_holder_mvno_sp = 6043 then 609
          when a.current_holder_mvno_sp = 6042 then 6577
          when a.current_holder_mvno_sp IN (6711, 6712, 6722, 6723, 6724) then 7
          else a.current_holder_mvno_sp end,
          case
          when a.current_holder_mvno_sp IN (1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712) then 2
          when a.current_holder_mvno_sp IN (5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6580, 6650, 6711, 6580) then 1
          else mvno_winning_plan_type_id end
        """

      val expectedData: Dataset[RateCenterNPAPortedCustomerBase] = spark.sql(query)
        .as[RateCenterNPAPortedCustomerBase]

      val actualData = RateCenterPortedTNCustBaseAgg().computeRateCenterNpaPortedCBInputs(
        monthlyCustomerBaseWirelessData,
        xmsmTiebreakerIMData,
        dCommonOcnLookupData,
        npaToNpaComplexLookupData,
        mvnoSPList1, mvnoSPList2,
        startDate, endDate
      )

      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()

      // Sum validation
      expectedData.select("ported_tn_subscribers").collect() shouldEqual
        actualData.select("ported_tn_subscribers").collect()
      expectedData.select("ported_tn_losses").collect() shouldEqual
        actualData.select("ported_tn_losses").collect()

      // Dataset equality
      assertDatasetUnsortedEquals(actualData, expectedData)
    }
  }

  // For Step 4.2
  describe("Rate Center Npa Adds Ported CB Inputs Tests") {
    import spark.implicits._

    it("Rate Center Npa Adds Ported CB Inputs") {

      val monthlyCustomerBaseWirelessData = dataset[MvnoCustomerBase] {
        """
          |current_holder_sp|previous_holder_sp|current_holder_mvno_sp|previous_holder_mvno_sp|noncompetitive_ind|zip_rc_kblock|npa|mvno_losing_plan_type_id|mvno_winning_plan_type_id|current_tenure_months|tenancy_months|total_customers|total_wins|total_losses|customer_base_date|
          |1 |5  |6105   |5 |0 |91702 |626|1 |2  |0  |10  |1.00 |0.00 |0.00 |2021-11-01|
          | 1|2|6105|6576|0|43113|740|2|2|19|14|1.00|0.00|0.00|2020-12-01|
          |6105|3|6105|6577|0|91355|661|1|2|127|6|1.00|0.00|0.00|2018-12-01|
          |6105|6|6105|6|0|92704|657|1|2|0|0|0.00|1.00|0.00|2021-07-01|
          |6649|1113|6052|1113|0|37211|615|2|2|34|24|1.00|0.00|0.00|2021-12-01|
        """
      }

      val xmsmTiebreakerIMData = dataset[XMSMTiebreaker] {
        """
          |zip_rc_kblock|current_holder_mvno_sp|
          |        96797|                     2|
          |        91709|                  5932|
          |        52402|                  6052|
          |        20723|                     8|
          |        44306|                     5|
          """
      }

      val dCommonOcnLookupData = dataset[DCommonOcnLookup] {
        """
          |ocn_common_dim_id|     common_name|common_name_mode| id6|changed|sp_reporting_nm|
          |                1|Verizon Wireless|               W|   1|    YES|            VZW|
          |                2|     Cablevision|               L|   2|    YES|         TYPELL|
          |                3|   AT&T Wireless|               P|   3|       |            ATT|
          |                4| Sprint Wireless|               W|   4|    YES|            SPR|
          |                5|Verizon Wireless|               M|   5|       |            VZW|
          """
      }

      val npaToNpaComplexLookupData = dataset[NpaToNpaComplexLookup] {
        """
          |npa|npa_complex_cld|state|state_name|final_shapefile_complex|nanpa_complex|
          |626|        205_659|   AL|   Alabama|                    205|      205/659|
          |740|        205_659|   CA|California|                    217|            0|
          |657|            216|   CA|California|                    207|          659|
          |217|            209|   ME|     Maine|                    209|      213/323|
          |207|            225|   WA|Washington|                    210|            0|
          """
      }

      monthlyCustomerBaseWirelessData.createOrReplaceTempView("monthlyCustomerBaseWirelessDataView")
      xmsmTiebreakerIMData.createOrReplaceTempView("xmsmTiebreakerIMDataView")
      dCommonOcnLookupData.createOrReplaceTempView("dCommonOcnLookupDataView")
      npaToNpaComplexLookupData.createOrReplaceTempView("npaToNpaComplexLookupDataView")

      val query =
        """
          with start_date as (select date('2017-07-01') as date),
          end_date as (select date('2022-08-01') as date)

          SELECT
          customer_base_date,
          a.zip_rc_kblock,
          a.npa,
          d.npa_complex_cld,
          case
          when a.current_holder_mvno_sp IN (6650, 6651) then 1
          when a.current_holder_mvno_sp = 6649 then b.current_holder_mvno_sp
          when a.current_holder_mvno_sp IN (2620, 6050, 178) then 6576
          when a.current_holder_mvno_sp = 6043 then 609
          when a.current_holder_mvno_sp = 6042 then 6577
          when a.current_holder_mvno_sp IN (6711, 6712, 6722, 6723, 6724) then 7
          else a.current_holder_mvno_sp end as current_holder_sp,
          case
          when a.current_holder_mvno_sp IN (1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712) then 2
          when a.current_holder_mvno_sp IN (5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6580, 6650, 6711, 6580) then 1
          else mvno_winning_plan_type_id end as current_holder_plan_type_id,
          case
          when a.previous_holder_mvno_sp IN (6650, 6651) then 1
          when a.previous_holder_mvno_sp = 6649 then b.current_holder_mvno_sp
          when a.previous_holder_mvno_sp IN (2620, 6050, 178) then 6576
          when a.previous_holder_mvno_sp = 6043 then 609
          when a.previous_holder_mvno_sp = 6042 then 6577
          when a.previous_holder_mvno_sp IN (6711, 6712, 6722, 6723, 6724) then 7
          else a.previous_holder_mvno_sp end as previous_holder_sp,
          case
          when a.previous_holder_mvno_sp IN (1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712) then 2
          when a.previous_holder_mvno_sp IN (5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6650, 6711, 6580) then 1
          else mvno_losing_plan_type_id end as previous_holder_plan_type_id,
          sum(cast(a.total_wins as double)) as ported_tn_adds
          FROM monthlyCustomerBaseWirelessDataView a
          LEFT JOIN xmsmTiebreakerIMDataView b
          ON a.zip_rc_kblock = b.zip_rc_kblock
          LEFT JOIN dCommonOcnLookupDataView c
          ON a.previous_holder_sp = c.ocn_common_dim_id
          LEFT JOIN dCommonOcnLookupDataView e
          ON a.current_holder_sp = e.ocn_common_dim_id
          LEFT JOIN npaToNpaComplexLookupDataView d
          ON a.npa = d.npa
          WHERE
          noncompetitive_ind = 0
          AND a.customer_base_date between (select date from start_date) and (select date from end_date)
          AND (c.common_name_mode = 'W' OR c.common_name_mode IS NULL) -- d_common_ocn is out of date here
          AND (e.common_name_mode = 'W' OR e.common_name_mode IS NULL) -- d_common_ocn is out of date here
          AND a.current_holder_mvno_sp <> 5932
          AND a.previous_holder_mvno_sp <> 5932
          AND cast(a.total_wins as double) > cast(0.0000 as double)
          AND case
          when a.current_holder_mvno_sp IN (1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712) then 2
          when a.current_holder_mvno_sp IN (5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6650, 6711, 6580) then 1
          else mvno_winning_plan_type_id end IN (1, 2)
          AND case
          when a.previous_holder_mvno_sp IN (1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712) then 2
          when a.previous_holder_mvno_sp IN (5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6650, 6711, 6580) then 1
          else mvno_losing_plan_type_id end IN (1, 2)
          GROUP BY
          customer_base_date,
          a.zip_rc_kblock,
          a.npa,
          d.npa_complex_cld,
          case
          when a.current_holder_mvno_sp IN (6650, 6651) then 1
          when a.current_holder_mvno_sp = 6649 then b.current_holder_mvno_sp
          when a.current_holder_mvno_sp IN (2620, 6050, 178) then 6576
          when a.current_holder_mvno_sp = 6043 then 609
          when a.current_holder_mvno_sp = 6042 then 6577
          when a.current_holder_mvno_sp IN (6711, 6712, 6722, 6723, 6724) then 7
          else a.current_holder_mvno_sp end,
          case
          when a.current_holder_mvno_sp IN (1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712) then 2
          when a.current_holder_mvno_sp IN (5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6580, 6650, 6711, 6580) then 1
          else mvno_winning_plan_type_id end,
          case
          when a.previous_holder_mvno_sp IN (6650, 6651) then 1
          when a.previous_holder_mvno_sp = 6649 then b.current_holder_mvno_sp
          when a.previous_holder_mvno_sp IN (2620, 6050, 178) then 6576
          when a.previous_holder_mvno_sp = 6043 then 609
          when a.previous_holder_mvno_sp = 6042 then 6577
          when a.previous_holder_mvno_sp IN (6711, 6712, 6722, 6723, 6724) then 7
          else a.previous_holder_mvno_sp end,
          case
          when a.previous_holder_mvno_sp IN (1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712) then 2
          when a.previous_holder_mvno_sp IN (5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6650, 6711, 6580) then 1
          else mvno_losing_plan_type_id end
        """

      val expectedData: Dataset[RateCenterNPAAddsPortedCustomerBase] = spark.sql(query)
        .as[RateCenterNPAAddsPortedCustomerBase]

      val actualData = RateCenterPortedTNCustBaseAgg().computeRateCenterNpaAddsPortedCBInputs(
        monthlyCustomerBaseWirelessData,
        xmsmTiebreakerIMData,
        dCommonOcnLookupData,
        npaToNpaComplexLookupData,
        mvnoSPList1, mvnoSPList2,
        startDate, endDate
      )

      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()

      // Sum validation
      expectedData.select("ported_tn_adds").collect() shouldEqual
        actualData.select("ported_tn_adds").collect()

      // Dataset equality
      assertDatasetUnsortedEquals(actualData, expectedData)
    }
  }

  // For Step 4.3
  describe("Rate Center Adds Intra MNO Inputs Tests") {
    import spark.implicits._

    it("Rate Center Adds Intra MNO Inputs") {


      val wirelessMovementWideData = dataset[WirelessMovementWideMonthlyAgg] {
        """
          |zip_cd|primary_sp|secondary_sp|primary_plan_type_id|secondary_plan_type_id|      adjusted_wins|     month|
          | 41101|       609|           2|                   1|                     1|0.43757701925336867|2017-02-01|
          | 36322|       609|           2|                   1|                     2|                0.0|2021-02-01|
          | 02169|      6577|           2|                   2|                     2|                1.0|2021-02-01|
          | 98103|      6577|           2|                   2|                     2| 1.1141435283720935|2021-02-01|
          | 45505|       609|           2|                   1|                     2| 12.110000000000001|2021-02-01|
          | 60010|      6546|           7|                   1|                     1|                1.0|2017-02-01|
          | 45238|         3|           8|                   1|                     1|                0.0|2021-02-01|
          | 63334|         2|        6546|                   1|                     1|                1.0|2021-02-01|
          | 76009|         4|           1|                   2|                     2|                1.0|2021-02-01|
          | 20158|      6545|           3|                   1|                     2|                0.0|2021-02-01|
        """
      }

      val industryModelTotalData = dataset[IndustryModelTotalOutput] {
        """
          |year|month|cld_observed_loser|cld_observed_winner|quarter| begin_segment|begin_mno|begin_mvno|   end_segment|end_mno|end_mvno|diagonal|migration| port_est|      est|winning_losing_prop|winning_prop|losing_prop| top_prop|      prop|churn_losing_mno_sp|churn_winning_mno_sp|churn_losing_mvno_sp|churn_winning_mvno_sp|churn_losing_plan_type_id|churn_winning_plan_type_id|
          |2017|    7|               ATT|                BST|      3|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|11887.766|18927.371|         0.30224314|  0.30506408| 0.32162955|0.3213925|0.30224314|                  2|                   2|                   2|                  609|                        2|                         1|
          |2017|    7|               ATT|                BST|      3| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|1501.2604| 6589.807|         0.30224314|  0.30506408| 0.32162955|0.3213925|0.30224314|               6577|                6577|                   2|                  609|                        1|                         1|
          |2017|    7|               ATT|                PCS|      3|postpaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0|30411.941|45670.445|         0.29406747|  0.29787907| 0.32162955|0.3213925|0.29406747|                  2|                   4|                   2|                    6|                        2|                         1|
          |2021|    2|               ATT|                PCS|      3| prepaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0|7916.0737|30581.549|         0.29406747|  0.29787907| 0.32162955|0.3213925|0.29406747|                  2|                6577|                   2|                    6|                        1|                         1|
          |2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0|257.53076|1317.0182|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        2|                         2|
          |2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0| 280.6646|1423.7842|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                609|                 609|                   2|                 6577|                        1|                         2|
          |2022|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1304.8761| 6292.739|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        2|                         1|
          |2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1504.7664|7800.3594|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  4|                   4|                   2|                 6577|                        1|                         1|
          |2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 43334.21|48085.137|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  5|                   3|                   2|                    3|                        2|                         2|
          |2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 9153.899|41041.812|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  1|                   1|                   2|                    3|                        1|                         2|
        """
      }

      wirelessMovementWideData.createOrReplaceTempView("wirelessMovementWideDataView")
      industryModelTotalData.createOrReplaceTempView("industryModelTotalDataView")

      val query =
        """
          with start_date as (select date('2017-07-01') as date),
          end_date as (select date('2022-08-01') as date)

          SELECT
          date(a.month) as customer_base_date,
          a.zip_cd as zip_rc_kblock,
          cast(a.primary_sp as integer) as current_holder_sp,
          cast(a.primary_plan_type_id as integer) as current_holder_plan_type_id,
          cast(a.secondary_sp as integer) as previous_holder_sp,
          cast(a.secondary_plan_type_id as integer) as previous_holder_plan_type_id,
          sum(cast(a.adjusted_wins as double)) as ported_tn_adds
          FROM wirelessMovementWideDataView a
          INNER JOIN
          (
          SELECT distinct
          churn_losing_mvno_sp,
          churn_winning_mvno_sp
          FROM industryModelTotalDataView
          WHERE churn_losing_mno_sp = churn_winning_mno_sp
          AND churn_losing_mvno_sp <> churn_winning_mvno_sp
          AND churn_losing_mvno_sp IS NOT NULL
          AND churn_winning_mvno_sp IS NOT NULL
          AND churn_losing_mvno_sp <> 6
          AND churn_winning_mvno_sp <> 6
          ) b
          ON cast(a.primary_sp as integer) = b.churn_winning_mvno_sp
          AND cast(a.secondary_sp as integer) = b.churn_losing_mvno_sp
          WHERE
          date(a.month) between (select date from start_date) and (select date from end_date)
          GROUP BY
          a.zip_cd,
          date(a.month),
          a.primary_sp,
          a.primary_plan_type_id,
          a.secondary_sp,
          a.secondary_plan_type_id
        """

      val expectedData: Dataset[RateCenterAddsIntraMNO] = spark.sql(query)
        .as[RateCenterAddsIntraMNO]

      val actualData = RateCenterPortedTNCustBaseAgg().computeRateCenterAddsIntraMNOs(
        wirelessMovementWideData,
        industryModelTotalData,
        startDate, endDate
      )

      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()

      // Sum validation
      expectedData.select("ported_tn_adds").collect() shouldEqual
        actualData.select("ported_tn_adds").collect()

      // Dataset equality
      assertDatasetUnsortedEquals(actualData, expectedData)
    }
  }
}