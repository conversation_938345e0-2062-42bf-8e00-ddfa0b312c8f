package com.comlinkdata.emrjobs.spark.wireless_market_share.inputs

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import org.apache.spark.sql.{Dataset}

import java.sql.Date

class RegionalMNOUpscaleSpec extends CldSparkBaseSpec {


  import spark.implicits._

  describe("NPAComplexTotalSubs")  {
    it("correct schema")  {

      val tempDate: Date = Date.valueOf("2022-09-15")
      val rateCenter = Seq(RateCenterNPATotalSubs(tempDate,"","","",0,0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0)).toDF().as[RateCenterNPATotalSubs]
      val result = RegionalMNOUpscale.apply()(spark).computeNPAComplexTotalSubs(rateCenter)
      val expected = Seq(NPAComplexTotalSubs("",tempDate,0,0,0.0,0.0,0.0,0,0.0,0.0,0.0,0.0)).toDF().as[NPAComplexTotalSubs]

      result.dtypes shouldBe expected.dtypes
    }
    it("correct output")  {
      val rateCenter = dataset[RateCenterNPATotalSubs] {
        """
          |customer_base_date|zip_rc_kblock|npa|npa_complex_cld|current_holder_sp|current_holder_plan_type_id|ported_tn_subscribers|ported_tn_losses|national_ported_tn_subscribers|national_industry_model_subscribers|national_total_subs_to_ported_subs_ratio|rate_center_industry_model_subscribers|national_ported_tn_losses|national_industry_model_losses|national_total_losses_to_ported_losses_ratio|rate_center_industry_model_losses|industry_model_sp_ind|
          |        2022-09-15|             |  0|              0|                0|                          0|                  0.0|             0.0|                           0.0|                                0.0|                                     0.0|                                   0.0|                      0.0|                           0.0|                                         0.0|                              0.0|                    0|
          |        2022-09-16|             |  1|              0|                1|                          1|                  0.0|             0.0|                           1.0|                                1.0|                                     1.0|                                   0.0|                      0.0|                           1.0|                                         0.0|                              1.0|                    1|
          |        2022-09-16|             |  1|              0|                1|                          1|                  0.0|             0.0|                           1.0|                                1.0|                                     1.0|                                   0.0|                      0.0|                           1.0|                                         0.0|                              1.0|                    1|
          |        2022-09-17|             |  2|              0|                2|                          2|                  0.0|             0.0|                           2.0|                                2.0|                                     2.0|                                   0.0|                      0.0|                           2.0|                                         0.0|                              2.0|                    2|
          |        2022-09-15|             |  0|              0|                0|                          0|                  0.0|             0.0|                           0.0|                                0.0|                                     0.0|                                   0.0|                      0.0|                           0.0|                                         0.0|                              0.0|                    0|
        """
      }

      val expectedData = dataset[NPAComplexTotalSubs] {
        """
          |npa_complex_cld|customer_base_date|current_holder_sp|current_holder_plan_type_id|national_ported_tn_subscribers|national_industry_model_subscribers|national_total_subs_to_ported_subs_ratio|industry_model_sp_ind|ported_tn_subscribers|ported_tn_losses|npa_complex_industry_model_subscribers|npa_complex_industry_model_losses|
          |              0|        2022-09-15|                0|                          0|                           0.0|                                0.0|                                     0.0|                    0|                  0.0|             0.0|                                   0.0|                              0.0|
          |              0|        2022-09-16|                1|                          1|                           1.0|                                1.0|                                     1.0|                    1|                  0.0|             0.0|                                   0.0|                              2.0|
          |              0|        2022-09-17|                2|                          2|                           2.0|                                2.0|                                     2.0|                    2|                  0.0|             0.0|                                   0.0|                              2.0|
        """
      }

      val result = RegionalMNOUpscale.apply()(spark).computeNPAComplexTotalSubs(rateCenter)

      assertDatasetEquals(expectedData, result)
    }
  }
  describe("NPAComplexTotalAdds") {
    it("correct schema") {
      val tempDate: Date = Date.valueOf("2022-09-15")
      val rateCenter = Seq(RateCenterNPATotalAdds(tempDate, "", "", "", 0, 0, 0, 0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0)).toDF().as[RateCenterNPATotalAdds]
      val result = RegionalMNOUpscale.apply()(spark).computeNPAComplexTotalAdds(rateCenter)
      val expected = Seq(NPAComplexTotalAdds("", tempDate, 0, 0, 0, 0, 0.0, 0.0, 0.0, 0, 0, 0.0, 0.0)).toDF().as[NPAComplexTotalAdds]

      result.dtypes shouldBe expected.dtypes
    }
    it("correct output") {
      val rateCenter = dataset[RateCenterNPATotalAdds] {
        """
          |customer_base_date|zip_rc_kblock|npa|npa_complex_cld|current_holder_sp|current_holder_plan_type_id|previous_holder_sp|previous_holder_plan_type_id|ported_tn_adds|national_ported_tn_adds|national_industry_model_adds|national_total_adds_to_ported_adds_ratio|rate_center_industry_model_adds|industry_model_sp_ind_current|industry_model_sp_ind_previous|
          |        2022-09-15|             |  0|              0|                0|                          0|                 0|                           0|           0.0|                    0.0|                         0.0|                                     0.0|                            0.0|                            0|                             0|
          |        2022-09-16|             |  0|              1|                1|                          1|                 1|                           1|           1.0|                    1.0|                         1.0|                                     1.0|                            1.0|                            0|                             0|
          |        2022-09-16|             |  0|              1|                1|                          1|                 1|                           1|           2.0|                    1.0|                         1.0|                                     1.0|                            2.0|                            0|                             0|
          |        2022-09-17|             |  0|              2|                2|                          2|                 2|                           2|           3.0|                    2.0|                         2.0|                                     2.0|                            3.0|                            0|                             0|
          |        2022-09-17|             |  0|              2|                2|                          2|                 2|                           2|           4.0|                    2.0|                         2.0|                                     2.0|                            4.0|                            0|                             0|
        """
      }

      val expectedData = dataset[NPAComplexTotalAdds] {
        """
          |npa_complex_cld|customer_base_date|current_holder_sp|current_holder_plan_type_id|previous_holder_sp|previous_holder_plan_type_id|national_ported_tn_adds|national_industry_model_adds|national_total_adds_to_ported_adds_ratio|industry_model_sp_ind_current|industry_model_sp_ind_previous|ported_tn_adds|npa_complex_industry_model_adds|
          |              0|        2022-09-15|                0|                          0|                 0|                           0|                    0.0|                         0.0|                                     0.0|                            0|                             0|           0.0|                            0.0|
          |              1|        2022-09-16|                1|                          1|                 1|                           1|                    1.0|                         1.0|                                     1.0|                            0|                             0|           3.0|                            3.0|
          |              2|        2022-09-17|                2|                          2|                 2|                           2|                    2.0|                         2.0|                                     2.0|                            0|                             0|           7.0|                            7.0|
        """
      }

      val result = RegionalMNOUpscale.apply()(spark).computeNPAComplexTotalAdds(rateCenter)

      assertDatasetEquals(expectedData, result)
    }
  }
  describe("NPAComplexPlanTypeMultipliers") {
    it("correct schema") {
      val tempDate: Date = Date.valueOf("2022-09-15")
      val rateCenter = Seq(NPAComplexTotalSubs("", tempDate, 0, 0, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 0.0, 0.0)).toDF().as[NPAComplexTotalSubs]
      val result = RegionalMNOUpscale.apply()(spark).computeNPAComplexPlanTypeMultipliers(rateCenter)
      val expected = Seq(NPAComplexPlanTypeMultipliers("", tempDate, 0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)).toDF().as[NPAComplexPlanTypeMultipliers]

      result.dtypes shouldBe expected.dtypes
    }
    it("correct output") {
      val npaSubs = dataset[NPAComplexTotalSubs] {
        """
          |npa_complex_cld|customer_base_date|current_holder_sp|current_holder_plan_type_id|national_ported_tn_subscribers|national_industry_model_subscribers|national_total_subs_to_ported_subs_ratio|industry_model_sp_ind|ported_tn_subscribers|ported_tn_losses|npa_complex_industry_model_subscribers|npa_complex_industry_model_losses|
          |              1|        2022-09-15|                0|                          1|                           0.0|                                0.0|                                     0.0|                    1|                  1.0|             2.0|                                   2.0|                              1.0|
          |              1|        2022-09-15|                0|                          1|                           0.0|                                0.0|                                     0.0|                    1|                  1.0|             2.0|                                   2.0|                              1.0|
          |              2|        2022-09-16|                0|                          2|                           0.0|                                0.0|                                     0.0|                    1|                  1.0|             2.0|                                   2.0|                              1.0|
          |              3|        2022-09-17|                0|                          3|                           0.0|                                0.0|                                     0.0|                    1|                  1.0|             2.0|                                   2.0|                              1.0|
          |              0|        2022-09-15|                0|                          0|                           0.0|                                0.0|                                     0.0|                    0|                  1.0|             2.0|                                   0.0|                              1.0|
        """
      }

      val expectedData = dataset[NPAComplexPlanTypeMultipliers] {
        """
          |npa_complex_cld|customer_base_date|current_holder_plan_type_id|ported_tn_subscribers|npa_complex_industry_model_subscribers|ported_to_total_subs_multiplier|ported_tn_losses|npa_complex_industry_model_losses|ported_to_total_loss_multiplier|
          |              1|        2022-09-15|                          1|                  2.0|                                   4.0|                            2.0|             4.0|                              2.0|                            0.5|
          |              2|        2022-09-16|                          2|                  1.0|                                   2.0|                            2.0|             2.0|                              1.0|                            0.5|
          |              3|        2022-09-17|                          3|                  1.0|                                   2.0|                            2.0|             2.0|                              1.0|                            0.5|
        """
      }

      val result = RegionalMNOUpscale.apply()(spark).computeNPAComplexPlanTypeMultipliers(npaSubs)

      assertDatasetEquals(expectedData, result)
    }
  }
  describe("NPAComplexPlanTypeAddMultipliers") {
    it("correct schema") {
      val tempDate: Date = Date.valueOf("2022-09-15")
      val rateCenter = Seq(NPAComplexTotalAdds("", tempDate, 0, 0, 0, 0, 0.0, 0.0, 0.0, 0, 0, 0.0, 0.0)).toDF().as[NPAComplexTotalAdds]
      val result = RegionalMNOUpscale.apply()(spark).computeNPAComplexPlanTypeAddMultipliers(rateCenter)
      val expected = Seq(NPAComplexPlanTypeAddMultipliers("", tempDate, 0, 0, 0.0, 0.0, 0.0)).toDF().as[NPAComplexPlanTypeAddMultipliers]

      result.dtypes shouldBe expected.dtypes
    }
    it("correct output") {
      val npaAdds = dataset[NPAComplexTotalAdds] {
        """
          |npa_complex_cld|customer_base_date|current_holder_sp|current_holder_plan_type_id|previous_holder_sp|previous_holder_plan_type_id|national_ported_tn_adds|national_industry_model_adds|national_total_adds_to_ported_adds_ratio|industry_model_sp_ind_current|industry_model_sp_ind_previous|ported_tn_adds|npa_complex_industry_model_adds|
          |              0|        2022-09-15|                0|                          0|                 0|                           0|                    0.0|                         0.0|                                     0.0|                            0|                             0|           1.0|                            2.0|
          |              1|        2022-09-15|                0|                          1|                 0|                           1|                    0.0|                         0.0|                                     0.0|                            1|                             1|           1.0|                            3.0|
          |              1|        2022-09-15|                0|                          1|                 0|                           1|                    0.0|                         0.0|                                     0.0|                            1|                             1|           1.0|                            4.0|
          |              2|        2022-09-16|                0|                          2|                 0|                           2|                    0.0|                         0.0|                                     0.0|                            1|                             1|           1.0|                            5.0|
          |              3|        2022-09-17|                0|                          3|                 0|                           3|                    0.0|                         0.0|                                     0.0|                            1|                             1|           1.0|                            6.0|
        """
      }
      val expectedData = dataset[NPAComplexPlanTypeAddMultipliers] {
        """
          |npa_complex_cld|customer_base_date|current_holder_plan_type_id|previous_holder_plan_type_id|ported_tn_adds|npa_complex_industry_model_adds|ported_to_total_adds_multiplier|
          |              1|        2022-09-15|                          1|                           1|           2.0|                            7.0|                            3.5|
          |              2|        2022-09-16|                          2|                           2|           1.0|                            5.0|                            5.0|
          |              3|        2022-09-17|                          3|                           3|           1.0|                            6.0|                            6.0|
        """
      }

      val result = RegionalMNOUpscale.apply()(spark).computeNPAComplexPlanTypeAddMultipliers(npaAdds)

      assertDatasetEquals(expectedData, result)
    }
  }
  describe("RateCenterNPATotalSubsWithRegionalMNOs") {
    it("correct schema") {
      val tempDate: Date = Date.valueOf("2022-09-15")
      val rateCenter = Seq(RateCenterNPATotalSubs(tempDate,"","","",0,0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0)).toDF().as[RateCenterNPATotalSubs]
      val npaComplex = Seq(NPAComplexPlanTypeMultipliers("", tempDate, 0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)).toDF().as[NPAComplexPlanTypeMultipliers]
      val result = RegionalMNOUpscale.apply()(spark).computeRateCenterNPATotalSubsWithRegionalMNOs(rateCenter, npaComplex)
      val expected = Seq(RateCenterNPATotalSubsWithRegionalMNOs(tempDate, "", "", "", 0, 0, 0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)).toDF().as[RateCenterNPATotalSubsWithRegionalMNOs]

      result.dtypes shouldBe expected.dtypes
    }
    it("correct output") {
      val rateCenter = dataset[RateCenterNPATotalSubs] {
        """
        |customer_base_date|zip_rc_kblock|npa|npa_complex_cld|current_holder_sp|current_holder_plan_type_id|ported_tn_subscribers|ported_tn_losses|national_ported_tn_subscribers|national_industry_model_subscribers|national_total_subs_to_ported_subs_ratio|rate_center_industry_model_subscribers|national_ported_tn_losses|national_industry_model_losses|national_total_losses_to_ported_losses_ratio|rate_center_industry_model_losses|industry_model_sp_ind|
        |        2022-09-15|             |  0|              0|                0|                          0|                  1.0|             5.0|                           0.0|                                0.0|                                     0.0|                                   1.0|                      0.0|                           0.0|                                         1.0|                              0.0|                    0|
        |        2022-09-16|             |  1|              0|                1|                          1|                  2.0|             5.0|                           1.0|                                1.0|                                     1.0|                                   1.0|                      0.0|                           1.0|                                         2.0|                              1.0|                    1|
        |        2022-09-16|             |  1|              0|                1|                          1|                  3.0|             5.0|                           1.0|                                1.0|                                     1.0|                                   1.0|                      0.0|                           1.0|                                         3.0|                              1.0|                    1|
        |        2022-09-17|             |  2|              0|                2|                          2|                  4.0|             5.0|                           2.0|                                2.0|                                     2.0|                                   1.0|                      0.0|                           2.0|                                         4.0|                              2.0|                    2|
        |        2022-09-15|             |  0|              0|                0|                          0|                  5.0|             5.0|                           0.0|                                0.0|                                     0.0|                                   1.0|                      0.0|                           0.0|                                         5.0|                              0.0|                    0|
        """
      }

      val npaComplex = dataset[NPAComplexPlanTypeMultipliers] {
        """
          |npa_complex_cld|customer_base_date|current_holder_plan_type_id|ported_tn_subscribers|npa_complex_industry_model_subscribers|ported_to_total_subs_multiplier|ported_tn_losses|npa_complex_industry_model_losses|ported_to_total_loss_multiplier|
          |              1|        2022-09-15|                          1|                  2.0|                                   4.0|                            2.0|             4.0|                              2.0|                            0.5|
          |              2|        2022-09-16|                          2|                  1.0|                                   2.0|                            2.0|             2.0|                              1.0|                            0.5|
          |              3|        2022-09-17|                          3|                  1.0|                                   2.0|                            2.0|             2.0|                              1.0|                            0.5|
          """
      }

      val expectedData = dataset[RateCenterNPATotalSubsWithRegionalMNOs] {
        """
          |customer_base_date|zip_rc_kblock|npa|npa_complex_cld|industry_model_sp_ind|current_holder_sp|current_holder_plan_type_id|national_ported_tn_subscribers|national_industry_model_subscribers|national_total_subs_to_ported_subs_ratio|national_ported_tn_losses|national_industry_model_losses|national_total_losses_to_ported_losses_ratio|ported_tn_subscribers|ported_tn_losses|rate_center_npa_industry_model_subscribers|rate_center_npa_industry_model_losses|
          |        2022-09-15|             |  0|              0|                    0|                0|                          0|                           0.0|                                0.0|                                     0.0|                      0.0|                           0.0|                                         1.0|                  1.0|             5.0|                                      null|                                 null|
          |        2022-09-16|             |  1|              0|                    1|                1|                          1|                           1.0|                                1.0|                                     1.0|                      0.0|                           1.0|                                         2.0|                  2.0|             5.0|                                       1.0|                                  1.0|
          |        2022-09-16|             |  1|              0|                    1|                1|                          1|                           1.0|                                1.0|                                     1.0|                      0.0|                           1.0|                                         3.0|                  3.0|             5.0|                                       1.0|                                  1.0|
          |        2022-09-17|             |  2|              0|                    2|                2|                          2|                           2.0|                                2.0|                                     2.0|                      0.0|                           2.0|                                         4.0|                  4.0|             5.0|                                       0.0|                                  0.0|
          |        2022-09-15|             |  0|              0|                    0|                0|                          0|                           0.0|                                0.0|                                     0.0|                      0.0|                           0.0|                                         5.0|                  5.0|             5.0|                                      null|                                 null|
        """
      }

      val result = RegionalMNOUpscale.apply()(spark).computeRateCenterNPATotalSubsWithRegionalMNOs(rateCenter, npaComplex)

      assertDatasetEquals(expectedData.na.fill(0), result.na.fill(0))
    }
  }
  describe("RateCenterNPATotalAddsWithRegionalMNOs") {
    it("correct schema") {
      val tempDate: Date = Date.valueOf("2022-09-15")
      val rateCenter = Seq(RateCenterNPATotalAdds(tempDate, "", "", "", 0, 0, 0, 0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0)).toDF().as[RateCenterNPATotalAdds]
      val npaComplex = Seq(NPAComplexPlanTypeAddMultipliers("", tempDate, 0, 0, 0.0, 0.0, 0.0)).toDF().as[NPAComplexPlanTypeAddMultipliers]
      val result = RegionalMNOUpscale.apply()(spark).computeRateCenterNPATotalAddsWithRegionalMNOs(rateCenter, npaComplex)
      val expected = Seq(RateCenterNPATotalAddsWithRegionalMNOs(tempDate, "", "", "", 0, 0, 0, 0, 0, 0, 0.0, 0.0, 0.0, 0.0, 0.0)).toDF().as[RateCenterNPATotalAddsWithRegionalMNOs]

      result.dtypes shouldBe expected.dtypes
    }
    it("correct output") {

      val rateCenter = dataset[RateCenterNPATotalAdds] {
        """
        |customer_base_date|zip_rc_kblock|npa|npa_complex_cld|current_holder_sp|current_holder_plan_type_id|previous_holder_sp|previous_holder_plan_type_id|ported_tn_adds|national_ported_tn_adds|national_industry_model_adds|national_total_adds_to_ported_adds_ratio|rate_center_industry_model_adds|industry_model_sp_ind_current|industry_model_sp_ind_previous|
        |        2022-09-15|             |  0|              0|                0|                          0|                 0|                           0|           0.0|                    0.0|                         0.0|                                     0.0|                            0.0|                            0|                             0|
        |        2022-09-16|             |  0|              1|                1|                          1|                 1|                           1|           1.0|                    1.0|                         1.0|                                     1.0|                            1.0|                            0|                             0|
        |        2022-09-16|             |  0|              1|                1|                          1|                 1|                           1|           2.0|                    1.0|                         1.0|                                     1.0|                            2.0|                            0|                             0|
        |        2022-09-17|             |  0|              2|                2|                          2|                 2|                           2|           3.0|                    2.0|                         2.0|                                     2.0|                            3.0|                            0|                             0|
        |        2022-09-17|             |  0|              2|                2|                          2|                 2|                           2|           4.0|                    2.0|                         2.0|                                     2.0|                            4.0|                            0|                             0|
        """
      }

      val npaComplex = dataset[NPAComplexPlanTypeAddMultipliers] {
        """
          |npa_complex_cld|customer_base_date|current_holder_plan_type_id|previous_holder_plan_type_id|ported_tn_adds|npa_complex_industry_model_adds|ported_to_total_adds_multiplier|
          |              1|        2022-09-15|                          1|                           1|           2.0|                            7.0|                            3.5|
          |              2|        2022-09-16|                          2|                           2|           1.0|                            5.0|                            5.0|
          |              3|        2022-09-17|                          3|                           3|           1.0|                            6.0|                            6.0|
          """
      }

      val expectedData = dataset[RateCenterNPATotalAddsWithRegionalMNOs] {
        """
          |customer_base_date|zip_rc_kblock|npa|npa_complex_cld|industry_model_sp_ind_current|industry_model_sp_ind_previous|current_holder_sp|current_holder_plan_type_id|previous_holder_sp|previous_holder_plan_type_id|national_ported_tn_adds|national_industry_model_adds|national_total_adds_to_ported_adds_ratio|ported_tn_adds|rate_center_npa_industry_model_adds|
          |        2022-09-15|             |  0|              0|                            0|                             0|                0|                          0|                 0|                           0|                    0.0|                         0.0|                                     0.0|           0.0|                                0.0|
          |        2022-09-16|             |  0|              1|                            0|                             0|                1|                          1|                 1|                           1|                    1.0|                         1.0|                                     1.0|           1.0|                                0.0|
          |        2022-09-16|             |  0|              1|                            0|                             0|                1|                          1|                 1|                           1|                    1.0|                         1.0|                                     1.0|           2.0|                                0.0|
          |        2022-09-17|             |  0|              2|                            0|                             0|                2|                          2|                 2|                           2|                    2.0|                         2.0|                                     2.0|           3.0|                                0.0|
          |        2022-09-17|             |  0|              2|                            0|                             0|                2|                          2|                 2|                           2|                    2.0|                         2.0|                                     2.0|           4.0|                                0.0|
        """
      }

      val result = RegionalMNOUpscale.apply()(spark).computeRateCenterNPATotalAddsWithRegionalMNOs(rateCenter, npaComplex)

      assertDatasetEquals(expectedData, result)
    }
  }
}
