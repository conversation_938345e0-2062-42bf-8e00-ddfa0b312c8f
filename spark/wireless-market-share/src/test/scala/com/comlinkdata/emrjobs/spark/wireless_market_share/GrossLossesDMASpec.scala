package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share._
import org.apache.spark.sql.types.{StructField, StructType}
import org.apache.spark.sql.DataFrame
import java.time.LocalDate


class GrossLossesDMASpec extends CldSparkBaseSpec {

  def setNullableStateForAllColumns(df: DataFrame, nullable: Boolean): DataFrame = {
    // get schema
    val schema = df.schema
    // modify [[StructField] with name `cn`
    val newSchema = StructType(schema.map {
      case StructField(c, t, _, m) ⇒ StructField(c, t, nullable = nullable, m)
    })
    // apply new schema
    df.sqlContext.createDataFrame(df.rdd, newSchema)
  }

  describe("Monthly National Average Gross Loss Rate") {
    import spark.implicits._
    it("schema") {
      val processingDate = LocalDate.of(2022, 6, 1)

      //AVG quarterly GA rate(Q3 23 GL / Q3 22 subs) * monthly losses(August 23 ported losses) / total quarterly losses
      val industryModelData = dataset[IndustryModel] {
        """
          |brand|plan_type|year|quarter|gross_additions|gross_losses|subscribers|ba |
          |a    |1        |2022|1      |90             |90          |100        |5  |
          |b    |1        |2022|1      |180             |180          |100        |-7 |
          |a    |1        |2022|2      |180             |180          |100        |10 |
          |b    |1        |2022|2      |270             |270          |100        |-12|
      """
      }.as[IndustryModel]

      val portedCarrierLosses = dataset[PortedCarrierLosses] {
        """
          |date_trunc |loser          |primary_plan_type_id|secondary_plan_type_id|dma|dma_name|switches|
          |2022-04-01 |a              |1                   |1                     |10 |aaa     |30.0    |
          |2022-05-01 |a              |1                   |1                     |20 |aaa     |30.0    |
          |2022-06-01 |a              |1                   |1                     |10 |aaa     |20.0    |
          |2022-06-01 |a              |1                   |1                     |20 |aaa     |10.0    |
          |2022-04-01 |b              |1                   |1                     |10 |aaa     |30.0    |
          |2022-05-01 |b              |1                   |1                     |20 |aaa     |30.0    |
          |2022-06-01 |b              |1                   |1                     |10 |aaa     |20.0    |
          |2022-06-01 |b              |1                   |1                     |20 |aaa     |10.0    |
    """
      }.as[PortedCarrierLosses]

      val expectedOutput = setNullableStateForAllColumns(dataframe[GrossAddsRateAverage] {
        """
          |brand         |plan_type|customer_base_date|gross_rate_average|
          |a             |1        |2022-06-01        |0.6               |
          |b             |1        |2022-06-01        |0.9               |
      """
      }, true).as[GrossAddsRateAverage]

      val actualOutput = setNullableStateForAllColumns(GrossLossesDMA().computeMonthlyNationalAverageGrossLossRate(processingDate, industryModelData, portedCarrierLosses).toDF(), true).as[GrossAddsRateAverage]

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

  describe("Flat Gross Losses") {
    import spark.implicits._
    it("output") {
      val processingDate = LocalDate.of(2022, 6, 1)
      val estimatedSubsInput = dataset[SubscribersOutput] {
        """
          |brand         |plan_type|customer_base_date|dma|subs|
          |AT&T Wireless |1        |2022-07-01        |10 |10                |
          |AT&T Wireless |1        |2022-08-01        |20 |10                |
          |AT&T Wireless |1        |2022-09-01        |30 |10                |
          |Altice        |1        |2022-10-01        |10 |10                |
          |Altice        |1        |2022-11-01        |20 |10                |
          |Altice        |1        |2022-12-01        |30 |10                |
          |Boost         |1        |2022-10-01        |10 |10                |
          |Boost         |1        |2022-11-01        |20 |10                |
        """
      }.as[SubscribersOutput]
      val grossLossesRateAverage = dataset[GrossLossesRateAverage] {
        """
          |brand         |plan_type|customer_base_date|gross_rate_average|
          |AT&T Wireless |1        |2022-08-01        |0.1               |
          |AT&T Wireless |1        |2022-09-01        |0.2               |
          |AT&T Wireless |1        |2022-07-01        |0.3               |
          |Altice        |1        |2022-10-01        |0.1               |
          |Altice        |1        |2022-11-01        |0.2               |
          |Altice        |1        |2022-12-01        |0.3               |
          |Boost         |1        |2022-10-01        |0.1               |
          |Boost         |1        |2022-11-01        |0.2               |
              """
      }.as[GrossLossesRateAverage]
      val expectedOutput = setNullableStateForAllColumns(dataframe[GrossLossesFlatDMA] {
        """
          |brand         |plan_type|customer_base_date|dma|gross_losses_flat   |
          |AT&T Wireless |1        |2022-10-01        |10 |1.0               |
          |AT&T Wireless |1        |2022-11-01        |20 |2.0               |
          |AT&T Wireless |1        |2022-12-01        |30 |3.0               |
          |Altice        |1        |2022-10-01        |10 |1.0               |
          |Altice        |1        |2022-11-01        |20 |2.0               |
          |Altice        |1        |2022-12-01        |30 |3.0               |
          |Boost         |1        |2022-10-01        |10 |1.0               |
          |Boost         |1        |2022-11-01        |20 |2.0               |
            """
      },true).as[GrossLossesFlatDMA]

      val actualOutput = setNullableStateForAllColumns(GrossLossesDMA().computeFlatGrossLosses(processingDate, estimatedSubsInput, grossLossesRateAverage).toDF(),true)

      // Count rows should be same
//      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
//      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }
  describe("Geographic Share of Carrier Flat Gross Losses") {
    import spark.implicits._
    it("output") {
      val grossLossesFlatInput = dataset[GrossLossesFlatDMA] {
        """
          |brand         |plan_type|customer_base_date|dma|gross_losses_flat   |
          |AT&T Wireless |1        |2022-10-01        |10 |2.0               |
          |AT&T Wireless |1        |2022-10-01        |20 |3.0               |
          |AT&T Wireless |1        |2022-10-01        |30 |5.0               |
          |Altice        |1        |2022-10-01        |10 |2.0               |
          |Altice        |1        |2022-10-01        |20 |3.0               |
          |Altice        |1        |2022-10-01        |30 |5.0               |
          |Boost         |1        |2022-10-01        |10 |1.0               |
          |Boost         |1        |2022-10-01        |20 |4.0               |
      """
      }.as[GrossLossesFlatDMA]
      val expectedOutput = setNullableStateForAllColumns(dataframe[GrossLossesAverageFlatShareDMA] {
        """
          |brand         |plan_type|customer_base_date|dma|gross_average_flat_share   |
          |AT&T Wireless |1        |2022-10-01        |10 |0.2               |
          |AT&T Wireless |1        |2022-10-01        |20 |0.3               |
          |AT&T Wireless |1        |2022-10-01        |30 |0.5               |
          |Altice        |1        |2022-10-01        |10 |0.2               |
          |Altice        |1        |2022-10-01        |20 |0.3               |
          |Altice        |1        |2022-10-01        |30 |0.5               |
          |Boost         |1        |2022-10-01        |10 |0.2               |
          |Boost         |1        |2022-10-01        |20 |0.8               |
            """
      },true).as[GrossLossesAverageFlatShareDMA]
      val actualOutput = setNullableStateForAllColumns(GrossLossesDMA().computeFlatGrossLossAverageFlatShare(grossLossesFlatInput).toDF(),true)

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }
  describe("Ported Geographic Share of Carrier Losses") {
    import spark.implicits._
    it("output") {
      val portedCarrierLosses = dataset[PortedCarrierLosses] {
        """
          |date_trunc |loser          |primary_plan_type_id|secondary_plan_type_id|dma|dma_name|switches|
          |2022-10-01 |AT&T Prepaid   |1                   |1                     |10 |aaa     |10.0    |
          |2022-10-01 |AT&T Prepaid   |1                   |1                     |20 |aaa     |40.0    |
          |2022-10-01 |AT&T Prepaid   |1                   |1                     |30 |aaa     |50.0    |
          |2022-10-01 |Altice Prepaid |1                   |1                     |10 |aaa     |10.0    |
          |2022-10-01 |Altice Prepaid |1                   |1                     |20 |aaa     |40.0    |
          |2022-10-01 |Altice Prepaid |1                   |1                     |30 |aaa     |50.0    |
          |2022-10-01 |Boost Prepaid  |1                   |1                     |10 |aaa     |20.0    |
          |2022-10-01 |Boost Prepaid  |1                   |1                     |20 |aaa     |80.0    |
  """
      }.as[PortedCarrierLosses]
      val expectedOutput = setNullableStateForAllColumns(dataframe[PortedGeographicLossShareDMA] {
        """
          |brand          |plan_type|customer_base_date|dma|ported_loss_shares |
          |AT&T Prepaid   |1        |2022-10-01        |10 |0.1    |
          |AT&T Prepaid   |1        |2022-10-01        |20 |0.4    |
          |AT&T Prepaid   |1        |2022-10-01        |30 |0.5    |
          |Altice Prepaid |1        |2022-10-01        |10 |0.1    |
          |Altice Prepaid |1        |2022-10-01        |20 |0.4    |
          |Altice Prepaid |1        |2022-10-01        |30 |0.5    |
          |Boost Prepaid  |1        |2022-10-01        |10 |0.2    |
          |Boost Prepaid  |1        |2022-10-01        |20 |0.8    |
"""
      },true).as[PortedGeographicLossShareDMA]
      val actualOutput = setNullableStateForAllColumns(GrossLossesDMA().computePortedCarrierLossGeoShare(portedCarrierLosses).toDF(),true)

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

  describe("Compute Estimated Gross Losses") {
    import spark.implicits._
    it("output") {

      val flatGrossLossesGeoShare = dataset[GrossLossesAverageFlatShareDMA] {
        """
          |brand         |plan_type|customer_base_date|dma|gross_average_flat_share|
          |AT&T Wireless |1        |2022-03-01        |10 |1.0               |
          |AT&T Wireless |1        |2022-03-01        |20 |2.0               |
          |AT&T Wireless |1        |2022-03-01        |30 |3.0               |
          |Altice        |1        |2022-03-01        |10 |1.0               |
          |Altice        |1        |2022-03-01        |20 |2.0               |
          |Altice        |1        |2022-03-01        |30 |3.0               |
          |Boost         |1        |2022-03-01        |10 |1.0               |
          |Boost         |1        |2022-03-01        |20 |2.0               |
        """
      }.as[GrossLossesAverageFlatShareDMA]

      val portedCarrierLossGeoShare = dataset[PortedGeographicLossShareDMA] {
        """
          |brand          |plan_type|customer_base_date|dma|ported_loss_shares|
          |AT&T Wireless  |1        |2022-03-01        |10 |0.1               |
          |AT&T Wireless  |1        |2022-03-01        |20 |0.4               |
          |AT&T Wireless  |1        |2022-03-01        |30 |0.5               |
          |Altice         |1        |2022-03-01        |10 |0.1               |
          |Altice         |1        |2022-03-01        |20 |0.4               |
          |Altice         |1        |2022-03-01        |30 |0.5               |
          |Boost          |1        |2022-03-01        |10 |0.2               |
          |Boost          |1        |2022-03-01        |20 |0.8               |
        """
      }

      val grossLossesFlatData = setNullableStateForAllColumns(dataframe[GrossLossesFlatDMA] {
        """
          |brand         |plan_type|customer_base_date|dma|gross_losses_flat|
          |AT&T Wireless |1        |2022-03-01        |10 |1000.0           |
          |AT&T Wireless |1        |2022-06-01        |20 |1000.0           |
          |AT&T Wireless |1        |2022-09-01        |30 |1000.0           |
          |Altice        |1        |2022-03-01        |10 |1000.0           |
          |Altice        |1        |2022-06-01        |20 |1000.0           |
          |Altice        |1        |2022-09-01        |30 |1000.0           |
          |Boost         |1        |2022-03-01        |10 |1000.0           |
          |Boost         |1        |2022-03-01        |20 |1000.0           |
            """
      }, true).as[GrossLossesFlatDMA]

      val expectedOutput = setNullableStateForAllColumns(dataframe[EstimatedGrossLossesDMA] {
        """
          |brand        |plan_type|customer_base_date|dma|estimated_gross_losses|
          |AT&T Wireless|        1|        2022-03-01| 30|              1750.0|
          |AT&T Wireless|        1|        2022-03-01| 20|              1200.0|
          |AT&T Wireless|        1|        2022-03-01| 10|               550.0|
          |AT&T Wireless|        1|        2022-06-01|  0|                 0.0|
          |AT&T Wireless|        1|        2022-09-01|  0|                 0.0|
          |       Altice|        1|        2022-03-01| 30|              1750.0|
          |       Altice|        1|        2022-03-01| 20|              1200.0|
          |       Altice|        1|        2022-03-01| 10|               550.0|
          |       Altice|        1|        2022-06-01|  0|                 0.0|
          |       Altice|        1|        2022-09-01|  0|                 0.0|
          |        Boost|        1|        2022-03-01| 20|              2800.0|
          |        Boost|        1|        2022-03-01| 10|              1200.0|
        """
      }, true)


      val actualOutput = setNullableStateForAllColumns(GrossLossesDMA().computeEstimatedGrossLosses(
        flatGrossLossesGeoShare, portedCarrierLossGeoShare, grossLossesFlatData
      ).toDF(), true)

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }
}
