package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share.{MonthlyOutputWithEthnicityVPGM, SubscribersByEthnicityAgeIncomeVPGM, WCVFlowsVPGM, WirelessMovementWideGenericVPGM, WmsVPGM}
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.{DataFrame, Dataset}
import org.apache.spark.sql.types.{StructField, StructType}

import java.net.URI
import java.time.LocalDate


class DemographicsUtilsVPGMSpec extends CldSparkBaseSpec {

  def setNullableStateForAllColumns(df: DataFrame, nullable: Boolean): DataFrame = {
    // get schema
    val schema = df.schema
    // modify [[Struct<PERSON>ield] with name `cn`
    val newSchema = StructType(schema.map {
      case StructField(c, t, _, m) ⇒ <PERSON>ruct<PERSON>ield(c, t, nullable = nullable, m)
    })
    // apply new schema
    df.sqlContext.createDataFrame(df.rdd, newSchema)
  }

  describe("calculateSubEthnicityPercentOfCarrier") {
    import spark.implicits._
    it("test") {
      val subscribersWithEthnicityData = dataset[SubscribersByEthnicityAgeIncomeVPGM] {
        """
          |date      |vpgm|winner_brand_plantype|ethnicity|age|income|subscribers|
          |2022-06-01|500|AT&T Postpaid|Black|18-34|$50-100K|221|
          |2022-06-01|500|Cricket Postpaid|Hispanic|18-34|GT 150k|78|
          |2022-06-01|501|AT&T Postpaid|White|18-34|$50-100K|13381|
          |2022-06-01|501|T-Mobile Postpaid|Asian|55-74|GT 150k|136|
          |2022-06-01|502|T-Mobile Postpaid|Black|55-74|$50-100K|560|
          |2022-06-01|503|Verizon Postpaid|Asian|18-34|GT 150k|46|
          |2022-06-01|501|Sprint Postpaid|Hispanic|55-74|100-150k|722|
          |2022-06-01|500|Verizon Postpaid|White|75 +|100-150k|5829|
          |2022-06-01|503|Cricket Postpaid|Asian|75 +|100-150k|4907|
          """
      }

      val expected = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrierVPGM] {
        """
          |date      |vpgm|winner_brand_plantype|ethnicity|age|income|subscribers|total_subscribers|ethnicity_pct_of_carrier|
          |2022-06-01|500|AT&T Postpaid|Black|18-34|$50-100K|221.0|221.0|1.0|
          |2022-06-01|500|Cricket Postpaid|Hispanic|18-34|GT 150k|78.0|78.0|1.0|
          |2022-06-01|500|Verizon Postpaid|White|75 +|100-150k|5829.0|5829.0|1.0|
          |2022-06-01|501|AT&T Postpaid|White|18-34|$50-100K|13381.0|13381.0|1.0|
          |2022-06-01|501|Sprint Postpaid|Hispanic|55-74|100-150k|722.0|722.0|1.0|
          |2022-06-01|501|T-Mobile Postpaid|Asian|55-74|GT 150k|136.0|136.0|1.0|
          |2022-06-01|502|T-Mobile Postpaid|Black|55-74|$50-100K|560.0|560.0|1.0|
          |2022-06-01|503|Cricket Postpaid|Asian|75 +|100-150k|4907.0|4907.0|1.0|
          |2022-06-01|503|Verizon Postpaid|Asian|18-34|GT 150k|46.0|46.0|1.0|
          """
      }, nullable = true).as[subsEthnicityPercentOfCarrierVPGM]

      val newDate = LocalDate.parse("2022-06-01")

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateSubEthnicityPercentOfCarrier(
        subscribersWithEthnicityData, newDate
      ).toDF(), nullable = true).as[subsEthnicityPercentOfCarrierVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateSubEthnicityPercentOfCarrierForwardInsertMonthly") {
    import spark.implicits._
    it("test") {
      val monthlyOutputWithEthnicityVPGMData = dataset[MonthlyOutputWithEthnicityVPGM] {
        """
          |wms_month |subs_join_month|losses_join_month|wins_join_month|vpgm|brand_plantype   |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|age  |income  |subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses     |winner_from_average_ethnicity_pct_of_carrier|gross_adds       |base_adjustment   |starting_subscribers|gross_add_rate   |churn_rate       |overall_gross_add_rate|overall_churn_rate|month     |
          |2022-05-01|2022-05-01     |2022-05-01       |2022-05-01     |500 |AT&T Postpaid    |112931           |1595            |722               |-58                  |112117                |113887                |Asian    |35-54|GT 150k |33                     |0.000292221592519            |33.0008766647776  |0.00026858006075              |0.193914803861373|0.000256319760791                           |0.408830018460894|-0.014866546125851|32.8008279963039    |0.012464015192146|0.005911887464646|0.014226210119786     |0.006439701383376 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500 |AT&T Postpaid    |112931           |1595            |722               |-58                  |112117                |113887                |Asian    |35-54|GT 150k |33                     |0.000292221592519            |33.0008766647776  |0.00026858006075              |0.193914803861373|0.000256319760791                           |0.408830018460894|-0.014866546125851|32.8008279963039    |0.012464015192146|0.005911887464646|0.014226210119786     |0.006439701383376 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500 |T-Mobile Postpaid|112931           |1595            |722               |-58                  |112117                |113887                |Black    |55-74|GT 150k |161                    |0.001425687163502            |161.004277061491  |0.001459632432233             |1.05385461607209 |0.001249051689792                           |1.99223744521793 |-0.072444998007925|160.138339230353    |0.012440727528416|0.006580901370259|0.014226210119786     |0.006439701383376 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500 |Verizon Postpaid |325881           |2500            |2756              |-5                   |326142                |325732                |Asian    |75 + |100-150k|100                    |0.000306862363024            |100.000613724726  |0.000290937850773             |0.801824716730904|0.000338413472305                           |0.846033680761643|-0.001692067361523|99.9580968280568    |0.008463883443249|0.008021608475701|0.007665372751746     |0.008450306921525 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500 |Sprint Postpaid  |163561           |2145            |1467              |-21                  |162904                |164172                |Black    |18-34|$50-100K|221                    |0.001351186109073            |221.001351186109  |0.001298407861585             |1.90476433294464 |0.001411096538191                           |3.02680207441893 |-0.029633027302004|219.908946471937    |0.013763887840758|0.008661604557265|0.013167264155576     |0.00900530373717  |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |501 |AT&T Postpaid    |3790286          |40833           |33598             |-475                 |3783527               |3796149               |Asian    |18-34|$50-100K|33118                  |0.00873760383127             |33118.0174752077  |0.008570801158171             |287.961777312214 |0.008046974525438                           |328.582110797228 |-3.82231289958326 |33081.2194546222    |0.00993258761963 |0.008704690518051|0.010792310983905     |0.008880074068455 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |501 |T-Mobile Postpaid|5662625          |64346           |47850             |-5516                |5651645               |5682695               |Hispanic |35-54|$50-100K|154906                 |0.027355849616115            |154905.917932451  |0.02776122454973              |1328.37459470457 |0.026843094809806                           |1727.24577863179 |-148.066510970891 |154655.113259495    |0.01116837162528 |0.008589270452867|0.011385357714435     |0.008466561505544 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |501 |Verizon Postpaid |3790286          |40833           |33598             |-475                 |3783527               |3796149               |Black    |75 + |GT 150k |24906                  |0.00657101156536             |24906.0131420231  |0.007060121039448             |237.205946683368 |0.006303180605968                           |257.377773683475 |-2.99401078783461 |24888.8353258109    |0.010341093519011|0.009530616582825|0.010792310983905     |0.008880074068455 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |502 |Verizon Postpaid |95045            |642             |840               |-2                   |95245                 |94825                 |Black    |35-54|100-150k|44                     |0.000462928866771            |43.9990741422665  |0.000453481489009             |0.380924450767509|0.000487633298596                           |0.313060577698733|-0.000975266597192|44.0679132819324    |0.007104048147138|0.008644031958819|0.00674051131293      |0.008819360596357 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |502 |AT&T Postpaid    |64582            |801             |497               |-6                   |64283                 |64873                 |Hispanic |35-54|LT $50K |217                    |0.003360173428306            |217.006720346857  |0.003279263062106             |1.62979374186652 |0.003420865352557                           |2.74011314739835 |-0.020525192115344|215.91692613344     |0.012690589832244|0.007548244461665|0.012460526111103     |0.007731437549585 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |502 |AT&T Postpaid    |64582            |801             |497               |-6                   |64283                 |64873                 |Hispanic |35-54|LT $50K |217                    |0.003360173428306            |217.006720346857  |0.003279263062106             |1.62979374186652 |0.003420865352557                           |2.74011314739835 |-0.020525192115344|215.91692613344     |0.012690589832244|0.007548244461665|0.012460526111103     |0.007731437549585 |2022-06-01|
        """
      }.as[MonthlyOutputWithEthnicityVPGM]

      val expected = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrierVPGM] {
        """
          |date      |vpgm|winner_brand_plantype|ethnicity|age  |income  |subscribers     |total_subscribers|ethnicity_pct_of_carrier|
          |2022-07-01|500 |AT&T Postpaid        |Asian    |35-54|GT 150k |33.0008766647776|33.0008766647776 |1.0                     |
          |2022-07-01|500 |Sprint Postpaid      |Black    |18-34|$50-100K|221.001351186109|221.001351186109 |1.0                     |
          |2022-07-01|500 |T-Mobile Postpaid    |Black    |55-74|GT 150k |161.004277061491|161.004277061491 |1.0                     |
          |2022-07-01|500 |Verizon Postpaid     |Asian    |75 + |100-150k|100.000613724726|100.000613724726 |1.0                     |
          |2022-07-01|501 |AT&T Postpaid        |Asian    |18-34|$50-100K|33118.0174752077|33118.0174752077 |1.0                     |
          |2022-07-01|501 |T-Mobile Postpaid    |Hispanic |35-54|$50-100K|154905.917932451|154905.917932451 |1.0                     |
          |2022-07-01|501 |Verizon Postpaid     |Black    |75 + |GT 150k |24906.0131420231|24906.0131420231 |1.0                     |
          |2022-07-01|502 |AT&T Postpaid        |Hispanic |35-54|LT $50K |217.006720346857|434.013440693714 |0.5                     |
          |2022-07-01|502 |AT&T Postpaid        |Hispanic |35-54|LT $50K |217.006720346857|434.013440693714 |0.5                     |
          |2022-07-01|502 |Verizon Postpaid     |Black    |35-54|100-150k|43.9990741422665|43.9990741422665 |1.0                     |
        """
      }, nullable = true).as[subsEthnicityPercentOfCarrierVPGM]

      val newDate = LocalDate.parse("2022-07-01")

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateSubEthnicityPercentOfCarrierForwardInsertMonthly(
        monthlyOutputWithEthnicityVPGMData, newDate
      ).toDF(), nullable = true).as[subsEthnicityPercentOfCarrierVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateTotalLosses") {
    import spark.implicits._
    it("") {
      val wcvFlowsVPGMData = setNullableStateForAllColumns(dataframe[WCVFlowsVPGM] {
        """
          |month_date|vpgm|loser_brand_plantype|winner_brand_plantype|wins|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|10|
          |2017-08-01|500|AT&T_Postpaid Phone|Verizon_Postpaid Phone|50|
          |2019-05-01|500|AT&T_Postpaid Phone|Sprint_Postpaid Phone|20|
          |2019-05-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|50|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|70|
          """
      }, nullable = true).as[WCVFlowsVPGM]

      val expected = setNullableStateForAllColumns(dataframe[totalLossesVPGM] {
        """
          |date|vpgm|loser_brand_plantype|losses|
          |2017-08-01|500|AT&T_Postpaid Phone|60|
          |2019-05-01|500|AT&T_Postpaid Phone|140|
          """
      }, nullable = true).as[totalLossesVPGM]

      val result = setNullableStateForAllColumns(
        DemographicsUtilsVPGM().calculateTotalLosses(wcvFlowsVPGMData).toDF(), nullable = true).as[totalLossesVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }
  describe("calculateTotalWins") {
    import spark.implicits._
    it("test") {
      val wcvFlowsVPGMData = setNullableStateForAllColumns(dataframe[WCVFlowsVPGM] {
        """
          |month_date|vpgm|loser_brand_plantype|winner_brand_plantype|wins|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|10|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|50|
          |2019-05-01|500|AT&T_Postpaid Phone|Sprint_Postpaid Phone|20|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|50|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|70|
        """
      }, nullable = true).as[WCVFlowsVPGM]

      val expected = setNullableStateForAllColumns(dataframe[totalWinsVPGM] {
        """
          |date      |vpgm|winner_brand_plantype|wins |
          |2017-08-01|500|AT&T_Postpaid Phone  |60.0 |
          |2019-05-01|500|Sprint_Postpaid Phone|20.0 |
          |2019-05-01|500|Other_Postpaid Phone |120.0|
        """
      }, nullable = true).as[totalWinsVPGM]

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateTotalWins(
        wcvFlowsVPGMData).toDF(), nullable = true).as[totalWinsVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }
  describe("calculateLossesEthnicityPercentOfCarrier") {
    import spark.implicits._
    it("test") {

      val subsEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrierVPGM] {
        """
          |      date|vpgm|winner_brand_plantype|ethnicity|  age|  income|subscribers|total_subscribers|ethnicity_pct_of_carrier|
          |2022-06-01|500|        AT&T Postpaid|    Black|18-34|$50-100K|      221.0|            221.0|                     1.0|
          |2022-06-01|500|     Cricket Postpaid| Hispanic|18-34| GT 150k|       78.0|             78.0|                     1.0|
          |2022-06-01|500|     Verizon Postpaid|    White| 75 +|100-150k|     5829.0|           5829.0|                     1.0|
          |2022-06-01|501|        AT&T Postpaid|    White|18-34|$50-100K|    13381.0|          13381.0|                     1.0|
          |2022-06-01|501|      Sprint Postpaid| Hispanic|55-74|100-150k|      722.0|            722.0|                     1.0|
          |2022-06-01|501|    T-Mobile Postpaid|    Asian|55-74| GT 150k|      136.0|            136.0|                     1.0|
          |2022-06-01|502|    T-Mobile Postpaid|    Black|55-74|$50-100K|      560.0|            560.0|                     1.0|
          |2022-06-01|503|     Cricket Postpaid|    Asian| 75 +|100-150k|     4907.0|           4907.0|                     1.0|
          |2022-06-01|503|     Verizon Postpaid|    Asian|18-34| GT 150k|       46.0|             46.0|                     1.0|
        """
      }, nullable = true).as[subsEthnicityPercentOfCarrierVPGM]

      val totalLossesData = setNullableStateForAllColumns(dataframe[totalLossesVPGM] {
        """
          |date     |vpgm|loser_brand_plantype|losses|
          |2022-06-01|505|Verizon Postpaid    |40.0  |
          |2022-06-01|501|AT&T Postpaid       |12.0  |
          |2022-06-01|500|AT&T Postpaid       |6.0   |
          |2022-06-01|502|T-Mobile Postpaid   |6.0   |
          |2022-06-01|503|Sprint Postpaid     |17.0  |
          |2022-06-01|502|Verizon Postpaid    |2.0   |
          |2022-06-01|504|Cricket Prepaid     |20.0  |
          |2022-06-01|501|Verizon Postpaid    |1250.0|
          |2022-06-01|503|T-Mobile Postpaid   |74.0  |
        """
      }, nullable = true).as[totalLossesVPGM]

      val expected = setNullableStateForAllColumns(dataframe[lossesEthnicityPercentOfCarrierVPGM] {
        """
          |date     |vpgm|ethnicity|age  |income  |loser_brand_plantype|losses_total|loser_ethnicity_pct_of_carrier|losses_by_ethnicity|
          |null      |501|Hispanic |55-74|100-150k|Sprint Postpaid     |null        |1.0                           |null               |
          |null      |503|Asian    |75 + |100-150k|Cricket Postpaid    |null        |1.0                           |null               |
          |null      |501|Asian    |55-74|GT 150k |T-Mobile Postpaid   |null        |1.0                           |null               |
          |2022-06-01|502|Black    |55-74|$50-100K|T-Mobile Postpaid   |6.0         |1.0                           |6.0                |
          |null      |503|Asian    |18-34|GT 150k |Verizon Postpaid    |null        |1.0                           |null               |
          |2022-06-01|501|White    |18-34|$50-100K|AT&T Postpaid       |12.0        |1.0                           |12.0               |
          |null      |500|White    |75 + |100-150k|Verizon Postpaid    |null        |1.0                           |null               |
          |null      |500|Hispanic |18-34|GT 150k |Cricket Postpaid    |null        |1.0                           |null               |
          |2022-06-01|500|Black    |18-34|$50-100K|AT&T Postpaid       |6.0         |1.0                           |6.0                |
        """
      }, nullable = true).as[lossesEthnicityPercentOfCarrierVPGM]

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateLossesEthnicityPercentOfCarrier(
        subsEthnicityPercentOfCarrierData, totalLossesData
      ).toDF(), nullable = true).as[lossesEthnicityPercentOfCarrierVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())

    }
  }

  describe("calculateWinsEthnicityPercentOfCarrier") {
    import spark.implicits._
    it("test") {
      val subsEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrierVPGM] {
        """
          |      date|vpgm|winner_brand_plantype|ethnicity|  age|  income|subscribers|total_subscribers|ethnicity_pct_of_carrier|
          |2022-06-01|500|        AT&T Postpaid|    Black|18-34|$50-100K|      221.0|            221.0|                     1.0|
          |2022-06-01|500|     Cricket Postpaid| Hispanic|18-34| GT 150k|       78.0|             78.0|                     1.0|
          |2022-06-01|500|     Verizon Postpaid|    White| 75 +|100-150k|     5829.0|           5829.0|                     1.0|
          |2022-06-01|501|        AT&T Postpaid|    White|18-34|$50-100K|    13381.0|          13381.0|                     1.0|
          |2022-06-01|501|      Sprint Postpaid| Hispanic|55-74|100-150k|      722.0|            722.0|                     1.0|
          |2022-06-01|501|    T-Mobile Postpaid|    Asian|55-74| GT 150k|      136.0|            136.0|                     1.0|
          |2022-06-01|502|    T-Mobile Postpaid|    Black|55-74|$50-100K|      560.0|            560.0|                     1.0|
          |2022-06-01|503|     Cricket Postpaid|    Asian| 75 +|100-150k|     4907.0|           4907.0|                     1.0|
          |2022-06-01|503|     Verizon Postpaid|    Asian|18-34| GT 150k|       46.0|             46.0|                     1.0|
        """
      }, nullable = true).as[subsEthnicityPercentOfCarrierVPGM]

      val totalWinsData = setNullableStateForAllColumns(dataframe[totalWinsVPGM] {
        """
          |date      |vpgm|winner_brand_plantype|wins  |
          |2022-06-01|503|T-Mobile Postpaid    |74.0  |
          |2022-06-01|502|Verizon Postpaid     |2.0   |
          |2022-06-01|503|Sprint Postpaid      |17.0  |
          |2022-06-01|504|Cricket Prepaid      |20.0  |
          |2022-06-01|502|T-Mobile Postpaid    |6.0   |
          |2022-06-01|500|AT&T Postpaid        |6.0   |
          |2022-06-01|505|Verizon Postpaid     |40.0  |
          |2022-06-01|501|AT&T Postpaid        |12.0  |
          |2022-06-01|501|Verizon Postpaid     |1250.0|
        """
      }, nullable = true).as[totalWinsVPGM]

      val expected = setNullableStateForAllColumns(dataframe[winsEthnicityPercentOfCarrierVPGM] {
        """
          |date      |vpgm|ethnicity|age  |income  |winner_brand_plantype|wins_total |winner_ethnicity_pct_of_carrier| wins_by_ethnicity|
          |null      |501|Hispanic |55-74|100-150k|Sprint Postpaid     |null        |1.0                           |null               |
          |null      |503|Asian    |75 + |100-150k|Cricket Postpaid    |null        |1.0                           |null               |
          |null      |501|Asian    |55-74|GT 150k |T-Mobile Postpaid   |null        |1.0                           |null               |
          |2022-06-01|502|Black    |55-74|$50-100K|T-Mobile Postpaid   |6.0         |1.0                           |6.0                |
          |null      |503|Asian    |18-34|GT 150k |Verizon Postpaid    |null        |1.0                           |null               |
          |2022-06-01|501|White    |18-34|$50-100K|AT&T Postpaid       |12.0        |1.0                           |12.0               |
          |null      |500|White    |75 + |100-150k|Verizon Postpaid    |null        |1.0                           |null               |
          |null      |500|Hispanic |18-34|GT 150k |Cricket Postpaid    |null        |1.0                           |null               |
          |2022-06-01|500|Black    |18-34|$50-100K|AT&T Postpaid       |6.0         |1.0                           |6.0                |
          """
      }, nullable = true).as[winsEthnicityPercentOfCarrierVPGM]

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateWinsEthnicityPercentOfCarrier(
        subsEthnicityPercentOfCarrierData, totalWinsData
      ).toDF(), nullable = true).as[winsEthnicityPercentOfCarrierVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateWinnerPercentOfLoserCreate") {
    import spark.implicits._
    it("test") {
      val wcvFlowsVPGMData = setNullableStateForAllColumns(dataframe[WCVFlowsVPGM] {
        """
          |month_date|vpgm|loser_brand_plantype|winner_brand_plantype|wins|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|10|
          |2017-08-01|500|AT&T_Postpaid Phone|Verizon_Postpaid Phone|90|
          |2019-05-01|500|AT&T_Postpaid Phone|Sprint_Postpaid Phone|20|
          |2019-05-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|30|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|50|
          """
      }, nullable = true).as[WCVFlowsVPGM]

      val expected = setNullableStateForAllColumns(dataframe[winnerPercentOfLoserVPGM] {
        """
          |      date|vpgm|loser_brand_plantype|winner_brand_plantype|wins|total_losses|winner_pct_of_loser|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|10|100|0.1|
          |2017-08-01|500|AT&T_Postpaid Phone|Verizon_Postpaid Phone|90|100|0.9|
          |2019-05-01|500|AT&T_Postpaid Phone|Sprint_Postpaid Phone|20|100|0.2|
          |2019-05-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|30|100|0.3|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|50|100|0.5|
          """
      }, nullable = true).as[winnerPercentOfLoserVPGM]

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateWinnerPercentOfLoserCreate(
        wcvFlowsVPGMData
      ).toDF(), nullable = true).as[winnerPercentOfLoserVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateSubEthnicityPercentOfCarrierBackwardInsertMonthly") {
    import spark.implicits._
    it("test") {
      val monthlyOutputWithEthnicityVPGMData = dataset[MonthlyOutputWithEthnicityVPGM] {
        """
          |wms_month |subs_join_month|losses_join_month|wins_join_month|vpgm|brand_plantype   |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|age  |income  |subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses     |winner_from_average_ethnicity_pct_of_carrier|gross_adds       |base_adjustment   |starting_subscribers|gross_add_rate   |churn_rate       |overall_gross_add_rate|overall_churn_rate|month     |
          |2022-05-01|2022-05-01     |2022-05-01       |2022-05-01     |500 |AT&T Postpaid    |112931           |1595            |722               |-58                  |112117                |113887                |Asian    |35-54|GT 150k |33                     |0.000292221592519            |33.0008766647776  |0.00026858006075              |0.193914803861373|0.000256319760791                           |0.408830018460894|-0.014866546125851|32.8008279963039    |0.012464015192146|0.005911887464646|0.014226210119786     |0.006439701383376 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500 |AT&T Postpaid    |112931           |1595            |722               |-58                  |112117                |113887                |Asian    |35-54|GT 150k |33                     |0.000292221592519            |33.0008766647776  |0.00026858006075              |0.193914803861373|0.000256319760791                           |0.408830018460894|-0.014866546125851|32.8008279963039    |0.012464015192146|0.005911887464646|0.014226210119786     |0.006439701383376 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500 |T-Mobile Postpaid|112931           |1595            |722               |-58                  |112117                |113887                |Black    |55-74|GT 150k |161                    |0.001425687163502            |161.004277061491  |0.001459632432233             |1.05385461607209 |0.001249051689792                           |1.99223744521793 |-0.072444998007925|160.138339230353    |0.012440727528416|0.006580901370259|0.014226210119786     |0.006439701383376 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500 |Verizon Postpaid |325881           |2500            |2756              |-5                   |326142                |325732                |Asian    |75 + |100-150k|100                    |0.000306862363024            |100.000613724726  |0.000290937850773             |0.801824716730904|0.000338413472305                           |0.846033680761643|-0.001692067361523|99.9580968280568    |0.008463883443249|0.008021608475701|0.007665372751746     |0.008450306921525 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500 |Sprint Postpaid  |163561           |2145            |1467              |-21                  |162904                |164172                |Black    |18-34|$50-100K|221                    |0.001351186109073            |221.001351186109  |0.001298407861585             |1.90476433294464 |0.001411096538191                           |3.02680207441893 |-0.029633027302004|219.908946471937    |0.013763887840758|0.008661604557265|0.013167264155576     |0.00900530373717  |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |501 |AT&T Postpaid    |3790286          |40833           |33598             |-475                 |3783527               |3796149               |Asian    |18-34|$50-100K|33118                  |0.00873760383127             |33118.0174752077  |0.008570801158171             |287.961777312214 |0.008046974525438                           |328.582110797228 |-3.82231289958326 |33081.2194546222    |0.00993258761963 |0.008704690518051|0.010792310983905     |0.008880074068455 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |501 |T-Mobile Postpaid|5662625          |64346           |47850             |-5516                |5651645               |5682695               |Hispanic |35-54|$50-100K|154906                 |0.027355849616115            |154905.917932451  |0.02776122454973              |1328.37459470457 |0.026843094809806                           |1727.24577863179 |-148.066510970891 |154655.113259495    |0.01116837162528 |0.008589270452867|0.011385357714435     |0.008466561505544 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |501 |Verizon Postpaid |3790286          |40833           |33598             |-475                 |3783527               |3796149               |Black    |75 + |GT 150k |24906                  |0.00657101156536             |24906.0131420231  |0.007060121039448             |237.205946683368 |0.006303180605968                           |257.377773683475 |-2.99401078783461 |24888.8353258109    |0.010341093519011|0.009530616582825|0.010792310983905     |0.008880074068455 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |502 |Verizon Postpaid |95045            |642             |840               |-2                   |95245                 |94825                 |Black    |35-54|100-150k|44                     |0.000462928866771            |43.9990741422665  |0.000453481489009             |0.380924450767509|0.000487633298596                           |0.313060577698733|-0.000975266597192|44.0679132819324    |0.007104048147138|0.008644031958819|0.00674051131293      |0.008819360596357 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |502 |AT&T Postpaid    |64582            |801             |497               |-6                   |64283                 |64873                 |Hispanic |35-54|LT $50K |217                    |0.003360173428306            |217.006720346857  |0.003279263062106             |1.62979374186652 |0.003420865352557                           |2.74011314739835 |-0.020525192115344|215.91692613344     |0.012690589832244|0.007548244461665|0.012460526111103     |0.007731437549585 |2022-06-01|
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |502 |AT&T Postpaid    |64582            |801             |497               |-6                   |64283                 |64873                 |Hispanic |35-54|LT $50K |217                    |0.003360173428306            |217.006720346857  |0.003279263062106             |1.62979374186652 |0.003420865352557                           |2.74011314739835 |-0.020525192115344|215.91692613344     |0.012690589832244|0.007548244461665|0.012460526111103     |0.007731437549585 |2022-06-01|
          """
      }.as[MonthlyOutputWithEthnicityVPGM]
      val expected = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrierVPGM] {
        """
          |date      |vpgm|winner_brand_plantype|ethnicity|age  |income  |subscribers     |total_subscribers|ethnicity_pct_of_carrier|
          |2022-05-01|500|AT&T Postpaid        |Asian    |35-54|GT 150k |32.8008279963039|32.8008279963039 |1.0                     |
          |2022-05-01|500|Sprint Postpaid      |Black    |18-34|$50-100K|219.908946471937|219.908946471937 |1.0                     |
          |2022-05-01|500|T-Mobile Postpaid    |Black    |55-74|GT 150k |160.138339230353|160.138339230353 |1.0                     |
          |2022-05-01|500|Verizon Postpaid     |Asian    |75 + |100-150k|99.9580968280568|99.9580968280568 |1.0                     |
          |2022-05-01|501|AT&T Postpaid        |Asian    |18-34|$50-100K|33081.2194546222|33081.2194546222 |1.0                     |
          |2022-05-01|501|T-Mobile Postpaid    |Hispanic |35-54|$50-100K|154655.113259495|154655.113259495 |1.0                     |
          |2022-05-01|501|Verizon Postpaid     |Black    |75 + |GT 150k |24888.8353258109|24888.8353258109 |1.0                     |
          |2022-05-01|502|AT&T Postpaid        |Hispanic |35-54|LT $50K |215.91692613344 |431.83385226688  |0.5                     |
          |2022-05-01|502|AT&T Postpaid        |Hispanic |35-54|LT $50K |215.91692613344 |431.83385226688  |0.5                     |
          |2022-05-01|502|Verizon Postpaid     |Black    |35-54|100-150k|44.0679132819324|44.0679132819324 |1.0                     |
          """
      }, nullable = true).as[subsEthnicityPercentOfCarrierVPGM]
      val newDate = LocalDate.parse("2022-05-01")
      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateSubEthnicityPercentOfCarrierBackwardInsertMonthly(
        monthlyOutputWithEthnicityVPGMData, newDate
      ).toDF(), nullable = true).as[subsEthnicityPercentOfCarrierVPGM]
      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateLoserPercentOfWinnerCreate") {
    import spark.implicits._
    it("test") {
      val wcvFlowsVPGMData = setNullableStateForAllColumns(dataframe[WCVFlowsVPGM] {
        """
          |month_date|vpgm|loser_brand_plantype|winner_brand_plantype|wins|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|10|
          |2017-08-01|500|AT&T_Postpaid Phone|Verizon_Postpaid Phone|90|
          |2019-05-01|500|AT&T_Postpaid Phone|Sprint_Postpaid Phone|20|
          |2019-05-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|30|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|50|
    """
      }, nullable = true).as[WCVFlowsVPGM]

      val expected = setNullableStateForAllColumns(dataframe[loserPercentOfWinnerVPGM] {
        """
          |      date|vpgm|loser_brand_plantype|winner_brand_plantype|wins|total_wins|loser_pct_of_winner|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|10|100|0.1|
          |2017-08-01|500|AT&T_Postpaid Phone|Verizon_Postpaid Phone|90|100|0.9|
          |2019-05-01|500|AT&T_Postpaid Phone|Sprint_Postpaid Phone|20|100|0.2|
          |2019-05-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|30|100|0.3|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|50|100|0.5|
    """
      }, nullable = true).as[loserPercentOfWinnerVPGM]

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateLoserPercentOfWinnerCreate(
        wcvFlowsVPGMData
      ).toDF(), nullable = true).as[loserPercentOfWinnerVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateWinsLossesByEthnicity") {
    import spark.implicits._

    it("interim1") {
      val lossesEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[lossesEthnicityPercentOfCarrierVPGM] {
        """
          |date      |vpgm|ethnicity|age  |income  |loser_brand_plantype  |losses_total|loser_ethnicity_pct_of_carrier|losses_by_ethnicity|
          |2017-08-01|500|Hispanic |55-74|100-150k|Verizon_Postpaid Phone|100        |0.9                            |90|
          |2017-08-01|500|Asian    |75 + |100-150k|Sprint_Postpaid Phone |100        |0.1                            |10|
          |2019-05-01|500|Asian    |55-74|GT 150k |AT&T_Postpaid Phone   |100        |0.2                            |20|
          |2019-05-01|500|Black    |55-74|$50-100K|Other_Postpaid Phone  |100        |0.3                            |30|
          |2022-06-01|500|Black    |18-34|$50-100K|AT&T_Postpaid Phone   |100        |0.5                            |50|
  """
      }, nullable = true).as[lossesEthnicityPercentOfCarrierVPGM]

      val winsEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[winsEthnicityPercentOfCarrierVPGM] {
        """
          |date      |vpgm|ethnicity|age  |income  |winner_brand_plantype |wins_total |winner_ethnicity_pct_of_carrier| wins_by_ethnicity|
          |2017-08-01|500|Hispanic |55-74|100-150k|AT&T_Postpaid Phone   |100        |0.9                            |90|
          |2017-08-01|500|Asian    |75 + |100-150k|AT&T_Postpaid Phone   |100        |0.1                            |10|
          |2019-05-01|500|Asian    |55-74|GT 150k |AT&T_Postpaid Phone   |100        |0.2                            |20|
          |2019-05-01|500|Black    |55-74|$50-100K|AT&T_Postpaid Phone   |100        |0.3                            |30|
          |2022-06-01|500|Black    |18-34|$50-100K|AT&T_Postpaid Phone   |100        |0.5                            |50|
          """
      }, nullable = true).as[winsEthnicityPercentOfCarrierVPGM]

      val winnerPercentOfLoserData = setNullableStateForAllColumns(dataframe[winnerPercentOfLoserVPGM] {
        """
          |      date|vpgm|loser_brand_plantype|winner_brand_plantype|wins|total_losses|winner_pct_of_loser|
          |2017-08-01|500|Verizon_Postpaid Phone|AT&T_Postpaid Phone|10|100|0.1|
          |2017-08-01|500|Sprint_Postpaid Phone |AT&T_Postpaid Phone|90|100|0.9|
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone|20|100|0.2|
          |2019-05-01|500|Other_Postpaid Phone  |AT&T_Postpaid Phone|30|100|0.3|
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone|50|100|0.5|
          """
      }, nullable = true).as[winnerPercentOfLoserVPGM]

      val loserPercentOfLoserData = setNullableStateForAllColumns(dataframe[loserPercentOfWinnerVPGM] {
        """
          |      date|vpgm|loser_brand_plantype|winner_brand_plantype|wins|total_wins|loser_pct_of_winner|
          |2017-08-01|500|Verizon_Postpaid Phone|AT&T_Postpaid Phone|10|100|0.1|
          |2017-08-01|500|Sprint_Postpaid Phone |AT&T_Postpaid Phone|90|100|0.9|
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone|20|100|0.2|
          |2019-05-01|500|Other_Postpaid Phone  |AT&T_Postpaid Phone|30|100|0.3|
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone|50|100|0.5|
    """
      }, nullable = true).as[loserPercentOfWinnerVPGM]

      val expected = setNullableStateForAllColumns(dataframe[winsLossesByEthnicityInterim1VPGM] {
        """
          |      date|vpgm|loser_brand_plantype|winner_brand_plantype|ethnicity|  age|  income|wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier|losses_by_ethnicity|wins_by_ethnicity_calc|winner_ethnicity_pct_of_carrier|wins_by_ethnicity|losses_by_ethnicity_recalc|w_l_avg_ethnicity_pct_of_carrier|loser_avg_churn_volume_by_ethnicity|winner_avg_churn_volume_by_ethnicity|
          |2017-08-01|500|Sprint_Postpaid Phone|  AT&T_Postpaid Phone|    Asian| 75 +|100-150k|90.0|       100.0|     100.0|                0.9|                           0.1|               10.0|                   9.0|                            0.1|             10.0|                       9.0|                             0.1|                                9.0|                                 9.0|
          |2017-08-01|500|Verizon_Postpaid Phone|  AT&T_Postpaid Phone| Hispanic|55-74|100-150k|10.0|       100.0|     100.0|                0.1|                           0.9|               90.0|                   9.0|                            0.9|             90.0|                       9.0|                             0.9|                                9.0|                                 9.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                           0.2|               20.0|                   4.0|                            0.2|             20.0|                       4.0|                             0.2|                                4.0|                                 4.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                           0.2|               20.0|                   4.0|                            0.2|             20.0|                      10.0|                             0.2|                                4.0|                                 4.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                           0.2|               20.0|                  10.0|                            0.2|             20.0|                       4.0|                             0.2|                               10.0|                                10.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                           0.2|               20.0|                  10.0|                            0.2|             20.0|                      10.0|                             0.2|                               10.0|                                10.0|
          |2019-05-01|500|Other_Postpaid Phone|  AT&T_Postpaid Phone|    Black|55-74|$50-100K|30.0|       100.0|     100.0|                0.3|                           0.3|               30.0|                   9.0|                            0.3|             30.0|                       9.0|                             0.3|                                9.0|                                 9.0|
          """
      }, nullable = true).as[winsLossesByEthnicityInterim1VPGM]

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateWinsLossesByEthnicityInterim1(
        lossesEthnicityPercentOfCarrierData, winsEthnicityPercentOfCarrierData, winnerPercentOfLoserData, loserPercentOfLoserData
      ).toDF(), nullable = true).as[winsLossesByEthnicityInterim1VPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }

    it("interim2") {
      val interim1Data = setNullableStateForAllColumns(dataframe[winsLossesByEthnicityInterim1VPGM] {
        """
          |      date|vpgm|loser_brand_plantype|winner_brand_plantype|ethnicity|  age|  income|wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier|losses_by_ethnicity|wins_by_ethnicity_calc|winner_ethnicity_pct_of_carrier|wins_by_ethnicity|losses_by_ethnicity_recalc|w_l_avg_ethnicity_pct_of_carrier|loser_avg_churn_volume_by_ethnicity|winner_avg_churn_volume_by_ethnicity|
          |2017-08-01|500|Sprint_Postpaid Phone|  AT&T_Postpaid Phone|    Asian| 75 +|100-150k|90.0|       100.0|     100.0|                0.9|                           0.1|               10.0|                   9.0|                            0.1|             10.0|                       9.0|                             0.1|                                9.0|                                 9.0|
          |2017-08-01|500|Verizon_Postpaid Phone|  AT&T_Postpaid Phone| Hispanic|55-74|100-150k|10.0|       100.0|     100.0|                0.1|                           0.9|               90.0|                   9.0|                            0.9|             90.0|                       9.0|                             0.9|                                9.0|                                 9.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                           0.2|               20.0|                   4.0|                            0.2|             20.0|                       4.0|                             0.2|                                4.0|                                 4.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                           0.2|               20.0|                   4.0|                            0.2|             20.0|                      10.0|                             0.2|                                4.0|                                 4.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                           0.2|               20.0|                  10.0|                            0.2|             20.0|                       4.0|                             0.2|                               10.0|                                10.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                           0.2|               20.0|                  10.0|                            0.2|             20.0|                      10.0|                             0.2|                               10.0|                                10.0|
          |2019-05-01|500|Other_Postpaid Phone|  AT&T_Postpaid Phone|    Black|55-74|$50-100K|30.0|       100.0|     100.0|                0.3|                           0.3|               30.0|                   9.0|                            0.3|             30.0|                       9.0|                             0.3|                                9.0|                                 9.0|
          """
      }, nullable = true).as[winsLossesByEthnicityInterim1VPGM]

      val demoVarianceFactorCompressedData = setNullableStateForAllColumns(dataframe[demoVarianceFactorCompressed] {
        """
          |date      |winner_brand_plantype |ethnicity|age  |income  |wins_numerator|wins_denominator|wins_prop            |long_term_wins_numerator|long_term_wins_denominator|long_term_wins_prop  |variance_factor   |variance_factor_compressed|
          |2017-08-01|AT&T_Postpaid Phone   |Asian    |75+|LT $50K |457           |16583           |0.027558342881263943 |3125                    |107855                    |0.028974085577859163 |0.9511376228667913|0.9511376228667913|
          |2017-08-01|AT&T_Postpaid Phone   |Hispanic    |55-74|LT $50K |746           |16583           |0.04498582886088163  |4814                    |107855                    |0.04463399935098048  |1.007882545033317 |1.007882545033317|
          |2019-05-01|AT&T_Postpaid Phone   |Asian    |55-74|$50-100K|18            |16583           |0.001085448953747814 |126                     |107855                    |0.0011682351304992814|0.9291356897338927|0.9291356897338927|
          |2019-05-01|AT&T_Postpaid Phone   |Asian    |55-74|$50-100K|122           |16583           |0.007356931797624073 |1059                    |107855                    |0.009818738120624913 |0.7492746733075962|0.9|
          |2019-05-01|AT&T_Postpaid Phone   |Asian |55-74|100-150k|107           |16583           |0.006452391002834228 |801                     |107855                    |0.007426637615316861 |0.8688172679284465|0.9|
          |2019-05-01|AT&T_Postpaid Phone   |Black    |55-74|$50-100K|88            |16583           |0.005306639329433757 |834                     |107855                    |0.007732603959019054 |0.6862680873813883|0.9|
        """
      }, nullable = true).as[demoVarianceFactorCompressed]

      val expected = setNullableStateForAllColumns(dataframe[winsLossesByEthnicityInterim2VPGM] {
        """
          |      date|vpgm|loser_brand_plantype  |winner_brand_plantype|ethnicity|  age|  income|wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier_original|losses_by_ethnicity|winner_ethnicity_pct_of_carrier_original|wins_by_ethnicity|loser_avg_churn_volume_by_ethnicity_original|winner_avg_churn_volume_by_ethnicity_original|variance_factor_compressed_not_null_check|variance_factor_compressed|
          |2017-08-01|500|Sprint_Postpaid Phone |  AT&T_Postpaid Phone|    Asian| 75 +|100-150k|90.0|       100.0|     100.0|                0.9|                                    0.1|               10.0|                                     0.1|             10.0|                                         9.0|                                          9.0|                                        0|                       1.0|
          |2017-08-01|500|Verizon_Postpaid Phone|  AT&T_Postpaid Phone| Hispanic|55-74|100-150k|10.0|       100.0|     100.0|                0.1|                                    0.9|               90.0|                                     0.9|             90.0|                                         9.0|                                          9.0|                                        0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                                    0.2|               20.0|                                     0.2|             20.0|                                         4.0|                                          4.0|                                        0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                                    0.2|               20.0|                                     0.2|             20.0|                                         4.0|                                          4.0|                                        0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                                    0.2|               20.0|                                     0.2|             20.0|                                        10.0|                                         10.0|                                        0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                                    0.2|               20.0|                                     0.2|             20.0|                                        10.0|                                         10.0|                                        0|                       1.0|
          |2019-05-01|500|Other_Postpaid Phone  |  AT&T_Postpaid Phone|    Black|55-74|$50-100K|30.0|       100.0|     100.0|                0.3|                                    0.3|               30.0|                                     0.3|             30.0|                                         9.0|                                          9.0|                                        1|                       0.9|
          """
      }, nullable = true).as[winsLossesByEthnicityInterim2VPGM]

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateWinsLossesByEthnicityInterim2(
        interim1Data, demoVarianceFactorCompressedData
      ).toDF(), nullable = true).as[winsLossesByEthnicityInterim2VPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }

    it("final calculateWinsLossesByEthnicity") {
      val interim2Data = setNullableStateForAllColumns(dataframe[winsLossesByEthnicityInterim2VPGM] {
        """
          |      date|vpgm|loser_brand_plantype  |winner_brand_plantype|ethnicity|  age|  income|wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier_original|losses_by_ethnicity|winner_ethnicity_pct_of_carrier_original|wins_by_ethnicity|loser_avg_churn_volume_by_ethnicity_original|winner_avg_churn_volume_by_ethnicity_original|variance_factor_compressed_not_null_check|variance_factor_compressed|
          |2017-08-01|500|Sprint_Postpaid Phone |  AT&T_Postpaid Phone|    Asian| 75 +|100-150k|90.0|       100.0|     100.0|                0.9|                                    0.1|               10.0|                                     0.1|             10.0|                                         9.0|                                          9.0|                                      0.0|                       1.0|
          |2017-08-01|500|Verizon_Postpaid Phone|  AT&T_Postpaid Phone| Hispanic|55-74|100-150k|10.0|       100.0|     100.0|                0.1|                                    0.9|               90.0|                                     0.9|             90.0|                                         9.0|                                          9.0|                                      0.0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                                    0.2|               20.0|                                     0.2|             20.0|                                         4.0|                                          4.0|                                      0.0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                                    0.2|               20.0|                                     0.2|             20.0|                                         4.0|                                          4.0|                                      0.0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                                    0.2|               20.0|                                     0.2|             20.0|                                        10.0|                                         10.0|                                      0.0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                                    0.2|               20.0|                                     0.2|             20.0|                                        10.0|                                         10.0|                                      0.0|                       1.0|
          |2019-05-01|500|Other_Postpaid Phone  |  AT&T_Postpaid Phone|    Black|55-74|$50-100K|30.0|       100.0|     100.0|                0.3|                                    0.3|               30.0|                                     0.3|             30.0|                                         9.0|                                          9.0|                                      1.0|                       0.9|
       """
      }, nullable = true).as[winsLossesByEthnicityInterim2VPGM]
      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateWinsLossesByEthnicity(
        interim2Data
      ).toDF(), nullable = true).as[winsLossesByEthnicityVPGM]

      val expected = setNullableStateForAllColumns(dataframe[winsLossesByEthnicityVPGM] {
        """
          |date      |vpgm|loser_brand_plantype  |winner_brand_plantype|ethnicity|age  |income  |wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier_original|losses_by_ethnicity|winner_ethnicity_pct_of_carrier_original|wins_by_ethnicity|loser_avg_churn_volume_by_ethnicity_original|winner_avg_churn_volume_by_ethnicity_original|variance_factor_compressed|loser_ethnicity_pct_of_carrier_numerator|winner_ethnicity_pct_of_carrier_numerator|loser_ethnicity_pct_of_carrier_denominator|winner_ethnicity_pct_of_carrier_denominator|loser_ethnicity_pct_of_carrier|winner_ethnicity_pct_of_carrier|loser_avg_churn_volume_by_ethnicity|winner_avg_churn_volume_by_ethnicity|
          |2017-08-01|500|Sprint_Postpaid Phone |AT&T_Postpaid Phone  |Asian    |75 + |100-150k|90.0|100.0       |100.0     |0.9                |0.1                                    |10.0               |0.1                                     |10.0             |9.0                                         |9.0                                          |1.0                       |0.1                                     |0.1                                      |0.1                                       |0.1                                        |1.0                           |1.0                            |90.0                               |90.0                                |
          |2017-08-01|500|Verizon_Postpaid Phone|AT&T_Postpaid Phone  |Hispanic |55-74|100-150k|10.0|100.0       |100.0     |0.1                |0.9                                    |90.0               |0.9                                     |90.0             |9.0                                         |9.0                                          |1.0                       |0.9                                     |0.9                                      |0.9                                       |0.9                                        |1.0                           |1.0                            |10.0                               |10.0                                |
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone  |Asian    |55-74|GT 150k |20.0|100.0       |100.0     |0.2                |0.2                                    |20.0               |0.2                                     |20.0             |4.0                                         |4.0                                          |1.0                       |0.2                                     |0.2                                      |0.8                                       |0.8                                        |0.25                          |0.25                           |5.0                                |5.0                                 |
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone  |Asian    |55-74|GT 150k |20.0|100.0       |100.0     |0.2                |0.2                                    |20.0               |0.2                                     |20.0             |4.0                                         |4.0                                          |1.0                       |0.2                                     |0.2                                      |0.8                                       |0.8                                        |0.25                          |0.25                           |5.0                                |5.0                                 |
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone  |Asian    |55-74|GT 150k |50.0|100.0       |100.0     |0.5                |0.2                                    |20.0               |0.2                                     |20.0             |10.0                                        |10.0                                         |1.0                       |0.2                                     |0.2                                      |0.8                                       |0.8                                        |0.25                          |0.25                           |12.5                               |12.5                                |
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone  |Asian    |55-74|GT 150k |50.0|100.0       |100.0     |0.5                |0.2                                    |20.0               |0.2                                     |20.0             |10.0                                        |10.0                                         |1.0                       |0.2                                     |0.2                                      |0.8                                       |0.8                                        |0.25                          |0.25                           |12.5                               |12.5                                |
          |2019-05-01|500|Other_Postpaid Phone  |AT&T_Postpaid Phone  |Black    |55-74|$50-100K|30.0|100.0       |100.0     |0.3                |0.3                                    |30.0               |0.3                                     |30.0             |9.0                                         |9.0                                          |0.9                       |0.27                                    |0.27                                     |0.27                                      |0.27                                       |1.0                           |1.0                            |30.0                               |30.0                                |
        """
      }, nullable = true).as[winsLossesByEthnicityVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateWinsFromAverageEthnicityPercentOfCarrier") {
    import spark.implicits._
    it("test") {
      val winsLossesByEthnicityData = setNullableStateForAllColumns(dataframe[winsLossesByEthnicityVPGM] {
        """
          |      date|vpgm|loser_brand_plantype|winner_brand_plantype|ethnicity|  age|  income|wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier_original|losses_by_ethnicity|winner_ethnicity_pct_of_carrier_original|wins_by_ethnicity|loser_avg_churn_volume_by_ethnicity_original|winner_avg_churn_volume_by_ethnicity_original|loser_ethnicity_pct_of_carrier_numerator|winner_ethnicity_pct_of_carrier_numerator|loser_ethnicity_pct_of_carrier_denominator|winner_ethnicity_pct_of_carrier_denominator|variance_factor_compressed|loser_ethnicity_pct_of_carrier|winner_ethnicity_pct_of_carrier|loser_avg_churn_volume_by_ethnicity|winner_avg_churn_volume_by_ethnicity|
          |2017-08-01|500|Sprint_Postpaid Phone|  AT&T_Postpaid Phone|    Asian| 75 +|100-150k|90.0|       100.0|     100.0|                0.9|                                    0.1|               10.0|                                     0.1|             10.0|                                         9.0|                                          9.0|                                     0.1|                                      0.1|                                       0.1|                                        0.1|                       1.0|                          0.5|                           0.5|                               10|                                10|
          |2017-08-01|500|Verizon_Postpaid Phone|  AT&T_Postpaid Phone| Hispanic|55-74|100-150k|10.0|       100.0|     100.0|                0.1|                                    0.9|               90.0|                                     0.9|             90.0|                                         9.0|                                          9.0|                                     0.9|                                      0.9|                                       0.9|                                        0.9|                       1.0|                          0.5|                          0.5|                               10|                                10|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                                    0.2|               20.0|                                     0.2|             20.0|                                         4.0|                                          4.0|                                     0.2|                                      0.2|                                       0.8|                                        0.8|                       1.0|                          0.5|                           0.5|                               10|                                10|
        """
      }, nullable = true).as[winsLossesByEthnicityVPGM]

      val totalWinsData = setNullableStateForAllColumns(dataframe[totalWinsVPGM] {
        """
          |date      |vpgm|winner_brand_plantype|wins  |
          |2017-08-01|500|AT&T_Postpaid Phone  |74.0  |
          |2017-08-01|500|AT&T_Postpaid Phone  |2.0   |
          |2019-05-01|500|AT&T_Postpaid Phone  |17.0  |
        """
      }, nullable = true).as[totalWinsVPGM]

      val expected = setNullableStateForAllColumns(dataframe[winsFromAverageEthnicityPercentOfCarrierVPGM] {
        """
          |      date|vpgm|ethnicity|  age|  income|winner_brand_plantype|wins_total|wins_from_average_by_ethnicity|winner_from_average_ethnicity_pct_of_carrier|
          |2017-08-01|500|    Asian| 75 +|100-150k|  AT&T_Postpaid Phone|      74.0|                          10.0|                         0.13513513513513514|
          |2017-08-01|500|    Asian| 75 +|100-150k|  AT&T_Postpaid Phone|       2.0|                          10.0|                                         5.0|
          |2017-08-01|500| Hispanic|55-74|100-150k|  AT&T_Postpaid Phone|      74.0|                          10.0|                         0.13513513513513514|
          |2017-08-01|500| Hispanic|55-74|100-150k|  AT&T_Postpaid Phone|       2.0|                          10.0|                                         5.0|
          |2019-05-01|500|    Asian|55-74| GT 150k|  AT&T_Postpaid Phone|      17.0|                          10.0|                          0.5882352941176471|
    """
      }, nullable = true).as[winsFromAverageEthnicityPercentOfCarrierVPGM]

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateWinsFromAverageEthnicityPercentOfCarrier(
        winsLossesByEthnicityData, totalWinsData
      ).toDF(), nullable = true).as[winsFromAverageEthnicityPercentOfCarrierVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateLossesFromAverageEthnicityPercentOfCarrier") {
    import spark.implicits._
    it("test") {
      val winsLossesByEthnicityData = setNullableStateForAllColumns(dataframe[winsLossesByEthnicityVPGM] {
        """
          |      date|vpgm|loser_brand_plantype  |winner_brand_plantype |ethnicity|  age|  income|wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier_original|losses_by_ethnicity|winner_ethnicity_pct_of_carrier_original|wins_by_ethnicity|loser_avg_churn_volume_by_ethnicity_original|winner_avg_churn_volume_by_ethnicity_original|loser_ethnicity_pct_of_carrier_numerator|winner_ethnicity_pct_of_carrier_numerator|loser_ethnicity_pct_of_carrier_denominator|winner_ethnicity_pct_of_carrier_denominator|variance_factor_compressed|loser_ethnicity_pct_of_carrier|winner_ethnicity_pct_of_carrier|loser_avg_churn_volume_by_ethnicity|winner_avg_churn_volume_by_ethnicity|
          |2017-08-01|500|Sprint_Postpaid Phone |  AT&T_Postpaid Phone |    Asian| 75 +|100-150k|90.0|       100.0|     100.0|                0.9|                                    0.1|               10.0|                                     0.1|             10.0|                                         9.0|                                          9.0|                                     0.1|                                      0.1|                                       0.1|                                        0.1|                       1.0|                          0.5|                           0.5|                               10|                                10|
          |2017-08-01|500|Verizon_Postpaid Phone|  AT&T_Postpaid Phone | Hispanic|55-74|100-150k|10.0|       100.0|     100.0|                0.1|                                    0.9|               90.0|                                     0.9|             90.0|                                         9.0|                                          9.0|                                     0.9|                                      0.9|                                       0.9|                                        0.9|                       1.0|                          0.5|                          0.5|                               10|                                10|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone |    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                                    0.2|               20.0|                                     0.2|             20.0|                                         4.0|                                          4.0|                                     0.2|                                      0.2|                                       0.8|                                        0.8|                       1.0|                          0.5|                           0.5|                               10|                                10|
       """
      }, nullable = true).as[winsLossesByEthnicityVPGM]

      val totalLossesData = setNullableStateForAllColumns(dataframe[totalLossesVPGM] {
        """
          |date      |vpgm|loser_brand_plantype  |losses|
          |2017-08-01|500|Sprint_Postpaid Phone |20    |
          |2017-08-01|500|Verizon_Postpaid Phone|10    |
          |2019-05-01|500|AT&T_Postpaid Phone   |30    |
        """
      }, nullable = true).as[totalLossesVPGM]

      val expected = setNullableStateForAllColumns(dataframe[lossesFromAverageEthnicityPercentOfCarrierVPGM] {
        """
          |      date|vpgm|ethnicity|  age|  income|loser_brand_plantype  |losses_total|losses_from_average_by_ethnicity|loser_from_average_ethnicity_pct_of_carrier|
          |2017-08-01|500|    Asian| 75 +|100-150k|Sprint_Postpaid Phone |        20.0|                            10.0|                                        0.5|
          |2017-08-01|500| Hispanic|55-74|100-150k|Verizon_Postpaid Phone|        10.0|                            10.0|                                        1.0|
          |2019-05-01|500|    Asian|55-74| GT 150k| AT&T_Postpaid Phone  |        30.0|                            10.0|                         0.3333333333333333|
        """
      }, nullable = true).as[lossesFromAverageEthnicityPercentOfCarrierVPGM]

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateLossesFromAverageEthnicityPercentOfCarrier(
        winsLossesByEthnicityData, totalLossesData
      ).toDF(), nullable = true).as[lossesFromAverageEthnicityPercentOfCarrierVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())

    }
  }

  describe("calculateWMSWithEthnicityFromAverageBackwards") {
    import spark.implicits._

    it("test") {
      val wmsVPGMData = setNullableStateForAllColumns(dataframe[WmsVPGM] {
        """
          |month_date|       brand_plantype|vpgm|gross_adds|gross_losses|base_adjustment|subscribers|last_period_subs|next_period_subs|
          |2017-08-01|Sprint_Postpaid Phone|500|100       |100         |5              |300        |100            |100             |
          |2017-08-01|Verizon_Postpaid Phone|500|100       |100         |5              |300        |200            |100             |
          |2019-05-01|AT&T_Postpaid Phone  |500|100       |100         |5              |300        |100            |100             |
       """
      }, nullable = true).as[WmsVPGM]

      val subsEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrierVPGM] {
        """
          |date      |vpgm|winner_brand_plantype|ethnicity|age|income|subscribers|total_subscribers|ethnicity_pct_of_carrier|
          |2017-08-01|500|Sprint_Postpaid Phone|Asian|75 +|100-150k|221.0|221.0|1.0|
          |2017-08-01|500|AT&T_Postpaid Phone|Hispanic|55-74|100-150k|78.0|78.0|1.0|
          |2017-08-01|500|Verizon_Postpaid Phone|Hispanic|55-74|100-150k|5829.0|5829.0|1.0|
          |2017-08-01|500|AT&T_Postpaid Phone|Asian|75 +|100-150k|13381.0|13381.0|1.0|
          |2019-05-01|500|AT&T_Postpaid Phone|Hispanic|55-74|100-150k|722.0|722.0|1.0|
          """
      }, nullable = true).as[subsEthnicityPercentOfCarrierVPGM]

      val winsFromAverageEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[winsFromAverageEthnicityPercentOfCarrierVPGM] {
        """
          |      date|vpgm|ethnicity|  age|  income|winner_brand_plantype|wins_total|wins_from_average_by_ethnicity|winner_from_average_ethnicity_pct_of_carrier|
          |2017-08-01|500|    Asian| 75 +|100-150k|  Sprint_Postpaid Phone|      74.0|                          10.0|                         0.13513513513513514|
          |2017-08-01|500|    Asian| 75 +|100-150k|  AT&T_Postpaid Phone|       2.0|                          10.0|                                         5.0|
          |2017-08-01|500| Hispanic|55-74|100-150k|  Verizon_Postpaid Phone|      74.0|                          10.0|                         0.13513513513513514|
          |2017-08-01|500| Hispanic|55-74|100-150k|  AT&T_Postpaid Phone|       2.0|                          10.0|                                         5.0|
          |2019-05-01|500|    Asian|55-74| GT 150k|  AT&T_Postpaid Phone|      17.0|                          10.0|                          0.5882352941176471|
        """
      }, nullable = true).as[winsFromAverageEthnicityPercentOfCarrierVPGM]

      val lossesFromAverageEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[lossesFromAverageEthnicityPercentOfCarrierVPGM] {
        """
          |      date|vpgm|ethnicity|  age|  income|loser_brand_plantype|losses_total|losses_from_average_by_ethnicity|loser_from_average_ethnicity_pct_of_carrier|
          |2017-08-01|500|    Asian| 75 +|100-150k|Sprint_Postpaid Phone|        20.0|                            10.0|                                        0.5|
          |2017-08-01|500| Hispanic|55-74|100-150k|Verizon_Postpaid Phone|        10.0|                            10.0|                                        1.0|
          |2019-05-01|500|    Asian|55-74| GT 150k| AT&T_Postpaid Phone|        30.0|                            10.0|                         0.3333333333333333|
          """
      }, nullable = true).as[lossesFromAverageEthnicityPercentOfCarrierVPGM]

      val totalLossesData = setNullableStateForAllColumns(dataframe[totalLossesVPGM] {
        """
          |date      |vpgm|loser_brand_plantype   |losses|
          |2017-08-01|500|Sprint_Postpaid Phone  |20    |
          |2017-08-01|500|Verizon_Postpaid Phone |10    |
          |2019-05-01|500|AT&T_Postpaid Phone    |30    |
        """
      }, nullable = true).as[totalLossesVPGM]

      val totalWinsData = setNullableStateForAllColumns(dataframe[totalWinsVPGM] {
        """
          |date      |vpgm|winner_brand_plantype|wins  |
          |2017-08-01|500|Sprint_Postpaid Phone  |74.0  |
          |2017-08-01|500|Verizon_Postpaid Phone|2.0   |
          |2019-05-01|500|AT&T_Postpaid Phone  |17.0  |
        """
      }, nullable = true).as[totalWinsVPGM]

      val expected = setNullableStateForAllColumns(dataframe[wmsWithEthnicityFromAverageBackwardsVPGM] {
        """
          | wms_month|subs_join_month|losses_join_month|wins_join_month|vpgm|      brand_plantype   |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|  age|  income|subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses|winner_from_average_ethnicity_pct_of_carrier|        gross_adds|   base_adjustment|
          |2017-08-01|    2017-08-01 |      2017-08-01 |    2017-08-01 |500|Sprint_Postpaid Phone |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0|    Asian| 75 +|100-150k|                  221.0|                          1.0|             300.0|                           0.5|        50.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2017-08-01|    2017-08-01 |      2017-08-01 |    2017-08-01 |500|Verizon_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 200.0|                 100.0| Hispanic|55-74|100-150k|                 5829.0|                          1.0|             300.0|                           1.0|       100.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2019-05-01|    2019-05-01 |            null |          null |500| AT&T_Postpaid Phone  |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0| Hispanic|55-74|100-150k|                  722.0|                          1.0|             300.0|                          null|       100.0|                                        null|             100.0|               5.0|
        """
      }, nullable = true).as[wmsWithEthnicityFromAverageBackwardsVPGM]

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateWMSWithEthnicityFromAverageBackwards(
        wmsVPGMData, subsEthnicityPercentOfCarrierData, lossesFromAverageEthnicityPercentOfCarrierData, winsFromAverageEthnicityPercentOfCarrierData, totalLossesData, totalWinsData
      ).toDF(), nullable = true).as[wmsWithEthnicityFromAverageBackwardsVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateWMSWithEthnicityWithStartingSubsFromAverageBackwards") {
    import spark.implicits._
    it("test") {
      val wmsWithEthnicityFromAverageBackwardsData = setNullableStateForAllColumns(dataframe[wmsWithEthnicityFromAverageBackwardsVPGM] {
        """
          | wms_month|subs_join_month|losses_join_month|wins_join_month|vpgm|      brand_plantype   |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|  age|  income|subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses|winner_from_average_ethnicity_pct_of_carrier|        gross_adds|   base_adjustment|
          |2017-08-01|    2017-08-01 |      2017-08-01 |    2017-08-01 |500|Sprint_Postpaid Phone |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0|    Asian| 75 +|100-150k|                  221.0|                          1.0|             300.0|                           0.5|        50.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2017-08-01|    2017-08-01 |      2017-08-01 |    2017-08-01 |500|Verizon_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 200.0|                 100.0| Hispanic|55-74|100-150k|                 5829.0|                          1.0|             300.0|                           1.0|       100.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2019-05-01|    2019-05-01 |            null |          null |500| AT&T_Postpaid Phone  |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0| Hispanic|55-74|100-150k|                  722.0|                          1.0|             300.0|                          null|       100.0|                                        null|             100.0|               5.0|
      """
      }, nullable = true).as[wmsWithEthnicityFromAverageBackwardsVPGM]

      val expected = setNullableStateForAllColumns(dataframe[wmsWithEthnicityWithStartingSubsFromAverageBackwardsVPGM] {
        """
          | wms_month|subs_join_month|losses_join_month|wins_join_month|vpgm|      brand_plantype  |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|  age|  income|subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses|winner_from_average_ethnicity_pct_of_carrier|        gross_adds|   base_adjustment|starting_subscribers|     gross_add_rate|         churn_rate|overall_gross_add_rate|overall_churn_rate|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01|500 |Sprint_Postpaid Phone |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0|    Asian| 75 +|100-150k|                  221.0|                          1.0|             300.0|                           0.5|        50.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|  335.81081081081084|0.04024144869215292|0.14889336016096577|                   1.0|               1.0|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01|500 |Verizon_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 200.0|                 100.0| Hispanic|55-74|100-150k|                 5829.0|                          1.0|             300.0|                           1.0|       100.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|  385.81081081081084|0.03502626970227671| 0.2591943957968476|                   0.5|               0.5|
          |2019-05-01|     2019-05-01|             null|           null|500 | AT&T_Postpaid Phone  |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0| Hispanic|55-74|100-150k|                  722.0|                          1.0|             300.0|                           0.0|       100.0|                                         0.0|             100.0|               5.0|               295.0| 0.3389830508474576| 0.3389830508474576|                   1.0|               1.0|
        """
      }, nullable = true).as[wmsWithEthnicityWithStartingSubsFromAverageBackwardsVPGM]

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().calculateWMSWithEthnicityWithStartingSubsFromAverageBackwards(
        wmsWithEthnicityFromAverageBackwardsData
      ).toDF(), nullable = true).as[wmsWithEthnicityWithStartingSubsFromAverageBackwardsVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("assignCarrierNameCaseStatement") {
    import spark.implicits._
    it("test") {
      val wirelessMovementWideData = setNullableStateForAllColumns(dataframe[WirelessMovementWideGenericVPGM] {
        """
          |zip_cd|region_dim_id|state|bta|cbsa|vpgm|cma|secondary_sp|primary_sp|merger_id|tenure|race_dim_id|primary_plan_type_id|secondary_plan_type_id|prior_losing_plan_type_id|prior_losing_sp|ustn_ind|adjusted_wins|customer_base_date|
          | 41104|            0|    0|  0|   0|  0|  0|           3|         6|        0|     0|          0|                   1|                     1|                        0|               0|    true|          100|2020-02-01|
          | 41104|            0|    0|  0|   0|  0|  0|           3|         4|        0|     0|          0|                   1|                     1|                        0|               0|    true|          100|2020-02-01|
       """
      }, nullable = true).as[WirelessMovementWideGenericVPGM]

      val result = wirelessMovementWideData
        .withColumn("a", lit(0))
        .transform(DemographicsUtilsVPGM()
          .assignCarrierNameCaseStatement($"primary_sp", $"primary_plan_type_id", "winner"))
    }
  }

  describe("wmsWithEthnicityWithEndingSubsFromAverageForwards") {
    import spark.implicits._
    it("test") {
      val wmsWithEthnicityFromAverageBackwardsData = setNullableStateForAllColumns(dataframe[wmsWithEthnicityFromAverageForwardsVPGM] {
        """
          | wms_month|subs_join_month|losses_join_month|wins_join_month|vpgm|brand_plantype        |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|  age|  income|subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|starting_subscribers|loser_ethnicity_pct_of_carrier|gross_losses|winner_from_average_ethnicity_pct_of_carrier|        gross_adds|   base_adjustment|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01| 500|Sprint_Postpaid Phone |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0|    Asian| 75 +|100-150k|                  221.0|                          1.0|               100.0|                           0.5|        50.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01| 500|Verizon_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 200.0|                 100.0| Hispanic|55-74|100-150k|                 5829.0|                          1.0|               200.0|                           1.0|       100.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2019-05-01|     2019-05-01|             null|           null| 500| AT&T_Postpaid Phone  |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0| Hispanic|55-74|100-150k|                  722.0|                          1.0|               100.0|                           0.0|       100.0|                                         0.0|             100.0|               5.0|
        """
      }, nullable = true).as[wmsWithEthnicityFromAverageForwardsVPGM]

      val expected = setNullableStateForAllColumns(dataframe[wmsWithEthnicityWithStartingSubsFromAverageForwardsVPGM] {
        """
          | wms_month|subs_join_month|losses_join_month|wins_join_month|vpgm|        brand_plantype|total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|  age|  income|subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses|winner_from_average_ethnicity_pct_of_carrier|        gross_adds|   base_adjustment|starting_subscribers|     gross_add_rate|churn_rate|overall_gross_add_rate|overall_churn_rate|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01| 500| Sprint_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0|    Asian| 75 +|100-150k|                  221.0|                          1.0|  64.1891891891892|                           0.5|        50.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|               100.0|0.13513513513513514|       0.5|                   1.0|               1.0|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01| 500|Verizon_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 200.0|                 100.0| Hispanic|55-74|100-150k|                 5829.0|                          1.0| 114.1891891891892|                           1.0|       100.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|               200.0|0.06756756756756757|       0.5|                   0.5|               0.5|
          |2019-05-01|     2019-05-01|             null|           null| 500|   AT&T_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0| Hispanic|55-74|100-150k|                  722.0|                          1.0|             105.0|                           0.0|       100.0|                                         0.0|             100.0|               5.0|               100.0|                1.0|       1.0|                   1.0|               1.0|
        """
      }, nullable = true).as[wmsWithEthnicityWithStartingSubsFromAverageForwardsVPGM]

      val result = setNullableStateForAllColumns(DemographicsUtilsVPGM().wmsWithEthnicityWithEndingSubsFromAverageForwards(
        wmsWithEthnicityFromAverageBackwardsData
      ).toDF(), nullable = true).as[wmsWithEthnicityWithStartingSubsFromAverageForwardsVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

//  // DO NOT USE/RUN this test case, it is mainly used for the local to generate the QA dataset
//  describe("QA comparison") {
//    import spark.implicits._
//
//    it("Generate difference QA file") {
//      val manualOutputData = MonthlyOutputWithEthnicityVPGM.read_csv(URI create "wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/Demographics/demographics_manual_vpgm_output.csv")
//        .na.fill(0)
//      val automatedOutputData = MonthlyOutputWithEthnicityVPGM.read_csv(URI create "wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/Demographics/demographics_automated_vpgm_output.csv")
//      manualOutputData.show(10, truncate = false)
//      println(manualOutputData.count())
//
//      automatedOutputData.show(10, truncate = false)
//      println(automatedOutputData.count())
//
//      val output = manualOutputData.alias("a")
//        .join(automatedOutputData.alias("b"),Seq(
//          "wms_month", "vpgm", "brand_plantype", "ethnicity", "age", "income"
//        ) , "LEFT")
//        .withColumn("total_subscribers_diff", $"a.total_subscribers" - $"b.total_subscribers")
//        .withColumn("total_gross_adds_diff", $"a.total_gross_adds" - $"b.total_gross_adds")
//        .withColumn("total_gross_losses_diff", $"a.total_gross_losses" - $"b.total_gross_losses")
//        .withColumn("total_base_adjustment_diff", $"a.total_base_adjustment" - $"b.total_base_adjustment")
//        .withColumn("total_last_period_subs_diff", $"a.total_last_period_subs" - $"b.total_last_period_subs")
//        .withColumn("total_next_period_subs_diff", $"a.total_next_period_subs" - $"b.total_next_period_subs")
//        .withColumn("subs_by_ethnicity_check_diff", $"a.subs_by_ethnicity_check" - $"b.subs_by_ethnicity_check")
//        .withColumn("subs_ethnicity_pct_of_carrier_diff", $"a.subs_ethnicity_pct_of_carrier" - $"b.subs_ethnicity_pct_of_carrier")
//        .withColumn("ending_subscribers_diff", $"a.ending_subscribers" - $"b.ending_subscribers")
//        .withColumn("loser_ethnicity_pct_of_carrier_diff", $"a.loser_ethnicity_pct_of_carrier" - $"b.loser_ethnicity_pct_of_carrier")
//        .withColumn("gross_losses_diff", $"a.gross_losses" - $"b.gross_losses")
//        .withColumn("winner_from_average_ethnicity_pct_of_carrier_diff", $"a.winner_from_average_ethnicity_pct_of_carrier" - $"b.winner_from_average_ethnicity_pct_of_carrier")
//        .withColumn("gross_adds_diff", $"a.gross_adds" - $"b.gross_adds")
//        .withColumn("base_adjustment_diff", $"a.base_adjustment" - $"b.base_adjustment")
//        .withColumn("starting_subscribers_diff", $"a.starting_subscribers" - $"b.starting_subscribers")
//        .withColumn("gross_add_rate_diff", $"a.gross_add_rate" - $"b.gross_add_rate")
//        .withColumn("churn_rate_diff", $"a.churn_rate" - $"b.churn_rate")
//        .withColumn("overall_gross_add_rate_diff", $"a.overall_gross_add_rate" - $"b.overall_gross_add_rate")
//        .withColumn("overall_churn_rate_diff", $"a.overall_churn_rate" - $"b.overall_churn_rate")
//        .select(
//          $"a.wms_month", $"a.subs_join_month", $"a.losses_join_month", $"a.wins_join_month", $"a.vpgm",
//          $"a.brand_plantype",
//          $"a.total_subscribers".as("manual_total_subscribers"), $"b.total_subscribers".as("automated_total_subscribers"), $"total_subscribers_diff",
//          $"a.total_gross_adds".as("manual_total_gross_adds"), $"b.total_gross_adds".as("automated_total_gross_adds"), $"total_gross_adds_diff",
//          $"a.total_gross_losses".as("manual_total_gross_losses"), $"b.total_gross_losses".as("automated_total_gross_losses"), $"total_gross_losses_diff",
//          $"a.total_base_adjustment".as("manual_total_base_adjustment"), $"b.total_base_adjustment".as("automated_total_base_adjustment"), $"total_base_adjustment_diff",
//          $"a.total_last_period_subs".as("manual_total_last_period_subs"), $"b.total_last_period_subs".as("automated_total_last_period_subs"), $"total_last_period_subs_diff",
//          $"a.total_next_period_subs".as("manual_total_next_period_subs"), $"b.total_next_period_subs".as("automated_total_next_period_subs"), $"total_next_period_subs_diff",
//          $"a.ethnicity", $"a.age", $"a.income",
//          $"a.subs_by_ethnicity_check".as("manual_subs_by_ethnicity_check"), $"b.subs_by_ethnicity_check".as("automated_subs_by_ethnicity_check"), $"subs_by_ethnicity_check_diff",
//          $"a.subs_ethnicity_pct_of_carrier".as("manual_subs_ethnicity_pct_of_carrier"), $"b.subs_ethnicity_pct_of_carrier".as("automated_subs_ethnicity_pct_of_carrier"), $"subs_ethnicity_pct_of_carrier_diff",
//          $"a.ending_subscribers".as("manual_ending_subscribers"), $"b.ending_subscribers".as("automated_ending_subscribers"), $"ending_subscribers_diff",
//          $"a.loser_ethnicity_pct_of_carrier".as("manual_loser_ethnicity_pct_of_carrier"), $"b.loser_ethnicity_pct_of_carrier".as("automated_loser_ethnicity_pct_of_carrier"), $"loser_ethnicity_pct_of_carrier_diff",
//          $"a.gross_losses".as("manual_gross_losses"), $"b.gross_losses".as("automated_gross_losses"), $"gross_losses_diff",
//          $"a.winner_from_average_ethnicity_pct_of_carrier".as("manual_winner_from_average_ethnicity_pct_of_carrier"), $"b.winner_from_average_ethnicity_pct_of_carrier".as("automated_winner_from_average_ethnicity_pct_of_carrier"), $"winner_from_average_ethnicity_pct_of_carrier_diff",
//          $"a.gross_adds".as("manual_gross_adds"), $"b.gross_adds".as("automated_gross_adds"), $"gross_adds_diff",
//          $"a.base_adjustment".as("manual_base_adjustment"), $"b.base_adjustment".as("automated_base_adjustment"), $"base_adjustment_diff",
//          $"a.starting_subscribers".as("manual_starting_subscribers"), $"b.starting_subscribers".as("automated_starting_subscribers"), $"starting_subscribers_diff",
//          $"a.gross_add_rate".as("manual_gross_add_rate"), $"b.gross_add_rate".as("automated_gross_add_rate"), $"gross_add_rate_diff",
//          $"a.churn_rate".as("manual_churn_rate"), $"b.churn_rate".as("automated_churn_rate"), $"churn_rate_diff",
//          $"a.overall_gross_add_rate".as("manual_overall_gross_add_rate"), $"b.overall_gross_add_rate".as("automated_overall_gross_add_rate"), $"overall_gross_add_rate_diff",
//          $"a.overall_churn_rate".as("manual_overall_churn_rate"), $"b.overall_churn_rate".as("automated_overall_churn_rate"), $"overall_churn_rate_diff",
//          $"a.month"
//      )
//      output.show(10, truncate = false)
//
//      output.repartition(1).write.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/demographics_outputs/vpgm_output_20220601_20231109")
//    }
//  }
//  }
}
