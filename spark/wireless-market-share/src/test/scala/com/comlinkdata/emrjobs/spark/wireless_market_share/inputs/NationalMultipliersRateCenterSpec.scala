package com.comlinkdata.emrjobs.spark.wireless_market_share.inputs

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import org.apache.spark.sql.Dataset

import java.sql.Date

class NationalMultipliersRateCenterSpec extends CldSparkBaseSpec {
  describe("RateCenterNPATotalSubs tests")  {
    import spark.implicits._

    it("Correct schema")  {
      val tempDate: Date = Date.valueOf("2022-08-01")
      val rateCenter = Seq(RateCenterNPAPortedCustomerBase(tempDate,"a","a","",1,1,1,1.0)).toDF().as[RateCenterNPAPortedCustomerBase]
      val nationalSubMult = Seq(NationalSubscriberMultipliers(tempDate,1,1,1.0,1.0,1.0)).toDF().as[NationalSubscriberMultipliers]
      val lossMultipliers = Seq(NationalLossMultipliers(tempDate,1,1,1.0,1.0,1.0)).toDF().as[NationalLossMultipliers]
      val nationalSubMultIM = Seq(NationalSubscriberMultipliersIM(tempDate,1,1,1.0)).toDF().as[NationalSubscriberMultipliersIM]
      val expected = Seq(RateCenterNPATotalSubs(tempDate,"","","",1,1,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1)).toDF().as[RateCenterNPATotalSubs]

      val result = NationalMultipliersRateCenter.apply().computeRateCenterNPATotalSubs(rateCenter,nationalSubMult,lossMultipliers,nationalSubMultIM)

      result.dtypes shouldBe expected.dtypes
    }
    it("Check output")  {
      val rateCenter = dataset[RateCenterNPAPortedCustomerBase] {
          """
            |customer_base_date |zip_rc_kblock |npa |npa_complex_cld |current_holder_sp |current_holder_plan_type_id |ported_tn_subscribers |ported_tn_losses |
            |2020-10-10         | a            | 0  | 0              |      1           |1                           |0.0                   |1.0              |
            |2020-11-10         | a            | 0  | 0              |      2           |2                           |0.0                   |2.0              |
            |2020-12-10         | a            | 0  | 0              |      3           |1                           |0.0                   |3.0              |
              """
        }

      val nationalSubMult = dataset[NationalSubscriberMultipliers] {
          """
            |customer_base_date |current_holder_sp  | current_holder_plan_type_id | ported_tn_subscribers | industry_model_subscribers | total_subs_to_ported_subs_ratio |
            |2020-10-10         | 1                 | 1                           | 0.0                   | 0.0                            |0.0              |
            |2020-11-10         | 2                 | 2                           | 0.0                   | 0.0                            |0.0               |
            |2020-12-10         | 3                 | 1                           | 0.0                   | 0.0                            |0.0               |
              """
        }
      val lossMultipliers = dataset[NationalLossMultipliers]  {
        """
          |customer_base_date |current_holder_sp  | current_holder_plan_type_id | ported_tn_losses | industry_model_losses | total_losses_to_ported_losses_ratio|
          |2020-10-10         | 1                 | 1                           | 1.0                | 0                     |1.0                                   |
          |2020-11-10         | 2                 | 2                           | 2.0                | 0                     |0.5                                   |
          |2020-12-10         | 3                 | 1                           | 3.0                | 0                     |0.33                                   |                       |
            """
      }
      val nationalSubMultIM = dataset[NationalSubscriberMultipliersIM] {
        """
          |customer_base_date |current_holder_sp  |current_holder_plan_type_id  |industry_model_subscribers |
          |2020-10-10         |1                  |1                            |0.0                        |
          |2020-11-10         |2                  |2                            |0.0                        |
          |2020-12-10         |4                  |3                            |0.0                        |
        """
      }
      val expectedData = dataset[RateCenterNPATotalSubs] {
        """
          |customer_base_date|zip_rc_kblock|npa|npa_complex_cld|current_holder_sp|current_holder_plan_type_id|ported_tn_subscribers|ported_tn_losses|national_ported_tn_subscribers|national_industry_model_subscribers|national_total_subs_to_ported_subs_ratio|rate_center_industry_model_subscribers|national_ported_tn_losses|national_industry_model_losses|national_total_losses_to_ported_losses_ratio|rate_center_industry_model_losses|industry_model_sp_ind|
          |        2020-10-10|            a|  0|              0|                1|                          1|                  0.0|             1.0|                           0.0|                                0.0|                                     0.0|                                   0.0|                      1.0|                           0.0|                                         1.0|                              1.0|                    1|
          |        2020-11-10|            a|  0|              0|                2|                          2|                  0.0|             2.0|                           0.0|                                0.0|                                     0.0|                                   0.0|                      2.0|                           0.0|                                         0.5|                              1.0|                    1|
          |        2020-12-10|            a|  0|              0|                3|                          1|                  0.0|             3.0|                           0.0|                                0.0|                                     0.0|                                   0.0|                      3.0|                           0.0|                                        0.33|                             0.99|                    0|
        """
      }

      val result = NationalMultipliersRateCenter.apply().computeRateCenterNPATotalSubs(rateCenter, nationalSubMult, lossMultipliers, nationalSubMultIM)

      assertDatasetEquals(expectedData, result)
    }
  }
  describe ("computeRateCenterNPATotalAdds tests") {
    import spark.implicits._

    it("Correct schema") {
      val tempDate: Date = Date.valueOf("2022-08-01")
      val rateCenter = Seq(RateCenterNPAAddsPortedCustomerBase(tempDate, "a", "a", "", 0, 0, 0, 0, 0.0)).toDF().as[RateCenterNPAAddsPortedCustomerBase]
      val nationalAddMult = Seq(NationalAddMultipliers(tempDate, 0, 0, 0, 0, 0.0, 0.0, 0.0)).toDF().as[NationalAddMultipliers]
      val nationalSubMultIM = Seq(NationalSubscriberMultipliersIM(tempDate, 0, 0, 0.0)).toDF().as[NationalSubscriberMultipliersIM]
      val expected = Seq(RateCenterNPATotalAdds(tempDate, "", "", "", 0, 0, 0, 0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0)).toDF().as[RateCenterNPATotalAdds]

      val result = NationalMultipliersRateCenter.apply().computeRateCenterNPATotalAdds(rateCenter, nationalAddMult, nationalSubMultIM)

      result.dtypes shouldBe expected.dtypes
    }

    it("Correct output") {
      val rateCenter = dataset[RateCenterNPAAddsPortedCustomerBase] {
        """
          |customer_base_date |zip_rc_kblock |npa |npa_complex_cld |current_holder_sp |current_holder_plan_type_id |previous_holder_sp |previous_holder_plan_type_id |ported_tn_adds|
          |2020-10-10         | a            | 0  | 0              |      1           |1                           |0                  |0                            |1.0           |
          |2020-11-10         | a            | 0  | 0              |      2           |2                           |0                  |1                            |2.0           |
          |2020-12-10         | a            | 0  | 0              |      3           |3                           |0                  |2                            |3.0           |
        """
      }
      val nationalAddMult = dataset[NationalAddMultipliers] {
        """
          |customer_base_date |current_holder_sp  | current_holder_plan_type_id | previous_holder_sp | previous_holder_plan_type_id | ported_tn_adds | industry_model_adds | total_adds_to_ported_adds_ratio|
          |2020-10-10         | 1                 | 1                           | 0                  | 0                            |0               | 0.0                 | 1.0                            |
          |2020-11-10         | 2                 | 2                           | 0                  | 1                            |0               | 0.0                 | 0.5                            |
          |2020-12-10         | 3                 | 3                           | 0                  | 2                            |0               | 0.0                 | 0.33                            |
        """
      }
      val nationalSubMultIM = dataset[NationalSubscriberMultipliersIM]  {
        """
          |customer_base_date |current_holder_sp  |current_holder_plan_type_id  |industry_model_subscribers |
          |2020-10-10         |1                  |1                            |0.0                        |
          |2020-11-10         |2                  |1                            |0.0                        |
        """
      }
      val expectedData = dataset[RateCenterNPATotalAdds] {
        """
          |customer_base_date|zip_rc_kblock|npa|npa_complex_cld|current_holder_sp|current_holder_plan_type_id|previous_holder_sp|previous_holder_plan_type_id|ported_tn_adds|national_ported_tn_adds|national_industry_model_adds|national_total_adds_to_ported_adds_ratio|rate_center_industry_model_adds|industry_model_sp_ind_current|industry_model_sp_ind_previous|
          |        2020-11-10|            a|  0|              0|                2|                          2|                 0|                           1|           2.0|                    0.0|                         0.0|                                     0.5|                            1.0|                            1|                             0|
        """
      }

      val result = NationalMultipliersRateCenter.apply().computeRateCenterNPATotalAdds(rateCenter, nationalAddMult, nationalSubMultIM)

      assertDatasetEquals(expectedData, result)
    }
  }
  describe ("computeRateCenterTotalIntraMNOAdds tests") {
    import spark.implicits._

    it("Correct schema") {
      val tempDate: Date = Date.valueOf("2022-08-01")
      val rateCenter = Seq(RateCenterAddsIntraMNO(tempDate, "a", 0, 0, 0, 0, 0)).toDF().as[RateCenterAddsIntraMNO]
      val nationalIntraAddMult = Seq(NationalAddMultipliersIntraMno(tempDate, 0, 0, 0, 0, 0.0, 0.0, 0.0)).toDF().as[NationalAddMultipliersIntraMno]
      val nationalSubMultIM = Seq(NationalSubscriberMultipliersIM(tempDate, 0, 0, 0.0)).toDF().as[NationalSubscriberMultipliersIM]
      val expected = Seq(RateCenterTotalIntraMNOAdds(tempDate, "", 0, 0, 0, 0, 0, 0, 0.0, 0.0, 0.0, 0.0, 0.0)).toDF().as[RateCenterTotalIntraMNOAdds]

      val result = NationalMultipliersRateCenter.apply().computeRateCenterTotalIntraMNOAdds(rateCenter, nationalIntraAddMult, nationalSubMultIM)

      result.printSchema()
      expected.printSchema()

      result.dtypes shouldBe expected.dtypes
    }

    it("Correct output") {
      val rateCenter = dataset[RateCenterAddsIntraMNO] {
        """
          |customer_base_date |zip_rc_kblock |current_holder_sp |current_holder_plan_type_id |previous_holder_sp |previous_holder_plan_type_id |ported_tn_adds|
          |2020-10-10         | a            | 1                |      1                     |1                  |1                            |1.0           |
          |2020-11-10         | a            | 2                |      2                     |2                  |2                            |2.0           |
          |2020-12-10         | a            | 3                |      3                     |3                  |3                            |3.0           |
        """
      }
      val nationalIntraAddMult = dataset[NationalAddMultipliersIntraMno] {
        """
          |customer_base_date |current_holder_sp  | current_holder_plan_type_id | previous_holder_sp | previous_holder_plan_type_id | ported_tn_adds | industry_model_adds | total_adds_to_ported_adds_ratio|
          |2020-10-10         | 1                 | 1                           | 1                  | 1                            |1.0               | 0.0                 | 1.0                            |
          |2020-11-10         | 2                 | 2                           | 2                  | 2                            |2.0               | 0.0                 | 0.5                            |
          |2020-12-10         | 3                 | 3                           | 3                  | 3                            |3.0               | 0.0                 | 0.33                            |
        """
      }
      val nationalSubMultIM = dataset[NationalSubscriberMultipliersIM] {
        """
          |customer_base_date |current_holder_sp  |current_holder_plan_type_id  |industry_model_subscribers |
          |2020-10-10         |1                  |1                            |0.0                        |
          |2020-11-10         |2                  |2                            |0.0                        |
        """
      }

      val expectedData = dataset[RateCenterTotalIntraMNOAdds] {
        """
          |customer_base_date|zip_rc_kblock|industry_model_sp_ind_current|industry_model_sp_ind_previous|current_holder_sp|current_holder_plan_type_id|previous_holder_sp|previous_holder_plan_type_id|national_ported_tn_adds|national_industry_model_adds|national_total_adds_to_ported_adds_ratio|ported_tn_adds|rate_center_industry_model_adds|
          |        2020-10-10|            a|                            1|                             1|                1|                          1|                 1|                           1|                    1.0|                         0.0|                                     1.0|           1.0|                            1.0|
          |        2020-11-10|            a|                            1|                             1|                2|                          2|                 2|                           2|                    2.0|                         0.0|                                     0.5|           2.0|                            1.0|
        """
      }

      val result = NationalMultipliersRateCenter.apply().computeRateCenterTotalIntraMNOAdds(rateCenter, nationalIntraAddMult, nationalSubMultIM)

      assertDatasetEquals(expectedData, result)
    }
  }
}
