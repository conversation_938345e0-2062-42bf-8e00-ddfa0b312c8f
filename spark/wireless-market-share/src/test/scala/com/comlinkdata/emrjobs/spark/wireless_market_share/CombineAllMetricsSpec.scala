package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share._
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.ZipToDMA
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{concat_ws, date_format, lit, split, trim}
import org.apache.spark.sql.types.{StructField, StructType}
import java.time.LocalDate


class CombineAllMetricsSpec extends CldSparkBaseSpec {

  def setNullableStateForAllColumns(df: DataFrame, nullable: Boolean): DataFrame = {
    // get schema
    val schema = df.schema
    // modify [[StructField] with name `cn`
    val newSchema = StructType(schema.map {
      case StructField(c, t, _, m) ⇒ Struct<PERSON>ield(c, t, nullable = nullable, m)
    })
    // apply new schema
    df.sqlContext.createDataFrame(df.rdd, newSchema)
  }

  describe("Combine All Metrics - DMA") {
    import spark.implicits._
    it("test all metrics - DMA") {

      val subs = dataset[SubscribersOutput] {
        """
          |brand         |plan_type|customer_base_date|dma|subs|
          |AT&T Wireless |1        |2022-06-01        |10 |10  |
          |AT&T Wireless |1        |2022-06-01        |20 |10  |
          |AT&T Wireless |1        |2022-06-01        |30 |10  |
          |Altice        |1        |2022-06-01        |10 |10  |
          |Altice        |1        |2022-06-01        |20 |10  |
          |Altice        |1        |2023-06-01        |30 |10  |
          |Boost         |1        |2022-06-01        |10 |10  |
          |Boost         |1        |2022-06-01        |20 |10  |
        """
      }.as[SubscribersOutput]

      val grossAdds = dataset[EstimatedGrossAddsDMA] {
        """
          |brand         |plan_type|customer_base_date|dma|estimated_gross_adds|
          |AT&T Wireless |1        |2022-07-01        |10 |10                |
          |AT&T Wireless |1        |2022-07-01        |20 |10                |
          |AT&T Wireless |1        |2022-07-01        |30 |10                |
          |Altice        |1        |2022-07-01        |10 |10                |
          |Altice        |1        |2022-07-01        |20 |10                |
          |Altice        |1        |2023-07-01        |30 |10                |
          |Boost         |1        |2022-07-01        |10 |10                |
          |Boost         |1        |2022-07-01        |20 |10                |
        """
      }.as[EstimatedGrossAddsDMA]

      val grossLosses = dataset[EstimatedGrossLossesDMA] {
        """
          |brand        |plan_type|customer_base_date|dma|estimated_gross_losses|
          |AT&T Wireless |1        |2022-07-01        |10 |10                |
          |AT&T Wireless |1        |2022-07-01        |20 |10                |
          |AT&T Wireless |1        |2022-07-01        |30 |10                |
          |Altice        |1        |2022-07-01        |10 |10                |
          |Altice        |1        |2022-07-01        |20 |10                |
          |Altice        |1        |2023-07-01        |30 |10                |
          |Boost         |1        |2022-07-01        |10 |10                |
          |Boost         |1        |2022-07-01        |20 |10                |
        """
      }.as[EstimatedGrossLossesDMA]

      val baseAdjustments = dataset[EstimatedMonthlyBA] {
        """
          |brand         |plan_type|customer_base_date|dma|estimated_ba      |
          |AT&T Wireless |1        |2022-07-01        |10 |10                |
          |AT&T Wireless |1        |2022-07-01        |20 |10                |
          |AT&T Wireless |1        |2022-07-01        |30 |10                |
          |Altice        |1        |2022-07-01        |10 |10                |
          |Altice        |1        |2022-07-01        |20 |10                |
          |Altice        |1        |2023-07-01        |30 |10                |
          |Boost         |1        |2022-07-01        |10 |10                |
          |Boost         |1        |2022-07-01        |20 |10                |
        """
      }.as[EstimatedMonthlyBA]

      val subsEopData = dataset[EndOfPeriodDMA] {
        """
        |        brand|customer_base_date|plan_type|dma|end_of_period_subscribers|
        |AT&T Wireless|        2022-07-01|        1| 10|                     20.0|
        |AT&T Wireless|        2022-07-01|        1| 20|                     20.0|
        |AT&T Wireless|        2022-07-01|        1| 30|                     20.0|
        |       Altice|        2022-07-01|        1| 10|                     20.0|
        |       Altice|        2022-07-01|        1| 20|                     20.0|
        |       Altice|        2023-07-01|        1| 30|                      0.0|
        |        Boost|        2022-07-01|        1| 10|                     20.0|
        |        Boost|        2022-07-01|        1| 20|                     20.0|
        """
      }.as[EndOfPeriodDMA]

      val zipToDMAData = dataset[ZipToDMA] {
        """
          |dma|dma_name|
          | 10|dma1|
          | 10|dma1|
          | 10|dma1|
          | 20|dma2|
          | 20|dma2|
          | 20|dma2|
          | 20|dma2|
          | 30|dma3|
        """
      }.as[ZipToDMA]

      val expectedOutput = setNullableStateForAllColumns(dataframe[finalDatasetWithAllMetricsDMA] {
        """
          |customer_base_date|geography|geography_type|brand        |plan_type|bpt          |dma|ga  |gl  |ba  |subs|
          |2022-07-01        |dma1 (10)|DMA           |AT&T Wireless|1        |AT&T Wireless|10 |10.0|10.0|10.0|20.0|
          |2022-07-01        |dma2 (20)|DMA           |AT&T Wireless|1        |AT&T Wireless|20 |10.0|10.0|10.0|20.0|
          |2022-07-01        |dma3 (30)|DMA           |AT&T Wireless|1        |AT&T Wireless|30 |10.0|10.0|10.0|20.0|
          |2022-07-01        |dma1 (10)|DMA           |Altice       |1        |Altice       |10 |10.0|10.0|10.0|20.0|
          |2022-07-01        |dma2 (20)|DMA           |Altice       |1        |Altice       |20 |10.0|10.0|10.0|20.0|
          |2023-07-01        |dma3 (30)|DMA           |Altice       |1        |Altice       |30 |10.0|10.0|10.0|0.0 |
          |2022-07-01        |dma1 (10)|DMA           |Boost        |1        |Boost        |10 |10.0|10.0|10.0|20.0|
          |2022-07-01        |dma2 (20)|DMA           |Boost        |1        |Boost        |20 |10.0|10.0|10.0|20.0|
        """
      }, nullable = true).as[finalDatasetWithAllMetricsDMA]

      val actualOutput = setNullableStateForAllColumns(CombineAllMetrics().combineAllMetricsDMA(
        grossAdds, grossLosses, baseAdjustments, subsEopData, zipToDMAData
      ).toDF(), nullable = true).as[finalDatasetWithAllMetricsDMA]

//       Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
//       Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

  describe("Combine All Metrics - VPGM") {
    import spark.implicits._
    it("test all metrics - VPGM") {

      val subs = dataset[SubscribersOutputVPGM] {
        """
          |brand         |plan_type|customer_base_date|vpgm|subs|
          |AT&T Wireless |1        |2022-06-01        |10 |10  |
          |AT&T Wireless |1        |2022-06-01        |20 |10  |
          |AT&T Wireless |1        |2022-06-01        |30 |10  |
          |Altice        |1        |2022-06-01        |10 |10  |
          |Altice        |1        |2022-06-01        |20 |10  |
          |Altice        |1        |2023-06-01        |30 |10  |
          |Boost         |1        |2022-06-01        |10 |10  |
          |Boost         |1        |2022-06-01        |20 |10  |
        """
      }.as[SubscribersOutputVPGM]

      val grossAdds = dataset[EstimatedGrossAddsVPGM] {
        """
          |brand         |plan_type|customer_base_date|vpgm|estimated_gross_adds|
          |AT&T Wireless |1        |2022-07-01        |10 |10                |
          |AT&T Wireless |1        |2022-07-01        |20 |10                |
          |AT&T Wireless |1        |2022-07-01        |30 |10                |
          |Altice        |1        |2022-07-01        |10 |10                |
          |Altice        |1        |2022-07-01        |20 |10                |
          |Altice        |1        |2023-07-01        |30 |10                |
          |Boost         |1        |2022-07-01        |10 |10                |
          |Boost         |1        |2022-07-01        |20 |10                |
        """
      }.as[EstimatedGrossAddsVPGM]

      val grossLosses = dataset[EstimatedGrossLossesVPGM] {
        """
          |brand        |plan_type|customer_base_date|vpgm|estimated_gross_losses|
          |AT&T Wireless |1        |2022-07-01        |10 |10                |
          |AT&T Wireless |1        |2022-07-01        |20 |10                |
          |AT&T Wireless |1        |2022-07-01        |30 |10                |
          |Altice        |1        |2022-07-01        |10 |10                |
          |Altice        |1        |2022-07-01        |20 |10                |
          |Altice        |1        |2023-07-01        |30 |10                |
          |Boost         |1        |2022-07-01        |10 |10                |
          |Boost         |1        |2022-07-01        |20 |10                |
        """
      }.as[EstimatedGrossLossesVPGM]

      val baseAdjustments = dataset[EstimatedMonthlyBAVPGM] {
        """
          |brand         |plan_type|customer_base_date|vpgm|estimated_ba     |
          |AT&T Wireless |1        |2022-07-01        |10 |10                |
          |AT&T Wireless |1        |2022-07-01        |20 |10                |
          |AT&T Wireless |1        |2022-07-01        |30 |10                |
          |Altice        |1        |2022-07-01        |10 |10                |
          |Altice        |1        |2022-07-01        |20 |10                |
          |Altice        |1        |2023-07-01        |30 |10                |
          |Boost         |1        |2022-07-01        |10 |10                |
          |Boost         |1        |2022-07-01        |20 |10                |
        """
      }.as[EstimatedMonthlyBAVPGM]

      val subsEopData = dataset[EndOfPeriodVPGM] {
        """
          |        brand|customer_base_date|plan_type|vpgm|end_of_period_subscribers|
          |AT&T Wireless|        2022-07-01|        1| 10|                     20.0|
          |AT&T Wireless|        2022-07-01|        1| 20|                     20.0|
          |AT&T Wireless|        2022-07-01|        1| 30|                     20.0|
          |       Altice|        2022-07-01|        1| 10|                     20.0|
          |       Altice|        2022-07-01|        1| 20|                     20.0|
          |       Altice|        2023-07-01|        1| 30|                      0.0|
          |        Boost|        2022-07-01|        1| 10|                     20.0|
          |        Boost|        2022-07-01|        1| 20|                     20.0|
        """
      }.as[EndOfPeriodVPGM]

      val expectedOutput = setNullableStateForAllColumns(dataframe[finalDatasetWithAllMetricsVPGM] {
        """
        |customer_base_date|geography|geography_type|brand        |plan_type|bpt            |vpgm|ga  |gl  |ba  |subs|
        |2022-07-01        |10       |VPGM          |AT&T Wireless|1        |AT&T Wireless_1|10  |10.0|10.0|10.0|20.0|
        |2022-07-01        |20       |VPGM          |AT&T Wireless|1        |AT&T Wireless_1|20  |10.0|10.0|10.0|20.0|
        |2022-07-01        |30       |VPGM          |AT&T Wireless|1        |AT&T Wireless_1|30  |10.0|10.0|10.0|20.0|
        |2022-07-01        |10       |VPGM          |Altice       |1        |Altice_1       |10  |10.0|10.0|10.0|20.0|
        |2022-07-01        |20       |VPGM          |Altice       |1        |Altice_1       |20  |10.0|10.0|10.0|20.0|
        |2023-07-01        |30       |VPGM          |Altice       |1        |Altice_1       |30  |10.0|10.0|10.0|0.0 |
        |2022-07-01        |10       |VPGM          |Boost        |1        |Boost_1        |10  |10.0|10.0|10.0|20.0|
        |2022-07-01        |20       |VPGM          |Boost        |1        |Boost_1        |20  |10.0|10.0|10.0|20.0|
        """
      }, nullable = true).as[finalDatasetWithAllMetricsVPGM]

      val actualOutput = setNullableStateForAllColumns(CombineAllMetrics().combineAllMetricsVPGM(
        grossAdds, grossLosses, baseAdjustments, subsEopData
      ).toDF(), nullable = true).as[finalDatasetWithAllMetricsVPGM]

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

//  Do not run these test cases as they are only use to generate QA dataset
//  describe("Actual Data") {
//    import spark.implicits._
//    it("test full run with subset of actual data - DMA")  {
////      val grossAdditionsData = spark.read.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ga_28Sept.csv").toDF().as[EstimatedGrossAddsDMA]
////      val grossLossesData = spark.read.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/gl_28Sept.csv").as[EstimatedGrossLossesDMA]
////      val baseAdjustmentsData = spark.read.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ba_28thSept.csv").as[EstimatedMonthlyBA]
////      val subsEOPData = spark.read.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/subsEop28Sept.csv").as[EndOfPeriodDMA]
////      val zipToDmaData = spark.read.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ziptodma.csv")
//
//      val finalOutputData = spark.read.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/final_output_11thOct_nullfix.csv")
//      val expectedData = spark.read.option("header", value = true).csv("/home/<USER>/Downloads/1Q19-2Q23-DMA-WO_ETH-PACKAGED-NEGSOLVED-2023_08_07.csv")
//          .filter($"customer_base_date" === "2022-07-01")
//          .select(
//            $"bpt" as "brand",
//            $"customer_base_date",
//            $"dma",
//            $"subs" as "expected_subs",
//            $"ga" as "expected_ga",
//            $"gl" as "expected_gl",
//            $"ba" as "expected_ba"
//          )
//
//      val actualData = expectedData.join(
//          finalOutputData, Seq("brand", "customer_base_date", "dma"),
//        "LEFT"
//      ).withColumn("ga difference", ($"expected_ga" - $"ga"))
//      .withColumn("gl difference", ($"expected_gl" - $"gl"))
//      .withColumn("ba difference", ($"expected_ba" - $"ba"))
//      .withColumn("subs difference", ($"expected_subs" - $"subs"))
//        .select(
//          "customer_base_date", "brand", "plan_type", "dma", "ga", "expected_ga", "ga difference",
//          "gl", "expected_gl", "gl difference", "ba", "expected_ba", "ba difference", "subs", "expected_subs", "subs difference",
//        ).orderBy("brand","dma")
//
////        .withColumn("ga % difference", ($"expected_ga" - $"ga") / $"ga")
////        .withColumn("gl % difference", ($"expected_gl" - $"gl") / $"gl")
////        .withColumn("ba % difference", ($"expected_ba" - $"ba") / $"ba")
////        .withColumn("subs % difference", ($"expected_subs" - $"subs") / $"subs")
//      actualData.write.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/final_output_only_diff_20220801_11thOct_NullFix")
//    }
//  }

//  describe("Actual Data") {
//    import spark.implicits._
//    it("test full run with subset of actual data - VPGM") {
//
//      val finalOutputData = spark.read.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/final_output_11thOct_vpgm_nullfix.csv")
//      val expectedData = spark.read.option("header", value = true).csv("/home/<USER>/Downloads/1Q19-3Q23-VPGM-WO_ETH-PACKAGED-14_08_2023.csv")
//        .withColumn("customer_base_date", date_format($"Date", "yyyy-MM-dd"))
//        .withColumn("bpt", concat_ws("_", $"brand", $"plan_type"))
//        .filter($"customer_base_date" === "2022-07-01")
//        .select(
//          $"bpt" as "brand",
//          $"customer_base_date",
//          $"Geography" as "vpgm",
//          $"subs" as "expected_subs",
//          $"ga" as "expected_ga",
//          $"gl" as "expected_gl",
//          $"ba" as "expected_ba"
//        )
//
//      val actualData = expectedData.join(
//          finalOutputData, Seq("brand", "customer_base_date", "vpgm"),
//          "LEFT"
//        ).withColumn("ga difference", ($"expected_ga" - $"ga"))
//        .withColumn("gl difference", ($"expected_gl" - $"gl"))
//        .withColumn("ba difference", ($"expected_ba" - $"ba"))
//        .withColumn("subs difference", ($"expected_subs" - $"subs"))
//        .select(
//          "customer_base_date", "brand", "plan_type", "vpgm", "ga", "expected_ga", "ga difference",
//          "gl", "expected_gl", "gl difference", "ba", "expected_ba", "ba difference", "subs", "expected_subs", "subs difference",
//        ).orderBy("brand", "vpgm")
//
//      actualData.write.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/final_output_only_diff_20220801_11thOct_vpgm_NullFix")
//    }
//  }
}
