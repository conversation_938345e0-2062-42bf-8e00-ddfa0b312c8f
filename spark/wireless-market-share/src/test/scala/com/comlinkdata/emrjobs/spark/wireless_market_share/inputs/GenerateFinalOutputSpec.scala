package com.comlinkdata.emrjobs.spark.wireless_market_share.inputs
import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.GenerateFinalOutput
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import org.apache.spark.sql.Dataset

import java.sql.Date

class GenerateFinalOutputSpec extends CldSparkBaseSpec {

  describe("Generate final output") {
    import spark.implicits._

    it("correct schema")  {
      val tempDate: Date = Date.valueOf("2022-09-15")
      val rateCenterNPATotalSubsWithRegionalMNOs = Seq(RateCenterNPATotalSubsWithRegionalMNOs(tempDate, "", "", "", 0, 0, 0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)).toDF().as[RateCenterNPATotalSubsWithRegionalMNOs]
      val rateCenterTotalIntraMNOAdds = Seq(RateCenterTotalIntraMNOAdds(tempDate, "", 0, 0, 0, 0, 0, 0, 0.0, 0.0, 0.0, 0.0, 0.0)).toDF().as[RateCenterTotalIntraMNOAdds]
      val rateCenterNPATotalAddsWithRegionalMNOs = Seq(RateCenterNPATotalAddsWithRegionalMNOs(tempDate, "", "", "", 0, 0, 0, 0, 0, 0, 0.0, 0.0, 0.0, 0.0, 0.0)).toDF().as[RateCenterNPATotalAddsWithRegionalMNOs]
      val nationalAddMultipliersActivationsIM = Seq(NationalAddMultipliersActivationsIM(tempDate,0,0,0.0,0.0,0.0,0.0,0.0,0.0)).toDF().as[NationalAddMultipliersActivationsIM]
      val expected = Seq(FinalOutput("a",0,0,0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,tempDate)).toDF().as[FinalOutput]
      val result = GenerateFinalOutput.apply().calculateFinalOutput(rateCenterNPATotalSubsWithRegionalMNOs, rateCenterTotalIntraMNOAdds, rateCenterNPATotalAddsWithRegionalMNOs, nationalAddMultipliersActivationsIM)
      result.dtypes shouldBe expected.dtypes
    }

    it("correct output")  {
      val rateCenterNPATotalSubsWithRegionalMNOs = dataset[RateCenterNPATotalSubsWithRegionalMNOs] {
        """
          |customer_base_date|zip_rc_kblock|npa|npa_complex_cld|industry_model_sp_ind|current_holder_sp|current_holder_plan_type_id|national_ported_tn_subscribers|national_industry_model_subscribers|national_total_subs_to_ported_subs_ratio|national_ported_tn_losses|national_industry_model_losses|national_total_losses_to_ported_losses_ratio|ported_tn_subscribers|ported_tn_losses|rate_center_npa_industry_model_subscribers|rate_center_npa_industry_model_losses|
          |        2022-09-15|            a|  0|              0|                    1|                1|                          1|                           0.0|                                0.0|                                     0.0|                      0.0|                           0.0|                                         1.0|                  1.0|             5.0|                                       0.0|                                  0.0|
          |        2022-09-16|            a|  1|              0|                    1|                1|                          1|                           1.0|                                1.0|                                     1.0|                      0.0|                           1.0|                                         2.0|                  2.0|             5.0|                                       1.0|                                  1.0|
          |        2022-09-16|            a|  1|              0|                    1|                1|                          1|                           1.0|                                1.0|                                     1.0|                      0.0|                           1.0|                                         3.0|                  3.0|             5.0|                                       1.0|                                  1.0|
          |        2022-09-17|            a|  2|              0|                    1|                2|                          2|                           2.0|                                2.0|                                     2.0|                      0.0|                           2.0|                                         4.0|                  4.0|             5.0|                                       0.0|                                  0.0|
          |        2022-10-15|            a|  0|              0|                    1|                1|                          1|                           0.0|                                0.0|                                     0.0|                      0.0|                           0.0|                                         1.0|                  1.0|             5.0|                                       0.0|                                  0.0|
          |        2022-10-16|            a|  1|              0|                    1|                1|                          1|                           1.0|                                1.0|                                     1.0|                      0.0|                           1.0|                                         3.0|                  3.0|             5.0|                                       1.0|                                  1.0|
          |        2022-10-17|            a|  2|              0|                    1|                2|                          2|                           2.0|                                2.0|                                     2.0|                      0.0|                           2.0|                                         4.0|                  4.0|             5.0|                                       0.0|                                  0.0|
          """
      }

      val rateCenterTotalIntraMNOAdds = dataset[RateCenterTotalIntraMNOAdds] {
        """
          |customer_base_date|zip_rc_kblock|industry_model_sp_ind_current|industry_model_sp_ind_previous|current_holder_sp|current_holder_plan_type_id|previous_holder_sp|previous_holder_plan_type_id|national_ported_tn_adds|national_industry_model_adds|national_total_adds_to_ported_adds_ratio|ported_tn_adds|rate_center_industry_model_adds|
          |        2022-09-15|            a|                            1|                             1|                1|                          1|                 1|                           1|                    1.0|                         0.0|                                     1.0|           1.0|                            1.0|
          |        2022-09-16|            a|                            1|                             1|                1|                          1|                 1|                           1|                    2.0|                         0.0|                                     0.5|           2.0|                            1.0|
          |        2022-09-17|            a|                            1|                             1|                2|                          2|                 2|                           2|                    2.0|                         0.0|                                     0.5|           2.0|                            1.0|
          """
      }

      val rateCenterNPATotalAddsWithRegionalMNOs = dataset[RateCenterNPATotalAddsWithRegionalMNOs] {
        """
          |customer_base_date|zip_rc_kblock|npa|npa_complex_cld|industry_model_sp_ind_current|industry_model_sp_ind_previous|current_holder_sp|current_holder_plan_type_id|previous_holder_sp|previous_holder_plan_type_id|national_ported_tn_adds|national_industry_model_adds|national_total_adds_to_ported_adds_ratio|ported_tn_adds|rate_center_npa_industry_model_adds|
          |        2022-09-15|            a|  0|              0|                            1|                             1|                1|                          1|                 0|                           0|                    0.0|                         0.0|                                     0.0|           0.0|                                0.0|
          |        2022-09-16|            a|  0|              1|                            1|                             1|                1|                          1|                 1|                           1|                    1.0|                         1.0|                                     1.0|           1.0|                                0.0|
          |        2022-09-16|            a|  0|              1|                            1|                             1|                1|                          1|                 1|                           1|                    1.0|                         1.0|                                     1.0|           2.0|                                0.0|
          |        2022-09-17|            a|  0|              2|                            1|                             1|                2|                          2|                 2|                           2|                    2.0|                         2.0|                                     2.0|           3.0|                                0.0|
          """
      }

      val nationalAddMultipliersActivationsIM = dataset[NationalAddMultipliersActivationsIM] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id|total_industry_model_adds|ported_industry_model_adds|non_ported_industry_model_adds|non_ported_switching_industry_model_adds|activations|activations_multiplier|
          |        2022-09-15|                1|                          1|                25517.178|        13389.026399999999|                    12128.1516|                              12128.1516|        0.0|                   0.0|
          |        2022-09-16|                1|                          1|                76251.994|                38328.0147|                    37923.9793|                              37923.9793|        0.0|                   0.0|
          |        2022-09-16|                1|                          1|                2740.8024|         538.1953599999999|                    2202.60704|                              2202.60704|        0.0|                   0.0|
          |        2022-09-17|                2|                          2|       14093.098399999999|                 2809.6425|                    11283.4559|                              11283.4559|        0.0|                   0.0|
          |        2022-09-17|                2|                          2|                89126.949|                 52488.109|            36638.840000000004|                      36638.840000000004|        0.0|                   0.0|
          """
      }

      val expectedData = dataset[FinalOutput] {
        """
          |zip_rc_kblock|current_holder_sp|current_holder_plan_type_id|industry_model_sp_ind|starting_customers|ported_starting_customers|losses_excl_intra_mno|ported_losses_excl_intra_mno|intra_mno_losses|ported_intra_mno_losses|total_losses|ported_total_losses|wins_excl_intra_mno|ported_wins_excl_intra_mno|intra_mno_wins|ported_intra_mno_wins|wins_excl_activations|ported_wins_excl_activations|activations_multiplier|activations|total_wins|ending_customers|ported_ending_customers|customer_base_date|
          |            a|                1|                          1|                    1|               0.0|                      1.0|                  0.0|                         5.0|             1.0|                    1.0|         1.0|                6.0|                0.0|                       0.0|           1.0|                  1.0|                  1.0|                         1.0|                   0.0|        0.0|       1.0|             0.0|                    1.0|        2022-09-15|
          |            a|                1|                          1|                    1|               2.0|                      5.0|                  2.0|                        10.0|             1.0|                    2.0|         3.0|               12.0|                0.0|                       3.0|           1.0|                  2.0|                  1.0|                         5.0|                   0.0|        0.0|       1.0|             1.0|                    3.0|        2022-09-16|
          |            a|                1|                          1|                    1|               2.0|                      5.0|                  2.0|                        10.0|             1.0|                    2.0|         3.0|               12.0|                0.0|                       3.0|           1.0|                  2.0|                  1.0|                         5.0|                   0.0|        0.0|       1.0|             1.0|                    3.0|        2022-09-16|
          |            a|                2|                          2|                    1|               0.0|                      4.0|                  0.0|                         5.0|             1.0|                    2.0|         1.0|                7.0|                0.0|                       3.0|           1.0|                  2.0|                  1.0|                         5.0|                   0.0|        0.0|       1.0|             0.0|                    4.0|        2022-09-17|
          |            a|                2|                          2|                    1|               0.0|                      4.0|                  0.0|                         5.0|             1.0|                    2.0|         1.0|                7.0|                0.0|                       3.0|           1.0|                  2.0|                  1.0|                         5.0|                   0.0|        0.0|       1.0|             0.0|                    4.0|        2022-09-17|
          |            a|                1|                          1|                    1|               0.0|                      1.0|                  0.0|                         5.0|            null|                   null|         0.0|                5.0|               null|                      null|          null|                 null|                  0.0|                         0.0|                  null|       null|      null|            null|                   null|        2022-10-15|
          |            a|                1|                          1|                    1|               1.0|                      3.0|                  1.0|                         5.0|            null|                   null|         1.0|                5.0|               null|                      null|          null|                 null|                  0.0|                         0.0|                  null|       null|      null|            null|                   null|        2022-10-16|
          |            a|                2|                          2|                    1|               0.0|                      4.0|                  0.0|                         5.0|            null|                   null|         0.0|                5.0|               null|                      null|          null|                 null|                  0.0|                         0.0|                  null|       null|      null|            null|                   null|        2022-10-17|
        """
      }
      val result = GenerateFinalOutput.apply().calculateFinalOutput(rateCenterNPATotalSubsWithRegionalMNOs, rateCenterTotalIntraMNOAdds, rateCenterNPATotalAddsWithRegionalMNOs, nationalAddMultipliersActivationsIM)

      assertDatasetEquals(expectedData.na.fill(0), result.na.fill(0))
    }
  }

}
