package com.comlinkdata.emrjobs.spark.wireless_market_share.inputs

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share._
import com.comlinkdata.largescale.schema.wireless_market_share.lookup._
import org.apache.spark.sql.{DataFrame, Dataset}

import java.sql.Date

class NationalCustomerBaseInputsSpec extends CldSparkBaseSpec {

  val startDate: Date = Date.valueOf("2017-07-01")
  val endDate: Date = Date.valueOf("2022-08-01")

  describe("XMSMTiebreaker") {
    it("Correct schema") {
      import spark.implicits._

      val expected = Seq(XMSMTiebreaker("test", 0)).toDF().as[XMSMTiebreaker]
      val tempDate: Date = Date.valueOf("2022-09-15")
      val custBase = Seq(MvnoCustomerBase(0, 0, 6052, 0, 0, "", "", 0, 0, 0, 0, 0.0, 0.0, 0.0, tempDate)).toDS()
      val result = NationalCustomerBaseInputs.apply()(spark).computeXMSMTiebreaker(custBase, tempDate)

      result.schema shouldBe expected.schema
    }
    it("Correct output") {
      import spark.implicits._

      val tempDate: Date = Date.valueOf("2021-12-01")

      val input = dataset[MvnoCustomerBase] {
        """
          |current_holder_sp|previous_holder_sp|current_holder_mvno_sp|previous_holder_mvno_sp|noncompetitive_ind|zip_rc_kblock|npa|mvno_losing_plan_type_id|mvno_winning_plan_type_id|current_tenure_months|tenancy_months|total_customers|total_wins|total_losses|customer_base_date|
          |1                |5                 |6105                  |5                      |0                 |91702        |626|1                       |2                        |0                    |10            |1.00           |0.00      |0.00        |2021-11-01|
          |1|2|6105|6576|0|43113|740|2|2|19|14|1.00|0.00|0.00|2021-12-01|
          |6105|3|6105|6577|0|91355|661|1|2|127|6|1.00|0.00|0.00|2021-12-01|
          |6105|6|6105|6|0|92704|657|1|2|0|0|0.00|1.00|0.00|2021-12-01|
          |6649|1113|6052|1113|0|37211|615|2|2|34|24|1.00|0.00|0.00|2021-12-01|
        """
      }
      val expectedData = dataset[XMSMTiebreaker] {
        """
          |zip_rc_kblock|current_holder_mvno_sp|
          |        43113|                  6105|
          |        91355|                  6105|
          |        92704|                  6105|
          |        37211|                  6052|
        """
      }

      val result = NationalCustomerBaseInputs.apply()(spark).computeXMSMTiebreaker(input, tempDate)
      assertDatasetEquals(expectedData, result)

      // TODO: manual confirmation works but should figure out automated
    }
  }

  describe("NationalSubscriberMultipliersPortedCustomerBaseSpec") {
    it("Correct schema") {
      import spark.implicits._

      val tempDate: Date = Date.valueOf("2022-09-15")
      val expected = Seq(NationalSubscriberMultipliersPortedCustomerBase(tempDate, 0, 0, 0, 0)).toDF().as[NationalSubscriberMultipliersPortedCustomerBase]
      val custBase = Seq(MvnoCustomerBase(0, 0, 5932,59320, 0, "", "", 0, 0, 0, 0, 0.0, 0.0, 0.0, tempDate)).toDS()
      val xmsmTiebreakerInput = Seq(XMSMTiebreaker("", 0)).toDS()
      val ocnInput = Seq(DCommonOcnLookup(0, "", "", 0, "", "")).toDS()
      val mvnoSPList1 = List[Int](1, 2)
      val mvnoSPList2 = List[Int](3, 4)
      val result = NationalCustomerBaseInputs().computeNationalSubscriberMultipliersPortedCustomerBase(custBase, xmsmTiebreakerInput, ocnInput, mvnoSPList1, mvnoSPList2, startDate, endDate)

      result.dtypes shouldBe expected.dtypes
    }
    it("Correct output") {
      import spark.implicits._

      val mvnoSPList1 = List[Int](5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6650, 6711, 6580)
      val mvnoSPList2 = List[Int](1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712)

      val custBaseInput = dataset[MvnoCustomerBase] {
        """
          |current_holder_sp|previous_holder_sp|current_holder_mvno_sp|previous_holder_mvno_sp|noncompetitive_ind|zip_rc_kblock|npa|mvno_losing_plan_type_id|mvno_winning_plan_type_id|current_tenure_months|tenancy_months|total_customers|total_wins|total_losses|customer_base_date|
          |1                |5                 |6105                  |5                      |0                 |91702        |626|1                       |2                        |0                    |10            |1.00           |0.00      |0.00        |2021-11-01|
          |1|2|6105|6576|0|43113|740|2|2|19|14|1.00|0.00|0.00|2021-12-01|
          |6105|3|6105|6577|0|91355|661|1|2|127|6|1.00|0.00|0.00|2021-12-01|
          |6105|6|6105|6|0|92704|657|1|2|0|0|0.00|1.00|0.00|2021-12-01|
          |6649|1113|6052|1113|0|37211|615|2|2|34|24|1.00|0.00|0.00|2021-12-01|
          """
      }

      val xmsmTiebreakerInput = dataset[XMSMTiebreaker] {
        """
          |zip_rc_kblock|current_holder_mvno_sp|
          |43113|6105|
          |91355|6105|
          |92704|6105|
          |37211|6052|
          """
      }

      val ocnInput = dataset[DCommonOcnLookup] {
        """
          |ocn_common_dim_id|common_name|common_name_mode|id6|changed|sp_reporting_nm|
          |1|Verizon Wireless|W|1|A|VZW|
          |2|AT&T Wireless|W|2|A|ATT|
          |3|Sprint Wireless|W|3|A|SPR|
          |4|T-Mobile Wireless|W|4|A|TMO|
          |5|TracFone Wireless|W|5|A|OTH|
          """
      }

      val expectedData = dataset[NationalSubscriberMultipliersPortedCustomerBase] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id|ported_tn_subscribers|losses|
          |        2021-11-01|             6105|                          2|                  1.0|   0.0|
          |        2021-12-01|             6105|                          2|                  2.0|   0.0|
          |        2021-12-01|             6052|                          2|                  1.0|   0.0|
        """
      }


      val result = NationalCustomerBaseInputs().computeNationalSubscriberMultipliersPortedCustomerBase(custBaseInput, xmsmTiebreakerInput, ocnInput, mvnoSPList1, mvnoSPList2, startDate, endDate)

      assertDatasetEquals(expectedData, result)
      // TODO: manual confirmation works but should figure out automated
    }
  }

  describe("NationalAddMultipliersPortedCustomerBaseInputs") {
    it("Correct schema") {
      import spark.implicits._

      val tempDate: Date = Date.valueOf("2022-09-15")
      val expected = Seq(NationalAddMultipliersPortedCustomerBase(tempDate, 0, 0, 0, 0, 0)).toDF().as[NationalAddMultipliersPortedCustomerBase]
      val custBase = Seq(MvnoCustomerBase(0,0,0,0,0,"","",0,0,0,0,0.0,0.0,0.0,tempDate)).toDF().as[MvnoCustomerBase]
      val tiebreaker = Seq(XMSMTiebreaker("a",0)).toDF().as[XMSMTiebreaker]
      val commonOCN = Seq(DCommonOcnLookup(0,"a","a",0,"a","a")).toDF().as[DCommonOcnLookup]
      val mvnoSPList1 = List[Int](5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6650, 6711, 6580)
      val mvnoSPList2 = List[Int](1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712)
      val result = NationalCustomerBaseInputs().computeNationalAddMultipliersPortedCustomerBase(custBase, tiebreaker, commonOCN, mvnoSPList1, mvnoSPList2, startDate, endDate)

      result.dtypes shouldBe expected.dtypes

    }
    it("Correct output") {
      import spark.implicits._


      val startDate: Date = Date.valueOf("2017-07-01")
      val endDate: Date = Date.valueOf("2022-08-01")

      val custBase = dataset[MvnoCustomerBase] {
        """
          |current_holder_sp|previous_holder_sp|current_holder_mvno_sp|previous_holder_mvno_sp|noncompetitive_ind|zip_rc_kblock|npa|mvno_losing_plan_type_id|mvno_winning_plan_type_id|current_tenure_months|tenancy_months|total_customers|total_wins|total_losses|customer_base_date|
          |              609|                 1|                   609|                      5|                 0|        30318|404|                       2|                        1|                    2|            31|            1.0|       2.0|         0.0|        2017-08-01|
          |                7|              1782|                     7|                   6712|                 0|        25043|304|                       0|                        2|                    0|             0|            0.0|       1.0|         0.0|        2017-08-01|
          |                5|                 1|                     5|                      6|                 0|        02909|401|                       5|                        5|                    1|            13|            1.0|       3.0|         0.0|        2017-08-01|
          |                5|                 1|                     5|                      8|                 0|        33311|954|                       5|                        5|                   93|            32|            1.0|       4.0|         0.0|        2017-08-01|
          |                2|                 4|                     2|                      8|                 0|        27292|336|                       2|                        2|                   14|            28|            1.0|       5.0|         0.0|        2017-08-01|
          |                4|                 1|                     4|                   1113|                 0|        02446|617|                       2|                        2|                    0|            12|            3.0|       0.0|         0.0|        2017-08-01|
          |                1|                 3|                     1|                   1113|                 0|        11368|347|                       1|                        2|                   45|            24|            1.0|       0.0|         0.0|        2017-08-01|
          |              609|                 6|                   609|                   6105|                 0|        77840|979|                       1|                        1|                   15|             3|            1.0|       0.0|         0.0|        2017-08-01|
          |                8|                 1|                     8|                   6105|                 0|        77396|281|                       5|                        1|                   26|            14|            1.0|       0.0|         0.0|        2017-08-01|
          |                1|                 2|                     1|                   6105|                 1|        29621|864|                       1|                        1|                    0|            48|          231.0|       0.0|         1.0|        2017-08-01|
          """

      }

      val xmsmTiebreaker = dataset[XMSMTiebreaker] {
        """
        |zip_rc_kblock|current_holder_mvno_sp|
        |30318|6105|
        |25043|6105|
        |02909|6105|
        |33311|6052|
        |27292|6651|
        |02446|6649|
        |11368|6052|
        |77840|2620|
        |77396|6043|
        |29621|6711|


                  """
      }

      val commonOCN = dataset[DCommonOcnLookup] {
        """
          |ocn_common_dim_id|common_name|common_name_mode|id6|changed|sp_reporting_nm|
          |                0|             NONE|                |  0|       |            OTH|
          |                1| Verizon Wireless|               W|  1|       |            VZW|
          |                2|    AT&T Wireless|               W|  2|       |            ATT|
          |                3|  Sprint Wireless|               W|  3|       |            SPR|
          |                4|T-Mobile Wireless|               W|  4|       |            TMO|
          |                5|TracFone Wireless|               W|  5|       |            OTH|
          |                6|         MetroPCS|               W|  6|       |            PCS|
          |                7|    U.S. Cellular|               W|  7|       |            USC|
          |                8|          Cricket|               W|  8|       |            LEP|
          |                9|          C Spire|               W|  9|       |            OTH|
          """
      }

      val expectedData = dataset[NationalAddMultipliersPortedCustomerBase] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id|previous_holder_sp|previous_holder_plan_type_id|adds|
          |        2017-08-01|              609|                          1|                 5|                           1| 2.0|
          |        2017-08-01|                7|                          2|                 7|                           2| 1.0|
          |        2017-08-01|                5|                          1|                 6|                           1| 3.0|
          |        2017-08-01|                5|                          1|                 8|                           1| 4.0|
          |        2017-08-01|                2|                          2|                 8|                           1| 5.0|
        """
      }

      val mvnoSPList1 = List[Int](5, 6, 8, 178, 609, 2620, 6042, 6043, 6050, 6544, 6545, 6546, 6547, 6650, 6711, 6580)
      val mvnoSPList2 = List[Int](1113, 6526, 6651, 6649, 6052, 6105, 6495, 6712)


      val result = NationalCustomerBaseInputs().computeNationalAddMultipliersPortedCustomerBase(custBase, xmsmTiebreaker, commonOCN, mvnoSPList1, mvnoSPList2, startDate, endDate)
      assertDatasetEquals(expectedData, result)

      // TODO: manual confirmation works but should figure out automated
    }
  }
  describe("NationalAddMultipliersIntraMNOInputs") {
    it("Correct schema") {
      import spark.implicits._

      val tempDate: Date = Date.valueOf("2022-09-15")
      val expected = Seq(NationalAddMultipliersIntraMnoInputs(tempDate, 0, 0, 0, 0, 0.0)).toDF().as[NationalAddMultipliersIntraMnoInputs]
      val wirelessMovement = Seq(WirelessMovementWideMonthlyAgg("a",0,0,0,0,0.0,tempDate)).toDF().as[WirelessMovementWideMonthlyAgg]
      val marketshareOutput = Seq(IndustryModelTotalOutput(0,0,"a","a",0,"a","a","a","a","a","a",0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)).toDF().as[IndustryModelTotalOutput]
      val result = NationalCustomerBaseInputs().computeNationalAddMultipliersIntraMnoInputs(wirelessMovement, marketshareOutput, startDate, endDate)

      result.dtypes shouldBe expected.dtypes

    }
    it("Correct output")  {
      import spark.implicits._

      val wirelessMovement = dataset[WirelessMovementWideMonthlyAgg] {
        """
          |zip_cd|primary_sp|secondary_sp|primary_plan_type_id|secondary_plan_type_id|      adjusted_wins|     month|
          | 41101|       609|           2|                   1|                     1|0.43757701925336867|2021-02-01|
          | 36322|       609|           2|                   1|                     2|                0.0|2021-02-01|
          | 02169|      6577|           2|                   2|                     2|                1.0|2021-02-01|
          | 98103|      6577|           2|                   2|                     2| 1.1141435283720935|2021-02-01|
          | 45505|       609|           2|                   1|                     2| 12.110000000000001|2021-02-01|
          | 60010|      6546|           7|                   1|                     1|                1.0|2021-02-01|
          | 45238|         3|           8|                   1|                     1|                0.0|2021-02-01|
          | 63334|         2|        6546|                   1|                     1|                1.0|2021-02-01|
          | 76009|         4|           1|                   2|                     2|                1.0|2021-02-01|
          | 20158|      6545|           3|                   1|                     2|                0.0|2021-02-01|
                  """
      }

      val marketshareOutput = dataset[IndustryModelTotalOutput] {
        """
          |year|month|cld_observed_loser|cld_observed_winner|quarter| begin_segment|begin_mno|begin_mvno|   end_segment|end_mno|end_mvno|diagonal|migration| port_est|      est|winning_losing_prop|winning_prop|losing_prop| top_prop|      prop|churn_losing_mno_sp|churn_winning_mno_sp|churn_losing_mvno_sp|churn_winning_mvno_sp|churn_losing_plan_type_id|churn_winning_plan_type_id|
          |2017|    1|               ATT|                BST|      1|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|11511.981| 18417.24|         0.30790854|  0.23039177|  0.3467255|0.3069786|0.30790854|                  2|                   2|                 609|                  609|                        2|                         1|
          |2017|    1|               ATT|                BST|      1| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|3767.8525|15683.629|         0.30790854|  0.23039177|  0.3467255|0.3069786|0.30790854|                  2|                   2|                   2|                  609|                        1|                         1|
          |2017|    1|               ATT|                PCS|      1|postpaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0| 43560.25|64641.297|          0.3383369|  0.28472412|  0.3467255|0.3069786| 0.3383369|                  2|                   2|                   6|                    6|                        2|                         1|
          |2017|    1|               ATT|                PCS|      1| prepaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0| 12468.31|41445.258|          0.3383369|  0.28472412|  0.3467255|0.3069786| 0.3383369|                  2|                   2|                   2|                    6|                        1|                         1|
          |2017|    1|               ATT|                SPR|      1|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0| 348.9449|1825.1088|          0.3762335|  0.36370814|  0.3467255|0.3069786| 0.3762335|                  2|                   2|                   2|                 6577|                        2|                         2|
          |2017|    1|               ATT|                SPR|      1| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0| 442.6422|2250.2527|          0.3762335|  0.36370814|  0.3467255|0.3069786| 0.3762335|                  2|                   2|                   2|                    2|                        1|                         2|
          |2017|    1|               ATT|                SPR|      1|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1234.7375|5977.2217|          0.3762335|  0.36370814|  0.3467255|0.3069786| 0.3762335|                  2|                   2|                   2|                    2|                        2|                         1|
          |2017|    1|               ATT|                SPR|      1| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1808.9415| 9659.043|          0.3762335|  0.36370814|  0.3467255|0.3069786| 0.3762335|                  2|                   2|                   2|                 6577|                        1|                         1|
          |2017|    1|               ATT|                SPR|      1|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 51811.45|57454.242|          0.3762335|  0.36370814|  0.3467255|0.3069786| 0.3762335|                  2|                   3|                   2|                    3|                        2|                         2|
          |2017|    1|               ATT|                SPR|      1| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 5315.234|22558.584|          0.3762335|  0.36370814|  0.3467255|0.3069786| 0.3762335|                  2|                   3|                   2|                    3|                        1|                         2|
                  """
      }

      val expectedData = dataset[NationalAddMultipliersIntraMnoInputs] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id|previous_holder_sp|previous_holder_plan_type_id|               adds|
          |        2021-02-01|              609|                          1|                 2|                           1|0.43757701925336867|
          |        2021-02-01|              609|                          1|                 2|                           2| 12.110000000000001|
          |        2021-02-01|             6577|                          2|                 2|                           2| 2.1141435283720935|
        """
      }

      val result = NationalCustomerBaseInputs().computeNationalAddMultipliersIntraMnoInputs(wirelessMovement, marketshareOutput, startDate, endDate)

      assertDatasetEquals(expectedData, result)
    }
  }
}

