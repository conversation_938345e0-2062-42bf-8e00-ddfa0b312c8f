package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model.{CmaFinalOutputAllDemo, CmaFinalOutputPairwiseDemo, DmaFinalOutputAllDemo, DmaFinalOutputPairwiseDemo, MsoIlecFootprintsFinalOutputAllDemo, MsoIlecFootprintsFinalOutputPairwiseDemo, VpgmFromDmaFinalOutputAllDemo, VpgmFromDmaFinalOutputPairwiseDemo}
import com.comlinkdata.largescale.schema.wireless_market_share.{BrandPlantypeLookup, CbgPctOfDmaGeorollups, MonthlyOutputWithEthnicityDMA}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.types.{StructField, StructType}
import org.apache.spark.sql.functions._

import java.sql.Date
import java.time.LocalDate


class PackagingSpec extends CldSparkBaseSpec {

  def setNullableStateForAllColumns(df: DataFrame, nullable: Boolean): DataFrame = {
    // get schema
    val schema = df.schema
    // modify [[StructField] with name `cn`
    val newSchema = StructType(schema.map {
      case StructField(c, t, _, m) ⇒ StructField(c, t, nullable = nullable, m)
    })
    // apply new schema
    df.sqlContext.createDataFrame(df.rdd, newSchema)
  }

  describe("dmaFinalOutputAllDemoCombinations") {

    import spark.implicits._

    it("test") {
      val dmaOutputData = setNullableStateForAllColumns(dataframe[MonthlyOutputWithEthnicityDMA] {
        """
          |wms_month |subs_join_month|losses_join_month|wins_join_month|dma|dma_name                        |brand_plantype         |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|age  |income  |subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses      |winner_from_average_ethnicity_pct_of_carrier|gross_adds        |base_adjustment|starting_subscribers|gross_add_rate       |churn_rate          |overall_gross_add_rate|overall_churn_rate   |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |511|Washington, DC-VA-MD-WV-PA (511)|XFINITY Mobile Postpaid|193803.6733      |5180.820901     |2218.336254       |0.0                  |190841.1886           |                      |Black    |55-74|$50-100K|4412.649898816132      |0.02312239987531913          |4478.105808977441 |0.02445519593993728           |54.24984775223647 |0.023094676215537166                        |119.64938123928253|0.0            |4412.706275490395   |0.027114739520247268 |0.012294008339861133|0.027147289005094784  |0.011623990975289912 |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |511|Washington, DC-VA-MD-WV-PA (511)|XFINITY Mobile Postpaid|193803.6733      |5180.820901     |2218.336254       |0.0                  |190841.1886           |                      |Black    |55-74|GT 125K |4856.738218808787      |0.025449434185832582         |4928.755964425787 |0.02692791901591969           |59.73517899779065 |0.025418920421741653                        |131.6908742018149 |0.0            |4856.800269221763   |0.027114739520247264 |0.012299286708646637|0.027147289005094784  |0.011623990975289912 |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |511|Washington, DC-VA-MD-WV-PA (511)|XFINITY Mobile Postpaid|193803.6733      |5180.820901     |2218.336254       |0.0                  |190841.1886           |                      |Black    |55-74|LT $50K |3484.9029229247885     |0.018260981668215735         |3536.596915848416 |0.019313561185683586          |42.84397297204912 |0.018239086828329552                        |94.49344225536355 |0.0            |3484.9474465651015  |0.027114739520247264 |0.012294008339861134|0.027147289005094784  |0.011623990975289912 |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |511|Washington, DC-VA-MD-WV-PA (511)|XFINITY Mobile Postpaid|193803.6733      |5180.820901     |2218.336254       |0.0                  |190841.1886           |                      |Black    |75 + |100-125K|896.116718594211       |0.0046956748387977105        |909.3969918621631 |0.0049719502497200165         |11.029457492038265|0.004690044744500426                        |24.29828183893301 |0.0            |896.1281675152684   |0.027114739520247268 |0.01230790180674724 |0.027147289005094784  |0.011623990975289912 |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |511|Washington, DC-VA-MD-WV-PA (511)|XFINITY Mobile Postpaid|193803.6733      |5180.820901     |2218.336254       |0.0                  |190841.1886           |                      |Black    |55-74|100-125K|3656.971342559576      |0.019162624648271145         |3711.2177462285554|0.02026717568348751           |44.95941058486757 |0.019139648742260405                        |99.15909224170106 |0.0            |3657.018064571722   |0.02711473952024727  |0.012294008339861133|0.027147289005094784  |0.011623990975289912 |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |511|Washington, DC-VA-MD-WV-PA (511)|XFINITY Mobile Postpaid|193803.6733      |5180.820901     |2218.336254       |0.0                  |190841.1886           |                      |Black    |75 + |$50-100K|876.8067626139486      |0.004594490168817383         |889.813048178975  |0.004859320742155182          |10.779607372137026|0.004588981394512003                        |23.77469072298791 |0.0            |876.8179648281241   |0.02711473952024726  |0.012294008339861136|0.027147289005094784  |0.011623990975289912 |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |75 + |$50-100K|185.80206844577182     |3.5247553815228016E-4        |185.8758033390309 |3.697018261925042E-4          |1.4059772472804324|3.623534552321808E-4                        |1.4383331642349406|0.0            |185.8434474220764   |0.007739488177747184 |0.007565385095807343|0.007528506272216101  |0.0072128753337167015|
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |75 + |LT $50K |79.76894628607145      |1.5132556114804218E-4        |79.80236177722136 |1.5825853640341462E-4         |0.6018577285989462|1.5556636989444656E-4                       |0.6175083080564223|0.0            |79.78671119776388   |0.0077394881777471825|0.007543332963144043|0.007528506272216101  |0.0072128753337167015|
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |55-74|LT $50K |1240.7354627871666     |0.0023537353680115982        |1240.9100653951323|0.0022997696072603498         |8.746031295261872 |0.0021777276601800586                       |8.644316401789775 |0.0            |1241.0117802886043  |0.006965539359972466 |0.007047500623425133|0.007528506272216101  |0.0072128753337167015|
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |18-34|LT $50K |1425.2439871989022     |0.0027037569903741645        |1425.3962983278868|0.002654455074771098          |10.09490128164239 |0.002501575353102309                        |9.92980401109815  |0.0            |1425.561395598431   |0.006965539359972466 |0.00708135146813841 |0.007528506272216101  |0.0072128753337167015|
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |55-74|GT 125K |3280.805659773406      |0.006223847507053915         |3281.3178713003854|0.006067864738422453          |23.076109332916715|0.005758440414779555                        |22.857670330592352|0.0            |3281.5363103027094  |0.006965539359972469 |0.007032105438073922|0.007528506272216101  |0.0072128753337167015|
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |35-54|GT 125K |4262.9218060681815     |0.008086969484591684         |4263.587349204262 |0.00788429294269134           |29.983991700775814|0.007482241790208169                        |29.70016251890082 |0.0            |4263.871178386137   |0.0069655393599724665|0.007032105438073921|0.007528506272216101  |0.0072128753337167015|
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |55-74|$50-100K|2188.5369258624005     |0.00415176072668227          |2188.722497510031 |0.004088758188385275          |15.549560687070828|0.0038413002140437256                       |15.247735082591213|0.0            |2189.024323114511   |0.0069655393599724665|0.007103420698837712|0.007528506272216101  |0.0072128753337167015|
        """
      }, nullable = true).as[MonthlyOutputWithEthnicityDMA]

      val brandPlantypeLookupData = setNullableStateForAllColumns(dataframe[BrandPlantypeLookup] {
        """
          |brand_plantype          |brand_plantype_raw       |brand           |plantype      |
          |Altice Postpaid         |Optimum Mobile Postpaid  |Optimum Mobile  |Postpaid Phone|
          |AT&T Postpaid           |AT&T Postpaid            |AT&T            |Postpaid Phone|
          |AT&T Prepaid            |AT&T Prepaid             |AT&T            |Prepaid Phone |
          |Boost Prepaid           |Boost Mobile Prepaid     |Boost Mobile    |Prepaid Phone |
          |Cricket Prepaid         |Cricket Wireless Prepaid |Cricket Wireless|Prepaid Phone |
          |Metro Prepaid           |MetroPCS Prepaid         |MetroPCS        |Prepaid Phone |
          |Other Wireless Postpaid |Other Postpaid           |Other           |Postpaid Phone|
          |Other Wireless Prepaid  |Other Prepaid            |Other           |Prepaid Phone |
          |Spectrum Mobile Postpaid|Spectrum Mobile Postpaid |Spectrum Mobile |Postpaid Phone|
          |T-Mobile Postpaid       |T-Mobile Postpaid        |T-Mobile        |Postpaid Phone|
          |T-Mobile Prepaid        |T-Mobile Prepaid         |T-Mobile        |Prepaid Phone |
          |Tracfone Prepaid        |Tracfone Prepaid         |Tracfone        |Prepaid Phone |
          |U.S. Cellular Postpaid  |U.S. Cellular Postpaid   |U.S. Cellular   |Postpaid Phone|
          |U.S. Cellular Prepaid   |U.S. Cellular Prepaid    |U.S. Cellular   |Prepaid Phone |
          |Verizon Postpaid        |Verizon Wireless Postpaid|Verizon         |Postpaid Phone|
          |Verizon Prepaid         |Verizon Wireless Prepaid |Verizon         |Prepaid Phone |
          |XFINITY Mobile Postpaid |Xfinity Mobile Postpaid  |Xfinity Mobile  |Postpaid Phone|
          |Cox Postpaid            |Cox Mobile Postpaid      |Cox Mobile      |Postpaid Phone|
        """
      }, nullable = true).as[BrandPlantypeLookup]

      val expected = setNullableStateForAllColumns(dataframe[DmaFinalOutputAllDemo] {
        """
          |month     |geography                       |geography_type|brand         |plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |75 + |$50K-$100K |186        |1         |1           |0              |2025-06-12   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |75 + |<$50K      |80         |1         |1           |0              |2025-06-12   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |55-74|<$50K      |1241       |9         |9           |0              |2025-06-12   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |18-34|<$50K      |1425       |10        |10          |0              |2025-06-12   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |55-74|$125K+     |3281       |23        |23          |0              |2025-06-12   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |35-54|$125K+     |4264       |30        |30          |0              |2025-06-12   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |55-74|$50K-$100K |2189       |15        |16          |0              |2025-06-12   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|Black    |55-74|$50K-$100K |4478       |120       |54          |0              |2025-06-12   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|Black    |55-74|$125K+     |4929       |132       |60          |0              |2025-06-12   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|Black    |55-74|<$50K      |3537       |94        |43          |0              |2025-06-12   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|Black    |75 + |$100K-$125K|909        |24        |11          |0              |2025-06-12   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|Black    |55-74|$100K-$125K|3711       |99        |45          |0              |2025-06-12   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|Black    |75 + |$50K-$100K |890        |24        |11          |0              |2025-06-12   |
        """
      }.withColumn("creation_date", current_date()), nullable = true).as[DmaFinalOutputAllDemo]

      val monthToProcess = LocalDate.of(2024, 6, 1)

      val result = setNullableStateForAllColumns(Packaging().dmaFinalOutputAllDemo(
        dmaOutputData, brandPlantypeLookupData, monthToProcess
      ).toDF(), nullable = true).as[DmaFinalOutputAllDemo]

      // result.show(Int.MaxValue, truncate = false)

      // Count should be the same
      result.count() shouldEqual expected.count()

      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }


  describe("vpgmFromDmaFinalOutputAllDemoCombinations") {

    import spark.implicits._

    it("test") {
      val dmaFinalOutputData = setNullableStateForAllColumns(dataframe[DmaFinalOutputAllDemo] {
        """
          |month     |geography               |geography_type|brand           |plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|Los Angeles, CA-NV (803)|DMA           |AT&T            |Postpaid Phone|Asian    |18-34|$100K-$125K|39017      |349       |287         |0              |2024-07-09   |
          |2024-06-01|Los Angeles, CA-NV (803)|DMA           |AT&T            |Postpaid Phone|Asian    |35-54|$125K+     |64969      |581       |477         |0              |2024-07-09   |
          |2024-06-01|New York, NY (501)      |DMA           |Verizon         |Postpaid Phone|Hispanic |18-34|$100K-$125K|24567      |300       |250         |0              |2024-07-09   |
          |2024-06-01|New York, NY (501)      |DMA           |Verizon         |Postpaid Phone|Hispanic |35-54|$50K-$100K |36045      |420       |370         |0              |2024-07-09   |
          |2024-06-01|Chicago, IL (602)       |DMA           |Cricket Wireless|Prepaid Phone |Black    |18-34|$50K-$100K |12890      |150       |120         |0              |2024-07-09   |
          |2024-06-01|Chicago, IL (602)       |DMA           |Cricket Wireless|Prepaid Phone |Black    |35-54|$100K-$125K|18745      |210       |190         |0              |2024-07-09   |
        """
      }, nullable = true).as[DmaFinalOutputAllDemo]

      val cbgPctOfDmaGeorollupsData = setNullableStateForAllColumns(dataframe[CbgPctOfDmaGeorollups] {
        """
          |month     |cbg         |dma|geography               |geography_type|brand_plantype|brand           |plan_type     |ethnicity|age  |income     |ethnicity_raw|age_raw|income_raw|subs  |dma_total_subs |cbg_pct_dma_total |cma                   |cma_name                                             |comcast_mso|spectrum_mso|altice_mso|cox_mso|att_fiber|att_ilec|verizon_fiber|verizon_ilec|lumen_fiber|lumen_ilec|brightspeed_fiber|brightspeed_ilec|frontier_fiber|frontier_ilec|windstream_fiber|windstream_ilec|county|vpgm               |boost_market |
          |2024-06-01|410510082011|803|Los Angeles, CA-NV (803)|DMA           |Postpaid Phone|AT&T            |Postpaid Phone|Asian    |18-34|$100K-$125K|Asian        |18-34  |100-125k  |1.0   |100.0          |0.01              |Los Angeles-Long Beach|Los Angeles-Long Beach/Anaheim-Santa Ana-Garden Grove|1          |0           |0         |0      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |6037  |Southern California|LA Metro     |
          |2024-06-01|410510082011|803|Los Angeles, CA-NV (803)|DMA           |Postpaid Phone|AT&T            |Postpaid Phone|Asian    |35-54|$125K+     |Asian        |35-54  |GT 125k   |1.0   |100.0          |0.01              |Los Angeles-Long Beach|Los Angeles-Long Beach/Anaheim-Santa Ana-Garden Grove|1          |0           |0         |0      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |6037  |Southern California|LA Metro     |
          |2024-06-01|510410088011|501|New York, NY (501)      |DMA           |Postpaid Phone|Verizon         |Postpaid Phone|Hispanic |18-34|$100K-$125K|Hispanic     |18-34  |100-125k  |1.0   |100.0          |0.01              |New York              |New York-Northern New Jersey-Long Island             |1          |1           |0         |0      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |51041 |Northeast          |NY Metro     |
          |2024-06-01|510410088012|501|New York, NY (501)      |DMA           |Postpaid Phone|Verizon         |Postpaid Phone|Hispanic |35-54|$50K-$100K |Hispanic     |35-54  |50-100k   |1.0   |100.0          |0.01              |New York              |New York-Northern New Jersey-Long Island             |1          |1           |0         |0      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |51042 |Northeast          |NY Metro     |
          |2024-06-01|620310065001|602|Chicago, IL (602)       |DMA           |Prepaid Phone |Cricket Wireless|Prepaid Phone |Black    |18-34|$50K-$100K |Black        |18-34  |50-100k   |1.0   |100.0          |0.01              |Chicago               |Chicago-Naperville-Elgin                             |0          |1           |0         |1      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |62031 |Midwest            |Chicago Metro|
          |2024-06-01|620310065002|602|Chicago, IL (602)       |DMA           |Prepaid Phone |Cricket Wireless|Prepaid Phone |Black    |35-54|$100K-$125K|Black        |35-54  |100-125k  |1.0   |100.0          |0.01              |Chicago               |Chicago-Naperville-Elgin                             |0          |1           |0         |1      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |62032 |Midwest            |Chicago Metro|
       """
      }, nullable = true).as[CbgPctOfDmaGeorollups]

      val expected = setNullableStateForAllColumns(dataframe[VpgmFromDmaFinalOutputAllDemo] {
        """
          |month     |geography          |geography_type|brand           |plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|Midwest            |VPGM          |Cricket Wireless|Prepaid Phone |Black    |18-34|$50K-$100K |129        |2         |1           |0              |2025-06-13   |
          |2024-06-01|Midwest            |VPGM          |Cricket Wireless|Prepaid Phone |Black    |35-54|$100K-$125K|187        |2         |2           |0              |2025-06-13   |
          |2024-06-01|Southern California|VPGM          |AT&T            |Postpaid Phone|Asian    |18-34|$100K-$125K|390        |3         |3           |0              |2025-06-13   |
          |2024-06-01|Southern California|VPGM          |AT&T            |Postpaid Phone|Asian    |35-54|$125K+     |650        |6         |5           |0              |2025-06-13   |
          |2024-06-01|Northeast          |VPGM          |Verizon         |Postpaid Phone|Hispanic |18-34|$100K-$125K|246        |3         |3           |0              |2025-06-13   |
          |2024-06-01|Northeast          |VPGM          |Verizon         |Postpaid Phone|Hispanic |35-54|$50K-$100K |360        |4         |4           |0              |2025-06-13   |
        """
      }.withColumn("creation_date", current_date()), nullable = true).as[VpgmFromDmaFinalOutputAllDemo]

      val monthToProcess = LocalDate.of(2024, 6, 1)

      val result = setNullableStateForAllColumns(Packaging().vpgmFromDmaFinalOutputAllDemo(
        dmaFinalOutputData, cbgPctOfDmaGeorollupsData, monthToProcess
      ).toDF(), nullable = true).as[VpgmFromDmaFinalOutputAllDemo]

      // result.show(Int.MaxValue, truncate = false)

      // Count should be the same
      result.count() shouldEqual expected.count()

      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }


  describe("msoIlecFootprintsFinalOutputAllDemoCombinations") {

    import spark.implicits._

    it("test") {
      val dmaFinalOutputData = setNullableStateForAllColumns(dataframe[DmaFinalOutputAllDemo] {
        """
          |month     |geography               |geography_type|brand|plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|Los Angeles, CA-NV (803)|DMA           |AT&T |Postpaid Phone|Asian    |18-34|$100K-$125K|39017      |349       |287         |0              |2024-07-09   |
          |2024-06-01|Los Angeles, CA-NV (803)|DMA           |AT&T |Postpaid Phone|Asian    |75 + |<$50K      |1864       |19        |15          |0              |2024-07-09   |
          |2024-06-01|Los Angeles, CA-NV (803)|DMA           |AT&T |Postpaid Phone|Asian    |35-54|$125K+     |64969      |581       |477         |0              |2024-07-09   |
          |2024-06-01|Los Angeles, CA-NV (803)|DMA           |AT&T |Postpaid Phone|Asian    |18-34|$125K+     |54692      |489       |402         |0              |2024-07-09   |
          |2024-06-01|Los Angeles, CA-NV (803)|DMA           |AT&T |Postpaid Phone|Asian    |18-34|$50K-$100K |45349      |406       |333         |0              |2024-07-09   |
        """

      }, nullable = true).as[DmaFinalOutputAllDemo]

      val cbgPctOfDmaGeorollupsData = setNullableStateForAllColumns(dataframe[CbgPctOfDmaGeorollups] {
        """
          |month     |cbg         |dma|geography               |geography_type|brand_plantype|brand           |plan_type     |ethnicity|age  |income     |ethnicity_raw|age_raw|income_raw|subs  |dma_total_subs |cbg_pct_dma_total |cma                   |cma_name                                             |comcast_mso|spectrum_mso|altice_mso|cox_mso|att_fiber|att_ilec|verizon_fiber|verizon_ilec|lumen_fiber|lumen_ilec|brightspeed_fiber|brightspeed_ilec|frontier_fiber|frontier_ilec|windstream_fiber|windstream_ilec|county|vpgm               |boost_market |
          |2024-06-01|410510082011|803|Los Angeles, CA-NV (803)|DMA           |Postpaid Phone|AT&T            |Postpaid Phone|Asian    |18-34|$100K-$125K|Asian        |18-34  |100-125k  |1.0   |100.0          |0.01              |Los Angeles-Long Beach|Los Angeles-Long Beach/Anaheim-Santa Ana-Garden Grove|1          |0           |0         |0      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |6037  |Southern California|LA Metro     |
          |2024-06-01|410510082011|803|Los Angeles, CA-NV (803)|DMA           |Postpaid Phone|AT&T            |Postpaid Phone|Asian    |35-54|$125K+     |Asian        |35-54  |GT 125k   |1.0   |100.0          |0.01              |Los Angeles-Long Beach|Los Angeles-Long Beach/Anaheim-Santa Ana-Garden Grove|1          |0           |0         |0      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |6037  |Southern California|LA Metro     |
          |2024-06-01|510410088011|501|New York, NY (501)      |DMA           |Postpaid Phone|Verizon         |Postpaid Phone|Hispanic |18-34|$100K-$125K|Hispanic     |18-34  |100-125k  |1.0   |100.0          |0.01              |New York              |New York-Northern New Jersey-Long Island             |1          |1           |0         |0      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |51041 |Northeast          |NY Metro     |
          |2024-06-01|510410088012|501|New York, NY (501)      |DMA           |Postpaid Phone|Verizon         |Postpaid Phone|Hispanic |35-54|$50K-$100K |Hispanic     |35-54  |50-100k   |1.0   |100.0          |0.01              |New York              |New York-Northern New Jersey-Long Island             |1          |1           |0         |0      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |51042 |Northeast          |NY Metro     |
          |2024-06-01|620310065001|602|Chicago, IL (602)       |DMA           |Prepaid Phone |Cricket Wireless|Prepaid Phone |Black    |18-34|$50K-$100K |Black        |18-34  |50-100k   |1.0   |100.0          |0.01              |Chicago               |Chicago-Naperville-Elgin                             |0          |1           |0         |1      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |62031 |Midwest            |Chicago Metro|
          |2024-06-01|620310065002|602|Chicago, IL (602)       |DMA           |Prepaid Phone |Cricket Wireless|Prepaid Phone |Black    |35-54|$100K-$125K|Black        |35-54  |100-125k  |1.0   |100.0          |0.01              |Chicago               |Chicago-Naperville-Elgin                             |0          |1           |0         |1      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |62032 |Midwest            |Chicago Metro|
       """
      }, nullable = true).as[CbgPctOfDmaGeorollups]

      val expected = setNullableStateForAllColumns(dataframe[MsoIlecFootprintsFinalOutputAllDemo] {
        """
          |month     |geography  |geography_type     |brand|plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T |Postpaid Phone|Asian    |18-34|$100K-$125K|390        |3         |3           |0              |2024-07-09   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T |Postpaid Phone|Asian    |35-54|$125K+     |650        |6         |5           |0              |2024-07-09   |
          |2024-06-01|AT&T_ILEC  |Broadband_footprint|AT&T |Postpaid Phone|Asian    |18-34|$100K-$125K|390        |3         |3           |0              |2024-07-09   |
          |2024-06-01|AT&T_ILEC  |Broadband_footprint|AT&T |Postpaid Phone|Asian    |35-54|$125K+     |650        |6         |5           |0              |2024-07-09   |
        """
      }, nullable = true).as[MsoIlecFootprintsFinalOutputAllDemo]

      val creationDate = LocalDate.of(2024, 6, 1).toString

      val result = setNullableStateForAllColumns(Packaging().msoIlecFootprintsFinalOutputAllDemo(
        dmaFinalOutputData, cbgPctOfDmaGeorollupsData, creationDate
      ).toDF(), nullable = true).as[MsoIlecFootprintsFinalOutputAllDemo]

      // result.show(Int.MaxValue, truncate = false)

      // Count should be the same
      result.count() shouldEqual expected.count()

      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("cmaFinalOutputAllDemoCombinations") {

    import spark.implicits._

    it("test") {
      val dmaFinalOutputData = setNullableStateForAllColumns(dataframe[DmaFinalOutputAllDemo] {
        """
          |month     |geography               |geography_type|brand           |plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|Los Angeles, CA-NV (803)|DMA           |AT&T            |Postpaid Phone|Asian    |18-34|$100K-$125K|39017      |349       |287         |0              |2024-07-09   |
          |2024-06-01|Los Angeles, CA-NV (803)|DMA           |AT&T            |Postpaid Phone|Asian    |35-54|$125K+     |64969      |581       |477         |0              |2024-07-09   |
          |2024-06-01|New York, NY (501)      |DMA           |Verizon         |Postpaid Phone|Hispanic |18-34|$100K-$125K|24567      |300       |250         |0              |2024-07-09   |
          |2024-06-01|New York, NY (501)      |DMA           |Verizon         |Postpaid Phone|Hispanic |35-54|$50K-$100K |36045      |420       |370         |0              |2024-07-09   |
          |2024-06-01|Chicago, IL (602)       |DMA           |Cricket Wireless|Prepaid Phone |Black    |18-34|$50K-$100K |12890      |150       |120         |0              |2024-07-09   |
          |2024-06-01|Chicago, IL (602)       |DMA           |Cricket Wireless|Prepaid Phone |Black    |35-54|$100K-$125K|18745      |210       |190         |0              |2024-07-09   |
        """

      }, nullable = true).as[DmaFinalOutputAllDemo]

      val cbgPctOfDmaGeorollupsData = setNullableStateForAllColumns(dataframe[CbgPctOfDmaGeorollups] {
        """
          |month     |cbg         |dma|geography               |geography_type|brand_plantype|brand           |plan_type     |ethnicity|age  |income     |ethnicity_raw|age_raw|income_raw|subs  |dma_total_subs |cbg_pct_dma_total |cma                   |cma_name                                             |comcast_mso|spectrum_mso|altice_mso|cox_mso|att_fiber|att_ilec|verizon_fiber|verizon_ilec|lumen_fiber|lumen_ilec|brightspeed_fiber|brightspeed_ilec|frontier_fiber|frontier_ilec|windstream_fiber|windstream_ilec|county|vpgm               |boost_market |
          |2024-06-01|410510082011|803|Los Angeles, CA-NV (803)|DMA           |Postpaid Phone|AT&T            |Postpaid Phone|Asian    |18-34|$100K-$125K|Asian        |18-34  |100-125k  |1.0   |100.0          |0.01              |Los Angeles-Long Beach|Los Angeles-Long Beach/Anaheim-Santa Ana-Garden Grove|1          |0           |0         |0      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |6037  |Southern California|LA Metro     |
          |2024-06-01|410510082011|803|Los Angeles, CA-NV (803)|DMA           |Postpaid Phone|AT&T            |Postpaid Phone|Asian    |35-54|$125K+     |Asian        |35-54  |GT 125k   |1.0   |100.0          |0.01              |Los Angeles-Long Beach|Los Angeles-Long Beach/Anaheim-Santa Ana-Garden Grove|1          |0           |0         |0      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |6037  |Southern California|LA Metro     |
          |2024-06-01|510410088011|501|New York, NY (501)      |DMA           |Postpaid Phone|Verizon         |Postpaid Phone|Hispanic |18-34|$100K-$125K|Hispanic     |18-34  |100-125k  |1.0   |100.0          |0.01              |New York              |New York-Northern New Jersey-Long Island             |1          |1           |0         |0      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |51041 |Northeast          |NY Metro     |
          |2024-06-01|510410088012|501|New York, NY (501)      |DMA           |Postpaid Phone|Verizon         |Postpaid Phone|Hispanic |35-54|$50K-$100K |Hispanic     |35-54  |50-100k   |1.0   |100.0          |0.01              |New York              |New York-Northern New Jersey-Long Island             |1          |1           |0         |0      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |51042 |Northeast          |NY Metro     |
          |2024-06-01|620310065001|602|Chicago, IL (602)       |DMA           |Prepaid Phone |Cricket Wireless|Prepaid Phone |Black    |18-34|$50K-$100K |Black        |18-34  |50-100k   |1.0   |100.0          |0.01              |Chicago               |Chicago-Naperville-Elgin                             |0          |1           |0         |1      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |62031 |Midwest            |Chicago Metro|
          |2024-06-01|620310065002|602|Chicago, IL (602)       |DMA           |Prepaid Phone |Cricket Wireless|Prepaid Phone |Black    |35-54|$100K-$125K|Black        |35-54  |100-125k  |1.0   |100.0          |0.01              |Chicago               |Chicago-Naperville-Elgin                             |0          |1           |0         |1      |0        |1       |0            |0           |0          |0         |0                |0               |0             |0            |0               |0              |62032 |Midwest            |Chicago Metro|
       """
      }, nullable = true).as[CbgPctOfDmaGeorollups]

      val expected = setNullableStateForAllColumns(dataframe[CmaFinalOutputAllDemo] {
        """
          |month     |geography                                                                     |geography_type|brand           |plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|Chicago-Naperville-Elgin (Chicago)                                            |CMA           |Cricket Wireless|Prepaid Phone |Black    |18-34|$50K-$100K |129        |2         |1           |0              |2024-07-09   |
          |2024-06-01|Chicago-Naperville-Elgin (Chicago)                                            |CMA           |Cricket Wireless|Prepaid Phone |Black    |35-54|$100K-$125K|187        |2         |2           |0              |2024-07-09   |
          |2024-06-01|Los Angeles-Long Beach/Anaheim-Santa Ana-Garden Grove (Los Angeles-Long Beach)|CMA           |AT&T            |Postpaid Phone|Asian    |18-34|$100K-$125K|390        |3         |3           |0              |2024-07-09   |
          |2024-06-01|Los Angeles-Long Beach/Anaheim-Santa Ana-Garden Grove (Los Angeles-Long Beach)|CMA           |AT&T            |Postpaid Phone|Asian    |35-54|$125K+     |650        |6         |5           |0              |2024-07-09   |
          |2024-06-01|New York-Northern New Jersey-Long Island (New York)                           |CMA           |Verizon         |Postpaid Phone|Hispanic |18-34|$100K-$125K|246        |3         |3           |0              |2024-07-09   |
          |2024-06-01|New York-Northern New Jersey-Long Island (New York)                           |CMA           |Verizon         |Postpaid Phone|Hispanic |35-54|$50K-$100K |360        |4         |4           |0              |2024-07-09   |
        """
      }, nullable = true).as[CmaFinalOutputAllDemo]

      val creationDate = LocalDate.of(2024, 6, 1).toString

      val result = setNullableStateForAllColumns(Packaging().cmaFinalOutputAllDemo(
        dmaFinalOutputData, cbgPctOfDmaGeorollupsData, creationDate
      ).toDF(), nullable = true).as[CmaFinalOutputAllDemo]

      // result.show(Int.MaxValue, truncate = false)

      // Count should be the same
      result.count() shouldEqual expected.count()

      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("packaging pairwise demographis Dma final output pairwise") {

    import spark.implicits._

    it("test") {
      val dmaOutputData = setNullableStateForAllColumns(dataframe[MonthlyOutputWithEthnicityDMA] {
        """
          |wms_month |subs_join_month|losses_join_month|wins_join_month|dma|dma_name                        |brand_plantype         |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|age  |income  |subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses      |winner_from_average_ethnicity_pct_of_carrier|gross_adds        |base_adjustment|starting_subscribers|gross_add_rate       |churn_rate          |overall_gross_add_rate|overall_churn_rate   |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |511|Washington, DC-VA-MD-WV-PA (511)|XFINITY Mobile Postpaid|193803.6733      |5180.820901     |2218.336254       |0.0                  |190841.1886           |                      |Black    |55-74|$50-100K|4412.649898816132      |0.02312239987531913          |4478.105808977441 |0.02445519593993728           |54.24984775223647 |0.023094676215537166                        |119.64938123928253|0.0            |4412.706275490395   |0.027114739520247268 |0.012294008339861133|0.027147289005094784  |0.011623990975289912 |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |511|Washington, DC-VA-MD-WV-PA (511)|XFINITY Mobile Postpaid|193803.6733      |5180.820901     |2218.336254       |0.0                  |190841.1886           |                      |Black    |55-74|GT 125K |4856.738218808787      |0.025449434185832582         |4928.755964425787 |0.02692791901591969           |59.73517899779065 |0.025418920421741653                        |131.6908742018149 |0.0            |4856.800269221763   |0.027114739520247264 |0.012299286708646637|0.027147289005094784  |0.011623990975289912 |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |511|Washington, DC-VA-MD-WV-PA (511)|XFINITY Mobile Postpaid|193803.6733      |5180.820901     |2218.336254       |0.0                  |190841.1886           |                      |Black    |55-74|LT $50K |3484.9029229247885     |0.018260981668215735         |3536.596915848416 |0.019313561185683586          |42.84397297204912 |0.018239086828329552                        |94.49344225536355 |0.0            |3484.9474465651015  |0.027114739520247264 |0.012294008339861134|0.027147289005094784  |0.011623990975289912 |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |511|Washington, DC-VA-MD-WV-PA (511)|XFINITY Mobile Postpaid|193803.6733      |5180.820901     |2218.336254       |0.0                  |190841.1886           |                      |Black    |75 + |100-125K|896.116718594211       |0.0046956748387977105        |909.3969918621631 |0.0049719502497200165         |11.029457492038265|0.004690044744500426                        |24.29828183893301 |0.0            |896.1281675152684   |0.027114739520247268 |0.01230790180674724 |0.027147289005094784  |0.011623990975289912 |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |511|Washington, DC-VA-MD-WV-PA (511)|XFINITY Mobile Postpaid|193803.6733      |5180.820901     |2218.336254       |0.0                  |190841.1886           |                      |Black    |55-74|100-125K|3656.971342559576      |0.019162624648271145         |3711.2177462285554|0.02026717568348751           |44.95941058486757 |0.019139648742260405                        |99.15909224170106 |0.0            |3657.018064571722   |0.02711473952024727  |0.012294008339861133|0.027147289005094784  |0.011623990975289912 |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |511|Washington, DC-VA-MD-WV-PA (511)|XFINITY Mobile Postpaid|193803.6733      |5180.820901     |2218.336254       |0.0                  |190841.1886           |                      |Black    |75 + |$50-100K|876.8067626139486      |0.004594490168817383         |889.813048178975  |0.004859320742155182          |10.779607372137026|0.004588981394512003                        |23.77469072298791 |0.0            |876.8179648281241   |0.02711473952024726  |0.012294008339861136|0.027147289005094784  |0.011623990975289912 |
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |75 + |$50-100K|185.80206844577182     |3.5247553815228016E-4        |185.8758033390309 |3.697018261925042E-4          |1.4059772472804324|3.623534552321808E-4                        |1.4383331642349406|0.0            |185.8434474220764   |0.007739488177747184 |0.007565385095807343|0.007528506272216101  |0.0072128753337167015|
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |75 + |LT $50K |79.76894628607145      |1.5132556114804218E-4        |79.80236177722136 |1.5825853640341462E-4         |0.6018577285989462|1.5556636989444656E-4                       |0.6175083080564223|0.0            |79.78671119776388   |0.0077394881777471825|0.007543332963144043|0.007528506272216101  |0.0072128753337167015|
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |55-74|LT $50K |1240.7354627871666     |0.0023537353680115982        |1240.9100653951323|0.0022997696072603498         |8.746031295261872 |0.0021777276601800586                       |8.644316401789775 |0.0            |1241.0117802886043  |0.006965539359972466 |0.007047500623425133|0.007528506272216101  |0.0072128753337167015|
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |18-34|LT $50K |1425.2439871989022     |0.0027037569903741645        |1425.3962983278868|0.002654455074771098          |10.09490128164239 |0.002501575353102309                        |9.92980401109815  |0.0            |1425.561395598431   |0.006965539359972466 |0.00708135146813841 |0.007528506272216101  |0.0072128753337167015|
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |55-74|GT 125K |3280.805659773406      |0.006223847507053915         |3281.3178713003854|0.006067864738422453          |23.076109332916715|0.005758440414779555                        |22.857670330592352|0.0            |3281.5363103027094  |0.006965539359972469 |0.007032105438073922|0.007528506272216101  |0.0072128753337167015|
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |35-54|GT 125K |4262.9218060681815     |0.008086969484591684         |4263.587349204262 |0.00788429294269134           |29.983991700775814|0.007482241790208169                        |29.70016251890082 |0.0            |4263.871178386137   |0.0069655393599724665|0.007032105438073921|0.007528506272216101  |0.0072128753337167015|
          |2024-06-01|2024-06-01     |2024-06-01       |2024-06-01     |512|Baltimore, MD (512)             |AT&T Postpaid          |527418.4596      |3969.420309     |3803.003252       |0.0                  |527252.0425           |                      |Asian    |55-74|$50-100K|2188.5369258624005     |0.00415176072668227          |2188.722497510031 |0.004088758188385275          |15.549560687070828|0.0038413002140437256                       |15.247735082591213|0.0            |2189.024323114511   |0.0069655393599724665|0.007103420698837712|0.007528506272216101  |0.0072128753337167015|
        """
      }, nullable = true).as[MonthlyOutputWithEthnicityDMA]

      val brandPlantypeLookupData = setNullableStateForAllColumns(dataframe[BrandPlantypeLookup] {
        """
          |brand_plantype          |brand_plantype_raw       |brand           |plantype      |
          |Altice Postpaid         |Optimum Mobile Postpaid  |Optimum Mobile  |Postpaid Phone|
          |AT&T Postpaid           |AT&T Postpaid            |AT&T            |Postpaid Phone|
          |AT&T Prepaid            |AT&T Prepaid             |AT&T            |Prepaid Phone |
          |Boost Prepaid           |Boost Mobile Prepaid     |Boost Mobile    |Prepaid Phone |
          |Cricket Prepaid         |Cricket Wireless Prepaid |Cricket Wireless|Prepaid Phone |
          |Metro Prepaid           |MetroPCS Prepaid         |MetroPCS        |Prepaid Phone |
          |Other Wireless Postpaid |Other Postpaid           |Other           |Postpaid Phone|
          |Other Wireless Prepaid  |Other Prepaid            |Other           |Prepaid Phone |
          |Spectrum Mobile Postpaid|Spectrum Mobile Postpaid |Spectrum Mobile |Postpaid Phone|
          |T-Mobile Postpaid       |T-Mobile Postpaid        |T-Mobile        |Postpaid Phone|
          |T-Mobile Prepaid        |T-Mobile Prepaid         |T-Mobile        |Prepaid Phone |
          |Tracfone Prepaid        |Tracfone Prepaid         |Tracfone        |Prepaid Phone |
          |U.S. Cellular Postpaid  |U.S. Cellular Postpaid   |U.S. Cellular   |Postpaid Phone|
          |U.S. Cellular Prepaid   |U.S. Cellular Prepaid    |U.S. Cellular   |Prepaid Phone |
          |Verizon Postpaid        |Verizon Wireless Postpaid|Verizon         |Postpaid Phone|
          |Verizon Prepaid         |Verizon Wireless Prepaid |Verizon         |Prepaid Phone |
          |XFINITY Mobile Postpaid |Xfinity Mobile Postpaid  |Xfinity Mobile  |Postpaid Phone|
          |Cox Postpaid            |Cox Mobile Postpaid      |Cox Mobile      |Postpaid Phone|
        """
      }, nullable = true).as[BrandPlantypeLookup]

      val expected = setNullableStateForAllColumns(dataframe[DmaFinalOutputPairwiseDemo] {
        """
          |month     |geography                       |geography_type|brand         |plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |75 + |All        |266        |2         |2           |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |55-74|All        |6711       |47        |48          |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |18-34|All        |1425       |10        |10          |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |35-54|All        |4264       |30        |30          |0              |2024-09-10   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|Black    |55-74|All        |16655      |445       |202         |0              |2024-09-10   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|Black    |75 + |All        |1799       |48        |22          |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |All  |$50K-$100K |2375       |16        |17          |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |All  |<$50K      |2746       |20        |20          |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|Asian    |All  |$125K+     |7545       |53        |53          |0              |2024-09-10   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|Black    |All  |$50K-$100K |5368       |144       |65          |0              |2024-09-10   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|Black    |All  |$125K+     |4929       |132       |60          |0              |2024-09-10   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|Black    |All  |<$50K      |3537       |94        |43          |0              |2024-09-10   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|Black    |All  |$100K-$125K|4620       |123       |56          |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|All      |75 + |$50K-$100K |186        |1         |1           |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|All      |75 + |<$50K      |80         |1         |1           |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|All      |55-74|<$50K      |1241       |9         |9           |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|All      |18-34|<$50K      |1425       |10        |10          |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|All      |55-74|$125K+     |3281       |23        |23          |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|All      |35-54|$125K+     |4264       |30        |30          |0              |2024-09-10   |
          |2024-06-01|Baltimore, MD (512)             |DMA           |AT&T          |Postpaid Phone|All      |55-74|$50K-$100K |2189       |15        |16          |0              |2024-09-10   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|All      |55-74|$50K-$100K |4478       |120       |54          |0              |2024-09-10   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|All      |55-74|$125K+     |4929       |132       |60          |0              |2024-09-10   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|All      |55-74|<$50K      |3537       |94        |43          |0              |2024-09-10   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|All      |75 + |$100K-$125K|909        |24        |11          |0              |2024-09-10   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|All      |55-74|$100K-$125K|3711       |99        |45          |0              |2024-09-10   |
          |2024-06-01|Washington, DC-VA-MD-WV-PA (511)|DMA           |Xfinity Mobile|Postpaid Phone|All      |75 + |$50K-$100K |890        |24        |11          |0              |2024-09-10   |
        """
      }.withColumn("creation_date", current_date()), nullable = true).as[DmaFinalOutputPairwiseDemo]

      val monthToProcess = LocalDate.of(2024, 6, 1)

      val result = setNullableStateForAllColumns(Packaging().dmaFinalOutputPairwiseDemo(
        dmaOutputData, brandPlantypeLookupData, monthToProcess
      ).toDF(), nullable = true).as[DmaFinalOutputPairwiseDemo]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("packaging pairwise demographis VPGM From Dma Final Output Pairwise") {

    import spark.implicits._

    it("test") {
      val vpgmFromDmaFinalOutputAllDemoCombinationsData = setNullableStateForAllColumns(dataframe[VpgmFromDmaFinalOutputAllDemo] {
        """
          |month     |geography               |geography_type|brand         |plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|Greater Lakes (IL/MI/WI)|VPGM          |AT&T          |Postpaid Phone|Caucasian|55-74|<50K       |274421     |2157      |1936        |0              |2024-09-03   |
          |2024-06-01|Desert Southwest        |VPGM          |AT&T          |Postpaid Phone|Asian    |55-74|<50K       |5423       |47        |43          |0              |2024-09-03   |
          |2024-06-01|Ohio/Pennsylvania       |VPGM          |AT&T          |Prepaid Phone |Asian    |35-54|$100K-$125K|485        |13        |13          |0              |2024-09-03   |
          |2024-06-01|Greater Lakes (IL/MI/WI)|VPGM          |AT&T          |Postpaid Phone|Hispanic |18-34|$100K-$125K|41534      |370       |321         |0              |2024-09-03   |
          |2024-06-01|Ohio/Pennsylvania       |VPGM          |AT&T          |Prepaid Phone |Hispanic |75 + |<50K       |329        |6         |6           |0              |2024-09-03   |
          |2024-06-01|Gulf States (AL/LA/MS)  |VPGM          |AT&T          |Postpaid Phone|Hispanic |35-54|$50K-$100K |16902      |134       |114         |0              |2024-09-03   |
          |2024-06-01|Ohio/Pennsylvania       |VPGM          |Xfinity Mobile|Postpaid Phone|Black    |55-74|$100K-$125K|6757       |164       |79          |0              |2024-09-03   |
          |2024-06-01|Florida                 |VPGM          |Xfinity Mobile|Postpaid Phone|Black    |55-74|<50K       |6993       |206       |94          |0              |2024-09-03   |
          |2024-06-01|New York/New Jersey     |VPGM          |Xfinity Mobile|Postpaid Phone|Asian    |55-74|$100K-$125K|2145       |51        |22          |0              |2024-09-03   |
          |2024-06-01|South Texas             |VPGM          |Xfinity Mobile|Postpaid Phone|Asian    |18-34|$50K-$100K |1491       |46        |19          |0              |2024-09-03   |
          |2024-06-01|Southern California     |VPGM          |Xfinity Mobile|Postpaid Phone|Black    |55-74|$125K+     |34         |1         |0           |0              |2024-09-03   |
          |2024-06-01|Greater Lakes (IL/MI/WI)|VPGM          |Xfinity Mobile|Postpaid Phone|Asian    |75 + |$100K-$125K|1365       |31        |14          |0              |2024-09-03   |
        """
      }, nullable = true).as[VpgmFromDmaFinalOutputAllDemo]

      val expected = setNullableStateForAllColumns(dataframe[VpgmFromDmaFinalOutputPairwiseDemo] {
        """
          |month     |geography               |geography_type|brand         |plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|Greater Lakes (IL/MI/WI)|VPGM          |AT&T          |Postpaid Phone|Caucasian|55-74|All        |274421     |2157      |1936        |0              |2024-09-10   |
          |2024-06-01|Desert Southwest        |VPGM          |AT&T          |Postpaid Phone|Asian    |55-74|All        |5423       |47        |43          |0              |2024-09-10   |
          |2024-06-01|Ohio/Pennsylvania       |VPGM          |AT&T          |Prepaid Phone |Asian    |35-54|All        |485        |13        |13          |0              |2024-09-10   |
          |2024-06-01|Greater Lakes (IL/MI/WI)|VPGM          |AT&T          |Postpaid Phone|Hispanic |18-34|All        |41534      |370       |321         |0              |2024-09-10   |
          |2024-06-01|Ohio/Pennsylvania       |VPGM          |AT&T          |Prepaid Phone |Hispanic |75 + |All        |329        |6         |6           |0              |2024-09-10   |
          |2024-06-01|Gulf States (AL/LA/MS)  |VPGM          |AT&T          |Postpaid Phone|Hispanic |35-54|All        |16902      |134       |114         |0              |2024-09-10   |
          |2024-06-01|Ohio/Pennsylvania       |VPGM          |Xfinity Mobile|Postpaid Phone|Black    |55-74|All        |6757       |164       |79          |0              |2024-09-10   |
          |2024-06-01|Florida                 |VPGM          |Xfinity Mobile|Postpaid Phone|Black    |55-74|All        |6993       |206       |94          |0              |2024-09-10   |
          |2024-06-01|New York/New Jersey     |VPGM          |Xfinity Mobile|Postpaid Phone|Asian    |55-74|All        |2145       |51        |22          |0              |2024-09-10   |
          |2024-06-01|South Texas             |VPGM          |Xfinity Mobile|Postpaid Phone|Asian    |18-34|All        |1491       |46        |19          |0              |2024-09-10   |
          |2024-06-01|Southern California     |VPGM          |Xfinity Mobile|Postpaid Phone|Black    |55-74|All        |34         |1         |0           |0              |2024-09-10   |
          |2024-06-01|Greater Lakes (IL/MI/WI)|VPGM          |Xfinity Mobile|Postpaid Phone|Asian    |75 + |All        |1365       |31        |14          |0              |2024-09-10   |
          |2024-06-01|Greater Lakes (IL/MI/WI)|VPGM          |AT&T          |Postpaid Phone|Caucasian|All  |<50K       |274421     |2157      |1936        |0              |2024-09-10   |
          |2024-06-01|Desert Southwest        |VPGM          |AT&T          |Postpaid Phone|Asian    |All  |<50K       |5423       |47        |43          |0              |2024-09-10   |
          |2024-06-01|Ohio/Pennsylvania       |VPGM          |AT&T          |Prepaid Phone |Asian    |All  |$100K-$125K|485        |13        |13          |0              |2024-09-10   |
          |2024-06-01|Greater Lakes (IL/MI/WI)|VPGM          |AT&T          |Postpaid Phone|Hispanic |All  |$100K-$125K|41534      |370       |321         |0              |2024-09-10   |
          |2024-06-01|Ohio/Pennsylvania       |VPGM          |AT&T          |Prepaid Phone |Hispanic |All  |<50K       |329        |6         |6           |0              |2024-09-10   |
          |2024-06-01|Gulf States (AL/LA/MS)  |VPGM          |AT&T          |Postpaid Phone|Hispanic |All  |$50K-$100K |16902      |134       |114         |0              |2024-09-10   |
          |2024-06-01|Ohio/Pennsylvania       |VPGM          |Xfinity Mobile|Postpaid Phone|Black    |All  |$100K-$125K|6757       |164       |79          |0              |2024-09-10   |
          |2024-06-01|Florida                 |VPGM          |Xfinity Mobile|Postpaid Phone|Black    |All  |<50K       |6993       |206       |94          |0              |2024-09-10   |
          |2024-06-01|New York/New Jersey     |VPGM          |Xfinity Mobile|Postpaid Phone|Asian    |All  |$100K-$125K|2145       |51        |22          |0              |2024-09-10   |
          |2024-06-01|South Texas             |VPGM          |Xfinity Mobile|Postpaid Phone|Asian    |All  |$50K-$100K |1491       |46        |19          |0              |2024-09-10   |
          |2024-06-01|Southern California     |VPGM          |Xfinity Mobile|Postpaid Phone|Black    |All  |$125K+     |34         |1         |0           |0              |2024-09-10   |
          |2024-06-01|Greater Lakes (IL/MI/WI)|VPGM          |Xfinity Mobile|Postpaid Phone|Asian    |All  |$100K-$125K|1365       |31        |14          |0              |2024-09-10   |
          |2024-06-01|Greater Lakes (IL/MI/WI)|VPGM          |AT&T          |Postpaid Phone|All      |55-74|<50K       |274421     |2157      |1936        |0              |2024-09-10   |
          |2024-06-01|Desert Southwest        |VPGM          |AT&T          |Postpaid Phone|All      |55-74|<50K       |5423       |47        |43          |0              |2024-09-10   |
          |2024-06-01|Ohio/Pennsylvania       |VPGM          |AT&T          |Prepaid Phone |All      |35-54|$100K-$125K|485        |13        |13          |0              |2024-09-10   |
          |2024-06-01|Greater Lakes (IL/MI/WI)|VPGM          |AT&T          |Postpaid Phone|All      |18-34|$100K-$125K|41534      |370       |321         |0              |2024-09-10   |
          |2024-06-01|Ohio/Pennsylvania       |VPGM          |AT&T          |Prepaid Phone |All      |75 + |<50K       |329        |6         |6           |0              |2024-09-10   |
          |2024-06-01|Gulf States (AL/LA/MS)  |VPGM          |AT&T          |Postpaid Phone|All      |35-54|$50K-$100K |16902      |134       |114         |0              |2024-09-10   |
          |2024-06-01|Ohio/Pennsylvania       |VPGM          |Xfinity Mobile|Postpaid Phone|All      |55-74|$100K-$125K|6757       |164       |79          |0              |2024-09-10   |
          |2024-06-01|Florida                 |VPGM          |Xfinity Mobile|Postpaid Phone|All      |55-74|<50K       |6993       |206       |94          |0              |2024-09-10   |
          |2024-06-01|New York/New Jersey     |VPGM          |Xfinity Mobile|Postpaid Phone|All      |55-74|$100K-$125K|2145       |51        |22          |0              |2024-09-10   |
          |2024-06-01|South Texas             |VPGM          |Xfinity Mobile|Postpaid Phone|All      |18-34|$50K-$100K |1491       |46        |19          |0              |2024-09-10   |
          |2024-06-01|Southern California     |VPGM          |Xfinity Mobile|Postpaid Phone|All      |55-74|$125K+     |34         |1         |0           |0              |2024-09-10   |
          |2024-06-01|Greater Lakes (IL/MI/WI)|VPGM          |Xfinity Mobile|Postpaid Phone|All      |75 + |$100K-$125K|1365       |31        |14          |0              |2024-09-10   |
        """
      }.withColumn("creation_date", current_date()), nullable = true).as[VpgmFromDmaFinalOutputPairwiseDemo]


      val result = setNullableStateForAllColumns(Packaging().vpgmFromDmaFinalOutputPairwiseDemo(
        vpgmFromDmaFinalOutputAllDemoCombinationsData
      ).toDF(), nullable = true).as[VpgmFromDmaFinalOutputPairwiseDemo]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("packaging pairwise demographics Mso Ilec Footprints Final Output Pairwise") {

    import spark.implicits._

    it("test") {
      val msoIlecFootprintsFinalOutputAllDemoCombinationsData = setNullableStateForAllColumns(dataframe[MsoIlecFootprintsFinalOutputAllDemo] {
        """
          |month     |geography  |geography_type     |brand         |plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|Hispanic |55-74|<50K       |220275     |2110      |1691        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|Caucasian|35-54|$50K-$100K |1852036    |14802     |13136       |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|Caucasian|75 + |$50K-$100K |268660     |2386      |2039        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |Hispanic |55-74|$100K-$125K|9203       |251       |277         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|Black    |55-74|<50K       |222836     |2020      |1686        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |Black    |55-74|$125K+     |12413      |351       |357         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |Asian    |18-34|$125K+     |8542       |255       |232         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |Hispanic |55-74|$125K+     |13815      |340       |414         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Caucasian|35-54|$125K+     |360333     |8644      |3879        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Hispanic |75 + |$100K-$125K|22156      |568       |267         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Asian    |75 + |<50K       |11277      |273       |128         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Caucasian|18-34|$50K-$100K |231784     |5457      |2472        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Hispanic |55-74|$125K+     |96805      |2480      |1179        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Caucasian|18-34|$125K+     |214841     |5155      |2309        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Asian    |55-74|$100K-$125K|41690      |1032      |454         |0              |2024-09-03   |
        """
      }, nullable = true).as[MsoIlecFootprintsFinalOutputAllDemo]

      val expected = setNullableStateForAllColumns(dataframe[MsoIlecFootprintsFinalOutputPairwiseDemo] {
        """
          |month     |geography  |geography_type     |brand         |plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|Hispanic |55-74|All        |220275     |2110      |1691        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|Caucasian|35-54|All        |1852036    |14802     |13136       |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|Caucasian|75 + |All        |268660     |2386      |2039        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |Hispanic |55-74|All        |23018      |591       |691         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|Black    |55-74|All        |222836     |2020      |1686        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |Black    |55-74|All        |12413      |351       |357         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |Asian    |18-34|All        |8542       |255       |232         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Caucasian|35-54|All        |360333     |8644      |3879        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Hispanic |75 + |All        |22156      |568       |267         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Asian    |75 + |All        |11277      |273       |128         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Caucasian|18-34|All        |446625     |10612     |4781        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Hispanic |55-74|All        |96805      |2480      |1179        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Asian    |55-74|All        |41690      |1032      |454         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|Hispanic |All  |<50K       |220275     |2110      |1691        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|Caucasian|All  |$50K-$100K |2120696    |17188     |15175       |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |Hispanic |All  |$100K-$125K|9203       |251       |277         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|Black    |All  |<50K       |222836     |2020      |1686        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |Black    |All  |$125K+     |12413      |351       |357         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |Asian    |All  |$125K+     |8542       |255       |232         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |Hispanic |All  |$125K+     |13815      |340       |414         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Caucasian|All  |$125K+     |575174     |13799     |6188        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Hispanic |All  |$100K-$125K|22156      |568       |267         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Asian    |All  |<50K       |11277      |273       |128         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Caucasian|All  |$50K-$100K |231784     |5457      |2472        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Hispanic |All  |$125K+     |96805      |2480      |1179        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|Asian    |All  |$100K-$125K|41690      |1032      |454         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|All      |55-74|<50K       |443111     |4130      |3377        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|All      |35-54|$50K-$100K |1852036    |14802     |13136       |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Postpaid Phone|All      |75 + |$50K-$100K |268660     |2386      |2039        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |All      |55-74|$100K-$125K|9203       |251       |277         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |All      |55-74|$125K+     |26228      |691       |771         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|AT&T          |Prepaid Phone |All      |18-34|$125K+     |8542       |255       |232         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|All      |35-54|$125K+     |360333     |8644      |3879        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|All      |75 + |$100K-$125K|22156      |568       |267         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|All      |75 + |<50K       |11277      |273       |128         |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|All      |18-34|$50K-$100K |231784     |5457      |2472        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|All      |55-74|$125K+     |96805      |2480      |1179        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|All      |18-34|$125K+     |214841     |5155      |2309        |0              |2024-09-03   |
          |2024-06-01|Comcast_MSO|Broadband_footprint|Xfinity Mobile|Postpaid Phone|All      |55-74|$100K-$125K|41690      |1032      |454         |0              |2024-09-03   |
        """
      }, nullable = true).as[MsoIlecFootprintsFinalOutputPairwiseDemo]

      val creationDate = LocalDate.of(2024, 6, 1).toString

      val result = setNullableStateForAllColumns(Packaging().msoIlecFootprintsFinalOutputPairwiseDemo(
        msoIlecFootprintsFinalOutputAllDemoCombinationsData, creationDate
      ).toDF(), nullable = true).as[MsoIlecFootprintsFinalOutputPairwiseDemo]

      // Count should be the same
      result.count() shouldEqual expected.count()

      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("packaging pairwise demographics CMA Final Output Pairwise") {

    import spark.implicits._

    it("test") {
      val cmaFinalOutputAllDemoCombinationsData = setNullableStateForAllColumns(dataframe[CmaFinalOutputAllDemo] {
        """
          |month     |geography                         |geography_type|brand         |plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|North Carolina 15 - Cabarrus (579)|CMA           |AT&T          |Prepaid Phone |Caucasian|75 + |$50K-$100K |120        |3         |3           |0              |2024-09-03   |
          |2024-06-01|Missouri 12 - Maries (515)        |CMA           |AT&T          |Prepaid Phone |Hispanic |75 + |<$50K      |7          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Louisiana 8 - St. James (461)     |CMA           |AT&T          |Prepaid Phone |Hispanic |55-74|$50K-$100K |10         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Fargo-Moorehead, ND-MN (221)      |CMA           |AT&T          |Postpaid Phone|Asian    |35-54|$125K+     |23         |0         |0           |0              |2024-09-03   |
          |2024-06-01|South Dakota 1 - Harding (634)    |CMA           |AT&T          |Postpaid Phone|Hispanic |18-34|<$50K      |31         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Utah 6 - Piute (678)              |CMA           |AT&T          |Prepaid Phone |Asian    |75 + |$50K-$100K |0          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Utah 4 - Beaver (676)             |CMA           |AT&T          |Prepaid Phone |Caucasian|75 + |$125K+     |13         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Nevada 5 - White Pine (547)       |CMA           |AT&T          |Postpaid Phone|Asian    |55-74|$100K-$125K|1          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Michigan 6 - Roscommon (477)      |CMA           |AT&T          |Prepaid Phone |Hispanic |35-54|$100K-$125K|1          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Florida 4 - Citrus (363)          |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |55-74|$50K-$100K |121        |3         |1           |0              |2024-09-03   |
          |2024-06-01|Elkhart-Goshen, IN (223)          |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |35-54|$100K-$125K|124        |2         |1           |0              |2024-09-03   |
          |2024-06-01|Knoxville, TN (79)                |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |75 + |<$50K      |77         |2         |1           |0              |2024-09-03   |
          |2024-06-01|Washington 6 - Pacific (698)      |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |75 + |<$50K      |13         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Illinois 2 - Bureau (395)         |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |35-54|<$50K      |37         |1         |0           |0              |2024-09-03   |
          |2024-06-01|West Virginia 6 - Lincoln (706)   |CMA           |Xfinity Mobile|Postpaid Phone|Caucasian|75 + |$100K-$125K|1          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Alabama 2 - Jackson (308)         |CMA           |Xfinity Mobile|Postpaid Phone|Black    |18-34|$50K-$100K |0          |0         |0           |0              |2024-09-03   |
        """
      }, nullable = true).as[CmaFinalOutputAllDemo]

      val expected = setNullableStateForAllColumns(dataframe[CmaFinalOutputPairwiseDemo] {
        """
          |month     |geography                         |geography_type|brand         |plan_type     |ethnicity|age  |income     |subscribers|gross_adds|gross_losses|base_adjustment|creation_date|
          |2024-06-01|North Carolina 15 - Cabarrus (579)|CMA           |AT&T          |Prepaid Phone |Caucasian|75 + |All        |120        |3         |3           |0              |2024-09-03   |
          |2024-06-01|Missouri 12 - Maries (515)        |CMA           |AT&T          |Prepaid Phone |Hispanic |75 + |All        |7          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Louisiana 8 - St. James (461)     |CMA           |AT&T          |Prepaid Phone |Hispanic |55-74|All        |10         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Fargo-Moorehead, ND-MN (221)      |CMA           |AT&T          |Postpaid Phone|Asian    |35-54|All        |23         |0         |0           |0              |2024-09-03   |
          |2024-06-01|South Dakota 1 - Harding (634)    |CMA           |AT&T          |Postpaid Phone|Hispanic |18-34|All        |31         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Utah 6 - Piute (678)              |CMA           |AT&T          |Prepaid Phone |Asian    |75 + |All        |0          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Utah 4 - Beaver (676)             |CMA           |AT&T          |Prepaid Phone |Caucasian|75 + |All        |13         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Nevada 5 - White Pine (547)       |CMA           |AT&T          |Postpaid Phone|Asian    |55-74|All        |1          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Michigan 6 - Roscommon (477)      |CMA           |AT&T          |Prepaid Phone |Hispanic |35-54|All        |1          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Florida 4 - Citrus (363)          |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |55-74|All        |121        |3         |1           |0              |2024-09-03   |
          |2024-06-01|Elkhart-Goshen, IN (223)          |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |35-54|All        |124        |2         |1           |0              |2024-09-03   |
          |2024-06-01|Knoxville, TN (79)                |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |75 + |All        |77         |2         |1           |0              |2024-09-03   |
          |2024-06-01|Washington 6 - Pacific (698)      |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |75 + |All        |13         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Illinois 2 - Bureau (395)         |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |35-54|All        |37         |1         |0           |0              |2024-09-03   |
          |2024-06-01|West Virginia 6 - Lincoln (706)   |CMA           |Xfinity Mobile|Postpaid Phone|Caucasian|75 + |All        |1          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Alabama 2 - Jackson (308)         |CMA           |Xfinity Mobile|Postpaid Phone|Black    |18-34|All        |0          |0         |0           |0              |2024-09-03   |
          |2024-06-01|North Carolina 15 - Cabarrus (579)|CMA           |AT&T          |Prepaid Phone |Caucasian|All  |$50K-$100K |120        |3         |3           |0              |2024-09-03   |
          |2024-06-01|Missouri 12 - Maries (515)        |CMA           |AT&T          |Prepaid Phone |Hispanic |All  |<$50K      |7          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Louisiana 8 - St. James (461)     |CMA           |AT&T          |Prepaid Phone |Hispanic |All  |$50K-$100K |10         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Fargo-Moorehead, ND-MN (221)      |CMA           |AT&T          |Postpaid Phone|Asian    |All  |$125K+     |23         |0         |0           |0              |2024-09-03   |
          |2024-06-01|South Dakota 1 - Harding (634)    |CMA           |AT&T          |Postpaid Phone|Hispanic |All  |<$50K      |31         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Utah 6 - Piute (678)              |CMA           |AT&T          |Prepaid Phone |Asian    |All  |$50K-$100K |0          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Utah 4 - Beaver (676)             |CMA           |AT&T          |Prepaid Phone |Caucasian|All  |$125K+     |13         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Nevada 5 - White Pine (547)       |CMA           |AT&T          |Postpaid Phone|Asian    |All  |$100K-$125K|1          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Michigan 6 - Roscommon (477)      |CMA           |AT&T          |Prepaid Phone |Hispanic |All  |$100K-$125K|1          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Florida 4 - Citrus (363)          |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |All  |$50K-$100K |121        |3         |1           |0              |2024-09-03   |
          |2024-06-01|Elkhart-Goshen, IN (223)          |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |All  |$100K-$125K|124        |2         |1           |0              |2024-09-03   |
          |2024-06-01|Knoxville, TN (79)                |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |All  |<$50K      |77         |2         |1           |0              |2024-09-03   |
          |2024-06-01|Washington 6 - Pacific (698)      |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |All  |<$50K      |13         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Illinois 2 - Bureau (395)         |CMA           |Xfinity Mobile|Postpaid Phone|Hispanic |All  |<$50K      |37         |1         |0           |0              |2024-09-03   |
          |2024-06-01|West Virginia 6 - Lincoln (706)   |CMA           |Xfinity Mobile|Postpaid Phone|Caucasian|All  |$100K-$125K|1          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Alabama 2 - Jackson (308)         |CMA           |Xfinity Mobile|Postpaid Phone|Black    |All  |$50K-$100K |0          |0         |0           |0              |2024-09-03   |
          |2024-06-01|North Carolina 15 - Cabarrus (579)|CMA           |AT&T          |Prepaid Phone |All      |75 + |$50K-$100K |120        |3         |3           |0              |2024-09-03   |
          |2024-06-01|Missouri 12 - Maries (515)        |CMA           |AT&T          |Prepaid Phone |All      |75 + |<$50K      |7          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Louisiana 8 - St. James (461)     |CMA           |AT&T          |Prepaid Phone |All      |55-74|$50K-$100K |10         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Fargo-Moorehead, ND-MN (221)      |CMA           |AT&T          |Postpaid Phone|All      |35-54|$125K+     |23         |0         |0           |0              |2024-09-03   |
          |2024-06-01|South Dakota 1 - Harding (634)    |CMA           |AT&T          |Postpaid Phone|All      |18-34|<$50K      |31         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Utah 6 - Piute (678)              |CMA           |AT&T          |Prepaid Phone |All      |75 + |$50K-$100K |0          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Utah 4 - Beaver (676)             |CMA           |AT&T          |Prepaid Phone |All      |75 + |$125K+     |13         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Nevada 5 - White Pine (547)       |CMA           |AT&T          |Postpaid Phone|All      |55-74|$100K-$125K|1          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Michigan 6 - Roscommon (477)      |CMA           |AT&T          |Prepaid Phone |All      |35-54|$100K-$125K|1          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Florida 4 - Citrus (363)          |CMA           |Xfinity Mobile|Postpaid Phone|All      |55-74|$50K-$100K |121        |3         |1           |0              |2024-09-03   |
          |2024-06-01|Elkhart-Goshen, IN (223)          |CMA           |Xfinity Mobile|Postpaid Phone|All      |35-54|$100K-$125K|124        |2         |1           |0              |2024-09-03   |
          |2024-06-01|Knoxville, TN (79)                |CMA           |Xfinity Mobile|Postpaid Phone|All      |75 + |<$50K      |77         |2         |1           |0              |2024-09-03   |
          |2024-06-01|Washington 6 - Pacific (698)      |CMA           |Xfinity Mobile|Postpaid Phone|All      |75 + |<$50K      |13         |0         |0           |0              |2024-09-03   |
          |2024-06-01|Illinois 2 - Bureau (395)         |CMA           |Xfinity Mobile|Postpaid Phone|All      |35-54|<$50K      |37         |1         |0           |0              |2024-09-03   |
          |2024-06-01|West Virginia 6 - Lincoln (706)   |CMA           |Xfinity Mobile|Postpaid Phone|All      |75 + |$100K-$125K|1          |0         |0           |0              |2024-09-03   |
          |2024-06-01|Alabama 2 - Jackson (308)         |CMA           |Xfinity Mobile|Postpaid Phone|All      |18-34|$50K-$100K |0          |0         |0           |0              |2024-09-03   |
        """
      }, nullable = true).as[CmaFinalOutputPairwiseDemo]

      val creationDate = LocalDate.of(2024, 6, 1).toString

      val result = setNullableStateForAllColumns(Packaging().cmaFinalOutputPairwiseDemo(
        cmaFinalOutputAllDemoCombinationsData, creationDate
      ).toDF(), nullable = true).as[CmaFinalOutputPairwiseDemo]

      // Count should be the same
      result.count() shouldEqual expected.count()

      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }
}