package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model.{demoVarianceFactorCompressed, lossesFromAverageEthnicityPercentOfCarrier, subsEthnicityPercentOfCarrier, winnerPercentOfLoser, winsFromAverageEthnicityPercentOfCarrier, wmsWithEthnicityFromAverageBackwards, _}
import com.comlinkdata.largescale.schema.wireless_market_share.{MonthlyOutputWithEthnicityDMA, NationalWCVFlows, SubscribersByEthnicityAgeIncomeDMA, WCVFlowsDMA, WirelessMovementWideGeneric, WmsDma, WmsDmaTemp}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, Dataset}
import org.apache.spark.sql.types.{StructField, StructType}

import java.net.URI
import java.time.LocalDate


class DemographicsUtilsDMASpec extends CldSparkBaseSpec {

  def setNullableStateForAllColumns(df: DataFrame, nullable: Boolean): DataFrame = {
    // get schema
    val schema = df.schema
    // modify [[StructField] with name `cn`
    val newSchema = StructType(schema.map {
      case StructField(c, t, _, m) ⇒ StructField(c, t, nullable = nullable, m)
    })
    // apply new schema
    df.sqlContext.createDataFrame(df.rdd, newSchema)
  }

  describe("Demo Variance Factor")  {

    import spark.implicits._

    it("calculateDemoVarianceInputThreeMonths")  {
      val newMonth = LocalDate.parse("2022-07-01")
      val compressedMax = 1.10
      val compressedMin = 0.90
      val wcvFlowsDMAData = dataset[NationalWCVFlows] {
        """
          |month_date    |loser_brand_plantype|winner_brand_plantype|ethnicity|age  |income  |wins|
          |2022-06-01    |Altice Postpaid     |AT&T Postpaid        |Black    |18-34|LT $50K |3   |
          |2022-06-01    |Altice Postpaid     |Verizon Postpaid     |Black    |18-34|LT $50K |1   |
          |2022-06-01    |Altice Postpaid     |Sprint Postpaid      |Hispanic |75 + |LT $50K |1   |
          |2022-06-01    |Altice Postpaid     |Verizon Postpaid     |White    |55-74|100-150k|1   |
          |2022-06-01    |AT&T Postpaid       |Verizon Postpaid     |Asian    |18-34|GT 150k |19  |
          |2022-06-01    |AT&T Postpaid       |Cricket Prepaid      |Asian    |35-54|100-150k|30  |
          |2022-06-01    |AT&T Postpaid       |T-Mobile Postpaid    |Asian    |75 + |GT 150k |2   |
          |2022-06-01    |AT&T Postpaid       |Verizon Postpaid     |Hispanic |18-34|100-150k|30  |
          |2022-06-01    |AT&T Postpaid       |T-Mobile Postpaid    |Hispanic |35-54|$50-100K|128 |
        """
      }.as[NationalWCVFlows]


      val expected = setNullableStateForAllColumns(dataframe[demoVarianceInput] {
        """
        |date     |winner_brand_plantype|ethnicity|age  |income  |wins|
        |2022-07-01|AT&T Postpaid        |Black    |18-34|LT $50K |3   |
        |2022-07-01|Cricket Prepaid      |Asian    |35-54|100-150k|30  |
        |2022-07-01|Verizon Postpaid     |White    |55-74|100-150k|1   |
        |2022-07-01|Verizon Postpaid     |Hispanic |18-34|100-150k|30  |
        |2022-07-01|T-Mobile Postpaid    |Hispanic |35-54|$50-100K|128 |
        |2022-07-01|Verizon Postpaid     |Asian    |18-34|GT 150k |19  |
        |2022-07-01|Verizon Postpaid     |Black    |18-34|LT $50K |1   |
        |2022-07-01|T-Mobile Postpaid    |Asian    |75 + |GT 150k |2   |
        """
      }, nullable = true).as[demoVarianceInput]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateDemoVarianceInputThreeMonths(
          newMonth, wcvFlowsDMAData
        ).toDF(), nullable = true).as[demoVarianceInput]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }

    it("calculateDemoVarianceFixedTwoYears") {
      val newMonth = LocalDate.parse("2022-06-01")
      val compressedMax = 1.1
      val compressedMin = 0.9
      val wcvFlowsDMAData = dataset[NationalWCVFlows] {
        """
          |month_date    |loser_brand_plantype|winner_brand_plantype|ethnicity|age  |income  |wins|
          |2022-06-01    |Altice Postpaid     |AT&T Postpaid        |Black    |18-34|LT $50K |3   |
          |2022-06-01    |Altice Postpaid     |Verizon Postpaid     |Black    |18-34|LT $50K |1   |
          |2022-06-01    |Altice Postpaid     |Sprint Postpaid      |Hispanic |75 + |LT $50K |1   |
          |2022-06-01    |Altice Postpaid     |Verizon Postpaid     |White    |55-74|100-150k|1   |
          |2022-06-01    |AT&T Postpaid       |Verizon Postpaid     |Asian    |18-34|GT 150k |19  |
          |2022-06-01    |AT&T Postpaid       |Cricket Prepaid      |Asian    |35-54|100-150k|30  |
          |2022-06-01    |AT&T Postpaid       |T-Mobile Postpaid    |Asian    |75 + |GT 150k |2   |
          |2022-06-01    |AT&T Postpaid       |Verizon Postpaid     |Hispanic |18-34|100-150k|30  |
          |2022-06-01    |AT&T Postpaid       |T-Mobile Postpaid    |Hispanic |35-54|$50-100K|128 |
          """
      }.as[NationalWCVFlows]

      val expected = setNullableStateForAllColumns(dataframe[demoVarianceInput] {
        """
          |      date|winner_brand_plantype|ethnicity|age|income|wins|
          |2022-06-01|Verizon Postpaid|Black|18-34|LT $50K|1|
          |2022-06-01|Verizon Postpaid|White|55-74|100-150k|1|
          |2022-06-01|Verizon Postpaid|Hispanic|18-34|100-150k|30|
          |2022-06-01|T-Mobile Postpaid|Hispanic|35-54|$50-100K|128|
          |2022-06-01|Cricket Prepaid|Asian|35-54|100-150k|30|
          |2022-06-01|AT&T Postpaid|Black|18-34|LT $50K|3|
          |2022-06-01|Verizon Postpaid|Asian|18-34|GT 150k|19|
          |2022-06-01|T-Mobile Postpaid|Asian|75 +|GT 150k|2|
    """
      }, nullable = true).as[demoVarianceInput]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateDemoVarianceFixedTwoYears(
        newMonth, wcvFlowsDMAData
      ).toDF(), nullable = true).as[demoVarianceInput]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }

    it("calculateDemoVarianceFactorCompressed") {
      val newMonth = LocalDate.parse("2022-06-01")
      val compressedMax = 1.1
      val compressedMin = 0.9
      val demoVarianceInputThreeMonths = dataset[demoVarianceInput] {
        """
          |      date|winner_brand_plantype|ethnicity|age|income|wins|
          |2022-07-01|Boost Prepaid|Black|55-74|LT $50K|20|
          |2022-06-01|Boost Prepaid|White|75 +|LT $50K|20|
          |2022-06-01|Metro Prepaid|Black|35-54|LT $50K|20|
          |2022-06-01|Tracfone Prepaid|Asian|55-74|$50-100K|20|
          |2022-06-01|U.S. Cellular Postpaid|White|75 +|100-150k|30|
          |2022-06-01|XFINITY Mobile Postpaid|Black|55-74|100-150k|10|
          |2022-07-01|XFINITY Mobile Postpaid|Hispanic|55-74|100-150k|50|
          |2022-07-01|Other Wireless Postpaid|Asian|18-34|LT $50K|70|
          |2022-07-01|AT&T Prepaid|White|18-34|LT $50K|10|
          |2022-07-01|Cricket Prepaid|Asian|18-34|LT $50K|10|
    """
      }.as[demoVarianceInput]

      val demoVarianceInputFixedTwoYears = dataset[demoVarianceInput] {
        """
          |      date|winner_brand_plantype|ethnicity|age|income|wins|
          |2022-07-01|Boost Prepaid|Black|55-74|LT $50K|80|
          |2022-06-01|Boost Prepaid|White|75 +|LT $50K|300|
          |2022-06-01|Metro Prepaid|Black|35-54|LT $50K|200|
          |2022-06-01|Tracfone Prepaid|Asian|55-74|$50-100K|100|
          |2022-06-01|U.S. Cellular Postpaid|White|75 +|100-150k|300|
          |2022-06-01|XFINITY Mobile Postpaid|Black|55-74|100-150k|100|
          |2022-07-01|XFINITY Mobile Postpaid|Hispanic|55-74|100-150k|150|
          |2022-07-01|Other Wireless Postpaid|Asian|18-34|LT $50K|140|
          |2022-07-01|AT&T Prepaid|White|18-34|LT $50K|100|
          |2022-07-01|Cricket Prepaid|Asian|18-34|LT $50K|100|
          """
      }.as[demoVarianceInput]

      val expected = setNullableStateForAllColumns(dataframe[demoVarianceFactorCompressed] {
        """
          |date      |winner_brand_plantype |ethnicity|age  |income  |wins_numerator|wins_denominator|wins_prop            |long_term_wins_numerator|long_term_wins_denominator|long_term_wins_prop  |variance_factor   |variance_factor_compressed|
          2022-06-01|Boost Prepaid          |White    |75 + |LT $50K |20.0          |20.0            |1.0      |300.0                   |300.0                     |1.0                |1.0            |0.9                       |
          |2022-06-01|Metro Prepaid          |Black    |35-54|LT $50K |20.0          |20.0            |1.0      |200.0                   |200.0                     |1.0                |1.0            |0.9                       |
          |2022-06-01|Tracfone Prepaid       |Asian    |55-74|$50-100K|20.0          |20.0            |1.0      |100.0                   |100.0                     |1.0                |1.0            |0.9                       |
          |2022-06-01|U.S. Cellular Postpaid |White    |75 + |100-150k|30.0          |30.0            |1.0      |300.0                   |300.0                     |1.0                |1.0            |0.9                       |
          |2022-06-01|XFINITY Mobile Postpaid|Black    |55-74|100-150k|10.0          |10.0            |1.0      |100.0                   |100.0                     |1.0                |1.0            |0.9                       |
          |2022-07-01|AT&T Prepaid           |White    |18-34|LT $50K |10.0          |10.0            |1.0      |100.0                   |100.0                     |1.0                |1.0            |0.9                       |
          |2022-07-01|Boost Prepaid          |Black    |55-74|LT $50K |20.0          |20.0            |1.0      |80.0                    |80.0                      |1.0                |1.0            |0.9                       |
          |2022-07-01|Cricket Prepaid        |Asian    |18-34|LT $50K |10.0          |10.0            |1.0      |100.0                   |100.0                     |1.0                |1.0            |0.9                       |
          |2022-07-01|Other Wireless Postpaid|Asian    |18-34|LT $50K |70.0          |70.0            |1.0      |140.0                   |140.0                     |1.0                |1.0            |0.9                       |
          |2022-07-01|XFINITY Mobile Postpaid|Hispanic |55-74|100-150k|50.0          |50.0            |1.0      |150.0                   |150.0                     |1.0                |1.0            |0.9                       |
        """
      }, nullable = true).as[demoVarianceFactorCompressed]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateDemoVarianceFactorCompressed(
        newMonth, compressedMin, compressedMax, demoVarianceInputThreeMonths, demoVarianceInputFixedTwoYears
      ).toDF(), nullable = true).as[demoVarianceFactorCompressed]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateSubEthnicityPercentOfCarrier") {
    import spark.implicits._
    it("test") {
      val subscribersWithEthnicityData = dataset[SubscribersByEthnicityAgeIncomeDMA]  {
        """
          |date      |dma|dma_name       |winner_brand_plantype|ethnicity|age|income|subscribers|
          |2022-06-01|500|Portland-Auburn|AT&T Postpaid|Black|18-34|$50-100K|221|
          |2022-06-01|500|Portland-Auburn|Cricket Postpaid|Hispanic|18-34|GT 150k|78|
          |2022-06-01|501|Portland-Auburn|AT&T Postpaid|White|18-34|$50-100K|13381|
          |2022-06-01|501|Portland-Auburn|T-Mobile Postpaid|Asian|55-74|GT 150k|136|
          |2022-06-01|502|Portland-Auburn|T-Mobile Postpaid|Black|55-74|$50-100K|560|
          |2022-06-01|503|Portland-Auburn|Verizon Postpaid|Asian|18-34|GT 150k|46|
          |2022-06-01|501|Portland-Auburn|Sprint Postpaid|Hispanic|55-74|100-150k|722|
          |2022-06-01|500|Portland-Auburn|Verizon Postpaid|White|75 +|100-150k|5829|
          |2022-06-01|503|New York       |Cricket Postpaid|Asian|75 +|100-150k|4907|
          """
      }

      val expected = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrier] {
        """
          |date      |dma|winner_brand_plantype|ethnicity|age|income|subscribers|total_subscribers|ethnicity_pct_of_carrier|
          |2022-06-01|500|AT&T Postpaid|Black|18-34|$50-100K|221.0|221.0|1.0|
          |2022-06-01|500|Cricket Postpaid|Hispanic|18-34|GT 150k|78.0|78.0|1.0|
          |2022-06-01|500|Verizon Postpaid|White|75 +|100-150k|5829.0|5829.0|1.0|
          |2022-06-01|501|AT&T Postpaid|White|18-34|$50-100K|13381.0|13381.0|1.0|
          |2022-06-01|501|Sprint Postpaid|Hispanic|55-74|100-150k|722.0|722.0|1.0|
          |2022-06-01|501|T-Mobile Postpaid|Asian|55-74|GT 150k|136.0|136.0|1.0|
          |2022-06-01|502|T-Mobile Postpaid|Black|55-74|$50-100K|560.0|560.0|1.0|
          |2022-06-01|503|Cricket Postpaid|Asian|75 +|100-150k|4907.0|4907.0|1.0|
          |2022-06-01|503|Verizon Postpaid|Asian|18-34|GT 150k|46.0|46.0|1.0|
          """
      }, nullable = true).as[subsEthnicityPercentOfCarrier]

      val newDate = LocalDate.parse("2022-06-01")

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateSubEthnicityPercentOfCarrier(
        subscribersWithEthnicityData,newDate
      ).toDF(), nullable = true).as[subsEthnicityPercentOfCarrier]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateSubEthnicityPercentOfCarrierForwardInsertMonthly") {
    import spark.implicits._
    it("test") {
      val monthlyOutputWithEthnicityDMAData = dataset[MonthlyOutputWithEthnicityDMA] {
        """
          |wms_month |subs_join_month|losses_join_month|wins_join_month|dma|dma_name                    |brand_plantype   |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|age  |income  |subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses     |winner_from_average_ethnicity_pct_of_carrier|gross_adds       |base_adjustment   |starting_subscribers|gross_add_rate   |churn_rate       |overall_gross_add_rate|overall_churn_rate|
          |2022-05-01|2022-05-01     |2022-05-01       |2022-05-01     |500|Portland-Auburn, ME-NH (500)|AT&T Postpaid    |112931           |1595            |722               |-58                  |112117                |113887                |Asian    |35-54|GT 150k |33                     |0.000292221592519            |33.0008766647776  |0.00026858006075              |0.193914803861373|0.000256319760791                           |0.408830018460894|-0.014866546125851|32.8008279963039    |0.012464015192146|0.005911887464646|0.014226210119786     |0.006439701383376 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500|Portland-Auburn, ME-NH (500)|AT&T Postpaid    |112931           |1595            |722               |-58                  |112117                |113887                |Asian    |35-54|GT 150k |33                     |0.000292221592519            |33.0008766647776  |0.00026858006075              |0.193914803861373|0.000256319760791                           |0.408830018460894|-0.014866546125851|32.8008279963039    |0.012464015192146|0.005911887464646|0.014226210119786     |0.006439701383376 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500|Portland-Auburn, ME-NH (500)|T-Mobile Postpaid|112931           |1595            |722               |-58                  |112117                |113887                |Black    |55-74|GT 150k |161                    |0.001425687163502            |161.004277061491  |0.001459632432233             |1.05385461607209 |0.001249051689792                           |1.99223744521793 |-0.072444998007925|160.138339230353    |0.012440727528416|0.006580901370259|0.014226210119786     |0.006439701383376 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500|Portland-Auburn, ME-NH (500)|Verizon Postpaid |325881           |2500            |2756              |-5                   |326142                |325732                |Asian    |75 + |100-150k|100                    |0.000306862363024            |100.000613724726  |0.000290937850773             |0.801824716730904|0.000338413472305                           |0.846033680761643|-0.001692067361523|99.9580968280568    |0.008463883443249|0.008021608475701|0.007665372751746     |0.008450306921525 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500|Portland-Auburn, ME-NH (500)|Sprint Postpaid  |163561           |2145            |1467              |-21                  |162904                |164172                |Black    |18-34|$50-100K|221                    |0.001351186109073            |221.001351186109  |0.001298407861585             |1.90476433294464 |0.001411096538191                           |3.02680207441893 |-0.029633027302004|219.908946471937    |0.013763887840758|0.008661604557265|0.013167264155576     |0.00900530373717  |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |501|New York, NY-NJ-CT-PA (501) |AT&T Postpaid    |3790286          |40833           |33598             |-475                 |3783527               |3796149               |Asian    |18-34|$50-100K|33118                  |0.00873760383127             |33118.0174752077  |0.008570801158171             |287.961777312214 |0.008046974525438                           |328.582110797228 |-3.82231289958326 |33081.2194546222    |0.00993258761963 |0.008704690518051|0.010792310983905     |0.008880074068455 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |501|New York, NY-NJ-CT-PA (501) |T-Mobile Postpaid|5662625          |64346           |47850             |-5516                |5651645               |5682695               |Hispanic |35-54|$50-100K|154906                 |0.027355849616115            |154905.917932451  |0.02776122454973              |1328.37459470457 |0.026843094809806                           |1727.24577863179 |-148.066510970891 |154655.113259495    |0.01116837162528 |0.008589270452867|0.011385357714435     |0.008466561505544 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |501|New York, NY-NJ-CT-PA (501) |Verizon Postpaid |3790286          |40833           |33598             |-475                 |3783527               |3796149               |Black    |75 + |GT 150k |24906                  |0.00657101156536             |24906.0131420231  |0.007060121039448             |237.205946683368 |0.006303180605968                           |257.377773683475 |-2.99401078783461 |24888.8353258109    |0.010341093519011|0.009530616582825|0.010792310983905     |0.008880074068455 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |502|Binghamton, NY (502)        |Verizon Postpaid |95045            |642             |840               |-2                   |95245                 |94825                 |Black    |35-54|100-150k|44                     |0.000462928866771            |43.9990741422665  |0.000453481489009             |0.380924450767509|0.000487633298596                           |0.313060577698733|-0.000975266597192|44.0679132819324    |0.007104048147138|0.008644031958819|0.00674051131293      |0.008819360596357 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |502|Binghamton, NY (502)        |AT&T Postpaid    |64582            |801             |497               |-6                   |64283                 |64873                 |Hispanic |35-54|LT $50K |217                    |0.003360173428306            |217.006720346857  |0.003279263062106             |1.62979374186652 |0.003420865352557                           |2.74011314739835 |-0.020525192115344|215.91692613344     |0.012690589832244|0.007548244461665|0.012460526111103     |0.007731437549585 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |502|Binghamton, NY (502)        |AT&T Postpaid    |64582            |801             |497               |-6                   |64283                 |64873                 |Hispanic |35-54|LT $50K |217                    |0.003360173428306            |217.006720346857  |0.003279263062106             |1.62979374186652 |0.003420865352557                           |2.74011314739835 |-0.020525192115344|215.91692613344     |0.012690589832244|0.007548244461665|0.012460526111103     |0.007731437549585 |
        """
      }.as[MonthlyOutputWithEthnicityDMA]

      val expected = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrier] {
        """
          |date      |dma|winner_brand_plantype|ethnicity|age  |income  |subscribers     |total_subscribers|ethnicity_pct_of_carrier|
          |2022-07-01|500|AT&T Postpaid        |Asian    |35-54|GT 150k |33.0008766647776|33.0008766647776 |1.0                     |
          |2022-07-01|500|Sprint Postpaid      |Black    |18-34|$50-100K|221.001351186109|221.001351186109 |1.0                     |
          |2022-07-01|500|T-Mobile Postpaid    |Black    |55-74|GT 150k |161.004277061491|161.004277061491 |1.0                     |
          |2022-07-01|500|Verizon Postpaid     |Asian    |75 + |100-150k|100.000613724726|100.000613724726 |1.0                     |
          |2022-07-01|501|AT&T Postpaid        |Asian    |18-34|$50-100K|33118.0174752077|33118.0174752077 |1.0                     |
          |2022-07-01|501|T-Mobile Postpaid    |Hispanic |35-54|$50-100K|154905.917932451|154905.917932451 |1.0                     |
          |2022-07-01|501|Verizon Postpaid     |Black    |75 + |GT 150k |24906.0131420231|24906.0131420231 |1.0                     |
          |2022-07-01|502|AT&T Postpaid        |Hispanic |35-54|LT $50K |217.006720346857|434.013440693714 |0.5                     |
          |2022-07-01|502|AT&T Postpaid        |Hispanic |35-54|LT $50K |217.006720346857|434.013440693714 |0.5                     |
          |2022-07-01|502|Verizon Postpaid     |Black    |35-54|100-150k|43.9990741422665|43.9990741422665 |1.0                     |
        """
      }, nullable = true).as[subsEthnicityPercentOfCarrier]

      val newDate = LocalDate.parse("2022-07-01")

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateSubEthnicityPercentOfCarrierForwardInsertMonthly(
        monthlyOutputWithEthnicityDMAData, newDate
      ).toDF(), nullable = true).as[subsEthnicityPercentOfCarrier]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateSubEthnicityPercentOfCarrierBackwardInsertMonthly") {
    import spark.implicits._
    it("test") {

      val monthlyOutputWithEthnicityDMAData = dataset[MonthlyOutputWithEthnicityDMA] {
        """
          |wms_month |subs_join_month|losses_join_month|wins_join_month|dma|dma_name                    |brand_plantype   |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|age  |income  |subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses     |winner_from_average_ethnicity_pct_of_carrier|gross_adds       |base_adjustment   |starting_subscribers|gross_add_rate   |churn_rate       |overall_gross_add_rate|overall_churn_rate|
          |2022-05-01|2022-05-01     |2022-05-01       |2022-05-01     |500|Portland-Auburn, ME-NH (500)|AT&T Postpaid    |112931           |1595            |722               |-58                  |112117                |113887                |Asian    |35-54|GT 150k |33                     |0.000292221592519            |33.0008766647776  |0.00026858006075              |0.193914803861373|0.000256319760791                           |0.408830018460894|-0.014866546125851|32.8008279963039    |0.012464015192146|0.005911887464646|0.014226210119786     |0.006439701383376 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500|Portland-Auburn, ME-NH (500)|AT&T Postpaid    |112931           |1595            |722               |-58                  |112117                |113887                |Asian    |35-54|GT 150k |33                     |0.000292221592519            |33.0008766647776  |0.00026858006075              |0.193914803861373|0.000256319760791                           |0.408830018460894|-0.014866546125851|32.8008279963039    |0.012464015192146|0.005911887464646|0.014226210119786     |0.006439701383376 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500|Portland-Auburn, ME-NH (500)|T-Mobile Postpaid|112931           |1595            |722               |-58                  |112117                |113887                |Black    |55-74|GT 150k |161                    |0.001425687163502            |161.004277061491  |0.001459632432233             |1.05385461607209 |0.001249051689792                           |1.99223744521793 |-0.072444998007925|160.138339230353    |0.012440727528416|0.006580901370259|0.014226210119786     |0.006439701383376 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500|Portland-Auburn, ME-NH (500)|Verizon Postpaid |325881           |2500            |2756              |-5                   |326142                |325732                |Asian    |75 + |100-150k|100                    |0.000306862363024            |100.000613724726  |0.000290937850773             |0.801824716730904|0.000338413472305                           |0.846033680761643|-0.001692067361523|99.9580968280568    |0.008463883443249|0.008021608475701|0.007665372751746     |0.008450306921525 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |500|Portland-Auburn, ME-NH (500)|Sprint Postpaid  |163561           |2145            |1467              |-21                  |162904                |164172                |Black    |18-34|$50-100K|221                    |0.001351186109073            |221.001351186109  |0.001298407861585             |1.90476433294464 |0.001411096538191                           |3.02680207441893 |-0.029633027302004|219.908946471937    |0.013763887840758|0.008661604557265|0.013167264155576     |0.00900530373717  |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |501|New York, NY-NJ-CT-PA (501) |AT&T Postpaid    |3790286          |40833           |33598             |-475                 |3783527               |3796149               |Asian    |18-34|$50-100K|33118                  |0.00873760383127             |33118.0174752077  |0.008570801158171             |287.961777312214 |0.008046974525438                           |328.582110797228 |-3.82231289958326 |33081.2194546222    |0.00993258761963 |0.008704690518051|0.010792310983905     |0.008880074068455 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |501|New York, NY-NJ-CT-PA (501) |T-Mobile Postpaid|5662625          |64346           |47850             |-5516                |5651645               |5682695               |Hispanic |35-54|$50-100K|154906                 |0.027355849616115            |154905.917932451  |0.02776122454973              |1328.37459470457 |0.026843094809806                           |1727.24577863179 |-148.066510970891 |154655.113259495    |0.01116837162528 |0.008589270452867|0.011385357714435     |0.008466561505544 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |501|New York, NY-NJ-CT-PA (501) |Verizon Postpaid |3790286          |40833           |33598             |-475                 |3783527               |3796149               |Black    |75 + |GT 150k |24906                  |0.00657101156536             |24906.0131420231  |0.007060121039448             |237.205946683368 |0.006303180605968                           |257.377773683475 |-2.99401078783461 |24888.8353258109    |0.010341093519011|0.009530616582825|0.010792310983905     |0.008880074068455 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |502|Binghamton, NY (502)        |Verizon Postpaid |95045            |642             |840               |-2                   |95245                 |94825                 |Black    |35-54|100-150k|44                     |0.000462928866771            |43.9990741422665  |0.000453481489009             |0.380924450767509|0.000487633298596                           |0.313060577698733|-0.000975266597192|44.0679132819324    |0.007104048147138|0.008644031958819|0.00674051131293      |0.008819360596357 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |502|Binghamton, NY (502)        |AT&T Postpaid    |64582            |801             |497               |-6                   |64283                 |64873                 |Hispanic |35-54|LT $50K |217                    |0.003360173428306            |217.006720346857  |0.003279263062106             |1.62979374186652 |0.003420865352557                           |2.74011314739835 |-0.020525192115344|215.91692613344     |0.012690589832244|0.007548244461665|0.012460526111103     |0.007731437549585 |
          |2022-06-01|2022-06-01     |2022-06-01       |2022-06-01     |502|Binghamton, NY (502)        |AT&T Postpaid    |64582            |801             |497               |-6                   |64283                 |64873                 |Hispanic |35-54|LT $50K |217                    |0.003360173428306            |217.006720346857  |0.003279263062106             |1.62979374186652 |0.003420865352557                           |2.74011314739835 |-0.020525192115344|215.91692613344     |0.012690589832244|0.007548244461665|0.012460526111103     |0.007731437549585 |
        """
      }.as[MonthlyOutputWithEthnicityDMA]

      val expected = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrier] {
        """
          |date      |dma|winner_brand_plantype|ethnicity|age  |income  |subscribers     |total_subscribers|ethnicity_pct_of_carrier|
          |2022-05-01|500|AT&T Postpaid        |Asian    |35-54|GT 150k |32.8008279963039|32.8008279963039 |1.0                     |
          |2022-05-01|500|Sprint Postpaid      |Black    |18-34|$50-100K|219.908946471937|219.908946471937 |1.0                     |
          |2022-05-01|500|T-Mobile Postpaid    |Black    |55-74|GT 150k |160.138339230353|160.138339230353 |1.0                     |
          |2022-05-01|500|Verizon Postpaid     |Asian    |75 + |100-150k|99.9580968280568|99.9580968280568 |1.0                     |
          |2022-05-01|501|AT&T Postpaid        |Asian    |18-34|$50-100K|33081.2194546222|33081.2194546222 |1.0                     |
          |2022-05-01|501|T-Mobile Postpaid    |Hispanic |35-54|$50-100K|154655.113259495|154655.113259495 |1.0                     |
          |2022-05-01|501|Verizon Postpaid     |Black    |75 + |GT 150k |24888.8353258109|24888.8353258109 |1.0                     |
          |2022-05-01|502|AT&T Postpaid        |Hispanic |35-54|LT $50K |215.91692613344 |431.83385226688  |0.5                     |
          |2022-05-01|502|AT&T Postpaid        |Hispanic |35-54|LT $50K |215.91692613344 |431.83385226688  |0.5                     |
          |2022-05-01|502|Verizon Postpaid     |Black    |35-54|100-150k|44.0679132819324|44.0679132819324 |1.0                     |
        """
      }, nullable = true).as[subsEthnicityPercentOfCarrier]

      val newDate = LocalDate.parse("2022-05-01")

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateSubEthnicityPercentOfCarrierBackwardInsertMonthly(
        monthlyOutputWithEthnicityDMAData, newDate
      ).toDF(), nullable = true).as[subsEthnicityPercentOfCarrier]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateTotalLosses") {
    import spark.implicits._
    it("") {
      val wcvFlowsDMAData = setNullableStateForAllColumns(dataframe[WCVFlowsDMA] {
        """
          |month_date|dma|loser_brand_plantype|winner_brand_plantype|wins|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|10|
          |2017-08-01|500|AT&T_Postpaid Phone|Verizon_Postpaid Phone|50|
          |2019-05-01|500|AT&T_Postpaid Phone|Sprint_Postpaid Phone|20|
          |2019-05-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|50|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|70|
          """
      }, nullable = true).as[WCVFlowsDMA]

      val expected = setNullableStateForAllColumns(dataframe[totalLosses] {
        """
          |date|dma|loser_brand_plantype|losses|
          |2017-08-01|500|AT&T_Postpaid Phone|60|
          |2019-05-01|500|AT&T_Postpaid Phone|140|
          """
      }, nullable = true).as[totalLosses]

      val result = setNullableStateForAllColumns(
        DemographicsUtilsDMA().calculateTotalLosses(wcvFlowsDMAData).toDF(), nullable = true).as[totalLosses]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }
  describe("calculateTotalWins") {
    import spark.implicits._
    it("test") {
      val wcvFlowsDMAData = setNullableStateForAllColumns(dataframe[WCVFlowsDMA] {
        """
          |month_date|dma|loser_brand_plantype|winner_brand_plantype|wins|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|10|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|50|
          |2019-05-01|500|AT&T_Postpaid Phone|Sprint_Postpaid Phone|20|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|50|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|70|
        """
      }, nullable = true).as[WCVFlowsDMA]

      val expected = setNullableStateForAllColumns(dataframe[totalWins] {
        """
          |date      |dma|winner_brand_plantype|wins |
          |2017-08-01|500|AT&T_Postpaid Phone  |60.0 |
          |2019-05-01|500|Sprint_Postpaid Phone|20.0 |
          |2019-05-01|500|Other_Postpaid Phone |120.0|
        """
      }, nullable = true).as[totalWins]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateTotalWins(
        wcvFlowsDMAData).toDF(), nullable = true).as[totalWins]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }
  describe("calculateLossesEthnicityPercentOfCarrier") {
    import spark.implicits._
    it("test") {

      val subsEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrier] {
        """
        |      date|dma|winner_brand_plantype|ethnicity|  age|  income|subscribers|total_subscribers|ethnicity_pct_of_carrier|
        |2022-06-01|500|        AT&T Postpaid|    Black|18-34|$50-100K|      221.0|            221.0|                     1.0|
        |2022-06-01|500|     Cricket Postpaid| Hispanic|18-34| GT 150k|       78.0|             78.0|                     1.0|
        |2022-06-01|500|     Verizon Postpaid|    White| 75 +|100-150k|     5829.0|           5829.0|                     1.0|
        |2022-06-01|501|        AT&T Postpaid|    White|18-34|$50-100K|    13381.0|          13381.0|                     1.0|
        |2022-06-01|501|      Sprint Postpaid| Hispanic|55-74|100-150k|      722.0|            722.0|                     1.0|
        |2022-06-01|501|    T-Mobile Postpaid|    Asian|55-74| GT 150k|      136.0|            136.0|                     1.0|
        |2022-06-01|502|    T-Mobile Postpaid|    Black|55-74|$50-100K|      560.0|            560.0|                     1.0|
        |2022-06-01|503|     Cricket Postpaid|    Asian| 75 +|100-150k|     4907.0|           4907.0|                     1.0|
        |2022-06-01|503|     Verizon Postpaid|    Asian|18-34| GT 150k|       46.0|             46.0|                     1.0|
        """
      }, nullable = true).as[subsEthnicityPercentOfCarrier]

      val totalLossesData = setNullableStateForAllColumns(dataframe[totalLosses] {
        """
          |date     |dma|loser_brand_plantype|losses|
          |2022-06-01|505|Verizon Postpaid    |40.0  |
          |2022-06-01|501|AT&T Postpaid       |12.0  |
          |2022-06-01|500|AT&T Postpaid       |6.0   |
          |2022-06-01|502|T-Mobile Postpaid   |6.0   |
          |2022-06-01|503|Sprint Postpaid     |17.0  |
          |2022-06-01|502|Verizon Postpaid    |2.0   |
          |2022-06-01|504|Cricket Prepaid     |20.0  |
          |2022-06-01|501|Verizon Postpaid    |1250.0|
          |2022-06-01|503|T-Mobile Postpaid   |74.0  |
        """
      }, nullable = true).as[totalLosses]

      val expected = setNullableStateForAllColumns(dataframe[lossesEthnicityPercentOfCarrier] {
        """
          |date     |dma|ethnicity|age  |income  |loser_brand_plantype|losses_total|loser_ethnicity_pct_of_carrier|losses_by_ethnicity|
          |null      |501|Hispanic |55-74|100-150k|Sprint Postpaid     |null        |1.0                           |null               |
          |null      |503|Asian    |75 + |100-150k|Cricket Postpaid    |null        |1.0                           |null               |
          |null      |501|Asian    |55-74|GT 150k |T-Mobile Postpaid   |null        |1.0                           |null               |
          |2022-06-01|502|Black    |55-74|$50-100K|T-Mobile Postpaid   |6.0         |1.0                           |6.0                |
          |null      |503|Asian    |18-34|GT 150k |Verizon Postpaid    |null        |1.0                           |null               |
          |2022-06-01|501|White    |18-34|$50-100K|AT&T Postpaid       |12.0        |1.0                           |12.0               |
          |null      |500|White    |75 + |100-150k|Verizon Postpaid    |null        |1.0                           |null               |
          |null      |500|Hispanic |18-34|GT 150k |Cricket Postpaid    |null        |1.0                           |null               |
          |2022-06-01|500|Black    |18-34|$50-100K|AT&T Postpaid       |6.0         |1.0                           |6.0                |
        """
      }, nullable = true).as[lossesEthnicityPercentOfCarrier]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateLossesEthnicityPercentOfCarrier(
        subsEthnicityPercentOfCarrierData, totalLossesData
      ).toDF(), nullable = true).as[lossesEthnicityPercentOfCarrier]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())

    }
  }

  describe("calculateWinsEthnicityPercentOfCarrier") {
    import spark.implicits._
    it("test") {
      val subsEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrier] {
        """
          |      date|dma|winner_brand_plantype|ethnicity|  age|  income|subscribers|total_subscribers|ethnicity_pct_of_carrier|
          |2022-06-01|500|        AT&T Postpaid|    Black|18-34|$50-100K|      221.0|            221.0|                     1.0|
          |2022-06-01|500|     Cricket Postpaid| Hispanic|18-34| GT 150k|       78.0|             78.0|                     1.0|
          |2022-06-01|500|     Verizon Postpaid|    White| 75 +|100-150k|     5829.0|           5829.0|                     1.0|
          |2022-06-01|501|        AT&T Postpaid|    White|18-34|$50-100K|    13381.0|          13381.0|                     1.0|
          |2022-06-01|501|      Sprint Postpaid| Hispanic|55-74|100-150k|      722.0|            722.0|                     1.0|
          |2022-06-01|501|    T-Mobile Postpaid|    Asian|55-74| GT 150k|      136.0|            136.0|                     1.0|
          |2022-06-01|502|    T-Mobile Postpaid|    Black|55-74|$50-100K|      560.0|            560.0|                     1.0|
          |2022-06-01|503|     Cricket Postpaid|    Asian| 75 +|100-150k|     4907.0|           4907.0|                     1.0|
          |2022-06-01|503|     Verizon Postpaid|    Asian|18-34| GT 150k|       46.0|             46.0|                     1.0|
        """
      }, nullable = true).as[subsEthnicityPercentOfCarrier]

      val totalWinsData = setNullableStateForAllColumns(dataframe[totalWins] {
        """
          |date      |dma|winner_brand_plantype|wins  |
          |2022-06-01|503|T-Mobile Postpaid    |74.0  |
          |2022-06-01|502|Verizon Postpaid     |2.0   |
          |2022-06-01|503|Sprint Postpaid      |17.0  |
          |2022-06-01|504|Cricket Prepaid      |20.0  |
          |2022-06-01|502|T-Mobile Postpaid    |6.0   |
          |2022-06-01|500|AT&T Postpaid        |6.0   |
          |2022-06-01|505|Verizon Postpaid     |40.0  |
          |2022-06-01|501|AT&T Postpaid        |12.0  |
          |2022-06-01|501|Verizon Postpaid     |1250.0|
        """
      }, nullable = true).as[totalWins]

      val expected = setNullableStateForAllColumns(dataframe[winsEthnicityPercentOfCarrier] {
        """
          |date      |dma|ethnicity|age  |income  |winner_brand_plantype|wins_total |winner_ethnicity_pct_of_carrier| wins_by_ethnicity|
          |null      |501|Hispanic |55-74|100-150k|Sprint Postpaid     |null        |1.0                           |null               |
          |null      |503|Asian    |75 + |100-150k|Cricket Postpaid    |null        |1.0                           |null               |
          |null      |501|Asian    |55-74|GT 150k |T-Mobile Postpaid   |null        |1.0                           |null               |
          |2022-06-01|502|Black    |55-74|$50-100K|T-Mobile Postpaid   |6.0         |1.0                           |6.0                |
          |null      |503|Asian    |18-34|GT 150k |Verizon Postpaid    |null        |1.0                           |null               |
          |2022-06-01|501|White    |18-34|$50-100K|AT&T Postpaid       |12.0        |1.0                           |12.0               |
          |null      |500|White    |75 + |100-150k|Verizon Postpaid    |null        |1.0                           |null               |
          |null      |500|Hispanic |18-34|GT 150k |Cricket Postpaid    |null        |1.0                           |null               |
          |2022-06-01|500|Black    |18-34|$50-100K|AT&T Postpaid       |6.0         |1.0                           |6.0                |
          """
      }, nullable = true).as[winsEthnicityPercentOfCarrier]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateWinsEthnicityPercentOfCarrier(
        subsEthnicityPercentOfCarrierData, totalWinsData
      ).toDF(), nullable = true).as[winsEthnicityPercentOfCarrier]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateWinnerPercentOfLoserCreate") {
    import spark.implicits._
    it("test") {
      val wcvFlowsDMAData = setNullableStateForAllColumns(dataframe[WCVFlowsDMA] {
        """
          |month_date|dma|loser_brand_plantype|winner_brand_plantype|wins|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|10|
          |2017-08-01|500|AT&T_Postpaid Phone|Verizon_Postpaid Phone|90|
          |2019-05-01|500|AT&T_Postpaid Phone|Sprint_Postpaid Phone|20|
          |2019-05-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|30|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|50|
          """
      }, nullable = true).as[WCVFlowsDMA]

      val expected = setNullableStateForAllColumns(dataframe[winnerPercentOfLoser] {
        """
          |      date|dma|loser_brand_plantype|winner_brand_plantype|wins|total_losses|winner_pct_of_loser|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|10|100|0.1|
          |2017-08-01|500|AT&T_Postpaid Phone|Verizon_Postpaid Phone|90|100|0.9|
          |2019-05-01|500|AT&T_Postpaid Phone|Sprint_Postpaid Phone|20|100|0.2|
          |2019-05-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|30|100|0.3|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|50|100|0.5|
          """
      }, nullable = true).as[winnerPercentOfLoser]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateWinnerPercentOfLoserCreate(
        wcvFlowsDMAData
      ).toDF(), nullable = true).as[winnerPercentOfLoser]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateLoserPercentOfWinnerCreate") {
    import spark.implicits._
    it("test") {
      val wcvFlowsDMAData = setNullableStateForAllColumns(dataframe[WCVFlowsDMA] {
        """
          |month_date|dma|loser_brand_plantype|winner_brand_plantype|wins|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|10|
          |2017-08-01|500|AT&T_Postpaid Phone|Verizon_Postpaid Phone|90|
          |2019-05-01|500|AT&T_Postpaid Phone|Sprint_Postpaid Phone|20|
          |2019-05-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|30|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|50|
    """
      }, nullable = true).as[WCVFlowsDMA]

      val expected = setNullableStateForAllColumns(dataframe[loserPercentOfWinner] {
        """
          |      date|dma|loser_brand_plantype|winner_brand_plantype|wins|total_wins|loser_pct_of_winner|
          |2017-08-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|10|100|0.1|
          |2017-08-01|500|AT&T_Postpaid Phone|Verizon_Postpaid Phone|90|100|0.9|
          |2019-05-01|500|AT&T_Postpaid Phone|Sprint_Postpaid Phone|20|100|0.2|
          |2019-05-01|500|AT&T_Postpaid Phone|AT&T_Postpaid Phone|30|100|0.3|
          |2019-05-01|500|AT&T_Postpaid Phone|Other_Postpaid Phone|50|100|0.5|
    """
      }, nullable = true).as[loserPercentOfWinner]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateLoserPercentOfWinnerCreate(
        wcvFlowsDMAData
      ).toDF(), nullable = true).as[loserPercentOfWinner]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateWinsLossesByEthnicity") {
    import spark.implicits._

    it("interim1") {
      val lossesEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[lossesEthnicityPercentOfCarrier] {
        """
          |date      |dma|ethnicity|age  |income  |loser_brand_plantype  |losses_total|loser_ethnicity_pct_of_carrier|losses_by_ethnicity|
          |2017-08-01|500|Hispanic |55-74|100-150k|Verizon_Postpaid Phone|100        |0.9                            |90|
          |2017-08-01|500|Asian    |75 + |100-150k|Sprint_Postpaid Phone |100        |0.1                            |10|
          |2019-05-01|500|Asian    |55-74|GT 150k |AT&T_Postpaid Phone   |100        |0.2                            |20|
          |2019-05-01|500|Black    |55-74|$50-100K|Other_Postpaid Phone  |100        |0.3                            |30|
          |2022-06-01|500|Black    |18-34|$50-100K|AT&T_Postpaid Phone   |100        |0.5                            |50|
  """
      }, nullable = true).as[lossesEthnicityPercentOfCarrier]

      val winsEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[winsEthnicityPercentOfCarrier] {
        """
          |date      |dma|ethnicity|age  |income  |winner_brand_plantype |wins_total |winner_ethnicity_pct_of_carrier| wins_by_ethnicity|
          |2017-08-01|500|Hispanic |55-74|100-150k|AT&T_Postpaid Phone   |100        |0.9                            |90|
          |2017-08-01|500|Asian    |75 + |100-150k|AT&T_Postpaid Phone   |100        |0.1                            |10|
          |2019-05-01|500|Asian    |55-74|GT 150k |AT&T_Postpaid Phone   |100        |0.2                            |20|
          |2019-05-01|500|Black    |55-74|$50-100K|AT&T_Postpaid Phone   |100        |0.3                            |30|
          |2022-06-01|500|Black    |18-34|$50-100K|AT&T_Postpaid Phone   |100        |0.5                            |50|
          """
      }, nullable = true).as[winsEthnicityPercentOfCarrier]

      val winnerPercentOfLoserData = setNullableStateForAllColumns(dataframe[winnerPercentOfLoser] {
        """
          |      date|dma|loser_brand_plantype|winner_brand_plantype|wins|total_losses|winner_pct_of_loser|
          |2017-08-01|500|Verizon_Postpaid Phone|AT&T_Postpaid Phone|10|100|0.1|
          |2017-08-01|500|Sprint_Postpaid Phone |AT&T_Postpaid Phone|90|100|0.9|
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone|20|100|0.2|
          |2019-05-01|500|Other_Postpaid Phone  |AT&T_Postpaid Phone|30|100|0.3|
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone|50|100|0.5|
          """
      }, nullable = true).as[winnerPercentOfLoser]

      val loserPercentOfLoserData = setNullableStateForAllColumns(dataframe[loserPercentOfWinner] {
        """
          |      date|dma|loser_brand_plantype|winner_brand_plantype|wins|total_wins|loser_pct_of_winner|
          |2017-08-01|500|Verizon_Postpaid Phone|AT&T_Postpaid Phone|10|100|0.1|
          |2017-08-01|500|Sprint_Postpaid Phone |AT&T_Postpaid Phone|90|100|0.9|
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone|20|100|0.2|
          |2019-05-01|500|Other_Postpaid Phone  |AT&T_Postpaid Phone|30|100|0.3|
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone|50|100|0.5|
    """
      }, nullable = true).as[loserPercentOfWinner]

      val expected = setNullableStateForAllColumns(dataframe[winsLossesByEthnicityInterim1] {
        """
          |      date|dma|loser_brand_plantype|winner_brand_plantype|ethnicity|  age|  income|wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier|losses_by_ethnicity|wins_by_ethnicity_calc|winner_ethnicity_pct_of_carrier|wins_by_ethnicity|losses_by_ethnicity_recalc|w_l_avg_ethnicity_pct_of_carrier|loser_avg_churn_volume_by_ethnicity|winner_avg_churn_volume_by_ethnicity|
          |2017-08-01|500|Sprint_Postpaid Phone|  AT&T_Postpaid Phone|    Asian| 75 +|100-150k|90.0|       100.0|     100.0|                0.9|                           0.1|               10.0|                   9.0|                            0.1|             10.0|                       9.0|                             0.1|                                9.0|                                 9.0|
          |2017-08-01|500|Verizon_Postpaid Phone|  AT&T_Postpaid Phone| Hispanic|55-74|100-150k|10.0|       100.0|     100.0|                0.1|                           0.9|               90.0|                   9.0|                            0.9|             90.0|                       9.0|                             0.9|                                9.0|                                 9.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                           0.2|               20.0|                   4.0|                            0.2|             20.0|                       4.0|                             0.2|                                4.0|                                 4.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                           0.2|               20.0|                   4.0|                            0.2|             20.0|                      10.0|                             0.2|                                4.0|                                 4.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                           0.2|               20.0|                  10.0|                            0.2|             20.0|                       4.0|                             0.2|                               10.0|                                10.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                           0.2|               20.0|                  10.0|                            0.2|             20.0|                      10.0|                             0.2|                               10.0|                                10.0|
          |2019-05-01|500|Other_Postpaid Phone|  AT&T_Postpaid Phone|    Black|55-74|$50-100K|30.0|       100.0|     100.0|                0.3|                           0.3|               30.0|                   9.0|                            0.3|             30.0|                       9.0|                             0.3|                                9.0|                                 9.0|
          """
      }, nullable = true).as[winsLossesByEthnicityInterim1]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateWinsLossesByEthnicityInterim1(
        lossesEthnicityPercentOfCarrierData,winsEthnicityPercentOfCarrierData, winnerPercentOfLoserData,loserPercentOfLoserData
      ).toDF(), nullable = true).as[winsLossesByEthnicityInterim1]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }

    it("interim2") {
      val interim1Data = setNullableStateForAllColumns(dataframe[winsLossesByEthnicityInterim1] {
        """
          |      date|dma|loser_brand_plantype|winner_brand_plantype|ethnicity|  age|  income|wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier|losses_by_ethnicity|wins_by_ethnicity_calc|winner_ethnicity_pct_of_carrier|wins_by_ethnicity|losses_by_ethnicity_recalc|w_l_avg_ethnicity_pct_of_carrier|loser_avg_churn_volume_by_ethnicity|winner_avg_churn_volume_by_ethnicity|
          |2017-08-01|500|Sprint_Postpaid Phone|  AT&T_Postpaid Phone|    Asian| 75 +|100-150k|90.0|       100.0|     100.0|                0.9|                           0.1|               10.0|                   9.0|                            0.1|             10.0|                       9.0|                             0.1|                                9.0|                                 9.0|
          |2017-08-01|500|Verizon_Postpaid Phone|  AT&T_Postpaid Phone| Hispanic|55-74|100-150k|10.0|       100.0|     100.0|                0.1|                           0.9|               90.0|                   9.0|                            0.9|             90.0|                       9.0|                             0.9|                                9.0|                                 9.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                           0.2|               20.0|                   4.0|                            0.2|             20.0|                       4.0|                             0.2|                                4.0|                                 4.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                           0.2|               20.0|                   4.0|                            0.2|             20.0|                      10.0|                             0.2|                                4.0|                                 4.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                           0.2|               20.0|                  10.0|                            0.2|             20.0|                       4.0|                             0.2|                               10.0|                                10.0|
          |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                           0.2|               20.0|                  10.0|                            0.2|             20.0|                      10.0|                             0.2|                               10.0|                                10.0|
          |2019-05-01|500|Other_Postpaid Phone|  AT&T_Postpaid Phone|    Black|55-74|$50-100K|30.0|       100.0|     100.0|                0.3|                           0.3|               30.0|                   9.0|                            0.3|             30.0|                       9.0|                             0.3|                                9.0|                                 9.0|
          """
      }, nullable = true).as[winsLossesByEthnicityInterim1]

      val demoVarianceFactorCompressedData = setNullableStateForAllColumns(dataframe[demoVarianceFactorCompressed] {
        """
          |date      |winner_brand_plantype |ethnicity|age  |income  |wins_numerator|wins_denominator|wins_prop            |long_term_wins_numerator|long_term_wins_denominator|long_term_wins_prop  |variance_factor   |variance_factor_compressed|
          |2017-08-01|AT&T_Postpaid Phone   |Asian    |75+|LT $50K |457           |16583           |0.027558342881263943 |3125                    |107855                    |0.028974085577859163 |0.9511376228667913|0.9511376228667913|
          |2017-08-01|AT&T_Postpaid Phone   |Hispanic    |55-74|LT $50K |746           |16583           |0.04498582886088163  |4814                    |107855                    |0.04463399935098048  |1.007882545033317 |1.007882545033317|
          |2019-05-01|AT&T_Postpaid Phone   |Asian    |55-74|$50-100K|18            |16583           |0.001085448953747814 |126                     |107855                    |0.0011682351304992814|0.9291356897338927|0.9291356897338927|
          |2019-05-01|AT&T_Postpaid Phone   |Asian    |55-74|$50-100K|122           |16583           |0.007356931797624073 |1059                    |107855                    |0.009818738120624913 |0.7492746733075962|0.9|
          |2019-05-01|AT&T_Postpaid Phone   |Asian |55-74|100-150k|107           |16583           |0.006452391002834228 |801                     |107855                    |0.007426637615316861 |0.8688172679284465|0.9|
          |2019-05-01|AT&T_Postpaid Phone   |Black    |55-74|$50-100K|88            |16583           |0.005306639329433757 |834                     |107855                    |0.007732603959019054 |0.6862680873813883|0.9|
        """
      }, nullable = true).as[demoVarianceFactorCompressed]

      val expected = setNullableStateForAllColumns(dataframe[winsLossesByEthnicityInterim2] {
        """
          |      date|dma|loser_brand_plantype  |winner_brand_plantype|ethnicity|  age|  income|wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier_original|losses_by_ethnicity|winner_ethnicity_pct_of_carrier_original|wins_by_ethnicity|loser_avg_churn_volume_by_ethnicity_original|winner_avg_churn_volume_by_ethnicity_original|variance_factor_compressed_not_null_check|variance_factor_compressed|
          |2017-08-01|500|Sprint_Postpaid Phone |  AT&T_Postpaid Phone|    Asian| 75 +|100-150k|90.0|       100.0|     100.0|                0.9|                                    0.1|               10.0|                                     0.1|             10.0|                                         9.0|                                          9.0|                                        0|                       1.0|
          |2017-08-01|500|Verizon_Postpaid Phone|  AT&T_Postpaid Phone| Hispanic|55-74|100-150k|10.0|       100.0|     100.0|                0.1|                                    0.9|               90.0|                                     0.9|             90.0|                                         9.0|                                          9.0|                                        0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                                    0.2|               20.0|                                     0.2|             20.0|                                         4.0|                                          4.0|                                        0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                                    0.2|               20.0|                                     0.2|             20.0|                                         4.0|                                          4.0|                                        0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                                    0.2|               20.0|                                     0.2|             20.0|                                        10.0|                                         10.0|                                        0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                                    0.2|               20.0|                                     0.2|             20.0|                                        10.0|                                         10.0|                                        0|                       1.0|
          |2019-05-01|500|Other_Postpaid Phone  |  AT&T_Postpaid Phone|    Black|55-74|$50-100K|30.0|       100.0|     100.0|                0.3|                                    0.3|               30.0|                                     0.3|             30.0|                                         9.0|                                          9.0|                                        1|                       0.9|
          """
      }, nullable = true).as[winsLossesByEthnicityInterim2]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateWinsLossesByEthnicityInterim2(
        interim1Data, demoVarianceFactorCompressedData
      ).toDF(), nullable = true).as[winsLossesByEthnicityInterim2]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }

    it("final calculateWinsLossesByEthnicity") {
      val interim2Data = setNullableStateForAllColumns(dataframe[winsLossesByEthnicityInterim2] {
        """
          |      date|dma|loser_brand_plantype  |winner_brand_plantype|ethnicity|  age|  income|wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier_original|losses_by_ethnicity|winner_ethnicity_pct_of_carrier_original|wins_by_ethnicity|loser_avg_churn_volume_by_ethnicity_original|winner_avg_churn_volume_by_ethnicity_original|variance_factor_compressed_not_null_check|variance_factor_compressed|
          |2017-08-01|500|Sprint_Postpaid Phone |  AT&T_Postpaid Phone|    Asian| 75 +|100-150k|90.0|       100.0|     100.0|                0.9|                                    0.1|               10.0|                                     0.1|             10.0|                                         9.0|                                          9.0|                                      0.0|                       1.0|
          |2017-08-01|500|Verizon_Postpaid Phone|  AT&T_Postpaid Phone| Hispanic|55-74|100-150k|10.0|       100.0|     100.0|                0.1|                                    0.9|               90.0|                                     0.9|             90.0|                                         9.0|                                          9.0|                                      0.0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                                    0.2|               20.0|                                     0.2|             20.0|                                         4.0|                                          4.0|                                      0.0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                                    0.2|               20.0|                                     0.2|             20.0|                                         4.0|                                          4.0|                                      0.0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                                    0.2|               20.0|                                     0.2|             20.0|                                        10.0|                                         10.0|                                      0.0|                       1.0|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|50.0|       100.0|     100.0|                0.5|                                    0.2|               20.0|                                     0.2|             20.0|                                        10.0|                                         10.0|                                      0.0|                       1.0|
          |2019-05-01|500|Other_Postpaid Phone  |  AT&T_Postpaid Phone|    Black|55-74|$50-100K|30.0|       100.0|     100.0|                0.3|                                    0.3|               30.0|                                     0.3|             30.0|                                         9.0|                                          9.0|                                      1.0|                       0.9|
       """
      }, nullable = true).as[winsLossesByEthnicityInterim2]
      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateWinsLossesByEthnicity(
        interim2Data
      ).toDF(), nullable = true).as[winsLossesByEthnicity]

      val expected = setNullableStateForAllColumns(dataframe[winsLossesByEthnicity] {
        """
          |date      |dma|loser_brand_plantype  |winner_brand_plantype|ethnicity|age  |income  |wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier_original|losses_by_ethnicity|winner_ethnicity_pct_of_carrier_original|wins_by_ethnicity|loser_avg_churn_volume_by_ethnicity_original|winner_avg_churn_volume_by_ethnicity_original|variance_factor_compressed|loser_ethnicity_pct_of_carrier_numerator|winner_ethnicity_pct_of_carrier_numerator|loser_ethnicity_pct_of_carrier_denominator|winner_ethnicity_pct_of_carrier_denominator|loser_ethnicity_pct_of_carrier|winner_ethnicity_pct_of_carrier|loser_avg_churn_volume_by_ethnicity|winner_avg_churn_volume_by_ethnicity|
          |2017-08-01|500|Sprint_Postpaid Phone |AT&T_Postpaid Phone  |Asian    |75 + |100-150k|90.0|100.0       |100.0     |0.9                |0.1                                    |10.0               |0.1                                     |10.0             |9.0                                         |9.0                                          |1.0                       |0.1                                     |0.1                                      |0.1                                       |0.1                                        |1.0                           |1.0                            |90.0                               |90.0                                |
          |2017-08-01|500|Verizon_Postpaid Phone|AT&T_Postpaid Phone  |Hispanic |55-74|100-150k|10.0|100.0       |100.0     |0.1                |0.9                                    |90.0               |0.9                                     |90.0             |9.0                                         |9.0                                          |1.0                       |0.9                                     |0.9                                      |0.9                                       |0.9                                        |1.0                           |1.0                            |10.0                               |10.0                                |
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone  |Asian    |55-74|GT 150k |20.0|100.0       |100.0     |0.2                |0.2                                    |20.0               |0.2                                     |20.0             |4.0                                         |4.0                                          |1.0                       |0.2                                     |0.2                                      |0.8                                       |0.8                                        |0.25                          |0.25                           |5.0                                |5.0                                 |
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone  |Asian    |55-74|GT 150k |20.0|100.0       |100.0     |0.2                |0.2                                    |20.0               |0.2                                     |20.0             |4.0                                         |4.0                                          |1.0                       |0.2                                     |0.2                                      |0.8                                       |0.8                                        |0.25                          |0.25                           |5.0                                |5.0                                 |
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone  |Asian    |55-74|GT 150k |50.0|100.0       |100.0     |0.5                |0.2                                    |20.0               |0.2                                     |20.0             |10.0                                        |10.0                                         |1.0                       |0.2                                     |0.2                                      |0.8                                       |0.8                                        |0.25                          |0.25                           |12.5                               |12.5                                |
          |2019-05-01|500|AT&T_Postpaid Phone   |AT&T_Postpaid Phone  |Asian    |55-74|GT 150k |50.0|100.0       |100.0     |0.5                |0.2                                    |20.0               |0.2                                     |20.0             |10.0                                        |10.0                                         |1.0                       |0.2                                     |0.2                                      |0.8                                       |0.8                                        |0.25                          |0.25                           |12.5                               |12.5                                |
          |2019-05-01|500|Other_Postpaid Phone  |AT&T_Postpaid Phone  |Black    |55-74|$50-100K|30.0|100.0       |100.0     |0.3                |0.3                                    |30.0               |0.3                                     |30.0             |9.0                                         |9.0                                          |0.9                       |0.27                                    |0.27                                     |0.27                                      |0.27                                       |1.0                           |1.0                            |30.0                               |30.0                                |
        """
      }, nullable = true).as[winsLossesByEthnicity]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateWinsFromAverageEthnicityPercentOfCarrier") {
    import spark.implicits._
    it("test") {
      val winsLossesByEthnicityData = setNullableStateForAllColumns(dataframe[winsLossesByEthnicity] {
        """
        |      date|dma|loser_brand_plantype|winner_brand_plantype|ethnicity|  age|  income|wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier_original|losses_by_ethnicity|winner_ethnicity_pct_of_carrier_original|wins_by_ethnicity|loser_avg_churn_volume_by_ethnicity_original|winner_avg_churn_volume_by_ethnicity_original|loser_ethnicity_pct_of_carrier_numerator|winner_ethnicity_pct_of_carrier_numerator|loser_ethnicity_pct_of_carrier_denominator|winner_ethnicity_pct_of_carrier_denominator|variance_factor_compressed|loser_ethnicity_pct_of_carrier|winner_ethnicity_pct_of_carrier|loser_avg_churn_volume_by_ethnicity|winner_avg_churn_volume_by_ethnicity|
        |2017-08-01|500|Sprint_Postpaid Phone|  AT&T_Postpaid Phone|    Asian| 75 +|100-150k|90.0|       100.0|     100.0|                0.9|                                    0.1|               10.0|                                     0.1|             10.0|                                         9.0|                                          9.0|                                     0.1|                                      0.1|                                       0.1|                                        0.1|                       1.0|                          0.5|                           0.5|                               10|                                10|
        |2017-08-01|500|Verizon_Postpaid Phone|  AT&T_Postpaid Phone| Hispanic|55-74|100-150k|10.0|       100.0|     100.0|                0.1|                                    0.9|               90.0|                                     0.9|             90.0|                                         9.0|                                          9.0|                                     0.9|                                      0.9|                                       0.9|                                        0.9|                       1.0|                          0.5|                          0.5|                               10|                                10|
        |2019-05-01|500| AT&T_Postpaid Phone|  AT&T_Postpaid Phone|    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                                    0.2|               20.0|                                     0.2|             20.0|                                         4.0|                                          4.0|                                     0.2|                                      0.2|                                       0.8|                                        0.8|                       1.0|                          0.5|                           0.5|                               10|                                10|
        """
      }, nullable = true).as[winsLossesByEthnicity]

      val totalWinsData = setNullableStateForAllColumns(dataframe[totalWins] {
        """
          |date      |dma|winner_brand_plantype|wins  |
          |2017-08-01|500|AT&T_Postpaid Phone  |74.0  |
          |2017-08-01|500|AT&T_Postpaid Phone  |2.0   |
          |2019-05-01|500|AT&T_Postpaid Phone  |17.0  |
        """
      }, nullable = true).as[totalWins]

      val expected = setNullableStateForAllColumns(dataframe[winsFromAverageEthnicityPercentOfCarrier] {
        """
          |      date|dma|ethnicity|  age|  income|winner_brand_plantype|wins_total|wins_from_average_by_ethnicity|winner_from_average_ethnicity_pct_of_carrier|
          |2017-08-01|500|    Asian| 75 +|100-150k|  AT&T_Postpaid Phone|      74.0|                          10.0|                         0.13513513513513514|
          |2017-08-01|500|    Asian| 75 +|100-150k|  AT&T_Postpaid Phone|       2.0|                          10.0|                                         5.0|
          |2017-08-01|500| Hispanic|55-74|100-150k|  AT&T_Postpaid Phone|      74.0|                          10.0|                         0.13513513513513514|
          |2017-08-01|500| Hispanic|55-74|100-150k|  AT&T_Postpaid Phone|       2.0|                          10.0|                                         5.0|
          |2019-05-01|500|    Asian|55-74| GT 150k|  AT&T_Postpaid Phone|      17.0|                          10.0|                          0.5882352941176471|
    """
      }, nullable = true).as[winsFromAverageEthnicityPercentOfCarrier]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateWinsFromAverageEthnicityPercentOfCarrier(
        winsLossesByEthnicityData,totalWinsData
      ).toDF(), nullable = true).as[winsFromAverageEthnicityPercentOfCarrier]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateLossesFromAverageEthnicityPercentOfCarrier") {
    import spark.implicits._
    it("test") {
      val winsLossesByEthnicityData = setNullableStateForAllColumns(dataframe[winsLossesByEthnicity] {
        """
          |      date|dma|loser_brand_plantype  |winner_brand_plantype |ethnicity|  age|  income|wins|total_losses|total_wins|winner_pct_of_loser|loser_ethnicity_pct_of_carrier_original|losses_by_ethnicity|winner_ethnicity_pct_of_carrier_original|wins_by_ethnicity|loser_avg_churn_volume_by_ethnicity_original|winner_avg_churn_volume_by_ethnicity_original|loser_ethnicity_pct_of_carrier_numerator|winner_ethnicity_pct_of_carrier_numerator|loser_ethnicity_pct_of_carrier_denominator|winner_ethnicity_pct_of_carrier_denominator|variance_factor_compressed|loser_ethnicity_pct_of_carrier|winner_ethnicity_pct_of_carrier|loser_avg_churn_volume_by_ethnicity|winner_avg_churn_volume_by_ethnicity|
          |2017-08-01|500|Sprint_Postpaid Phone |  AT&T_Postpaid Phone |    Asian| 75 +|100-150k|90.0|       100.0|     100.0|                0.9|                                    0.1|               10.0|                                     0.1|             10.0|                                         9.0|                                          9.0|                                     0.1|                                      0.1|                                       0.1|                                        0.1|                       1.0|                          0.5|                           0.5|                               10|                                10|
          |2017-08-01|500|Verizon_Postpaid Phone|  AT&T_Postpaid Phone | Hispanic|55-74|100-150k|10.0|       100.0|     100.0|                0.1|                                    0.9|               90.0|                                     0.9|             90.0|                                         9.0|                                          9.0|                                     0.9|                                      0.9|                                       0.9|                                        0.9|                       1.0|                          0.5|                          0.5|                               10|                                10|
          |2019-05-01|500| AT&T_Postpaid Phone  |  AT&T_Postpaid Phone |    Asian|55-74| GT 150k|20.0|       100.0|     100.0|                0.2|                                    0.2|               20.0|                                     0.2|             20.0|                                         4.0|                                          4.0|                                     0.2|                                      0.2|                                       0.8|                                        0.8|                       1.0|                          0.5|                           0.5|                               10|                                10|
       """
      }, nullable = true).as[winsLossesByEthnicity]

      val totalLossesData = setNullableStateForAllColumns(dataframe[totalLosses] {
        """
          |date      |dma|loser_brand_plantype  |losses|
          |2017-08-01|500|Sprint_Postpaid Phone |20    |
          |2017-08-01|500|Verizon_Postpaid Phone|10    |
          |2019-05-01|500|AT&T_Postpaid Phone   |30    |
        """
      }, nullable = true).as[totalLosses]

      val expected = setNullableStateForAllColumns(dataframe[lossesFromAverageEthnicityPercentOfCarrier] {
        """
          |      date|dma|ethnicity|  age|  income|loser_brand_plantype  |losses_total|losses_from_average_by_ethnicity|loser_from_average_ethnicity_pct_of_carrier|
          |2017-08-01|500|    Asian| 75 +|100-150k|Sprint_Postpaid Phone |        20.0|                            10.0|                                        0.5|
          |2017-08-01|500| Hispanic|55-74|100-150k|Verizon_Postpaid Phone|        10.0|                            10.0|                                        1.0|
          |2019-05-01|500|    Asian|55-74| GT 150k| AT&T_Postpaid Phone  |        30.0|                            10.0|                         0.3333333333333333|
        """
      }, nullable = true).as[lossesFromAverageEthnicityPercentOfCarrier]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateLossesFromAverageEthnicityPercentOfCarrier(
        winsLossesByEthnicityData, totalLossesData
      ).toDF(), nullable = true).as[lossesFromAverageEthnicityPercentOfCarrier]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())

    }
  }

  describe("calculateWMSWithEthnicityFromAverageBackwards") {
    import spark.implicits._

    it("test") {
      val wmsDMAData = setNullableStateForAllColumns(dataframe[WmsDma] {
        """
          |month_date|       brand_plantype|dma|dma_name|gross_adds|gross_losses|base_adjustment|subscribers|last_period_subs|next_period_subs|
          |2017-08-01|Sprint_Postpaid Phone|500|aaa     |100       |100         |5              |300        |100            |100             |
          |2017-08-01|Verizon_Postpaid Phone|500|aaa     |100       |100         |5              |300        |200            |100             |
          |2019-05-01|AT&T_Postpaid Phone  |500|aaa     |100       |100         |5              |300        |100            |100             |
       """
      }, nullable = true).as[WmsDma]

      val subsEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrier] {
        """
          |date      |dma|winner_brand_plantype|ethnicity|age|income|subscribers|total_subscribers|ethnicity_pct_of_carrier|
          |2017-08-01|500|Sprint_Postpaid Phone|Asian|75 +|100-150k|221.0|221.0|1.0|
          |2017-08-01|500|AT&T_Postpaid Phone|Hispanic|55-74|100-150k|78.0|78.0|1.0|
          |2017-08-01|500|Verizon_Postpaid Phone|Hispanic|55-74|100-150k|5829.0|5829.0|1.0|
          |2017-08-01|500|AT&T_Postpaid Phone|Asian|75 +|100-150k|13381.0|13381.0|1.0|
          |2019-05-01|500|AT&T_Postpaid Phone|Hispanic|55-74|100-150k|722.0|722.0|1.0|
          """
      }, nullable = true).as[subsEthnicityPercentOfCarrier]

      val winsFromAverageEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[winsFromAverageEthnicityPercentOfCarrier] {
        """
          |      date|dma|ethnicity|  age|  income|winner_brand_plantype|wins_total|wins_from_average_by_ethnicity|winner_from_average_ethnicity_pct_of_carrier|
          |2017-08-01|500|    Asian| 75 +|100-150k|  Sprint_Postpaid Phone|      74.0|                          10.0|                         0.13513513513513514|
          |2017-08-01|500|    Asian| 75 +|100-150k|  AT&T_Postpaid Phone|       2.0|                          10.0|                                         5.0|
          |2017-08-01|500| Hispanic|55-74|100-150k|  Verizon_Postpaid Phone|      74.0|                          10.0|                         0.13513513513513514|
          |2017-08-01|500| Hispanic|55-74|100-150k|  AT&T_Postpaid Phone|       2.0|                          10.0|                                         5.0|
          |2019-05-01|500|    Asian|55-74| GT 150k|  AT&T_Postpaid Phone|      17.0|                          10.0|                          0.5882352941176471|
        """
      }, nullable = true).as[winsFromAverageEthnicityPercentOfCarrier]

      val lossesFromAverageEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[lossesFromAverageEthnicityPercentOfCarrier] {
        """
          |      date|dma|ethnicity|  age|  income|loser_brand_plantype|losses_total|losses_from_average_by_ethnicity|loser_from_average_ethnicity_pct_of_carrier|
          |2017-08-01|500|    Asian| 75 +|100-150k|Sprint_Postpaid Phone|        20.0|                            10.0|                                        0.5|
          |2017-08-01|500| Hispanic|55-74|100-150k|Verizon_Postpaid Phone|        10.0|                            10.0|                                        1.0|
          |2019-05-01|500|    Asian|55-74| GT 150k| AT&T_Postpaid Phone|        30.0|                            10.0|                         0.3333333333333333|
          """
      }, nullable = true).as[lossesFromAverageEthnicityPercentOfCarrier]

      val totalLossesData = setNullableStateForAllColumns(dataframe[totalLosses] {
        """
          |date      |dma|loser_brand_plantype   |losses|
          |2017-08-01|500|Sprint_Postpaid Phone  |20    |
          |2017-08-01|500|Verizon_Postpaid Phone |10    |
          |2019-05-01|500|AT&T_Postpaid Phone    |30    |
        """
      }, nullable = true).as[totalLosses]

      val totalWinsData = setNullableStateForAllColumns(dataframe[totalWins] {
        """
          |date      |dma|winner_brand_plantype|wins  |
          |2017-08-01|500|Sprint_Postpaid Phone  |74.0  |
          |2017-08-01|500|Verizon_Postpaid Phone|2.0   |
          |2019-05-01|500|AT&T_Postpaid Phone  |17.0  |
        """
      }, nullable = true).as[totalWins]

      val expected = setNullableStateForAllColumns(dataframe[wmsWithEthnicityFromAverageBackwards] {
        """
          | wms_month|subs_join_month|losses_join_month|wins_join_month|dma|dma_name|      brand_plantype   |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|  age|  income|subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses|winner_from_average_ethnicity_pct_of_carrier|        gross_adds|   base_adjustment|
          |2017-08-01|    2017-08-01 |      2017-08-01 |    2017-08-01 |500|     aaa|Sprint_Postpaid Phone |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0|    Asian| 75 +|100-150k|                  221.0|                          1.0|             300.0|                           0.5|        50.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2017-08-01|    2017-08-01 |      2017-08-01 |    2017-08-01 |500|     aaa|Verizon_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 200.0|                 100.0| Hispanic|55-74|100-150k|                 5829.0|                          1.0|             300.0|                           1.0|       100.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2019-05-01|    2019-05-01 |            null |          null |500|     aaa| AT&T_Postpaid Phone  |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0| Hispanic|55-74|100-150k|                  722.0|                          1.0|             300.0|                          null|       100.0|                                        null|             100.0|               5.0|
        """
      }, nullable = true).as[wmsWithEthnicityFromAverageBackwards]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateWMSWithEthnicityFromAverageBackwards(
        wmsDMAData, subsEthnicityPercentOfCarrierData, lossesFromAverageEthnicityPercentOfCarrierData,winsFromAverageEthnicityPercentOfCarrierData,totalLossesData,totalWinsData
      ).toDF(), nullable = true).as[wmsWithEthnicityFromAverageBackwards]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateWMSWithEthnicityFromAverageForwards") {
    import spark.implicits._

    it("test") {
      val wmsDMAData = setNullableStateForAllColumns(dataframe[WmsDma] {
        """
          |month_date|       brand_plantype|dma|dma_name|gross_adds|gross_losses|base_adjustment|subscribers|last_period_subs|next_period_subs|
          |2017-08-01|Sprint_Postpaid Phone|500|aaa     |100       |100         |5              |300        |100            |100             |
          |2017-08-01|Verizon_Postpaid Phone|500|aaa     |100       |100         |5              |300        |200            |100             |
          |2019-05-01|AT&T_Postpaid Phone  |500|aaa     |100       |100         |5              |300        |100            |100             |
       """
      }, nullable = true).as[WmsDma]

      val subsEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[subsEthnicityPercentOfCarrier] {
        """
          |date      |dma|winner_brand_plantype|ethnicity|age|income|subscribers|total_subscribers|ethnicity_pct_of_carrier|
          |2017-08-01|500|Sprint_Postpaid Phone|Asian|75 +|100-150k|221.0|221.0|1.0|
          |2017-08-01|500|AT&T_Postpaid Phone|Hispanic|55-74|100-150k|78.0|78.0|1.0|
          |2017-08-01|500|Verizon_Postpaid Phone|Hispanic|55-74|100-150k|5829.0|5829.0|1.0|
          |2017-08-01|500|AT&T_Postpaid Phone|Asian|75 +|100-150k|13381.0|13381.0|1.0|
          |2019-05-01|500|AT&T_Postpaid Phone|Hispanic|55-74|100-150k|722.0|722.0|1.0|
          """
      }, nullable = true).as[subsEthnicityPercentOfCarrier]

      val winsFromAverageEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[winsFromAverageEthnicityPercentOfCarrier] {
        """
          |      date|dma|ethnicity|  age|  income|winner_brand_plantype|wins_total|wins_from_average_by_ethnicity|winner_from_average_ethnicity_pct_of_carrier|
          |2017-08-01|500|    Asian| 75 +|100-150k|  Sprint_Postpaid Phone|      74.0|                          10.0|                         0.13513513513513514|
          |2017-08-01|500|    Asian| 75 +|100-150k|  AT&T_Postpaid Phone|       2.0|                          10.0|                                         5.0|
          |2017-08-01|500| Hispanic|55-74|100-150k|  Verizon_Postpaid Phone|      74.0|                          10.0|                         0.13513513513513514|
          |2017-08-01|500| Hispanic|55-74|100-150k|  AT&T_Postpaid Phone|       2.0|                          10.0|                                         5.0|
          |2019-05-01|500|    Asian|55-74| GT 150k|  AT&T_Postpaid Phone|      17.0|                          10.0|                          0.5882352941176471|
        """
      }, nullable = true).as[winsFromAverageEthnicityPercentOfCarrier]

      val lossesFromAverageEthnicityPercentOfCarrierData = setNullableStateForAllColumns(dataframe[lossesFromAverageEthnicityPercentOfCarrier] {
        """
          |      date|dma|ethnicity|  age|  income|loser_brand_plantype|losses_total|losses_from_average_by_ethnicity|loser_from_average_ethnicity_pct_of_carrier|
          |2017-08-01|500|    Asian| 75 +|100-150k|Sprint_Postpaid Phone|        20.0|                            10.0|                                        0.5|
          |2017-08-01|500| Hispanic|55-74|100-150k|Verizon_Postpaid Phone|        10.0|                            10.0|                                        1.0|
          |2019-05-01|500|    Asian|55-74| GT 150k| AT&T_Postpaid Phone|        30.0|                            10.0|                         0.3333333333333333|
          """
      }, nullable = true).as[lossesFromAverageEthnicityPercentOfCarrier]

      val totalLossesData = setNullableStateForAllColumns(dataframe[totalLosses] {
        """
          |date      |dma|loser_brand_plantype   |losses|
          |2017-08-01|500|Sprint_Postpaid Phone  |20    |
          |2017-08-01|500|Verizon_Postpaid Phone |10    |
          |2019-05-01|500|AT&T_Postpaid Phone    |30    |
        """
      }, nullable = true).as[totalLosses]

      val totalWinsData = setNullableStateForAllColumns(dataframe[totalWins] {
        """
          |date      |dma|winner_brand_plantype|wins  |
          |2017-08-01|500|Sprint_Postpaid Phone  |74.0  |
          |2017-08-01|500|Verizon_Postpaid Phone|2.0   |
          |2019-05-01|500|AT&T_Postpaid Phone  |17.0  |
        """
      }, nullable = true).as[totalWins]

      val expected = setNullableStateForAllColumns(dataframe[wmsWithEthnicityFromAverageForwards] {
        """
          | wms_month|subs_join_month|losses_join_month|wins_join_month|dma|dma_name|      brand_plantype|total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|  age|  income|subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|starting_subscribers|loser_ethnicity_pct_of_carrier|gross_losses|winner_from_average_ethnicity_pct_of_carrier|        gross_adds|   base_adjustment|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01|500|     aaa|Sprint_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0|    Asian| 75 +|100-150k|                  221.0|                          1.0|               100.0|                           0.5|        50.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01|500|     aaa|Verizon_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 200.0|                 100.0| Hispanic|55-74|100-150k|                 5829.0|                          1.0|               200.0|                           1.0|       100.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2019-05-01|     2019-05-01|             null|           null|500|     aaa| AT&T_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0| Hispanic|55-74|100-150k|                  722.0|                          1.0|               100.0|                           0.0|       100.0|                                         0.0|             100.0|               5.0|
        """
      }, nullable = true).as[wmsWithEthnicityFromAverageForwards]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateWMSWithEthnicityFromAverageForwards(
        wmsDMAData, subsEthnicityPercentOfCarrierData, lossesFromAverageEthnicityPercentOfCarrierData, winsFromAverageEthnicityPercentOfCarrierData, totalLossesData, totalWinsData
      ).toDF(), nullable = true).as[wmsWithEthnicityFromAverageForwards]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateWMSWithEthnicityWithStartingSubsFromAverageBackwards") {
    import spark.implicits._
    it("test") {
      val wmsWithEthnicityFromAverageBackwardsData = setNullableStateForAllColumns(dataframe[wmsWithEthnicityFromAverageBackwards] {
        """
          | wms_month|subs_join_month|losses_join_month|wins_join_month|dma|dma_name|      brand_plantype   |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|  age|  income|subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses|winner_from_average_ethnicity_pct_of_carrier|        gross_adds|   base_adjustment|
          |2017-08-01|    2017-08-01 |      2017-08-01 |    2017-08-01 |500|     aaa|Sprint_Postpaid Phone |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0|    Asian| 75 +|100-150k|                  221.0|                          1.0|             300.0|                           0.5|        50.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2017-08-01|    2017-08-01 |      2017-08-01 |    2017-08-01 |500|     aaa|Verizon_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 200.0|                 100.0| Hispanic|55-74|100-150k|                 5829.0|                          1.0|             300.0|                           1.0|       100.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2019-05-01|    2019-05-01 |            null |          null |500|     aaa| AT&T_Postpaid Phone  |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0| Hispanic|55-74|100-150k|                  722.0|                          1.0|             300.0|                          null|       100.0|                                        null|             100.0|               5.0|
      """
      }, nullable = true).as[wmsWithEthnicityFromAverageBackwards]

      val expected = setNullableStateForAllColumns(dataframe[wmsWithEthnicityWithStartingSubsFromAverageBackwards] {
        """
          | wms_month|subs_join_month|losses_join_month|wins_join_month|dma|dma_name|      brand_plantype  |total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|  age|  income|subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses|winner_from_average_ethnicity_pct_of_carrier|        gross_adds|   base_adjustment|starting_subscribers|     gross_add_rate|         churn_rate|overall_gross_add_rate|overall_churn_rate|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01|500|     aaa|Sprint_Postpaid Phone |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0|    Asian| 75 +|100-150k|                  221.0|                          1.0|             300.0|                           0.5|        50.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|  335.81081081081084|0.04024144869215292|0.14889336016096577|                   1.0|               1.0|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01|500|     aaa|Verizon_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 200.0|                 100.0| Hispanic|55-74|100-150k|                 5829.0|                          1.0|             300.0|                           1.0|       100.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|  385.81081081081084|0.03502626970227671|0.2591943957968476 |                   0.5|               0.5|
          |2019-05-01|     2019-05-01|             null|           null|500|     aaa| AT&T_Postpaid Phone  |            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0| Hispanic|55-74|100-150k|                  722.0|                          1.0|             300.0|                           0.0|       100.0|                                         0.0|             100.0|               5.0|               295.0| 0.3389830508474576| 0.3389830508474576|                   1.0|               1.0|
        """
      }, nullable = true).as[wmsWithEthnicityWithStartingSubsFromAverageBackwards]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().calculateWMSWithEthnicityWithStartingSubsFromAverageBackwards(
        wmsWithEthnicityFromAverageBackwardsData
      ).toDF(), nullable = true).as[wmsWithEthnicityWithStartingSubsFromAverageBackwards]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("assignCarrierNameCaseStatement") {
    import spark.implicits._
    it("test") {
      val wirelessMovementWideData = setNullableStateForAllColumns(dataframe[WirelessMovementWideGeneric] {
        """
          |zip_cd|region_dim_id|state|bta|cbsa|dma|cma|secondary_sp|primary_sp|merger_id|tenure|primary_plan_type_id|secondary_plan_type_id|prior_losing_plan_type_id|prior_losing_sp|ustn_ind|adjusted_wins|customer_base_date|
          | 41104|            0|    0|  0|   0|  0|  0|           3|         6|        0|     0|                   1|                     1|                        0|               0|    true|          100|2020-02-01|
          | 41104|            0|    0|  0|   0|  0|  0|           3|         4|        0|     0|                   1|                     1|                        0|               0|    true|          100|2020-02-01|
        """
      }, nullable = true).as[WirelessMovementWideGeneric]

      val result = wirelessMovementWideData
        .withColumn("a",lit(0))
        .transform(DemographicsUtilsDMA()
        .assignCarrierNameCaseStatement($"primary_sp", $"primary_plan_type_id", "winner"))
    }
  }

  describe("wmsWithEthnicityWithEndingSubsFromAverageForwards") {
    import spark.implicits._
    it("test") {
      val wmsWithEthnicityFromAverageBackwardsData = setNullableStateForAllColumns(dataframe[wmsWithEthnicityFromAverageForwards] {
        """
          | wms_month|subs_join_month|losses_join_month|wins_join_month|dma|dma_name|      brand_plantype|total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|  age|  income|subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|starting_subscribers|loser_ethnicity_pct_of_carrier|gross_losses|winner_from_average_ethnicity_pct_of_carrier|        gross_adds|   base_adjustment|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01|500|     aaa|Sprint_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0|    Asian| 75 +|100-150k|                  221.0|                          1.0|               100.0|                           0.5|        50.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01|500|     aaa|Verizon_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 200.0|                 100.0| Hispanic|55-74|100-150k|                 5829.0|                          1.0|               200.0|                           1.0|       100.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|
          |2019-05-01|     2019-05-01|             null|           null|500|     aaa| AT&T_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0| Hispanic|55-74|100-150k|                  722.0|                          1.0|               100.0|                           0.0|       100.0|                                         0.0|             100.0|               5.0|
        """
      }, nullable = true).as[wmsWithEthnicityFromAverageForwards]

      val expected = setNullableStateForAllColumns(dataframe[wmsWithEthnicityWithStartingSubsFromAverageForwards] {
        """
          | wms_month|subs_join_month|losses_join_month|wins_join_month|dma|dma_name|      brand_plantype|total_subscribers|total_gross_adds|total_gross_losses|total_base_adjustment|total_last_period_subs|total_next_period_subs|ethnicity|  age|  income|subs_by_ethnicity_check|subs_ethnicity_pct_of_carrier|ending_subscribers|loser_ethnicity_pct_of_carrier|gross_losses|winner_from_average_ethnicity_pct_of_carrier|        gross_adds|   base_adjustment|starting_subscribers|     gross_add_rate|churn_rate|overall_gross_add_rate|overall_churn_rate|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01|500|     aaa|Sprint_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0|    Asian| 75 +|100-150k|                  221.0|                          1.0|  64.1891891891892|                           0.5|        50.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|               100.0|0.13513513513513514|       0.5|                   1.0|               1.0|
          |2017-08-01|     2017-08-01|       2017-08-01|     2017-08-01|500|     aaa|Verizon_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 200.0|                 100.0| Hispanic|55-74|100-150k|                 5829.0|                          1.0| 114.1891891891892|                           1.0|       100.0|                         0.13513513513513514|13.513513513513514|0.6756756756756757|               200.0|0.06756756756756757|       0.5|                   0.5|               0.5|
          |2019-05-01|     2019-05-01|             null|           null|500|     aaa| AT&T_Postpaid Phone|            300.0|           100.0|             100.0|                  5.0|                 100.0|                 100.0| Hispanic|55-74|100-150k|                  722.0|                          1.0|             105.0|                           0.0|       100.0|                                         0.0|             100.0|               5.0|               100.0|                1.0|       1.0|                   1.0|               1.0|
        """
      }, nullable = true).as[wmsWithEthnicityWithStartingSubsFromAverageForwards]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().wmsWithEthnicityWithEndingSubsFromAverageForwards(
        wmsWithEthnicityFromAverageBackwardsData
      ).toDF(), nullable = true).as[wmsWithEthnicityWithStartingSubsFromAverageForwards]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("addLastAndNextPeriodSubs") {
    import spark.implicits._

    it("test") {
      val wmsDMASubsData = setNullableStateForAllColumns(dataframe[WmsDmaTemp] {
        """
          |month_date|         brand_plantype|dma|dma_name|gross_adds|gross_losses|base_adjustment|subscribers|
          |2022-07-01|  Sprint_Postpaid Phone|500|aaa     |100       |100         |5              |300        |
          |2022-07-01| Verizon_Postpaid Phone|501|aaa     |100       |100         |5              |300        |
          |2022-07-01|  AT&T_Postpaid Phone  |502|aaa     |100       |100         |5              |300        |
       """
      }, nullable = true).as[WmsDmaTemp]

      val wmsDMALastPeriodSubsData = setNullableStateForAllColumns(dataframe[WmsDmaTemp] {
        """
          |month_date|         brand_plantype|dma|dma_name|gross_adds|gross_losses|base_adjustment|subscribers|
          |2022-06-01|  Sprint_Postpaid Phone|500|aaa     |100       |100         |5              |200        |
          |2022-06-01| Verizon_Postpaid Phone|501|aaa     |100       |100         |5              |200        |
          |2022-06-01|  AT&T_Postpaid Phone  |502|aaa     |100       |100         |5              |200        |
       """
      }, nullable = true).as[WmsDmaTemp]

      val wmsDMANextPeriodSubsData = setNullableStateForAllColumns(dataframe[WmsDmaTemp] {
        """
          |month_date|         brand_plantype|dma|dma_name|gross_adds|gross_losses|base_adjustment|subscribers|
          |2022-08-01|  Sprint_Postpaid Phone|500|aaa     |100       |100         |5              |400        |
          |2022-08-01| Verizon_Postpaid Phone|501|aaa     |100       |100         |5              |400        |
          |2022-08-01|  AT&T_Postpaid Phone  |503|aaa     |100       |100         |5              |400        |
       """
      }, nullable = true).as[WmsDmaTemp]

      val expected = setNullableStateForAllColumns(dataframe[WmsDma] {
        """
          |month_date|dma|dma_name|brand_plantype        |subscribers|gross_adds|gross_losses|base_adjustment|last_period_subs|next_period_subs|
          |2022-07-01|502|aaa     |AT&T_Postpaid Phone   |300.0      |100.0     |100.0       |5.0            |200.0           |null            |
          |2022-07-01|500|aaa     |Sprint_Postpaid Phone |300.0      |100.0     |100.0       |5.0            |200.0           |400.0           |
          |2022-07-01|501|aaa     |Verizon_Postpaid Phone|300.0      |100.0     |100.0       |5.0            |200.0           |400.0           |
        """
      }, nullable = true).as[WmsDma]

      val result = setNullableStateForAllColumns(DemographicsUtilsDMA().addLastAndNextPeriodSubs(
        wmsDMASubsData, wmsDMALastPeriodSubsData, wmsDMANextPeriodSubsData
      ).toDF(), nullable = true).as[WmsDma]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset Equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

//  // LOCAL use only
//  describe("generate comparison data")  {
//    import spark.implicits._
//    it("combine") {
//      val ds1 = spark.read.option("header", "true").csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/Demographics/insertTableSparkJuly.csv")
//        .select(MonthlyOutputWithEthnicityDMA.cols.map(c => c.cast(MonthlyOutputWithEthnicityDMA.schema(s"$c").dataType)): _*)
//        .where($"wms_month" === "2022-06-01")
//        .as[MonthlyOutputWithEthnicityDMA]
//      ///Users/<USER>/Development/emr-jobs/spark/wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/Demographics/insertTableExpected.csv
//      val ds2 = spark.read.option("header", "true").csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/Demographics/insertTableExpected.csv")
//        .select(MonthlyOutputWithEthnicityDMA.cols.map(c => c.cast(MonthlyOutputWithEthnicityDMA.schema(s"$c").dataType)): _*)
//        .where($"wms_month" === "2022-06-01")
//        .as[MonthlyOutputWithEthnicityDMA]
//      val ds3 = ds1.alias("a")
//        .join(ds2.alias("b"),Seq("wms_month","subs_join_month","dma","brand_plantype","ethnicity","age","income"),"INNER")
//      ds3.show()
//    }
//  }

  // DO NOT USE THIS TESTING CASE which was used only for local runs -- vkor
//  describe("Actual Data") {
//    import spark.implicits._
//    it("test calculateEthnicityMonthlyOutput") {
//      val nationalWCVFlowsData = NationalWCVFlows.read(URI create "/home/<USER>/Downloads/national_wcv_jun24/national_wcv_data_jun24_date_converted.csv")
////      val subscribersWithEthnicityData = SubscribersByEthnicityAgeIncomeDMA.read(URI create "wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/Demographics/date_converted_test_data_dma_subs_by_ethnicity_age_income_jun2022.csv")
//      val subscribersWithEthnicityData = MonthlyOutputWithEthnicityDMA.read_csv(URI create "/home/<USER>/Downloads/dma_monthly_output_ethnicity_age_income_v9_may2024_20240719.csv")
//      val wcvFlowsDMAData = WCVFlowsDMA.read(URI create "/home/<USER>/Downloads/manual_wcv_data_reformated_20240726.csv")
//      val wmsDMACurrMonthData = WmsDma.read(URI create "/home/<USER>/Downloads/WMS_US_manual_final_output_2024-06.csv")
//      val wmsDMALastPeriodData = WmsDma.read(URI create "/home/<USER>/Downloads/WMS_US_manual_final_output_2024-05.csv")
//      val wmsDMANextPeriodData = spark.emptyDataset[WmsDmaTemp]
//      val wmsDMAData = DemographicsUtilsDMA().addLastAndNextPeriodSubs(
//        wmsDMACurrMonthData, wmsDMALastPeriodData, wmsDMANextPeriodData
//      )
//
////      nationalWCVFlowsData.show(10, false)
////      nationalWCVFlowsData.repartition(1).write.option("header", true).parquet("/home/<USER>/Downloads/national_wcv_jun24_parquet")
////
////      subscribersWithEthnicityData.show(10, false)
////      subscribersWithEthnicityData.withColumn("month", $"wms_month").repartition(1).write.option("header", true).parquet("/home/<USER>/Downloads/dma_monthly_output_may24_parquet_withMonth")
////
////      wcvFlowsDMAData.show(10, false)
////      wcvFlowsDMAData.repartition(1).write.option("header", true).parquet("/home/<USER>/Downloads/wcv_flows_dma_parquet")
////
////      wmsDMAData.show(10, false)
////      wmsDMAData.repartition(1).write.option("header", true).parquet("/home/<USER>/Downloads/wms_dma_data_with_prev_next_subs_parquet")
//
//      val newMonth = LocalDate.of(2024, 6, 1)
//
//      val demoVarianceInputThreeMonthsData = DemographicsUtilsDMA().calculateDemoVarianceInputThreeMonths(
//        newMonth, nationalWCVFlowsData
//      )
//      //      println("Count of demoVarianceFactorCompressedData", demoVarianceInputThreeMonthsData.select(col("wins")).rdd.map(_(0).asInstanceOf[Double]).reduce(_+_))
////      demoVarianceInputThreeMonthsData.show(10)
//      println("demoVarianceInputThreeMonthsData Verizon Postpaid")
//      demoVarianceInputThreeMonthsData.filter(
//        $"winner_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+"
//      ).show(false)
//
//      val demoVarianceFixedTwoYearsData = DemographicsUtilsDMA().calculateDemoVarianceFixedTwoYears(
//        newMonth, nationalWCVFlowsData
//      )
//      //      println("Count of demoVarianceFixedTwoYearsData", demoVarianceFixedTwoYearsData.select(col("wins")).rdd.map(_(0).asInstanceOf[Double]).reduce(_+_))
//      println("demoVarianceFixedTwoYearsData Verizon Postpaid")
//      demoVarianceFixedTwoYearsData.filter(
//        $"winner_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+"
//      ).show(false)
//
//      val demoVarianceFactorCompressedData = DemographicsUtilsDMA().calculateDemoVarianceFactorCompressed(
//        newMonth, 1.1, 0.9, demoVarianceInputThreeMonthsData, demoVarianceFixedTwoYearsData
//      )
//      //      demoVarianceFactorCompressedData.show(false)
//      //      println("Count of demoVarianceFactorCompressedData", demoVarianceFactorCompressedData.count())
//      //      println("Count of demoVarianceFactorCompressedData", demoVarianceFactorCompressedData.select(col("variance_factor_compressed")).rdd.map(_(0).asInstanceOf[Double]).reduce(_+_))
//      println("demoVarianceFactorCompressedData Verizon Postpaid")
//      demoVarianceFactorCompressedData.filter(
//        $"winner_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+"
//      ).show(false)
//
//      val subEthnicityPercentOfCarrierData = DemographicsUtilsDMA().calculateSubEthnicityPercentOfCarrierForwardInsertMonthly(
//        subscribersWithEthnicityData, newMonth
//      )
//      //      subEthnicityPercentOfCarrierData.show(false)
//      //      println("Count of subEthnicityPercentOfCarrierData", subEthnicityPercentOfCarrierData.count())
//      //      println("Count of subEthnicityPercentOfCarrierData", subEthnicityPercentOfCarrierData.select(col("ethnicity_pct_of_carrier")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("subEthnicityPercentOfCarrierData Verizon Postpaid")
//      subEthnicityPercentOfCarrierData.filter(
//        $"winner_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+" &&
//          $"dma" === "501"
//      ).show(false)
//
//      val totalLossesData = DemographicsUtilsDMA().calculateTotalLosses(
//        wcvFlowsDMAData
//      )
//      //      totalLossesData.show(false)
//      //      println("Count of totalLossesData", totalLossesData.count())
//      //      println("Count of totalLossesData", totalLossesData.select(col("losses")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("totalLossesData Verizon Postpaid")
//      totalLossesData.filter(
//        $"loser_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"dma" === "501"
//      ).show(false)
//
//      val totalWinsData = DemographicsUtilsDMA().calculateTotalWins(
//        wcvFlowsDMAData
//      )
//      //      totalWinsData.show(false)
//      //      println("Count of totalWinsData", totalWinsData.count())
//      //      println("Count of totalWinsData", totalWinsData.select(col("wins")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("totalWinsData Verizon Postpaid")
//      totalWinsData.filter(
//        $"winner_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"dma" === "501"
//      ).show(false)
//
//
//      val lossesEthnicityPercentOfCarrierData = DemographicsUtilsDMA().calculateLossesEthnicityPercentOfCarrier(
//        subEthnicityPercentOfCarrierData, totalLossesData
//      )
//      //      lossesEthnicityPercentOfCarrierData.show(false)
//      //      println("Count of lossesEthnicityPercentOfCarrierData", lossesEthnicityPercentOfCarrierData.count())
//      //      println("Count of lossesEthnicityPercentOfCarrierData", lossesEthnicityPercentOfCarrierData.select(col("losses_by_ethnicity")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("lossesEthnicityPercentOfCarrierData Verizon Postpaid")
//      lossesEthnicityPercentOfCarrierData.filter(
//        $"loser_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+" &&
//          $"dma" === "501"
//      ).show(false)
//
//      val winsEthnicityPercentOfCarrierData = DemographicsUtilsDMA().calculateWinsEthnicityPercentOfCarrier(
//        subEthnicityPercentOfCarrierData, totalWinsData
//      )
//      //      winsEthnicityPercentOfCarrierData.show(false)
//      //      println("Count of winsEthnicityPercentOfCarrierData", winsEthnicityPercentOfCarrierData.count())
//      //      println("Count of winsEthnicityPercentOfCarrierData", winsEthnicityPercentOfCarrierData.select(col("wins_by_ethnicity")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("winsEthnicityPercentOfCarrierData Verizon Postpaid")
//      winsEthnicityPercentOfCarrierData.filter(
//        $"winner_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+" &&
//          $"dma" === "501"
//      ).show(false)
//
//      val winnerPercentOfLoserCreateData = DemographicsUtilsDMA().calculateWinnerPercentOfLoserCreate(
//        wcvFlowsDMAData
//      )
//      //      winnerPercentOfLoserCreateData.show(false)
//      //      println("Count of winnerPercentOfLoserCreateData", winnerPercentOfLoserCreateData.count())
//      //      println("Count of winnerPercentOfLoserCreateData", winnerPercentOfLoserCreateData.select(col("wins")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("winnerPercentOfLoserCreateData Verizon Postpaid")
//      winnerPercentOfLoserCreateData.filter(
//        $"winner_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"dma" === "501"
//      ).show(false)
//
//      val loserPercentOfWinnerCreateData = DemographicsUtilsDMA().calculateLoserPercentOfWinnerCreate(
//        wcvFlowsDMAData
//      )
//      //      loserPercentOfWinnerCreateData.show(false)
//      //      println("Count of loserPercentOfWinnerCreateData", loserPercentOfWinnerCreateData.count())
//      //      println("Count of loserPercentOfWinnerCreateData", loserPercentOfWinnerCreateData.select(col("wins")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("loserPercentOfWinnerCreateData Verizon Postpaid")
//      loserPercentOfWinnerCreateData.filter(
//        $"loser_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"dma" === "501"
//      ).show(false)
//
//      val winsLossesByEthnicityInterim1Data = DemographicsUtilsDMA().calculateWinsLossesByEthnicityInterim1(
//        lossesEthnicityPercentOfCarrierData, winsEthnicityPercentOfCarrierData, winnerPercentOfLoserCreateData, loserPercentOfWinnerCreateData
//      )
//      //      winsLossesByEthnicityInterim1Data.show(false)
//      //      println("Count of winsLossesByEthnicityInterim1Data", winsLossesByEthnicityInterim1Data.count())
//      //      println("Count of winsLossesByEthnicityInterim1Data", winsLossesByEthnicityInterim1Data.select(col("winner_avg_churn_volume_by_ethnicity")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("winsLossesByEthnicityInterim1Data Verizon Postpaid")
//      winsLossesByEthnicityInterim1Data.filter(
//        $"winner_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+" &&
//          $"dma" === "501"
//      ).show(false)
//
//      val winsLossesByEthnicityInterim2Data = DemographicsUtilsDMA().calculateWinsLossesByEthnicityInterim2(
//        winsLossesByEthnicityInterim1Data, demoVarianceFactorCompressedData
//      )
//      //      winsLossesByEthnicityInterim2Data.show(false)
//      //      println("Count of winsLossesByEthnicityInterim2Data", winsLossesByEthnicityInterim2Data.count())
//      //      println("Count of winsLossesByEthnicityInterim2Data", winsLossesByEthnicityInterim2Data.select(col("variance_factor_compressed")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("winsLossesByEthnicityInterim2Data Verizon Postpaid")
//      winsLossesByEthnicityInterim2Data.filter(
//        $"winner_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+" &&
//          $"dma" === "501"
//      ).show(false)
//
//      val winsLossesByEthnicityData = DemographicsUtilsDMA().calculateWinsLossesByEthnicity(
//        winsLossesByEthnicityInterim2Data
//      )
//      //      winsLossesByEthnicityData.show(false)
//      //      println("Count of winsLossesByEthnicityData", winsLossesByEthnicityData.count())
//      //      println("Count of winsLossesByEthnicityData", winsLossesByEthnicityData.select(col("winner_ethnicity_pct_of_carrier_denominator")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      //      println("Count of winsLossesByEthnicityData", winsLossesByEthnicityData.select(col("winner_ethnicity_pct_of_carrier")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("winsLossesByEthnicityData Verizon Postpaid")
//      winsLossesByEthnicityData.filter(
//        $"winner_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+" &&
//          $"dma" === "501"
//      ).show(false)
//
//      val winsFromAverageEthnicityPercentOfCarrierData = DemographicsUtilsDMA().calculateWinsFromAverageEthnicityPercentOfCarrier(
//        winsLossesByEthnicityData, totalWinsData
//      )
//      //      winsFromAverageEthnicityPercentOfCarrierData.show(false)
//      //      println("Count of winsFromAverageEthnicityPercentOfCarrierData", winsFromAverageEthnicityPercentOfCarrierData.count())
//      //      println("Count of winsFromAverageEthnicityPercentOfCarrierData", winsFromAverageEthnicityPercentOfCarrierData.select(col("winner_from_average_ethnicity_pct_of_carrier")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("winsFromAverageEthnicityPercentOfCarrierData Verizon Postpaid")
//      winsFromAverageEthnicityPercentOfCarrierData.filter(
//        $"winner_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+" &&
//          $"dma" === "501"
//      ).show(false)
//
//      val lossesFromAverageEthnicityPercentOfCarrierData = DemographicsUtilsDMA().calculateLossesFromAverageEthnicityPercentOfCarrier(
//        winsLossesByEthnicityData, totalLossesData
//      )
//      //      lossesFromAverageEthnicityPercentOfCarrierData.show(false)
//      //      println("Count of lossesFromAverageEthnicityPercentOfCarrierData", lossesFromAverageEthnicityPercentOfCarrierData.count())
//      //      println("Count of lossesFromAverageEthnicityPercentOfCarrierData", lossesFromAverageEthnicityPercentOfCarrierData.select(col("loser_from_average_ethnicity_pct_of_carrier")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("lossesFromAverageEthnicityPercentOfCarrierData Verizon Postpaid")
//      lossesFromAverageEthnicityPercentOfCarrierData.filter(
//        $"loser_brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+" &&
//          $"dma" === "501"
//      ).show(false)
//
//      val wmsWithEthnicityFromAverageBackwardsData = DemographicsUtilsDMA().calculateWMSWithEthnicityFromAverageForwards(
//        wmsDMAData, subEthnicityPercentOfCarrierData, lossesFromAverageEthnicityPercentOfCarrierData, winsFromAverageEthnicityPercentOfCarrierData, totalLossesData, totalWinsData
//      )
////      wmsWithEthnicityFromAverageBackwardsData.show(false)
////      println("Count of wmsWithEthnicityFromAverageBackwardsData", wmsWithEthnicityFromAverageBackwardsData.count())
////      println("Count of wmsWithEthnicityFromAverageBackwardsData", wmsWithEthnicityFromAverageBackwardsData.select(col("winner_from_average_ethnicity_pct_of_carrier")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
////      println("Count of wmsWithEthnicityFromAverageBackwardsData", wmsWithEthnicityFromAverageBackwardsData.select(col("starting_subscribers")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("wmsWithEthnicityFromAverageBackwardsData Verizon Postpaid")
//      wmsWithEthnicityFromAverageBackwardsData.filter(
//        $"brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+" &&
//          $"dma" === "501"
//      ).show(false)
//
//      val wmsWithEthnicityWithStartingSubsFromAverageBackwardsData = DemographicsUtilsDMA().wmsWithEthnicityWithEndingSubsFromAverageForwards(
//        wmsWithEthnicityFromAverageBackwardsData
//      )
////      wmsWithEthnicityWithStartingSubsFromAverageBackwardsData.show(false)
////      println("Count of wmsWithEthnicityWithStartingSubsFromAverageBackwardsData", wmsWithEthnicityWithStartingSubsFromAverageBackwardsData.count())
////      println("Count of wmsWithEthnicityWithStartingSubsFromAverageBackwardsData", wmsWithEthnicityWithStartingSubsFromAverageBackwardsData.select(col("starting_subscribers")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
////      println("Count of wmsWithEthnicityWithStartingSubsFromAverageBackwardsData", wmsWithEthnicityWithStartingSubsFromAverageBackwardsData.select(col("ending_subscribers")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
////      println("Count of wmsWithEthnicityWithStartingSubsFromAverageBackwardsData", wmsWithEthnicityWithStartingSubsFromAverageBackwardsData.select(col("overall_churn_rate")).rdd.map(_(0).asInstanceOf[Double]).reduce(_ + _))
//      println("wmsWithEthnicityWithStartingSubsFromAverageBackwardsData Verizon Postpaid")
//      wmsWithEthnicityWithStartingSubsFromAverageBackwardsData.filter(
//        $"brand_plantype" === "Verizon Wireless_Postpaid Phone" &&
//          $"age" === "55-74" &&
//          $"ethnicity" === "White" &&
//          $"income" === "$150K+" &&
//          $"dma" === "501"
//      ).show(false)
////      ending subs = 497184.75990004686
//
//      wmsWithEthnicityWithStartingSubsFromAverageBackwardsData.repartition(1).write.option("header", value = true).csv("/home/<USER>/Downloads/pipeline_demographics_jun24_20240823")
//      //      val demographicsData = DemographicsMonthlyCreateOutputDMA().calculateEthnicityMonthlyOutput(
//      //        newMonth, nationalWCVFlowsData, subscribersWithEthnicityData, wcvFlowsDMAData, wmsDMAData
//      //      )
//      //
//      //      println("final output demographics")
//      //      demographicsData.show(10, false)
//      //      println("demographics count", demographicsData.count())
//
//      //      demographicsData.write.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/demographics/output_20220601_20231027")
//    }

//    it("Generate difference QA file") {
//      val manualOutputData = MonthlyOutputWithEthnicityDMA.read_csv(URI create "/home/<USER>/Downloads/12d5882a-f227-46c6-99e6-39b95d7f3533.csv")
//        .na.fill(0)
//      val automatedOutputData = MonthlyOutputWithEthnicityDMA.read_csv(URI create "/home/<USER>/Downloads/pipeline_demographics_jun24_20240823/part-00000-442a65c1-62eb-49b3-8556-9b138176346e-c000.csv")
//      manualOutputData.show(10, truncate = false)
//      println(manualOutputData.count())
//
//      automatedOutputData.show(10, truncate = false)
//      println(automatedOutputData.count())
//
//      val output = manualOutputData.alias("a")
//        .join(automatedOutputData.alias("b"),Seq(
//          "wms_month", "dma", "brand_plantype", "ethnicity", "age", "income"
//        ) , "LEFT")
//        .withColumn("total_subscribers_diff", $"a.total_subscribers" - $"b.total_subscribers")
//        .withColumn("total_gross_adds_diff", $"a.total_gross_adds" - $"b.total_gross_adds")
//        .withColumn("total_gross_losses_diff", $"a.total_gross_losses" - $"b.total_gross_losses")
//        .withColumn("total_base_adjustment_diff", $"a.total_base_adjustment" - $"b.total_base_adjustment")
//        .withColumn("total_last_period_subs_diff", $"a.total_last_period_subs" - $"b.total_last_period_subs")
//        .withColumn("total_next_period_subs_diff", $"a.total_next_period_subs" - $"b.total_next_period_subs")
//        .withColumn("subs_by_ethnicity_check_diff", $"a.subs_by_ethnicity_check" - $"b.subs_by_ethnicity_check")
//        .withColumn("subs_ethnicity_pct_of_carrier_diff", $"a.subs_ethnicity_pct_of_carrier" - $"b.subs_ethnicity_pct_of_carrier")
//        .withColumn("ending_subscribers_diff", $"a.ending_subscribers" - $"b.ending_subscribers")
//        .withColumn("loser_ethnicity_pct_of_carrier_diff", $"a.loser_ethnicity_pct_of_carrier" - $"b.loser_ethnicity_pct_of_carrier")
//        .withColumn("gross_losses_diff", $"a.gross_losses" - $"b.gross_losses")
//        .withColumn("winner_from_average_ethnicity_pct_of_carrier_diff", $"a.winner_from_average_ethnicity_pct_of_carrier" - $"b.winner_from_average_ethnicity_pct_of_carrier")
//        .withColumn("gross_adds_diff", $"a.gross_adds" - $"b.gross_adds")
//        .withColumn("base_adjustment_diff", $"a.base_adjustment" - $"b.base_adjustment")
//        .withColumn("starting_subscribers_diff", $"a.starting_subscribers" - $"b.starting_subscribers")
//        .withColumn("gross_add_rate_diff", $"a.gross_add_rate" - $"b.gross_add_rate")
//        .withColumn("churn_rate_diff", $"a.churn_rate" - $"b.churn_rate")
//        .withColumn("overall_gross_add_rate_diff", $"a.overall_gross_add_rate" - $"b.overall_gross_add_rate")
//        .withColumn("overall_churn_rate_diff", $"a.overall_churn_rate" - $"b.overall_churn_rate")
//        .select(
//          $"a.wms_month", $"a.subs_join_month", $"a.losses_join_month", $"a.wins_join_month", $"a.dma", $"a.dma_name",
//          $"a.brand_plantype",
//          $"a.total_subscribers".as("manual_total_subscribers"), $"b.total_subscribers".as("automated_total_subscribers"), $"total_subscribers_diff",
//          $"a.total_gross_adds".as("manual_total_gross_adds"), $"b.total_gross_adds".as("automated_total_gross_adds"), $"total_gross_adds_diff",
//          $"a.total_gross_losses".as("manual_total_gross_losses"), $"b.total_gross_losses".as("automated_total_gross_losses"), $"total_gross_losses_diff",
//          $"a.total_base_adjustment".as("manual_total_base_adjustment"), $"b.total_base_adjustment".as("automated_total_base_adjustment"), $"total_base_adjustment_diff",
//          $"a.total_last_period_subs".as("manual_total_last_period_subs"), $"b.total_last_period_subs".as("automated_total_last_period_subs"), $"total_last_period_subs_diff",
//          $"a.total_next_period_subs".as("manual_total_next_period_subs"), $"b.total_next_period_subs".as("automated_total_next_period_subs"), $"total_next_period_subs_diff",
//          $"a.ethnicity", $"a.age", $"a.income",
//          $"a.subs_by_ethnicity_check".as("manual_subs_by_ethnicity_check"), $"b.subs_by_ethnicity_check".as("automated_subs_by_ethnicity_check"), $"subs_by_ethnicity_check_diff",
//          $"a.subs_ethnicity_pct_of_carrier".as("manual_subs_ethnicity_pct_of_carrier"), $"b.subs_ethnicity_pct_of_carrier".as("automated_subs_ethnicity_pct_of_carrier"), $"subs_ethnicity_pct_of_carrier_diff",
//          $"a.ending_subscribers".as("manual_ending_subscribers"), $"b.ending_subscribers".as("automated_ending_subscribers"), $"ending_subscribers_diff",
//          $"a.loser_ethnicity_pct_of_carrier".as("manual_loser_ethnicity_pct_of_carrier"), $"b.loser_ethnicity_pct_of_carrier".as("automated_loser_ethnicity_pct_of_carrier"), $"loser_ethnicity_pct_of_carrier_diff",
//          $"a.gross_losses".as("manual_gross_losses"), $"b.gross_losses".as("automated_gross_losses"), $"gross_losses_diff",
//          $"a.winner_from_average_ethnicity_pct_of_carrier".as("manual_winner_from_average_ethnicity_pct_of_carrier"), $"b.winner_from_average_ethnicity_pct_of_carrier".as("automated_winner_from_average_ethnicity_pct_of_carrier"), $"winner_from_average_ethnicity_pct_of_carrier_diff",
//          $"a.gross_adds".as("manual_gross_adds"), $"b.gross_adds".as("automated_gross_adds"), $"gross_adds_diff",
//          $"a.base_adjustment".as("manual_base_adjustment"), $"b.base_adjustment".as("automated_base_adjustment"), $"base_adjustment_diff",
//          $"a.starting_subscribers".as("manual_starting_subscribers"), $"b.starting_subscribers".as("automated_starting_subscribers"), $"starting_subscribers_diff",
//          $"a.gross_add_rate".as("manual_gross_add_rate"), $"b.gross_add_rate".as("automated_gross_add_rate"), $"gross_add_rate_diff",
//          $"a.churn_rate".as("manual_churn_rate"), $"b.churn_rate".as("automated_churn_rate"), $"churn_rate_diff",
//          $"a.overall_gross_add_rate".as("manual_overall_gross_add_rate"), $"b.overall_gross_add_rate".as("automated_overall_gross_add_rate"), $"overall_gross_add_rate_diff",
//          $"a.overall_churn_rate".as("manual_overall_churn_rate"), $"b.overall_churn_rate".as("automated_overall_churn_rate"), $"overall_churn_rate_diff"
//      )
//      output.show(10, false)
//
//      output.repartition(1).write.option("header", value = true).csv("/home/<USER>/Downloads/pipeline_demographics_output_june2024_qa_comparison_20240823")
//    }

//    it("conversion") {
//      val nationalWCVFlowsData = NationalWCVFlows.read(URI create "/home/<USER>/Downloads/manual_oracle_data_reformated_20240725.csv")
////      val nationalWCVFlowsData = spark.read.option("header", true).csv("/home/<USER>/Downloads/manual_oracle_data_reformated_20240725.csv")
////        .withColumn("month_date", to_date($"month_date", "M/d/y"))
//      nationalWCVFlowsData.show(10, false)
////      nationalWCVFlowsData.repartition(1).write.option("header", true).csv("/home/<USER>/Downloads/vk/manual_oracle_data_reformated_20240725_date_fixed.csv")
//    }
//  }
}

