package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share._
import org.apache.spark.sql.types.{StructField, StructType}
import org.apache.spark.sql.DataFrame
import java.net.URI
import java.time.LocalDate


class BaseAdjustmentsDMASpec extends CldSparkBaseSpec {

  def setNullableStateForAllColumns(df: DataFrame, nullable: Boolean): DataFrame = {
    // get schema
    val schema = df.schema
    // modify [[StructField] with name `cn`
    val newSchema = StructType(schema.map {
      case StructField(c, t, _, m) ⇒ StructField(c, t, nullable = nullable, m)
    })
    // apply new schema
    df.sqlContext.createDataFrame(df.rdd, newSchema)
  }

  describe("Base Adjustments - Monthly Losses") {
    import spark.implicits._
    it("test monthly losses by geos") {

      val portedCarrierLossesByQuarterData = dataset[PortedCarrierLossesByQuarter] {
        """
        |brand|plan_type|customer_base_date|quarter|dma|switches|
        |a    |1        |2022-04-01        |2      |10 |30.0    |
        |a    |1        |2022-05-01        |2      |20 |30.0    |
        |a    |1        |2022-06-01        |2      |10 |20.0    |
        |a    |1        |2022-06-01        |2      |20 |10.0    |
        |b    |1        |2022-04-01        |2      |10 |30.0    |
        |b    |1        |2022-05-01        |2      |20 |30.0    |
        |b    |1        |2022-06-01        |2      |10 |20.0    |
        |b    |1        |2022-06-01        |2      |20 |10.0    |
      """
      }.as[PortedCarrierLossesByQuarter]

      val expectedOutput = setNullableStateForAllColumns(dataframe[MonthlyLossesByGeos] {
        """
        |brand|plan_type|customer_base_date|dma|quarter|switches_per_geo|
        |    a|        1|        2022-04-01| 10|      2|            30.0|
        |    a|        1|        2022-05-01| 20|      2|            30.0|
        |    a|        1|        2022-06-01| 10|      2|            20.0|
        |    a|        1|        2022-06-01| 20|      2|            10.0|
        |    b|        1|        2022-04-01| 10|      2|            30.0|
        |    b|        1|        2022-05-01| 20|      2|            30.0|
        |    b|        1|        2022-06-01| 10|      2|            20.0|
        |    b|        1|        2022-06-01| 20|      2|            10.0|
        """
      }, nullable = true).as[MonthlyLossesByGeos]

      val actualOutput = setNullableStateForAllColumns(BaseAdjustmentsDMA().calculateMonthlyLossesByGeos(
        portedCarrierLossesByQuarterData
      ).toDF(), nullable = true).as[MonthlyLossesByGeos]

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

  describe("Base Adjustments - Quarterly Losses") {
    import spark.implicits._
    it("test quarterly losses agg by quarter") {

      val portedCarrierLossesByQuarterData = dataset[PortedCarrierLossesByQuarter] {
        """
          |brand|plan_type|customer_base_date|quarter|dma|switches|
          |a    |1        |2022-04-01        |2      |10 |30.0    |
          |a    |1        |2022-05-01        |2      |20 |30.0    |
          |a    |1        |2022-06-01        |2      |10 |20.0    |
          |a    |1        |2022-06-01        |2      |20 |10.0    |
          |b    |1        |2022-04-01        |2      |10 |30.0    |
          |b    |1        |2022-05-01        |2      |20 |30.0    |
          |b    |1        |2022-06-01        |2      |10 |20.0    |
          |b    |1        |2022-06-01        |2      |20 |10.0    |
      """
      }.as[PortedCarrierLossesByQuarter]

      val expectedOutput = setNullableStateForAllColumns(dataframe[PortedCarrierLossesAggByQuarter] {
        """
        |brand|plan_type|quarter|switches_per_quarter|
        |    a|        1|      2|                90.0|
        |    b|        1|      2|                90.0|
        """
      }, nullable = true).as[PortedCarrierLossesAggByQuarter]

      val actualOutput = setNullableStateForAllColumns(BaseAdjustmentsDMA().calculatePortedCarrierLossesAggByQuarter(
        portedCarrierLossesByQuarterData
      ).toDF(), nullable = true).as[PortedCarrierLossesAggByQuarter]

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

  describe("Base Adjustments - Monthly Losses Share") {
    import spark.implicits._
    it("test monthly losses geographic share") {

      val processingDate = LocalDate.of(2022, 6, 1)

      val portedCarrierLossesAggByQuarterData = dataset[PortedCarrierLossesAggByQuarter] {
        """
          |brand|plan_type|quarter|switches_per_quarter|
          |    a|        1|      2|                90.0|
          |    b|        1|      2|                90.0|
        """
      }.as[PortedCarrierLossesAggByQuarter]

      val portedCarrierMonthlyLossesByGeosData = dataset[MonthlyLossesByGeos] {
        """
          |brand|plan_type|customer_base_date|dma|quarter|switches_per_geo|
          |    a|        1|        2022-04-01| 10|      2|            30.0|
          |    a|        1|        2022-05-01| 20|      2|            30.0|
          |    a|        1|        2022-06-01| 10|      2|            20.0|
          |    a|        1|        2022-06-01| 20|      2|            10.0|
          |    b|        1|        2022-04-01| 10|      2|            30.0|
          |    b|        1|        2022-05-01| 20|      2|            30.0|
          |    b|        1|        2022-06-01| 10|      2|            20.0|
          |    b|        1|        2022-06-01| 20|      2|            10.0|
       """
      }.as[MonthlyLossesByGeos]

      val expectedOutput = setNullableStateForAllColumns(dataframe[PortedCarrierMonthlyLossesGeoShare] {
        """
        |brand|plan_type|customer_base_date|quarter|dma|monthly_loss_share|
        |    a|        1|        2022-06-01|      2| 10|0.2222222222222222|
        |    a|        1|        2022-06-01|      2| 20|0.1111111111111111|
        |    b|        1|        2022-06-01|      2| 10|0.2222222222222222|
        |    b|        1|        2022-06-01|      2| 20|0.1111111111111111|
        """
      }, nullable = true).as[PortedCarrierMonthlyLossesGeoShare]

      val actualOutput = setNullableStateForAllColumns(BaseAdjustmentsDMA().calculatePortedCarrierMonthlyLossesGeoShare(
        processingDate,
        portedCarrierMonthlyLossesByGeosData,
        portedCarrierLossesAggByQuarterData
      ).toDF(), nullable = true).as[PortedCarrierMonthlyLossesGeoShare]

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

  describe("Base Adjustments - End to End Test") {
    import spark.implicits._
    it("test base adjustments") {
      val processingDate = LocalDate.of(2022, 6, 1)

      //AVG quarterly GA rate(Q3 23 GL / Q3 22 subs) * monthly losses(August 23 ported losses) / total quarterly losses
      val industryModelData = dataset[IndustryModel] {
        """
          |brand|plan_type|year|quarter|gross_additions|gross_losses|subscribers|ba  |
          |a    |1        |2022|1      |30             |30          |100        |-150 |
          |b    |1        |2022|1      |60             |60          |100        |-30 |
          |a    |1        |2022|2      |60             |60          |100        |60  |
          |b    |1        |2022|2      |90             |90          |100        |0   |
      """
      }.as[IndustryModel]

      val portedCarrierLosses = dataset[PortedCarrierLosses] {
        """
          |date_trunc |loser          |primary_plan_type_id|secondary_plan_type_id|dma|dma_name|switches|
          |2022-04-01 |a              |1                   |1                     |10 |aaa     |30.0    |
          |2022-05-01 |a              |1                   |1                     |20 |aaa     |30.0    |
          |2022-06-01 |a              |1                   |1                     |10 |aaa     |20.0    |
          |2022-06-01 |a              |1                   |1                     |20 |aaa     |10.0    |
          |2022-04-01 |b              |1                   |1                     |10 |aaa     |30.0    |
          |2022-05-01 |b              |1                   |1                     |20 |aaa     |30.0    |
          |2022-06-01 |b              |1                   |1                     |10 |aaa     |20.0    |
          |2022-06-01 |b              |1                   |1                     |20 |aaa     |10.0    |
    """
      }.as[PortedCarrierLosses]

      val expectedOutput = setNullableStateForAllColumns(dataframe[EstimatedMonthlyBA] {
        """
          |brand|plan_type|customer_base_date|dma|      estimated_ba|
          |    a|        1|        2022-06-01| 20| 6.666666666666666|
          |    a|        1|        2022-06-01| 10|13.333333333333332|
          |    b|        1|        2022-06-01| 20|               0.0|
          |    b|        1|        2022-06-01| 10|               0.0|
        """
      }, true).as[EstimatedMonthlyBA]

      val actualOutput = setNullableStateForAllColumns(BaseAdjustmentsDMA().calculateBaseAdjustments(
        processingDate, industryModelData, portedCarrierLosses
      ).toDF(), true).as[EstimatedMonthlyBA]

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

//  describe("Actual Data") {
//    import spark.implicits._
//    it("test full run with subset of actual data")  {
//      val industryModelData = IndustryModel.read(URI create "wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/reported_input_kevin_vishal_final.csv")
//      val portedCarrierLossesData = PortedCarrierLosses.read(URI create "wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ported_losses_wins_2022_07_to_2022_09.csv")
//
//      industryModelData.show(10, false)
//      portedCarrierLossesData.show(10, false)
//
//      val processingDate = LocalDate.of(2022,9,1)
//
//      val baData = BaseAdjustmentsDMA().calculateBaseAdjustments(
//        processingDate, industryModelData, portedCarrierLossesData
//      )
//      baData.write.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ba_output_20220901_21stSept")
//    }
//  }

//  // DO NOT USE - This testcase was used to generate the % difference in between actual and expected output only
//    describe("Actual Data - DMA") {
//      import spark.implicits._
//      it("test full run with subset of actual data")  {
//        val baOutputData = spark.read.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ba_output_20220701/ba_dma_output_20220701.csv")
//        val expectedData = spark.read.option("header", value = true).csv("/home/<USER>/Downloads/1Q19-2Q23-DMA-WO_ETH-PACKAGED-NEGSOLVED-2023_08_07.csv")
//          .filter($"customer_base_date" === "2022-07-01")
//          .select(
//            $"bpt" as "brand",
//            $"customer_base_date",
//            $"dma",
//            $"ba"
//          )
//
//        val actualData = expectedData.join(
//          baOutputData, Seq("brand", "customer_base_date", "dma"),
//          "LEFT"
//        ).withColumn("% difference", ($"ba" - $"estimated_ba") / $"estimated_ba")
//          .select(
//            "customer_base_date", "brand", "plan_type", "dma", "ba", "estimated_ba", "% difference"
//          ).orderBy("brand","dma")
//
//        actualData.write.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ba_output_20220701_%_diff")
//      }
//    }

}
