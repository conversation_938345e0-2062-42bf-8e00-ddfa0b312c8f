package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.job._
import java.time.LocalDate


class RedshiftUploadWMSJobSpec extends CldSparkBaseSpec {

  describe("Release Month Year Test") {

    it("If current month is release month") {

      val processingDate = LocalDate.of(2023, 2, 1)
      val expectedData = "2023_02"
      val actualData = RedshiftUploadWMSJobRunner.getReleaseMonthYear(processingDate)

      expectedData.shouldBe(actualData)
      expectedData.contentEquals(actualData)
    }

    it("If previous month is release month") {

      val processingDate = LocalDate.of(2023, 1, 1)
      val expectedData = "2023_01"
      val actualData = RedshiftUploadWMSJobRunner.getReleaseMonthYear(processingDate)

      expectedData.shouldBe(actualData)
      expectedData.contentEquals(actualData)
    }
  }

  describe("Final Month Year Test") {

    it("If current month is release month") {

      val processingDate = LocalDate.of(2023, 2, 1)
      val expectedData = "2023_01"
      val actualData = RedshiftUploadWMSJobRunner.getFinalMonthYear(processingDate)

      expectedData.shouldBe(actualData)
      expectedData.contentEquals(actualData)
    }

    it("If previous month is release month") {

      val processingDate = LocalDate.of(2023, 1, 1)
      val expectedData = "2022_12"
      val actualData = RedshiftUploadWMSJobRunner.getFinalMonthYear(processingDate)

      expectedData.shouldBe(actualData)
      expectedData.contentEquals(actualData)
    }
  }
}
