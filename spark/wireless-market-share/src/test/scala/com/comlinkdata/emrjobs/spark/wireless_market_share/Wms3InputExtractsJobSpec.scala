package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.inputs.{CaMonthlyPortedWinsAndLossesWMS, MonthlyPortedWinsAndLossesWCV, NationalOracleWCVFlows}
import com.comlinkdata.emrjobs.spark.wireless_market_share.model.{CaMonthlyPortedWinsAndLossesOutput, PortedCarrierWinsLossesExtractDMA, PortedCarrierWinsLossesExtractVPGM, rateCenterWinnerLoserTemp}
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.{CustomRegionsPreProdLookup, FZipLookup}
import com.comlinkdata.largescale.schema.wireless_market_share.{NationalOracleDemographicsFFC, NationalWCVFlows, WirelessMovementWideGeneric, WirelessMovementWideMonthlyAgg}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.types.{<PERSON><PERSON><PERSON><PERSON><PERSON>, StructType}
import org.apache.spark.sql.functions._
import com.comlinkdata.largescale.schema.wireless_market_share.redshift.{CaWirelessMovementWide, DDa, DSpIdCatFlanker, ShawMigrations, Wms10ModelWithPlanType2023Dec, Wms10SinglePeriodSubsBaseline2020Dec}

import java.net.URI
import java.sql.Date



class Wms3InputExtractsJobSpec extends CldSparkBaseSpec {


  def setNullableStateForAllColumns(df: DataFrame, nullable: Boolean): DataFrame = {
    // get schema
    val schema = df.schema
    // modify [[StructField] with name `cn`
    val newSchema = StructType(schema.map {
      case StructField(c, t, _, m) ⇒ StructField(c, t, nullable = nullable, m)
    })
    // apply new schema
    df.sqlContext.createDataFrame(df.rdd, newSchema)
  }

  describe("WMS 3.0 Wins & Losses Monthly Extracts WCV")  {

    import spark.implicits._

    it("test assignBrandNamesToSP")  {
      val wirelessMovementWideData = setNullableStateForAllColumns(dataframe[WirelessMovementWideMonthlyAgg] {
        """
          |zip_cd|primary_sp|secondary_sp|primary_plan_type_id|secondary_plan_type_id|      adjusted_wins|month     |
          | 41101|       609|           2|                   1|                     1|0.43757701925336867|2017-02-01|
          | 36322|       609|           2|                   1|                     2|                0.0|2021-02-01|
          | 02169|      6577|           2|                   2|                     2|                1.0|2021-02-01|
          | 98103|      6577|           2|                   2|                     2| 1.1141435283720935|2021-02-01|
          | 45505|       609|           2|                   1|                     2| 12.110000000000001|2021-02-01|
          | 60010|      6546|           7|                   1|                     1|                1.0|2017-02-01|
          | 45238|         3|           8|                   1|                     1|                0.0|2021-02-01|
          | 63334|         2|        6546|                   1|                     1|                1.0|2021-02-01|
          | 76009|         4|           1|                   2|                     2|                1.0|2021-02-01|
          | 20158|      6545|           3|                   1|                     2|                0.0|2021-02-01|
          | 20158|      6042|           3|                   1|                     2|                0.0|2021-02-01|
          | 20158|      6042|           3|                   1|                     2|                0.0|2021-02-01|
          | 20158|         1|           3|                   1|                     2|                0.0|2021-02-01|
          | 20158|         1|           3|                   2|                     2|                0.0|2021-02-01|
        """
      }, nullable = true).as[WirelessMovementWideMonthlyAgg]

      val expected = setNullableStateForAllColumns(dataframe[rateCenterWinnerLoserTemp] {
        """
          |zip_cd|primary_sp|secondary_sp|primary_plan_type_id|secondary_plan_type_id|adjusted_wins      |date      |winner                        |loser                         |
          |41101 |609       |2           |1                   |1                     |0.43757701925336867|2017-02-01|Boost Mobile_Prepaid Phone    |AT&T_Prepaid Phone            |
          |36322 |609       |2           |1                   |2                     |0.0                |2021-02-01|Boost Mobile_Prepaid Phone    |AT&T_Postpaid Phone           |
          |02169 |6577      |2           |2                   |2                     |1.0                |2021-02-01|Other_Postpaid Phone          |AT&T_Postpaid Phone           |
          |98103 |6577      |2           |2                   |2                     |1.1141435283720935 |2021-02-01|Other_Postpaid Phone          |AT&T_Postpaid Phone           |
          |45505 |609       |2           |1                   |2                     |12.110000000000001 |2021-02-01|Boost Mobile_Prepaid Phone    |AT&T_Postpaid Phone           |
          |60010 |6546      |7           |1                   |1                     |1.0                |2017-02-01|Tracfone_Prepaid Phone        |U.S. Cellular_Prepaid Phone   |
          |45238 |3         |8           |1                   |1                     |0.0                |2021-02-01|Boost Mobile_Prepaid Phone    |Cricket Wireless_Prepaid Phone|
          |63334 |2         |6546        |1                   |1                     |1.0                |2021-02-01|AT&T_Prepaid Phone            |Tracfone_Prepaid Phone        |
          |76009 |4         |1           |2                   |2                     |1.0                |2021-02-01|T-Mobile_Postpaid Phone       |Verizon Wireless_Postpaid Phone|
          |20158 |6545      |3           |1                   |2                     |0.0                |2021-02-01|Tracfone_Prepaid Phone        |T-Mobile_Postpaid Phone       |
          |20158 |6042      |3           |1                   |2                     |0.0                |2021-02-01|Boost Mobile_Prepaid Phone    |T-Mobile_Postpaid Phone       |
          |20158 |6042      |3           |1                   |2                     |0.0                |2021-02-01|Boost Mobile_Prepaid Phone    |T-Mobile_Postpaid Phone       |
          |20158 |1         |3           |1                   |2                     |0.0                |2021-02-01|Verizon Wireless_Prepaid Phone|T-Mobile_Postpaid Phone       |
          |20158 |1         |3           |2                   |2                     |0.0                |2021-02-01|Verizon Wireless_Postpaid Phone|T-Mobile_Postpaid Phone       |
        """
      }, nullable = true).as[rateCenterWinnerLoserTemp]

      val result =
        setNullableStateForAllColumns(
          wirelessMovementWideData.toDF()
            .transform(MonthlyPortedWinsAndLossesWCV().assignBrandNamesToSP($"primary_sp", $"primary_plan_type_id", "winner"))
            .transform(MonthlyPortedWinsAndLossesWCV().assignBrandNamesToSP($"secondary_sp", $"secondary_plan_type_id", "loser"))
            .withColumnRenamed("month", "date")
        .toDF(), nullable = true).as[rateCenterWinnerLoserTemp]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateMonthlyPortedWinsAndLossesWcvExtractDma") {

    import spark.implicits._

    it("test") {
      val wirelessMovementWideData = setNullableStateForAllColumns(dataframe[WirelessMovementWideGeneric] {
        """
          |zip_cd|region_dim_id|state|bta|cbsa |dma|cma|secondary_sp|primary_sp|merger_id|tenure|primary_plan_type_id|secondary_plan_type_id|prior_losing_plan_type_id|prior_losing_sp|ustn_ind|adjusted_wins    |customer_base_date|
          |1002  |0            |19   |427|44140|543|63 |6545        |4         |0        |1     |2                   |1                     |0                        |0              |true    |0.001194361166443|2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6545        |6526      |0        |1     |2                   |1                     |0                        |0              |true    |0.003814661428704|2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6545        |6578      |0        |1     |2                   |1                     |0                        |0              |true    |0.00477731559344 |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6578        |4         |0        |1     |1                   |2                     |0                        |0              |true    |0.002177050617613|2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6578        |4         |0        |1     |2                   |2                     |0                        |0              |true    |0.002158635697611|2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6578        |6526      |0        |1     |2                   |2                     |0                        |0              |true    |0.002743772248122|2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6578        |6545      |0        |1     |1                   |2                     |0                        |0              |true    |0.004717697106694|2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |2         |0        |1     |1                   |1                     |0                        |0              |true    |4.7720084018E-4  |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |2         |0        |1     |2                   |1                     |0                        |0              |true    |0.00145058809745 |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |2         |0        |1     |2                   |1                     |0                        |0              |true    |1.8763591908E-4  |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |5         |0        |1     |1                   |1                     |0                        |0              |true    |4.51902927668E-4 |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |5         |0        |1     |1                   |1                     |0                        |0              |true    |5.84543753785145 |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |8         |0        |1     |1                   |1                     |0                        |0              |true    |9.01931349539E-4 |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |8         |0        |1     |1                   |1                     |0                        |0              |true    |1.16666280397E-4 |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |1113      |0        |1     |2                   |1                     |0                        |0              |true    |0.002753713566517|2023-10-01        |
        """
      }, nullable = true).as[WirelessMovementWideGeneric]

      val fZipData = setNullableStateForAllColumns(dataframe[FZipLookup] {
        """
          |zip_varchar|dma|dma_name      |
          |1002      |543|Springfield-Holyoke, MA|
          |20783      |511|Washington, DC|
          |20877      |511|Washington, DC|
          |20878      |511|Washington, DC|
        """
      }, nullable = true).as[FZipLookup]

      val expected = setNullableStateForAllColumns(dataframe[PortedCarrierWinsLossesExtractDMA] {
        """
          |date_trunc|winner                        |loser                 |primary_plan_type_id|secondary_plan_type_id|dma|dma_name               |switches            |
          |2023-10-01|Cricket Wireless_Prepaid Phone|Other_Prepaid Phone   |1                   |1                     |543|Springfield-Holyoke, MA|0.001018597629936   |
          |2023-10-01|T-Mobile_Prepaid Phone        |Other_Postpaid Phone  |1                   |2                     |543|Springfield-Holyoke, MA|0.002177050617613   |
          |2023-10-01|Other_Postpaid Phone          |Other_Postpaid Phone  |2                   |2                     |543|Springfield-Holyoke, MA|0.002743772248122   |
          |2023-10-01|T-Mobile_Postpaid Phone       |Tracfone_Prepaid Phone|2                   |1                     |543|Springfield-Holyoke, MA|0.001194361166443   |
          |2023-10-01|Other_Postpaid Phone          |Other_Prepaid Phone   |2                   |1                     |543|Springfield-Holyoke, MA|0.002753713566517   |
          |2023-10-01|Other_Postpaid Phone          |Tracfone_Prepaid Phone|2                   |1                     |543|Springfield-Holyoke, MA|0.008591977022143999|
          |2023-10-01|T-Mobile_Postpaid Phone       |Other_Postpaid Phone  |2                   |2                     |543|Springfield-Holyoke, MA|0.002158635697611   |
          |2023-10-01|Tracfone_Prepaid Phone        |Other_Postpaid Phone  |1                   |2                     |543|Springfield-Holyoke, MA|0.004717697106694   |
          |2023-10-01|AT&T_Prepaid Phone            |Other_Prepaid Phone   |1                   |1                     |543|Springfield-Holyoke, MA|4.7720084018E-4     |
          |2023-10-01|AT&T_Postpaid Phone           |Other_Prepaid Phone   |2                   |1                     |543|Springfield-Holyoke, MA|0.00163822401653    |
          |2023-10-01|Tracfone_Prepaid Phone        |Other_Prepaid Phone   |1                   |1                     |543|Springfield-Holyoke, MA|5.845889440779118   |
        """
      }, nullable = true).as[PortedCarrierWinsLossesExtractDMA]

      val monthToProcess = Date.valueOf("2024-01-01")

      val result = setNullableStateForAllColumns(MonthlyPortedWinsAndLossesWCV().calculateMonthlyPortedWinsAndLossesWcvExtractDma(
        monthToProcess, wirelessMovementWideData, fZipData
      ).toDF(), nullable = true).as[PortedCarrierWinsLossesExtractDMA]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateMonthlyPortedWinsAndLossesWcvExtractVPGM") {

    import spark.implicits._

    it("test") {
      val wirelessMovementWideData = setNullableStateForAllColumns(dataframe[WirelessMovementWideGeneric] {
        """
          |zip_cd|region_dim_id|state|bta|cbsa |dma|cma|secondary_sp|primary_sp|merger_id|tenure|primary_plan_type_id|secondary_plan_type_id|prior_losing_plan_type_id|prior_losing_sp|ustn_ind|adjusted_wins    |customer_base_date|
          |1002  |0            |19   |427|44140|543|63 |6545        |4         |0        |1     |2                   |1                     |0                        |0              |true    |0.001194361166443|2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6545        |6526      |0        |1     |2                   |1                     |0                        |0              |true    |0.003814661428704|2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6545        |6578      |0        |1     |2                   |1                     |0                        |0              |true    |0.00477731559344 |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6578        |4         |0        |1     |1                   |2                     |0                        |0              |true    |0.002177050617613|2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6578        |4         |0        |1     |2                   |2                     |0                        |0              |true    |0.002158635697611|2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6578        |6526      |0        |1     |2                   |2                     |0                        |0              |true    |0.002743772248122|2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6578        |6545      |0        |1     |1                   |2                     |0                        |0              |true    |0.004717697106694|2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |2         |0        |1     |1                   |1                     |0                        |0              |true    |4.7720084018E-4  |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |2         |0        |1     |2                   |1                     |0                        |0              |true    |0.00145058809745 |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |2         |0        |1     |2                   |1                     |0                        |0              |true    |1.8763591908E-4  |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |5         |0        |1     |1                   |1                     |0                        |0              |true    |4.51902927668E-4 |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |5         |0        |1     |1                   |1                     |0                        |0              |true    |5.84543753785145 |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |8         |0        |1     |1                   |1                     |0                        |0              |true    |9.01931349539E-4 |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |8         |0        |1     |1                   |1                     |0                        |0              |true    |1.16666280397E-4 |2023-10-01        |
          |1002  |0            |19   |427|44140|543|63 |6727        |1113      |0        |1     |2                   |1                     |0                        |0              |true    |0.002753713566517|2023-10-01        |
        """
      }, nullable = true).as[WirelessMovementWideGeneric]

      val CustomRegionsPreProdData = setNullableStateForAllColumns(dataframe[CustomRegionsPreProdLookup] {
        """
          |custom_region_name         |geo_code_value|
          |Greater Lakes (IL/MI/WI)   |1002          |
          |KAMO                       |74820         |
          |Southeast States (GA/SC/TN)|38310         |
          |Ohio/Pennsylvania          |17501         |
          |Northern Plains (MN/IA/NE) |58830         |
        """
      }, nullable = true).as[CustomRegionsPreProdLookup]

      val expected = setNullableStateForAllColumns(dataframe[PortedCarrierWinsLossesExtractVPGM] {
        """
          |date_trunc|winner                        |loser                 |primary_plan_type_id|secondary_plan_type_id|custom_region_name      |switches            |
          |2023-10-01|T-Mobile_Postpaid Phone       |Tracfone_Prepaid Phone|2                   |1                     |Greater Lakes (IL/MI/WI)|0.001194361166443   |
          |2023-10-01|Other_Postpaid Phone          |Tracfone_Prepaid Phone|2                   |1                     |Greater Lakes (IL/MI/WI)|0.008591977022143999|
          |2023-10-01|T-Mobile_Prepaid Phone        |Other_Postpaid Phone  |1                   |2                     |Greater Lakes (IL/MI/WI)|0.002177050617613   |
          |2023-10-01|T-Mobile_Postpaid Phone       |Other_Postpaid Phone  |2                   |2                     |Greater Lakes (IL/MI/WI)|0.002158635697611   |
          |2023-10-01|Other_Postpaid Phone          |Other_Postpaid Phone  |2                   |2                     |Greater Lakes (IL/MI/WI)|0.002743772248122   |
          |2023-10-01|Tracfone_Prepaid Phone        |Other_Postpaid Phone  |1                   |2                     |Greater Lakes (IL/MI/WI)|0.004717697106694   |
          |2023-10-01|AT&T_Prepaid Phone            |Other_Prepaid Phone   |1                   |1                     |Greater Lakes (IL/MI/WI)|4.7720084018E-4     |
          |2023-10-01|AT&T_Postpaid Phone           |Other_Prepaid Phone   |2                   |1                     |Greater Lakes (IL/MI/WI)|0.00163822401653    |
          |2023-10-01|Tracfone_Prepaid Phone        |Other_Prepaid Phone   |1                   |1                     |Greater Lakes (IL/MI/WI)|5.845889440779118   |
          |2023-10-01|Cricket Wireless_Prepaid Phone|Other_Prepaid Phone   |1                   |1                     |Greater Lakes (IL/MI/WI)|0.001018597629936   |
          |2023-10-01|Other_Postpaid Phone          |Other_Prepaid Phone   |2                   |1                     |Greater Lakes (IL/MI/WI)|0.002753713566517   |
        """
      }, nullable = true).as[PortedCarrierWinsLossesExtractVPGM]

      val monthToProcess = Date.valueOf("2024-01-01")

      val result = setNullableStateForAllColumns(MonthlyPortedWinsAndLossesWCV().calculateMonthlyPortedWinsAndLossesWcvExtractVPGM(
        monthToProcess, wirelessMovementWideData, CustomRegionsPreProdData
      ).toDF(), nullable = true).as[PortedCarrierWinsLossesExtractVPGM]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("Test National WCV Flows Data Conversion") {

    import spark.implicits._

    it("calculateNationalWCVFlowsData") {

      val nationalOracleDemoData = setNullableStateForAllColumns(dataframe[NationalOracleDemographicsFFC] {
        """
          |year|month|mvno_winner|mvno_loser|winner_plan_type|loser_plan_type|age|age_range|race|ethnic_code|ethnicity|estimated_household_income|wins|losses|thecount|
          |2024|3    |2          |4         |2               |2              |49 |G        |B   |A          |E1       |I                         |1.00|1.00  |1       |
          |2024|3    |2          |4         |2               |2              |49 |G        |B   |A          |E1       |I                         |1.00|1.00  |1       |
          |2024|3    |6578       |1113      |2               |2              |75 |L        |    |G          |05       |H                         |1.00|1.00  |1       |
          |2024|3    |4          |8         |2               |1              |28 |C        |    |D          |15       |J                         |1.00|1.00  |1       |
          |2024|3    |6052       |4         |2               |2              |79 |M        |H   |B          |20       |G                         |1.00|1.00  |1       |
          |2024|3    |1          |2         |2               |2              |40 |E        |    |G          |19       |I                         |1.00|1.00  |1       |
          |2024|3    |6          |2         |1               |2              |36 |E        |    |B          |20       |G                         |1.00|1.00  |1       |
          |2024|3    |8          |1         |1               |2              |56 |I        |H   |B          |20       |E                         |1.00|1.00  |1       |
          |2024|3    |2          |4         |2               |2              |63 |J        |    |A          |E5       |C                         |1.00|1.00  |1       |
          |2024|3    |4          |1         |2               |2              |20 |A        |    |D          |09       |L                         |1.00|1.00  |1       |
          |2024|3    |4          |2         |2               |2              |47 |G        |H   |G          |19       |A                         |1.00|1.00  |1       |
          |2024|3    |4          |2         |2               |2              |47 |G        |H   |G          |19       |A                         |1.00|1.00  |1       |
        """
      }, nullable = true).as[NationalOracleDemographicsFFC]

      val expected = setNullableStateForAllColumns(dataframe[NationalWCVFlows] {
        """
          |month_date|loser_brand_plantype           |winner_brand_plantype          |ethnicity               |age  |income     |wins|
          |2024-03-01|T-Mobile_Postpaid Phone        |AT&T_Postpaid Phone            |Black / African American|35-54|$100K-$150K|2.0 |
          |2024-03-01|Cricket Wireless_Prepaid Phone |T-Mobile_Postpaid Phone        |White                   |18-34|$100K-$150K|1.0 |
          |2024-03-01|T-Mobile_Postpaid Phone        |Xfinity Mobile_Postpaid Phone  |Hispanic / Latino       |75+  |$50K-$100K |1.0 |
          |2024-03-01|AT&T_Postpaid Phone            |MetroPCS_Prepaid Phone         |Hispanic / Latino       |35-54|$50K-$100K |1.0 |
          |2024-03-01|Verizon Wireless_Postpaid Phone|Cricket Wireless_Prepaid Phone |Hispanic / Latino       |55-74|<$50K      |1.0 |
          |2024-03-01|T-Mobile_Postpaid Phone        |AT&T_Postpaid Phone            |Black / African American|55-74|<$50K      |1.0 |
          |2024-03-01|Verizon Wireless_Postpaid Phone|T-Mobile_Postpaid Phone        |White                   |18-34|$150K+     |1.0 |
        """
      }, nullable = true).as[NationalWCVFlows]

      val monthToProcess = Date.valueOf("2024-04-01")

      val result = setNullableStateForAllColumns(NationalOracleWCVFlows().calculateNationalWCVFlowsData(
        monthToProcess, nationalOracleDemoData
      ).toDF(), nullable = true).as[NationalWCVFlows]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

  describe("calculateMonthlyPortedWinsAndLossesWMSExtractCa") {

    import spark.implicits._

    it("ported wins ans losses") {
      val caWirelessMovementWideData = setNullableStateForAllColumns(dataframe[CaWirelessMovementWide] {
        """
            |the_date  |pruid|cduid|primary_sp|secondary_sp|adjusted_wins       |
            |2013-08-12|59   |5907 |4532      |99999       |0.93                |
            |2018-02-20|35   |3520 |6102      |5129        |0.025               |
            |2021-09-15|24   |2423 |4532      |6102        |0.1536              |
            |2013-05-09|12   |1217 |6015      |3708        |0.0                 |
            |2021-03-24|48   |4806 |3896      |5129        |0.3094              |
            |2018-06-21|24   |2437 |6189      |5908        |0.0754              |
            |2023-04-16|48   |4806 |6101      |5129        |0.7644              |
            |2015-11-13|35   |3520 |5908      |5129        |0.0032              |
            |2016-04-22|35   |3520 |5129      |6102        |0.010000000000000002|
            |2024-03-04|35   |3506 |526       |4532        |-0.0047             |
        """
      }, nullable = true).as[CaWirelessMovementWide]



      val dDaData = setNullableStateForAllColumns(dataframe[DDa] {
       """
           |pruid|prname                                             |
           |46   |Manitoba                                           |
           |24   |Quebec / Québec                                    |
           |35   |Ontario                                            |
           |61   |Northwest Territories / Territoires du Nord-Ouest  |
           |10   |Newfoundland and Labrador / Terre-Neuve-et-Labrador|
           |13   |New Brunswick / Nouveau-Brunswick                  |
           |11   |Prince Edward Island / Île-du-Prince-Édouard       |
           |48   |Alberta                                            |
           |62   |Nunavut                                            |
           |60   |Yukon                                              |
       """
      }, nullable = true).as[DDa]

      val dSpIdCatFlankerData = setNullableStateForAllColumns(dataframe[DSpIdCatFlanker] {
        """
            |sp_dim_id|sp_reporting_name_group|
            |0        |None                   |
            |6094     |Xplore Mobile          |
            |1474     |Eastlink               |
            |5129     |Freedom Mobile         |
            |6102     |Virgin Canada          |
            |6101     |Koodo                  |
            |2973     |Bell MTS               |
            |66666    |Other Wireless         |
            |4532     |Telus Wireless         |
            |3963     |SaskTel Wireless       |
        """
      },nullable = true).as[DSpIdCatFlanker]

      val shawMigrationsData = setNullableStateForAllColumns(dataframe[ShawMigrations] {
        """
            |the_date  |pruid|primary_sp|secondary_sp|migrations|
            |2023-07-12|48   |3896      |5129        |699.2     |
            |2023-07-30|48   |3896      |5129        |440.2     |
            |2023-08-02|48   |3896      |5129        |595.4     |
            |2023-08-03|48   |3896      |5129        |711.0     |
            |2023-08-06|48   |3896      |5129        |371.8     |
            |2023-08-12|48   |3896      |5129        |659.6     |
            |2023-08-27|48   |3896      |5129        |458.2     |
            |2023-09-05|59   |3896      |5129        |385.6     |
            |2023-09-06|59   |3896      |5129        |459.8     |
            |2023-09-17|48   |3896      |5129        |618.0     |
        """
      },nullable = true).as[ShawMigrations]

      val wms10ModelWithPlanType2023DecData = setNullableStateForAllColumns(dataframe[Wms10ModelWithPlanType2023Dec] {
        """
            |pruid|brand         |plan_type_id|subs  |
            |59   |virgin mobile |1           |21260 |
            |59   |chatr         |1           |116566|
            |46   |freedom mobile|1           |188   |
            |13   |telus         |1           |5791  |
            |10   |virgin mobile |1           |5260  |
            |12   |virgin mobile |1           |9158  |
            |62   |telus         |1           |0     |
            |62   |public mobile |1           |0     |
            |35   |fido          |1           |99087 |
            |11   |virgin mobile |1           |1699  |
        """
      },nullable = true).as[Wms10ModelWithPlanType2023Dec]

      val wms10SinglePeriodSubsBaseline2020DecData = setNullableStateForAllColumns(dataframe[Wms10SinglePeriodSubsBaseline2020Dec] {
        """
            |pruid|brand         |network       |plan_type_id|subs |
            |48   |freedom mobile|freedom mobile|1           |46439|
            |59   |lucky mobile  |bell          |1           |34381|
            |59   |videotron     |videotron     |1           |0    |
            |59   |freedom mobile|freedom mobile|1           |63759|
            |13   |chatr         |rogers        |1           |11978|
            |13   |koodo mobile  |telus         |1           |7412 |
            |13   |videotron     |videotron     |1           |0    |
            |13   |fizz          |videotron     |1           |0    |
            |10   |lucky mobile  |bell          |1           |13216|
            |10   |freedom mobile|freedom mobile|1           |135  |
        """
      },nullable = true).as[Wms10SinglePeriodSubsBaseline2020Dec]

      val expected = setNullableStateForAllColumns(dataframe[CaMonthlyPortedWinsAndLossesOutput] {
        """
          |date_trunc|winner|loser|primary_plan_type_id|secondary_plan_type_id|dma|dma_name       |switches|
          |2021-09-01|null  |null |null                |null                  |24 |Quebec / Québec|null    |
          |2023-04-01|null  |null |null                |null                  |48 |Alberta        |null    |
        """
      },nullable = true).as[CaMonthlyPortedWinsAndLossesOutput]

      val result = setNullableStateForAllColumns(CaMonthlyPortedWinsAndLossesWMS().calculateMonthlyPortedWinsAndLossesWMSExtractCa(
        caWirelessMovementWideData, dDaData, dSpIdCatFlankerData, shawMigrationsData, wms10ModelWithPlanType2023DecData, wms10SinglePeriodSubsBaseline2020DecData
      ).toDF(), nullable = true).as[CaMonthlyPortedWinsAndLossesOutput]

      // Count should be the same
      result.count() shouldEqual expected.count()
      // Dataset equality
      assertDatasetUnsortedEquals(result.toDF(), expected.toDF())
    }
  }

//  Do not run these test cases as they are only use to generate QA dataset and the local testing
//  describe("Actual Data") {
//    import spark.implicits._
//    it("test full run of ported wins and losses - DMA")  {
//
//      val caWirelessMovementWideData = CaWirelessMovementWide.read(URI create "/home/<USER>/Downloads/")
//
//      val dDaData = DDa.read(URI create "/home/<USER>/Downloads/")
//
//      val dSpIdCatFlankerData = DSpIdCatFlanker.read(URI create "/home/<USER>/Downloads/")
//
//      val shawMigrationsData = ShawMigrations.read(URI create "/home/<USER>/Downloads/")
//
//      val wms10ModelWithPlanType2020DecData = Wms10ModelWithPlanType2023Dec.read(URI create "/home/<USER>/Downloads/")
//
//      val wms10SinglePeriodSubsBaseline2020DecData = Wms10SinglePeriodSubsBaseline2020Dec.read(URI create "/home/<USER>/Downloads/")
//
//      caWirelessMovementWideData.show(5, truncate = false)
//      dDaData.show(5, truncate = false)
//      dSpIdCatFlankerData.show(5, truncate = false)
//      shawMigrationsData.show(5, truncate = false)
//      wms10ModelWithPlanType2020DecData.show(5, truncate = false)
//      wms10SinglePeriodSubsBaseline2020DecData.show(5, truncate = false)
//
//      val output = CaMonthlyPortedWinsAndLossesWMS().calculateMonthlyPortedWinsAndLossesWMSExtractCa(
//        caWirelessMovementWideData, dDaData, dSpIdCatFlankerData, shawMigrationsData,
//        wms10ModelWithPlanType2020DecData, wms10SinglePeriodSubsBaseline2020DecData
//      )
//
//      output.show(10, truncate = false)
//    }
//
//    it("QA comparison") {
//      val ManualDF = spark.read.option("header", "true").csv("/home/<USER>/Downloads/wcv_ca_monthly_ported_wins_losses_thru_2024_07.csv")
//      val AutoDF = spark.read.option("header", "true").csv("/home/<USER>/Downloads/part-00000-a016c799-1a37-4efd-ad78-2ba54feab35a-c000.csv")
//      val renamedManualDF = ManualDF
//        .withColumnRenamed("switches", "manual_switches")
//        .withColumnRenamed("dma_name", "manual_dma_name")
//      val renamedAutoDF = AutoDF
//        .withColumnRenamed("switches", "auto_switches")
//        .withColumnRenamed("dma_name", "auto_dma_name")
//      val joinColumns = Seq("date_trunc", "winner", "loser", "primary_plan_type_id", "secondary_plan_type_id", "dma")
//      val joinedDF = renamedManualDF.join(renamedAutoDF, joinColumns, "LEFT")
//      val resultDF = joinedDF
//        .select(
//          col("date_trunc"),
//          col("winner"),
//          col("loser"),
//          col("primary_plan_type_id"),
//          col("secondary_plan_type_id"),
//          col("dma"),
//          col("manual_dma_name"),
//          col("manual_switches"),
//          col("auto_switches"),
//          (col("manual_switches") - col("auto_switches")).alias("diff_total_switches")
//        )
//      resultDF.show()
//      resultDF.repartition(1).write.option("header", true).csv("/home/<USER>/Downloads/Canada_wins_losses_qa_comparison_20240820_aug24_1")
//    }
//  }
}