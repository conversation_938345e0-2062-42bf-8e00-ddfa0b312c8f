package com.comlinkdata.emrjobs.spark.wireless_market_share

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share._
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.ZipToVPGM
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{concat_ws, date_format}
import org.apache.spark.sql.types.{IntegerType, StructField, StructType}
import java.net.URI
import java.time.LocalDate


class BaseAdjustmentsVPGMSpec extends CldSparkBaseSpec {

  def setNullableStateForAllColumns(df: DataFrame, nullable: Boolean): DataFrame = {
    // get schema
    val schema = df.schema
    // modify [[StructField] with name `cn`
    val newSchema = StructType(schema.map {
      case StructField(c, t, _, m) ⇒ StructField(c, t, nullable = nullable, m)
    })
    // apply new schema
    df.sqlContext.createDataFrame(df.rdd, newSchema)
  }

  describe("Base Adjustments - Monthly Losses") {
    import spark.implicits._
    it("test monthly losses by geos") {

      val portedCarrierLossesByQuarterData = dataset[PortedCarrierLossesByQuarterVPGM] {
        """
        |brand|plan_type|customer_base_date|quarter|vpgm|switches|
        |a    |1        |2022-04-01        |2      |10 |30.0    |
        |a    |1        |2022-05-01        |2      |20 |30.0    |
        |a    |1        |2022-06-01        |2      |10 |20.0    |
        |a    |1        |2022-06-01        |2      |20 |10.0    |
        |b    |1        |2022-04-01        |2      |10 |30.0    |
        |b    |1        |2022-05-01        |2      |20 |30.0    |
        |b    |1        |2022-06-01        |2      |10 |20.0    |
        |b    |1        |2022-06-01        |2      |20 |10.0    |
      """
      }.as[PortedCarrierLossesByQuarterVPGM]

      val expectedOutput = setNullableStateForAllColumns(dataframe[MonthlyLossesByGeosVPGM] {
        """
        |brand|plan_type|customer_base_date|vpgm|quarter|switches_per_geo|
        |    a|        1|        2022-04-01| 10|      2|            30.0|
        |    a|        1|        2022-05-01| 20|      2|            30.0|
        |    a|        1|        2022-06-01| 10|      2|            20.0|
        |    a|        1|        2022-06-01| 20|      2|            10.0|
        |    b|        1|        2022-04-01| 10|      2|            30.0|
        |    b|        1|        2022-05-01| 20|      2|            30.0|
        |    b|        1|        2022-06-01| 10|      2|            20.0|
        |    b|        1|        2022-06-01| 20|      2|            10.0|
        """
      }, nullable = true).as[MonthlyLossesByGeosVPGM]

      val actualOutput = setNullableStateForAllColumns(BaseAdjustmentsVPGM().calculateMonthlyLossesByGeos(
        portedCarrierLossesByQuarterData
      ).toDF(), nullable = true).as[MonthlyLossesByGeosVPGM]

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

  describe("Base Adjustments - Quarterly Losses") {
    import spark.implicits._
    it("test quarterly losses agg by quarter") {

      val portedCarrierLossesByQuarterData = dataset[PortedCarrierLossesByQuarterVPGM] {
        """
          |brand|plan_type|customer_base_date|quarter|vpgm|switches|
          |a    |1        |2022-04-01        |2      |10 |30.0    |
          |a    |1        |2022-05-01        |2      |20 |30.0    |
          |a    |1        |2022-06-01        |2      |10 |20.0    |
          |a    |1        |2022-06-01        |2      |20 |10.0    |
          |b    |1        |2022-04-01        |2      |10 |30.0    |
          |b    |1        |2022-05-01        |2      |20 |30.0    |
          |b    |1        |2022-06-01        |2      |10 |20.0    |
          |b    |1        |2022-06-01        |2      |20 |10.0    |
      """
      }.as[PortedCarrierLossesByQuarterVPGM]

      val expectedOutput = setNullableStateForAllColumns(dataframe[PortedCarrierLossesAggByQuarterVPGM] {
        """
        |brand|plan_type|quarter|switches_per_quarter|
        |    a|        1|      2|                90.0|
        |    b|        1|      2|                90.0|
        """
      }, nullable = true).as[PortedCarrierLossesAggByQuarterVPGM]

      val actualOutput = setNullableStateForAllColumns(BaseAdjustmentsVPGM().calculatePortedCarrierLossesAggByQuarter(
        portedCarrierLossesByQuarterData
      ).toDF(), nullable = true).as[PortedCarrierLossesAggByQuarterVPGM]

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

  describe("Base Adjustments - Monthly Losses Share") {
    import spark.implicits._
    it("test monthly losses geographic share") {

      val processingDate = LocalDate.of(2022, 6, 1)

      val portedCarrierLossesAggByQuarterData = dataset[PortedCarrierLossesAggByQuarterVPGM] {
        """
          |brand|plan_type|quarter|switches_per_quarter|
          |    a|        1|      2|                90.0|
          |    b|        1|      2|                90.0|
        """
      }.as[PortedCarrierLossesAggByQuarterVPGM]

      val portedCarrierMonthlyLossesByGeosData = dataset[MonthlyLossesByGeosVPGM] {
        """
          |brand|plan_type|customer_base_date|vpgm|quarter|switches_per_geo|
          |    a|        1|        2022-04-01| 10|      2|            30.0|
          |    a|        1|        2022-05-01| 20|      2|            30.0|
          |    a|        1|        2022-06-01| 10|      2|            20.0|
          |    a|        1|        2022-06-01| 20|      2|            10.0|
          |    b|        1|        2022-04-01| 10|      2|            30.0|
          |    b|        1|        2022-05-01| 20|      2|            30.0|
          |    b|        1|        2022-06-01| 10|      2|            20.0|
          |    b|        1|        2022-06-01| 20|      2|            10.0|
       """
      }.as[MonthlyLossesByGeosVPGM]

      val expectedOutput = setNullableStateForAllColumns(dataframe[PortedCarrierMonthlyLossesGeoShareVPGM] {
        """
        |brand|plan_type|customer_base_date|quarter|vpgm|monthly_loss_share|
        |    a|        1|        2022-06-01|      2| 10|0.2222222222222222|
        |    a|        1|        2022-06-01|      2| 20|0.1111111111111111|
        |    b|        1|        2022-06-01|      2| 10|0.2222222222222222|
        |    b|        1|        2022-06-01|      2| 20|0.1111111111111111|
        """
      }, nullable = true).as[PortedCarrierMonthlyLossesGeoShareVPGM]

      val actualOutput = setNullableStateForAllColumns(BaseAdjustmentsVPGM().calculatePortedCarrierMonthlyLossesGeoShare(
        processingDate,
        portedCarrierMonthlyLossesByGeosData,
        portedCarrierLossesAggByQuarterData
      ).toDF(), nullable = true).as[PortedCarrierMonthlyLossesGeoShareVPGM]

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

  describe("Base Adjustments - End to End Test") {
    import spark.implicits._
    it("test base adjustments") {
      val processingDate = LocalDate.of(2022, 6, 1)

      //AVG quarterly GA rate(Q3 23 GL / Q3 22 subs) * monthly losses(August 23 ported losses) / total quarterly losses
      val industryModelData = dataset[IndustryModel] {
        """
          |brand|plan_type|year|quarter|gross_additions|gross_losses|subscribers|ba  |
          |a    |1        |2022|1      |30             |30          |100        |-50 |
          |b    |1        |2022|1      |60             |60          |100        |-10 |
          |a    |1        |2022|2      |60             |60          |100        |20  |
          |b    |1        |2022|2      |90             |90          |100        |0   |
      """
      }.as[IndustryModel]

      val portedCarrierLosses = dataset[PortedCarrierLossesVPGM] {
        """
          |date_trunc |loser          |primary_plan_type_id|secondary_plan_type_id|vpgm|switches|
          |2022-04-01 |a              |1                   |1                     |10    |30.0    |
          |2022-05-01 |a              |1                   |1                     |20    |30.0    |
          |2022-06-01 |a              |1                   |1                     |10    |20.0    |
          |2022-06-01 |a              |1                   |1                     |20    |10.0    |
          |2022-04-01 |b              |1                   |1                     |10    |30.0    |
          |2022-05-01 |b              |1                   |1                     |20    |30.0    |
          |2022-06-01 |b              |1                   |1                     |10    |20.0    |
          |2022-06-01 |b              |1                   |1                     |20    |10.0    |
    """
      }.as[PortedCarrierLossesVPGM]


      val expectedOutput = setNullableStateForAllColumns(dataframe[EstimatedMonthlyBAVPGM] {
        """
          |brand|plan_type|customer_base_date|  vpgm|      estimated_ba|
          |    a|        1|        2022-06-01| 20|2.2222222222222223|
          |    a|        1|        2022-06-01| 10| 4.444444444444445|
          |    b|        1|        2022-06-01| 20|               0.0|
          |    b|        1|        2022-06-01| 10|               0.0|
        """
      }, true).as[EstimatedMonthlyBAVPGM]

      val actualOutput = setNullableStateForAllColumns(BaseAdjustmentsVPGM().calculateBaseAdjustments(
        processingDate, industryModelData, portedCarrierLosses
      ).toDF(), true).as[EstimatedMonthlyBAVPGM]

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

//  describe("Actual Data") {
//    import spark.implicits._
//    it("test full run with subset of actual data")  {
//      val industryModelData = IndustryModel.read(URI create "wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/reported_input_kevin_vishal_final.csv")
//      val portedCarrierLossesData = PortedCarrierLossesVPGMInput.read(URI create "wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ported_losses_wins_2022_07_to_2022_09_vpgm.csv")
//      val attFootprintZipVsVPGMData = AttFootprintZipVsVpgmLookup.read(URI create "wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/att_footprint_ZIP_vs_VPGM_fixed_leading_zero.csv")
//
//      val processingDate = LocalDate.of(2022,8,1)
//
//      val baData = BaseAdjustmentsVPGM().calculateBaseAdjustments(
//        processingDate, industryModelData, portedCarrierLossesData, attFootprintZipVsVPGMData
//      )
//      baData.write.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ba_output_20220801_vpgm")
//    }
//  }

//  // DO NOT USE - This testcase was used to generate the % difference in between actual and expected output only
//  // DMA version
//    describe("Actual Data - DMA") {
//      import spark.implicits._
//      it("test full run with subset of actual data")  {
//        val baOutputData = spark.read.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ba_output_20220601/part-00000-c214da69-4e76-439f-a65c-8be38cd649ad-c000.csv")
//        val expectedData = spark.read.option("header", value = true).csv("/home/<USER>/Downloads/1Q19-2Q23-DMA-WO_ETH-PACKAGED-NEGSOLVED-2023_08_07.csv")
//          .filter($"customer_base_date" === "2022-06-01")
//          .select(
//            $"bpt" as "brand",
//            $"customer_base_date",
//            $"dma",
//            $"ba"
//          )
//
//        val actualData = expectedData.join(
//          baOutputData, Seq("brand", "customer_base_date", "dma"),
//          "LEFT"
//        ).withColumn("% difference", ($"ba" - $"estimated_ba") / $"estimated_ba")
//          .select(
//            "customer_base_date", "brand", "plan_type", "dma", "ba", "estimated_ba", "% difference"
//          ).orderBy("brand","dma")
//
//        actualData.write.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ba_output_20220601_%_diff")
//      }
//    }
//
//  // VPGM version
//  describe("Actual Data - VPGM") {
//    import spark.implicits._
//    it("test full run with subset of actual data") {
//      val baOutputData = spark.read.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ba_output_20220801_vpgm/ba_output_20220801_vpgm.csv")
//      val expectedData = spark.read.option("header", value = true).csv("/home/<USER>/Downloads/1Q19-3Q23-VPGM-WO_ETH-PACKAGED-14_08_2023.csv")
//        .withColumn("customer_base_date", date_format($"Date", "yyyy-MM-dd"))
//        .withColumn("bpt", concat_ws("_", $"brand", $"plan_type"))
//        .filter($"customer_base_date" === "2022-08-01")
//        .select(
//          $"bpt" as "brand",
//          $"customer_base_date",
//          $"Geography" as "vpgm",
//          $"ba"
//        )
//
//      val actualData = expectedData.join(
//          baOutputData, Seq("brand", "customer_base_date", "vpgm"),
//          "LEFT"
//        ).withColumn("% difference", ($"ba" - $"estimated_ba") / $"estimated_ba")
//        .select(
//          "customer_base_date", "brand", "plan_type", "vpgm", "ba", "estimated_ba", "% difference"
//        ).orderBy("brand", "vpgm")
//
//      actualData.write.option("header", value = true).csv("wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/ba_output_20220801_%_diff_vpgm")
//    }
//  }

}
