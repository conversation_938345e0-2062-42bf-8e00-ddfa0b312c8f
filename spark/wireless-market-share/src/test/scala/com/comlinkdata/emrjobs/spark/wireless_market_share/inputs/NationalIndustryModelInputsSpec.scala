package com.comlinkdata.emrjobs.spark.wireless_market_share.inputs

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.largescale.schema.wireless_market_share.IndustryModelTotalOutput
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import org.apache.spark.sql.Dataset
import java.sql.Date


class NationalIndustryModelInputsSpec extends CldSparkBaseSpec {

  val startDate: Date = Date.valueOf("2017-07-01")
  val endDate: Date = Date.valueOf("2022-12-01")

  describe("National Subscriber Multipliers IM Tests") {

    import spark.implicits._

    it("National Subscriber Multipliers IM") {

      val industryModelTotalData = dataset[IndustryModelTotalOutput] {
        """
          |year|month|cld_observed_loser|cld_observed_winner|quarter| begin_segment|begin_mno|begin_mvno|   end_segment|end_mno|end_mvno|diagonal|migration| port_est|      est|winning_losing_prop|winning_prop|losing_prop| top_prop|      prop|churn_losing_mno_sp|churn_winning_mno_sp|churn_losing_mvno_sp|churn_winning_mvno_sp|churn_losing_plan_type_id|churn_winning_plan_type_id|
          |2017|    7|               ATT|                BST|      3|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|11887.766|18927.371|         0.30224314|  0.30506408| 0.32162955|0.3213925|0.30224314|                  2|                   3|                   2|                  609|                        2|                         1|
          |2017|    7|               ATT|                BST|      3| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|1501.2604| 6589.807|         0.30224314|  0.30506408| 0.32162955|0.3213925|0.30224314|                  2|                   3|                   2|                  609|                        1|                         1|
          |2017|    7|               ATT|                PCS|      3|postpaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0|30411.941|45670.445|         0.29406747|  0.29787907| 0.32162955|0.3213925|0.29406747|                  2|                   4|                   2|                    6|                        2|                         1|
          |2017|    7|               ATT|                PCS|      3| prepaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0|7916.0737|30581.549|         0.29406747|  0.29787907| 0.32162955|0.3213925|0.29406747|                  2|                   4|                   2|                    6|                        1|                         1|
          |2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0|257.53076|1317.0182|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        2|                         2|
          |2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0| 280.6646|1423.7842|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        1|                         2|
          |2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1304.8761| 6292.739|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        2|                         1|
          |2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1504.7664|7800.3594|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        1|                         1|
          |2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 43334.21|48085.137|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                    3|                        2|                         2|
          |2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 9153.899|41041.812|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                    3|                        1|                         2|
        """
      }

      val expectedData = dataset[NationalSubscriberMultipliersIM] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id|industry_model_subscribers|
          |        2017-08-01|              609|                          1|                 25517.178|
          |        2017-08-01|                6|                          1|                 76251.994|
          |        2017-08-01|             6577|                          2|                 2740.8024|
          |        2017-08-01|             6577|                          1|        14093.098399999999|
          |        2017-08-01|                3|                          2|                 89126.949|
        """
      }


      val actualData = NationalIndustryModelInputs().computeNationalSubscriberMultipliers(
        industryModelTotalData, startDate, endDate
      )


      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()
      // Dataset equality
      assertDatasetEquals(actualData, expectedData)
    }
  }

  describe("National Loss Multipliers IM Tests") {

    import spark.implicits._

    it("National Loss Multipliers IM") {

      val industryModelTotalData = dataset[IndustryModelTotalOutput] {
        """
          |year|month|cld_observed_loser|cld_observed_winner|quarter| begin_segment|begin_mno|begin_mvno|   end_segment|end_mno|end_mvno|diagonal|migration| port_est|      est|winning_losing_prop|winning_prop|losing_prop| top_prop|      prop|churn_losing_mno_sp|churn_winning_mno_sp|churn_losing_mvno_sp|churn_winning_mvno_sp|churn_losing_plan_type_id|churn_winning_plan_type_id|
          |2017|    7|               ATT|                BST|      3|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|11887.766|18927.371|         0.30224314|  0.30506408| 0.32162955|0.3213925|0.30224314|                  2|                   3|                   2|                  609|                        2|                         1|
          |2017|    7|               ATT|                BST|      3| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|1501.2604| 6589.807|         0.30224314|  0.30506408| 0.32162955|0.3213925|0.30224314|                  2|                   3|                   2|                  609|                        1|                         1|
          |2017|    7|               ATT|                PCS|      3|postpaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0|30411.941|45670.445|         0.29406747|  0.29787907| 0.32162955|0.3213925|0.29406747|                  2|                   4|                   2|                    6|                        2|                         1|
          |2017|    7|               ATT|                PCS|      3| prepaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0|7916.0737|30581.549|         0.29406747|  0.29787907| 0.32162955|0.3213925|0.29406747|                  2|                   4|                   2|                    6|                        1|                         1|
          |2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0|257.53076|1317.0182|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        2|                         2|
          |2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0| 280.6646|1423.7842|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        1|                         2|
          |2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1304.8761| 6292.739|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        2|                         1|
          |2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1504.7664|7800.3594|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        1|                         1|
          |2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 43334.21|48085.137|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                    3|                        2|                         2|
          |2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 9153.899|41041.812|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                    3|                        1|                         2|
        """
      }

      val expectedData = dataset[NationalLossMultipliersIM] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id|industry_model_losses|
          |        2017-07-01|                2|                          2|          120292.7102|
          |        2017-07-01|                2|                          1|           87437.3116|
        """
      }

      val actualData = NationalIndustryModelInputs().computeNationalLossMultipliers(
        industryModelTotalData, startDate, endDate
      )

      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()
      // Dataset equality
      assertDatasetEquals(actualData, expectedData)
    }
  }

  describe("National Loss Multipliers Disconnects IM Tests") {

    import spark.implicits._

    it("National Loss Multipliers Disconnects IM") {

      val industryModelTotalData = dataset[IndustryModelTotalOutput] {
        """
          |year|month|cld_observed_loser|cld_observed_winner|quarter| begin_segment|begin_mno|begin_mvno|   end_segment|end_mno|end_mvno|diagonal|migration| port_est|      est|winning_losing_prop|winning_prop|losing_prop| top_prop|      prop|churn_losing_mno_sp|churn_winning_mno_sp|churn_losing_mvno_sp|churn_winning_mvno_sp|churn_losing_plan_type_id|churn_winning_plan_type_id|
          |2017|    7|               ATT|                BST|      3|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|11887.766|18927.371|         0.30224314|  0.30506408| 0.32162955|0.3213925|0.30224314|                  2|                   3|                   2|                  609|                        2|                         1|
          |2017|    7|               ATT|                BST|      3| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|1501.2604| 6589.807|         0.30224314|  0.30506408| 0.32162955|0.3213925|0.30224314|                  2|                   3|                   2|                  609|                        1|                         1|
          |2017|    7|               ATT|                PCS|      3|postpaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0|30411.941|45670.445|         0.29406747|  0.29787907| 0.32162955|0.3213925|0.29406747|                  2|                   4|                   2|                    6|                        2|                         1|
          |2017|    7|               ATT|                PCS|      3| prepaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0|7916.0737|30581.549|         0.29406747|  0.29787907| 0.32162955|0.3213925|0.29406747|                  2|                   4|                   2|                    6|                        1|                         1|
          |2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0|257.53076|1317.0182|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        2|                         2|
          |2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0| 280.6646|1423.7842|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        1|                         2|
          |2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1304.8761| 6292.739|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        2|                         1|
          |2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1504.7664|7800.3594|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        1|                         1|
          |2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 43334.21|48085.137|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                    3|                        2|                         2|
          |2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 9153.899|41041.812|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                    3|                        1|                         2|
        """
      }

      val expectedData = dataset[NationalLossMultipliersDisconnectsIM] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id|total_industry_model_losses|ported_industry_model_losses|non_ported_industry_model_losses|non_ported_switching_industry_model_losses|disconnects|
          |        2017-07-01|                2|                          2|                120292.7102|                 87196.32386|              33096.386340000005|                        33096.386340000005|        0.0|
          |        2017-07-01|                2|                          1|                 87437.3116|                  20356.6641|               67080.64749999999|                         67080.64749999999|        0.0|
        """
      }

      val actualData = NationalIndustryModelInputs().computeNationalLossMultipliersDisconnects(
        industryModelTotalData, startDate, endDate
      )

      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()
      // Dataset equality
      assertDatasetEquals(actualData, expectedData)
    }
  }

  describe("National Add Multipliers IM Tests") {

    import spark.implicits._

    it("National Add Multipliers IM") {

      val industryModelTotalData = dataset[IndustryModelTotalOutput] {
        """
      |year|month|cld_observed_loser|cld_observed_winner|quarter| begin_segment|begin_mno|begin_mvno|   end_segment|end_mno|end_mvno|diagonal|migration| port_est|      est|winning_losing_prop|winning_prop|losing_prop| top_prop|      prop|churn_losing_mno_sp|churn_winning_mno_sp|churn_losing_mvno_sp|churn_winning_mvno_sp|churn_losing_plan_type_id|churn_winning_plan_type_id|
      |2017|    8|               ATT|                BST|      1|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|11511.981| 18417.24|         0.30790854|  0.23039177|  0.3467255|0.3069786|0.30790854|                  2|                   3|                   2|                  609|                        2|                         1|
      |2017|    9|               ATT|                BST|      1| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|3767.8525|15683.629|         0.30790854|  0.23039177|  0.3467255|0.3069786|0.30790854|                  2|                   3|                   2|                  609|                        1|                         1|
      |2017|    9|               ATT|                PCS|      1|postpaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0| 43560.25|64641.297|          0.3383369|  0.28472412|  0.3467255|0.3069786| 0.3383369|                  2|                   4|                   2|                    6|                        2|                         1|
      |2017|    9|               ATT|                PCS|      1| prepaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0| 12468.31|41445.258|          0.3383369|  0.28472412|  0.3467255|0.3069786| 0.3383369|                  2|                   4|                   2|                    6|                        1|                         1|
      |2017|   10|               ATT|                SPR|      1|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0| 348.9449|1825.1088|          0.3762335|  0.36370814|  0.3467255|0.3069786| 0.3762335|                  2|                   3|                   2|                 6577|                        2|                         2|
      |2017|   12|               ATT|                SPR|      1| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0| 442.6422|2250.2527|          0.3762335|  0.36370814|  0.3467255|0.3069786| 0.3762335|                  2|                   3|                   2|                 6577|                        1|                         2|
      |2017|    1|               ATT|                SPR|      1|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1234.7375|5977.2217|          0.3762335|  0.36370814|  0.3467255|0.3069786| 0.3762335|                  2|                   3|                   2|                 6577|                        2|                         1|
      |2017|    5|               ATT|                SPR|      1| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1808.9415| 9659.043|          0.3762335|  0.36370814|  0.3467255|0.3069786| 0.3762335|                  2|                   3|                   2|                 6577|                        1|                         1|
      |2017|    1|               ATT|                SPR|      1|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 51811.45|57454.242|          0.3762335|  0.36370814|  0.3467255|0.3069786| 0.3762335|                  2|                   3|                   2|                    3|                        2|                         2|
      |2017|    1|               ATT|                SPR|      1| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 5315.234|22558.584|          0.3762335|  0.36370814|  0.3467255|0.3069786| 0.3762335|                  2|                   3|                   2|                    3|                        1|                         2|
      """
      }

      val expectedData = dataset[NationalAddMultipliersIM] {
        """
          |customer_base_date|previous_holder_sp|previous_holder_plan_type_id|current_holder_sp|current_holder_plan_type_id|industry_model_adds|
          |        2017-08-01|                 2|                           2|              609|                          1|           18417.24|
          |        2017-09-01|                 2|                           1|              609|                          1|          15683.629|
          |        2017-09-01|                 2|                           2|                6|                          1|          64641.297|
          |        2017-09-01|                 2|                           1|                6|                          1|          41445.258|
          |        2017-10-01|                 2|                           2|             6577|                          2|          1825.1088|
          |        2017-12-01|                 2|                           1|             6577|                          2|          2250.2527|
        """
      }

      val actualData = NationalIndustryModelInputs().computeNationalAddMultipliers(
        industryModelTotalData, startDate, endDate
      )

      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()

      // Dataset equality
      assertDatasetEquals(expectedData, actualData)
    }
  }

  describe("National Add Multipliers Intra MNO IM Tests") {

    import spark.implicits._

    it("National Add Multipliers Intra MNO IM") {

      val industryModelTotalData = dataset[IndustryModelTotalOutput] {
        """
          |year|month|cld_observed_loser|cld_observed_winner|quarter| begin_segment|begin_mno|begin_mvno|   end_segment|end_mno|end_mvno|diagonal|migration| port_est|      est|winning_losing_prop|winning_prop|losing_prop| top_prop|     prop|churn_losing_mno_sp|churn_winning_mno_sp|churn_losing_mvno_sp|churn_winning_mvno_sp|churn_losing_plan_type_id|churn_winning_plan_type_id|
          |2017|    7|               BST|                 na|      3| prepaid phone|      SPR|       BST|postpaid phone|    SPR|     OTH|       0|        0|257.52206|1207.4717|               null|        null|  0.2987629|0.3213925|0.3213925|                  3|                   3|                 609|                 6577|                        1|                         2|
          |2017|    7|               BST|                 na|      3| prepaid phone|      SPR|       BST|postpaid phone|    SPR|     RET|       0|        0|27067.197|  42582.9|               null|        null|  0.2987629|0.3213925|0.3213925|                  3|                   3|                 609|                    3|                        1|                         2|
          |2017|    7|               BST|                 na|      3| prepaid phone|      SPR|       BST| prepaid phone|    SPR|     RET|       0|        0| 477.9316|2329.7742|               null|        null|  0.2987629|0.3213925|0.3213925|                  3|                   3|                 609|                    3|                        1|                         1|
          |2017|    7|               BST|                 na|      3| prepaid phone|      SPR|       BST| prepaid phone|    SPR|     TRA|       0|        0| 580.7579|2880.6409|               null|        null|  0.2987629|0.3213925|0.3213925|                  3|                   3|                 609|                 6546|                        1|                         1|
          |2017|    7|               COC|                LEP|      3|postpaid phone|      ATT|       COC| prepaid phone|    ATT|     LEP|       0|        0|4118.8823| 7115.951|               null|  0.34265152| 0.31639376|0.3213925|0.3213925|                  2|                   2|                1113|                    8|                        2|                         1|
          |2017|    7|               COC|                TRA|      3|postpaid phone|      ATT|       COC| prepaid phone|    ATT|     TRA|       0|        0|902.21906| 4396.328|               null|  0.35338667| 0.31639376|0.3213925|0.3213925|                  2|                   2|                1113|                    5|                        2|                         1|
          |2017|    7|               COC|                 na|      3|postpaid phone|      ATT|       COC|postpaid phone|    ATT|     RET|       0|        0|2694.1936| 5354.399|               null|        null| 0.31639376|0.3213925|0.3213925|                  2|                   2|                1113|                    2|                        2|                         2|
          |2017|    7|               COC|                 na|      3|postpaid phone|      ATT|       COC| prepaid phone|    ATT|     RET|       0|        0|1101.4912|5645.2593|               null|        null| 0.31639376|0.3213925|0.3213925|                  2|                   2|                1113|                    2|                        2|                         1|
          |2017|    7|               LEP|                COC|      3| prepaid phone|      ATT|       LEP|postpaid phone|    ATT|     COC|       0|        0|2750.8928| 5979.186|               null|  0.32845944| 0.31548905|0.3213925|0.3213925|                  2|                   2|                   8|                 1113|                        1|                         2|
          |2017|    7|               LEP|                TRA|      3| prepaid phone|      ATT|       LEP| prepaid phone|    ATT|     TRA|       0|        0|5542.3037|26204.416|               null|  0.35338667| 0.31548905|0.3213925|0.3213925|                  2|                   2|                   8|                    5|                        1|                         1|
        """
      }


      val expectedData = dataset[NationalAddMultipliersIntraMnoIM] {
        """
          |customer_base_date|previous_holder_sp|previous_holder_plan_type_id|current_holder_sp|current_holder_plan_type_id|industry_model_adds|
          |        2017-07-01|               609|                           1|             6577|                          2|          1207.4717|
          |        2017-07-01|               609|                           1|                3|                          2|            42582.9|
          |        2017-07-01|               609|                           1|                3|                          1|          2329.7742|
          |        2017-07-01|               609|                           1|             6546|                          1|          2880.6409|
          |        2017-07-01|              1113|                           2|                8|                          1|           7115.951|
          |        2017-07-01|              1113|                           2|                5|                          1|           4396.328|
          |        2017-07-01|              1113|                           2|                2|                          2|           5354.399|
          |        2017-07-01|              1113|                           2|                2|                          1|          5645.2593|
          |        2017-07-01|                 8|                           1|             1113|                          2|           5979.186|
          |        2017-07-01|                 8|                           1|                5|                          1|          26204.416|
        """
      }

      val actualData = NationalIndustryModelInputs().computeNationalAddMultipliersIntraMno(
        industryModelTotalData, startDate, endDate
      )
      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()

      // Dataset equality
      assertDatasetEquals(expectedData, actualData)
    }
  }

  describe("National Add Multipliers Activations IM Tests") {

    import spark.implicits._

    it("National Add Multipliers Activations IM") {

      val industryModelTotalData = dataset[IndustryModelTotalOutput] {
        """
          year|month|cld_observed_loser|cld_observed_winner|quarter| begin_segment|begin_mno|begin_mvno|   end_segment|end_mno|end_mvno|diagonal|migration| port_est|      est|winning_losing_prop|winning_prop|losing_prop| top_prop|      prop|churn_losing_mno_sp|churn_winning_mno_sp|churn_losing_mvno_sp|churn_winning_mvno_sp|churn_losing_plan_type_id|churn_winning_plan_type_id|
          2017|    7|               ATT|                BST|      3|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|11887.766|18927.371|         0.30224314|  0.30506408| 0.32162955|0.3213925|0.30224314|                  2|                   3|                   2|                  609|                        2|                         1|
          2017|    7|               ATT|                BST|      3| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     BST|       0|        0|1501.2604| 6589.807|         0.30224314|  0.30506408| 0.32162955|0.3213925|0.30224314|                  2|                   3|                   2|                  609|                        1|                         1|
          2017|    7|               ATT|                PCS|      3|postpaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0|30411.941|45670.445|         0.29406747|  0.29787907| 0.32162955|0.3213925|0.29406747|                  2|                   4|                   2|                    6|                        2|                         1|
          2017|    7|               ATT|                PCS|      3| prepaid phone|      ATT|       RET| prepaid phone|    TMO|     PCS|       0|        0|7916.0737|30581.549|         0.29406747|  0.29787907| 0.32162955|0.3213925|0.29406747|                  2|                   4|                   2|                    6|                        1|                         1|
          2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0|257.53076|1317.0182|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        2|                         2|
          2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     OTH|       0|        0| 280.6646|1423.7842|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        1|                         2|
          2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1304.8761| 6292.739|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        2|                         1|
          2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET| prepaid phone|    SPR|     OTH|       0|        0|1504.7664|7800.3594|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                 6577|                        1|                         1|
          2017|    7|               ATT|                SPR|      3|postpaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 43334.21|48085.137|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                    3|                        2|                         2|
          2017|    7|               ATT|                SPR|      3| prepaid phone|      ATT|       RET|postpaid phone|    SPR|     RET|       0|        0| 9153.899|41041.812|         0.35827482|   0.3575724| 0.32162955|0.3213925|0.35827482|                  2|                   3|                   2|                    3|                        1|                         2|
        """
      }

      val expectedData = dataset[NationalAddMultipliersActivationsIM] {
        """
          |customer_base_date|current_holder_sp|current_holder_plan_type_id|total_industry_model_adds|ported_industry_model_adds|non_ported_industry_model_adds|non_ported_switching_industry_model_adds|activations|activations_multiplier|
          |        2017-07-01|              609|                          1|                25517.178|        13389.026399999999|                    12128.1516|                              12128.1516|        0.0|                   0.0|
          |        2017-07-01|                6|                          1|                76251.994|                38328.0147|                    37923.9793|                              37923.9793|        0.0|                   0.0|
          |        2017-07-01|             6577|                          2|                2740.8024|         538.1953599999999|                    2202.60704|                              2202.60704|        0.0|                   0.0|
          |        2017-07-01|             6577|                          1|       14093.098399999999|                 2809.6425|                    11283.4559|                              11283.4559|        0.0|                   0.0|
          |        2017-07-01|                3|                          2|                89126.949|                 52488.109|            36638.840000000004|                      36638.840000000004|        0.0|                   0.0|
        """
      }

      val actualData = NationalIndustryModelInputs().computeNationalAddMultipliersActivations(
        industryModelTotalData, startDate, endDate
      )

      // Count rows should be same
      actualData.count() shouldEqual expectedData.count()

      // Dataset equality
      assertDatasetEquals(expectedData, actualData)
    }
  }

}
