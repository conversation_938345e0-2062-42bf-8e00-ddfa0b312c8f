package com.comlinkdata.emrjobs.spark.wireless_market_share
import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
import com.comlinkdata.largescale.schema.wireless_market_share._
import com.comlinkdata.largescale.schema.wireless_market_share.lookup.ZipToVPGM
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.types.{DateType, DoubleType, IntegerType, StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Dataset, SaveMode}

import java.net.URI
import java.time.LocalDate

class GrossLossesVPGMSpec extends CldSparkBaseSpec {

  def setNullableStateForAllColumns(df: DataFrame, nullable: Boolean): DataFrame = {
    // get schema
    val schema = df.schema
    // modify [[StructField] with name `cn`
    val newSchema = StructType(schema.map {
      case StructField(c, t, _, m) ⇒ Struct<PERSON>ield(c, t, nullable = nullable, m)
    })
    // apply new schema
    df.sqlContext.createDataFrame(df.rdd, newSchema)
  }

  describe("Monthly National Average Gross Loss Rate") {
    import spark.implicits._
    it("schema") {
      val processingDate = LocalDate.of(2022, 6, 1)

      //AVG quarterly GA rate(Q3 23 GL / Q3 22 subs) * monthly losses(August 23 ported losses) / total quarterly losses
      val industryModelData = dataset[IndustryModel] {
        """
          |brand|plan_type|year|quarter|gross_additions|gross_losses|subscribers|ba |
          |a    |1        |2022|1      |30             |30          |100        |10 |
          |b    |1        |2022|1      |60             |60          |100        |2  |
          |a    |1        |2022|2      |60             |60          |100        |-5 |
          |b    |1        |2022|2      |90             |90          |100        |-7 |
      """
      }.as[IndustryModel]

      val portedCarrierLosses = dataset[PortedCarrierLossesVPGM] {
        """
          |date_trunc |loser          |primary_plan_type_id|secondary_plan_type_id|vpgm|switches|
          |2022-04-01 |a              |1                   |1                     |10 |30.0    |
          |2022-05-01 |a              |1                   |1                     |20 |30.0    |
          |2022-06-01 |a              |1                   |1                     |10 |20.0    |
          |2022-06-01 |a              |1                   |1                     |20 |10.0    |
          |2022-04-01 |b              |1                   |1                     |10 |30.0    |
          |2022-05-01 |b              |1                   |1                     |20 |30.0    |
          |2022-06-01 |b              |1                   |1                     |10 |20.0    |
          |2022-06-01 |b              |1                   |1                     |20 |10.0    |
    """
      }.as[PortedCarrierLossesVPGM]

      val expectedOutput = setNullableStateForAllColumns(dataframe[GrossAddsRateAverage] {
        """
          |brand         |plan_type|customer_base_date|gross_rate_average|
          |a             |1        |2022-06-01        |0.2               |
          |b             |1        |2022-06-01        |0.3               |
      """
      }, true).as[GrossAddsRateAverage]

      val actualOutput = setNullableStateForAllColumns(GrossLossesVPGM().computeMonthlyNationalAverageGrossLossRate(processingDate, industryModelData, portedCarrierLosses).toDF(), true).as[GrossAddsRateAverage]
      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }
  describe("Flat Gross Losses") {
    import spark.implicits._
    it("output") {
      val processingDate = LocalDate.of(2022, 6, 1)
      val estimatedSubsInput = dataset[SubscribersOutputVPGM] {
        """
          |brand         |plan_type|customer_base_date|vpgm|subs|
          |AT&T Wireless |1        |2022-07-01        |10 |10                |
          |AT&T Wireless |1        |2022-08-01        |20 |10                |
          |AT&T Wireless |1        |2022-09-01        |30 |10                |
          |Altice        |1        |2022-10-01        |10 |10                |
          |Altice        |1        |2022-11-01        |20 |10                |
          |Altice        |1        |2022-12-01        |30 |10                |
          |Boost         |1        |2022-10-01        |10 |10                |
          |Boost         |1        |2022-11-01        |20 |10                |
        """
      }.as[SubscribersOutputVPGM]
      val grossLossesRateAverage = dataset[GrossLossesRateAverage] {
        """
          |brand         |plan_type|customer_base_date|gross_rate_average|
          |AT&T Wireless |1        |2022-08-01        |0.1               |
          |AT&T Wireless |1        |2022-09-01        |0.2               |
          |AT&T Wireless |1        |2022-07-01        |0.3               |
          |Altice        |1        |2022-10-01        |0.1               |
          |Altice        |1        |2022-11-01        |0.2               |
          |Altice        |1        |2022-12-01        |0.3               |
          |Boost         |1        |2022-10-01        |0.1               |
          |Boost         |1        |2022-11-01        |0.2               |
              """
      }.as[GrossLossesRateAverage]
      val expectedOutput = setNullableStateForAllColumns(dataframe[GrossLossesFlatVPGM] {
        """
          |brand         |plan_type|customer_base_date|vpgm|gross_losses_flat   |
          |AT&T Wireless |1        |2022-10-01        |10 |1.0               |
          |AT&T Wireless |1        |2022-11-01        |20 |2.0               |
          |AT&T Wireless |1        |2022-12-01        |30 |3.0               |
          |Altice        |1        |2022-10-01        |10 |1.0               |
          |Altice        |1        |2022-11-01        |20 |2.0               |
          |Altice        |1        |2022-12-01        |30 |3.0               |
          |Boost         |1        |2022-10-01        |10 |1.0               |
          |Boost         |1        |2022-11-01        |20 |2.0               |
            """
      },true).as[GrossLossesFlatVPGM]

      val actualOutput = setNullableStateForAllColumns(GrossLossesVPGM().computeFlatGrossLosses(processingDate, estimatedSubsInput, grossLossesRateAverage).toDF(),true)

      // Count rows should be same
      //      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      //      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }
  describe("Geographic Share of Carrier Flat Gross Losses") {
    import spark.implicits._
    it("output") {
      val grossLossesFlatInput = dataset[GrossLossesFlatVPGM] {
        """
          |brand         |plan_type|customer_base_date|vpgm|gross_losses_flat   |
          |AT&T Wireless |1        |2022-10-01        |10 |2.0               |
          |AT&T Wireless |1        |2022-10-01        |20 |3.0               |
          |AT&T Wireless |1        |2022-10-01        |30 |5.0               |
          |Altice        |1        |2022-10-01        |10 |2.0               |
          |Altice        |1        |2022-10-01        |20 |3.0               |
          |Altice        |1        |2022-10-01        |30 |5.0               |
          |Boost         |1        |2022-10-01        |10 |1.0               |
          |Boost         |1        |2022-10-01        |20 |4.0               |
      """
      }.as[GrossLossesFlatVPGM]
      val expectedOutput = setNullableStateForAllColumns(dataframe[GrossLossesAverageFlatShareVPGM] {
        """
          |brand         |plan_type|customer_base_date|vpgm|gross_average_flat_share   |
          |AT&T Wireless |1        |2022-10-01        |10 |0.2               |
          |AT&T Wireless |1        |2022-10-01        |20 |0.3               |
          |AT&T Wireless |1        |2022-10-01        |30 |0.5               |
          |Altice        |1        |2022-10-01        |10 |0.2               |
          |Altice        |1        |2022-10-01        |20 |0.3               |
          |Altice        |1        |2022-10-01        |30 |0.5               |
          |Boost         |1        |2022-10-01        |10 |0.2               |
          |Boost         |1        |2022-10-01        |20 |0.8               |
            """
      },true).as[GrossLossesAverageFlatShareVPGM]
      val actualOutput = setNullableStateForAllColumns(GrossLossesVPGM().computeFlatGrossLossAverageFlatShare(grossLossesFlatInput).toDF(),true)

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }
  describe("Ported Geographic Share of Carrier Losses") {
    import spark.implicits._
    it("output") {
      val portedCarrierLosses = dataset[PortedCarrierLossesVPGM] {
        """
          |date_trunc |loser          |primary_plan_type_id|secondary_plan_type_id|vpgm|switches|
          |2022-10-01 |AT&T Prepaid   |1                   |1                     |10 |10.0    |
          |2022-10-01 |AT&T Prepaid   |1                   |1                     |20 |40.0    |
          |2022-10-01 |AT&T Prepaid   |1                   |1                     |30 |50.0    |
          |2022-10-01 |Altice Prepaid |1                   |1                     |10 |10.0    |
          |2022-10-01 |Altice Prepaid |1                   |1                     |20 |40.0    |
          |2022-10-01 |Altice Prepaid |1                   |1                     |30 |50.0    |
          |2022-10-01 |Boost Prepaid  |1                   |1                     |10 |20.0    |
          |2022-10-01 |Boost Prepaid  |1                   |1                     |20 |80.0    |
  """
      }.as[PortedCarrierLossesVPGM]
      val expectedOutput = setNullableStateForAllColumns(dataframe[PortedGeographicLossShareVPGM] {
        """
          |brand          |plan_type|customer_base_date|vpgm|ported_loss_shares |
          |AT&T Prepaid   |1        |2022-10-01        |10 |0.1    |
          |AT&T Prepaid   |1        |2022-10-01        |20 |0.4    |
          |AT&T Prepaid   |1        |2022-10-01        |30 |0.5    |
          |Altice Prepaid |1        |2022-10-01        |10 |0.1    |
          |Altice Prepaid |1        |2022-10-01        |20 |0.4    |
          |Altice Prepaid |1        |2022-10-01        |30 |0.5    |
          |Boost Prepaid  |1        |2022-10-01        |10 |0.2    |
          |Boost Prepaid  |1        |2022-10-01        |20 |0.8    |
"""
      },true).as[PortedGeographicLossShareVPGM]
      val actualOutput = setNullableStateForAllColumns(GrossLossesVPGM().computePortedCarrierLossGeoShare(portedCarrierLosses).toDF(),true)

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }

  describe("Compute Estimated Gross Losses") {
    import spark.implicits._
    it("output") {

      val flatGrossLossesGeoShare = dataset[GrossLossesAverageFlatShareVPGM] {
        """
          |brand         |plan_type|customer_base_date|vpgm|gross_average_flat_share|
          |AT&T Wireless |1        |2022-03-01        |10 |1.0               |
          |AT&T Wireless |1        |2022-03-01        |20 |2.0               |
          |AT&T Wireless |1        |2022-03-01        |30 |3.0               |
          |Altice        |1        |2022-03-01        |10 |1.0               |
          |Altice        |1        |2022-03-01        |20 |2.0               |
          |Altice        |1        |2022-03-01        |30 |3.0               |
          |Boost         |1        |2022-03-01        |10 |1.0               |
          |Boost         |1        |2022-03-01        |20 |2.0               |
        """
      }.as[GrossLossesAverageFlatShareVPGM]

      val portedCarrierLossGeoShare = dataset[PortedGeographicLossShareVPGM] {
        """
          |brand          |plan_type|customer_base_date|vpgm|ported_loss_shares|
          |AT&T Wireless  |1        |2022-03-01        |10 |0.1               |
          |AT&T Wireless  |1        |2022-03-01        |20 |0.4               |
          |AT&T Wireless  |1        |2022-03-01        |30 |0.5               |
          |Altice         |1        |2022-03-01        |10 |0.1               |
          |Altice         |1        |2022-03-01        |20 |0.4               |
          |Altice         |1        |2022-03-01        |30 |0.5               |
          |Boost          |1        |2022-03-01        |10 |0.2               |
          |Boost          |1        |2022-03-01        |20 |0.8               |
        """
      }

      val grossLossesFlatData = setNullableStateForAllColumns(dataframe[GrossLossesFlatVPGM] {
        """
          |brand         |plan_type|customer_base_date|vpgm|gross_losses_flat|
          |AT&T Wireless |1        |2022-03-01        |10 |1000.0           |
          |AT&T Wireless |1        |2022-06-01        |20 |1000.0           |
          |AT&T Wireless |1        |2022-09-01        |30 |1000.0           |
          |Altice        |1        |2022-03-01        |10 |1000.0           |
          |Altice        |1        |2022-06-01        |20 |1000.0           |
          |Altice        |1        |2022-09-01        |30 |1000.0           |
          |Boost         |1        |2022-03-01        |10 |1000.0           |
          |Boost         |1        |2022-03-01        |20 |1000.0           |
            """
      }, true).as[GrossLossesFlatVPGM]

      val expectedOutput = setNullableStateForAllColumns(dataframe[EstimatedGrossLossesVPGM] {
        """
          |brand        |plan_type|customer_base_date|vpgm|estimated_gross_losses|
          |AT&T Wireless|        1|        2022-03-01| 30|              1750.0|
          |AT&T Wireless|        1|        2022-03-01| 20|              1200.0|
          |AT&T Wireless|        1|        2022-03-01| 10|               550.0|
          |AT&T Wireless|        1|        2022-06-01|  0|                 0.0|
          |AT&T Wireless|        1|        2022-09-01|  0|                 0.0|
          |       Altice|        1|        2022-03-01| 30|              1750.0|
          |       Altice|        1|        2022-03-01| 20|              1200.0|
          |       Altice|        1|        2022-03-01| 10|               550.0|
          |       Altice|        1|        2022-06-01|  0|                 0.0|
          |       Altice|        1|        2022-09-01|  0|                 0.0|
          |        Boost|        1|        2022-03-01| 20|              2800.0|
          |        Boost|        1|        2022-03-01| 10|              1200.0|
        """
      }, true)


      val actualOutput = setNullableStateForAllColumns(GrossLossesVPGM().computeEstimatedGrossLosses(
        flatGrossLossesGeoShare, portedCarrierLossGeoShare, grossLossesFlatData
      ).toDF(), true)

      // Count rows should be same
      actualOutput.count() shouldEqual expectedOutput.count()
      // Dataset equality
//      assertDatasetUnsortedEquals(actualOutput.toDF(), expectedOutput.toDF())
    }
  }
}
