//package com.comlinkdata.emrjobs.spark.wireless_market_share
//
//import com.comlinkdata.commons.testing.CldSparkBaseSpec
//import com.comlinkdata.emrjobs.spark.wireless_market_share.model._
//import com.comlinkdata.largescale.schema.wireless_market_share._
//import org.apache.spark.sql.Dataset
//import java.net.URI
//
//
//class SubscribersSpec extends CldSparkBaseSpec {
//  describe("subs step 1 tests") {
//    import spark.implicits._
//    it("test 1")  {
//      val industryModelData = dataset[IndustryModel] {
//        """
//          |        brand   |plan_type|year|month|gross_additions|gross_losses|subscribers|
//          |AT&T            |        2|2022|   10|         10|      10|    100|
//          |AT&T            |        1|2023|    1|         10|      10|    40|
//          |T-Mobile        |        2|2022|   10|         10|      10|    10|
//          |Boost           |        1|2017|    4|         10|      10|    10|
//          |AT&T Wireless   |        1|2022|    4|         10|      10|    10|
//        """
//      }
//      val installedBaseData = dataset[InstalledBase] {
//        """
//          |current_holder |customer_base_date|mvno_winning_plan_type_id|dma  |dma_name  |total_customers|
//          |AT&T           |2022-10-01        |2                        |532  |532|100        |
//          |T-Mobile       |2022-10-01        |2                        |532  |532|10        |
//          |Boost          |2022-10-01        |2                        |532  |532|10        |
//          |Metro          |2022-10-01        |1                        |506  |506|10       |
//          |Altice         |2022-10-01        |2                        |506  |506|875.00         |
//          |Boost          |2022-10-01        |1                        |506  |506|38735.81       |
//          |Altice         |2022-10-01        |2                        |506  |506|219.00         |
//        """
//      }.as[InstalledBase]
//
//      val portedCarrierWinsData = dataset[PortedCarrierWins] {
//        """
//          |date_trunc |winner         |primary_plan_type_id|secondary_plan_type_id|dma|dma_name|switches|
//          |2022-10-01 |AT&T           |2                   |1                     |532|aaa     |50      |
//          |2022-10-01 |AT&T           |2                   |1                     |506|aaa     |10      |
//          |2022-10-01 |AT&T           |2                   |1                     |507|aaa     |10      |
//          |2022-10-01 |T-Mobile        |2                   |1                     |532|aaa     |10      |
//          |2022-10-01 |T-Mobile        |2                   |1                     |506|aaa     |10      |
//          |2022-10-01 |T-Mobile        |2                   |1                     |507|aaa     |10      |
//          |2022-10-01 |Boost           |2                   |1                     |532|aaa     |10      |
//          |2022-10-01 |Boost           |2                   |1                     |506|aaa     |10      |
//      """
//      }.as[PortedCarrierWins]
//
//      val storeLocationsData = dataset[StoreLocations] {
//        """
//          |store_count|store_type         |carrier        |dma|dma_name|
//          |200         |Authorized Retailer|AT&T           |532|aaaa    |
//          |10         |Authorized Retailer|T-Mobile       |532|aaaa    |
//          |10         |Authorized Retailer|Boost          |532|aaaa    |
//          |10         |Company Owned      |AT&T           |506|aaaa    |
//          |10         |Company Owned      |T-Mobile       |506|aaaa    |
//          |10         |Company Owned      |Boost          |506|aaaa    |
//          |10         |Big Box            |AT&T           |507|aaaa    |
//          |10         |Big Box            |T-Mobile       |507|aaaa    |
//      """
//      }.as[StoreLocations]
//
//      val populationData = dataset[Population] {
//        """
//          |dma|dma_name|pop|
//          |532|aaaa    |20 |
//          |506|aaaa    |30 |
//          |507|aaaa    |50 |
//      """
//      }.as[Population]
//
//      val subscriberWeightsData = SubscriberWeights.read(URI create "wireless-market-share/src/test/resources/com/comlinkdata/emrjobs/spark/wireless_market_share/job/subscriberWeights.csv")
//
//      val actualData = Subscribers().computeInitialDistribution(
//              industryModelData,installedBaseData,portedCarrierWinsData,storeLocationsData,populationData,subscriberWeightsData
//            )
//      actualData.show()
//    }
//  }
//
////  describe("Carriers Installed Base Geographic Share Tests") {
////
////    import spark.implicits._
////
////    it("test geo share") {
////
////      val installedBaseData = dataset[InstalledBase] {
////        """
////          |current_holder |customer_base_date|mvno_winning_plan_type_id|cma  |dma|total_customers|
////          |AT&T Wireless  |2022-10-01        |2                        |213  |532|1641.00        |
////          |T-Mobile       |2022-10-01        |2                        |213  |532|3522.00        |
////          |T-Mobile       |2022-10-01        |2                        |213  |532|3311.00        |
////          |AT&T Wireless  |2022-10-01        |2                        |213  |532|687.00         |
////          |AT&T Wireless  |2023-01-01        |1                        |6    |506|35675.00       |
////          |Metro          |2023-01-01        |1                        |6    |506|90963.00       |
////          |U.S. Cellular  |2023-01-01        |2                        |6    |506|875.00         |
////          |AT&T Wireless  |2023-01-01        |1                        |7    |507|53443.00       |
////          |Boost          |2022-04-01        |1                        |6    |506|38735.81       |
////          |Altice         |2022-04-01        |2                        |6    |506|219.00         |
////        """
////      }
////
////      installedBaseData.createOrReplaceTempView("installedBaseDataView")
////
////      val query =
////        """
////          select
////              n.current_holder,
////              n.customer_base_date,
////              n.mvno_winning_plan_type_id,
////              n.cma,
////              n.dma,
////              n.numerator/d.denominator as share
////          from (
////            select
////              current_holder,
////              customer_base_date,
////              mvno_winning_plan_type_id,
////              cma,
////              dma,
////              sum(total_customers) as numerator
////          from installedBaseDataView
////          group by
////              current_holder,
////              customer_base_date,
////              mvno_winning_plan_type_id,
////              cma,
////              dma
////          ) n
////          left join (
////            select
////              current_holder,
////              customer_base_date,
////              mvno_winning_plan_type_id,
////              sum(total_customers) as denominator
////          from installedBaseDataView
////          group by
////              current_holder,
////              customer_base_date,
////              mvno_winning_plan_type_id
////          ) d
////              on n.current_holder = d.current_holder AND
////              n.customer_base_date = d.customer_base_date AND
////              n.mvno_winning_plan_type_id = d.mvno_winning_plan_type_id
////        """
////      val expectedData: Dataset[geoShareIB] = spark.sql(query).as[geoShareIB]
////
////      val actualData = Subscribers().computeCarriersIBGeoShare(
////        installedBaseData
////      )
////
////      // Count rows should be same
////      actualData.count() shouldEqual expectedData.count()
////      // Dataset equality
////      assertDatasetUnsortedEquals(actualData, expectedData)
////    }
////  }
////
////  describe("Store Locations Geographic Share Tests") {
////
////    import spark.implicits._
////
////    it("test store locations share") {
////
////      val storeLocationsData = dataset[StoreLocations] {
////        """
////          |store_count|store_type         |carrier          |cma  |dma|
////          |1          |Authorized Retailer|AT&T Wireless    |213  |532|
////          |48         |Authorized Retailer|AT&T Wireless    |213  |532|
////          |8          |Authorized Retailer|AT&T Wireless    | 7   |507|
////          |5          |Company Owned      |Sprint Wireless  |25   |514|
////          |30         |Authorized Retailer|Verizon Wireless |31   |535|
////          |18         |Authorized Retailer|T-Mobile         |213  |532|
////          |10         |Authorized Retailer|Verizon Wireless |120  |691|
////          |22         |Authorized Retailer|Cricket Wireless |112  |600|
////          |9          |Big Box            |Best Buy         |60   |534|
////          |9          |Company Owned      |Spectrum Mobile  |21   |617|
////        """
////      }
////
////      storeLocationsData.createOrReplaceTempView("storeLocationsDataView")
////
////      val query =
////        """
////          select
////              n.carrier,
////              n.cma,
////              n.dma,
////              n.numerator/d.denominator as share
////          from (
////            select
////              carrier,
////              cma,
////              dma,
////              sum(store_count) as numerator
////          from storeLocationsDataView
////          group by
////              carrier,
////              cma,
////              dma
////          ) n
////          left join (
////            select
////              carrier,
////              sum(store_count) as denominator
////          from storeLocationsDataView
////          group by
////              carrier
////          ) d
////              on n.carrier = d.carrier
////        """
////
////      val expectedData: Dataset[geoShareStoreLoc] = spark.sql(query).as[geoShareStoreLoc]
////
////      val actualData = Subscribers().computeStoreLocGeoShare(
////        storeLocationsData
////      )
////
////      // Count rows should be same
////      actualData.count() shouldEqual expectedData.count()
////      // Dataset equality
////      assertDatasetUnsortedEquals(actualData, expectedData)
////    }
////  }
////
////  describe("Estimated Subscribers Tests") {
////
////    import spark.implicits._
////
////    it("test estimated subscribers") {
////
////      val industryModelData = dataset[IndustryModel] {
////        """
////          |        brand   |plan_type|year|month|gross_additions|gross_losses|subscribers|
////          |AT&T Wireless   |        2|2022|   10|         266651|      244245|    1493193|
////          |AT&T Wireless   |        1|2023|    1|         212176|      237561|    1515244|
////          |T-Mobile        |        2|2022|   10|         328062|      234743|    1801472|
////          |Boost           |        1|2017|    4|         259374|      179877|    1527790|
////          |AT&T Wireless   |        1|2022|    4|         253258|      195521|    1604543|
////        """
////      }
////
////      val carriersIBGeoShareData = dataset[geoShareIB] {
////        """
////          |current_holder|customer_base_date|mvno_winning_plan_type_id|cma  |dma|share             |
////          |AT&T Wireless |2022-10-01        |2                        |213  |532|0.54              |
////          |AT&T Wireless |2022-10-01        |1                        |213  |532|0.33              |
////          |T-Mobile      |2022-10-01        |2                        |213  |532|1.0               |
////          |AT&T Wireless |2023-01-01        |1                        |6    |506|0.4003119459592899|
////          |Metro         |2023-01-01        |1                        |6    |506|1.0               |
////          |U.S. Cellular |2023-01-01        |2                        |6    |506|1.0               |
////          |AT&T Wireless |2023-01-01        |1                        |7    |507|0.59968805404071  |
////          |Boost         |2022-04-01        |1                        |6    |506|1.0               |
////          |Altice        |2022-04-01        |2                        |6    |506|1.0               |
////        """
////      }
////
////      val storeLocGeoShareData = dataset[geoShareStoreLoc] {
////        """
////          |carrier         |cma  |dma|share              |
////          |AT&T Wireless   |213  |532|0.8596491228070176 |
////          |AT&T Wireless   |7    |507|0.14035087719298245|
////          |Sprint Wireless |25   |514|1.0                |
////          |Verizon Wireless|31   |535|0.75               |
////          |T-Mobile        |213  |532|1.0                |
////          |Verizon Wireless|120  |691|0.25               |
////          |Cricket Wireless|112  |600|1.0                |
////          |Best Buy        |60   |534|1.0                |
////          |Spectrum Mobile |21   |617|1.0                |
////        """
////      }
////
////      industryModelData.createOrReplaceTempView("industryModelDataView")
////      carriersIBGeoShareData.createOrReplaceTempView("carriersIBGeoShareDataView")
////      storeLocGeoShareData.createOrReplaceTempView("storeLocGeoShareDataView")
////
////      val query =
////        """
////          select
////              a.brand,
////              date(cast(a.year as string)||'-'||cast(a.month as string)||'-01') as customer_base_date,
////              a.plan_type,
////              b.cma,
////              b.dma,
////              a.subscribers * b.share as estimated_subs
////              from industryModelDataView a
////          left join (
////            select
////              a.current_holder as brand,
////              a.customer_base_date,
////              a.mvno_winning_plan_type_id as plan_type,
////              a.cma,
////              a.dma,
////              0.8 * a.share + 0.2 * b.share as share
////          from carriersIBGeoShareDataView a
////          left join storeLocGeoShareDataView b
////              on a.current_holder = b.carrier AND
////              a.cma = b.cma AND
////              a.dma = b.dma
////          ) b
////              on a.brand = b.brand AND
////              a.plan_type = b.plan_type AND
////              date(cast(a.year as string)||'-'||cast(a.month as string)||'-01') = b.customer_base_date
////        """
////
////      val expectedData: Dataset[subscribersOutput] = spark.sql(query)
////        .na.fill(0)
////        .as[subscribersOutput]
////
////      val actualData = Subscribers().computeEstimatedSubs(
////        industryModelData, carriersIBGeoShareData, storeLocGeoShareData
////      )
////
////      // Count rows should be same
////      actualData.count() shouldEqual expectedData.count()
////      // Dataset equality
////      assertDatasetUnsortedEquals(actualData, expectedData)
////    }
////  }
//
//}
