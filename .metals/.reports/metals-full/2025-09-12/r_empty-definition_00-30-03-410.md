error id: file://<WORKSPACE>/spark/device-master-table/src/main/scala/com/comlinkdata/dmt/UpdateDeviceMasterTable.scala:`<none>`.
file://<WORKSPACE>/spark/device-master-table/src/main/scala/com/comlinkdata/dmt/UpdateDeviceMasterTable.scala
empty definition using pc, found symbol in pc: `<none>`.
empty definition using semanticdb
empty definition using fallback
non-local guesses:
	 -com/comlinkdata/dmt/MapUtils.RawCarrierMapping.
	 -com/comlinkdata/dmt/MapUtils.monoid_instances.RawCarrierMapping.
	 -spark/implicits/RawCarrierMapping.
	 -RawCarrierMapping.
	 -scala/Predef.RawCarrierMapping.
offset: 1301
uri: file://<WORKSPACE>/spark/device-master-table/src/main/scala/com/comlinkdata/dmt/UpdateDeviceMasterTable.scala
text:
```scala
package com.comlinkdata.dmt

import com.comlinkdata.dmt.MapUtils._
import com.comlinkdata.dmt.MapUtils.monoid_instances._
import com.comlinkdata.dmt.Reconciliation.{findModelCode, findModelInfo, resolveFinalCarrierName}
import com.comlinkdata.dmt.UpdateDeviceMasterTable.DeviceSummaryByDays
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.schema.device_switching.lookup.DeviceLookupTable
import org.apache.spark.sql.{SparkSession, Dataset}
import com.comlinkdata.largescale.schema.udp.lookup.MccMncLookupTableNoSubbrand

import java.sql.Date
import java.time.LocalDate
import com.comlinkdata.largescale.schema.device_master_table.{IfaMasterTable, DeviceSummary}

class UpdateDeviceMasterTable(implicit spark: SparkSession) {

  import spark.implicits._

  def run(
    processingDate: LocalDate,
    deviceSummary: Dataset[DeviceSummary],
    dlt: Dataset[DeviceLookupTable],
    mccmnc: Dataset[MccMncLookupTableNoSubbrand]
  )(ifaTable: Dataset[IfaMasterTable]): Dataset[IfaMasterTable] = {

    val deviceSummaryByDays = deviceSummary.map(summary => DeviceSummaryByDays(
      summary.ifa,
      summary.modelCounts,
      summary.carrierCounts,
      summary.newCarrierCounts
    ))

    val broadcastCarriers = spark.sparkContext.broadcast(RawCarri@@erMapping.getAsMap)
    val broadcastDlt = spark.sparkContext.broadcast(dlt.collect())

    val mccMncLookup = mccmnc
      .map(row => row.mcc_mnc -> row.carrier)
      .collect()
      .toMap

    val broadcastMccMnc = spark.sparkContext.broadcast(mccMncLookup)

    val src = ifaTable.groupByKey(_.ifa)
    val dst = deviceSummaryByDays.groupByKey(_.ifa)

    src.cogroup(dst) {
      case (_, srcRows, dstRows) if dstRows.isEmpty =>
        srcRows

      case (ifa, srcRows, dstRows) if srcRows.isEmpty =>
        // insert new
        val dst = dstRows.next()

        val filtered = dst.filterByDaysRange(processingDate)
        val modelCounts = reduceValues(filtered.modelCounts)
        val carrierCounts = reduceValues(filtered.carrierCounts)
        val newCarrierCounts = reduceValues(filtered.newCarrierCounts)

        val modelCode = findModelCode(modelCounts)
        val modelInfo = findModelInfo(modelCode, broadcastDlt.value)

        val carrier = resolveFinalCarrierName(
          carrierCounts, newCarrierCounts, broadcastCarriers.value, broadcastMccMnc.value
        )

        Seq(IfaMasterTable(
          ifa = ifa,
          min_date = Date.valueOf(processingDate),
          max_date = Date.valueOf(processingDate),
          distinct_days = 1,
          final_carrier_name = carrier,
          modal_model_code = modelCode,
          model_name = modelInfo.map(_.name),
          oem = modelInfo.map(_.oem),
          device_type = modelInfo.flatMap(_.deviceType),
          model_hist = modelCounts,
          carrier_hist = carrierCounts,
          newcarrier_hist = newCarrierCounts
        ))

      case (_, srcRows, dstRows) =>
        // update existing
        val src = srcRows.next()
        val dst = dstRows.next()

        val filtered = dst.filterByDaysRange(src.min_date)
        val modelCounts = reduceValues(filtered.modelCounts)
        val carrierCounts = reduceValues(filtered.carrierCounts)
        val newCarrierCounts = reduceValues(filtered.newCarrierCounts)

        // resolve distinct days and merge existing and new maps of counts
        val updatedMinDate = Date.valueOf(processingDate.min(src.min_date.toLocalDate))
        val updatedMaxDate = Date.valueOf(processingDate.max(src.max_date.toLocalDate))

        val updatedDistinctDays = src.distinct_days + 1

        val mc = Monoid[Map[String, Int]]
        val updatedModelCounts = mc.combine(src.model_hist, modelCounts)
        val updatedCarrierCounts = mc.combine(src.carrier_hist, carrierCounts)
        val updatedNewCarrierCounts = mc.combine(src.newcarrier_hist, newCarrierCounts)

        val modelUpdate = src.modal_model_code.fold({
          val code = findModelCode(updatedModelCounts)
          findModelInfo(code, broadcastDlt.value)
        })(_ => None)

        val carrierUpdate = src.final_carrier_name.orElse(resolveFinalCarrierName(
          updatedCarrierCounts, updatedNewCarrierCounts, broadcastCarriers.value, broadcastMccMnc.value
        ))

        Seq(src.copy(
          modal_model_code = modelUpdate.map(_.code).orElse(src.modal_model_code),
          model_name = modelUpdate.map(_.name).orElse(src.model_name),
          oem = modelUpdate.map(_.oem).orElse(src.oem),
          device_type = modelUpdate.flatMap(_.deviceType).orElse(src.device_type),
          final_carrier_name = carrierUpdate,
          min_date = updatedMinDate,
          max_date = updatedMaxDate,
          distinct_days = updatedDistinctDays,
          model_hist = updatedModelCounts,
          carrier_hist = updatedCarrierCounts,
          newcarrier_hist = updatedNewCarrierCounts
        ))
    }
  }
}

object UpdateDeviceMasterTable {

  case class DeviceSummaryByDays(
    ifa: String,
    modelCounts: CountsMap,
    carrierCounts: CountsMap,
    newCarrierCounts: CountsMap
  ) {
    val resolutionWindowMaxDays: Int = 21

    def filterByDaysRange(minDay: LocalDate): DeviceSummaryByDays = {
      filterByDaysRange(minDay.toEpochDay)
    }

    def filterByDaysRange(minDay: Date): DeviceSummaryByDays = {
      filterByDaysRange(minDay.toLocalDate.toEpochDay)
    }

    def filterByDaysRange(minDay: Long): DeviceSummaryByDays = {
      val daysRange = minDay to minDay + resolutionWindowMaxDays
      this.copy(
        modelCounts = modelCounts filterKeys daysRange.contains,
        carrierCounts = carrierCounts filterKeys daysRange.contains,
        newCarrierCounts = newCarrierCounts filterKeys daysRange.contains
      )
    }
  }

  def apply()(implicit spark: SparkSession) = new UpdateDeviceMasterTable()
}
```


#### Short summary: 

empty definition using pc, found symbol in pc: `<none>`.