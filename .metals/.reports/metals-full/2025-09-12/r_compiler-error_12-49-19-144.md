file://<WORKSPACE>/spark/device-master-table/src/main/scala/com/comlinkdata/dmt/UpdateDeviceMasterTable.scala
### java.lang.AssertionError: NoDenotation.owner

occurred in the presentation compiler.

presentation compiler configuration:


action parameters:
uri: file://<WORKSPACE>/spark/device-master-table/src/main/scala/com/comlinkdata/dmt/UpdateDeviceMasterTable.scala
text:
```scala
package com.comlinkdata.dmt

import com.comlinkdata.dmt.MapUtils._
import com.comlinkdata.dmt.MapUtils.monoid_instances._
import com.comlinkdata.dmt.Reconciliation.{findModelCode, findModelInfo, resolveFinalCarrierName}
import com.comlinkdata.dmt.UpdateDeviceMasterTable.DeviceSummaryByDays
import com.comlinkdata.largescale.commons.RichDate.toRichDate
import com.comlinkdata.largescale.schema.device_switching.lookup.DeviceLookupTable
import org.apache.spark.sql.{SparkSession, Dataset}
import com.comlinkdata.largescale.schema.udp.lookup.MccMncLookupTableNoSubbrand

import java.sql.Date
import java.time.LocalDate
import com.comlinkdata.largescale.schema.device_master_table.{IfaMasterTable, DeviceSummary}

class UpdateDeviceMasterTable(implicit spark: SparkSession) {

  import spark.implicits._

  def run(
    processingDate: LocalDate,
    deviceSummary: Dataset[DeviceSummary],
    dlt: Dataset[DeviceLookupTable],
    mccmnc: Dataset[MccMncLookupTableNoSubbrand]
  )(ifaTable: Dataset[IfaMasterTable]): Dataset[IfaMasterTable] = {

    val deviceSummaryByDays = deviceSummary.map(summary => DeviceSummaryByDays(
      summary.ifa,
      summary.modelCounts,
      summary.carrierCounts,
      summary.newCarrierCounts
    ))

    val broadcastCarriers = spark.sparkContext.broadcast(RawCarrierMapping.getAsMap)
    val broadcastDlt = spark.sparkContext.broadcast(dlt.collect())

    val mccMncLookup = mccmnc
      .map(row => row.mcc_mnc -> row.carrier)
      .collect()
      .toMap

    val broadcastMccMnc = spark.sparkContext.broadcast(mccMncLookup)

    val src = ifaTable.groupByKey(_.ifa)
    val dst = deviceSummaryByDays.groupByKey(_.ifa)

    src.cogroup(dst) {
      case (_, srcRows, dstRows) if dstRows.isEmpty =>
        srcRows

      case (ifa, srcRows, dstRows) if srcRows.isEmpty =>
        // insert new
        val dst = dstRows.next()

        val filtered = dst.filterByDaysRange(processingDate)
        val modelCounts = reduceValues(filtered.modelCounts)
        val carrierCounts = reduceValues(filtered.carrierCounts)
        val newCarrierCounts = reduceValues(filtered.newCarrierCounts)

        val modelCode = findModelCode(modelCounts)
        val modelInfo = findModelInfo(modelCode, broadcastDlt.value)

        val carrier = resolveFinalCarrierName(
          carrierCounts, newCarrierCounts, broadcastCarriers.value, broadcastMccMnc.value
        )

        Seq(IfaMasterTable(
          ifa = ifa,
          min_date = Date.valueOf(processingDate),
          max_date = Date.valueOf(processingDate),
          distinct_days = 1,
          final_carrier_name = carrier,
          modal_model_code = modelCode,
          model_name = modelInfo.map(_.name),
          oem = modelInfo.map(_.oem),
          device_type = modelInfo.flatMap(_.deviceType),
          model_hist = modelCounts,
          carrier_hist = carrierCounts,
          newcarrier_hist = newCarrierCounts
        ))

      case (_, srcRows, dstRows) =>
        // update existing
        val src = srcRows.next()
        val dst = dstRows.next()

        val filtered = dst.filterByDaysRange(src.min_date)
        val modelCounts = reduceValues(filtered.modelCounts)
        val carrierCounts = reduceValues(filtered.carrierCounts)
        val newCarrierCounts = reduceValues(filtered.newCarrierCounts)

        // resolve distinct days and merge existing and new maps of counts
        val updatedMinDate = Date.valueOf(processingDate.min(src.min_date.toLocalDate))
        val updatedMaxDate = Date.valueOf(processingDate.max(src.max_date.toLocalDate))

        val updatedDistinctDays = src.distinct_days + 1

        val mc = Monoid[Map[String, Int]]
        val updatedModelCounts = mc.combine(src.model_hist, modelCounts)
        val updatedCarrierCounts = mc.combine(src.carrier_hist, carrierCounts)
        val updatedNewCarrierCounts = mc.combine(src.newcarrier_hist, newCarrierCounts)

        val modelUpdate = src.modal_model_code.fold({
          val code = findModelCode(updatedModelCounts)
          findModelInfo(code, broadcastDlt.value)
        })(_ => None)

        val carrierUpdate = src.final_carrier_name.orElse(resolveFinalCarrierName(
          updatedCarrierCounts, updatedNewCarrierCounts, broadcastCarriers.value, broadcastMccMnc.value
        ))

        Seq(src.copy(
          modal_model_code = modelUpdate.map(_.code).orElse(src.modal_model_code),
          model_name = modelUpdate.map(_.name).orElse(src.model_name),
          oem = modelUpdate.map(_.oem).orElse(src.oem),
          device_type = modelUpdate.flatMap(_.deviceType).orElse(src.device_type),
          final_carrier_name = carrierUpdate,
          min_date = updatedMinDate,
          max_date = updatedMaxDate,
          distinct_days = updatedDistinctDays,
          model_hist = updatedModelCounts,
          carrier_hist = updatedCarrierCounts,
          newcarrier_hist = updatedNewCarrierCounts
        ))
    }
  }
}

object UpdateDeviceMasterTable {

  case class DeviceSummaryByDays(
    ifa: String,
    modelCounts: CountsMap,
    carrierCounts: CountsMap,
    newCarrierCounts: CountsMap
  ) {
    val resolutionWindowMaxDays: Int = 21

    def filterByDaysRange(minDay: LocalDate): DeviceSummaryByDays = {
      filterByDaysRange(minDay.toEpochDay)
    }

    def filterByDaysRange(minDay: Date): DeviceSummaryByDays = {
      filterByDaysRange(minDay.toLocalDate.toEpochDay)
    }

    def filterByDaysRange(minDay: Long): DeviceSummaryByDays = {
      val daysRange = minDay to minDay + resolutionWindowMaxDays
      this.copy(
        modelCounts = modelCounts filterKeys daysRange.contains,
        carrierCounts = carrierCounts filterKeys daysRange.contains,
        newCarrierCounts = newCarrierCounts filterKeys daysRange.contains
      )
    }
  }

  def apply()(implicit spark: SparkSession) = new UpdateDeviceMasterTable()
}
```



#### Error stacktrace:

```
dotty.tools.dotc.core.SymDenotations$NoDenotation$.owner(SymDenotations.scala:2609)
	dotty.tools.dotc.core.SymDenotations$SymDenotation.isSelfSym(SymDenotations.scala:715)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse(ExtractSemanticDB.scala:330)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.apply(Trees.scala:1770)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.apply(Trees.scala:1770)
	dotty.tools.dotc.ast.Trees$Instance$TreeAccumulator.fold$1(Trees.scala:1636)
	dotty.tools.dotc.ast.Trees$Instance$TreeAccumulator.apply(Trees.scala:1638)
	dotty.tools.dotc.ast.Trees$Instance$TreeAccumulator.foldOver(Trees.scala:1669)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.traverseChildren(Trees.scala:1771)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse(ExtractSemanticDB.scala:457)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.apply(Trees.scala:1770)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.apply(Trees.scala:1770)
	dotty.tools.dotc.ast.Trees$Instance$TreeAccumulator.foldOver(Trees.scala:1677)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.traverseChildren(Trees.scala:1771)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse(ExtractSemanticDB.scala:457)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.apply(Trees.scala:1770)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.apply(Trees.scala:1770)
	dotty.tools.dotc.ast.Trees$Instance$TreeAccumulator.fold$1(Trees.scala:1636)
	dotty.tools.dotc.ast.Trees$Instance$TreeAccumulator.apply(Trees.scala:1638)
	dotty.tools.dotc.ast.Trees$Instance$TreeAccumulator.foldOver(Trees.scala:1675)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.traverseChildren(Trees.scala:1771)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse(ExtractSemanticDB.scala:457)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse$$anonfun$13(ExtractSemanticDB.scala:391)
	scala.runtime.function.JProcedure1.apply(JProcedure1.java:15)
	scala.runtime.function.JProcedure1.apply(JProcedure1.java:10)
	scala.collection.immutable.List.foreach(List.scala:334)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse(ExtractSemanticDB.scala:386)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.apply(Trees.scala:1770)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.apply(Trees.scala:1770)
	dotty.tools.dotc.ast.Trees$Instance$TreeAccumulator.foldOver(Trees.scala:1669)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.traverseChildren(Trees.scala:1771)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse(ExtractSemanticDB.scala:457)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.apply(Trees.scala:1770)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.apply(Trees.scala:1770)
	dotty.tools.dotc.ast.Trees$Instance$TreeAccumulator.foldOver(Trees.scala:1724)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.traverseChildren(Trees.scala:1771)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse(ExtractSemanticDB.scala:354)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse$$anonfun$11(ExtractSemanticDB.scala:377)
	scala.runtime.function.JProcedure1.apply(JProcedure1.java:15)
	scala.runtime.function.JProcedure1.apply(JProcedure1.java:10)
	scala.collection.immutable.List.foreach(List.scala:334)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse(ExtractSemanticDB.scala:377)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.apply(Trees.scala:1770)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.apply(Trees.scala:1770)
	dotty.tools.dotc.ast.Trees$Instance$TreeAccumulator.foldOver(Trees.scala:1728)
	dotty.tools.dotc.ast.Trees$Instance$TreeAccumulator.foldOver(Trees.scala:1642)
	dotty.tools.dotc.ast.Trees$Instance$TreeTraverser.traverseChildren(Trees.scala:1771)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse(ExtractSemanticDB.scala:351)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse$$anonfun$1(ExtractSemanticDB.scala:315)
	scala.runtime.function.JProcedure1.apply(JProcedure1.java:15)
	scala.runtime.function.JProcedure1.apply(JProcedure1.java:10)
	scala.collection.immutable.List.foreach(List.scala:334)
	dotty.tools.dotc.semanticdb.ExtractSemanticDB$Extractor.traverse(ExtractSemanticDB.scala:315)
	dotty.tools.pc.SemanticdbTextDocumentProvider.textDocument(SemanticdbTextDocumentProvider.scala:36)
	dotty.tools.pc.ScalaPresentationCompiler.semanticdbTextDocument$$anonfun$1(ScalaPresentationCompiler.scala:242)
```
#### Short summary: 

java.lang.AssertionError: NoDenotation.owner