error id: file://<WORKSPACE>/spark/device-master-table/src/main/scala/com/comlinkdata/dmt/job/UpdateDeviceMasterTableJob.scala:java/net/URI#
file://<WORKSPACE>/spark/device-master-table/src/main/scala/com/comlinkdata/dmt/job/UpdateDeviceMasterTableJob.scala
empty definition using pc, found symbol in pc: java/net/URI#
empty definition using semanticdb
empty definition using fallback
non-local guesses:
	 -java/net/URI#
	 -URI#
	 -scala/Predef.URI#
offset: 813
uri: file://<WORKSPACE>/spark/device-master-table/src/main/scala/com/comlinkdata/dmt/job/UpdateDeviceMasterTableJob.scala
text:
```scala
package com.comlinkdata.dmt.job

import com.comlinkdata.dmt.UpdateDeviceMasterTable
import org.apache.spark.sql.SparkSession
import com.comlinkdata.largescale.commons.io.dataset.dynamicOverwrite

import java.time.LocalDate
import com.comlinkdata.largescale.schema.device_master_table.{IfaMasterTable, DeviceSummary}
import com.comlinkdata.largescale.schema.device_switching.lookup.DeviceLookupTable
import com.typesafe.scalalogging.LazyLogging

import java.net.URI
import org.apache.spark.sql.functions.lit
import com.comlinkdata.largescale.schema.udp.lookup.MccMncLookupTable
import com.comlinkdata.largescale.commons.{SparkJobRunner, SparkJob}

import java.sql.Date

case class UpdateDeviceMasterTableJobConfig(
  processingDate: Option[LocalDate],
  ifaMasterTableDatesLocation: URI,
  deviceSummaryLocation: U@@RI,
  dltLocation: URI,
  mccMncLocation: URI,
  outputPartitions: Int
)

object UpdateDeviceMasterTableJob extends SparkJob[UpdateDeviceMasterTableJobConfig](UpdateDeviceMasterTableRunner)

object UpdateDeviceMasterTableRunner extends SparkJobRunner[UpdateDeviceMasterTableJobConfig] with LazyLogging {

  def runJob(config: UpdateDeviceMasterTableJobConfig)(implicit spark: SparkSession): Unit = {
    // if config.processingDate is defined, IFA master table must exist on that day - 1
    val processingDate = config.processingDate
      .orElse(IfaMasterTable.latestDate(config.ifaMasterTableDatesLocation).map(_.plusDays(1)))
      .get // one of these two must exist

    logger.info(s"Processing date: $processingDate")
    val ifaTable = IfaMasterTable.read(config.ifaMasterTableDatesLocation, processingDate.minusDays(1))

    val deviceSummary = DeviceSummary.read(config.deviceSummaryLocation, processingDate)
    val dlt = DeviceLookupTable.read(config.dltLocation)
    val mccMnc = MccMncLookupTable.readNoSubbrand(config.mccMncLocation)

    val updated = ifaTable
      .transform(UpdateDeviceMasterTable().run(processingDate, deviceSummary, dlt, mccMnc))
      .withColumn("date", lit(Date.valueOf(processingDate)))
      .cache()

    dynamicOverwrite(updated, config.ifaMasterTableDatesLocation, config.outputPartitions, "date")
  }
}

```


#### Short summary: 

empty definition using pc, found symbol in pc: java/net/URI#