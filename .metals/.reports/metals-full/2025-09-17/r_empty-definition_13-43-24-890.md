error id: file://<WORKSPACE>/spark/first-party-ingestion/src/main/scala/com/opensignal/emrjobs/spark/firstpartyingestion/RawIngestionJob.scala:`<none>`.
file://<WORKSPACE>/spark/first-party-ingestion/src/main/scala/com/opensignal/emrjobs/spark/firstpartyingestion/RawIngestionJob.scala
empty definition using pc, found symbol in pc: `<none>`.
empty definition using semanticdb
empty definition using fallback
non-local guesses:
	 -com/comlinkdata/largescale/commons/RichDate.config.target.
	 -com/comlinkdata/largescale/commons/RichDate.config.target#
	 -com/comlinkdata/largescale/commons/RichDate.config.target().
	 -spark/implicits/config/target.
	 -spark/implicits/config/target#
	 -spark/implicits/config/target().
	 -config/target.
	 -config/target#
	 -config/target().
	 -scala/Predef.config.target.
	 -scala/Predef.config.target#
	 -scala/Predef.config.target().
offset: 2697
uri: file://<WORKSPACE>/spark/first-party-ingestion/src/main/scala/com/opensignal/emrjobs/spark/firstpartyingestion/RawIngestionJob.scala
text:
```scala
package com.opensignal.emrjobs.spark.firstpartyingestion

import com.amazonaws.services.simplesystemsmanagement.{AWSSimpleSystemsManagement, AWSSimpleSystemsManagementClientBuilder}
import com.comlinkdata.largescale.commons.RichDate._
import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner, TimeSeriesLocation, Utils}
import com.opensignal.largescale.schema.first_party_ingestion.ingested_raw.{IngestedConnects, IngestedDisconnects}
import com.typesafe.scalalogging.LazyLogging

import java.net.URI
import java.sql.Date
import org.apache.spark.sql.functions.{col, max}
import org.apache.spark.sql.{SaveMode, SparkSession}

import scala.util.Try

case class RawIngestionConfig(
  provider: String,
  source: URI,
  target: String,
  partitions: Int,
  dateRangeOpt: Option[LocalDateRange],
  hashingSaltParameterStoreKey: String
)

case class RawIngestionRunner[T](getAdapter: String => Option[Adapter[T]])
  extends SparkJobRunner[RawIngestionConfig] with LazyLogging {
  override def runJob(config: RawIngestionConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._
    spark.conf.set("spark.sql.sources.partitionOverwriteMode", "dynamic")
    spark.conf.set("spark.sql.legacy.timeParserPolicy", "CORRECTED")
    val ssmClient: AWSSimpleSystemsManagement = AWSSimpleSystemsManagementClientBuilder.defaultClient
    val hashingSalt: String = Utils.getPasswordFromParameterStore(ssmClient, config.hashingSaltParameterStoreKey)
    val adapter = getAdapter(config.provider.toLowerCase())
      .getOrElse(scala.sys.error(s"Unrecognized provider: ${config.provider}"))
    val sourceTsl = TimeSeriesLocation
      .ofDatePartitions(config.source, datePartitionName = adapter.readPartitionName)
      .build
    val dateRange = config.dateRangeOpt.getOrElse {
      val earliestAvailableSource = sourceTsl.earliestDate
      val earliestUnprocessedTarget = Try {
        spark.read.parquet(config.target)
          .agg(max('upload_date)).as[Date]
          .collect.head.toLocalDate
          .plusDays(1)
      }.toOption
      LocalDateRange.of(
        Seq(Some(earliestAvailableSource), earliestUnprocessedTarget).flatten.max,
        sourceTsl.latestDate)
    }
    if (!dateRange.isEmpty)
      for (uploadDate <- dateRange)
        if (sourceTsl.exists(uploadDate)) {
          logger.info(s"Processing ${config.provider} data for $uploadDate")
          adapter.read(sourceTsl, uploadDate.toDate, hashingSalt)
            .repartition(config.partitions, col(adapter.writePartitionName), 'upload_date)
            .write.mode(SaveMode.Append)
            .partitionBy(adapter.writePartitionName, "upload_date")
            .parquet(config.tar@@get)
        }
        else logger.info(s"Skipping non-existent upload date $uploadDate")
    else logger.warn(s"No data to process in the range $dateRange")
  }
}

object RawConnectIngestionJob extends SparkJob(RawIngestionRunner[IngestedConnects](connectAdapters.get(_)))

object RawDisconnectIngestionJob extends SparkJob(RawIngestionRunner[IngestedDisconnects](disconnectAdapters.get(_)))

```


#### Short summary: 

empty definition using pc, found symbol in pc: `<none>`.