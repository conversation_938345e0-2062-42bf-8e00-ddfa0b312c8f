error id: file://<WORKSPACE>/spark/first-party-ingestion/src/test/scala/com/opensignal/emrjobs/spark/firstpartyingestion/tier2/OptimizationPreparation/OptimizationPreparationJobSpec.scala:`<none>`.
file://<WORKSPACE>/spark/first-party-ingestion/src/test/scala/com/opensignal/emrjobs/spark/firstpartyingestion/tier2/OptimizationPreparation/OptimizationPreparationJobSpec.scala
empty definition using pc, found symbol in pc: `<none>`.
empty definition using semanticdb
empty definition using fallback
non-local guesses:
	 -spark/implicits/adjusted_non_mover_wins.
	 -spark/implicits/adjusted_non_mover_wins#
	 -spark/implicits/adjusted_non_mover_wins().
	 -adjusted_non_mover_wins.
	 -adjusted_non_mover_wins#
	 -adjusted_non_mover_wins().
	 -scala/Predef.adjusted_non_mover_wins.
	 -scala/Predef.adjusted_non_mover_wins#
	 -scala/Predef.adjusted_non_mover_wins().
offset: 9644
uri: file://<WORKSPACE>/spark/first-party-ingestion/src/test/scala/com/opensignal/emrjobs/spark/firstpartyingestion/tier2/OptimizationPreparation/OptimizationPreparationJobSpec.scala
text:
```scala
package com.opensignal.emrjobs.spark.firstpartyingestion.tier2.OptimizationPreparation

import com.comlinkdata.commons.testing.CldSparkBaseSpec
import com.opensignal.emrjobs.spark.firstpartyingestion.tier2.OptimizationPreparation.IntermediaryModels.PreparedBbcvAggregationModels.{BbcvLossAgg, BbcvWinAgg, BbcvWinAndLossAgg, SpatialInputLossAgg, SpatialInputWinAgg, SpatialInputWinAndLossAgg}
import com.opensignal.largescale.schema.first_party_ingestion.tier1.BbcvDataPreparation.{PreparedBbcvOutputLosses, PreparedBbcvOutputWins}
import com.opensignal.largescale.schema.first_party_ingestion.tier2.OptimizationPreparation.NonMoverWinsPerLossOptimizationInput
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.functions.lit

import java.sql.Date
import java.time.LocalDate


class OptimizationPreparationJobSpec extends CldSparkBaseSpec {
  import spark.implicits._
  implicit val DYNAMIC_CONFIG: OptimizationPreparationDynamicConfig = OptimizationPreparationDynamicConfig("inner", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)

  object Shared {
    val SAMPLE_BBCV_WIN: PreparedBbcvOutputWins = PreparedBbcvOutputWins(
      ifa = Array(0.toByte),
      churn_date = Date.valueOf("2022-01-01"),
      loser_sp_platform = 9001,
      winner_sp_platform = 1009,
      is_mover = true,
      losing_census_blockid = "54321",
      winning_census_blockid = "12345",
      latitude = Some(0.toFloat),
      longitude = Some(0.toFloat),
      submarket = Some("submarket"),
      winning_dma_name = Some("DMA 1"),
      month_end_date = Date.valueOf("2022-01-21"),
      processing_date = Date.valueOf("2022-01-21"),
      multiplier = 1
    )
    val SAMPLE_BBCV_LOSS: PreparedBbcvOutputLosses = PreparedBbcvOutputLosses(
      ifa = Array(0.toByte),
      churn_date = Date.valueOf("2022-01-01"),
      loser_sp_platform = 9001,
      winner_sp_platform = 1009,
      is_mover = true,
      losing_census_blockid = "54321",
      winning_census_blockid = "12345",
      latitude = Some(0.toFloat),
      longitude = Some(0.toFloat),
      submarket = Some("submarket"),
      losing_dma_name = Some("DMA 1"),
      month_end_date = Date.valueOf("2022-01-21"),
      processing_date = Date.valueOf("2022-01-21"),
      multiplier = 1
    )

    val SAMPLE_SPATIAL_INPUT_WIN_AGG: SpatialInputWinAgg = SpatialInputWinAgg(
      month_end_date = Date.valueOf("2022-01-21"),
      dma_name = "DMA",
      wins = 1,
      competitive_wins = 1,
      cord_attachers = 1
    )
    val SAMPLE_SPATIAL_INPUT_WIN_AGG_DS: Dataset[SpatialInputWinAgg] = Seq(SAMPLE_SPATIAL_INPUT_WIN_AGG).toDS().persist

    val SAMPLE_SPATIAL_INPUT_LOSS_AGG: SpatialInputLossAgg = SpatialInputLossAgg(
      month_end_date = Date.valueOf("2022-01-21"),
      dma_name = "DMA",
      losses = 2,
      competitive_losses = 2,
      cord_cutters = 2
    )
    val SAMPLE_SPATIAL_INPUT_LOSS_AGG_DS: Dataset[SpatialInputLossAgg] = Seq(SAMPLE_SPATIAL_INPUT_LOSS_AGG).toDS().persist

    val SAMPLE_BBCV_WIN_DS: Dataset[PreparedBbcvOutputWins] = Seq(
      SAMPLE_BBCV_WIN.copy(),
      SAMPLE_BBCV_WIN.copy(), // duplicate ifa in a group, not included in count
      SAMPLE_BBCV_WIN.copy(winning_dma_name = Some("DMA 2")), // new dma group
      SAMPLE_BBCV_WIN.copy(month_end_date = Date.valueOf("2022-02-21")), // new date group
      SAMPLE_BBCV_WIN.copy(winning_dma_name = Some("DMA 3"), month_end_date = Date.valueOf("2022-03-21")), // new date & dma
      SAMPLE_BBCV_WIN.copy(ifa = Array(1.toByte), winning_dma_name = Some("DMA 2")), // second win for DMA 2 with default FME
      SAMPLE_BBCV_WIN.copy(ifa = Array(2.toByte), is_mover = false), // non mover win
      SAMPLE_BBCV_WIN.copy(loser_sp_platform = 6746, multiplier = 2, is_mover = false), // non mover win with multiplier
      SAMPLE_BBCV_WIN.copy(loser_sp_platform = 6746, multiplier = 3, is_mover = true) // mover win with multiplier
    ).toDS()
    val SAMPLE_BBCV_WIN_AGG_DS: Dataset[BbcvWinAgg] = OptimizationPreparationJobRunner.aggregateBbcvWins(SAMPLE_BBCV_WIN_DS).persist

    val SAMPLE_BBCV_LOSS_DS: Dataset[PreparedBbcvOutputLosses] = Seq(
      SAMPLE_BBCV_LOSS.copy(),
      SAMPLE_BBCV_LOSS.copy(), // duplicate ifa in a group, not included in count
      SAMPLE_BBCV_LOSS.copy(losing_dma_name = Some("DMA 2")), // new dma group
      SAMPLE_BBCV_LOSS.copy(month_end_date = Date.valueOf("2022-02-21")), // new date group
      SAMPLE_BBCV_LOSS.copy(losing_dma_name = Some("DMA 3"), month_end_date = Date.valueOf("2022-03-21")), // new date & dma
      SAMPLE_BBCV_LOSS.copy(ifa = Array(1.toByte), losing_dma_name = Some("DMA 2")), // second win for DMA 2 with default FME
      SAMPLE_BBCV_LOSS.copy(ifa = Array(2.toByte), is_mover = false), // non mover win
      SAMPLE_BBCV_LOSS.copy(winner_sp_platform = 6746, multiplier = 2, is_mover = false), // non mover win with multiplier
      SAMPLE_BBCV_LOSS.copy(winner_sp_platform = 6746, multiplier = 3, is_mover = true) // non mover win with multiplier
    ).toDS()
    val SAMPLE_BBCV_LOSS_AGG_DS: Dataset[BbcvLossAgg] = OptimizationPreparationJobRunner.aggregateBbcvLosses(SAMPLE_BBCV_LOSS_DS).persist

  }

  describe("aggregateBbcvWins"){
    it("groups by month_end_date & winning_dma_name as dma"){
      Shared.SAMPLE_BBCV_WIN_AGG_DS.count() shouldEqual Shared.SAMPLE_BBCV_WIN_AGG_DS.select($"month_end_date", $"dma_name").distinct().count()
    }
    it("only counts distinct IFAs per group"){
      Shared.SAMPLE_BBCV_WIN_AGG_DS.count() shouldEqual 4
      Shared.SAMPLE_BBCV_WIN_AGG_DS.where($"dma_name"===lit("DMA 2")).collect()(0).wins shouldEqual 2
    }
    it("aggregates non-mover wins"){
      val result = Shared.SAMPLE_BBCV_WIN_AGG_DS.where($"non_mover_wins"===lit(3)).collect()(0)
      result.month_end_date shouldEqual Shared.SAMPLE_BBCV_WIN.month_end_date
      result.dma_name shouldEqual Shared.SAMPLE_BBCV_WIN.winning_dma_name.get
    }
  }

  describe("aggregateBbcvLosses"){
    it("groups by month_end_date & losing_dma_name as dma"){
      Shared.SAMPLE_BBCV_LOSS_AGG_DS.count() shouldEqual Shared.SAMPLE_BBCV_LOSS_AGG_DS.select($"month_end_date", $"dma_name").distinct().count()
    }
    it("only counts distinct IFAs per group"){
      Shared.SAMPLE_BBCV_LOSS_AGG_DS.count() shouldEqual 4
      Shared.SAMPLE_BBCV_LOSS_AGG_DS.where($"dma_name"===lit("DMA 2")).collect()(0).losses shouldEqual 2
    }
    it("aggregates non-mover losses"){
      val result = Shared.SAMPLE_BBCV_LOSS_AGG_DS.where($"non_mover_losses"===lit(3)).collect()(0)
      result.month_end_date shouldEqual Shared.SAMPLE_BBCV_LOSS.month_end_date
      result.dma_name shouldEqual Shared.SAMPLE_BBCV_LOSS.losing_dma_name.get
    }
  }

  describe("combineBbcvWinsAndLosses"){
    it("Joins the expected fields together"){
      val result = OptimizationPreparationJobRunner.combineBbcvWinsAndLosses(Shared.SAMPLE_BBCV_WIN_AGG_DS, Shared.SAMPLE_BBCV_LOSS_AGG_DS).persist()
      val expected = result.count()
      val actual = result.where($"wins"===$"losses" && $"mover_wins"===$"mover_losses" && $"non_mover_wins"===$"non_mover_losses").count()
      expected shouldEqual actual
    }
    it("inner joins"){
      val result = OptimizationPreparationJobRunner.combineBbcvWinsAndLosses(Shared.SAMPLE_BBCV_WIN_AGG_DS, spark.emptyDataset[BbcvLossAgg])
      result.count() shouldEqual 0
    }
  }

  describe("combineSpatialInputWinsAndLosses"){
    it("joins the expected fields together"){
      val result = OptimizationPreparationJobRunner.combineSpatialInputWinsAndLosses(Shared.SAMPLE_SPATIAL_INPUT_WIN_AGG_DS, Shared.SAMPLE_SPATIAL_INPUT_LOSS_AGG_DS).collect()(0)
      val expected = SpatialInputWinAndLossAgg(Date.valueOf("2022-01-21"), "DMA", 1, 1.toFloat, 1.toFloat, 2, 2.toFloat, 2.toFloat)
      result shouldEqual expected
    }
    it("inner joins"){
      val result = OptimizationPreparationJobRunner.combineSpatialInputWinsAndLosses(Shared.SAMPLE_SPATIAL_INPUT_WIN_AGG_DS, spark.emptyDataset[SpatialInputLossAgg])
      result.count() shouldEqual 0
    }
  }

  describe("calculateNonMoverWinsPerLossMetrics"){
    it("Produces the expected results"){
      val bbcv = BbcvWinAndLossAgg(
        month_end_date = Date.valueOf("2022-01-21"),
        dma_name = "DMA",
        wins = 20,
        losses = 10,
        mover_wins = 20,
        mover_losses = 10,
        non_mover_wins = 20,
        non_mover_losses = 10
      )
      val spatialInput = SpatialInputWinAndLossAgg(
        month_end_date = Date.valueOf("2022-01-21"),
        dma_name = "DMA",
        wins = 50,
        competitive_wins = 50,
        cord_attachers = 50,
        losses = 25,
        competitive_losses = 25,
        cord_cutters = 25
      )
      val bbcvDs = Seq(bbcv).toDS()
      val spatialInputDs = Seq(spatialInput).toDS()
      val expected = NonMoverWinsPerLossOptimizationInput(
        month_end_date = bbcv.month_end_date,
        dma_name = bbcv.dma_name,
        raw_wins = bbcv.wins,
        raw_losses = bbcv.losses,
        raw_wins_per_loss = 2.0,
        raw_mover_wins = bbcv.mover_wins,
        raw_mover_losses = bbcv.mover_losses,
        raw_mover_wins_per_loss = 2.0,
        raw_non_mover_wins = bbcv.non_mover_wins,
        raw_non_mover_losses = bbcv.non_mover_losses,
        raw_non_mover_wins_per_loss = 2.0,
        adjusted_wins = spatialInput.wins,
        adjusted_losses = spatialInput.losses,
        adjusted_wins_per_loss = 2.0,
        adjusted_mover_wins = 50.0,  // 50 * 20(mover_wins) / 20(non_mover_wins) = 50
        adjusted_mover_losses = 25.0, // 25 * 10(mover_losses) / 10(non_mover_losses) = 25
        adjusted_mover_wins_per_loss = 2.0,  // adjusted mover wins / adjusted mover losses
        ad@@justed_non_mover_wins = 50.0, // 50 * 20(non_mover_wins) / 20(mover_losses)
        adjusted_non_mover_losses = 25.0, // 25 * 10(non_mover_losses) / 10(mover_losses)
        adjusted_non_mover_wins_per_loss = 2.0, // adjusted non mover wins / adjusted non mover losses
        adjusted_cord_attachers = spatialInput.cord_attachers,
        adjusted_cord_cutters = spatialInput.cord_cutters,
        processing_date = Date.valueOf("2022-02-21")
      )
      val result = OptimizationPreparationJobRunner.calculateNonMoverWinsPerLossMetrics(bbcvDs, spatialInputDs, LocalDate.of(2022,2,21)).collect()(0)
      result shouldEqual expected
    }
  }

}

```


#### Short summary: 

empty definition using pc, found symbol in pc: `<none>`.