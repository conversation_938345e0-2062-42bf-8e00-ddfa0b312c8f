error id: file://<WORKSPACE>/spark/first-party-ingestion/src/main/scala/com/opensignal/emrjobs/spark/firstpartyingestion/tier1/SpatialInputPreparation/SpatialInputPreparationJob.scala:`<none>`.
file://<WORKSPACE>/spark/first-party-ingestion/src/main/scala/com/opensignal/emrjobs/spark/firstpartyingestion/tier1/SpatialInputPreparation/SpatialInputPreparationJob.scala
empty definition using pc, found symbol in pc: `<none>`.
empty definition using semanticdb
empty definition using fallback
non-local guesses:
	 -com/opensignal/emrjobs/spark/firstpartyingestion/FirstPartyIngestionUtils.validateDateRange.
	 -com/opensignal/emrjobs/spark/firstpartyingestion/FirstPartyIngestionUtils.validateDateRange#
	 -com/opensignal/emrjobs/spark/firstpartyingestion/FirstPartyIngestionUtils.validateDateRange().
	 -com/opensignal/largescale/schema/first_party_ingestion/ingested_raw/validateDateRange.
	 -com/opensignal/largescale/schema/first_party_ingestion/ingested_raw/validateDateRange#
	 -com/opensignal/largescale/schema/first_party_ingestion/ingested_raw/validateDateRange().
	 -com/opensignal/largescale/schema/first_party_ingestion/tier1/SpatialInputPreparation.validateDateRange.
	 -com/opensignal/largescale/schema/first_party_ingestion/tier1/SpatialInputPreparation.validateDateRange#
	 -com/opensignal/largescale/schema/first_party_ingestion/tier1/SpatialInputPreparation.validateDateRange().
	 -spark/implicits/validateDateRange.
	 -spark/implicits/validateDateRange#
	 -spark/implicits/validateDateRange().
	 -validateDateRange.
	 -validateDateRange#
	 -validateDateRange().
	 -scala/Predef.validateDateRange.
	 -scala/Predef.validateDateRange#
	 -scala/Predef.validateDateRange().
offset: 2262
uri: file://<WORKSPACE>/spark/first-party-ingestion/src/main/scala/com/opensignal/emrjobs/spark/firstpartyingestion/tier1/SpatialInputPreparation/SpatialInputPreparationJob.scala
text:
```scala
package com.opensignal.emrjobs.spark.firstpartyingestion.tier1.SpatialInputPreparation

import com.comlinkdata.largescale.commons.{LocalDateRange, SparkJob, SparkJobRunner}
import com.opensignal.emrjobs.spark.firstpartyingestion.FirstPartyIngestionUtils
import com.opensignal.emrjobs.spark.firstpartyingestion.FirstPartyIngestionUtils.validateDateRange
import com.opensignal.emrjobs.spark.firstpartyingestion.tier1.SpatialInputPreparation.lookup.SpatialInputLookupManager
import com.opensignal.largescale.schema.first_party_ingestion.ingested_raw._
import com.opensignal.largescale.schema.first_party_ingestion.tier1.SpatialInputPreparation._
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{Dataset, SaveMode, SparkSession}

import java.net.URI
import java.time.LocalDate
import scala.reflect.ClassTag
import scala.reflect.runtime.universe.TypeTag


case class SpatialInputPreparationConfig(
  provider: String,
  connectsSource: URI,
  disconnectsSource: URI,
  connectsDestination: URI,
  disconnectsDestination: URI,
  masterBlockLookupPath: URI,
  masterBlockNumPartitions: Option[Int],
  censusBlockNumPartitions: Option[Int],
  dateRangeOpt: Option[LocalDateRange],
  lookBackMonths: Option[Int],
  endOfMonthDay: Option[Int],
  processingDate: Option[LocalDate],
  numFilesPerPartition: Option[Int]
) {
  val DEFAULT_MB_PARTS: Int = 200
  val DEFAULT_CB_PARTS: Int = 8
  val DEFAULT_INGESION_REV_NAME: String = "upload_date"
  val DEFAULT_NUM_FILES_PER_PARTITION: Int = 1
}

object SpatialInputPreparationJobRunner extends SparkJobRunner[SpatialInputPreparationConfig] with LazyLogging {
  override def runJob(config: SpatialInputPreparationConfig)(implicit spark: SparkSession): Unit = {
    import spark.implicits._

    // Initial Setup
    spark.conf.set("spark.sql.sources.partitionOverwriteMode", "dynamic")
    val mbParts = config.masterBlockNumPartitions.getOrElse(config.DEFAULT_MB_PARTS)
    val cbParts = config.censusBlockNumPartitions.getOrElse(config.DEFAULT_CB_PARTS)
    val outParts = config.numFilesPerPartition.getOrElse(config.DEFAULT_NUM_FILES_PER_PARTITION)
    val dateRange = FirstPartyIngestionUtils.getProcessingDateRange(config.dateRangeOpt, config.lookBackMonths, config.endOfMonthDay)
    validateDa@@teRange(dateRange)
    val revision = config.processingDate.getOrElse(LocalDate.now())
    logger.info(s"Running Spatial Input Preparation for Dates in range: (${dateRange.startDate}, ${dateRange.endDate})")

    // Load Source Tables
    val connects = IngestedConnects.read(config.connectsSource.toString, dateRange, revision)
    val disconnects = IngestedDisconnects.read(config.disconnectsSource.toString, dateRange, revision)

    // Create the Lookup Manager
    val spatialInputLookupManager = SpatialInputLookupManager(config.provider, config.masterBlockLookupPath, mbParts, cbParts)

    // Get the Custom Rules Adapter
    val adapter = SpatialInputCustomRulesAdapter.forProvider(config.provider, spatialInputLookupManager)

    // Join connects to location-based lookups
    val spatialInputConnects = connects
      .map(row => IntermediarySpatialInput(row))
      .transform(runSharedGeoLookupsForSpatialInput(spatialInputLookupManager, adapter))
      .map(row => SpatialInputConnect(row, config.processingDate))
      .repartition(outParts, $"month_end_date", $"processing_date")

    // Join Disconnects to location-based lookups
    val spatialInputDisconnects = disconnects
      .map(row => IntermediarySpatialInput(row))
      .transform(runSharedGeoLookupsForSpatialInput(spatialInputLookupManager, adapter))
      .map(row => SpatialInputDisconnect(row, config.processingDate))
      .repartition(outParts, $"month_end_date", $"processing_date")

    // Write to S3
    spatialInputConnects.write.mode(SaveMode.Overwrite).partitionBy("month_end_date", "processing_date").parquet(config.connectsDestination.toString)
    spatialInputDisconnects.write.mode(SaveMode.Overwrite).partitionBy("month_end_date", "processing_date").parquet(config.disconnectsDestination.toString)
  }


  /**
    * Wrapper method for performing all shared geo-based lookups (including any custom lookup rules provided within the
    * SpatialInputCustomRulesAdapter provided. The lookups performed are (1) census block (2) block group (3) census
    * tract (4) submarket (5) all custom business rules lookups
    * @param lookupManager SpatialInputLookupManager that has been pre-configured with the necessary lookup paths
    * @param adapter SpatialInputCustomRulesAdapter that has been pre-configured for the current provider
    * @param data IntermediarySpatialInput dataset to be transformed with all shared geo lookups
    * @param spark
    * @tparam T
    * @return
    */
  def runSharedGeoLookupsForSpatialInput[T <: IngestedConnectionCommons : ClassTag : TypeTag]
  (lookupManager: SpatialInputLookupManager, adapter: SpatialInputCustomRulesAdapter)
  (data: Dataset[IntermediarySpatialInput[T]])
  (implicit spark: SparkSession): Dataset[IntermediarySpatialInput[T]] = {
    data
      .transform(lookupManager.runCensusBlockLookup)
      .transform(lookupManager.runBlockGroupLookup)
      .transform(lookupManager.runCensusTractLookup)
      .transform(adapter.applyCustomRules) // requires implicit spark to be in-scope
      .transform(lookupManager.runSubmarketsLookup) // must run last
  }

}

object SpatialInputPreparationJob extends SparkJob(SpatialInputPreparationJobRunner)

```


#### Short summary: 

empty definition using pc, found symbol in pc: `<none>`.