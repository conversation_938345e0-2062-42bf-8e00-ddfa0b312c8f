"""Common pytest fixtures"""
import pytest
from pyspark.sql import SparkSession


@pytest.fixture(scope="session", autouse=True)
def spark_session(request) -> SparkSession:
    """Create the one spark tests session

    Parameters
    ----------
    request : _type_
        for finalizer

    Returns
    -------
    SparkSession
        test spark session
    """
    spark_session = SparkSession.builder.appName("AllChurn")\
        .master("local[8]")\
        .config("spark.executor.memory", "4g")\
        .config("spark.driver.memory", "4g")\
        .config("spark.sql.shuffle.partitions", "1")\
        .getOrCreate()

    request.addfinalizer(lambda: spark_session.sparkContext.stop())

    return spark_session
