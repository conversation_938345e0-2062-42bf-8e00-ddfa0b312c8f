"""Helper methods dealing with dataframes related to tests"""
from tempfile import NamedTemporaryFile
from pyspark.sql import DataFrame, SparkSession


def show_to_df(in_str: str, spark: SparkSession) -> DataFrame:
    """Convert the output of df.show() to a dataframe

    Parameters
    ----------
    in_str: str
        The string to convert
    spark: SparkSession
        The spark session to use

    Returns
    -------
    DataFrame
        The dataframe
    """
    with NamedTemporaryFile() as temp:
        temp.write(in_str.encode("utf-8"))
        temp.flush()

        result = spark.read \
                      .option("header", "true") \
                      .option("inferSchema", "true") \
                      .option("delimiter", "|") \
                      .option("parserLib", "UNIVOCITY") \
                      .option("ignoreLeadingWhiteSpace", "true") \
                      .option("ignoreTrailingWhiteSpace", "true") \
                      .option("comment", "+") \
                      .csv(spark.sparkContext.textFile(temp.name)).cache()
        result = result.drop(*[c for c in result.columns if c.startswith('_c')])
        result.count()  # force caching of dataframe. Underlying temp file will be deleted
        return result
