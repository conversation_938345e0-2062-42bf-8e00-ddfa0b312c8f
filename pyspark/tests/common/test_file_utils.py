"""tests for file utils"""
import os
import unittest

from tests.common.test_path import TEST_DATA_ROOT
from common import fileutils


def test_get_scheme() -> None:
    """tests get scheme"""
    assert fileutils.get_scheme(TEST_DATA_ROOT) == ""
    assert fileutils.get_scheme("s3://bucket") == "s3"
    assert fileutils.get_scheme("s3a://bucket") == "s3"


def test_list_directories() -> None:
    """tests list directory"""
    root = os.path.join(TEST_DATA_ROOT, "allchurn", "wireless_carrier") + "/"
    expected = sorted(["year=2021/month=12/day=31", "year=2022/month=03/day=31"])
    actual = sorted([x[len(root):] for x in fileutils.list_directories(root, recursive=True)])
    expected == actual

    expected = sorted(["year=2021", "year=2022"])
    actual = sorted([x[len(root):] for x in fileutils.list_directories(root, recursive=False)])
    expected == actual


@unittest.skip("s3")
def test_list_directories_s3() -> None:
    """tests s3 list directory"""
    path = "s3://e000-comlinkdata-com/staging/broadband/installbase/ifa_wireless_carrier/v=0.2/r=2/"
    assert fileutils.list_directories(path, recursive=True, max_level=1) is not None


def test_exists() -> None:
    """tests exists"""
    assert fileutils.exists(TEST_DATA_ROOT)


@unittest.skip("s3")
def test_exists_s3() -> None:
    """tests s3 exists"""
    path = "s3://e000-comlinkdata-com/staging/broadband/installbase/ifa_wireless_carrier/v=0.2/r=2/"
    assert fileutils.exists(path)
    assert fileutils.exists(path + "bad") is False


def test_is_directory() -> None:
    """tests is directory"""
    assert fileutils.is_directory(TEST_DATA_ROOT)
    assert fileutils.is_directory(os.path.join(TEST_DATA_ROOT, "common", "test_file.txt")) is False


def test_all_directory_strings_have_data_or_throw() -> None:
    """tests all_directory_strings_have_data_or_throw"""
    root = os.path.join(TEST_DATA_ROOT, 'allchurn', 'wireless_carrier')

    expected = [os.path.join(root, "year=2022/month=03/day=31"), os.path.join(root, "year=2021/month=12/day=31")]
    actual = fileutils.all_directory_strings_have_data_or_throw(paths=expected, has_success_marker=False)
    assert sorted(expected) == sorted(actual)

    try:
        fileutils.all_directory_strings_have_data_or_throw(
            paths=[os.path.join(root, "year=2021/month=12/day=31")],
            has_success_marker=True
        )
    except Exception:
        assert True
    else:
        assert False

    expected = [os.path.join(root, "year=2022/month=03/day=31")]
    actual = fileutils.all_directory_strings_have_data_or_throw(paths=expected, has_success_marker=True)
    assert sorted(expected) == sorted(actual)

    assert fileutils.all_directory_strings_have_data_or_throw(paths=[]) == []


def test_bad_scheme() -> None:
    """tests a bad scheme and ensures the right exceptions are thrown"""
    try:
        fileutils.exists("IAmABadScheme://" + os.path.join("some", "file", "path"))
    except NotImplementedError:
        assert True
    except Exception:
        assert False
    else:
        assert False
