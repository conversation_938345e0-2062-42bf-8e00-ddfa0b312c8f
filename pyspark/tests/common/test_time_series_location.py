"""unit tests for time series location"""
import os
from datetime import datetime, timedelta

import pytest
from common import time_series_location
from common.path import parse_date_partition_from_date, ymd_from_date, ymd_path
from common.time_series_location import YMDTimeSeriesLocation
from tests.common.test_path import TEST_DATA_ROOT

source_date_uri = os.path.join(TEST_DATA_ROOT, "date")
source_ymd_uri = os.path.join(TEST_DATA_ROOT, "ymd")
FIRST_DATE = datetime(2010, 1, 1)
LAST_DATE = datetime(2010, 1, 10)


class TestCustomTSL:
    """Using TEST_DATA_ROOT test some specific edge cases"""

    def test_test_bbchurn_dist(self) -> None:
        """tests tsl on bbchurn inputs"""
        tsl = YMDTimeSeriesLocation(os.path.join(TEST_DATA_ROOT, "allchurn", "bbchurn_dist"))
        assert tsl.earliest_date() == datetime(year=2022, month=3, day=5)
        assert tsl.earliest_date(datetime(year=2022, month=3, day=4)) == datetime(year=2022, month=3, day=5)
        assert tsl.earliest_date(datetime(year=2022, month=3, day=5)) == datetime(year=2022, month=3, day=5)
        assert tsl.earliest_date(datetime(year=2022, month=3, day=6)) == datetime(year=2022, month=3, day=6)

        assert tsl.latest_date() == datetime(year=2022, month=3, day=6)
        assert tsl.latest_date(datetime(year=2022, month=3, day=5)) == datetime(year=2022, month=3, day=5)
        with pytest.raises(ValueError):
            tsl.latest_date(datetime(year=2022, month=3, day=1))

    def test_test_no_partitions(self) -> None:
        """tests ymd time series on a partition that doesn't exist"""
        tsl = YMDTimeSeriesLocation(os.path.join(TEST_DATA_ROOT, "allchurn", "wireless_carrier"))
        with pytest.raises(ValueError):
            tsl.latest_date(before_or_equal=datetime(year=2021, month=1, day=1))


class TestOfPartitionBuilderWithLooks:
    """Tests creation of TSL with lookahead and lookbacks"""

    default_tsl_date = time_series_location.of_date_partitions(source=source_date_uri, partition_name="date")
    default_tsl_ymd = time_series_location.of_ymd_partitions(source=source_ymd_uri)
    initial_tsl_date = time_series_location.of_date_partitions(source=source_date_uri, partition_name="date")
    initial_tsl_ymd = time_series_location.of_ymd_partitions(source=source_ymd_uri)

    def test_it_sets_src_value(self) -> None:
        """tests source path is set"""
        assert self.default_tsl_date.source == source_date_uri
        assert self.default_tsl_ymd.source == source_ymd_uri
        assert self.initial_tsl_date.source == source_date_uri
        assert self.initial_tsl_ymd.source == source_ymd_uri

    def test_it_sets_default_first_date_to_none(self) -> None:
        """tests first date is default"""
        assert self.default_tsl_date.first_date is None
        assert self.default_tsl_ymd.first_date is None

    def test_it_sets_default_last_date_to_none(self) -> None:
        """tests last date is default"""
        assert self.default_tsl_date.last_date is None
        assert self.default_tsl_ymd.last_date is None


class TestPartitionCreation:
    """Tests partition creations during the initialization of tsl object"""

    tsl_date = time_series_location.of_date_partitions(source_date_uri)
    tsl_ymd = time_series_location.of_ymd_partitions(source_ymd_uri)

    def test_it_generates_results_with_date(self) -> None:
        """tests tsl.partition"""
        expected = os.path.join(source_date_uri, parse_date_partition_from_date(FIRST_DATE, "date"))
        assert self.tsl_date.partition(FIRST_DATE) == expected
        assert self.tsl_ymd.partition(FIRST_DATE) == ymd_path(source_ymd_uri, FIRST_DATE)

    def test_it_handles_multiple_partitions(self) -> None:
        """Handles custom partitions"""
        tsl_date = self.tsl_date \
            .with_partition("custom_partition=hello") \
            .with_partition("custom_partition_2=subfolder")
        expected = os.path.join(
            source_date_uri,
            "custom_partition=hello",
            "custom_partition_2=subfolder",
            parse_date_partition_from_date(FIRST_DATE, "date")
        )
        assert tsl_date.partition(FIRST_DATE) == expected

        tsl_ymd = self.tsl_ymd \
            .with_partition("custom_partition=hello") \
            .with_partition("custom_partition_2=subfolder")
        expected = os.path.join(
            source_ymd_uri,
            "custom_partition=hello",
            "custom_partition_2=subfolder",
            f"{ymd_from_date(FIRST_DATE)}"
        )
        assert tsl_ymd.partition(FIRST_DATE) == expected


class TestTimeSeriesLocation:
    """test cases for input partitions"""

    def test_throw_when_input_partition_before_first_date(self):
        """Should throw a ValueError"""
        tsl_date = time_series_location.of_date_partitions(source=source_date_uri, first_date=FIRST_DATE)

        try:
            tsl_date.input_partitions_for(FIRST_DATE - timedelta(days=1), FIRST_DATE, True)
        except ValueError:
            assert True
        else:
            assert False

        tsl_ymd = time_series_location.of_ymd_partitions(source=source_ymd_uri, first_date=FIRST_DATE)
        try:
            tsl_ymd.input_partitions_for(FIRST_DATE - timedelta(days=1), FIRST_DATE, True)
        except ValueError:
            assert True
        else:
            assert False

    def test_throw_when_input_partition_after_last_date(self):
        """Should throw a ValueError"""
        tsl_date = time_series_location.of_date_partitions(source=source_date_uri, last_date=LAST_DATE)
        try:
            tsl_date.input_partitions_for(FIRST_DATE, LAST_DATE + timedelta(days=1), True)
        except ValueError:
            assert True
        else:
            assert False

        tsl_ymd = time_series_location.of_ymd_partitions(source=source_ymd_uri, last_date=LAST_DATE)
        try:
            tsl_ymd.input_partitions_for(FIRST_DATE, LAST_DATE + timedelta(days=1), True)
        except ValueError:
            assert True
        else:
            assert False

    def test_do_not_throw(self):
        """Doesn't throw with input_partitions_for throw_if_out_range = False (Default)"""
        # input_partition before first date
        try:
            parts = time_series_location \
                .of_date_partitions(source=source_date_uri, first_date=FIRST_DATE) \
                .input_partitions_for(start_date=FIRST_DATE - timedelta(days=1), end_date=FIRST_DATE)
            expected = os.path.join(source_date_uri, parse_date_partition_from_date(FIRST_DATE, "date"))
            assert expected in parts
        except ValueError:
            assert False

        # input_partition after last date
        try:
            parts = time_series_location \
                .of_date_partitions(source=source_date_uri, last_date=LAST_DATE) \
                .input_partitions_for(start_date=FIRST_DATE, end_date=LAST_DATE + timedelta(days=1))
            expected = os.path.join(source_date_uri, parse_date_partition_from_date(FIRST_DATE, "date"))
            assert expected in parts
        except ValueError:
            assert False

    def test_check_throws_on_safest(self):
        """Don't throw when asking for existing input partitions"""
        try:
            parts = time_series_location \
                .of_date_partitions(source=source_date_uri, first_date=FIRST_DATE, last_date=LAST_DATE) \
                .input_partitions_for(start_date=FIRST_DATE, end_date=LAST_DATE)
            expected = os.path.join(source_date_uri, parse_date_partition_from_date(FIRST_DATE, "date"))
            assert expected in parts
        except ValueError:
            assert False
