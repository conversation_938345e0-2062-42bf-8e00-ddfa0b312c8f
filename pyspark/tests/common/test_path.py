"""unit tests for paths"""
import os
import unittest
from datetime import datetime
import tests

TEST_DATA_ROOT = tests.__file__.replace("__init__.py", "") + "data/"


def test_parse_ymd_partition() -> None:
    """tests parsing ymd partitions"""
    from common.path import parse_ymd_partition
    assert parse_ymd_partition("bbchurn_dist/year=2022/month=03/day=05") == "year=2022/month=03/day=05"


def test_parse_date_partition() -> None:
    """tests parsing date partitions"""
    from common.path import parse_date_partition
    assert parse_date_partition("bbchurn_dist/date=2022-01-01", "date") == "date=2022-01-01"
    assert parse_date_partition("bbchurn_dist/churn_date=2022-01-01", "churn_date") == "churn_date=2022-01-01"
    assert parse_date_partition("bbchurn_dist/window_tail_date=2022-01-01",
                                "window_tail_date") == "window_tail_date=2022-01-01"


def test_parse_ymd_partition_to_date() -> None:
    """tests parsing ymd partitions to dates"""
    from common.path import parse_ymd_partition_to_date
    d = datetime(year=2022, month=3, day=5)
    assert parse_ymd_partition_to_date("year=2022/month=03/day=05") == d
    # these should never happen -- Maybe we should throw error instead
    assert parse_ymd_partition_to_date("year=2022/month=3/day=05") == d
    assert parse_ymd_partition_to_date("year=2022/month=03/day=5") == d
    assert parse_ymd_partition_to_date("year=2022/month=3/day=5") == d


def test_parse_date_partition_to_date() -> None:
    """tests parsing date partitions to date"""
    from common.path import parse_date_partition_to_date
    d = datetime(year=2022, month=3, day=5)
    assert parse_date_partition_to_date("date=2022-03-05", "date") == d
    assert parse_date_partition_to_date("churn_date=2022-03-05", "churn_date") == d
    assert parse_date_partition_to_date("window_tail_date=2022-03-05", "window_tail_date") == d


def test_get_earliest_date() -> None:
    """tests getting the earliest dates from ymd"""
    from common.path import get_earliest_date
    path = os.path.join(TEST_DATA_ROOT, "allchurn", "bbchurn_dist")
    assert get_earliest_date(path, "ymd") == datetime(year=2022, month=3, day=5)
    assert get_earliest_date(path, "ymd", datetime(year=2022, month=3, day=5)) == datetime(year=2022, month=3, day=5)
    assert get_earliest_date(path, "ymd", datetime(year=2022, month=3, day=6)) == datetime(year=2022, month=3, day=6)


@unittest.skip("s3")
def test_get_earliest_date_s3() -> None:
    """tests getting the earliest dates from s3 ymd"""
    from common.path import get_earliest_date
    p = "s3://e000-comlinkdata-com/staging/broadband/installbase/ifa_wireless_carrier/v=0.2/r=2/"
    assert get_earliest_date(p, "ymd") == datetime(year=2018, month=3, day=31)


def test_get_latest_date() -> None:
    """tests getting the earliest dates from ymd partition"""
    from common.path import get_latest_date
    path = os.path.join(TEST_DATA_ROOT, "allchurn", "bbchurn_dist")
    # assume today == 2022-03-06
    assert get_latest_date(path, "ymd") == datetime(year=2022, month=3, day=6)
    # test past
    assert get_latest_date(path, "ymd", datetime(year=2022, month=3, day=5)) == datetime(year=2022, month=3, day=5)
    # test future
    assert get_latest_date(path, "ymd", datetime(year=2022, month=12, day=1)) == datetime(year=2022, month=3, day=6)


def test_max_partition_value() -> None:
    """tests helper function for getting the max partitions"""
    from common.path import _max_partition_value
    t = _max_partition_value(directory=os.path.join(TEST_DATA_ROOT, "allchurn", "bbchurn_dist"), partition_name="ymd")
    assert t[1] == "/year=2022/month=03/day=05/"


def test_get_date() -> None:
    """tests helper function get date"""
    from common.path import _get_date
    d = _get_date(directory=os.path.join(TEST_DATA_ROOT, "allchurn", "bbchurn_dist"), partition_name="ymd")
    assert d == datetime(year=2022, month=3, day=5)


def test_parse_partition() -> None:
    """test parsing a partition string to dates"""
    from common.path import parse_partition
    expected = datetime(year=2022, month=3, day=31)
    assert parse_partition(os.path.join("x", "year=2022", "month=03", "day=31"), "ymd") == expected
    assert parse_partition(os.path.join("x", "date=2022-03-31"), "date") == expected
    assert parse_partition(os.path.join("x", "churn_date=2022-03-31"), "churn_date") == expected
    assert parse_partition(os.path.join("x", "window_tail_date=2022-03-31"), "window_tail_date") == expected
    try:
        parse_partition(os.path.join("x", "bad_date=2022-03-31"), "bad_date")
    except NotImplementedError:
        assert True
    except Exception:
        assert False
    else:
        assert False

    try:
        parse_partition(os.path.join("x", "unknown_partition=2022-03-31"), "unknown_partition")
    except NotImplementedError:
        assert True
    except Exception:
        assert False
    else:
        assert False
