"""All Churn tests"""
import os
from datetime import datetime

from common.date import DATE_FORMAT
from pyspark.sql import SparkSession
from tests.common.test_path import TEST_DATA_ROOT

from broadband.all_churn import AllChurn

ALL_CHURN_DATA = os.path.join(TEST_DATA_ROOT, "allchurn")


def _test_data(ds_name: str) -> str:
    return os.path.join(ALL_CHURN_DATA, ds_name) + os.sep


def test_saved_data(spark_session: SparkSession) -> None:
    """Test All Churn with saved data

    Parameters
    ----------
    spark_session : SparkSession
        test spark sessions
    """
    ac = AllChurn(
        spark_session,
        _test_data("high_confidence"),
        _test_data("cell_only"),
        _test_data("deactivations"),
        _test_data("activations"),
        _test_data("transfers"),
        _test_data("bbchurn_dist"),
        _test_data("wireless_carrier"))

    all_churn_out = ac.all_churn(datetime.strptime("2022-03-05", DATE_FORMAT), 1, 60)
    assert all_churn_out.count() == 50


def test_saved_data_bb_look_back(spark_session: SparkSession) -> None:
    """
    Test All Churn with the same saved data but bb data has been move forward one day.

    Check that it we still find the cord things, cell only, and transfers from prev saturday

    Parameters
    ----------
    spark_session : SparkSession
        test spark sessions
    """
    ac = AllChurn(
        spark_session,
        _test_data("high_confidence"),
        _test_data("cell_only"),
        _test_data("deactivations"),
        _test_data("activations"),
        _test_data("transfers"),
        _test_data("bbchurn_dist"),
        _test_data("wireless_carrier"))

    all_churn_out = ac.all_churn(datetime.strptime("2022-03-06", DATE_FORMAT), 1, 60)
    all_churn_out.show(100)
    assert all_churn_out.count() == 50
