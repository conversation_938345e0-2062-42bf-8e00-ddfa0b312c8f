"""Test the wms spark pipeline."""

from tests.common.dataframe_helpers import show_to_df
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, format_string
import pytest
try:
    from wms.model import format_inputs_pandas, build_model, return_estimates
    skip_no_pandas = False
except ModuleNotFoundError:
    # Pandas & PyTorch is not installed on the build machines currently
    skip_no_pandas = True


def _remake_dma_cma_combined_col(df):
    return df.withColumn(
        "dma|cma",
        format_string("%d|%d", col("dma"), col("cma")),
    ).drop("dma", "cma")


@pytest.mark.skipif(skip_no_pandas, reason="requires pandas and pytorch")
def test_single_geo(spark_session: SparkSession) -> None:
    """Test the wms model spark pipeline."""
    age = """
+-----------+-------------------+-------+-----+
|age_segment|                 V2|dma|cma|  age|
+-----------+-------------------+-------+-----+
|      age_0| 0.2641997123315224|  501|1|age_0|
|      age_1| 0.3301362056290934|  501|1|age_1|
|      age_2| 0.2926062417370915|  501|1|age_2|
|      age_3|0.08939790833437809|  501|1|age_3|
+-----------+-------------------+-------+-----+
"""
    age_df = show_to_df(age, spark_session)
    age_df = _remake_dma_cma_combined_col(age_df)

    inc = """
+-----------+-------------------+-------+-----+
|inc_segment|                 V2|dma|cma|  inc|
+-----------+-------------------+-------+-----+
|      inc_3|0.26161341098947777|  501|1|inc_3|
|      inc_2|0.16333113551215706|  501|1|inc_2|
|      inc_1|  0.242009777096575|  501|1|inc_1|
|      inc_0| 0.3116991603745911|  501|1|inc_0|
+-----------+-------------------+-------+-----+
"""

    inc_df = _remake_dma_cma_combined_col(show_to_df(inc, spark_session))
    race = """
+------------+-------------------+-------+------------+
|race_segment|                 V2|dma|cma|        race|
+------------+-------------------+-------+------------+
|   asian_pop| 0.1060988153525666|  501|1|   asian_pop|
|   black_pop| 0.1713503447551014|  501|1|   black_pop|
|hispanic_pop|0.25076304499965535|  501|1|hispanic_pop|
|   white_pop|0.44835517544295816|  501|1|   white_pop|
+------------+-------------------+-------+------------+
"""

    race_df = _remake_dma_cma_combined_col(show_to_df(race, spark_session))
    dem_data = [{
        "age": age_df.toPandas(),
        "inc": inc_df.toPandas(),
        "race": race_df.toPandas()
    }]

    carrier = """
+-------------------+-----------------+---------------------------+------------------+-----+-------+
|    new_common_name|          carrier|current_holder_plan_type_id| sum(total_losses)|   V2|dma|cma|
+-------------------+-----------------+---------------------------+------------------+-----+-------+
|    AT&T Wireless_1|    AT&T Wireless|                          1|16105.849480745044|  0.5|  501|1|
|    AT&T Wireless_2|    AT&T Wireless|                          2| 25818.46970953684| 0.25|  501|1|
|  Altice Wireless_2|  Altice Wireless|                          2| 3160.737914833728| 0.25|  501|1|
+-------------------+-----------------+---------------------------+------------------+-----+-------+
"""
    carrier_df = show_to_df(carrier, spark_session)

    geo_dim = 1
    car_dim = 3

    carrier_data = [carrier_df.toPandas()]

    gt_d, inputs = format_inputs_pandas(dem_data, carrier_data, geo_dim, car_dim)

    optimal_params, dem_model, dem_carrier_model, dem_mix = \
        build_model(geo_dim, car_dim, gt_d, inputs, 1, 2500, cuda=False)

    df_output = return_estimates(["2022-01-01"], carrier_data[0], optimal_params)

    for demo in ("age", "inc", "race"):
        grouped_by_demo_pd = df_output[[demo, "estimate"]].groupby(demo).sum()

        joined = grouped_by_demo_pd.merge(dem_data[0][demo], on=demo, how="left")
        joined["diff"] = joined["estimate"] - joined["V2"]

        assert joined["diff"].max() < 0.1

    grouped_by_carrier_pd = df_output[["new_common_name", "estimate"]].groupby("new_common_name").sum()
    joined = grouped_by_carrier_pd.merge(carrier_data[0], on="new_common_name", how="left")
    joined["diff"] = joined["estimate"] - joined["V2"]
    assert joined["diff"].max() < 0.1
