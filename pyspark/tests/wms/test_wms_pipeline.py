"""Test the wms spark pipeline."""
from datetime import datetime

from pyspark.sql import SparkSession
from pyspark.sql.functions import col, lit
from wms.pipeline import Pi<PERSON>ine


def test_format_demographic_data(spark_session: SparkSession) -> None:
    """Test that the demographic data is formatted correctly."""
    df = spark_session.createDataFrame(
        [
            (123, 456, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
            (123, 456, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
            (123, 456, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
            (123, 456, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
        ],
        ('dma', 'cma', 'inc_0', 'inc_1', 'inc_2', 'inc_3',
         'black_pop', 'asian_pop', 'hispanic_pop', 'white_pop',
         'age_0', 'age_1', 'age_2', 'age_3')
    )

    result = Pipeline.format_demographic_data(df)

    for cat in ['inc', 'race', 'age']:
        assert result[cat].where(col('V2') == lit(1 / 16)).count() == 16


def test_aggregate_demographic_data(spark_session: SparkSession) -> None:
    """Test that the demographic data is aggregated correctly."""
    cmap_cols = ("serv_terr_blockid", "dma", "cma")

    cmap_df = spark_session.createDataFrame(
        [("010010201001000", 698, 139),
            ("010010201001010", 698, 139),
            ("fake", 999, 999)],
        cmap_cols)

    dem_date_cols = (
        "serv_terr_blockid", "inc_0_30k_hh", "inc_30_50k_hh", "inc_50_75k_hh",
        "inc_75_100k_hh", "inc_100_125k_hh", "inc_125_150k_hh", "inc_150k_plus_hh",
        "black_pop", "asian_pop", "hispanic_pop", "white_pop",
        "age_18_24_pop", "age_25_34_pop", "age_35_44_pop", "age_45_54_pop",
        "age_55_64_pop", "age_65_74_pop", "age_75_84_pop", "age_85_plus_pop")

    dem_date_df = spark_session.createDataFrame(
        [("010010201001000", 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8,
          0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9),
         ("010010201001010", 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8,
          0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9),
         ("fake", 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9,
          1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9),
         ], dem_date_cols)

    result = Pipeline.aggregate_demographic_data(cmap_df, dem_date_df)
    assert result.count() == 1
    as_dict = result.collect()[0].asDict()
    assert as_dict['dma'] == 698
    assert (as_dict['inc_0'] - (0.1 + 0.2 + 0.3) * 2) < 0.0000001
    assert as_dict['hispanic_pop'] == (1.0) * 2
    assert as_dict['age_0'] == (1.2 + 1.3) * 2


def test_format_carrier_share_data(spark_session: SparkSession) -> None:
    """Test format_carrier_share_data"""
    ocn_df = spark_session.createDataFrame(
        [
            (0, "NONE", None, None, "OTH"),
            (1, "Fake", "W", 1, "FKE"),
        ], ("OCN_COMMON_DIM_ID", "COMMON_NAME", "COMMON_NAME_MODE", "ID6", "SP_REPORTING_NM")
    ).withColumn("Changed", lit(None).cast("string"))

    cmap_df = spark_session.createDataFrame(
        [(698, 139, 1),
            (698, 139, 1),
            (999, 999, 1)],
        ("dma", "cma", "rc_zip"))

    carrier_shares_alls =\
        spark_session.createDataFrame(
            [
                ("FakeT&T", "FakeTAndT", 1, 1, 1, 1, 17),
            ], (
                "new_common_name",
                "carrier",
                "current_holder_plan_type_id",
                "zip_rc_kblock",
                "current_holder_sp",
                "primary_sp",
                "ending_customers"))

    c_shares_finals =\
        Pipeline.format_carrier_share_data(ocn_df, cmap_df, carrier_shares_alls, "ending_customers")

    assert c_shares_finals.count() == 2
    assert c_shares_finals.where(col('dma') == lit(999)).select("sum(ending_customers)").collect()[0][0] == 17
    assert c_shares_finals.where(col('dma') == lit(698)).select("sum(ending_customers)").collect()[0][0] == 34


def test_filter_dma_and_cma(spark_session: SparkSession) -> None:
    """Test that the filter_dma_and_cma function filters out the correct rows and columns are replaced"""
    dem_data_segments = {
        "race":
            spark_session.createDataFrame(
                [
                    (123, 456, "asian_pop", 1, 0.1),
                    (123, 456, "asian_pop", 2, 0.2),
                ], ("dma", "cma", "race_segment", "value", "V2")),
        "age":
            spark_session.createDataFrame(
                [
                    (123, 456, "age_0", 1, 0.1),
                    (123, 456, "age_1", 2, 0.2),
                    (999, 999, "remove me", 2, 0.2),
                ], ("dma", "cma", "age_segment", "value", "V2")),
        "inc":
            spark_session.createDataFrame(
                [
                    (123, 456, "inc_0", 1, 0.1),
                    (123, 456, "inc_1", 2, 0.2),
                ], ("dma", "cma", "age_segment", "value", "V2"))}

    carrier_shares_all = spark_session.createDataFrame(
        [(123, 456, "FakeT&T", "FakeTAndT", 1, 702.1, 0.1)],
        ("dma",
         "cma",
         "new_common_name",
         "carrier",
         "current_holder_plan_type_id",
         "sum(ending_customers)",
         "V2"))

    dem_data_segments_out, carrier_shares_alls_out = Pipeline.filter_dma_and_cma(dem_data_segments, carrier_shares_all)
    for k in dem_data_segments_out.keys():
        assert dem_data_segments_out[k].count() == 2
        assert 'dma|cma' in dem_data_segments_out['race'].columns
        assert 'cma' not in dem_data_segments_out['race'].columns

    assert carrier_shares_alls_out.count() == 1


def test_q_logic() -> None:
    """Test that the get_quarter_year function returns the correct quarter"""
    Pipeline.get_quarter_year(datetime.strptime("2022-03-01", "%Y-%m-%d")) == "2022q1"
    Pipeline.get_quarter_year(datetime.strptime("2022-01-01", "%Y-%m-%d")) == "2022q1"
    Pipeline.get_quarter_year(datetime.strptime("2022-04-01", "%Y-%m-%d")) == "2022q2"
    Pipeline.get_quarter_year(datetime.strptime("2022-07-01", "%Y-%m-%d")) == "2022q3"
    Pipeline.get_quarter_year(datetime.strptime("2022-10-01", "%Y-%m-%d")) == "2022q4"
