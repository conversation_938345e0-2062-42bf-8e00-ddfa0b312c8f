"""Permanent Store Reader Class Adapted from relay-data-jobs"""
import logging

import pandas as pd
from datetime import date, datetime
from pandas.core.frame import DataFrame
from broadband_firstparty.permanent_store.permanent_store import PermanentStore
import boto3
import os


class PermanentStoreReader(PermanentStore):
    """Permanent Store Reader Class"""

    def __init__(self, partition_name: str, logger: logging.Logger):
        """
        Constructor for Permanent Store Reader Class

        :param partition_name: default partition name to use
        :param logger: shared logger to output to
        """
        super().__init__(partition_name)
        self.logger = logger

    def read_from_path(self, s3_path: str, start_date: date = None, end_date: date = None) -> DataFrame:
        """
        Read from S3 Path with optional Start and End Dates

        :param s3_path: S3 path to read from
        :param start_date: optional start_date of data
        :param end_date: optional end_date of data
        :return: Dataframe with the data read from S3
        """
        prefixes = self._find_most_recent_prefixes(s3_path, start_date, end_date)

        if not prefixes:
            return pd.DataFrame()

        dataframe_list = []
        for date_partition, values in prefixes.items():
            path = values['path']
            self.logger.info(f'Reading parquet file from {path}.')
            df = self.s3_parquet_util.read_dataset(path)
            df[self.partition_name] = date_partition
            dataframe_list.append(df)
            self.logger.info(f'Read {len(df)} records from {path}.')

        return pd.concat(dataframe_list)

    def read_from_path_with_revision(self, s3_path: str, start_date: date = None, end_date: date = None,
                                     revision_date: date = None, columns=None) -> DataFrame:
        """
        Read parquet data from permanent store s3 path (revisioned) as a data frame

        :param s3_path: Base s3 path to read from
        :param start_date: Optional Start Date for reading
        :param end_date: Optional End Date for reading
        :param revision_date: Optional date revision to read from
        :param columns: Optional columns if only certain columns are needed
        :return: Dataframe with the data that was read from S3
        """
        self.logger.info(s3_path)
        client = boto3.client('s3')
        base = s3_path.lstrip('s3://').lstrip('s3a://').rstrip('/') + '/'
        bucket = base[:base.index('/')]
        prefix = base[base.index('/') + 1:]
        response = client.list_objects(Bucket=bucket, Prefix=prefix, Delimiter='/')
        # for prefixes in response.get('CommonPrefixes'):
        #     self.logger.info(prefixes)
        fme_dates = [prefix.get('Prefix')[-11:-1] for prefix in response.get('CommonPrefixes')]
        fme_prefixes = [(d, os.path.join(prefix,
                                         f'{self.partition_name}={d}/')) for d in fme_dates
                        if start_date <= datetime.strptime(d, '%Y-%m-%d').date() <= end_date]
        revision_prefixes = []
        for date_part, fme_prefix in fme_prefixes:
            response = client.list_objects(Bucket=bucket, Prefix=fme_prefix, Delimiter='/')
            r_prefix = [(date_part, _.get('Prefix')) for _ in response.get('CommonPrefixes')]
            if revision_date is not None:
                r_prefix = [p for p in r_prefix
                            if datetime.strptime(self._find_partition_date_value(p[1],
                                                                                 self.revision_date_name),
                                                 '%Y-%m-%d').date() <= revision_date]
            revision_prefixes.append(max(r_prefix, key=lambda x: x[1]))

        if len(revision_prefixes) == 0:
            return pd.DataFrame()

        dataframe_list = []
        for date_part, prefix in revision_prefixes:
            path = os.path.join('s3://', bucket, prefix).rstrip('/')
            self.logger.info(f'Reading parquet file from {path}.')
            df = self.s3_parquet_util.read_dataset(path, columns)
            df[self.partition_name] = date_part
            dataframe_list.append(df)
            self.logger.info(f'Read {len(df)} records from {path}.')

        return pd.concat(dataframe_list)
