"""Base Class for Permanent Store operations and associated datatypes"""

from s3fs.core import S3FileSystem
from broadband_firstparty.utils.s3_parquet_util import S3ParquetUtil
import os
from broadband_firstparty.utils.util import is_s3_path_valid, InvalidS3Path
import re
from datetime import datetime, date


class VersionDoesNotExist(Exception):
    """Exception class for invalid version"""

    def __init__(self, path: str):
        """
        Constructor for Version Does Not Exist Exception

        :param path: Base S3 Path not containing the given version
        """
        super(VersionDoesNotExist, self).__init__(f'Version does not be exist in {path}')


class RevisionDoesNotExist(Exception):
    """Exception class for invalid revision"""

    def __init__(self, path: str):
        """
        Constructor for Revision Does not Exist Exception

        :param path: path that revision does not exist in
        """
        super(RevisionDoesNotExist, self).__init__(f'Revision does not exist in {path}')


class PartitionDoesNotExist(Exception):
    """Exception class for invalid partition"""

    def __init__(self, partition_name, prefix):
        """
        Constructor for Partition Does Not Exist Exception

        :param partition_name: name of requested partition that does not exist
        :param prefix: prefix along with the partition
        """
        super(PartitionDoesNotExist, self).__init__(f'Partition {partition_name} could not be found from {prefix}')


class MostRecentPrefixesSearchError(Exception):
    """Exception class for error searching for prefixes in S3"""

    def __init__(self, exception):
        """
        Constructor for Prefix Search error

        :param exception: Exception from searching S3 for prefixes
        """
        super(MostRecentPrefixesSearchError, self).__init__(f'Failed to search most recent prefixes: {exception}')


class PermanentStore:
    """Base class for Permanent Store operations"""

    def __init__(self, partition_name):
        """
        Constructor for Permanent Store base class

        :param partition_name: partition name to use for S3 Operations
        """
        self.fs = S3FileSystem()
        self.s3_parquet_util = S3ParquetUtil()
        self.version_name = 'version'
        self.revision_name = 'r'
        self.revision_date_name = 'processing_date'
        self.partition_name = partition_name
        self.default_version = 1
        self.default_revision = 1

    def _find_prefixes(self, s3_path: str, depth: int) -> list:
        """
        Helper function for finding prefixes from the PS with a depth limit

        :param s3_path: Path to search for prefixes in
        :param depth: maximum depth to look from base path
        :return: the prefixes from the given path and depth
        """
        if not is_s3_path_valid(s3_path):
            raise InvalidS3Path(s3_path)
        # i.e. depth = 1, search_pattern = path/*
        # i.e. depth = 2, search_pattern = path/*/*
        search_path = os.path.join(s3_path, *['*'] * depth)
        return self.fs.glob(search_path)

    def _find_most_recent_prefixes(self, s3_path: str, start_date: date = None, end_date: date = None) -> dict:
        """
        Helper function for finding most recent data versions in permanent store

        :param s3_path: Path to search in
        :param start_date: optional start_date when finding most recent
        :param end_date:  optional end_date when finding most recent
        :return: Dict containing the most recent prefixes
        """
        try:
            prefixes = self._find_prefixes(s3_path=s3_path, depth=2)
            most_recent_prefixes = {}

            for prefix in prefixes:
                if '_SUCCESS' in prefix:
                    continue

                version = self._find_version(prefix)
                date_partition = self._find_date_partition(prefix)

                if start_date and datetime.strptime(date_partition, '%Y-%m-%d').date() < start_date:
                    continue

                if end_date and datetime.strptime(date_partition, '%Y-%m-%d').date() > end_date:
                    continue

                if date_partition not in most_recent_prefixes \
                        or version > most_recent_prefixes[date_partition]['version']:
                    most_recent_prefixes[date_partition] = {
                        'version': version,
                        'path': prefix
                    }
        except Exception as exception:
            raise MostRecentPrefixesSearchError(exception)
        else:
            return most_recent_prefixes

    def _find_most_recent_revisioned_prefixes(self, s3_path: str, start_date: date = None,
                                              end_date: date = None) -> dict:
        """
        Find prefixes corresponding to most recent data revision from permanent store

        :param s3_path: S3 path for permanent store
        :param start_date: optional start_date to search within
        :param end_date: optional end_date to search within
        :return:
        """
        try:
            prefixes = self._find_prefixes(s3_path=s3_path, depth=2)
            most_recent_prefixes = {}

            for prefix in prefixes:
                if '_SUCCESS' in prefix:
                    continue

                revision = self._find_revision_date(prefix)
                date_partition = self._find_date_partition(prefix)

                if start_date and datetime.strptime(date_partition, '%Y-%m-%d').date() < start_date:
                    continue

                if end_date and datetime.strptime(date_partition, '%Y-%m-%d').date() > end_date:
                    continue

                if date_partition not in most_recent_prefixes \
                        or revision > most_recent_prefixes[date_partition]['revision']:
                    most_recent_prefixes[date_partition] = {
                        'revision': revision,
                        'path': f's3://{prefix}'
                    }
        except Exception as exception:
            raise MostRecentPrefixesSearchError(exception)
        else:
            return most_recent_prefixes

    def _find_next_version(self, s3_path: str) -> int:
        """
        Find next version from permanent store

        :param s3_path: Base path to find version from
        :return: int corresponding to the next sequential version number
        """
        prefixes = self._find_prefixes(s3_path=s3_path, depth=1)

        if not prefixes:
            return self.default_version

        max_version = self.default_version
        for _path in prefixes:
            if '_SUCCESS' in _path:
                continue
            version = self._find_version(_path)
            max_version = version if version > max_version else max_version
        return max_version + 1

    def _find_next_revision(self, s3_path: str, output_date_partition: date) -> int:
        """
        Find next revision from permanent store

        :param s3_path: Base s3 path to find revision from
        :param output_date_partition: date_partition within the s3 path
        :return: Int corresponding to the next sequential revision based on path and date
        """
        prefixes = self._find_prefixes(s3_path=s3_path, depth=2)

        if not prefixes:
            return self.default_revision

        max_revision = self.default_revision - 1
        for _path in prefixes:
            if '_SUCCESS' in _path:
                continue

            revision = self._find_revision(_path)
            date_partition = self._find_date_partition(_path)

            if datetime.strptime(date_partition, '%Y-%m-%d').date() == output_date_partition:
                max_revision = max(revision, max_revision)

        return max_revision + 1

    def _find_version(self, prefix: str) -> int:
        """
        Find version number from s3 prefix

        :param prefix: given s3 prefix
        :return: the version number
        """
        match = re.search(rf'/{self.version_name}=(\d+)/?', prefix)
        if not match:
            raise VersionDoesNotExist(prefix)
        return int(match.group(1))

    def _find_revision(self, prefix: str) -> int:
        """
        Find revision number from s3 prefix

        :param prefix: given s3 prefix
        :return: the revision number
        """
        match = re.search(rf'/{self.revision_name}=(\d+)/?', prefix)
        if not match:
            raise RevisionDoesNotExist(prefix)
        return int(match.group(1))

    def _find_revision_date(self, prefix: str) -> date:
        """
        Find revision number from s3 prefix

        :param prefix: prefix from s3
        :return: the date within the prefix
        """
        match = re.search(rf'/{self.revision_name}=(\d{{4}})-(\d{{2}})-(\d{{2}})/?', prefix)
        if not match:
            raise RevisionDoesNotExist(prefix)
        datestr = match.group().split('=')[1].rstrip('/')
        return datetime.strptime(datestr, '%Y-%m-%d').date()

    def _find_partition_date_value(self, prefix: str, partition_name: str) -> str:
        """
        Find the date value of a specific partition given a s3 prefix

        :param prefix: the prefix to search
        :param partition_name: the partition name within the prefix
        :return: The date from the given s3 prefix
        """
        date_pattern = r'\d{4}-\d{2}-\d{2}'
        match = re.search(f'{partition_name}=({date_pattern})/?', prefix)
        if not match:
            raise PartitionDoesNotExist(self.partition_name, prefix)
        return match.group(1)

    def _find_date_partition(self, prefix: str) -> str:
        """
        Find date partition from s3 prefix

        :param prefix: s3 prefix to search
        :return: Date partition within s3 prefix
        """
        date_pattern = r'\d{4}-\d{2}-\d{2}'
        match = re.search(f'{self.partition_name}=({date_pattern})/?', prefix)
        if not match:
            raise PartitionDoesNotExist(self.partition_name, prefix)
        return match.group(1)
