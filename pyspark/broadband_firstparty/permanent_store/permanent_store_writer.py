"""Permanent Store Writer Class Adapted from relay-data-jobs"""
import os
from pandas.core.frame import DataFrame
from datetime import date

from broadband_firstparty.permanent_store.permanent_store import PermanentStore


class PermanentStoreWriter(PermanentStore):
    """Permanent Store Writer Class"""

    def __init__(self, partition_name: str, logger):
        """
        Permanent Store Writer Constructor

        :param partition_name: partition name to be written to s3
        :param logger: shared logger
        """
        super().__init__(partition_name)
        self.logger = logger

    def save_with_new_version(self, data_frame: DataFrame, s3_path: str):
        """
        Method to save input data_frame to s3 with an incremented version number

        :param data_frame: Dataframe to be written
        :param s3_path: S3 path to write data_frame to
        :return: None
        """
        new_version = self._find_next_version(s3_path)
        self.logger.info(f'Next data version for {s3_path} will be: {new_version}.')
        dest_path = os.path.join(s3_path, f'version={new_version}')
        self.logger.info(f'Copying dataframe to {dest_path}.')
        self.s3_parquet_util.write_dataset(df=data_frame, s3_dest_path=dest_path, partition_cols=[self.partition_name])
        self.logger.info(f'Successfully copied dataframe to {dest_path}')

    def save_with_new_revision(self, data_frame: DataFrame, s3_path: str, output_date_partition: date):
        """
        Method for writing to s3 with a new revision

        :param data_frame: DataFrame to be written
        :param s3_path: Path to write DataFrame to
        :param output_date_partition: Date of output to find revision for
        :return:
        """
        path = s3_path.rstrip('/')
        new_revision = self._find_next_revision(s3_path, output_date_partition)
        self.logger.info(f'Next data revision for date {output_date_partition} in path {s3_path} will be {new_revision}'
                         )
        data_frame[self.revision_name] = new_revision
        dest_path = os.path.join(path, f'{self.partition_name}={output_date_partition}', f'{self.revision_name}='
                                                                                         f'{new_revision}')
        self.logger.info(f'Copying dataframe to {dest_path}')
        self.s3_parquet_util.write_dataset(df=data_frame, s3_dest_path=path, partition_cols=[self.partition_name,
                                                                                             self.revision_name])
        self.logger.info(f'Successfully copied dataframe to {dest_path}')

    def save_with_new_revision_date(self, data_frame: DataFrame, s3_path: str, output_date_partition: date,
                                    output_revision_partition: date):
        """
        Method for writing to S3 for a given Dataframe, date partition, and revision

        :param data_frame: DataFrame to be written
        :param s3_path: Base S3 path for DataFrame to be written to
        :param output_date_partition: date for Dataframe to be written with
        :param output_revision_partition: revision in S3 for given Dataframe
        :return: None
        """
        path = s3_path.rstrip('/')
        new_revision = output_revision_partition
        self.logger.info(f'Next data revision for date {output_date_partition} in path {s3_path} will be '
                         f'{new_revision}')
        data_frame[self.revision_date_name] = new_revision
        dest_path = os.path.join(path, f'{self.partition_name}={output_date_partition}',
                                 f'{self.revision_date_name}={new_revision}')
        self.logger.info(f'Copying dataframe to {dest_path}')
        self.s3_parquet_util.write_dataset(df=data_frame, s3_dest_path=path,
                                           partition_cols=[self.partition_name, self.revision_date_name])
        self.logger.info(f'Successfully copied dataframe to {dest_path}')
