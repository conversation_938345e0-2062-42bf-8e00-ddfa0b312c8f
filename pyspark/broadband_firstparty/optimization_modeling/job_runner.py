"""contains Job runner class and associated types"""

import logging
import warnings
from datetime import datetime
from pandas.core.frame import <PERSON>Frame
from sys import exit

from broadband_firstparty.optimization_modeling.optimization_model import OptimizationModel
from broadband_firstparty.permanent_store.permanent_store_reader import PermanentStoreReader
from broadband_firstparty.permanent_store.permanent_store_writer import PermanentStoreWriter
from broadband_firstparty.utils.project_scope_features import ProjectScopeFeatures
from broadband_firstparty.utils.util import is_s3_path_valid, validate_date_range


# These warnings will be printed hundreds of times every second. Filter them out to make logs readable
warnings.filterwarnings("ignore", message="delta_grad == 0.0. Check if the approximated function is linear.")
warnings.filterwarnings("ignore",
                        message="Singular Jacobian matrix. Using SVD decomposition to perform the factorizations.")


class JobRunner(ProjectScopeFeatures):
    """Job Runner Class for running Optimaztion job"""

    def __init__(self, job_name: str, logger: logging.Logger, s3_src_path: str,
                 s3_results_path: str, s3_zero_volume_path: str, s3_unsolved_path: str,
                 force_run: bool, look_back_months: int = None, fiscal_month_period: dict = None,
                 processing_date: str = None, fiscal_month_last_date=21):
        """
        Constructor for Job Runner Class

        :param job_name: Name of Job being run
        :param logger: logger to output to
        :param s3_src_path: input s3 path
        :param s3_results_path: output solved s3 path
        :param s3_zero_volume_path: output zero_volume path
        :param s3_unsolved_path: output unsolved path
        :param force_run: Force run flag
        :param look_back_months: How many months to run for
        :param fiscal_month_period: dict of fiscal months
        :param processing_date: date processing is being run
        :param fiscal_month_last_date: last day of fiscal month
        """
        super().__init__(job_name, look_back_months, fiscal_month_period, fiscal_month_last_date)

        is_s3_path_valid(s3_results_path)
        is_s3_path_valid(s3_zero_volume_path)
        is_s3_path_valid(s3_unsolved_path)
        self.job_name = job_name
        self.logger = logger
        self.s3_src_path = s3_src_path
        self.s3_results_path = s3_results_path
        self.s3_zero_volume_path = s3_zero_volume_path
        self.s3_unsolved_path = s3_unsolved_path
        self.force_run = force_run
        self.results = []
        self.zero_volume_dma_list = []
        self.unresolved_dma_list = []
        self.revision_date = datetime.strptime(processing_date,
                                               '%Y-%m-%d').date() if processing_date is not None else None

    def run_job(self):
        """
        Driver code for running optimization job with job runner

        :return: None
        """
        try:
            self.logger.info('Starting Optimization Process.')

            if not self.force_run and self.has_current_fiscal_month_already_been_processed(self.s3_results_path):
                self.logger.warning(f'Current fiscal month already exists in {self.s3_results_path}')
                return

            fiscal_month_date_range = self.get_fiscal_month_date_range()
            validate_date_range(fiscal_month_date_range)
            start_date = fiscal_month_date_range[0]
            end_date = fiscal_month_date_range[-1]
            input_revision = self.revision_date
            output_revision = self.revision_date if self.revision_date is not None else self._current_revision_date

            self.logger.info(f'Processing start date: {start_date}.')
            self.logger.info(f'Processing end date: {end_date}.')

            self.logger.info(f'Reading data from {self.s3_src_path}.')
            non_mover_wins_per_loss = PermanentStoreReader(
                self.partition_name, self.logger).read_from_path_with_revision(self.s3_src_path, start_date, end_date,
                                                                               input_revision)
            self.logger.info(f'Read {len(non_mover_wins_per_loss)} records from {self.s3_src_path}.')

            for fiscal_month_end_date in fiscal_month_date_range:
                self.logger.info(f'Starting optimization model job for fiscal month `{fiscal_month_end_date}`.')
                fiscal_month_end_date_str = fiscal_month_end_date.strftime('%Y-%m-%d')
                input_data = non_mover_wins_per_loss.loc[
                    non_mover_wins_per_loss[self.partition_name] == fiscal_month_end_date_str].copy(deep=True)

                if input_data.empty:
                    self.logger.warning(f'No data to process for `{fiscal_month_end_date}`.')
                    continue

                results, zero_volume, unresolved = OptimizationModel(
                    job_name=self.job_name,
                    input_data=input_data,
                    fiscal_month_end_date=fiscal_month_end_date_str).main()

                permanent_store_writer = PermanentStoreWriter(self.partition_name, self.logger)
                if type(results) == DataFrame and not results.empty:
                    results[self.partition_name] = fiscal_month_end_date
                    permanent_store_writer.save_with_new_revision_date(results, self.s3_results_path,
                                                                       fiscal_month_end_date, output_revision)
                    self.results.append(results)
                if type(zero_volume) == DataFrame and not zero_volume.empty:
                    zero_volume[self.partition_name] = fiscal_month_end_date
                    permanent_store_writer.save_with_new_revision_date(zero_volume, self.s3_zero_volume_path,
                                                                       fiscal_month_end_date, output_revision)
                    self.zero_volume_dma_list.append(zero_volume)
                if type(unresolved) == DataFrame and not unresolved.empty:
                    unresolved[self.partition_name] = fiscal_month_end_date
                    permanent_store_writer.save_with_new_revision_date(unresolved, self.s3_unsolved_path,
                                                                       fiscal_month_end_date, output_revision)
                    self.unresolved_dma_list.append(unresolved)

                self.logger.info(f'Finished optimization model job for fiscal month `{fiscal_month_end_date}`.')

                # Used to write the whole table at once. For revisioned data, it's easier to write each date's partition
                # separately.
                # permanent_store_writer = PermanentStoreWriter(self.partition_name, self.logger)
                # if self.results:
                #     results_df = pd.concat(self.results)
                #     permanent_store_writer.save_with_new_revision(results_df,
                #       self.s3_results_path, fiscal_month_end_date)
                #
                # if self.zero_volume_dma_list:
                #     zero_volume_df = pd.concat(self.zero_volume_dma_list)
                #     permanent_store_writer.save_with_new_version(zero_volume_df, self.s3_zero_volume_path)
                #
                # if self.unresolved_dma_list:
                #     unresolved_df = pd.concat(self.unresolved_dma_list)
                #     permanent_store_writer.save_with_new_version(unresolved_df, self.s3_unsolved_path)

                self.logger.info('Exiting Optimization Process.')
        except KeyboardInterrupt:
            self.logger.info('Job was terminated by user request.')
            exit(1)
        except Exception as _exception:
            self.logger.error(f'Optimization process job returned an exception: {_exception}')
            exit(1)
