"""Class with driver code for running optimization models"""

import pandas as pd
from pandas.core.frame import <PERSON><PERSON>rame
from multiprocessing import Pool
import multiprocessing
import copy
import json

from broadband_firstparty.optimization_modeling.model_solution import BaselineModel
from broadband_firstparty.optimization_modeling.model_solution import RelaxedModelFirstStep
from broadband_firstparty.optimization_modeling.model_solution import RelaxedModelSecondStep
from broadband_firstparty.utils.logging import Logger


def is_invalid(df_dma: DataFrame) -> bool:
    """
    Ensure that input data has no zero values

    :param df_dma: data to validate
    :return: True if given data is invalid. False otherwise
    """
    return ((df_dma.adjusted_non_mover_losses.values[0] == 0)
            or (df_dma.adjusted_non_mover_wins.values[0] == 0)
            or (len(df_dma[df_dma.adjusted_non_mover_losses.isna()]) > 0)
            or (len(df_dma[df_dma.adjusted_non_mover_wins.isna()]) > 0)
            or len(df_dma) == 0)


def apply_baseline_model(df_dma: DataFrame, dma_name: str) -> dict:
    """
    Create and solve Baseline Model for a dma

    :param df_dma: Input data for the dma
    :param dma_name: name of the dma
    :return: Outcome, response and dma_name from the model solution attempt
    """
    return BaselineModel(data=df_dma, dma_name=dma_name).solve_model()


def apply_relaxed_model_first_step(df_dma: DataFrame, dma_name: str) -> dict:
    """
    Create and Solve First Relaxed Model for a dma

    :param df_dma: Input data for the dma
    :param dma_name: Name of the Dma
    :return: Outcome, response, dma_name from the model solution attempt
    """
    return RelaxedModelFirstStep(data=df_dma, dma_name=dma_name).solve_model()


def apply_relaxed_model_second_step(df_dma: DataFrame, dma_name: str) -> dict:
    """
    Create and Solve Second Relaxed Model for a dma

    :param df_dma: Input data for the dma
    :param dma_name: Name of the dma
    :return: Outcome, response, dma_name from the model solution attempt
    """
    return RelaxedModelSecondStep(data=df_dma, dma_name=dma_name).solve_model()


class OptimizationModel:
    """Class for modeling an optimization attempt run"""

    def __init__(self, job_name: str, input_data: DataFrame, fiscal_month_end_date: str):
        """
        Constructor for Optimization Model Class

        :param job_name: Name of Optimization job
        :param input_data: Input data to run the optimization job on
        :param fiscal_month_end_date: end day of fiscal month
        """
        self.input_data = input_data
        self.fiscal_month_end_date = fiscal_month_end_date
        self.logger = Logger.get_existing_logger(job_name=job_name)
        self.unique_dma_list = sorted(self.input_data.dma_name.unique())
        self.zeroes = []
        self.unsolved = []
        self.final_unsolved = []
        self.outcome = {}

    def main(self) -> tuple:
        """
        Main driver code of Optimization Model

        :return: results, zero_volume, unsolved dmas
        """
        try:
            self.run_baseline_model_solution_attempt()
            if len(self.unsolved) > 0:
                self.address_unsolved_dmas()

            results_df = self.prepare_output_dataframe()

            zero_volume_df = None
            if len(self.zeroes) > 0:
                zero_volume_df = pd.DataFrame(self.zeroes, columns=['dma'])

            unresolved_df = None
            if len(self.final_unsolved) > 0:
                unresolved_df = pd.DataFrame(self.final_unsolved, columns=['dma'])

            return results_df, zero_volume_df, unresolved_df
        except KeyboardInterrupt:
            exit_msg = 'User interrupted program, all workers have been terminated.'
            raise Exception(exit_msg)

    def run_baseline_model_solution_attempt(self):
        """
        Driver code to run the baseline model solution

        :return: None
        """
        with Pool(processes=multiprocessing.cpu_count() - 1) as pool:
            pool_results = []

            self.logger.info(f'{self.fiscal_month_end_date}: {len(self.unique_dma_list)} unique DMAs to process.')
            for dma in self.unique_dma_list:
                df_dma = self.input_data[self.input_data.dma_name == dma].copy(deep=True)

                if is_invalid(df_dma):
                    self.logger.warning(f'Zero value not allowed - skipping for `{dma}`.')
                    self.zeroes.append(dma)
                else:
                    self.logger.info(f'{self.fiscal_month_end_date}: adding asynchronous work '
                                     f'to apply baseline model for `{dma}`')
                    pool_results.append(pool.apply_async(apply_baseline_model, [df_dma, dma]))
            for pool_result in pool_results:
                optimization_model_output = pool_result.get()

                if optimization_model_output is not None:
                    res = optimization_model_output['res']
                    outcome = optimization_model_output['outcome']
                    dma_name = optimization_model_output['dma_name']
                    self.outcome.update(copy.deepcopy(outcome))
                    self.logger.debug(f'{self.fiscal_month_end_date}: `{dma_name}`, res={res} \n {json.dumps(outcome)}')
                    if res != 0:
                        self.logger.warning(f'{self.fiscal_month_end_date}: constraint out-of-tolerance '
                                            f'from baseline model for `{dma_name}`: {res}')
                        self.unsolved.append((dma_name, 'constraintsViolation'))

        self.logger.info(f'{self.fiscal_month_end_date}: {len(self.outcome.keys())} results in baseline model outcome.')

    def address_unsolved_dmas(self):
        """
        Driver code to address dmas that were unsolved with baseline model

        :return: None
        """
        # STEP 1: FIRST RELAXATION
        self.logger.info(f'{self.fiscal_month_end_date}: beginning secondary model solution attempt '
                         f'with constraints relaxation.')
        unsolved_1_step = []
        with Pool(processes=multiprocessing.cpu_count() - 1) as pool:
            pool_results = []

            for dma_tup in self.unsolved:
                dma_name = dma_tup[0]
                df_dma = self.input_data[self.input_data.dma_name == dma_name].copy(deep=True)
                self.logger.info(f'{self.fiscal_month_end_date}: adding asynchronous work '
                                 f'to apply relaxed model 1-step for `{dma_name}`')
                pool_results.append(pool.apply_async(apply_relaxed_model_first_step, [df_dma, dma_name]))

            for pool_result in pool_results:
                optimization_model_output = pool_result.get()
                self.logger.debug(optimization_model_output)
                if optimization_model_output is not None:
                    res = optimization_model_output['res']
                    outcome = optimization_model_output['outcome']
                    dma_name = optimization_model_output['dma_name']
                    self.outcome.update(copy.deepcopy(outcome))
                    self.logger.debug(f'{self.fiscal_month_end_date}: `{dma_name}` res={res} {json.dumps(outcome)}')
                    if res != 0:
                        self.logger.warning(f'{self.fiscal_month_end_date}: constraint out-of-tolerance '
                                            f'from relaxed 1-step model for `{dma_name}`: {res}')
                        unsolved_1_step.append((dma_name, 'constraintsViolation'))

        self.logger.info(f'{self.fiscal_month_end_date}: {len(unsolved_1_step)} DMAs remaining unsolved '
                         f'after 1-step relaxation.')

        if len(unsolved_1_step) > 0:
            # STEP 2: SECOND RELAXATION
            self.logger.info(f'{self.fiscal_month_end_date}: beginning tertiary model solution attempt '
                             f'with constraints relaxation.')
            unsolved_2_step = []
            with Pool(processes=multiprocessing.cpu_count() - 1) as pool:
                pool_results = []

                for dma_tup in unsolved_1_step:
                    dma = dma_tup[0]
                    df_dma = self.input_data[self.input_data.dma_name == dma].copy(deep=True)
                    self.logger.info(f'{self.fiscal_month_end_date}: applying relaxed model 2-step for `{dma}`')
                    pool_results.append(pool.apply_async(apply_relaxed_model_second_step, [df_dma, dma]))

                for pool_result in pool_results:
                    optimization_model_output = pool_result.get()
                    self.logger.debug(optimization_model_output)
                    if optimization_model_output is not None:
                        res = optimization_model_output['res']
                        outcome = optimization_model_output['outcome']
                        dma_name = optimization_model_output['dma_name']
                        self.outcome.update(copy.deepcopy(outcome))
                        self.logger.debug(f'{self.fiscal_month_end_date}:`{dma_name}` res={res} {json.dumps(outcome)}')
                        if res != 0:
                            self.logger.warning(f'{self.fiscal_month_end_date}: constraint out-of-tolerance '
                                                f'from relaxed 2-step model for `{dma_name}`: {res}')
                            unsolved_2_step.append((dma_name, 'constraintsViolation'))

            self.logger.info(f'{self.fiscal_month_end_date}: {len(unsolved_2_step)} DMAs remaining unsolved '
                             f'after 2-step relaxation.')

            self.final_unsolved = [item[0] for item in unsolved_2_step]
        else:
            self.final_unsolved = [item[0] for item in unsolved_1_step]

    def prepare_output_dataframe(self):
        """
        Prepare the output into desired structure

        :return: the formatted output dataframe
        """
        for dma in self.zeroes:
            zero_df = self.input_data[self.input_data.dma_name.isin(self.zeroes)].copy(deep=True)
            self.outcome[dma] = {
                'dma': dma,
                'adjusted_non_mover_losses': zero_df.adjusted_non_mover_losses.values[0],
                'final_non_mover_losses': None,
                'adjusted_mover_losses': zero_df.adjusted_mover_losses.values[0],
                'final_mover_losses': None,
                'adjusted_cord_cutters': zero_df.adjusted_cord_cutters.values[0],
                'final_cord_cutters': None,
                'adjusted_losses': zero_df.adjusted_losses.values[0],
                'final_total_losses': None,
                'adjusted_non_mover_wins': zero_df.adjusted_non_mover_wins.values[0],
                'final_non_mover_wins': None,
                'adjusted_mover_wins': zero_df.adjusted_mover_wins.values[0],
                'final_mover_wins': None,
                'adjusted_cord_attachers': zero_df.adjusted_cord_attachers.values[0],
                'final_cord_attachers': None,
                'adjusted_wins': zero_df.adjusted_wins.values[0],
                'final_total_wins': None,
                'raw_non_mover_wins_per_loss': zero_df.raw_non_mover_wins_per_loss.values[0],
                'final_non_mover_wins_per_loss': None,
                'relax_step': 'zero_volume',
                'fiscal_month_end_date': self.fiscal_month_end_date
            }

        for dma in self.outcome.keys():
            if dma not in self.zeroes:
                self.outcome[dma]['final_non_mover_wins_per_loss'] = \
                    self.outcome[dma]['final_non_mover_wins'] / self.outcome[dma]['final_non_mover_losses']

        res = pd.DataFrame(self.outcome)
        res = res.transpose()
        res['dma'] = res.index.values
        res.reset_index(drop=True)

        cols = ['dma', 'adjusted_non_mover_losses', 'final_non_mover_losses', 'adjusted_mover_losses',
                'final_mover_losses', 'adjusted_cord_cutters', 'final_cord_cutters', 'adjusted_losses',
                'final_total_losses', 'adjusted_non_mover_wins', 'final_non_mover_wins', 'adjusted_mover_wins',
                'final_mover_wins', 'adjusted_cord_attachers', 'final_cord_attachers', 'adjusted_wins',
                'final_total_wins', 'raw_non_mover_wins_per_loss', 'final_non_mover_wins_per_loss',
                'relax_step']
        res = res[cols]
        res.rename({'adjusted_wins': 'provider_wins', 'adjusted_losses': 'provider_losses', 'dma': 'dma_name'},
                   axis=1, inplace=True)
        return res
