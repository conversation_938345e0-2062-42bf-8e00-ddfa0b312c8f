"""Class containing math & associated structures for solving optimization problems"""
from pandas.core.frame import Data<PERSON>rame
from scipy.optimize import minimize

from broadband_firstparty.optimization_modeling.constraints import baseline_constraints
from broadband_firstparty.optimization_modeling.constraints import constraints_relaxed_1step
from broadband_firstparty.optimization_modeling.constraints import constraints_relaxed_2step


class ModelSolution:
    """Base ModelSolution Class"""

    def __init__(self, data: DataFrame, dma_name: str, constraints: dict):
        """
        Constructor for Base Model Solution Class

        :param data: data from the given dma of this model
        :param dma_name: name of dma this model is for
        :param constraints: constraints to be observed in optimization
        """
        self.columns_to_swap = [
            'adjusted_non_mover_losses',
            'adjusted_mover_losses',
            'adjusted_cord_cutters',
            'adjusted_losses',
            'adjusted_non_mover_wins',
            'adjusted_mover_wins',
            'adjusted_cord_attachers',
            'adjusted_wins',
            'raw_non_mover_wins_per_loss',
            'raw_mover_wins_per_loss'
        ]
        self.data = data[self.columns_to_swap].transpose()
        self.data.columns = ['actual']
        self.data['guess'] = 1
        self.outcome = {}
        self.dma_name = dma_name
        self.constraints = constraints

    def c1_cut_lb(self, x):
        """
        Cutters lower bound

        :param x: input data
        :return: cutters lower bound
        """
        losses = self.data['actual']['adjusted_losses']
        calc_ml = self.data['actual']['adjusted_mover_losses'] / self.data['actual']['adjusted_non_mover_losses'] * x[0]
        calc_cut = self.data['actual']['adjusted_losses'] - (calc_ml + x[0])
        cutter_pct_lower_bound = self.constraints['CUTTER_PCT_LOWER_BOUND']
        # cutters over losses minus minimum must be non-negative
        return (calc_cut / losses) - cutter_pct_lower_bound

    def c2_cut_ub(self, x):
        """
        Cutters Upper Bound

        :param x: input data
        :return: Cutters upper bound
        """
        losses = self.data['actual']['adjusted_losses']
        calc_ml = self.data['actual']['adjusted_mover_losses'] / self.data['actual']['adjusted_non_mover_losses'] * x[0]
        calc_cut = self.data['actual']['adjusted_losses'] - (calc_ml + x[0])
        cutter_pct_upper_bound = self.constraints['CUTTER_PCT_UPPER_BOUND']
        # maximum minus cutters over losses must be non-negative
        return cutter_pct_upper_bound - (calc_cut / losses)

    def c3_att_lb(self, x):
        """
        Attachers lower bound

        :param x: input data
        :return: Attachers lower bound
        """
        wins = self.data['actual']['adjusted_wins']
        calc_mw = self.data['actual']['adjusted_mover_wins'] / self.data['actual']['adjusted_non_mover_wins'] * x[1]
        calc_att = self.data['actual']['adjusted_wins'] - (calc_mw + x[1])
        attacher_pct_lower_bound = self.constraints['ATTACHER_PCT_LOWER_BOUND']
        # attachers over wins minus minimum must be non-negative
        return (calc_att / wins) - attacher_pct_lower_bound

    def c4_att_ub(self, x):
        """
        Attachers Upper bound

        :param x: input data
        :return: Attachers upper bound
        """
        wins = self.data['actual']['adjusted_wins']
        calc_mw = self.data['actual']['adjusted_mover_wins'] / self.data['actual']['adjusted_non_mover_wins'] * x[1]
        calc_att = self.data['actual']['adjusted_wins'] - (calc_mw + x[1])
        attacher_pct_upper_bound = self.constraints['ATTACHER_PCT_UPPER_BOUND']
        # maximum minus attachers over wins must be non-negative
        return attacher_pct_upper_bound - (calc_att / wins)

    def c5_min_ratio(self, x):
        """
        Minimum Ratio

        :param x: input data
        :return: Minimum ratio
        """
        target_ratio = self.data['actual']['raw_non_mover_wins_per_loss']
        ratio_of_ratios_minimum = self.constraints['RATIO_OF_RATIOS_MINIMUM']
        # ratio of ratios (NMW / NML) minus minimum
        return (((x[1] / x[0]) / target_ratio) - ratio_of_ratios_minimum) * 1

    def c6_max_ratio(self, x):
        """
        Maximum Ratio

        :param x: Input data
        :return: Maximum Ratio
        """
        target_ratio = self.data['actual']['raw_non_mover_wins_per_loss']
        ratio_of_ratios_maximum = self.constraints['RATIO_OF_RATIOS_MAXIMUM']
        # maximum minus ratio of ratios
        return (ratio_of_ratios_maximum - ((x[1] / x[0]) / target_ratio)) * 1

    def c7_cut_att_ratio(self, x):
        """
        Cutter to Attacher ratio

        :param x: input data
        :return: Cutter to Attacher Ratio
        """
        calc_ml = self.data['actual']['adjusted_mover_losses'] / self.data['actual'][
            'adjusted_non_mover_losses'] * x[0]
        calc_mw = self.data['actual']['adjusted_mover_wins'] / self.data['actual']['adjusted_non_mover_wins'] * x[1]
        calc_att = self.data['actual']['adjusted_wins'] - (calc_mw + x[1])
        calc_cut = self.data['actual']['adjusted_losses'] - (calc_ml + x[0])
        # attacher over cutter minus 1 must be non-negative
        return (calc_att / calc_cut) - 1

    def c8_loss_total(self, x):
        """
        Loss total

        :param x: input data
        :return: Loss total
        """
        losses = self.data['actual']['adjusted_losses']
        calc_ml = self.data['actual']['adjusted_mover_losses'] / self.data['actual'][
            'adjusted_non_mover_losses'] * x[0]
        calc_cut = self.data['actual']['adjusted_losses'] - (calc_ml + x[0])
        return 0.001 - (x[0] + calc_ml + calc_cut - losses)

    def c9_win_total(self, x):
        """
        Win total

        :param x: input data
        :return: win total
        """
        wins = self.data['actual']['adjusted_wins']
        calc_mw = self.data['actual']['adjusted_mover_wins'] / self.data['actual']['adjusted_non_mover_wins'] * x[1]
        calc_att = self.data['actual']['adjusted_wins'] - (calc_mw + x[1])
        return 0.001 - (x[1] + calc_mw + calc_att - wins)

    def objective(self, x):
        """
        Objective function to be optimized

        :param x: Input data where x[0] is Non-mover losses and x[1] is non-mover wins
        :return: updates based on the input data
        """
        calc_ml = self.data['actual']['adjusted_mover_losses'] / self.data['actual'][
            'adjusted_non_mover_losses'] * x[0]
        calc_cut = self.data['actual']['adjusted_losses'] - (calc_ml + x[0])
        calc_mw = self.data['actual']['adjusted_mover_wins'] / self.data['actual']['adjusted_non_mover_wins'] * x[1]
        calc_att = self.data['actual']['adjusted_wins'] - (calc_mw + x[1])

        initials = self.data['actual'][
            ['adjusted_non_mover_losses',
             'adjusted_mover_losses',
             'adjusted_cord_cutters',
             'adjusted_non_mover_wins',
             'adjusted_mover_wins',
             'adjusted_cord_attachers'
             ]
        ].values

        finals = [x[0], calc_ml, calc_cut, x[1], calc_mw, calc_att]

        return sum([(item[1] - item[0]) ** 2 for item in list(zip(initials, finals))])

    def solve_model(self):
        """
        Driver function for solving the model

        :return: Response, outcome and name for given dma_name
        """
        """
        accept only two decision variables
        Solver:
        https://docs.scipy.org/doc/scipy/reference/optimize.minimize-trustconstr.html#optimize-minimize-trustconstr
        """
        x0 = self.data.guess.values
        constraints = ([
            {'type': 'ineq', 'fun': self.c1_cut_lb},
            {'type': 'ineq', 'fun': self.c2_cut_ub},
            {'type': 'ineq', 'fun': self.c3_att_lb},
            {'type': 'ineq', 'fun': self.c4_att_ub},
            {'type': 'ineq', 'fun': self.c5_min_ratio},
            {'type': 'ineq', 'fun': self.c6_max_ratio},
            {'type': 'ineq', 'fun': self.c8_loss_total},
            {'type': 'ineq', 'fun': self.c9_win_total}
        ])

        solution = minimize(self.objective, x0, constraints=constraints, method='trust-constr', options={'verbose': 0})
        x = solution.x

        if (self.c5_min_ratio(x) < 0.0) or (self.c6_max_ratio(x) < 0.0):
            return {'outcome': self.outcome, 'res': solution.constr_violation, 'dma_name': self.dma_name}
        elif (self.c1_cut_lb(x) < 0.0) \
                or (self.c2_cut_ub(x) < 0.0) \
                or (self.c3_att_lb(x) < 0.0) \
                or (self.c4_att_ub(x) < 0.0):
            return {'outcome': self.outcome, 'res': solution.constr_violation, 'dma_name': self.dma_name}
        else:
            self.update_outcome(x=x)
            return {'outcome': self.outcome, 'res': 0, 'dma_name': self.dma_name}

    def update_outcome(self, x):
        """
        Update outcome data based on given data

        :param x: data to update based on
        :return: None
        """
        calc_ml = self.data['actual']['adjusted_mover_losses'] / self.data['actual']['adjusted_non_mover_losses'] * x[0]
        calc_cut = self.data['actual']['adjusted_losses'] - (calc_ml + x[0])
        calc_mw = self.data['actual']['adjusted_mover_wins'] / self.data['actual']['adjusted_non_mover_wins'] * x[1]
        calc_att = self.data['actual']['adjusted_wins'] - (calc_mw + x[1])
        calc_total_losses = x[0] + calc_ml + calc_cut
        calc_total_wins = x[1] + calc_mw + calc_att
        relaxation = "0"

        self.outcome[self.dma_name] = {
            'final_non_mover_losses': x[0],
            'adjusted_non_mover_losses': self.data['actual']['adjusted_non_mover_losses'],
            'final_mover_losses': calc_ml,
            'final_cord_cutters': calc_cut,
            'final_total_losses': calc_total_losses,
            'final_non_mover_wins': x[1],
            'final_mover_wins': calc_mw,
            'final_cord_attachers': calc_att,
            'final_total_wins': calc_total_wins,
            'adjusted_mover_losses': self.data['actual']['adjusted_mover_losses'],
            'adjusted_cord_cutters': self.data['actual']['adjusted_cord_cutters'],
            'adjusted_losses': self.data['actual']['adjusted_losses'],
            'adjusted_non_mover_wins': self.data['actual']['adjusted_non_mover_wins'],
            'adjusted_mover_wins': self.data['actual']['adjusted_mover_wins'],
            'adjusted_cord_attachers': self.data['actual']['adjusted_cord_attachers'],
            'adjusted_wins': self.data['actual']['adjusted_wins'],
            'raw_non_mover_wins_per_loss': self.data['actual']['raw_non_mover_wins_per_loss'],
            'raw_mover_wins_per_loss': self.data['actual']['raw_mover_wins_per_loss'],
            'relax_step': relaxation
        }


class BaselineModel(ModelSolution):
    """Class for the baseline model with no relaxations"""

    def __init__(self, data: DataFrame, dma_name: str):
        """
        Constructor for Baseline Model

        :param data: data for the given model
        :param dma_name: name of the dma that corresponds to the data
        """
        super().__init__(data, dma_name, baseline_constraints)


class RelaxedModelFirstStep(ModelSolution):
    """Class for the model with the first constraint relaxation"""

    def __init__(self, data: DataFrame, dma_name: str):
        """
        Constructor for first relaxed model

        :param data: data for the given model
        :param dma_name: name of the dma that corresponds to the data
        """
        super().__init__(data, dma_name, constraints_relaxed_1step)


class RelaxedModelSecondStep(ModelSolution):
    """Class for the model with the second constraints violation"""

    def __init__(self, data: DataFrame, dma_name: str):
        """
        Constructor for second relaxed model

        :param data: data for the given model
        :param dma_name: name of the dma that corresponds to the data
        """
        super().__init__(data, dma_name, constraints_relaxed_2step)
