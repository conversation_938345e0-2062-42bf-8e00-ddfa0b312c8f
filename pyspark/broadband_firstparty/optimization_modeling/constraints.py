"""File with constraints for optimization"""

baseline_constraints = {
    "CUTTER_PCT_LOWER_BOUND": 0.05,
    "CUTTER_PCT_UPPER_BOUND": 0.4,
    "ATTACHER_PCT_LOWER_BOUND": 0.05,
    "ATTACHER_PCT_UPPER_BOUND": 0.5,
    "RATIO_OF_RATIOS_MINIMUM": 0.99,
    "RATIO_OF_RATIOS_MAXIMUM": 1.01
}

constraints_relaxed_1step = {
    "CUTTER_PCT_LOWER_BOUND": 0.05,
    "CUTTER_PCT_UPPER_BOUND": 0.4,
    "ATTACHER_PCT_LOWER_BOUND": 0.05,
    "ATTACHER_PCT_UPPER_BOUND": 0.5,
    "RATIO_OF_RATIOS_MINIMUM": 0.98,
    "RATIO_OF_RATIOS_MAXIMUM": 1.02
}

constraints_relaxed_2step = {
    "CUTTER_PCT_LOWER_BOUND": 0.0,
    "CUTTER_PCT_UPPER_BOUND": 1,
    "ATTACHER_PCT_LOWER_BOUND": 0.0,
    "ATTACHER_PCT_UPPER_BOUND": 1,
    "RATIO_OF_RATIOS_MINIMUM": 0.9,
    "RATIO_OF_RATIOS_MAXIMUM": 1.1
}
