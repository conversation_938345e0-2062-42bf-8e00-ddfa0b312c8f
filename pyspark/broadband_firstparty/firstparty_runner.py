"""Code called by runner.py to run first party pyspark jobs"""
import argparse
import json
from broadband_firstparty.optimization_modeling.job_runner import <PERSON><PERSON><PERSON><PERSON>
from broadband_firstparty.spatial_allocation.job_runner import <PERSON><PERSON><PERSON>ner as SpatialJob<PERSON>unner
from broadband_firstparty.utils.logging import Logger


def run_optimization_job(args):
    """
    Driver code to run an optimization job

    :param args: Additional arguments passed in for the optimization job
    :return: None
    """
    try:

        job_config = args
        write_to_file = job_config['logger_write_to_file'] == 'true'
        logger = Logger(job_name=job_config['job_name'],
                        log_options={"write_to_file": write_to_file,
                                     "level": job_config['logger_level']}).get_logger()
        logger.info(json.dumps(job_config, indent=2))

        start_date = job_config['fiscal_month_period_start_date']
        end_date = job_config['fiscal_month_period_end_date']
        if not start_date or not end_date:
            fiscal_range = None
        else:
            fiscal_range = {"start-date": start_date, 'end-date': end_date}
        force_run = job_config.get('force_run', False) == 'true'
        JobRunner(
            job_name=job_config['job_name'],
            logger=logger,
            s3_src_path=job_config['s3_src_path'],
            s3_results_path=job_config['s3_results_path'],
            s3_zero_volume_path=job_config['s3_zero_volume_path'],
            s3_unsolved_path=job_config['s3_unsolved_path'],
            force_run=force_run,
            look_back_months=job_config['look_back_months'],
            fiscal_month_period=fiscal_range,
            processing_date=job_config['processing_date'],
            fiscal_month_last_date=job_config['fiscal_month_last_date']
        ).run_job()

    except KeyError as key_error:
        print(f'{key_error} not in json config file.')
        exit(1)
    except Exception as exception:
        error_msg = f'Failed to instantiate job runner: {exception}'
        print(error_msg)
        exit(1)


def run_spatial_allocation(args):
    """
    Driver code to run spatial allocation

    :param args: Additonal Arguments passed for spatial allocation
    :return: None
    """
    try:

        job_config = args
        write_to_file = job_config['logger_write_to_file'] == 'true'
        logger = Logger(job_name=job_config['job_name'],
                        log_options={"write_to_file": write_to_file,
                                     "level": job_config['logger_level']}).get_logger()
        logger.info(json.dumps(job_config, indent=2))

        start_date = job_config['fiscal_month_period_start_date']
        end_date = job_config['fiscal_month_period_end_date']
        if not start_date or not end_date:
            fiscal_range = None
        else:
            fiscal_range = {"start-date": start_date, 'end-date': end_date}
        force_run = job_config.get('force_run', False) == 'true'
        SpatialJobRunner(
            job_name=job_config['job_name'],
            logger=logger,
            s3_cld_connects_path=job_config['s3_cld_connects_path'],
            s3_cld_disconnects_path=job_config['s3_cld_disconnects_path'],
            s3_xray_connects_path=job_config['s3_xray_connects_path'],
            s3_xray_disconnects_path=job_config['s3_xray_disconnects_path'],
            s3_connects_dest_path=job_config['s3_connects_dest_path'],
            s3_disconnects_dest_path=job_config['s3_disconnects_dest_path'],
            force_run=force_run,
            look_back_months=job_config['look_back_months'],
            fiscal_month_period=fiscal_range,
            processing_date=job_config['processing_date']
        ).run_job()
    except KeyError as key_error:
        print(f'{key_error} not in json config file.')
        exit(1)
    except Exception as exception:
        error_msg = f'Failed to instantiate job runner: {exception}'
        print(error_msg)
        exit(1)


def main():
    """
    main function for broadband_firstparty module

    :return: None
    """
    parser = argparse.ArgumentParser()
    parser.add_argument('--job-type', type=str, help='optimization or spatial')
    parser.add_argument('--job-name', type=str, help='Job Name')
    parser.add_argument('--s3-src-path', type=str, help='Input path to optimization job')
    parser.add_argument('--s3-results-path', type=str, help='Results path for optimization job')
    parser.add_argument('--s3-zero-volume-path', type=str, help='Zero volume path for optimization job')
    parser.add_argument('--s3-unsolved-path', type=str, help='Unsolved path for optimization job')
    parser.add_argument('--look-back-months', type=int, help='Look back months optimization job')
    parser.add_argument('--fiscal-month-period-start-date', type=str, help='start of processing range')
    parser.add_argument('--fiscal-month-period-end-date', type=str, help='end of processing range')
    parser.add_argument('--force-run', type=str, help='Force run optimization?')
    parser.add_argument('--processing-date', type=str, help='Optional Processing date')
    parser.add_argument('--logger-write-to-file', type=str, help='Write to file?')
    parser.add_argument('--logger-level', type=str, help='Logger Level')
    parser.add_argument('--fiscal-month-last-date', type=int, help='Fiscal month date', default=21)

    parser.add_argument('--s3-cld-connects-path', type=str, help='Cld Connect location')
    parser.add_argument('--s3-cld-disconnects-path', type=str, help='Cld disconnect location')
    parser.add_argument('--s3-xray-connects-path', type=str, help='Xray connects location')
    parser.add_argument('--s3-xray-disconnects-path', type=str, help='Xray disconnects location')
    parser.add_argument('--s3-connects-dest-path', type=str, help='Connect output location')
    parser.add_argument('--s3-disconnects-dest-path', type=str, help='Disconnect output location')
    args = vars(parser.parse_args())

    if args['job_type'] == 'optimization':
        run_optimization_job(args)
    elif args['job_type'] == 'spatial':
        run_spatial_allocation(args)
    else:
        exit(0)


if __name__ == '__main__':
    main()
