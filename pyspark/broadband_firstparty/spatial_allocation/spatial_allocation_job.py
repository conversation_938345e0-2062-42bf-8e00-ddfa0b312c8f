"""Class outlining a Spatial Allocation Job"""
import multiprocessing
import os
import pandas as pd
from pandas.core.frame import <PERSON><PERSON>rame

from broadband_firstparty.spatial_allocation import FileType, SharedListIndex
from broadband_firstparty.spatial_allocation.dma_allocation_worker import DmaAllocationWorker
from broadband_firstparty.utils.logging import Logger


class SpatialAllocationJob:
    """Spatial Allocation Job Class"""

    def __init__(self,
                 job_name: str,
                 fiscal_month_end_date: str,
                 cld_connects: DataFrame,
                 cld_disconnects: DataFrame,
                 xray_connects: DataFrame,
                 xray_disconnects: DataFrame,
                 logger: Logger):
        """
        Constructor for Spatial Allocation Job

        :param job_name: job name
        :param fiscal_month_end_date: date (number) of fiscal month end
        :param cld_connects: Syndicated connects
        :param cld_disconnects: Syndicated Disconnects
        :param xray_connects: Client Connects
        :param xray_disconnects: Client Disconnects
        :param logger: logger
        """
        self.job_name = job_name
        self.logger = logger
        self.fiscal_month_end_date = fiscal_month_end_date
        self.worker_count = os.cpu_count() - 1
        self.workers = []
        self.cld_connects = cld_connects
        self.cld_disconnects = cld_disconnects
        self.xray_connects = xray_connects
        self.xray_disconnects = xray_disconnects

    def main(self):
        """
        Main function of Spatial Allocation Job. Manages Multiprocessing

        :return: None
        """
        try:
            # init manager to share dictionary between workers
            manager = multiprocessing.Manager()
            result_dict = manager.dict({
                str(FileType.CONNECTS): manager.list(),
                str(FileType.DISCONNECTS): manager.list()
            })

            # init all workers
            self.init_workers(result_dict=result_dict)

            # wait for all workers to complete
            [worker.join() for worker in self.workers]

            # check exit code for every worker
            [self.check_exit_code(worker) for worker in self.workers]

            self.logger.info('Finished running all workers.')
            self.logger.info(f'{self.fiscal_month_end_date}: Number of connects: {len(result_dict["connects"])}')
            self.logger.info(
                f'{self.fiscal_month_end_date}: Number of disconnects: {len(result_dict["disconnects"])}')

            return pd.concat(result_dict[str(FileType.CONNECTS)]), pd.concat(result_dict[str(FileType.DISCONNECTS)])
        except KeyboardInterrupt:
            self.terminate_all_workers()
            exit_msg = 'User interrupted program, all workers have been terminated.'
            raise Exception(exit_msg)

    def get_unique_dma_list(self) -> list:
        """
        Helper to get all unique dmas

        :return: sorted list of unique dmas from connect/disconnect files
        """
        return sorted(list(set(
            list(self.cld_connects.dma_name.unique()) + list(self.cld_disconnects.dma_name.unique())
            + list(self.xray_connects.dma_name.unique()) + list(self.xray_disconnects.dma_name.unique()))))

    def init_workers(self, result_dict: dict):
        """
        Initializes and starts Dma Allocation Workers for Spatial Allocation

        :param result_dict: dict to be modified with worker results
        :return: None
        """
        dma_list = self.get_unique_dma_list()
        resources = {
            'cld_connects': self.cld_connects,
            'cld_disconnects': self.cld_disconnects,
            'xray_connects': self.xray_connects,
            'xray_disconnects': self.xray_disconnects,
            'job_name': self.job_name,
            'dma_list': dma_list
        }
        shared_list_index = SharedListIndex(list_size=len(dma_list))
        self.logger.info(f'{self.worker_count} workers will process {len(dma_list)} DMAs in parallel.')

        for worker_id in range(self.worker_count):
            self.workers.append(DmaAllocationWorker(
                worker_id=worker_id,
                counter=shared_list_index,
                resources=resources,
                result_dict=result_dict,
                logger=self.logger))
            self.logger.info(f'Starting worker #{worker_id}.')
            self.workers[-1].start()

    def check_exit_code(self, worker):
        """
        Check to ensure worker exited with code 0

        :param worker: Worker to be checked
        :return: None
        """
        if worker.exitcode and worker.exitcode != 0:
            self.terminate_all_workers()
            raise Exception(f'Worker #{worker.worker_id} completed with exit code: {worker.exitcode}.')

    def terminate_all_workers(self):
        """
        Terminate all worker processes

        :return: None
        """
        self.logger.info('Terminating all running workers.')
        [worker.terminate() for worker in self.workers]
