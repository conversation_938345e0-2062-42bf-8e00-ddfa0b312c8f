"""Module for Spatial allocation code and job runner"""
from enum import Enum
from multiprocessing import Value, Lock


class FileType(Enum):
    """Enum for file types"""

    CONNECTS = 'connects'
    DISCONNECTS = 'disconnects'

    def __str__(self):
        """Enum value for File type"""
        return str(self.value)


class EndOfListReached(Exception):
    """Exception for End of List"""

    pass


class SharedListIndex:
    """Shared List Index Class"""

    def __init__(self, list_size: int):
        """
        Constructor for Shared List Index Object

        :param list_size: size of the shared list
        """
        self.idx = Value('i', 0)
        self.list_size = list_size
        self.lock = Lock()

    def get_index(self):
        """
        Get current index of the shared list

        :return: current index
        """
        with self.lock:
            idx = self.idx.value
            if idx == self.list_size:
                raise EndOfListReached
            self.idx.value += 1
            return idx
