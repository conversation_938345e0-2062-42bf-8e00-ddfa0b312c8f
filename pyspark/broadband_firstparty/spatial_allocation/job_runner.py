"""Job Runner Class for Spatial ALlocation"""
import logging
import pandas as pd
from pandas.core.frame import DataFrame
import traceback

from broadband_firstparty.permanent_store.permanent_store_reader import PermanentStoreReader
from broadband_firstparty.permanent_store.permanent_store_writer import PermanentStoreWriter
from broadband_firstparty.utils.project_scope_features import ProjectScopeFeatures
from broadband_firstparty.spatial_allocation.spatial_allocation_job import SpatialAllocationJob
from broadband_firstparty.utils.util import is_s3_path_valid, validate_date_range

from datetime import datetime


class JobRunner(ProjectScopeFeatures):
    """Class defining Job Runner for Spatial Allocation"""

    def __init__(self, logger: logging.Logger, job_name: str, s3_cld_connects_path: str, s3_cld_disconnects_path: str,
                 s3_xray_connects_path: str, s3_xray_disconnects_path: str, s3_connects_dest_path: str,
                 s3_disconnects_dest_path: str, force_run: bool, look_back_months: int = None,
                 fiscal_month_period: dict = None,
                 processing_date: str = None):
        """
        Constructor for Spatial Allocation Job Runner

        :param logger: logger
        :param job_name: Job name
        :param s3_cld_connects_path: S3 path to syndicated connects
        :param s3_cld_disconnects_path: S3 path to syndicated disconnects
        :param s3_xray_connects_path: S3 path to client connects
        :param s3_xray_disconnects_path: S3 path to client disconnects
        :param s3_connects_dest_path: S3 path for connects output
        :param s3_disconnects_dest_path: S3 path for disconnects output
        :param force_run: Force run?
        :param look_back_months: Months to look back
        :param fiscal_month_period: Optional range to be processed
        :param processing_date: Optional Processing date
        """
        super().__init__(job_name, look_back_months, fiscal_month_period)

        is_s3_path_valid(s3_cld_connects_path)
        is_s3_path_valid(s3_cld_disconnects_path)
        is_s3_path_valid(s3_xray_connects_path)
        is_s3_path_valid(s3_xray_disconnects_path)
        is_s3_path_valid(s3_connects_dest_path)
        is_s3_path_valid(s3_disconnects_dest_path)

        self.job_name = job_name
        self.logger = logger
        self.s3_cld_connects_path = s3_cld_connects_path
        self.s3_cld_disconnects_path = s3_cld_disconnects_path
        self.s3_xray_connects_path = s3_xray_connects_path
        self.s3_xray_disconnects_path = s3_xray_disconnects_path
        self.s3_connects_dest_path = s3_connects_dest_path
        self.s3_disconnects_dest_path = s3_disconnects_dest_path
        self.force_run = force_run
        self.revision_date = datetime.strptime(processing_date,
                                               '%Y-%m-%d').date() if processing_date is not None else None
        self.xray_raw_columns = ['customer_id', 'latitude', 'longitude', 'census_block_id', 'dma_name',
                                 'inclusion_flag', 'submarket']

    @property
    def cld_connects_columns_mapping(self):
        """
        Property containing mappings for cld connects

        :return: None
        """
        return {
            'winning_census_blockid': 'census_blockid',
            'winning_dma_name': 'dma_name',
            'latitude': 'lat',
            'longitude': 'long'
        }

    @property
    def cld_disconnects_columns_mapping(self):
        """
        Property containing mappings for cld disconnects

        :return: None
        """
        return {
            'losing_census_blockid': 'census_blockid',
            'losing_dma_name': 'dma_name',
            'latitude': 'lat',
            'longitude': 'long'
        }

    @property
    def xray_columns_mapping(self):
        """
        Property containing mappings for client files

        :return: None
        """
        return {
            'latitude': 'lat',
            'longitude': 'long',
            'census_block_id': 'census_blockid'
        }

    def run_job(self):
        """
        Driver code for starting a spatial allocation job

        :return: None
        """
        try:
            self.logger.info('Starting Spatial Allocation.')

            # check if current fiscal month has already been processed
            if (not self.force_run and self.has_current_fiscal_month_already_been_processed(
                    s3_path=self.s3_connects_dest_path)):
                self.logger.info(f'Current fiscal month already exists in {self.s3_connects_dest_path}')
                return

            # get fiscal month date range to process
            fiscal_month_date_range = self.get_fiscal_month_date_range()
            validate_date_range(fiscal_month_date_range)
            self.logger.info(f'Processing start date: {fiscal_month_date_range[0]}.')
            self.logger.info(f'Processing end date: {fiscal_month_date_range[-1]}.')
            output_revision = self.revision_date if self.revision_date is not None else self._current_revision_date

            all_connects = []
            all_disconnects = []

            # run spatial allocation for each fiscal month end date
            for fiscal_month_end_date in fiscal_month_date_range:
                connects, disconnects = self.run_spatial_allocation(fiscal_month_end_date)
                all_connects.append(connects)
                all_disconnects.append(disconnects)

                # write all results to permanent store
                permanent_store_writer = PermanentStoreWriter(self.partition_name, self.logger)
                permanent_store_writer.save_with_new_revision_date(pd.concat(all_connects),
                                                                   self.s3_connects_dest_path,
                                                                   fiscal_month_end_date, output_revision)
                permanent_store_writer.save_with_new_revision_date(pd.concat(all_disconnects),
                                                                   self.s3_disconnects_dest_path,
                                                                   fiscal_month_end_date, output_revision)
                all_connects = []
                all_disconnects = []

            self.logger.info('Exiting Xray Spatial Allocation.')
        except KeyboardInterrupt:
            self.logger.info('Job was terminated by user request.')
            exit(1)
        except Exception as _exception:
            self.logger.error(f'Xray Optimization process job returned an exception: {_exception}')
            traceback.print_exc()
            exit(1)

    def run_spatial_allocation(self, fiscal_month_end_date):
        """
        Load files and create Spatial Allocation Job

        :param fiscal_month_end_date: date (number) of fiscal month end
        :return:
        """
        self.logger.info(f'Starting spatial allocation job for fiscal month `{fiscal_month_end_date}`.')
        permanent_store_reader = PermanentStoreReader(self.partition_name, self.logger)
        input_revision = self.revision_date

        cld_connects = permanent_store_reader.read_from_path_with_revision(s3_path=self.s3_cld_connects_path,
                                                                           start_date=fiscal_month_end_date,
                                                                           end_date=fiscal_month_end_date,
                                                                           revision_date=input_revision)
        self.logger.info(f'{len(cld_connects)} cld connects have been read from s3.')

        cld_disconnects = permanent_store_reader.read_from_path_with_revision(s3_path=self.s3_cld_disconnects_path,
                                                                              start_date=fiscal_month_end_date,
                                                                              end_date=fiscal_month_end_date,
                                                                              revision_date=input_revision)
        self.logger.info(f'{len(cld_disconnects)} cld disconnects have been read from s3.')

        xray_connects = permanent_store_reader.read_from_path_with_revision(s3_path=self.s3_xray_connects_path,
                                                                            start_date=fiscal_month_end_date,
                                                                            end_date=fiscal_month_end_date,
                                                                            revision_date=input_revision,
                                                                            columns=self.xray_raw_columns)
        self.logger.info(f'{len(xray_connects)} xray connects have been read from s3.')

        xray_disconnects = permanent_store_reader.read_from_path_with_revision(s3_path=self.s3_xray_disconnects_path,
                                                                               start_date=fiscal_month_end_date,
                                                                               end_date=fiscal_month_end_date,
                                                                               revision_date=input_revision,
                                                                               columns=self.xray_raw_columns)
        self.logger.info(f'{len(xray_disconnects)} xray disconnects have been read from s3.')

        if cld_connects.empty and cld_disconnects.empty and \
                xray_connects.empty and xray_disconnects.empty:
            self.logger.warning(f'No data to process for `{fiscal_month_end_date}`.')
            return

        xray_connects = self.apply_xray_filters(xray_connects)
        xray_disconnects = self.apply_xray_filters(xray_disconnects)

        xray_connects.rename(self.xray_columns_mapping, axis=1, inplace=True)
        xray_disconnects.rename(self.xray_columns_mapping, axis=1, inplace=True)
        cld_connects.rename(self.cld_connects_columns_mapping, axis=1, inplace=True)
        cld_disconnects.rename(self.cld_disconnects_columns_mapping, axis=1, inplace=True)

        fiscal_month_end_date_str = fiscal_month_end_date.strftime('%Y-%m-%d')

        connects, disconnects = SpatialAllocationJob(
            job_name=self.job_name,
            fiscal_month_end_date=fiscal_month_end_date_str,
            cld_connects=cld_connects,
            cld_disconnects=cld_disconnects,
            xray_connects=xray_connects,
            xray_disconnects=xray_disconnects,
            logger=self.logger).main()

        connects[self.partition_name] = fiscal_month_end_date_str
        disconnects[self.partition_name] = fiscal_month_end_date_str
        self.logger.info(f'Completed spatial allocation for fiscal month {fiscal_month_end_date}.')
        return connects, disconnects

    @staticmethod
    def apply_xray_filters(df) -> DataFrame:
        """
        Filter dataframe based on client filters

        :param df: df to be filtered
        :return:
        """
        return df[(~ df.inclusion_flag) & (~ df.dma_name.isna())].reset_index(drop=True)
