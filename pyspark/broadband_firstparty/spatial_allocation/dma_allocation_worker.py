"""Class for dma allocation workers"""
import random
from multiprocessing import Process

import pandas as pd
import time
import utm
from pandas.core.frame import DataFrame
from scipy.spatial import distance_matrix
import psutil

from broadband_firstparty.spatial_allocation import FileType, EndOfListReached
from broadband_firstparty.spatial_allocation.spatial_allocation_job import SharedListIndex
from broadband_firstparty.utils.logging import Logger


def gcd(a, b):
    """
    Recursive function to return gcd of a and b

    :param a: input 1
    :param b: input 2
    :return: greatest common denominator of a and b
    """
    if a == 0:
        return b
    return gcd(b % a, a)


def compute_lcm(a, b):
    """
    Function to return LCM of two numbers

    :param a: input 1
    :param b: input 2
    :return: least common multiple of a and b
    """
    return (a * b) / gcd(a, b)


class DmaAllocationWorker(Process):
    """Dma Allocation Worker Class"""

    def __init__(self, worker_id: int, counter: SharedListIndex, resources: dict, result_dict: dict, logger: Logger):
        """
        Constructor for Dma Allocation Worker class

        :param worker_id: id number of worker
        :param counter: counter
        :param resources: shared resources to be used
        :param result_dict: results of the process
        """
        super(DmaAllocationWorker, self).__init__()
        self.worker_id = worker_id
        self.shared_list_index = counter
        self.resources = resources
        self.result_dict = result_dict
        self.logger = logger

    def _set_logger(self):
        """
        Get existing logger for the job

        :return: None
        """
        self.logger = Logger.get_existing_logger(job_name=self.resources['job_name'])

    def run(self):
        """
        Driver for dma allocation process

        :return: None
        """
        while True:
            self.logger.info(f'MEMORY USAGE: {psutil.virtual_memory().percent} percent')
            try:
                time.sleep(0.01)
                synchronized_index = self.shared_list_index.get_index()
            except EndOfListReached:
                break
            else:
                self.logger.debug(f'Worker #{self.worker_id} found index {synchronized_index}.')
                dma_name = self.resources['dma_list'][synchronized_index]
                cleaned_dma_name = dma_name.replace(' ', '').replace(',', '').replace('.', '').lower()
                cld_connects = self.resources['cld_connects'].loc[
                    self.resources['cld_connects'].dma_name == dma_name].copy(deep=True)
                xray_connects = self.resources['xray_connects'].loc[
                    self.resources['xray_connects'].dma_name == dma_name].copy(deep=True)

                if len(cld_connects) == 0 or len(xray_connects) == 0:
                    warning_msg = f'Worker #{self.worker_id} cannot process connects for `{cleaned_dma_name}` ' \
                                  f'({len(cld_connects)} CLD, {len(xray_connects)} Xray).'
                    self.logger.warning(warning_msg)
                else:
                    self.logger.info(f'Worker #{self.worker_id} is processing connects for {cleaned_dma_name}.')

                    outcome = self._allocate_dma(
                        file_type=FileType.CONNECTS,
                        cld_df=cld_connects,
                        xray_df=xray_connects,
                        dma_name=cleaned_dma_name)

                    self.result_dict[str(FileType.CONNECTS)].append(outcome)

                del cld_connects
                del xray_connects

                cld_disconnects = self.resources['cld_disconnects'].loc[
                    self.resources['cld_disconnects'].dma_name == dma_name].copy(deep=True)
                xray_disconnects = self.resources['xray_disconnects'].loc[
                    self.resources['xray_disconnects'].dma_name == dma_name].copy(deep=True)

                if len(cld_disconnects) == 0 or len(xray_disconnects) == 0:
                    warning_msg = f'Worker #{self.worker_id} cannot process disconnects for `{cleaned_dma_name}` ' \
                                  f'({len(cld_disconnects)} CLD, {len(xray_disconnects)} Xray).'
                    self.logger.warning(warning_msg)
                else:
                    self.logger.info(f'Worker #{self.worker_id} is processing disconnects for {cleaned_dma_name}.')

                    outcome = self._allocate_dma(
                        file_type=FileType.DISCONNECTS,
                        cld_df=cld_disconnects,
                        xray_df=xray_disconnects,
                        dma_name=cleaned_dma_name)

                    self.result_dict[str(FileType.DISCONNECTS)].append(outcome)

                del cld_disconnects
                del xray_disconnects

        self.logger.info(f'Worker #{self.worker_id} is finishing.')

    def _allocate_dma(self, file_type: str, cld_df: DataFrame, xray_df: DataFrame, dma_name: str) -> DataFrame:
        """
        Driver to execute allocation for a specific dma

        :param file_type: `connects` or `disconnects`
        :param cld_df: CLD connects or disconnects as data frame
        :param xray_df: COM connects or disconnects as data frame
        :param dma_name:dma name
        """
        cld_df = cld_df.groupby(['ifa', 'lat', 'long', 'census_blockid', 'submarket'], as_index=False).agg({
            'multiplier': 'sum'})

        xray_df['census_blockid'] = xray_df['census_blockid'].astype(str)
        cld_df['census_blockid'] = cld_df['census_blockid'].astype(str)

        # Convert to UTM coordinates to use fast distance matrix calculation - assume that every DMA is in one UTM zone
        utm_coords = xray_df.apply(lambda x: utm.from_latlon(float(x.lat), float(x.long)), axis=1)
        xray_df[['utm_e', 'utm_n']] = pd.DataFrame(
            list(utm_coords),
            columns=['utm_e', 'utm_n', 'zone_let', 'zone_num']
        )[['utm_e', 'utm_n']]

        utm_coords = cld_df.apply(lambda x: utm.from_latlon(float(x.lat), float(x.long)), axis=1)
        cld_df[['utm_e', 'utm_n']] = pd.DataFrame(
            list(utm_coords),
            columns=['utm_e', 'utm_n', 'zone_let', 'zone_num'])[['utm_e', 'utm_n']]
        # Create unique indices
        xray_df['X_IDX'] = 'X' + xray_df.index.astype(str)

        block_to_xidx = xray_df.drop_duplicates('census_blockid').set_index('census_blockid')['X_IDX'].to_dict()

        cld_df['C_IDX'] = 'C' + cld_df.index.astype(str)

        cld_weights = cld_df.set_index('C_IDX')['multiplier'].to_dict()

        # Determine sub-market order - group by and count C and X events by carrier pattern
        cx_ratio = pd.merge(
            pd.DataFrame(cld_df.groupby('submarket').submarket.count()).rename(
                {'submarket': 'C_record_count'}, axis=1
            ).reset_index(),
            pd.DataFrame(xray_df.groupby('submarket').submarket.count()).rename(
                {'submarket': 'X_record_count'}, axis=1
            ).reset_index(),
            how='outer'
        )

        cx_ratio['ratio'] = cx_ratio.C_record_count / cx_ratio.X_record_count
        cx_ratio.ratio.fillna(0, inplace=True)

        cx_ratio.sort_values('ratio', ascending=False, inplace=True)

        total_multiplier = cld_df['multiplier'].sum()
        xray_capacity = {x: total_multiplier / len(xray_df) for x in xray_df['X_IDX']}

        dm_array = distance_matrix(
            xray_df[['utm_e', 'utm_n']].values,
            cld_df[['utm_e', 'utm_n']].values
        )

        dm = pd.DataFrame(
            dm_array,
            index=xray_df['census_blockid'].values,   # rows: XRAYs
            columns=cld_df['C_IDX'].values            # columns: unique CLDs
        )

        #    print("DM index sample:", dm.index[:5])
        #    print("block_to_xidx sample keys:", list(block_to_xidx.keys())[:5])

        # Initialize 'taken' for ALL C_IDXs to ensure no one gets skipped
        taken = {c: [] for c in cld_df['C_IDX']}

        for submarket_id in cx_ratio.submarket.values:

            c_sub = cld_df.loc[cld_df.submarket == submarket_id]

            # Random order of CLDs
            random.seed(1234)
            c_order = c_sub.C_IDX.values
            random.shuffle(c_order)

            for c in c_order:
                taken[c] = []

                weight_remaining = cld_weights[c]

                x_series = dm[c]  # use C_IDX directly
                x_blocks_ranked = x_series.sort_values().index

                for i in x_blocks_ranked:
                    x_idx = block_to_xidx.get(i)
                    if x_idx is None:
                        continue  # Safety check
                    available = xray_capacity.get(x_idx, 0.0)
                    if available <= 0:
                        continue

                    to_assign = min(weight_remaining, available)
                    if to_assign > 0:
                        taken[c].append((x_idx, to_assign))
                        xray_capacity[x_idx] -= to_assign
                        weight_remaining -= to_assign

                    if weight_remaining <= 1e-6:
                        break

        # === BEGIN DISTANCE-AWARE FALLBACK (with full XRAY coverage) ===

        # Ensure XRAY census_blockid and X_IDX are consistent
        xray_df['census_blockid'] = xray_df['census_blockid'].astype(str)
        cld_df['census_blockid'] = cld_df['census_blockid'].astype(str)

        # Build fallback DM: rows = X_IDX (unique), columns = C_IDX
        dm_fallback = pd.DataFrame(
            distance_matrix(
                xray_df[['utm_e', 'utm_n']].values,
                cld_df[['utm_e', 'utm_n']].values
            ),
            index=xray_df['X_IDX'].values,  # XRAYs = unique!
            columns=cld_df['C_IDX'].values  # CLDs = unique
        )

        # Set up fallback logic
        assigned_weights = {c: sum(w for _, w in taken.get(c, [])) for c in cld_df['C_IDX']}
        cld_leftover = {
            c: cld_weights[c] - assigned_weights.get(c, 0.0)
            for c in cld_df['C_IDX']
            if cld_weights[c] - assigned_weights.get(c, 0.0) > 1e-6
        }

        #    print(f"[FALLBACK] Unassigned CLDs: {len(cld_leftover)}")
        #    print(f"[FALLBACK] XRAYs with remaining capacity: {sum(v > 1e-6 for v in xray_capacity.values())}")

        assigned_in_fallback = 0
        fallback_assignments = 0

        for c, weight_remaining in cld_leftover.items():
            if c not in taken:
                taken[c] = []

            # Candidate XRAYs with capacity
            candidate_xidxs = [
                x_idx for x_idx in xray_df['X_IDX']
                if xray_capacity.get(x_idx, 0.0) > 1e-6
            ]

            # Rank those XRAYs by distance to this CLD
            try:
                x_blocks_ranked = sorted(
                    candidate_xidxs,
                    key=lambda x: dm_fallback.at[x, c]
                )
            except KeyError:
                continue

            for x_idx in x_blocks_ranked:
                available = xray_capacity.get(x_idx, 0.0)
                if available <= 0:
                    continue

                to_assign = min(weight_remaining, available)
                taken[c].append((x_idx, to_assign))
                xray_capacity[x_idx] -= to_assign
                weight_remaining -= to_assign
                assigned_in_fallback += to_assign
                fallback_assignments += 1

                if weight_remaining <= 1e-6:
                    break

        #        if assigned_in_fallback == assigned_before and logged_fallbacks < 10:
        #            print(f"[FALLBACK] CLD {c} made no assignments (remaining={weight_remaining:.4f})")
        #            logged_fallbacks += 1

        #  print(f"[FALLBACK] Total weight assigned during fallback: {assigned_in_fallback:.4f}")
        #  print(f"[FALLBACK] Number of fallback assignments: {fallback_assignments}")

        # === END DISTANCE-AWARE FALLBACK ===

        #    print(f"[AFTER FALLBACK] Total assigned weight: {assigned_total:.4f}")
        #    print(f"[AFTER FALLBACK] Unassigned weight: {total_cld_weight - assigned_total:.4f}")
        #    print(f"[AFTER FALLBACK] XRAY capacity remaining: {sum(xray_capacity.values()):.4f}")

        # Flatten and join
        taken_df = pd.DataFrame(
            [(c, x, w) for c, rows in taken.items() for x, w in rows],
            columns=['C_IDX', 'X_IDX', 'weight']
        )

        final = taken_df.merge(cld_df[['ifa', 'C_IDX']], on='C_IDX', how='left', validate='many_to_one')
        final = final.merge(
            xray_df[['X_IDX', 'customer_id', 'dma_name', 'census_blockid']],
            on='X_IDX',
            how='left',
            validate='many_to_one'
        )

        scaling_factor = len(xray_df) / cld_df['multiplier'].sum()
        final['weight'] *= scaling_factor

        return final[['customer_id', 'dma_name', 'ifa', 'census_blockid', 'weight']]
