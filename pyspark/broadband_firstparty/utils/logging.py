"""Logging utils from relay-data-jobs"""
import datetime
import os
import re
from os.path import expanduser
import logging

__author__ = 'maldeiri'


class Logger:
    """Logger Class"""

    def __init__(self, job_name, log_options):
        """
        Constructor for logger

        :param job_name: name of job
        :param log_options: optional configurations for logger
        """
        self.log_options = log_options
        self.job_name = job_name
        self.logger = None

    def determine_log_level(self, handle, level):
        """
        Determine Log level

        :param handle: log handle
        :param level: log level
        :return: None
        """
        if level.lower() == "CRITICAL".lower():
            handle.setLevel(logging.CRITICAL)
        elif level.lower() == "ERROR".lower():
            handle.setLevel(logging.ERROR)
        elif level.lower() == "WARNING".lower():
            handle.setLevel(logging.WARNING)
        elif level.lower() == "INFO".lower():
            handle.setLevel(logging.INFO)
        elif level.lower() == "DEBUG".lower():
            handle.setLevel(logging.DEBUG)
        else:
            handle.setLevel(logging.NOTSET)

    def get_logger(self):
        """
        Get logger

        :return: configured logger
        """
        if self.logger is None:
            self.logger = logging.getLogger(__name__)
            if not len(self.logger.handlers):
                self.logger.name = self.job_name
                formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                self.determine_log_level(self.logger, self.log_options['level'])

                # Setup console logging
                console_logging = logging.StreamHandler()
                console_logging.setFormatter(formatter)
                self.determine_log_level(console_logging, self.log_options['level'])
                self.logger.addHandler(console_logging)

                # Setup log file logging
                if self.log_options.get('write-to-file', False):
                    directory = self.log_options['file-location']
                    if directory[-1:] == '/':
                        directory = directory[:-1]
                    # Check to see if home directory has been specified, this needs to be done manually
                    if directory[0] == '~':
                        home_directory = expanduser("~")
                        directory = directory.replace("~", home_directory, 1)
                    date_string = datetime.date.today().strftime("%Y%m%d")
                    job_name_minus_punct = re.sub('[^0-9a-zA-Z]+', '_', self.job_name)
                    directory = '/tmp/' + directory + "/" + job_name_minus_punct
                    if not os.path.exists(directory):
                        os.makedirs(directory)
                    log_location = '{directory}/{job_name}_{date_string}.log'.format(directory=directory,
                                                                                     job_name=job_name_minus_punct,
                                                                                     date_string=date_string)
                    file_logging = logging.FileHandler(log_location)
                    file_logging.setFormatter(formatter)
                    self.determine_log_level(file_logging, self.log_options['level'])
                    self.logger.addHandler(file_logging)

        return self.logger

    @staticmethod
    def get_existing_logger(job_name):
        """
        Get existing logger if it exists for a job

        :param job_name: job name
        :return: logger for the associated job
        """
        return logging.getLogger(job_name)
