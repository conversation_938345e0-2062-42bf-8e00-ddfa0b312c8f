"""Errors and Classes to support reading and writing from S3"""
import pyarrow as pa
import pyarrow.parquet as pq

import s3fs
from botocore.exceptions import ClientError
from pandas.core.frame import DataFrame
import traceback


class S3PathDoesNotExistException(Exception):
    """Exception Class for nonexistent S3 path"""

    def __init__(self, path):
        """
        Constructor for S3 path Does not Exist Exception

        :param path: invalid path
        """
        super(S3PathDoesNotExistException, self).__init__(f'Path {path} does not exist')


class S3PathReadError(Exception):
    """Exception Class for Errors reading from S3 path"""

    def __init__(self, path, exception):
        """
        Constructor for S3 path read error

        :param path: the s3 path
        :param exception: the exception from reading from s3
        """
        super(S3PathReadError, self).__init__(f'Failed to read from {path}: {str(exception)}')


class S3PathWriteException(Exception):
    """Exception Class for Errors writing to s3"""

    def __init__(self, path, exception):
        """
        Constructor for s3 path write error

        :param path: the s3 path
        :param exception: the exception from writing to s3
        """
        super(S3PathWriteException, self).__init__(f'Failed to write dataset to {path}: {str(exception)}')


class S3ParquetUtil:
    """Class for S3 parquet Utils"""

    def __init__(self):
        """Constructor for S3 parquet Utils"""
        self.s3 = s3fs.S3FileSystem()
        s3fs.core.logger.setLevel("DEBUG")

    def read_dataset(self, s3_src_path: str, columns=None) -> DataFrame:
        """
        Read parquet data from s3 path as Pandas data frame

        :param s3_src_path: path to read from
        :param columns: columns to read
        :return: the data read from s3
        """
        try:
            if columns is None:
                df = pq.ParquetDataset(s3_src_path).read_pandas().to_pandas()
            else:
                df = pq.ParquetDataset(s3_src_path).read_pandas(columns=columns).to_pandas()
        except OSError:
            traceback.print_exc()
            raise S3PathDoesNotExistException(s3_src_path)
        except ClientError as exception:
            raise S3PathReadError(s3_src_path, exception)
        else:
            return df

    def write_dataset(self, df: DataFrame, s3_dest_path: str, partition_cols: list):
        """
        Write dataset from Pandas data frame to s3 path

        :param df: dataframe to be written to s3
        :param s3_dest_path: path to write to on s3
        :param partition_cols: columns to partition data by
        :return:
        """
        table = pa.Table.from_pandas(df, preserve_index=False)
        pq.write_to_dataset(table, root_path=s3_dest_path, partition_cols=partition_cols)

    def find_partitions(self, s3_path: str, partition_name: str) -> list:
        """
        Find all parquet partitions from s3 path

        :param s3_path: s3 path to search for partitions in
        :param partition_name: name of partition
        :return: existing partition values of given partition name
        """
        try:
            source_data = self.read_dataset(s3_path)
            partition_values = source_data[partition_name].unique().tolist()
        except KeyError:
            print(f'Column `{partition_name}` does not exist in source data')
            return []
        except S3PathDoesNotExistException as s3_read_exception:
            print(s3_read_exception)
            return []
        else:
            return partition_values
