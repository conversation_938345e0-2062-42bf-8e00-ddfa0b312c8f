"""Additional Utils for First party Optimization"""
from urllib.parse import urlparse
from datetime import date
import os
import json


class InvalidS3Path(Exception):
    """Exception Class for invalid s3 path"""

    def __init__(self, path):
        """
        Constructor for Invalid s3 path exception

        :param path: The path that caused the eception
        """
        super(InvalidS3Path, self).__init__(f'Path {path} is not a valid s3 path.')


def is_s3_path_valid(path: str) -> bool:
    """
    Check validity of s3 path based on schema

    :param path: path to check
    :return: whether it is a valid s3 path
    """
    if path:
        uri = urlparse(path)
        if uri.scheme in ['s3', 's3a', 's3n']:
            return True
    return False


def read_json_file(json_file_path):
    """
    Util for reading json file

    :param json_file_path: path to local json file
    :return: The read in json object
    """
    if os.path.isfile(json_file_path):
        # If file exists then load it as a dictionary
        with open(json_file_path) as json_file:
            json_object = json.load(json_file)
            return json_object
    else:
        print(f'The {json_file_path} config file can not be found, please ensure one is created before proceeding')


def validate_date_range(date_range):
    """
    Validate date range is not trying to process any frozen months

    :param date_range: date range to validate
    :return: IllegalArgumentException if trying to process frozen data
    """
    if date_range[0] < date(2024, 1, 21):
        raise ValueError(f'Cannot process data for {date_range[0]} to {date_range[-1]} '
                         f'because it contains frozen months')
