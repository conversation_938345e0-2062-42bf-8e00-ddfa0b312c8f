"""Project Scope Features class and DataTypes"""
from datetime import date
from datetime import datetime
from dateutil.relativedelta import relativedelta

from broadband_firstparty.permanent_store.permanent_store_reader import PermanentStoreReader
from broadband_firstparty.utils.logging import Logger


class FiscalMonthDateRangeError(Exception):
    """Exception Class for Fiscal Month Date Range Error"""

    def __init__(self, reason, exception):
        """
        Constructor for Fiscal Month Date Range Error

        :param reason: Cause of the error
        :param exception: the exception message
        """
        super(FiscalMonthDateRangeError, self).__init__(f'`{reason}`, fiscal month date range error: {exception}')


class FiscalMonthUnexpectedInput(Exception):
    """Exception Class for Fiscal Month Missing inputs"""

    def __init__(self):
        """Constructor for Fiscal Month Unexpected Input error"""
        super(FiscalMonthUnexpectedInput, self).__init__('Neither `look-back-months` nor '
                                                         '`fiscal-month-period` has been defined.')


class ProjectScopeFeatures:
    """Class containing Project Scope Features"""

    def __init__(self, job_name: str, look_back_months: int = None, fiscal_month_period: dict = None,
                 fiscal_month_last_date=21):
        """
        Constructor for Project Scope Features

        :param job_name: Mandetory Job name
        :param look_back_months: optional months to look back
        :param fiscal_month_period: Optional fiscal month period
        :param fiscal_month_last_date: last day of fiscal month
        """
        self._look_back_months = look_back_months
        self._fiscal_month_period = fiscal_month_period
        self._fiscal_month_last_day = fiscal_month_last_date
        self.partition_name = 'month_end_date'
        self.logger = Logger.get_existing_logger(job_name)

    @property
    def _current_fiscal_month_end_date(self) -> date:
        """
        Retrieves current fiscal month end data

        :return: calculated current fiscal month end date
        """
        now = datetime.now()
        one_month_from_now = (now - relativedelta(months=1)).replace(day=self._fiscal_month_last_day)
        return one_month_from_now.date()

    def get_fiscal_month_date_range(self) -> list:
        """
        Get fiscal month date range from either a number of months to look back or a fiscal month period

        :return: fiscal month date range
        """
        if not self._look_back_months and not self._fiscal_month_period:
            raise FiscalMonthUnexpectedInput()
        elif self._fiscal_month_period:
            return self._from_fiscal_month_period()
        else:
            return self._from_look_back_months()

    def _from_fiscal_month_period(self) -> list:
        """
        Create fiscal month date range from fiscal month period with start and end dates

        :return: the fiscal month date range
        """
        try:
            start_date = datetime.strptime(self._fiscal_month_period['start-date'], '%Y-%m-%d').date()
            end_date = datetime.strptime(self._fiscal_month_period['end-date'], '%Y-%m-%d').date()
            fiscal_month_date_range = [start_date]
            while fiscal_month_date_range[-1] != end_date:
                fiscal_month_date_range.append(fiscal_month_date_range[-1] + relativedelta(months=1))
        except Exception as exception:
            raise FiscalMonthDateRangeError('fiscal-month-period', exception)
        else:
            return fiscal_month_date_range

    def _from_look_back_months(self) -> list:
        """
        Create fiscal month date range from a number of months to look back

        :return: the fiscal month date range
        """
        try:
            fiscal_month_date_range = [self._current_fiscal_month_end_date]

            for _ in range(0, self._look_back_months - 1):
                last_inserted = fiscal_month_date_range[-1]
                fiscal_month_date_range.append(last_inserted - relativedelta(months=1))

            fiscal_month_date_range.reverse()
        except Exception as exception:
            raise FiscalMonthDateRangeError('look-back-months', exception)
        else:
            return fiscal_month_date_range

    def has_current_fiscal_month_already_been_processed(self, s3_path: str) -> bool:
        """
         Has the current fiscal month already been processed? (i.e. fiscal_month: 2020-04-21 when running in May 2020)

        :param s3_path: s3 path to check for fiscal month in
        :return: Bool Corresponding to whether or not the current month has been processed
        """
        start_date = end_date = self._current_fiscal_month_end_date
        permanent_store_reader = PermanentStoreReader(self.partition_name, self.logger)
        if not permanent_store_reader.read_from_path(s3_path, start_date, end_date).empty:
            return True
        return False

    def create_partition_key(self, partition_value: str) -> str:
        """
        Create partition key from the partition value

        :param partition_value: value of partition
        :return: formatted string with a partition name and value
        """
        return f'{self.partition_name}={partition_value}'

    @property
    def _current_revision_date(self) -> date:
        """
        Return the default revision date (today)

        :return: the current revision date
        """
        return date.today()
