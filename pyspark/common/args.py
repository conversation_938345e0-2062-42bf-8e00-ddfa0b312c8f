"""Argument parser."""
import argparse
import logging
import json


def _parse_config(c):
    try:
        return json.loads(c)
    except Exception as ex:
        logging.error("Couldn't parse config", ex)
        raise ex


def get_or_default(key, config, default):
    """Get key from config or return default."""
    return default if key not in config else config[key]


def get_arg_parser():
    """Arg parser."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '-d', '--debug',
        help="Print lots of debugging statements",
        action="store_const", dest="loglevel", const=logging.DEBUG,
        default=logging.WARNING,
    )
    parser.add_argument(
        '-v', '--verbose',
        help="Be verbose",
        action="store_const", dest="loglevel", const=logging.INFO,
    )
    parser.add_argument(
        '-c', '--config',
        help="json runner config",
        type=_parse_config,
        required=True
    )
    return parser
