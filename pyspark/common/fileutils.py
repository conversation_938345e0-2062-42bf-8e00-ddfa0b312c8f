"""Local and S3 file system commands"""
import glob
import os
import re
from collections import defaultdict
from typing import List, Tuple, Set
from urllib.parse import urlparse

import boto3


class OSFileUtils:
    """file utils for os level code"""

    scheme = ""

    @staticmethod
    def list_directories(root: str, recursive: bool = False, max_level: int = 3) -> List[str]:
        """functions as dir or ls. Allows for recursion

        Parameters
        ----------
        root : str
            root to list
        recursive : bool
            optional parameter to recurse
        max_level: int
            3 deep for y/m/d. Only 1 for date=yyyy-MM-dd

        Returns
        -------
            full paths for sub directories
        """
        r = root if root.endswith("/") else root + "/"
        if recursive:
            return [f for f in glob.iglob(r + "/".join("*" * max_level)) if OSFileUtils.is_directory(f)]
        else:
            return [r + f for f in os.listdir(r)]

    @staticmethod
    def all_directory_strings_have_data_or_throw(paths: List[str], has_success_marker=False) -> List[str]:
        """All directories must have data or throw an exception

        Parameters
        ----------
        paths : list[str]
            Non-empty list of paths to check
        has_success_marker : boolean
            For parquet directories, check if _SUCCESS marker is available
        Returns
        -------
        list[str] :
            all paths inputted
        """
        for p in paths:
            if not os.path.exists(p):
                raise Exception("Missing data for: ", p)
            if has_success_marker and not os.path.exists(os.path.join(p, "_SUCCESS")):
                raise Exception("Missing success marker for: ", p)
        return paths

    @staticmethod
    def exists(path: str) -> bool:
        """Check if path exists

        Parameters
        ----------
        path : str
            path to check

        Returns
        -------
        bool :
            True if path exists, else False
        """
        return os.path.exists(path)

    @staticmethod
    def is_directory(path: str) -> bool:
        """Checks to see if item is a directory

        Parameters
        ----------
        path : str
            path to check

        Returns
        -------
        True if item is a directory
        """
        return os.path.isdir(path)


class S3FileUtils:
    """file utils for s3"""

    s3 = boto3.client('s3')
    scheme = "s3"

    @staticmethod
    def split_s3(path: str) -> Tuple[str, str]:
        """Split a s3 path into bucket and key

        Parameters
        ----------
        path : str
             path to be split

        Returns
        -------
        Tuple[str, str]
            tuple of bucket & key
        """
        s3_path = path if path.endswith('/') else (path + '/')

        path_parts = s3_path.replace("s3://", "").split("/")
        bucket = path_parts.pop(0)
        prefix = "/".join(path_parts)
        return bucket, prefix

    @staticmethod
    def list_directories(root: str, recursive: bool = False, max_level: int = 2) -> List[str]:
        """functions as dir or ls. Allows for recursion

        Parameters
        ----------
        root : str
            root to list
        recursive : bool
            optional parameter to recurse
        max_level: int
            only works for s3, how many levels to recurse through

        Returns
        -------
            full paths for sub directories
        """
        (bucket, s3_path) = S3FileUtils.split_s3(root)

        def _recurse(path: str, level: int) -> Set[Tuple]:
            """Helper function to recurse through s3 directory

            Parameters
            ----------
            path : str
                root to list
            level : int
                how many levels to recurse through
            Returns
            -------
            Set(str) :
                Set of distinct paths
            """
            result = set()
            if level > max_level:
                return result

            for prefix in _find_common_prefixes(path):
                nested = _recurse(prefix, level + 1)
                prefix_split = tuple(re.findall('[^/]+/', prefix[len(path):]))
                result |= {prefix_split + nested_split for nested_split in nested}
                if not nested:
                    result.add(prefix_split)  # prefix has no children (or we passed max level)
            return result

        def _find_common_prefixes(path: str) -> Set[str]:
            """Helper function to parse through aws s3 outputs

            Parameters
            ----------
            path : str
                s3 path without bucket

            Returns
            -------
            str :
                fully paginated results of common prefixes
            """
            paginator = S3FileUtils.s3.get_paginator('list_objects')
            iterator = paginator.paginate(**{'Bucket': bucket, 'Prefix': path, 'Delimiter': '/'})
            return {p['Prefix'] for page in iterator if 'CommonPrefixes' in page for p in page['CommonPrefixes']}

        if recursive:
            return list(map(lambda x: root + ''.join(list(x)), _recurse(s3_path, 0)))
        else:
            return [root + x[len(s3_path):] for x in _find_common_prefixes(s3_path)]

    @staticmethod
    def all_directory_strings_have_data_or_throw(paths: List[str], has_success_marker=False) -> List[str]:
        """All directories must have data or throw an exception

        Parameters
        ----------
        paths : list[str]
            Non-empty list of paths to check
        has_success_marker : boolean
            For parquet directories, check if _SUCCESS marker is available
        Returns
        -------
        list[str] :
            all paths inputted
        """
        for p in paths:
            if not S3FileUtils.exists(p):
                raise Exception("Missing data for: ", p)
            if has_success_marker and not os.path.exists(os.path.join(p, "_SUCCESS")):
                raise Exception("Missing success marker for: ", p)
        return paths

    @staticmethod
    def exists(path: str) -> bool:
        """Check if path exists

        Parameters
        ----------
        path : str
            path to check

        Returns
        -------
        bool :
            True if path exists, else False
        """
        (bucket, s3_path) = S3FileUtils.split_s3(path)
        if not s3_path.endswith('/'):
            s3_path = s3_path + '/'
        resp = S3FileUtils.s3.list_objects_v2(Bucket=bucket, Prefix=s3_path, Delimiter='/', MaxKeys=1)
        return 'KeyCount' in resp and resp['KeyCount'] > 0

    @staticmethod
    def is_directory(path: str) -> bool:
        """Checks to see if item is a directory. Doesn't work well for aws s3

        Parameters
        ----------
        path : str
            path to check

        Returns
        -------
        True if item is a directory
        """
        return True


class NotImplementedFileUtils:
    """Catch all unimplemented file utils"""

    scheme = "Bad Scheme"

    @staticmethod
    def get_scheme(path: str) -> str:
        """Gets the scheme of the URI

        Parameters
        ----------
        path : str
            uri path

        Returns
        -------
        str:
            s3 or os
        """
        raise NotImplementedError(f"schema not found for {path}")

    @staticmethod
    def list_directories(root: str, recursive: bool = False, max_level: int = 3) -> List[str]:
        """functions as dir or ls. Allows for recursion

        Parameters
        ----------
        root : str
            root to list
        recursive : bool
            optional parameter to recurse
        max_level: int
            3 deep for y/m/d. Only 1 for date=yyyy-MM-dd

        Returns
        -------
            full paths for sub directories
        """
        raise NotImplementedError(f"schema not found for {root}")

    @staticmethod
    def all_directory_strings_have_data_or_throw(paths: List[str], has_success_marker=False) -> List[str]:
        """All directories must have data or throw an exception

        Parameters
        ----------
        paths : list[str]
            Non-empty list of paths to check
        has_success_marker : boolean
                For parquet directories, check if _SUCCESS marker is available
        Returns
        -------
        list[str] :
            all paths inputted
        """
        raise NotImplementedError(f"scheme not found for {paths[0]}")

    @staticmethod
    def exists(path: str) -> bool:
        """Check if path exists

        Parameters
        ----------
        path : str
            path to check

        Returns
        -------
        bool :
            True if path exists, else False
        """
        raise NotImplementedError(f"schema not found for {path}")

    @staticmethod
    def is_directory(path: str) -> bool:
        """Checks to see if item is a directory

        Parameters
        ----------
        path : str
            path to check

        Returns
        -------
        True if item is a directory
        """
        raise NotImplementedError(f"schema not found for {path}")


fs = defaultdict(NotImplementedFileUtils)
fs["s3"] = S3FileUtils
fs["s3a"] = S3FileUtils
fs[""] = OSFileUtils


def get_scheme(path: str) -> str:
    """Gets the scheme of the URI. Handles os, s3, and s3a

    Parameters
    ----------
    path : str
        uri path

    Returns
    -------
    str:
        s3 or os
    """
    p = urlparse(path)
    return fs[p.scheme].scheme


def list_directories(root: str, recursive: bool = False, max_level: int = 3) -> List[str]:
    """functions as dir or ls. Allows for recursion

    Parameters
    ----------
    root : str
        root to list
    recursive : bool
        optional parameter to recurse
    max_level: int
        3 deep for y/m/d. Only 1 for date=yyyy-MM-dd

    Returns
    -------
        full paths for sub directories
    """
    return fs[get_scheme(root)].list_directories(root, recursive, max_level)


def all_directory_strings_have_data_or_throw(paths: List[str], has_success_marker=False) -> List[str]:
    """All directories must have data or throw an exception

    Parameters
    ----------
    paths : list[str]
        root path to check
    has_success_marker : boolean
        For parquet directories, check if _SUCCESS marker is available
    Returns
    -------
    list[str] :
        all paths inputted
    """
    if paths:
        return fs[get_scheme(paths[0])].all_directory_strings_have_data_or_throw(paths, has_success_marker)
    return []


def exists(path: str) -> bool:
    """Check if path exists

    Parameters
    ----------
    path : str
        path to check

    Returns
    -------
    bool :
        True if path exists, else False
    """
    return fs[get_scheme(path)].exists(path)


def is_directory(path: str) -> bool:
    """Checks to see if item is a directory

    Parameters
    ----------
    path : str
        path to check

    Returns
    -------
    True if item is a directory
    """
    return fs[get_scheme(path)].is_directory(path)
