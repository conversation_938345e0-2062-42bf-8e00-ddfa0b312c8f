"""Common path operations"""
import os
import re
from datetime import datetime, date

from common.date import DATE_FORMAT
from common import fileutils


def with_slash(path: str) -> str:
    """Append trailing slash if needed

    Parameters
    ----------
    path : str
        in path

    Returns
    -------
    str
        path with trailing slash
    """
    return path if path.endswith('/') else path + '/'


def ymd_from_date(date: datetime) -> str:
    """convert date to ymd partition string

    Parameters
    ----------
    date : datetime
        date to expand

    Returns
    -------
    str :
        ymd partition string
    """
    return os.path.join(f"year={date.year}", f"month={date.strftime('%m')}", f"day={date.strftime('%d')}")


def ymd_path(path: str, date: datetime) -> str:
    """Append year, month, & date to a path.

    Parameters
    ----------
    path : str
        root path
    date : datetime
        date to expand

    Returns
    -------
    str
        root path with year, month, & date partitions added
    """
    return os.path.join(path, ymd_from_date(date))


def wtd(path: str, date: datetime) -> str:
    """Append a window_tail_date partition

    Parameters
    ----------
    path : str
        root path
    date : datetime
        date to expand

    Returns
    -------
    str
        root path with window_tail_date partition added
    """
    return f"{path}window_tail_date={date.strftime(DATE_FORMAT)}"


def parse_partition(path: str, partition_name: str) -> date:
    """parse a strings partition given a partition name

    Parameters
    ----------
    path : str
        full path
    partition_name : str
        partition type

    Returns
    -------
    datetime
        datetime from partition
    """
    if partition_name == "ymd":
        return parse_ymd_partition_to_date(parse_ymd_partition(path))
    elif partition_name in ["date", "churn_date", "window_tail_date"]:
        return parse_date_partition_to_date(parse_date_partition(path, partition_name), partition_name)
    elif partition_name.endswith("date"):
        raise NotImplementedError(partition_name)
    else:
        raise NotImplementedError(f"Cannot parse partition: {partition_name}")


def parse_ymd_partition(path: str) -> str:
    """helper function to parse ymd from a string

    Parameters
    ----------
    path : str
        full path

    Returns
    -------
    str
        ymd path
    """
    search = os.path.join("year=\\d{4}", "month=\\d{2}", "day=\\d{2}")
    r = re.search(search, path)
    return r.group(0) if r is not None else None


def parse_date_partition(path: str, partition_name: str = "date") -> str:
    """helper function to parse date partition from a string

    Parameters
    ----------
    path: str
        full path
    partition_name: str
        partition type

    Returns
    -------
    str
        ymd path
    """
    search = partition_name + "=\\d{4}-\\d{2}-\\d{2}"
    r = re.search(search, path)
    return r.group(0) if r is not None else None


def parse_partition_to_date(partition: str, partition_name: str) -> datetime:
    """parse standard partitions from a string to a date.

    Parameters
    ----------
    partition : str
        partition string
    partition_name : str
        partition type - enum of ymd, date, churn_date, or window_tail_date

    Returns
    -------
    datetime:
        parsed date
    """
    if partition_name == "ymd":
        return parse_ymd_partition_to_date(partition)
    elif partition_name in ["date", "churn_date", "window_tail_date"] or partition_name.endswith("date"):
        return parse_date_partition_to_date(partition, partition_name)


def parse_ymd_partition_to_date(partition: str) -> datetime:
    """helper function to parse a datetime from ymd partition string

    Parameters
    ----------
    partition : str
        partition string

    Returns
    -------
    datetime :
        date from partition
    """
    less_leading_slash = partition[1:] if partition.startswith("/") else partition
    less_trailing_slash = less_leading_slash[:-1] if less_leading_slash.endswith("/") else less_leading_slash
    return datetime.strptime(less_trailing_slash, "year=%Y/month=%m/day=%d")


def parse_date_partition_to_date(partition: str, partition_name: str = "date") -> datetime:
    """helper function that parses a date partition string to a date

    Parameters
    ----------
    partition : str
        date partition string
    partition_name : str
        partition type - usually date=, churn_date=, or window_tail_date=

    Returns
    -------
    datetime:
        parsed string
    """
    return datetime.strptime(partition, partition_name + "=%Y-%m-%d")


def parse_date_partition_from_date(start_date: datetime, partition_name: str = "date") -> str:
    """helper function that turns a datetime to a partition string

    Parameters
    ----------
    start_date : datetime
        date to convert
    partition_name : str
        partition name

    Returns
    -------
    str :
        {partition_name}=YYYY-MM-DD
    """
    return f"{partition_name}={start_date.strftime(DATE_FORMAT)}"


def get_earliest_date(
    directory: str,
    date_partition_name: str,
    after_or_equal_to: datetime = None
) -> datetime:
    """Get the earliest date from directory

    Parameters
    ----------
    directory : str
        root path
    date_partition_name : str
        partition type (ymd, date, churn_date, or window_tail_date)
    after_or_equal_to : datetime
        optional parameter to ignore dates before this date

    Returns
    -------
    datetime:
        gets the earliest date if available
    """
    return _get_date(directory, date_partition_name, after_or_equal_to, reverse=False)


def get_latest_date(
    directory: str,
    date_partition_name: str,
    before_or_equal_to: datetime = None
) -> datetime:
    """Get the latest date from the directory

    Parameters
    ----------
    directory : str
        root path
    date_partition_name : str
        partition type (ymd, date, churn_date, or window_tail_date)
    before_or_equal_to : datetime
        optional parameter to ignore dates after this date

    Returns
    -------
    datetime:
        gets the latest date if available
    """
    return _get_date(directory, date_partition_name, before_or_equal_to, reverse=True)


def _max_partition_value(
    directory: str,
    partition_name: str,
    before_or_equal_to: str = None,
    reverse: bool = False
) -> (str, str):
    """helper function to return the max partition

    Parameters
    ----------
    directory : str
        root path
    partition_name : str
        partition type (ymd, date, churn_date, or window_tail_date)
    before_or_equal_to : datetime
        optional parameter to set the upper or lower bounds depending on reverse
    reverse : bool
        parameter used to control earliest or latest dates

    Returns
    -------
    Tuple(str, str):
        returns a tuple of (path with partition, partition). The second item should be parseable into a date
        raises ValueError if no partitions are found
    """
    if not fileutils.is_directory(directory):
        raise Exception("Not a directory:", directory)

    # get all item in folder and strip directory
    dirs = set([with_slash(x[len(directory):]) for x in fileutils.list_directories(directory, True)])

    # filter for parquet partitions
    parts = [x for x in dirs if x.startswith(partition_name) or (partition_name == "ymd" and parse_ymd_partition(x))]
    # remove items outside of bounds
    if before_or_equal_to:
        # this is comparing dates as strings, I don't think that works

        def filter_date(d):
            if reverse:
                return parse_ymd_partition_to_date(d) <= before_or_equal_to
            else:
                return parse_ymd_partition_to_date(d) >= before_or_equal_to

        parts = list(filter(filter_date, parts))

    if len(parts) > 0:
        parts.sort(reverse=reverse)
        return directory + parts[0], parts[0]
    else:
        raise ValueError(f"No partitions found in directory: {directory} before_or_equal_to: {before_or_equal_to}")


def _get_date(
        directory: str,
        partition_name: str,
        before_or_equal_to: datetime = None,
        reverse: bool = False
) -> datetime:
    """helper function that splits ymd vs other date partitions

    Parameters
    ----------
    directory : str
        root path
    partition_name : str
        partition type (ymd, date, churn_date, or window_tail_date)
    before_or_equal_to : datetime
        optional parameter to set the upper or lower bounds depending on reverse
    reverse : bool
        parameter used to control earliest or latest dates

    Returns
    -------
    datetime:
        the earliest or latest date from the partition depending on the reverse
    """
    if partition_name == "ymd":
        path_tuple = _max_partition_value(
            directory=with_slash(directory),
            partition_name="ymd",
            before_or_equal_to=before_or_equal_to,
            reverse=reverse
        )
        if path_tuple:
            p = path_tuple[1] if not path_tuple[1].endswith("/") else path_tuple[1][:-1]
            return parse_ymd_partition_to_date(p)
    else:
        path_tuple = _max_partition_value(
            directory=with_slash(directory),
            partition_name=partition_name,
            before_or_equal_to=before_or_equal_to,
            reverse=reverse
        )
        if path_tuple:
            p = path_tuple[1] if not path_tuple[1].endswith("/") else path_tuple[1][:-1]
            return parse_date_partition_to_date(p, partition_name)
