"""Common date operations"""
from datetime import timedelta, date
from typing import List

DATE_FORMAT = '%Y-%m-%d'


def date_range(start_date: date, end_date: date) -> List[date]:
    """Generates a list of dates between start_date and end_date inclusive

    Parameters
    ----------
    start_date : date
        start date of range
    end_date : date
        end date of range

    Returns
    -------
        List of dates between start and end dates
    """
    i = start_date
    while i <= end_date:
        yield i
        i += timedelta(days=1)
