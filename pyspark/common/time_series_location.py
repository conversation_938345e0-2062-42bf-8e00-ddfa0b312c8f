"""Time Series Locations"""
import os
from abc import abstractmethod
from datetime import date, datetime, timedelta
from typing import List, Tuple

from common import fileutils
from common.date import DATE_FORMAT, date_range
from common.path import get_earliest_date, get_latest_date


class TimeSeriesLocation:
    """Functions to extract data and dates from parquet partitions"""

    def __init__(
            self,
            source: str,
            first_date: datetime = None,
            last_date: datetime = None
    ):
        """constructor

        Parameters
        ----------
        source : str
            root path of data
        first_date : datetime
            optional param to set the first date
        last_date : datetime
            optional param to set the last date
        """
        self.source = source
        self.first_date = first_date
        self.last_date = last_date

    def with_sub_folder(self, folder: str):
        """Adds a partition to source

        Parameters
        ----------
        folder : str
            folder name to add

        Returns
        -------
        Updates self.source
        """
        self.source = os.path.join(self.source, folder)

    def with_partition(self, partition: str):
        """Adds a partition to source

        Parameters
        ----------
        partition : str
            partition name

        Returns
        -------
        Updates self.source and returns self
        """
        self.with_sub_folder(partition)
        return self

    def valid_input_partition_or_throw(
            self,
            start_date: datetime,
            end_date: datetime = None,
            throw_if_out_of_range: bool = False
    ) -> List[str]:
        """validate inputs or throw an exception

        Parameters
        ----------
        start_date : datetime
            start date of partition
        end_date : datetime
            optional end date which turns this function to checking a range
        throw_if_out_of_range : bool
            optional parameter to silently warn instead

        Returns
        -------
        List[str] :
            list of strings that are valid
        """
        paths = self.input_partitions_for(start_date, end_date, throw_if_out_of_range)
        return fileutils.all_directory_strings_have_data_or_throw(paths)

    def input_partitions_for(
            self,
            start_date: datetime,
            end_date: datetime = None,
            throw_if_out_of_range: bool = False
    ) -> List[str]:
        """helper function to get potential partitions between start and end dates using first and last dates as bounds

        Parameters
        ----------
        start_date : datetime
            start date of partition
        end_date : datetime
            optional end date which turns this function to checking a range
        throw_if_out_of_range : bool
            optional parameter to silently warn instead

        Returns
        -------
        List[str] :
            list of strings that are valid
        """
        # error message string
        value_error_str = f"Requesting input partitions {start_date} to {end_date}"

        def get_start_date() -> date:
            potential = start_date
            if self.first_date is None:
                return potential
            elif start_date < self.first_date and throw_if_out_of_range:
                raise ValueError(f"{value_error_str} before first date of {self.first_date}")
            else:
                return max([potential, self.first_date])

        def get_end_date() -> date:
            potential = end_date
            if self.last_date is None:
                return potential
            elif end_date > self.last_date and throw_if_out_of_range:
                raise ValueError(f"{value_error_str} after last date of {self.last_date}")
            else:
                return min([potential, self.last_date])

        start_date = get_start_date()
        end_date = get_end_date()
        return self.partitions(start_date, end_date)

    def partitions(self, start_date: datetime, end_date: datetime = None) -> List[str]:
        """list of pqrquet partitions between start and end dates

        Parameters
        ----------
        start_date : datetime
            start date of partition
        end_date : datetime
            optional end date which turns this function to checking a range

        Returns
        -------
        List[str] :
            List of parquet paths
        """
        ldr = date_range(start_date, end_date) if end_date else [start_date]
        return [self.partition(i) for i in ldr]

    @abstractmethod
    def partition(self, start_date: datetime) -> str:
        """Gets a single parquet partition location. This should be overwritten by a subclass

        Parameters
        ----------
        start_date : datetime
            start date of partition

        Returns
        -------
        str :
            uri (location) of parquet partition
        """
        raise NotImplementedError("Abstract method")

    @abstractmethod
    def earliest_date(self, after_or_equal: datetime = None) -> date:
        """Gets the earliest partition of source. This should be overwritten by a subclass

        Parameters
        ----------
        after_or_equal : datetime
            optional parameter

        Returns
        -------
        date :
            earliest date
        """
        raise NotImplementedError("Abstract method")

    @abstractmethod
    def latest_date(self, before_or_equal: datetime = None) -> date:
        """Gets the latest partition of source. This should be overwritten by a subclass

        Parameters
        ----------
        before_or_equal : datetime
            optional parameter

        Returns
        -------
        date :
            latest date
        """
        raise NotImplementedError("Abstract method")

    def exists(self, start_date: datetime = None) -> bool:
        """Checks to see if partition exists on a specific day

        Parameters
        ----------
        start_date :
            date to check

        Returns
        -------
        bool :
            True if exists
        """
        path = self.partition(start_date) if start_date else self.source
        fileutils.exists(path)

    def exists_and_has_data(self) -> bool:
        """checks to see if source path exists and has data

        Returns
        -------
        bool :
            True if path exists and has data
        """
        # shortcut
        if not fileutils.exists(self.source):
            return False
        return fileutils.list_directories(self.source).length + fileutils.listFiles(self.source).length > 0

    def partitions_existing_between(self, start_date: datetime, end_date: datetime = None) -> List[Tuple]:
        """Get parquet partition locations between start and end date while skipping missing partitions

        Parameters
        ----------
        start_date : datetime
            start date of partition
        end_date : datetime
            optional end date which turns this function to checking a range

        Returns
        -------
        List(Tuple(day, path)) :
            List of partitions that exist on a given day
        """
        if start_date > end_date:
            raise ValueError("start_date must come before or be equal to end_date")
        for x in range((end_date - start_date).days + 1):
            day = start_date + timedelta(days=x)
            path = self.partition(day)
            if fileutils.is_directory(path):
                yield tuple(day, path)

    def partitions_existing_between_paths_only(self, start_date: datetime, end_date: datetime = None) -> List[str]:
        """Helper function to partitions_existing_between. Only returns the paths

        Parameters
        ----------
        start_date : datetime
            start date of partition
        end_date : datetime
            optional end date which turns this function to checking a range

        Returns
        -------
        List[str] :
            All existing parquet locations
        """
        return map(lambda x: x[1], self.partitions_existing_between(start_date, end_date))


class YMDTimeSeriesLocation(TimeSeriesLocation):
    """subclass for YMD datasources"""

    def __init__(
        self,
        source: str,
        first_date: datetime = None,
        last_date: datetime = None
    ):
        """constructor

        Parameters
        ----------
        source : str
            root path of data
        first_date : datetime
            optional param to set the first date
        last_date : datetime
            optional param to set the last date
        """
        super().__init__(
            source=source,
            first_date=first_date,
            last_date=last_date
        )

    def partition(self, start_date: datetime) -> str:
        """Gets a single parquet partition location

        Parameters
        ----------
        start_date : datetime
            start date of partition

        Returns
        -------
        str :
            uri (location) of parquet partition
        """
        return os.path.join(
            self.source,
            f"year={start_date.strftime('%Y')}",
            f"month={start_date.strftime('%m')}",
            f"day={start_date.strftime('%d')}"
        )

    def earliest_date(self, after_or_equal: datetime = None) -> date:
        """Gets the earliest date of source

        Parameters
        ----------
        after_or_equal : datetime
            optional parameter that sets a lower bound

        Returns
        -------
        date :
            earliest date
        """
        return get_earliest_date(
            directory=self.source,
            date_partition_name="ymd",
            after_or_equal_to=after_or_equal
        )

    def latest_date(self, before_or_equal: datetime = None) -> date:
        """Gets the latest date of the source

        Parameters
        ----------
        before_or_equal : datetime
            optional parameters that sets an upper bound

        Returns
        -------
            latest date
        """
        return get_latest_date(
            directory=self.source,
            date_partition_name="ymd",
            before_or_equal_to=before_or_equal)


class DateTimeSeriesLocation(TimeSeriesLocation):
    """subclass for YMD datasources"""

    def __init__(
            self,
            source: str,
            partition_type: str = "date",
            first_date: datetime = None,
            last_date: datetime = None
    ):
        """constructor

        Parameters
        ----------
        source : str
            root path of data
        first_date : datetime
            optional param to set the first date
        last_date : datetime
            optional param to set the last date
        """
        super().__init__(
            source=source,
            first_date=first_date,
            last_date=last_date
        )
        self.partition_type = partition_type

    def partition(self, start_date: datetime) -> str:
        """Gets a single parquet partition location

        Parameters
        ----------
        start_date : datetime
            start date of partition

        Returns
        -------
        str :
            uri (location) of parquet partition
        """
        return os.path.join(
            self.source,
            f"{self.partition_type}={start_date.strftime(DATE_FORMAT)}")

    def earliest_date(self, after_or_equal: datetime = None) -> date:
        """Gets the earliest date of source

        Parameters
        ----------
        after_or_equal : datetime
            optional parameter that sets a lower bound

        Returns
        -------
        date :
            earliest date
        """
        return get_earliest_date(
            directory=self.source,
            date_partition_name=self.partition_type,
            after_or_equal_to=after_or_equal
        )

    def latest_date(self, before_or_equal: datetime = None) -> date:
        """Gets the latest date of the source

        Parameters
        ----------
        before_or_equal : datetime
            optional parameters that sets an upper bound

        Returns
        -------
            latest date
        """
        return get_latest_date(
            directory=self.source,
            date_partition_name=self.partition_type,
            before_or_equal_to=before_or_equal
        )


def of_date_partitions(
        source: str,
        partition_name: str = "date",
        first_date: datetime = None,
        last_date: datetime = None
) -> TimeSeriesLocation:
    """gets a DateTimeSeriesLocation

    Parameters
    ----------
    source : str
        root path of data
    partition_name : str
        partition type (date, churn_date, or window_tail_date)
    first_date : datetime
        optional param to set the first date
    last_date : datetime
        optional param to set the last date

    Returns
    -------
    TimeSeriesLocation :
        DateTimeSeriesLocation
    """
    return DateTimeSeriesLocation(
        source=source,
        partition_type=partition_name,
        first_date=first_date,
        last_date=last_date,
    )


def of_ymd_partitions(
        source: str,
        first_date: datetime = None,
        last_date: datetime = None
) -> TimeSeriesLocation:
    """gets a YMDTimeSeriesLocation

    Parameters
    ----------
    source : str
        root path of data
    first_date : datetime
        optional param to set the first date
    last_date : datetime
        optional param to set the last date

    Returns
    -------
    YMDTimeSeriesLocation :
        YMDTimeSeriesLocation
    """
    return YMDTimeSeriesLocation(
        source=source,
        first_date=first_date,
        last_date=last_date,
    )
