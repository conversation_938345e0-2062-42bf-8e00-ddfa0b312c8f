"""
Maxmind IP Test Module.

@author: b<PERSON><PERSON>
@date: 2023-05-03
"""

from pyspark import SparkConf
from pyspark.sql import SparkSession, SQLContext, DataFrame
from pyspark.sql.functions import concat_ws, lit, split, regexp_extract, udf
from pyspark.sql.types import IntegerType
import datetime
from functools import reduce
import ipaddress
from datetime import datetime as dt
import boto3
from pyathena import connect


def calculate_usable_ips(network):
    """Calculate the number of usable IP addresses in a subnet."""
    subnet = ipaddress.ip_network(network)
    return subnet.num_addresses - 2


calculate_usable_ips_udf = udf(calculate_usable_ips, IntegerType())


class MaxmindIpTest:
    """Calculate the number of usable IP addresses in a subnet."""

    def __init__(self):
        conf = SparkConf()
        self.spark = SparkSession.builder.config(conf=conf).getOrCreate()
        self.sc = self.spark.sparkContext  # Derived SparkContext from SparkSession
        self.sqlContext = SQLContext(self.sc)  # Use the derived SparkContext

        self.client = boto3.client('s3')
        self.s3 = boto3.resource('s3')
        self.region = 'us-east-1'
        self.sns = boto3.client('sns', region_name='us-east-1')

        # s3 locations for the raw maxmind data
        self.mm_bucket = "externaldata-tomba-comlinkdata-com"
        self.ipv4_prefix = "maxmind/GeoIP2-ISP-Blocks-IPv4/"
        self.ipv6_prefix = "maxmind/GeoIP2-ISP-Blocks-IPv6/"

        self.ipv4_location = f"s3a://{self.mm_bucket}/{self.ipv4_prefix}"
        self.ipv6_location = f"s3a://{self.mm_bucket}/{self.ipv6_prefix}"

        # carrier lookup location
        self.cl_bucket = "d000-comlinkdata-com"
        self.cl_prefix = "prod/private/lookup-tables/broadband/carrier-lookup/"
        self.cl_location = f"s3a://{self.cl_bucket}/{self.cl_prefix}"
        self.cl_bucket_obj = self.s3.Bucket(name='d000-comlinkdata-com')

        self.mm_bucket_obj = self.s3.Bucket(name='externaldata-tomba-comlinkdata-com')

        self.table_bucket = "c400-athena-dev-comlinkdata-com"
        self.table_prefix = "data_ops/MaxMind/maxmind_ip_deltas_output/"

        self.upload_loc = "s3://c400-athena-dev-comlinkdata-com/data_ops/MaxMind/maxmind_ip_deltas_output/"
        self.athena_staging_dir = \
            's3://c400-athena-dev-comlinkdata-com/data_ops/4_0_daily_test_results/daily_test_staging/'

    def run(self):
        """Main method to run the Maxmind IP tests."""
        table_latest_date = self.get_latest_date_from_athena()
        if table_latest_date is not None:
            print(f"table_latest_date: {table_latest_date}")
        else:
            print("No data found in Athena table.")

        ipv4_dates = self.latest_file(self.mm_bucket, self.ipv4_prefix, table_latest_date)
        ipv6_dates = self.latest_file(self.mm_bucket, self.ipv6_prefix, table_latest_date)

        # Ensure dates match for ipv4 and ipv6
        assert ipv4_dates == ipv6_dates, "Mismatched dates in ipv4 and ipv6 files"

        for i in range(len(ipv4_dates)):

            curr_date = ipv6_dates[i]  # Set the current date for each iteration
            print(f"curr_date: {curr_date}")

            # determine if an update is needed
            if curr_date == table_latest_date:
                print("Dates match, no update needed")
            else:
                # continue updating
                # build the prefix to read in the data from the s3 locations
                ipv4_curr_prefix, ipv4_prev_prefix = self.build_prefixes(self.ipv4_location, ipv4_dates[i])
                ipv6_curr_prefix, ipv6_prev_prefix = self.build_prefixes(self.ipv6_location, ipv6_dates[i])
                # generate dfs for each data set
                ipv4_curr = self.generate_df(ipv4_curr_prefix)
                ipv4_prev = self.generate_df(ipv4_prev_prefix)
                ipv6_curr = self.generate_df(ipv6_curr_prefix)
                ipv6_prev = self.generate_df(ipv6_prev_prefix)
                # Split the IP address into parts and extract the subnet mask
                ipv4_curr = ipv4_curr.withColumn("ip_parts", split("network", r"\."))
                ipv4_curr = ipv4_curr.withColumn("subnet", regexp_extract("network", r"/(\d+)$", 1))

                ipv4_prev = ipv4_prev.withColumn("ip_parts", split("network", r"\."))
                ipv4_prev = ipv4_prev.withColumn("subnet", regexp_extract("network", r"/(\d+)$", 1))
                # Concatenate the first three parts of the IP address
                ipv4_curr = ipv4_curr.withColumn("first_three", concat_ws(".", ipv4_curr["ip_parts"][0],
                                                                          ipv4_curr["ip_parts"][1],
                                                                          ipv4_curr["ip_parts"][2]))

                ipv4_prev = ipv4_prev.withColumn("first_three", concat_ws(".", ipv4_prev["ip_parts"][0],
                                                                          ipv4_prev["ip_parts"][1],
                                                                          ipv4_prev["ip_parts"][2]))
                # drop the ip_parts columns because this won't join on the IPv6 version
                ipv4_curr = ipv4_curr.drop(ipv4_curr.ip_parts)
                ipv4_prev = ipv4_prev.drop(ipv4_prev.ip_parts)
                # extract the subnet mask using a regular expression
                ipv6_curr = ipv6_curr.withColumn("subnet", regexp_extract("network", r"/(\d+)$", 1))
                ipv6_prev = ipv6_prev.withColumn("subnet", regexp_extract("network", r"/(\d+)$", 1))
                # extract the first three sections of the IPv6 address
                ipv6_curr = ipv6_curr.withColumn("first_three",
                                                 regexp_extract("network", r"^(?:[a-fA-F0-9]{1,4}:){2}[a-fA-F0-9]{1,4}",
                                                                0))
                ipv6_prev = ipv6_prev.withColumn("first_three",
                                                 regexp_extract("network", r"^(?:[a-fA-F0-9]{1,4}:){2}[a-fA-F0-9]{1,4}",
                                                                0))
                # join the two current dfs and two prev dataframes together.
                df_prev = ipv4_prev.unionAll(ipv6_prev)
                df_curr = ipv4_curr.unionAll(ipv6_curr)
                # import the most recent carrier lookup data
                cl_list = self.file_list(self.cl_bucket_obj, self.cl_prefix)
                cl_location = f"s3a://{self.cl_bucket}/{max(cl_list)}"
                cl_df = self.spark.read.parquet(cl_location)
                # create local temporary views of the dataframes
                cl_df.createOrReplaceTempView("carrier_lookup")
                df_prev.createOrReplaceTempView("maxmind_previous")
                df_curr.createOrReplaceTempView("maxmind_current")
                # NETWORK CHANGES
                # return all results that had a network change isp names
                df_network_change = self.sqlContext.sql("""SELECT a.network AS curr_network, a.isp AS curr_isp,
                                                a.organization AS curr_org, b.network AS prev_network,
                                                b.isp AS prev_isp, b.organization AS prev_org
                                                FROM maxmind_current a
                                                JOIN maxmind_previous b
                                                ON a.network = b.network
                                                AND a.isp <> b.isp""")
                # create temp view of the network change df
                df_network_change.createOrReplaceTempView("network_change")
                # get the network changes where either side of the change is a carrier that we track in the carrier
                # lookup table
                df_universe_net_change = self.sqlContext.sql("""SELECT *
                                                FROM network_change
                                                WHERE (curr_isp IN (SELECT DISTINCT(mw_carrier) FROM carrier_lookup) OR
                                                prev_isp IN (SELECT DISTINCT(mw_carrier) FROM carrier_lookup))""")
                # add a note to the df to indicate that it's a network change
                df_universe_net_change = df_universe_net_change.withColumn("notes", lit("network change"))

                # NETWORK DROPS
                # need to join on the first three values of the ip for this
                df_network_drops = self.sqlContext.sql("""SELECT a.network AS curr_network, a.isp AS curr_isp,
                                                a.organization AS curr_org, b.network AS prev_network,
                                                b.isp AS prev_isp, b.organization AS prev_org
                                                FROM maxmind_current a
                                                LEFT JOIN maxmind_previous b
                                                ON a.first_three = b.first_three
                                                WHERE a.network IS NULL""")

                df_network_drops.createOrReplaceTempView("network_drops")

                df_universe_net_drops = self.sqlContext.sql("""SELECT *
                                                FROM network_drops
                                                WHERE (curr_isp IN (SELECT DISTINCT(mw_carrier) FROM carrier_lookup) OR
                                                prev_isp IN (SELECT DISTINCT(mw_carrier) FROM carrier_lookup))""")

                # add note to the network drops df
                df_universe_net_drops = df_universe_net_drops.withColumn("notes", lit("network drop"))

                # NETWORK ADDS
                df_network_adds = self.sqlContext.sql("""SELECT a.network AS curr_network, a.isp AS curr_isp,
                                                a.organization AS curr_org, b.network AS prev_network,
                                                b.isp AS prev_isp, b.organization AS prev_org
                                                FROM maxmind_current a
                                                LEFT JOIN maxmind_previous b
                                                ON a.network = b.network
                                                WHERE b.network IS NULL""")

                df_network_adds.createOrReplaceTempView("network_adds")

                df_universe_net_adds = self.sqlContext.sql("""SELECT *
                                                FROM network_adds
                                                WHERE (curr_isp IN (SELECT DISTINCT(mw_carrier) FROM carrier_lookup) OR
                                                prev_isp IN (SELECT DISTINCT(mw_carrier) FROM carrier_lookup))""")

                df_universe_net_adds = df_universe_net_adds.withColumn("notes", lit("network add"))

                # merge all dataframes
                final_df = reduce(DataFrame.unionAll,
                                  [df_universe_net_change, df_universe_net_drops, df_universe_net_adds])

                # add a date column to the df to indicate the current data this was done on
                final_df = final_df.withColumn("curr_date", lit(curr_date))

                # calculate the number of possible usable ips on each network
                # calculate_usable_ips_udf = udf(self.calculate_usable_ips, IntegerType())

                # add the number of usable ips to the final dataframe
                final_df = final_df.withColumn('usable_ips', calculate_usable_ips_udf(final_df['curr_network']))

                # write the final df to the s3 location
                final_df.write.parquet(f"{self.upload_loc}date={curr_date}/", mode="overwrite")

                # update the athena table
                self.repair_table()

                # send email alert that the athena table has been updated
                today = datetime.datetime.today().strftime('%Y-%m-%d')

                message = 'The Maxmind deltas table has been updated in Athena here: dataops.maxmind_ip_isp_results'
                self.sns.publish(
                    TopicArn='arn:aws:sns:us-east-1:282403293636:4_0_daily_validation',
                    Message=message,
                    Subject=f'Maxmind deltas data updated - {today}'
                )

    def latest_file(self, bucket, prefix, start_date):
        """returns a list of the files from the bucket and prefix location"""
        file_list = []
        result = self.client.list_objects(Bucket=bucket, Prefix=prefix, Delimiter='/')
        for o in result.get('CommonPrefixes'):
            f = o.get('Prefix')
            date_str = f[-11:-1]
            date = dt.strptime(date_str, '%Y-%m-%d')
            if date.date() > start_date:
                file_list.append(date_str)
        file_list.sort()
        return file_list

    def file_list(self, b, prefix):
        """Get a list of files in an S3 bucket with a specific prefix."""
        res = self.cl_bucket_obj.objects.filter(Prefix=prefix)
        f_list = []
        for obj in res:
            f_list.append(obj.key)
        for i in f_list:
            spl = i.split('/')[-1]
            if spl == "":
                f_list.remove(i)
        return f_list

    def repair_table(self):
        """Repair the Athena table."""
        cursor = connect(s3_staging_dir=self.athena_staging_dir, region_name='us-east-1',
                         work_group='DataOpsRole').cursor()
        cursor.execute('MSCK REPAIR TABLE dataops.maxmind_ip_isp_results;')

    def generate_df(self, prefix):
        """Generate DataFrame from a given S3 prefix."""
        return self.spark.read.option("header", "true").csv(prefix)

    def get_latest_date_from_athena(self):
        """Retrieve the latest date from the Athena table."""
        cursor = connect(s3_staging_dir=self.athena_staging_dir, region_name='us-east-1').cursor()
        cursor.execute('SELECT MAX(date) FROM dataops.maxmind_ip_isp_results')
        result = cursor.fetchone()
        return result[0] if result else None

    @staticmethod
    def build_prefixes(location, date):
        """Build S3 prefixes based on location and date."""
        # format for the dates in the list
        date_format = "%Y-%m-%d"

        # calculate the date 7 days prior
        curr_date = dt.strptime(date, date_format)
        prev_date = curr_date - datetime.timedelta(days=7)

        # build the prefixes
        curr_prefix = f"{location}/date={curr_date.strftime(date_format)}"
        prev_prefix = f"{location}/date={prev_date.strftime(date_format)}"

        return curr_prefix, prev_prefix


def main():
    """Main function to initiate Maxmind IP tests."""
    m = MaxmindIpTest()
    m.run()


if __name__ == '__main__':
    main()
