"""Broadband 2.0 All Churn"""

import functools
import logging
from datetime import datetime, timedelta

from common.args import get_arg_parser, get_or_default
from common.date import DATE_FORMAT
from common.path import with_slash, ymd_path
from common.time_series_location import YMDTimeSeriesLocation

from pyspark.sql import DataFrame, SparkSession, Window
from pyspark.sql.functions import (col, lit, row_number, substring, to_date,
                                   when, approx_count_distinct)


class AllChurn():
    """Broadband 2.0 All Churn"""

    def __init__(
        self,
        spark: SparkSession,
        high_confidence: str,
        cell_only: str,
        deactivations: str,
        activations: str,
        transfers: str,
        bb_churn: str,
        mvno: str
    ) -> None:
        """AllChurn Constructor

        Parameters
        ----------
        spark : SparkSession
            spark session
        high_confidence : str
            root of high confidance dataset
        cell_only : str
            root of cell only dataset
        deactivations : str
            root of deactivations bb2co (includes bb2fwa & bb2fco) cord thing dataset
        activations : str
            root of activations co2bb cord thing dataset
        transfers : str
            root of transfers dataset
        bb_churn : str
            root of bb_churn dataset

        Returns
        -------
        AllChurn
            AllChurn
        """
        self.spark = spark
        self.co_path = with_slash(cell_only)
        self.hc_path = with_slash(high_confidence)
        self.deactivations = with_slash(deactivations)
        self.activations = with_slash(activations)
        self.transfers_root = with_slash(transfers)
        self.bb_churn_root = with_slash(bb_churn)
        self.mvno_path = with_slash(mvno)
        self.mvno_tsl = YMDTimeSeriesLocation(self.mvno_path)

    def _read_in_churn_run_date(self, root, run_date, event_type):
        """
        Read in an upstream churn

        Read in generic churn for a given date and add an event_type column.
        Will also standardize CB column names.
        """
        temp = self.spark.read.parquet(ymd_path(root, run_date)).withColumn("date", lit(run_date))

        lcb = "losing_census_block" if "losing_census_block" in temp.columns else "losing_census_block_id"
        wcb = "winning_census_block" if "losing_census_block" in temp.columns else "winning_census_block_id"

        return temp\
            .select(
                col("ifa"),
                col("date"),
                col("losing_sp"),
                col("winning_sp"),
                col(lcb).alias("losing_census_block"),
                col(wcb).alias("winning_census_block"),
                col("churn_date"),
                lit(event_type).alias("event_type"))

    def _read_in_co(self, run_date, min_dates_seen=10):
        """Read in one month of Cell Only churn IFA's & apply min dates seen filter."""
        return self.spark.read.parquet(ymd_path(self.co_path, run_date))\
            .where(col("cell_only_days_count") >= min_dates_seen)\
            .select("ifa")

    def _read_in_hc(self, run_date, extra_cols=[], min_home_hours=4):
        """Read in one month of High Conf IFA's (and optionally extra columns) & apply min home hours seen filter."""
        return self.spark.read.parquet(ymd_path(self.hc_path, run_date))\
            .where(col("home_hour_date_count") >= min_home_hours)\
            .select("ifa", *extra_cols)

    def _read_in_mvno(self, run_date: datetime):
        latest_date = self.mvno_tsl.latest_date(before_or_equal=run_date)
        path = self.mvno_tsl.partition(latest_date)
        logging.info(f"Reading Wireless Carrier on {run_date}: {path}")
        return self.spark.read.parquet(path).select("ifa", "network", "brand").cache()

    @staticmethod
    def _join_df_on_ifa(a, b):
        """Helper function to join df a and df b on ifa"""
        return a.join(b, ["ifa"])

    @staticmethod
    def _union_all_elem_id(list_of_dfs):
        return functools.reduce(lambda a, b: a.select("element_id").unionByName(b.select("element_id")), list_of_dfs)

    @staticmethod
    def _union_all(list_of_dfs):
        return functools.reduce(lambda a, b: a.unionByName(b), list_of_dfs)

    def _attach_network_and_brand(self, implicit_df: DataFrame, wireless_carrier: DataFrame) -> DataFrame:
        """Attach network and brand columns from mvno/mno dataset on the ifa column

        Parameters
        ----------
        implicit_df : DataFrame
            dataframe to transform
        wireless_carrier : DataFrame
            DataFrame(ifa, network, brand) -- join on ifa

        Returns
        -------
            implicit_df with network and brand columns attached
        """
        return implicit_df.join(wireless_carrier, ["ifa"], "left")

    def _get_bb_to_possible_cell_ifas(self, run_date):
        """Get possible Broadband to Cell switchers for a run date"""
        co_curr = self._read_in_co(run_date)
        co_prev = self._read_in_co(run_date - timedelta(days=7))

        hc_prev_21 = self._read_in_hc(run_date - timedelta(days=21))
        hc_prev_28 = self._read_in_hc(run_date - timedelta(days=28))

        return functools.reduce(AllChurn._join_df_on_ifa, (co_curr, co_prev, hc_prev_28, hc_prev_21))

    def _get_cell_to_possible_bb_ifas(self, run_date):
        """Get possible Cell to BB switchers for a run date"""
        co_prev_28 = self._read_in_co(run_date - timedelta(days=28))
        co_prev_21 = self._read_in_co(run_date - timedelta(days=21))
        hc_prev = self._read_in_hc(run_date - timedelta(days=7))
        hc_curr = self._read_in_hc(run_date)

        return functools.reduce(AllChurn._join_df_on_ifa, (co_prev_28, co_prev_21, hc_prev, hc_curr))

    def _get_wireless_network_and_brand(self, run_date: datetime):
        """Wireless MNO and MVNO Dataset

        Parameters
        ----------
        run_date : datetime
            Day of wireless to get

        Returns
        -------
        DataFrame
            Wireless MNO (network) and MVNO (brand)
        """
        wireless = self._read_in_mvno(run_date)
        # previous step ensures case sensitivity on these values
        carriers = [
            'T-Mobile Wireless',
            'Verizon Wireless',
            'AT&T Wireless',
            'US Cellular'
        ]
        return wireless\
            .where(col("network").isin(carriers))

    def bb2co(self, run_date: datetime) -> DataFrame:
        """BB 2 CO cord things

        Read in bb2co (aka deactivations, this has been changed and now includes both bb2co & bb2fwa)
        cord thing for a single run date and make sure ifas are in the universe of possible bb to
        cell ifas

        Parameters
        ----------
        run_date : datetime
            Day of transfer to compute

        Returns
        -------
        DataFrame
            BB 2 CO dataframe
        """
        bb_to_possible_cell_ifas = self._get_bb_to_possible_cell_ifas(run_date)

        bb2co = self.spark.read.parquet(ymd_path(self.deactivations, run_date))\
            .withColumn("date", lit(run_date))\
            .select(
                col("ifa"),
                col("date"),
                col("losing_sp"),
                col("winning_sp"),
                col("losing_census_block"),
                col("winning_census_block"),
                col("churn_date"),
                col("churn_type").alias("event_type"))

        return bb2co\
            .join(bb_to_possible_cell_ifas, ["ifa"]) \
            .withColumn("element_id", col("ifa"))

    def co2bb(self, run_date: datetime) -> DataFrame:
        """CO 2 BB cord thing

        Read in co2bb cord thing for a single run date and make
        sure ifas are in the universe of possible cell to bb ifas

        Parameters
        ----------
        run_date : datetime
            Day of transfer to compute

        Returns
        -------
        DataFrame
            CO 2 BB dataframe
        """
        co2bb = self._read_in_churn_run_date(self.activations, run_date, 'co_to_bb')
        possible_cell_to_bb_ifas = self._get_cell_to_possible_bb_ifas(run_date)

        return co2bb\
            .join(possible_cell_to_bb_ifas, ["ifa"]) \
            .withColumn("element_id", col("ifa"))

    def transfers(
        self,
        run_date: datetime,
        bb2co: DataFrame,
        co2bb: DataFrame,
        move_dist: int = 10000
    ) -> DataFrame:
        """Read in transfers for a given run date and remove cord things

        Parameters
        ----------
        run_date : datetime
            Day of transfer to compute

        bb2co : DataFrame
            Cord thing, bb to cell only
        co2bb : DataFrame
            Cord thing, cell to bb
        move_dist : int, optional
            distance household must move to be a real move, by default 10000

        Returns
        -------
        DataFrame
            dataframe of transfers
        """
        prev_run_date = run_date - timedelta(days=7)
        prev_21_run_date = run_date - timedelta(days=21)
        prev_28_run_date = run_date - timedelta(days=28)

        # put all the cord things together
        anti_join_ifas = AllChurn._union_all_elem_id((bb2co, co2bb))\
            .withColumnRenamed("element_id", "ifa")
        # read in transfers churn & apply conditions
        churn_in = self._read_in_churn_run_date(self.transfers_root, run_date, 'transfers')\
            .where(col("move_dist") >= move_dist)\
            .where(col("winning_percent_block") >= 100)\
            .where(col("losing_percent_block") >= 100)\
            .join(anti_join_ifas, ["ifa"], "anti")

        # read in previous HC days
        hc_curr = self._read_in_hc(run_date, ["census_block_id", "ip"]).alias("hcm0")
        hc_prev = self._read_in_hc(prev_run_date, ["census_block_id", "ip"]).alias("hcm7")
        hc_prev_21 = self._read_in_hc(prev_21_run_date, ["census_block_id", "ip"]).alias("hcm21")
        hc_prev_28 = self._read_in_hc(prev_28_run_date, ["census_block_id", "ip"]).alias("hcm28")

        """
        From Maggie's sql:
        ip_0day <> ip_28day -- 2,818,345
        and ip_7day = ip_0day
        and ip_28day = ip_21day
        and substr(cb_7day, 1, 15) = substr(winning_census_block_id, 1, 15)
        and substr(cb_21day, 1, 15) = substr(losing_census_block_id, 1, 15)
        and substr(winning_census_block_id, 1, 11) <> substr(losing_census_block_id, 1, 11)
        """

        joined_df = functools.reduce(AllChurn._join_df_on_ifa, (churn_in, hc_curr, hc_prev, hc_prev_21, hc_prev_28))

        churn_in_hc_cols = joined_df\
            .where(col("hcm0.ip") != col("hcm28.ip"))\
            .where(col("hcm0.ip") == col("hcm7.ip"))\
            .where(col("hcm28.ip") == col("hcm21.ip"))\
            .where(substring(col("hcm7.census_block_id"), 1, 15) == substring(col("winning_census_block"), 1, 15))\
            .where(substring(col("hcm21.census_block_id"), 1, 15) == substring(col("losing_census_block"), 1, 15))\
            .where(substring(col("winning_census_block"), 1, 11) != substring(col("losing_census_block"), 1, 11)) \
            .select(
                col("ifa"),
                col("ifa").alias("element_id"),
                col("date"),
                col("losing_sp"),
                col("winning_sp"),
                col("losing_census_block"),
                col("winning_census_block"),
                col("churn_date"),
                col("event_type"))

        return churn_in_hc_cols

    def _single_day_bb_churn(self, run_date: datetime, short_move_dist: int) -> DataFrame:
        churn = self.spark.read.parquet(ymd_path(self.bb_churn_root, run_date))

        return churn \
            .select(
                col("household_id").alias("element_id"),
                lit(run_date).alias("date"),
                col("loser_sp_platform").alias("losing_sp"),
                col("winner_sp_platform").alias("winning_sp"),
                when(
                    (col("loser_census_block_id") != col("winner_census_block_id"))
                    & (col("distance") <= short_move_dist),
                    col("winner_census_block_id"))
                .otherwise(col("loser_census_block_id")).alias("losing_census_block"),
                col("winner_census_block_id").alias("winning_census_block"),
                col("churn_date"),
                lit('bb_churn').alias("event_type"),
                col("ifa"))

    def bb_churn(self, run_date: datetime, short_move_dist: int = 10000) -> DataFrame:
        """Broadband churn

        Read in run_date and its previous week day of broadband churn

        Parameters
        ----------
        run_date : datetime
            day to read in
        short_move_dist: int
            churn less than this distance in meters is considered a short move

        Returns
        -------
        DataFrame
            dataframe of bb churn
        """
        date_i = run_date
        all_day_dfs = []
        while True:
            all_day_dfs.append(self._single_day_bb_churn(date_i, short_move_dist))
            if date_i.weekday() == 5:  # saturday
                break
            date_i -= timedelta(days=1)
        return AllChurn._union_all(all_day_dfs)

    @staticmethod
    def _most_recent_churn(df) -> DataFrame:
        """Take in a churn df and only return rows that have the most recent churn date for each IFA"""
        window_ifa_churn_date = Window.partitionBy("ifa").orderBy(col("churn_date").desc())

        return df\
            .withColumn("rn", row_number().over(window_ifa_churn_date))\
            .where(col("rn") == 1)\
            .drop("rn")

    @staticmethod
    def _run_for_n_weeks_keep_most_recent_churn(start_date, n_weeks, parted_func) -> DataFrame:
        """Run a function for n previous weeks of input dates."""
        all_dfs = [parted_func(run_date=(start_date - timedelta(days=7 * i))) for i in range(n_weeks)]
        return AllChurn._most_recent_churn(AllChurn._union_all(all_dfs))

    @staticmethod
    def _run_for_n_weeks_keep_most_recent_churn_snap_fri(start_date, n_weeks, parted_func):
        """Run a function for n previous weeks of input dates. Run the 1st week on start_date and then snap to friday"""
        prev_fri = AllChurn._prev_sat(start_date) - timedelta(days=1)

        run_dates = [start_date] + [prev_fri - timedelta(days=7 * i) for i in range(n_weeks - 1)]
        all_dfs = [parted_func(run_date=run_date) for run_date in run_dates]
        return AllChurn._most_recent_churn(AllChurn._union_all(all_dfs))

    def _prev_sat(run_date: datetime):
        date_i = run_date
        while date_i.weekday() != 5:
            date_i -= timedelta(days=1)
        return date_i

    def remove_dupes(self, all_dfs: DataFrame) -> DataFrame:
        """
        Remove certain churn. Examples:

        id | isFwa | isBBChurn | isDupe | rank | note
        1  | no    | yes       | no     | 1    | keep - singles
        2  | no    | no        | no     | 1    | keep - singles
        3  | yes   | yes       | no     | 1    | keep - singles
        4  | yes   | no        | no     | 1    | keep - singles
        5  | no    | yes       | yes    | 1    | remove - non fwa follows the exact same rules
        5  | no    | no        | yes    | 2    | remove - non fwa follows the exact same rules
        6  | yes   | yes       | yes    | 1    | keep   - fwa should be kept on bb churn
        6  | yes   | no        | yes    | 2    | remove - fwa should be thrown away

        Parameters
        ----------
        all_dfs : DataFrame
            Dataframes that have been unioned together.At least bb churn and including transfers and coord things

        Returns
        -------
        DataFrame
            Subset of input with specific records removed
        """
        # todo: make this a param or read from s3
        fwa_spids: list = [6713, 6730, 6715]

        # Find any duplicate churn
        dups = all_dfs \
            .withColumn("isBBChurn", when(col("event_type") == "bb_churn", True).otherwise(False)) \
            .withColumn("isFWA",
                        when(col("winning_sp").isin(fwa_spids), True)
                        .when(col("losing_sp").isin(fwa_spids), True)
                        .otherwise(False))\
            .withColumn("element_id_count", approx_count_distinct("event_type").over(Window.partitionBy("element_id")))\
            .withColumn("isDupe", when(col("element_id_count") > 1, True).otherwise(False))\
            .withColumn("potentialFilterOut", ~(col("isBBChurn") & col("isFWA")))

        filter_out = dups\
            .filter(col("isDupe") & col("potentialFilterOut"))\
            .select(col("element_id"), col("event_type"))

        # Anti join with duplicate churn ifa's
        dedup = all_dfs.join(filter_out, ["element_id", "event_type"], "anti")
        return dedup

    def all_churn(self,
                  run_date: datetime,
                  n_history_weeks: int,
                  restatement_window: int,
                  bb_churn_only: bool = False) -> DataFrame:
        """
        Put all the churns together.

        Parameters
        ----------
        run_date : _type_
            Run date of all churn
        n_history_weeks : int,
            Number of weeks to run
        restatement_window : int
            Churn before this number of days will be dropped
        bb_churn_only : bool, optional (default False)
            If true, only return BB churns no transfers, co2bb, or bb2co

        Returns
        -------
        DataFrame
            DataFrame of all churn
        """
        prev_sat = AllChurn._prev_sat(run_date)
        wireless_carrier = self._get_wireless_network_and_brand(run_date)
        logging.info(f"Running All Churn for {run_date} and prev_sat {prev_sat}")
        churns = []

        if not bb_churn_only:
            # Cord things
            bb2co_df = AllChurn._run_for_n_weeks_keep_most_recent_churn(prev_sat, n_history_weeks, self.bb2co).cache()
            co2bb_df = AllChurn._run_for_n_weeks_keep_most_recent_churn(prev_sat, n_history_weeks, self.co2bb).cache()

            # Transfers
            transfers_df = AllChurn \
                ._run_for_n_weeks_keep_most_recent_churn(
                    prev_sat,
                    n_history_weeks,
                    functools.partial(self.transfers, bb2co=bb2co_df, co2bb=co2bb_df)) \
                .cache()
            churns.append(bb2co_df)
            churns.append(co2bb_df)
            churns.append(transfers_df)

        # BB Churn
        bb_churn_df = AllChurn \
            ._run_for_n_weeks_keep_most_recent_churn_snap_fri(run_date, n_history_weeks, self.bb_churn) \
            .cache()
        churns.append(bb_churn_df)

        # Join them all together and attach network and brand
        all_dfs = AllChurn \
            ._union_all(churns) \
            .join(wireless_carrier, ["ifa"], "left") \
            .withColumn("date", to_date("date")) \
            .withColumn("churn_date", to_date("churn_date"))

        # Anti join with duplicate churn ifa's
        dedup = self.remove_dupes(all_dfs)

        # Force all churn to be inside the restatement window'
        restatement_cliped =\
            dedup.where(to_date(col("churn_date")) >= (prev_sat - timedelta(days=restatement_window)))

        return restatement_cliped


def main():
    """Main entry point"""
    args = get_arg_parser().parse_args()
    logging.basicConfig(level=args.loglevel)
    config = args.config

    spark_ses = SparkSession.builder.appName("AllChurn").getOrCreate()

    ac = AllChurn(
        spark=spark_ses,
        **{k: config[k] for k in AllChurn.__init__.__code__.co_varnames[2:]})

    number_output_parts = get_or_default("output_partitions", config, 1)
    days_to_run = get_or_default("max_days_to_run", config, 1)
    n_history_weeks = get_or_default("n_history_weeks", config, 1)
    restatement_window = get_or_default("restatement_window", config, 60)
    bb_churn_only = get_or_default("bb_churn_only", config, False)

    day_zero = datetime.strptime(config["run_date"], DATE_FORMAT)
    for i in range(days_to_run):
        run_date = day_zero + timedelta(days=i)
        all_churn_out = ac.all_churn(run_date, n_history_weeks, restatement_window, bb_churn_only)
        all_churn_out.repartition(number_output_parts).write.parquet(ymd_path(config["output_location"], run_date))


if __name__ == "__main__":
    main()
