#!/usr/bin/env python3

"""Project build helper script

$ ./build.py -h
usage: build.py [-h] [-d] [-v] [-s] [-t] [-l] [-b BRANCH]

options:
  -h, --help            show this help message and exit
  -d, --debug           Print lots of debugging statements
  -v, --verbose         Be verbose
  -s, --ship-it         Upload to S3
  -t, --test            Run tests
  -l, --lint            Run linter (flake8)
  -b BRANCH, --branch BRANCH
"""

import argparse
import logging
import os
import subprocess
import sys
import zipfile

import boto3
import pytest
from setuptools import find_packages

s3 = boto3.resource('s3')

OUT_ZIP_NAME = "pyspark.zip"


def get_current_branch() -> str:
    """gets current git branch

    Returns
    -------
    str :
        current git branch
    """
    branch = subprocess.run(
        ['git', 'rev-parse', '--abbrev-ref', 'HEAD'],
        stdout=subprocess.PIPE
    ).stdout.decode('utf-8').strip()
    return branch


def get_arg_parser() -> argparse.ArgumentParser:
    """Get arguments

    Returns
    -------
    argparse.ArgumentParser
        argument parser
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '-d', '--debug',
        help="Print lots of debugging statements",
        action="store_const", dest="loglevel", const=logging.DEBUG,
        default=logging.WARNING,
    )
    parser.add_argument(
        '-v', '--verbose',
        help="Be verbose",
        action="store_const", dest="loglevel", const=logging.INFO,
    )
    parser.add_argument(
        '-s', '--ship-it',
        help="Upload to S3",
        action="store_true"
    )
    parser.add_argument(
        '-t', '--test',
        help="Run tests",
        action="store_true"
    )
    parser.add_argument(
        '-l', '--lint',
        help="Run linter (flake8)",
        action="store_true"
    )

    current_branch = get_current_branch()
    parser.add_argument(
        '-b', '--branch',
        help=f"Set branch name, default: {current_branch}",
        default=current_branch
    )
    parser.add_argument(
        '-3', '--s3-loc',
        help="Set the s3 upload path",
        default="s3://d000-emr-jobs/emr-jobs"
    )
    return parser


def split_s3(path: str) -> tuple[str, str]:
    """Split an s3 path into bucket and key

    Parameters
    ----------
    path : str
         path to be split

    Returns
    -------
    tuple[str, str]
        tuple of bucket & key
    """
    s3_path = path if path.endswith('/') else (path + '/')

    path_parts = s3_path.replace("s3://", "").split("/")
    bucket = path_parts.pop(0)
    prefix = "/".join(path_parts)
    return bucket, prefix


if __name__ == "__main__":
    args = get_arg_parser().parse_args()
    logging.basicConfig(level=args.loglevel)

    if args.test:
        retcode = pytest.main([])
        if retcode != pytest.ExitCode.OK:
            logging.error("Tests failed")
            sys.exit(retcode)

    if args.lint:
        try:
            process = subprocess.check_call(["flake8", "--docstring-convention", "numpy", "."])
        except subprocess.CalledProcessError as ex:
            logging.error("Linting failed")
            sys.exit(ex.returncode)

    if args.ship_it:
        modules_to_ship = [m for m in find_packages() if m not in ("tests")]

        with zipfile.ZipFile(OUT_ZIP_NAME, mode="w") as zipf:
            for module in modules_to_ship:
                for root, dirs, files in os.walk(module, topdown=False):
                    for name in files:
                        if name.endswith(".pyc"):
                            continue
                        zipf.write(os.path.join(root, name))

        full_s3_path = f"{args.s3_loc}/{args.branch}"

        bucket, prefix = split_s3(full_s3_path)

        s3.meta.client.upload_file(
            OUT_ZIP_NAME,
            bucket,
            prefix + OUT_ZIP_NAME)
        s3.meta.client.upload_file(
            'runner.py',
            bucket,
            prefix + "runner.py")
        logging.info(f"Shipped to: {full_s3_path}")
        os.remove(OUT_ZIP_NAME)
