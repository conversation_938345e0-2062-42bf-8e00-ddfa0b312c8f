"""Allow the easy running of modules with arguments"""
import sys
import importlib
import contextlib
import logging
import broadband_firstparty  # noqa: F401


@contextlib.contextmanager
def argv_replacer(new_argv: list):
    """Magic argv replacer

    Replaces argv with the supplied list

    Parameters
    ----------
    new_argv : list
        new input args
    """
    saved_argv = sys.argv
    sys.argv = [sys.argv[0]] + new_argv
    try:
        yield
    finally:
        sys.argv = saved_argv


def main():
    """Magic code to run pyspark modules

    Raises
    ------
    ex
        On module load failure
    """
    module_to_run = sys.argv[1]
    print(f"Running module: {module_to_run}")
    try:
        module = importlib.import_module(module_to_run)
    except ModuleNotFoundError as ex:
        logging.error(f"Couldn't load {module_to_run}")
        raise ex

    with argv_replacer(sys.argv[2:]):
        module.main()


if __name__ == '__main__':
    main()
