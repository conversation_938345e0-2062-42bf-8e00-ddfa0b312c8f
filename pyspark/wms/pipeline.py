"""WMS Raking Model Pipeline"""
import logging
from datetime import datetime

from common.args import get_arg_parser, get_or_default
from dateutil.relativedelta import relativedelta

from pyspark.sql import SparkSession
from pyspark.sql import functions as F


class Pipeline:
    """Class for holding demographic data"""

    def __init__(self,
                 car_cma_map,
                 dem_cma_map,
                 full_ocn_map,
                 wms_historical,
                 syndicated_demographics_path,
                 carrier_shares_path,
                 date,
                 sum_column,
                 spark):
        self.sum_column = sum_column
        self.car_cma_map = car_cma_map
        self.dem_cma_map = dem_cma_map
        self.full_ocn_map = full_ocn_map

        date_as_datetime = datetime.strptime(date, "%Y-%m-%d")
        year_q = Pipeline.get_quarter_year(date_as_datetime)
        if wms_historical:
            self.wms_historical = wms_historical.where(F.col('qtr') == year_q)
        else:
            self.wms_historical = None

        if syndicated_demographics_path:
            self.dem_data_raw = spark.read.parquet(f'{syndicated_demographics_path}/v={year_q}/')
        else:
            self.dem_data_raw = None

        self.carrier_shares_raw = spark.read.parquet(f'{carrier_shares_path}/customer_base_date={date}/')

    @staticmethod
    def get_quarter_year(date):
        """Helper function to get quarter and year from date"""
        q = (date.month - 1) // 3 + 1
        return f"{date.year}q{q}"

    def run_pipeline(self):
        """Runner function meant to generate model inputs, etc for a given time period

        Returns
        -------
        dem_data : List[Dict{string : Spark DF}]
            all monthly demographic data keyed by demographic type
        carrier_data : List[Spark DF]
            all carrier data shares keyed by date
        """
        if self.wms_historical:
            dem_data = Pipeline.remap_wms_historical_to_look_like_aggregated_syn_data(self.wms_historical)
        else:
            dem_data = Pipeline.aggregate_demographic_data(self.dem_cma_map, self.dem_data_raw)

        dem_data = Pipeline.format_demographic_data(dem_data)

        carrier_data = \
            Pipeline.format_carrier_share_data(self.full_ocn_map,
                                               self.car_cma_map,
                                               self.carrier_shares_raw,
                                               self.sum_column)

        dem_data, carrier_data = Pipeline.filter_dma_and_cma(dem_data, carrier_data)
        return dem_data, carrier_data

    @staticmethod
    def remap_wms_historical_to_look_like_aggregated_syn_data(wms_historical):
        """Remaps wms historical data tolook like aggregated syn data

        Parameters
        ----------
        wms_historical : Spark DF
            wms historical data

        Returns
        -------
        wms_historical : Spark DF
            wms historical data remapped to look like aggregated syn data
        """
        return wms_historical.selectExpr('dma',
                                         'cma',
                                         'inc_0_hh as inc_0',
                                         'inc_1_hh as inc_1',
                                         'inc_2_hh as inc_2',
                                         'inc_3_hh as inc_3',
                                         'black_pop',
                                         'asian_pop',
                                         'hispanic_pop',
                                         'white_pop',
                                         'age_0_pop as age_0',
                                         'age_1_pop as age_1',
                                         'age_2_pop as age_2',
                                         'age_3_pop as age_3')

    @staticmethod
    def aggregate_demographic_data(cma_map, dem_data_raw, filter_cols=['dma', 'cma']):
        """Aggregates and groups demographic data by dma,cma, age, race, income buckets

        Parameters
        ----------
        cma_map : Spark DF
            maps dmas and cmas from popstats_block_id's
        dem_data : List[Spark DF]
            all monthly demographic data listed by date

        Returns
        -------
        dem_datas : List[Spark DF]
            all monthly demographic data by cma listed by date
        """
        # get dma/cma granularity for demographic data
        dem_data_raw_cma = dem_data_raw.join(cma_map, on='serv_terr_blockid', how='left')
        dem_data_raw_cma = dem_data_raw_cma.filter(F.col('dma') != 999)

        # Format demographic data by dma/cma into relevant categories
        return dem_data_raw_cma.groupBy(filter_cols).agg(
            F.sum(F.col("inc_0_30k_hh") + F.col("inc_30_50k_hh")).alias("inc_0"),
            F.sum(F.col("inc_50_75k_hh") + F.col("inc_75_100k_hh")).alias("inc_1"),
            F.sum(F.col("inc_100_125k_hh") + F.col("inc_125_150k_hh")).alias("inc_2"),
            F.sum(F.col("inc_150k_plus_hh")).alias("inc_3"),
            F.sum(F.col("black_pop")).alias("black_pop"),
            F.sum(F.col("asian_pop")).alias("asian_pop"),
            F.sum(F.col("hispanic_pop")).alias("hispanic_pop"),
            F.sum(F.col("white_pop")).alias("white_pop"),
            F.sum(F.col("age_18_24_pop") + F.col("age_25_34_pop")).alias("age_0"),
            F.sum(F.col("age_35_44_pop") + F.col("age_45_54_pop")).alias("age_1"),
            F.sum(F.col("age_55_64_pop") + F.col("age_65_74_pop")).alias("age_2"),
            F.sum(F.col("age_75_84_pop") + F.col("age_85_plus_pop")).alias("age_3"))

    @staticmethod
    def _to_normalized_dem_segment_type(dem_data, filter_cols, cols, segment_type):
        dem_i = dem_data.select(filter_cols + cols)
        segment_col = f"{segment_type}_segment"

        inc = _cols_to_rows(dem_i, filter_cols, cols, segment_col, 'value').orderBy(['dma', segment_col]).cache()
        assert inc.where(F.col('value').isNull()).count() == 0, f"Demographics {segment_col} contains nulls"
        # normalize demographic data
        return _normalize(inc, 'value', 'V2')

    @staticmethod
    def format_demographic_data(dem_data, filter_cols=['dma', 'cma'], segment_and_cols={
            'inc': ['inc_0', 'inc_1', 'inc_2', 'inc_3'],
            'race': ['black_pop', 'asian_pop', 'hispanic_pop', 'white_pop'],
            'age': ['age_0', 'age_1', 'age_2', 'age_3']}):
        """Pulls s3 data for ocn and dma/cma maps, demographic and carrier data

        Parameters
        ----------
        q_dates : List[str]
            quarters grouped by count of dates in each
        dem_datas : List[Spark DF]
            all monthly demographic data by dma/cma listed by date

        Returns
        -------
        dem_data_segments : List[Dict{string : Spark DF}]
            all monthly demographic data keyed by demographic type
        """
        # get relevant demographic segments

        return {k: Pipeline._to_normalized_dem_segment_type(dem_data, filter_cols, cols, k)
                for k, cols in segment_and_cols.items()}

    @staticmethod
    def format_carrier_share_data(full_ocn_map, cma_map, carrier_shares_raw, sum_col):
        """Pulls s3 data for ocn and cma maps, demographic and carrier data

        Parameters
        ----------
        full_ocn_map : Spark DF
            maps ocn's to carrier names
        carrier_shares_raw : Spark DF
            all quarterly carrier data shares listed by date

        Returns
        -------
        carrier_shares_all : Spark DF
            all quarterly carrier data shares listed by date reformatted to represent tracked
            carriers with common names
        """
        # get carrier names for carrier shares data by dma,cma
        full_ocn_map = full_ocn_map.withColumnRenamed('OCN_COMMON_DIM_ID', 'primary_sp')

        carrier_shares_all = Pipeline._format_single_carrier_share(full_ocn_map, cma_map, carrier_shares_raw, sum_col)
        carrier_shares_all = Pipeline.carrier_shares_geo_overlap(carrier_shares_all)
        c_shares_finals = _normalize(carrier_shares_all, f'sum({sum_col})', 'V2')

        return c_shares_finals

    @staticmethod
    def _format_single_carrier_share(
            full_ocn_map, cma_map, carrier_share_raw,
            sum_col,
            filter_cols=['dma', 'cma'],
            filter_sps=[1, 2, 3, 4, 6, 7, 8, 609, 6052, 6105, 6495],
            tracfone_sps=[5, 6544, 6545, 6546, 6547]):
        wms_geo_df = carrier_share_raw.join(cma_map, carrier_share_raw["zip_rc_kblock"] == cma_map["rc_zip"], "left")

        wms_geo_sp_df = \
            wms_geo_df.join(full_ocn_map, wms_geo_df["current_holder_sp"] == full_ocn_map["primary_sp"], "left")\
            .withColumn("carrier", F.when((F.col("current_holder_sp").isin(*filter_sps)), F.col("common_name"))
                        .otherwise(F.when((F.col("current_holder_sp").isin(*tracfone_sps)), "Tracfone")
                        .otherwise("Other")))

        # Group by carrier, current_holder_plan_type_id, dma, and cma
        grouped_df = wms_geo_sp_df.groupBy("carrier", "current_holder_plan_type_id", "dma", "cma").agg(
            F.sum(sum_col)).alias(sum_col)
        carrier_shares_final = grouped_df.withColumn("new_common_name",
                                                     F.concat("carrier", F.lit("_"), "current_holder_plan_type_id"))

        distinct_carriers = carrier_shares_final.select('new_common_name').distinct()

        carrier_shares_all = carrier_shares_final.select(*filter_cols).distinct().crossJoin(
            distinct_carriers)\
            .join(carrier_shares_final, how='left', on=filter_cols + ['new_common_name'])\
            .fillna(0).orderBy(filter_cols + ['new_common_name'])

        if not carrier_shares_all.storageLevel.useMemory:
            carrier_shares_all = carrier_shares_all.cache()
        return carrier_shares_all

    @staticmethod
    def carrier_shares_geo_overlap(carrier_shares_all, filter_cols=['dma', 'cma']):
        """Makes geos consistent across all months"""
        geos = carrier_shares_all.select(*filter_cols).distinct()

        filt_carrier_shares = carrier_shares_all.join(geos, how='inner', on=filter_cols)
        return filt_carrier_shares

    @staticmethod
    def filter_dma_and_cma(dem_data_segment, carrier_shares_all):
        """Aggregates and groups demographic data by dma,cma, age, race, income buckets

        Parameters
        ----------
        dem_data_segments : List[Dict{string : Spark DF}]
            all monthly demographic data keyed by demographic type
        carrier_shares_alls : List[Spark DF]
            all monthly carrier data listed by date

        Returns
        -------
        dem_data_segments : List[Dict{string : Spark DF}]
            all monthly demographic data keyed by demographic type filtered by dma-cma
        carrier_shares_alls : List[Spark DF]
            all monthly carrier data listed by date filtered by dma-cma
        """
        dma_cma_filt = Pipeline._possible_dma_cmas(dem_data_segment['inc'], carrier_shares_all)
        carrier_shares_alls_out = Pipeline._add_dma_bar_cma_col(carrier_shares_all, dma_cma_filt)
        dem_data_segments_out = {k: Pipeline._add_dma_bar_cma_col(v, dma_cma_filt) for k, v in dem_data_segment.items()}

        return dem_data_segments_out, carrier_shares_alls_out

    @staticmethod
    def _possible_dma_cmas(dem_data_raw, car_cma_map):
        """Returns a list of possible dma-cma combinations

        Parameters
        ----------
        dem_data_raw : Spark DF
            raw demographic data
        car_cma_map : Spark DF
            map of carrier to cma

        Returns
        -------
        List[Dict{string : string}]
            list of possible dma-cma combinations
        """
        dem_data_raw = dem_data_raw.select('dma', 'cma').distinct()
        car_cma_map = car_cma_map.select('dma', 'cma').distinct()
        return dem_data_raw.join(car_cma_map, on=['dma', 'cma'], how='inner').cache()

    @staticmethod
    def _add_dma_bar_cma_col(df, dma_cma_filt):
        """Adds a dma|cma column to a dataframe

        Parameters
        ----------
        df : Spark DF
            dataframe to add column to
        dma_cma_filt : Spark DF
            dataframe of possible dma-cma combinations

        Returns
        -------
        Spark DF
            dataframe with added column
        """
        return df.join(dma_cma_filt, how='inner', on=['dma', 'cma'])\
                 .withColumn('dma|cma', F.concat(F.col('dma'), F.lit("|"), F.col('cma')))\
                 .drop('dma', 'cma')


def _normalize(df, normalize_col, new_col, sum_suffix="_sum"):
    """Normalize a column in a dataframe"""
    new_sum_col = normalize_col + sum_suffix
    return df.crossJoin(df.select(F.sum(normalize_col).alias(new_sum_col)))\
        .withColumn(new_col, F.col(normalize_col) / F.col(new_sum_col))\
        .drop(new_sum_col)


def _cols_to_rows(df, cols_to_keep, cols_to_melt, new_col_label, new_col_value, intermediate_col_suffix="_temp"):
    """Convert columns to rows"""
    cols_to_keep_as_col = [F.col(c) for c in cols_to_keep]
    temp_col = new_col_label + intermediate_col_suffix
    cols_out = cols_to_keep_as_col + \
        [F.explode(F.array([F.array(F.lit(c), F.col(c)) for c in cols_to_melt])).alias(temp_col)]

    return df.select(*cols_out)\
        .withColumn(new_col_label, F.col(temp_col)[0])\
        .withColumn(new_col_value, F.col(temp_col)[1].cast("double"))\
        .drop(temp_col)


def _get_spark_session(run_local=False):
    """Helper function to get spark session"""
    if run_local:  # todo remove this before merging to master
        spark_ses = SparkSession.builder.appName("WMS")\
            .master("local[16]")\
            .config("spark.executor.memory", "20g")\
            .config("spark.driver.memory", "20g")\
            .config("spark.sql.shuffle.partitions", "1")\
            .getOrCreate()
    else:
        spark_ses = SparkSession.builder.appName("WMS").getOrCreate()
    return spark_ses


def _run_spark_pipeline(spark_ses, date, sum_column,
                        car_cma_map, dem_cma_map, full_ocn_map,
                        wms_historical, syndicated_demographics_path, carrier_shares_path, output_path):
    """Helper function to run spark pipeline"""
    pipeline = Pipeline(
        car_cma_map=car_cma_map,
        dem_cma_map=dem_cma_map,
        full_ocn_map=full_ocn_map,
        wms_historical=wms_historical,
        syndicated_demographics_path=syndicated_demographics_path,
        carrier_shares_path=carrier_shares_path,
        date=date,
        sum_column=sum_column,
        spark=spark_ses)

    if output_path.endswith("/"):
        output_path = output_path[:-1]

    dem_data, carrier_data = pipeline.run_pipeline()

    for k, v in dem_data.items():
        v.repartition(1).write\
            .parquet(f"{output_path}/sum_col={sum_column}/date={date}/dem_data_{k}.parquet")

    carrier_data.repartition(1).write\
        .parquet(f"{output_path}/sum_col={sum_column}/date={date}/carrier_data.parquet")


def _month_generator(start_date, end_date):
    """Helper function to generate months between start and end date"""
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")
    while start <= end:
        yield start
        start += relativedelta(months=1)


def _year_generator(start_date, end_date):
    """Helper function to generate months between start and end date"""
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")
    while start <= end:
        yield start
        start += relativedelta(years=1)


def main():
    """Main function to run to att wireless market share"""
    args = get_arg_parser().parse_args()
    logging.basicConfig(level=args.loglevel)
    config = args.config

    end_date = config['end_date']
    start_date = config['start_date']

    run_local = get_or_default('run_local', config, False)

    is_ending_customers = get_or_default('is_ending_customers', config, True)

    spark_ses = _get_spark_session(run_local)

    car_cma_map = spark_ses.read.option("header", "true").csv(config['car_cma_map_path']).cache()
    dem_cma_map = spark_ses.read.parquet(config['dem_cma_map_path']).cache()
    full_ocn_map = spark_ses.read.option("header", "true").csv(config['ocn_map_path']).cache()
    if 'wms_historical_path' in config:
        wms_historical = spark_ses.read.parquet(config['wms_historical_path']).cache()
    else:
        wms_historical = None

    if 'syndicated_demographics_path' in config:
        syndicated_demographics_path = config['syndicated_demographics_path']
    else:
        syndicated_demographics_path = None

    if is_ending_customers:
        date_generator = _year_generator(start_date, end_date)
    else:
        date_generator = _month_generator(start_date, end_date)

    for d in date_generator:
        date = d.strftime("%Y-%m-%d")

        print(f"Running: {date}")

        _run_spark_pipeline(
            spark_ses, date,
            "ending_customers" if is_ending_customers else "total_losses",
            car_cma_map,
            dem_cma_map,
            full_ocn_map,
            wms_historical,
            syndicated_demographics_path,
            config['carrier_shares_path'],
            config['output_path'])


if __name__ == '__main__':
    main()
    print("Done")
