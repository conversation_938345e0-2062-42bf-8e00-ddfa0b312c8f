"""WMS Raking Model"""
import logging
from datetime import datetime

import io
import boto3
import os
import pandas as pd
import torch
from common.args import get_arg_parser, get_or_default
from dateutil.relativedelta import relativedelta
from torch import nn
from torch.nn.functional import normalize
from common.fileutils import S3FileUtils


class DemCarrierMixer(nn.Module):
    """Base model class

    For diagram, see third diagram here:
    https://app.diagrams.net/#G1KYzwaTa_fupxgOfdLEi5E8yimYJFSmZp

    """

    def __init__(self,
                 car_dim):
        super(DemCarrierMixer, self).__init__()
        # Currently, the dems are 4x4x4, so 64 segments
        # and there are 16 carriers, so 16x64 = 1024

        # multiply by each dem segment
        self.dem_full = nn.Sequential(
            nn.Linear(64, 64 * car_dim),
            nn.ReLU()
        )
        self.car_full = nn.Sequential(
            nn.Linear(car_dim, 64 * car_dim),
            nn.ReLU()
        )

    def forward(self, dems, car):
        """Method for building joint distribution predictions from residual inputs"""
        xd = self.dem_full(dems)
        xc = self.car_full(car)

        xcd = xc * xd
        # xcd = torch.nn.Softmax(xcd)
        xcd = torch.abs(normalize(xcd.squeeze(-1), p=1, dim=[1, 2]))

        return xcd


class DemMixer(nn.Module):
    """Base model class

    For diagram, see third diagram here:
    https://app.diagrams.net/#G1KYzwaTa_fupxgOfdLEi5E8yimYJFSmZp

    """

    def __init__(self):
        super(DemMixer, self).__init__()

        # multiply by each dem segment
        self.dem_age_feat = nn.Sequential(
            nn.Linear(4, 64),
            nn.ReLU()
        )
        self.dem_inc_feat = nn.Sequential(
            nn.Linear(4, 64),
            nn.ReLU()
        )
        self.dem_race_feat = nn.Sequential(
            nn.Linear(4, 64),
            nn.ReLU()
        )

    def forward(self, dem1, dem2, dem3, use_softmax=False):
        """Method for building joint distribution predictions from residual inputs"""
        xd1 = self.dem_age_feat(dem1)
        xd2 = self.dem_race_feat(dem2)
        xd3 = self.dem_inc_feat(dem3)

        # average the three demographic residuals
        xd_a = (xd1 + xd2 + xd3) / 3

        if use_softmax:
            xd_out = torch.softmax(xd_a.squeeze(-1), dim=1)
        else:
            xd_out = torch.abs(normalize(xd_a.squeeze(-1), p=1, dim=[1, 2]))

        return xd_out


def dem_loss_fn(n_month, output, target, geo_dim, use_abs=False):
    """Loss function for model - MSE, equally weights all residuals

    Parameters
    ----------
    n_month : int
        batch size for gradient updates
    output : Torch tensor
        prediction of the model
    target : List[Torch tensor]
        ground truth targets for each residual
    geo_dim : int
        dimension of geography

    Returns
    -------
    loss: float
        sum of MSE of carrier, age,race, income residuals
    """
    dem_age = output[0].reshape(n_month, geo_dim, 4, 4, 4).sum(axis=[3, 4])
    dem_race = output[0].reshape(n_month, geo_dim, 4, 4, 4).sum(axis=[2, 4])
    dem_inc = output[0].reshape(n_month, geo_dim, 4, 4, 4).sum(axis=[2, 3])

    if not use_abs:
        dem_age_loss = torch.mean((dem_age - target[0]) ** 2)
        dem_race_loss = torch.mean((dem_race - target[1]) ** 2)
        dem_inc_loss = torch.mean((dem_inc - target[2]) ** 2)
    else:
        dem_age_loss = torch.mean(torch.abs(dem_age - target[0]))
        dem_race_loss = torch.mean(torch.abs(dem_race - target[1]))
        dem_inc_loss = torch.mean(torch.abs(dem_inc - target[2]))

    loss = dem_age_loss + dem_race_loss + dem_inc_loss

    return loss


def dem_car_loss_fn(n_month, output, target, geo_dim, car_dim, use_abs=False):
    """Loss function for model - MSE, equally weights all residuals

    Parameters
    ----------
    n_month : int
        batch size for gradient updates
    output : Torch tensor
        prediction of the model
    target : List[Torch tensor]
        ground truth targets for each residual
    geo_dim : int
        dimension of geography
    car_dim : int
        dimension of geography

    Returns
    -------
    loss: float
        sum of MSE of carrier, age,race, income residuals
    """
    dem_age = output[0].reshape(n_month, geo_dim, car_dim, 4, 4, 4).sum(axis=[2, 4, 5])   # missing 3
    dem_race = output[0].reshape(n_month, geo_dim, car_dim, 4, 4, 4).sum(axis=[2, 3, 5])  # missing 4
    dem_inc = output[0].reshape(n_month, geo_dim, car_dim, 4, 4, 4).sum(axis=[2, 3, 4])   # missing 5
    car = output[0].reshape(n_month, geo_dim, car_dim, 4, 4, 4).sum(axis=[3, 4, 5])       # missing 2

    if use_abs:
        dem_age_loss = torch.mean(torch.abs(dem_age - target[0]))
        dem_race_loss = torch.mean(torch.abs(dem_race - target[1]))
        dem_inc_loss = torch.mean(torch.abs(dem_inc - target[2]))
        car_loss = torch.mean(torch.abs(car - target[3]))
    else:
        dem_age_loss = torch.mean((dem_age - target[0]) ** 2)
        dem_race_loss = torch.mean((dem_race - target[1]) ** 2)
        dem_inc_loss = torch.mean((dem_inc - target[2]) ** 2)
        car_loss = torch.mean((car - target[3]) ** 2)

    loss = dem_age_loss + dem_race_loss + dem_inc_loss + car_loss

    return loss


def dem_train(n_month, model, optim, gt_d, inputs, geo_dim, use_abs=False):
    """training procedure for the multlinear model

    Parameters
    ----------
    n_month : int
        batch size for first dimension of tensors
    model : MLM model class
        the model with relevant weights to be updated
    optim : Adam optimizer
        used to backpropagate errors through model weights
    gt_d : List[Torch tensor]
        ground truth targets for each residual
    inputs : List[Torch tensor]
        residuals from which we build a joint distribution
    geo_dim : int
        dimension of geography

    Returns
    -------
    loss: float
        sum of MSE of carrier, age,race, income residuals
    xcd: List[Torch tensor]
        joint distribution output of geopgraphy by carrier by demographic
    """
    # run the model forward on the data
    xd = model(inputs[0], inputs[1], inputs[2])

    # calculate the mse loss
    dem_loss = dem_loss_fn(n_month, [xd], [gt_d[2], gt_d[1], gt_d[0]], geo_dim, use_abs)
    # initialize gradients to zero
    optim.zero_grad()
    # backpropagate
    dem_loss.backward(retain_graph=True)
    # take a gradient step
    optim.step()
    return dem_loss, xd


def dem_car_train(n_month, model, optim, gt_d, inputs, geo_dim, car_dim, use_abs=False):
    """training procedure for the multlinear model

    Parameters
    ----------
    n_month : int
        batch size for first dimension of tensors
    model : MLM model class
        the model with relevant weights to be updated
    optim : Adam optimizer
        used to backpropagate errors through model weights
    gt_d : List[Torch tensor]
        ground truth targets for each residual
    inputs : List[Torch tensor]
        residuals from which we build a joint distribution
    geo_dim : int
        dimension of geography

    Returns
    -------
    loss: float
        sum of MSE of carrier, age,race, income residuals
    xcd: List[Torch tensor]
        joint distribution output of geopgraphy by carrier by demographic
    """
    # run the model forward on the data
    xcd = model(inputs[0], inputs[1])

    # calculate the mse loss
    full_loss = dem_car_loss_fn(n_month, [xcd], [gt_d[2], gt_d[1], gt_d[0], gt_d[3]], geo_dim, car_dim, use_abs)
    # initialize gradients to zero
    optim.zero_grad()
    # backpropagate
    full_loss.backward(retain_graph=True)
    # take a gradient step
    optim.step()
    return full_loss, [xcd]


def format_inputs(dem_data, carrier_data, geo_dim, car_dim):
    """Loss function for model - MSE, equally weights all residuals

    Parameters
    ----------
    dem_data : List[Dict{string : Spark DF}]
        all monthly demographic data keyed by demographic type
    carrier_data : List[Spark DF]
        all carrier data shares listed by date
    geo_dim : int
        dimension of geography
    car_dim : int
        dimension of carrier*plan type

    Returns
    -------
    Ground truth tensors: List[Torch tensor]
        all the ground truth residuals outputs will be compared to

    Input tensors: List[Torch tensor]
        flattened versions of the above tensors to be input into the model
    """
    dem_inc_t = torch.concat(tuple(torch.tensor(dem['inc'].select('V2').collect(),
                             dtype=torch.float).reshape(1, geo_dim, 4) for dem in dem_data), 0)
    dem_race_t = torch.concat(tuple(torch.tensor(dem['race'].select('V2').collect(),
                              dtype=torch.float).reshape(1, geo_dim, 4) for dem in dem_data), 0)
    dem_age_t = torch.concat(tuple(torch.tensor(dem['age'].select('V2').collect(),
                             dtype=torch.float).reshape(1, geo_dim, 4) for dem in dem_data), 0)

    car_t = torch.concat(tuple(torch.tensor(m.select('V2').collect(), dtype=torch.float)
                         .reshape(1, geo_dim, car_dim) for m in carrier_data), 0)

    # model inputs
    x_dem1 = torch.concat(tuple(torch.tensor(dem['inc'].select('V2').collect(), dtype=torch.float)
                          .reshape(1, geo_dim, 4) for dem in dem_data), 0)
    x_dem2 = torch.concat(tuple(torch.tensor(dem['race'].select('V2').collect(), dtype=torch.float)
                          .reshape(1, geo_dim, 4) for dem in dem_data), 0)
    x_dem3 = torch.concat(tuple(torch.tensor(dem['age'].select('V2').collect(), dtype=torch.float)
                          .reshape(1, geo_dim, 4) for dem in dem_data), 0)
    x_car = torch.concat(tuple(torch.tensor(m.select('V2').collect(), dtype=torch.float)
                         .reshape(1, geo_dim, car_dim) for m in carrier_data), 0)

    return [dem_inc_t, dem_race_t, dem_age_t, car_t], [x_dem1, x_dem2, x_dem3, x_car]


def format_inputs_pandas(dem_data, carrier_data, geo_dim, car_dim):
    """Loss function for model - MSE, equally weights all residuals

    Parameters
    ----------
    dem_data : List[Dict{string : Spark DF}]
        all monthly demographic data keyed by demographic type
    carrier_data : List[Spark DF]
        all carrier data shares listed by date
    geo_dim : int
        dimension of geography
    car_dim : int
        dimension of carrier*plan type

    Returns
    -------
    Ground truth tensors: List[Torch tensor]
        all the ground truth residuals outputs will be compared to

    Input tensors: List[Torch tensor]
        flattened versions of the above tensors to be input into the model
    """
    dem_inc_t = torch.concat(tuple(torch.tensor(dem['inc']['V2'],
                             dtype=torch.float).reshape(1, geo_dim, 4) for dem in dem_data), 0)
    dem_race_t = torch.concat(tuple(torch.tensor(dem['race']['V2'],
                              dtype=torch.float).reshape(1, geo_dim, 4) for dem in dem_data), 0)
    dem_age_t = torch.concat(tuple(torch.tensor(dem['age']['V2'],
                             dtype=torch.float).reshape(1, geo_dim, 4) for dem in dem_data), 0)

    car_t = torch.concat(tuple(torch.tensor(m['V2'], dtype=torch.float)
                         .reshape(1, geo_dim, car_dim) for m in carrier_data), 0)

    # model inputs
    x_dem1 = torch.concat(tuple(torch.tensor(dem['inc']['V2'], dtype=torch.float)
                          .reshape(1, geo_dim, 4) for dem in dem_data), 0)
    x_dem2 = torch.concat(tuple(torch.tensor(dem['race']['V2'], dtype=torch.float)
                          .reshape(1, geo_dim, 4) for dem in dem_data), 0)
    x_dem3 = torch.concat(tuple(torch.tensor(dem['age']['V2'], dtype=torch.float)
                          .reshape(1, geo_dim, 4) for dem in dem_data), 0)
    x_car = torch.concat(tuple(torch.tensor(m['V2'], dtype=torch.float)
                         .reshape(1, geo_dim, car_dim) for m in carrier_data), 0)

    return [dem_inc_t, dem_race_t, dem_age_t, car_t], [x_dem1, x_dem2, x_dem3, x_car]


def build_model(geo_dim,
                car_dim,
                gt_d,
                inputs,
                n_months,
                num_iterations,
                use_abs=True,
                cuda=True,
                dem_model=None,
                dem_carrier_model=None):
    """Loss function for model - MSE, equally weights all residuals

    Parameters
    ----------
    dates : List[str]
        a list of dates relevant for the dimensions of the model output
    dem_data : List[Dict{string : Spark DF}]
        all monthly demographic data keyed by demographic type
    carrier_data : List[Spark DF]
        all carrier data shares listed by date

    Returns
    -------
    full estimates : Pandas DF
        a table of month, carrier, age, race, income, and share across month
    """
    dem_model = DemMixer() if dem_model is None else dem_model
    if cuda:
        torch.cuda.set_device(0)
        dem_model.cuda()
    optim_init = torch.optim.Adam(dem_model.parameters(), lr=0.001, amsgrad=True)

    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optim_init,
        max_lr=0.001,
        steps_per_epoch=num_iterations // 10,
        epochs=10)

    for j in range(num_iterations):
        dem_loss, dem_mix = dem_train(n_months, dem_model, optim_init, gt_d, inputs, geo_dim, use_abs)

        if j % 10000 == 0:
            print("Dem [iteration %04d, lr=%.7f] loss: %.9f" % (j, optim_init.param_groups[0]["lr"], dem_loss.item()))

        scheduler.step()

    print(f"Demographic model done. loss: {dem_loss.item()} shape: {dem_mix.shape}")

    # train a model with output of demographics mixer and carrier input
    dem_carrier_model = DemCarrierMixer(car_dim) if dem_carrier_model is None else dem_carrier_model
    if cuda:
        dem_carrier_model.cuda()
    optim = torch.optim.Adam(dem_carrier_model.parameters(), lr=0.0001, amsgrad=True)

    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optim,
        max_lr=0.0001,
        steps_per_epoch=num_iterations // 10,
        epochs=10)
    for j in range(num_iterations):
        all_loss, y_pred = \
            dem_car_train(n_months, dem_carrier_model, optim, gt_d, [dem_mix, inputs[3]], geo_dim, car_dim, use_abs)

        if j % 10000 == 0:
            print("Car [iteration %04d, lr=%.7f ] loss: %.9f" % (j, optim.param_groups[0]["lr"], all_loss.item()))

        scheduler.step()

    print(f"Dem final loss: {dem_loss.item()}")
    print(f"Carrier model done. loss: {all_loss.item()} shape: {y_pred[0].shape}")
    return y_pred, dem_model, dem_carrier_model, dem_mix


def return_estimates(dates, carrier_data_pd, optimal_params, cuda=True):
    """Loss function for model - MSE, equally weights all residuals

    Parameters
    ----------
    dates : List[str]
        a list of dates relevant for the dimensions of the model output
    carrier_data : List[Spark DF]
        all carrier data shares listed by date
    optimal_params : Torch tensor
        direct model output formatted to append to an interpretable table

    Returns
    -------
    estimates_m : Pandas DF
        a table of month, carrier, age, race, income, and share across month
    """
    inc_dim = pd.DataFrame(['inc_0', 'inc_1', 'inc_2', 'inc_3'], columns=['inc'])
    inc_dim['key'] = 1
    race_dim = pd.DataFrame(['asian_pop', 'black_pop', 'hispanic_pop', 'white_pop'], columns=['race'])
    race_dim['key'] = 1
    age_dim = pd.DataFrame(['age_0', 'age_1', 'age_2', 'age_3'], columns=['age'])
    age_dim['key'] = 1
    month_dim = pd.DataFrame(dates, columns=['month'])
    month_dim['key'] = 1

    carrier_data_pd['key'] = 1

    mc = pd.merge(month_dim, carrier_data_pd, on='key')
    mca = pd.merge(mc, age_dim, on='key')
    mcar = pd.merge(mca, race_dim, on='key')
    estimate_m = pd.merge(mcar, inc_dim, on='key').drop("key", 1)

    if cuda:
        est_vals = optimal_params[0].detach().cpu().flatten().numpy()
    else:
        est_vals = optimal_params[0].detach().flatten().numpy()

    print(f"Estimate shape: {est_vals.shape}")
    print(estimate_m)
    print(est_vals)

    estimate_m['estimate'] = est_vals
    print(estimate_m)
    estimate_m['pct_estimate'] = estimate_m['estimate'] / estimate_m['estimate'].sum()
    return estimate_m


def _month_generator(start_date, end_date):
    """Helper function to generate months between start and end date"""
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")
    while start <= end:
        yield start
        start += relativedelta(months=1)


def _torch_model_save(model, path):
    """Helper function to save torch model"""
    if not path.startswith("s3://"):
        if not os.path.exists(path):
            os.makedirs(path)
        torch.save(model.state_dict(), path)
    else:
        buffer = io.BytesIO()
        torch.save(model.state_dict(), buffer)

        bucket, prefix = S3FileUtils.split_s3(path)
        if prefix.endswith("/"):
            prefix = prefix[:-1]
        print(f"Saving model to {bucket} {prefix}")
        s3 = boto3.client('s3')
        s3.put_object(Bucket=bucket, Key=prefix, Body=buffer.getvalue())


def _torch_model_load(path, model):
    """Helper function to load torch model"""
    if not path.startswith("s3://"):
        model.load_state_dict(torch.load(path))
    else:
        bucket, prefix = S3FileUtils.split_s3(path)
        if prefix.endswith("/"):
            prefix = prefix[:-1]
        print(f"Loading model from {bucket} {prefix}")
        s3 = boto3.client('s3')
        obj = s3.get_object(Bucket=bucket, Key=prefix)
        try:
            model.load_state_dict(torch.load(io.BytesIO(obj['Body'].read())))
        except RuntimeError as e:
            print(e, "\nError loading previous model... skipping")
            return None
    return model


def _run_model(run_date, input_path, sum_col, output_path, previous_output, cuda, num_iterations):
    print(f"Reading {run_date}")
    demos = ['inc', 'race', 'age']
    dem_data = [{do: pd.read_parquet(f"{input_path}/sum_col={sum_col}/date={run_date}/dem_data_{do}.parquet")
                 for do in demos}]
    carrier_data = [pd.read_parquet(f"{input_path}/sum_col={sum_col}/date={run_date}/carrier_data.parquet")]
    car_dim = len(pd.unique(carrier_data[-1]["new_common_name"]))

    geo_dim = dem_data[0]['inc'].shape[0] // 4

    dem_data[0]['inc'].sort_values(by=['dma|cma', 'inc_segment'], inplace=True)
    dem_data[0]['age'].sort_values(by=['dma|cma', 'age_segment'], inplace=True)
    dem_data[0]['race'].sort_values(by=['dma|cma', 'race_segment'], inplace=True)
    carrier_data[0].sort_values(by=['dma|cma', 'new_common_name'], inplace=True)

    gt_d, inputs = format_inputs_pandas(dem_data, carrier_data, geo_dim, car_dim)

    if cuda:
        gt_d = [d.cuda() for d in gt_d]
        inputs = [d.cuda() for d in inputs]

    n_months = len(dem_data)
    logging.info(f"Model Size: {geo_dim} {n_months} {carrier_data[0].shape}")

    if previous_output is not None:
        logging.info(f"Loading previous output from {previous_output}")
        if previous_output.endswith("/"):
            previous_output = previous_output[:-1]
        dem_model = _torch_model_load(previous_output + "/dem_model.pt", DemMixer())
        dem_carrier_model = _torch_model_load(previous_output + "/dem_carrier_model.pt", DemCarrierMixer(car_dim))
    else:
        dem_model = None
        dem_carrier_model = None

    optimal_params, dem_model, dem_carrier_model, dem_mix = build_model(
        geo_dim, car_dim, gt_d, inputs, n_months, num_iterations,
        cuda=cuda, dem_model=dem_model, dem_carrier_model=dem_carrier_model)
    logging.info("Done with model build")

    opt_parm_0 = optimal_params[0].cpu() if cuda else optimal_params[0]
    logging.info(f"Optimal Params: { opt_parm_0.sum()}")

    if (opt_parm_0.sum() - 3) > 0.0001:
        logging.warning("Optimal params don't sum to 3!")

    df_output = return_estimates([run_date], carrier_data[0], optimal_params, cuda=cuda)
    logging.info("Done with estimates")

    output_dir = f"{output_path}/sum_col={sum_col}/date={run_date}/"

    if not output_dir.startswith("s3://") and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    logging.info(f"Writing data to the path: {output_dir}")
    df_output.to_parquet(output_dir + "estimates.parquet")

    if dem_model is not None:
        _torch_model_save(dem_model, output_dir + "dem_model.pt")
        _torch_model_save(dem_carrier_model, output_dir + "dem_carrier_model.pt")

        # Turn off saving of dem_mix outside of local testing
        # dem_mix_output = dem_mix.cpu() if cuda else dem_mix
        # with open(output_dir + "dem_mix.np", 'wb') as f:
        #     np.save(f, dem_mix_output.detach().numpy())


def main():
    """Main function to run to att wireless market share model estimation"""
    args = get_arg_parser().parse_args()
    logging.basicConfig(level=args.loglevel)
    config = args.config

    start_date = config['start_date']
    end_date = config['end_date']
    input_path = config['input_path']
    if input_path.endswith('/'):
        input_path = input_path[:-1]
    num_iterations = get_or_default('num_iterations', config, 1000000)
    cuda = get_or_default('cuda', config, False)
    previous_output = get_or_default('previous_output', config, None)
    is_ending_customers = get_or_default('is_ending_customers', config, True)

    if is_ending_customers:
        sum_col = 'ending_customers'
    else:
        sum_col = 'total_losses'

    output_path = config["output_path"]
    if output_path.endswith("/"):
        output_path = output_path[:-1]

    for run_date in _month_generator(start_date, end_date):
        _run_model(run_date.strftime("%Y-%m-%d"),
                   input_path,
                   sum_col,
                   output_path,
                   previous_output,
                   cuda,
                   num_iterations)


if __name__ == '__main__':
    main()
    print("Done")
