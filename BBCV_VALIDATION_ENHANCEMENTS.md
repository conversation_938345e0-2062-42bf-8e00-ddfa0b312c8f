# BBCV Data Preparation Job - Validation Enhancements

## Overview

This document describes the validation enhancements applied to the `BbcvDataPreparationJob` to ensure data integrity across three critical scenarios:

1. **No gaps in time series data set**
2. **History remains static**
3. **Pipeline run overwrites only dates which were asked to reprocess**

## Implementation Details

### 1. Time Series Gap Validation

**Purpose**: Ensures there are no missing dates in the time series data.

**Implementation**:
- Uses the existing `validateDataSetTimeGap` method from `ValidationCases`
- Validates that all expected dates are present in the dataset
- Throws `IllegalStateException` if gaps are detected

**Code Location**: `validateTimeSeriesIntegrity` method

```scala
// Scenario 1: Validate no gaps in time series data set
try {
  validateDataSetTimeGap(currentData, dateColumn)
  logger.info("✓ Time series gap validation passed - no missing dates detected")
} catch {
  case e: IllegalArgumentException =>
    logger.error(s"✗ Time series gap validation failed: ${e.getMessage}")
    throw new IllegalStateException(s"Time series validation failed - gaps detected in data: ${e.getMessage}")
}
```

### 2. History Integrity Validation

**Purpose**: Ensures that historical data remains unchanged and only the requested processing dates are being overwritten.

**Implementation**:
- Compares existing output data with current processing data
- Validates that data outside the processing date range remains untouched
- Checks schema compatibility between existing and current data
- Logs processing scope for transparency

**Code Location**: `validateHistoryIntegrity` method

**Key Features**:
- Checks if existing output path exists
- Reads existing data and filters historical data (outside processing range)
- Validates schema compatibility
- Provides detailed logging of processing scope

### 3. Processing Scope Validation

**Purpose**: Validates that the processing only affects the intended date partitions.

**Implementation**:
- Compares actual output dates with expected date range
- Identifies unexpected dates in output
- Identifies missing expected dates
- Provides warnings for scope deviations

**Code Location**: `validateProcessingScopeIntegrity` method

## Integration Points

### Main Job Execution

The validation is integrated into the main `runJob` method and executes before writing output to S3:

```scala
// Apply validation for the three scenarios if applicable
logger.info("Applying time series integrity validation...")

// Validate each output dataset
validateTimeSeriesIntegrity(providerWinsUnion, Some(config.outputConfig.providerWinsPath.toString), generalDateRange, "month_end_date")
validateTimeSeriesIntegrity(providerLossesUnion, Some(config.outputConfig.providerLossesPath.toString), generalDateRange, "month_end_date")
validateTimeSeriesIntegrity(nonProviderWinsUnion, Some(config.outputConfig.nonProviderWinsPath.toString), generalDateRange, "month_end_date")
validateTimeSeriesIntegrity(nonProviderLossesUnion, Some(config.outputConfig.nonProviderLossesPath.toString), generalDateRange, "month_end_date")

logger.info("✓ All time series integrity validations passed")

// Additional validation: Ensure processing scope is correct
validateProcessingScopeIntegrity(providerWinsUnion, generalDateRange, "month_end_date")
validateProcessingScopeIntegrity(providerLossesUnion, generalDateRange, "month_end_date")
validateProcessingScopeIntegrity(nonProviderWinsUnion, generalDateRange, "month_end_date")
validateProcessingScopeIntegrity(nonProviderLossesUnion, generalDateRange, "month_end_date")

logger.info("✓ All processing scope validations passed")
```

### Output Datasets Validated

The validation is applied to all four output datasets:
- `providerWinsUnion`
- `providerLossesUnion`
- `nonProviderWinsUnion`
- `nonProviderLossesUnion`

## Error Handling

### Validation Failures

- **Time Series Gaps**: Throws `IllegalStateException` with detailed error message
- **History Integrity Issues**: Throws `IllegalStateException` with context
- **Processing Scope Deviations**: Logs warnings but does not fail the job

### Logging

The validation provides comprehensive logging:
- ✓ Success indicators for passed validations
- ✗ Error indicators for failed validations
- ⚠ Warning indicators for scope deviations
- Detailed information about processing dates and ranges

## Configuration

### Required Parameters

- `currentData`: Dataset being processed
- `existingOutputPath`: Optional path to existing output for history validation
- `processingDateRange`: Date range being processed
- `dateColumn`: Name of the date column (defaults to "the_date" or "month_end_date")

### Optional Features

- History validation only runs if `existingOutputPath` is provided
- Schema validation provides warnings for evolution but doesn't fail
- Processing scope validation provides warnings for deviations

## Testing

A comprehensive test suite has been created in `BbcvDataPreparationJobValidationSpec.scala` covering:

- Successful validation scenarios
- Gap detection in time series
- Processing scope validation
- Error handling

## Benefits

1. **Data Quality Assurance**: Ensures no gaps in time series data
2. **Historical Data Protection**: Prevents accidental modification of historical data
3. **Processing Transparency**: Clear logging of what dates are being processed
4. **Early Error Detection**: Catches data integrity issues before output
5. **Operational Confidence**: Provides clear validation status for monitoring

## Usage Notes

- Validation runs automatically as part of the job execution
- No additional configuration required
- Existing job behavior is preserved
- Validation can be extended for additional scenarios as needed

---

# OptimizationPreparationJob - Validation Enhancements

## Overview

The same validation enhancements have been applied to the `OptimizationPreparationJob` in the tier2 package. This job processes optimization input data and benefits from the same three validation scenarios.

## Job Characteristics

The `OptimizationPreparationJob`:
- Processes time series data with `month_end_date` partitions
- Uses `LocalDateRange` for date range processing
- Writes to S3 with dynamic partition overwrite mode
- Produces a single output dataset: `NonMoverWinsPerLossOptimizationInput`

## Applied Validations

### 1. Time Series Gap Validation
- Validates the final `nonMoversWinPerLoss` dataset for date gaps
- Ensures all expected dates are present in the output

### 2. History Integrity Validation
- Compares with existing output at `config.nonMoverWinsPerLossInputPath`
- Validates that historical data outside the processing range remains unchanged
- Provides schema compatibility checking

### 3. Processing Scope Validation
- Ensures output contains exactly the expected date range
- Warns about unexpected or missing dates

## Integration Points

```scala
// Apply validation for the three scenarios if applicable
logger.info("Applying time series integrity validation...")

validateTimeSeriesIntegrity(
  nonMoversWinPerLoss,
  Some(config.nonMoverWinsPerLossInputPath.toString),
  dateRange,
  "month_end_date"
)

logger.info("✓ Time series integrity validation passed")

// Additional validation: Ensure processing scope is correct
validateProcessingScopeIntegrity(nonMoversWinPerLoss, dateRange, "month_end_date")

logger.info("✓ Processing scope validation passed")
```

## Key Differences from BbcvDataPreparationJob

1. **Single Output**: Only validates one output dataset instead of four
2. **Date Column**: Uses `month_end_date` instead of `the_date`
3. **Simpler Integration**: Less complex due to single output stream

## Testing

A comprehensive test suite has been created in `OptimizationPreparationJobValidationSpec.scala` covering the same validation scenarios as the BBCV job.

## Benefits for OptimizationPreparationJob

1. **Data Quality**: Ensures optimization input data has no gaps
2. **Historical Protection**: Prevents accidental modification of historical optimization data
3. **Processing Transparency**: Clear logging of processing scope
4. **Consistency**: Same validation patterns across tier1 and tier2 jobs

---

# Code Refactoring - Shared Validation Methods

## Overview

To improve code maintainability and reusability, the validation methods have been refactored and moved to a shared location in `ValidationCases.scala`. This eliminates code duplication between the BbcvDataPreparationJob and OptimizationPreparationJob.

## Refactoring Changes

### 1. ValidationCases.scala Enhancements

**Added Methods**:
- `validateTimeSeriesIntegrity`: Main validation orchestrator for all three scenarios
- `validateHistoryIntegrity`: Validates historical data remains unchanged
- `validateProcessingScopeIntegrity`: Validates processing scope integrity

**Updated Imports**:
- Added `SparkSession` import for implicit parameter support
- Extended `LazyLogging` trait for consistent logging

### 2. Job-Specific Changes

**BbcvDataPreparationJob**:
- **Removed**: Duplicate validation method definitions (150+ lines of code)
- **Updated**: Import statement to include shared validation methods
- **Preserved**: All validation functionality through shared methods

**OptimizationPreparationJob**:
- **Removed**: Duplicate validation method definitions (110+ lines of code)
- **Updated**: Import statement to include shared validation methods
- **Preserved**: All validation functionality through shared methods

### 3. Import Updates

Both jobs now use:
```scala
import com.opensignal.emrjobs.spark.firstpartyingestion.ValidationCases.{
  validateDataSetTimeGap,
  validateTimeSeriesIntegrity,
  validateProcessingScopeIntegrity
}
```

## Benefits of Refactoring

1. **Code Reusability**: Single source of truth for validation logic
2. **Maintainability**: Changes to validation logic only need to be made in one place
3. **Consistency**: Identical validation behavior across all jobs
4. **Reduced Duplication**: Eliminated 260+ lines of duplicate code
5. **Centralized Testing**: Validation logic can be tested once in ValidationCases
6. **Future Extensibility**: New jobs can easily adopt the same validation patterns

## Usage Pattern

Jobs now simply call the shared validation methods:

```scala
// Apply validation for the three scenarios if applicable
validateTimeSeriesIntegrity(
  dataset,
  Some(outputPath),
  dateRange,
  "date_column_name"
)

validateProcessingScopeIntegrity(dataset, dateRange, "date_column_name")
```

This refactoring maintains all existing functionality while significantly improving code organization and maintainability.

---

# Architectural Change - History Validation Moved to Redshift Layer

## Overview

Based on requirements, the history validation has been moved from the S3 write layer (tier1/tier2 jobs) to the Redshift write layer (tier3 job). This ensures that history integrity is validated specifically before database writes, not file writes.

## Architectural Changes

### 1. Tier1/Tier2 Jobs (S3 Layer)
**BbcvDataPreparationJob & OptimizationPreparationJob**:
- **Removed**: Full validation suite including history validation
- **Kept**: Basic time series gap validation for S3 output quality
- **Purpose**: Ensure S3 outputs have no date gaps

```scala
// S3 Layer - Basic validation only
validateDataSetTimeGap(dataset, "date_column")
```

### 2. Tier3 Job (Redshift Layer)
**FirstPartyRedshiftUploadJob**:
- **Added**: Comprehensive validation suite before Redshift writes
- **Added**: Redshift-specific history validation
- **Purpose**: Ensure database integrity before writes

```scala
// Redshift Layer - Comprehensive validation
validateTimeSeriesIntegrity(dataset, None, dateRange, "date_column")
validateProcessingScopeIntegrity(dataset, dateRange, "date_column")
validateRedshiftHistoryIntegrity(dataset, tableName, dateRange, "date_column", rsConfig)
```

## New Validation Method

### validateRedshiftHistoryIntegrity

A new validation method specifically designed for Redshift history validation:

**Features**:
- Connects directly to Redshift to read existing table data
- Validates that historical data outside processing range remains unchanged
- Handles cases where Redshift table doesn't exist (initial loads)
- Provides schema compatibility checking
- Uses Redshift JDBC connection with proper authentication

**Parameters**:
- `currentData`: Dataset being uploaded to Redshift
- `redshiftTableName`: Name of the target Redshift table
- `processingDateRange`: Date range being processed
- `dateColumn`: Date column name for validation
- `redshiftConfig`: Redshift connection configuration

## Validation Flow

### Before (S3-based validation):
1. **Tier1/Tier2**: Full validation before S3 write
2. **Tier3**: No validation, direct Redshift upload

### After (Redshift-based validation):
1. **Tier1/Tier2**: Basic gap validation before S3 write
2. **Tier3**: **Full validation before Redshift write** ✅

## Benefits of This Architecture

1. **Correct Validation Point**: History validation occurs exactly where it matters - before database writes
2. **Database-Aware**: Validation can check actual Redshift table state, not just S3 files
3. **Separation of Concerns**: S3 layer focuses on file quality, Redshift layer focuses on database integrity
4. **Performance**: Reduces validation overhead in tier1/tier2 jobs
5. **Accuracy**: Validates against the actual target system (Redshift) rather than intermediate files

## Implementation Details

### Redshift Connection
The validation uses the same Redshift configuration as the upload process:
- JDBC endpoint connection
- Parameter store authentication
- S3 temporary directory for data staging
- Proper error handling for non-existent tables

### Error Handling
- **Table doesn't exist**: Logs info message, skips history validation (initial load)
- **Connection issues**: Throws IllegalStateException with detailed error
- **Schema mismatches**: Logs warning but continues (supports schema evolution)

This architectural change ensures that the critical history validation ("history remains static") is applied at the correct layer - immediately before the data reaches its final destination in Redshift.
