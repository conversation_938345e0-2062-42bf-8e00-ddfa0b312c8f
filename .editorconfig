root = true

[*]
ij_continuation_indent_size = 8
ij_formatter_off_tag = @formatter:off
ij_formatter_on_tag = @formatter:on
ij_formatter_tags_enabled = false
ij_smart_tabs = false
ij_wrap_on_typing = false

[*.scala]
ij_continuation_indent_size = 2
ij_scala_align_composite_pattern = true
ij_scala_align_extends_with = 0
ij_scala_align_group_field_declarations = false
ij_scala_align_if_else = false
ij_scala_align_in_columns_case_branch = false
ij_scala_align_multiline_binary_operation = false
ij_scala_align_multiline_chained_methods = false
ij_scala_align_multiline_for = true
ij_scala_align_multiline_parameters = false
ij_scala_align_multiline_parameters_in_calls = false
ij_scala_align_multiline_parenthesized_expression = false
ij_scala_align_tuple_elements = false
ij_scala_align_types_in_multiline_declarations = false
ij_scala_alternate_continuation_indent_for_params = 4
ij_scala_binary_operation_wrap = off
ij_scala_blank_lines_after_anonymous_class_header = 0
ij_scala_blank_lines_after_class_header = 0
ij_scala_blank_lines_after_imports = 1
ij_scala_blank_lines_after_package = 1
ij_scala_blank_lines_around_class = 1
ij_scala_blank_lines_around_field = 0
ij_scala_blank_lines_around_field_in_inner_scopes = 0
ij_scala_blank_lines_around_field_in_interface = 0
ij_scala_blank_lines_around_method = 1
ij_scala_blank_lines_around_method_in_inner_scopes = 1
ij_scala_blank_lines_around_method_in_interface = 1
ij_scala_blank_lines_before_imports = 1
ij_scala_blank_lines_before_method_body = 0
ij_scala_blank_lines_before_package = 0
ij_scala_block_brace_style = end_of_line
ij_scala_block_comment_at_first_column = true
ij_scala_call_parameters_new_line_after_lparen = 0
ij_scala_call_parameters_right_paren_on_new_line = false
ij_scala_call_parameters_wrap = off
ij_scala_case_clause_brace_force = never
ij_scala_catch_on_new_line = false
ij_scala_class_annotation_wrap = split_into_lines
ij_scala_class_brace_style = end_of_line
ij_scala_closure_brace_force = never
ij_scala_do_not_align_block_expr_params = true
ij_scala_do_not_indent_case_clause_body = false
ij_scala_do_not_indent_tuples_close_brace = true
ij_scala_do_while_brace_force = never
ij_scala_else_on_new_line = false
ij_scala_enable_scaladoc_formatting = true
ij_scala_enforce_functional_syntax_for_unit = true
ij_scala_extends_keyword_wrap = off
ij_scala_extends_list_wrap = off
ij_scala_field_annotation_wrap = split_into_lines
ij_scala_finally_brace_force = never
ij_scala_finally_on_new_line = false
ij_scala_for_brace_force = never
ij_scala_for_statement_wrap = off
ij_scala_formatter = 0
ij_scala_if_brace_force = never
ij_scala_implicit_value_class_suffix = Ops
ij_scala_indent_braced_function_args = true
ij_scala_indent_case_from_switch = true
ij_scala_indent_first_parameter = true
ij_scala_indent_first_parameter_clause = false
ij_scala_indent_type_arguments = true
ij_scala_indent_type_parameters = true
ij_scala_insert_whitespaces_in_simple_one_line_method = true
ij_scala_keep_blank_lines_before_right_brace = 2
ij_scala_keep_blank_lines_in_code = 2
ij_scala_keep_blank_lines_in_declarations = 2
ij_scala_keep_comments_on_same_line = true
ij_scala_keep_first_column_comment = false
ij_scala_keep_indents_on_empty_lines = false
ij_scala_keep_line_breaks = true
ij_scala_keep_one_line_lambdas_in_arg_list = false
ij_scala_keep_simple_blocks_in_one_line = false
ij_scala_keep_simple_methods_in_one_line = false
ij_scala_keep_xml_formatting = false
ij_scala_line_comment_at_first_column = true
ij_scala_method_annotation_wrap = split_into_lines
ij_scala_method_brace_force = never
ij_scala_method_brace_style = end_of_line
ij_scala_method_call_chain_wrap = off
ij_scala_method_parameters_new_line_after_left_paren = false
ij_scala_method_parameters_right_paren_on_new_line = false
ij_scala_method_parameters_wrap = off
ij_scala_modifier_list_wrap = false
ij_scala_multiline_string_align_dangling_closing_quotes = false
ij_scala_multiline_string_closing_quotes_on_new_line = false
ij_scala_multiline_string_insert_margin_on_enter = true
ij_scala_multiline_string_margin_char = |
ij_scala_multiline_string_margin_indent = 2
ij_scala_multiline_string_opening_quotes_on_new_line = true
ij_scala_multiline_string_process_margin_on_copy_paste = true
ij_scala_newline_after_annotations = false
ij_scala_not_continuation_indent_for_params = false
ij_scala_parameter_annotation_wrap = off
ij_scala_parentheses_expression_new_line_after_left_paren = false
ij_scala_parentheses_expression_right_paren_on_new_line = false
ij_scala_place_closure_parameters_on_new_line = false
ij_scala_place_self_type_on_new_line = true
ij_scala_prefer_parameters_wrap = false
ij_scala_preserve_space_after_method_declaration_name = false
ij_scala_reformat_on_compile = false
ij_scala_replace_case_arrow_with_unicode_char = false
ij_scala_replace_for_generator_arrow_with_unicode_char = false
ij_scala_replace_lambda_with_greek_letter = false
ij_scala_replace_map_arrow_with_unicode_char = false
ij_scala_scalafmt_fallback_to_default_settings = false
ij_scala_scalafmt_reformat_on_files_save = false
ij_scala_scalafmt_show_invalid_code_warnings = true
ij_scala_scalafmt_use_intellij_formatter_for_range_format = true
ij_scala_sd_align_exception_comments = true
ij_scala_sd_align_list_item_content = true
ij_scala_sd_align_other_tags_comments = true
ij_scala_sd_align_parameters_comments = true
ij_scala_sd_align_return_comments = true
ij_scala_sd_blank_line_after_parameters_comments = false
ij_scala_sd_blank_line_after_return_comments = false
ij_scala_sd_blank_line_before_parameters = false
ij_scala_sd_blank_line_before_tags = true
ij_scala_sd_blank_line_between_parameters = false
ij_scala_sd_keep_blank_lines_between_tags = false
ij_scala_sd_preserve_spaces_in_tags = false
ij_scala_space_after_comma = true
ij_scala_space_after_for_semicolon = true
ij_scala_space_after_modifiers_constructor = false
ij_scala_space_after_type_colon = true
ij_scala_space_before_brace_method_call = true
ij_scala_space_before_class_left_brace = true
ij_scala_space_before_for_parentheses = true
ij_scala_space_before_if_parentheses = true
ij_scala_space_before_infix_like_method_parentheses = false
ij_scala_space_before_infix_method_call_parentheses = false
ij_scala_space_before_infix_operator_like_method_call_parentheses = true
ij_scala_space_before_method_call_parentheses = false
ij_scala_space_before_method_left_brace = true
ij_scala_space_before_method_parentheses = false
ij_scala_space_before_type_colon = false
ij_scala_space_before_type_parameter_in_def_list = false
ij_scala_space_before_type_parameter_leading_context_bound_colon = false
ij_scala_space_before_type_parameter_leading_context_bound_colon_hk = true
ij_scala_space_before_type_parameter_list = false
ij_scala_space_before_type_parameter_rest_context_bound_colons = true
ij_scala_space_before_while_parentheses = true
ij_scala_space_inside_closure_braces = true
ij_scala_space_inside_self_type_braces = true
ij_scala_space_within_empty_method_call_parentheses = false
ij_scala_spaces_around_at_in_patterns = false
ij_scala_spaces_in_imports = false
ij_scala_spaces_in_one_line_blocks = false
ij_scala_spaces_within_brackets = false
ij_scala_spaces_within_for_parentheses = false
ij_scala_spaces_within_if_parentheses = false
ij_scala_spaces_within_method_call_parentheses = false
ij_scala_spaces_within_method_parentheses = false
ij_scala_spaces_within_parentheses = false
ij_scala_spaces_within_while_parentheses = false
ij_scala_special_else_if_treatment = true
ij_scala_trailing_comma_arg_list_enabled = true
ij_scala_trailing_comma_import_selector_enabled = false
ij_scala_trailing_comma_mode = trailing_comma_keep
ij_scala_trailing_comma_params_enabled = true
ij_scala_trailing_comma_pattern_arg_list_enabled = false
ij_scala_trailing_comma_tuple_enabled = false
ij_scala_trailing_comma_tuple_type_enabled = false
ij_scala_trailing_comma_type_params_enabled = false
ij_scala_try_brace_force = never
ij_scala_type_annotation_exclude_constant = true
ij_scala_type_annotation_exclude_in_dialect_sources = true
ij_scala_type_annotation_exclude_in_test_sources = false
ij_scala_type_annotation_exclude_member_of_anonymous_class = false
ij_scala_type_annotation_exclude_member_of_private_class = false
ij_scala_type_annotation_exclude_when_type_is_stable = true
ij_scala_type_annotation_function_parameter = false
ij_scala_type_annotation_implicit_modifier = true
ij_scala_type_annotation_local_definition = false
ij_scala_type_annotation_private_member = false
ij_scala_type_annotation_protected_member = true
ij_scala_type_annotation_public_member = true
ij_scala_type_annotation_structural_type = true
ij_scala_type_annotation_underscore_parameter = false
ij_scala_type_annotation_unit_type = true
ij_scala_use_alternate_continuation_indent_for_params = false
ij_scala_use_scaladoc2_formatting = true
ij_scala_variable_annotation_wrap = off
ij_scala_while_brace_force = never
ij_scala_while_on_new_line = false
ij_scala_wrap_before_with_keyword = false
ij_scala_wrap_first_method_in_call_chain = false
ij_scala_wrap_long_lines = false
