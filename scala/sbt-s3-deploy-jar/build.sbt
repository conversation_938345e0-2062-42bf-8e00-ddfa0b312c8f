enablePlugins(GitVersioning)

// https://github.com/sbt/sbt/issues/5849
Global / lintUnusedKeysOnLoad := false

ThisBuild / organization := "com.comlinkdata"
ThisBuild / name := "sbt-s3-deploy-jar"
ThisBuild / versionScheme := Some("semver-spec")

// CHANGE VERSION HERE:
lazy val productVersion = s"2.1.1"
ThisBuild / version := productVersion // needs to be defined at root, so isSnapshot setting is properly set

lazy val scalaLibVersion = "2.12.20"
ThisBuild / scalaVersion := scalaLibVersion
ThisBuild / autoAPIMappings := true

ThisBuild / crossSbtVersions := List("1.9.4", "1.10.7")
ThisBuild / crossScalaVersions := Seq(scalaLibVersion)
ThisBuild / scalacOptions += "-target:jvm-1.8"
ThisBuild / sbtPlugin := true

ThisBuild / description := "Library deploying JAR files to S3 and publishing documentation to Atlassian Confluence."

//ThisBuild / scmInfo := Some(
//    ScmInfo(
//        url("https://bitbucket.org/comniscientss/emr-jobs/src/master/"),
//        "scm:*****************:comniscient/emr-jobs.git"))

resolvers ++= Seq(CommonResolvers.cldReleases, Resolver.mavenCentral)

publishTo := {
    if (isSnapshot.value) Some(CommonResolvers.cldSnapshots)
    else Some(CommonResolvers.cldReleases)
}

addSbtPlugin("com.eed3si9n" % "sbt-assembly" % "1.2.0")
addSbtPlugin("com.typesafe.sbt" % "sbt-git" % "1.0.2") // 2.0.0 is using org.eclipse.jgit > last with java 1.8
addSbtPlugin("com.github.sbt" % "sbt-unidoc" % "0.5.0")

// Latest for Java 1.8: 5.13.3.202401111512-r
// Latest for Java 11: 6.10.0.202406032230-r
// Latest for Javs 17: 7.1.0.202411261347-r

libraryDependencies ++= Seq(
  "org.eclipse.jgit" % "org.eclipse.jgit" % "6.10.0.202406032230-r",  // last with java 11
  "com.michaelpollmeier" % "versionsort" % "1.0.13"
)
libraryDependencies += "org.sahli.asciidoc.confluence.publisher" % "asciidoc-confluence-publisher-converter" % "0.29.0"
libraryDependencies += "org.sahli.asciidoc.confluence.publisher" % "asciidoc-confluence-publisher-client" % "0.29.0"