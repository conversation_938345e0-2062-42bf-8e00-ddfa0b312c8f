#!/usr/bin/env bash
  
# Exit when any command fails
set -e

# Trap commands to log last bash command and error it out if exit code is different than 0
trap 'last_command=$this_command; this_command=$BASH_COMMAND' DEBUG
trap 'echo "\"${last_command}\" command failed with exit code $?."' ERR

# Today's date minus X days as "YYYY-mm-dd"
TODAY_DATE=$(echo `date +%F`)

while (( "$#" )); do
  case "$1" in
    --s3-hbase-snapshots-path) # S3 location with HBase snapshots
      S3_HBASE_SNAPSHOTS_LOCATION=$2
      shift 2
      ;;
    --hbase-snapshot-base-name) # Snapshot base name before a date suffix gets added to it
      HBASE_SNAPSHOT_BASE_NAME=$2
      shift 2
      ;;
    --hbase-table-name) # # HBase table name to export a snapshot from
      HBASE_TABLE_NAME=$2
      shift 2
      ;;
    --mappers-count) # Number of parellel workers to export snapshot back to HBase's HDFS root directory
      MAPPERS_COUNT=$2
      shift 2
      ;;
    --date) # custom date
      TODAY_DATE=$2
      shift 2
      ;;
    --) # end argument parsing
      shift
      break
      ;;
    -*|--*=) # unsupported flags
      echo "Error: Unsupported flag $1" >&2
      exit 1
      ;;
  esac
done

# Today's date using underscores
TODAY_DATE_UNDERSCORED=$(echo $TODAY_DATE | tr - _)

# HBase snapshot name with today's date using underscores
HBASE_SNAPSHOT_NAME="${HBASE_SNAPSHOT_BASE_NAME}_${TODAY_DATE_UNDERSCORED}"

# HBase snapshot remote directory
HBASE_SNAPSHOT_S3_OUTPUT_LOCATION="${S3_HBASE_SNAPSHOTS_LOCATION}/${TODAY_DATE}"

echo "Name of HBase snapshot to create: $HBASE_SNAPSHOT_NAME"
echo "S3 output location to export HBase snapshot to: $HBASE_SNAPSHOT_S3_OUTPUT_LOCATION"


sudo -u hbase /usr/bin/hbase snapshot create -n $HBASE_SNAPSHOT_NAME -t $HBASE_TABLE_NAME
/usr/bin/aws s3 rm $HBASE_SNAPSHOT_S3_OUTPUT_LOCATION --recursive
sudo -u hbase /usr/bin/hbase snapshot export -snapshot $HBASE_SNAPSHOT_NAME -copy-to $HBASE_SNAPSHOT_S3_OUTPUT_LOCATION

