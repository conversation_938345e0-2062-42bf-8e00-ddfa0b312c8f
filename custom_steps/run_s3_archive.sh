#!/usr/bin/env bash

# Exit when any command fails
set -ev

# Trap commands to log last bash command and error it out if exit code is different than 0
trap 'last_command=$this_command; this_command=$BASH_COMMAND' DEBUG
trap 'echo "\"${last_command}\" command failed with exit code $?."' ERR

while (( "$#" )); do
  case "$1" in
    --s3-data-to-archive-location) # S3 location with the data to archive
      S3_DATA_TO_ARCHIVE_LOCATION=$2
      shift 2
      ;;
    --s3-archive-location) # S3 location to archive data to
      S3_ARCHIVE_LOCATION=$2
      shift 2
      ;;
    --) # end argument parsing
      shift
      break
      ;;
    -*|--*=) # unsupported flags
      echo "Error: Unsupported flag $1" >&2
      exit 1
      ;;
  esac
done

echo "S3 location with data to archive: $S3_DATA_TO_ARCHIVE_LOCATION"
echo "S3 location to archive data to: $S3_ARCHIVE_LOCATION"

# Today's date minus X days as "YYYY-mm-dd"  
TODAY_DATE=$(echo `date +%F`)

# S3 archive output location
S3_ARCHIVE_OUTPUT_LOCATION="${S3_ARCHIVE_LOCATION}/${TODAY_DATE}"

echo "S3 archive output location: $S3_ARCHIVE_OUTPUT_LOCATION"

/usr/bin/aws s3 rm $S3_ARCHIVE_OUTPUT_LOCATION --recursive
/usr/bin/aws s3 cp $S3_DATA_TO_ARCHIVE_LOCATION $S3_ARCHIVE_OUTPUT_LOCATION --recursive
/usr/bin/aws s3 rm $S3_DATA_TO_ARCHIVE_LOCATION --recursive

