#!/usr/bin/env python3

import argparse
import logging
import time
from datetime import datetime, timedelta
from typing import <PERSON>ple

import boto3
from botocore.exceptions import ClientError

s3 = boto3.client('s3')


def split_s3(path: str) -> Tuple[str, str]:
    """Split an s3 path into bucket and key

    Parameters
    ----------
    path : str
         path to be split

    Returns
    -------
    <PERSON>ple[str, str]
        tuple of bucket & key
    """
    s3_path = path if path.endswith('/') else (path + '/')

    path_parts = s3_path.replace("s3://", "").split("/")
    bucket = path_parts.pop(0)
    prefix = "/".join(path_parts)
    return bucket, prefix


def folder_exists_and_not_empty(bucket: str, path: str) -> bool:
    """Test a bucket & key exists and is not empty

    Parameters
    ----------
    bucket : str
        bucket for path
    path : str
        key for path

    Returns
    -------
    bool
        True if path exists and is not empty
    """

    if not path.endswith('/'):
        path = path + '/'
    resp = s3.list_objects_v2(Bucket=bucket, Prefix=path, Delimiter='/', MaxKeys=1)
    return 'Contents' in resp or 'CommonPrefixes' in resp


def get_date_last_modified(path: str) -> datetime:
    # _SUCCESS is at the top so MaxKeys = 1!
    bucket, prefix = split_s3(path)
    response = s3.list_objects_v2(Bucket=bucket, Prefix=prefix, Delimiter='/', MaxKeys=1)
    return response["Contents"][0]["LastModified"] - timedelta(hours=2)


def copy(src: str, tgt_bucket: str, tgt: str):
    delay = 1       # initial delay
    delay_incr = 1  # additional delay in each loop
    max_delay = 30  # max delay of one loop. Total delay is (max_delay**2)/2

    while delay < max_delay:
        try:
            s3.copy_object(
                CopySource=src,     # /Bucket-name/path/filename
                Bucket=tgt_bucket,  # Destination bucket
                Key=tgt             # Destination path/filename
            )
            break
        except ClientError:
            time.sleep(delay)
            delay += delay_incr
    else:
        raise


def snapshot(path: str, snapshot_path: str, last_modified: datetime, dry_run=True):

    bucket, prefix = split_s3(path)
    snapshot_bucket, snapshot_prefix = split_s3(snapshot_path)

    # check to see if snapshot exists:
    snapshot_prefix_with_date = f"{snapshot_prefix}snapshot_date={datetime.strftime(last_modified, '%Y-%m-%d')}/"
    if folder_exists_and_not_empty(snapshot_bucket, snapshot_prefix_with_date):
        logging.warning(f"snapshot already exists: {snapshot_prefix_with_date}")
    else:
        logging.warning(f"querying everything after: {last_modified}")
        # get objects
        # jmes_query = f"Contents[?to_string(LastModified)>='\"{last_modified}\"' && ends_with(Key, '_SUCCESS')].Key"
        jmes_query = f"Contents[?to_string(LastModified)>='\"{last_modified}\"'].Key"
        s3_paginator = s3.get_paginator('list_objects_v2')
        s3_iterator = s3_paginator.paginate(Bucket=bucket, Prefix=prefix)
        filtered_iterator = s3_iterator.search(jmes_query)
        objects = [k for k in filtered_iterator]  # if k != prefix+"_SUCCESS"]
        for src in objects:
            tgt = snapshot_prefix_with_date + src[len(prefix):]
            logging.debug("copying:", src, " to ", tgt)
            if not dry_run:
                copy(f'{bucket}/{src}', snapshot_bucket, tgt)


def get_common_args_parser() -> argparse.ArgumentParser:
    """Get an argparser w/ common options already added
        (needs from work)
    Returns
    -------
    argparse.ArgumentParser
        argparser
    """
    parser = argparse.ArgumentParser()
    parser._action_groups.pop()
    required = parser.add_argument_group('required arguments')
    optional = parser.add_argument_group('optional arguments')

    required.add_argument(
        '-p', '--path',
        help="Source path to be snapshotted",
        required=True
    )
    required.add_argument(
        '-sp', '--snapshot-path',
        help="Target path to store snapshot data",
        required=True
    )

    optional.add_argument(
        '-v', '--verbose',
        help="Be verbose",
        action="store_const", dest="loglevel", const=logging.INFO,
        default=logging.WARNING,
    )
    optional.add_argument(
        '-dr', '--dry-run',
        help="Don't do anything; just log",
        action="store_true"
    )

    return parser


if __name__ == '__main__':

    args = get_common_args_parser().parse_args()
    logging.root.setLevel(level=args.loglevel)
    date_last_modified = get_date_last_modified(args.path)
    snapshot(
        path=args.path,
        snapshot_path=args.snapshot_path,
        last_modified=date_last_modified,
        dry_run=args.dry_run
    )