#!/usr/bin/env bash

# Argument Variables
START_DATE=$1
END_DATE=$2
SPARK_JAR=$3
CLASS=$4
HDFS_LOCAL=$5
S3_LOCATION=$6

# Commands
UPLOAD="s3-dist-cp --src "$HDFS_LOCAL" --dest "$S3_LOCATION
CLEAR_HDFS="hdfs dfs -rm -r "$HDFS_LOCAL

# DATE value for iterations
DATE=$START_DATE

echo $PWD

# Loop through historical data
until [[ $DATE > $END_DATE ]]; do

   echo $DATE

   COMMAND="spark-submit --class "$CLASS" "$SPARK_JAR" "$DATE

   echo $COMMAND

   eval $COMMAND
   eval $UPLOAD
   eval $CLEAR_HDFS
   DATE=$(date -I -d "$DATE + 1 day")

done
