#!/usr/bin/env bash

ARGS=()

while [[ $# -gt 0 ]]; do
  case $1 in
  --src)
    SRC="$2"
    ARGS+=("$1" "$2")
    shift
    shift
    ;;
  --deleteOnSuccess)
    DELETE_ON_SUCCESS=TRUE
    ARGS+=("$i")
    shift
    ;;
  --ignore-empty-hdfs)
    IGNORE_EMPTY_HDFS=TRUE
    shift
    ;;
  --delete-s3-override)
    OVERRIDE=TRUE
    shift
    ;;
  *)
    ARGS+=("$1")
    shift
    ;;
  esac
done

if [[ ($SRC == s3:/* || $SRC == s3a:/* || $SRC == s3n:/*) && $DELETE_ON_SUCCESS == "TRUE" && -z "$OVERRIDE" ]]; then
  echo "--deleteOnSuccess option cannot be used with S3 sources"
  exit 1
elif [[ $SRC != s3:/* && $SRC != s3a:/* && $SRC != s3n:/* && -n "$OVERRIDE" ]]; then
  echo "--delete-s3-override option cannot be used with non-S3 sources"
  exit 1
elif [[ $SRC == hdfs:/* && -n $IGNORE_EMPTY_HDFS ]] && ! hdfs dfs -test -d "$SRC"; then
  echo "Ignoring empty HDFS source $SRC"
  exit 0
fi

/usr/bin/s3-dist-cp "${ARGS[@]}"
