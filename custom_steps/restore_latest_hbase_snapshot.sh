#!/usr/bin/env bash

# Exit when any command fails
set -v

# Trap commands to log last bash command and error it out if exit code is different than 0
# trap 'last_command=$this_command; this_command=$BASH_COMMAND' DEBUG
# trap 'echo "\"${last_command}\" command failed with exit code $?."' ERR

# EMR cluster's master public DNS
MASTER_HOSTNAME=`curl -s http://***************/latest/meta-data/hostname`

# HBase root port should be 8020
HBASE_ROOT_PORT="8020"

# HBase's HDFS directory to import snapshots into
HBASE_HDFS_DIR="hdfs://$MASTER_HOSTNAME:$HBASE_ROOT_PORT/user/hbase"
# s3a://d000-emr-jobs/emr-jobs/staging-emr-530/custom_steps/restore_latest_hbase_snapshot.sh
#   --s3-hbase-snapshots-path s3://d000-comlinkdata-com/prod/private/device-master-table/hbase-snapshots/v=1.1.0
#   --hbase-snapshot-base-name ifa_master_table_snapshot
#   --mappers-count 25
#   --hbase-table-name ifa_master_table
# S3_HBASE_SNAPSHOTS_LOCATION='s3://d000-comlinkdata-com/prod/private/device-master-table/hbase-snapshots/v=1.1.0'
# HBASE_SNAPSHOT_BASE_NAME='ifa_master_table_snapshot'
# HBASE_TABLE_NAME=ifa_master_table
# MAPPERS_COUNT=25
while (( "$#" )); do
  case "$1" in
    --s3-hbase-snapshots-path) # S3 location with HBase snapshots
      S3_HBASE_SNAPSHOTS_LOCATION=$2
      shift 2
      ;;
    --hbase-snapshot-base-name) # Snapshot base name before a date suffix gets added to it
      HBASE_SNAPSHOT_BASE_NAME=$2
      shift 2
      ;;
    --hbase-table-name) # HBase table to restore
      HBASE_TABLE_NAME=$2
      shift 2
      ;;
    --mappers-count) # Number of parellel workers to export snapshot back to HBase's HDFS root directory
      MAPPERS_COUNT=$2
      shift 2
      ;;
    --) # end argument parsing
      shift
      break
      ;;
    -*|--*=) # unsupported flags
      echo "Error: Unsupported flag $1" >&2
      exit 1
      ;;
  esac
done

# Add missing trailing slash
length=${#S3_HBASE_SNAPSHOTS_LOCATION}
last_char=${STR:length-1:1}
[[ $last_char != "/" ]] && S3_HBASE_SNAPSHOTS_LOCATION="$S3_HBASE_SNAPSHOTS_LOCATION/"; :

LATEST_DATE=$(aws s3 ls $S3_HBASE_SNAPSHOTS_LOCATION | grep -Eo "[0-9]{4}-[0-9]{2}-[0-9]{2}/" | tail -n 1)
length=${#LATEST_DATE}
last_char=${LATEST_DATE:length-1:1}
[[ $last_char == "/" ]] && LATEST_DATE=${LATEST_DATE:0:length-1}; :

LATEST_DATE_UNDERSCORED=$(echo $LATEST_DATE | tr - _)
HBASE_SNAPSHOT_NAME="${HBASE_SNAPSHOT_BASE_NAME}_${LATEST_DATE_UNDERSCORED}"
HBASE_SNAPSHOT_INPUT_LOCATION="${S3_HBASE_SNAPSHOTS_LOCATION}${LATEST_DATE}"

echo "S3 path with HBase snapshots: $S3_HBASE_SNAPSHOTS_LOCATION"
echo "HBase snapshot base name: $HBASE_SNAPSHOT_BASE_NAME"
echo "Latest snapshot date: $LATEST_DATE"
echo "HBase snapshot to import: $HBASE_SNAPSHOT_NAME"
echo "HBase snapshot input directory: $HBASE_SNAPSHOT_INPUT_LOCATION"
echo "HBase table name: $HBASE_TABLE_NAME"
echo "HBase HDFS directory: $HBASE_HDFS_DIR"

# Export latest HBase snapshot back to HBase root directory


# Restore HBase table from HBase snapshot
n=5
while [ $n -gt 0 ]; do
  echo "Trying with remaining retries $n"
  sudo -u hbase /usr/bin/hbase snapshot export -D hbase.rootdir=$HBASE_SNAPSHOT_INPUT_LOCATION -snapshot $HBASE_SNAPSHOT_NAME -copy-to $HBASE_HDFS_DIR -mappers $MAPPERS_COUNT && (echo "restore_snapshot '$HBASE_SNAPSHOT_NAME'; enable '$HBASE_TABLE_NAME'" | sudo /usr/bin/hbase shell)
  retVal=$?
  if [ $retVal -eq 0 ]; then
    exit 0
  else
    echo "Failed, waiting before trying again."
    n=$(($n -1))
    sleep 60
    echo "Done Waiting, retrying now."
  fi
done
echo "Retries Exhausted exiting with failure."
exit 1
