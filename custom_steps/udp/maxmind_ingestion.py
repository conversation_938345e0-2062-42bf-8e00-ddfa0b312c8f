import boto3
import gzip
import logging
import re
import tempfile
from zipfile import ZipFile


class maxmindIngester:
    def __init__(self,
                 source_bucket,
                 conn_type_source_prefix,
                 ip2isp_source_prefix,
                 ip2location_source_prefix,
                 ipv_filename,
                 target_bucket,
                 conn_type_target_prefix,
                 ip2isp_target_prefix,
                 ip2location_target_prefix):
        self.source_bucket = source_bucket
        self.conn_type_source_prefix = conn_type_source_prefix
        self.ip2isp_source_prefix = ip2isp_source_prefix
        self.ip2location_source_prefix = ip2location_source_prefix
        self.ipv_filename = ipv_filename
        self.target_bucket = target_bucket
        self.conn_type_target_prefix = conn_type_target_prefix
        self.ip2isp_target_prefix = ip2isp_target_prefix
        self.ip2location_target_prefix = ip2location_target_prefix
        self.s3_date_partition_format = re.compile(r'/date=(\d\d\d\d-\d\d-\d\d)/')
        self.s3 = boto3.client('s3')
        self.paginator = self.s3.get_paginator("list_objects_v2")
        self.logger = logging.getLogger()
        logging.basicConfig(level=logging.INFO)

    def run(self):
        self.process_conn_type()
        self.process_ip2isp()
        self.process_ip2location()

    def process_ip2location(self):
        self.logger.info("Starting ip2location process\n")
        for i in [4, 6]:
            self.process_ip2location_by_version(i)

    def process_ip2location_by_version(self, ip_version):
        self.logger.info(f"Starting pre-processing for ip2location file for ip_version={ip_version}\n")
        latest_upload_date = self.get_latest_target_date_partition(self.target_bucket,
                                                                   self.ip2location_target_prefix.format(v=ip_version))
        self.logger.info(f"Latest date in target: {latest_upload_date}\n")
        source_upload_dates = self.get_source_date_partitions(self.source_bucket,
                                                              self.ip2location_source_prefix.format(v=ip_version))
        dates_to_process = [date for date in source_upload_dates if date > latest_upload_date]
        if len(dates_to_process) == 0:
            self.logger.info(f"Target directory is up to date for IP2Location, ipversion={ip_version}\n")
            return
        self.logger.info(f"Dates from source bucket to process: {[date for date in dates_to_process]}\n")
        for date in dates_to_process:
            self.process_ip2location_for_date(date, ip_version)

    def process_ip2location_for_date(self, date, ip_version):
        self.logger.info(f"Starting pre-processing for ip2location {date} file\n")
        tmp = tempfile.gettempdir()
        source_key = self.get_s3_key_for_date(self.source_bucket, self.ip2location_source_prefix.format(v=ip_version),
                                              date)
        source_name = self.get_filename_from_key(source_key)
        interim_path = f'{tmp}/{source_name}'
        self.logger.info(f"Downloading {source_key} from {self.source_bucket} to {interim_path}\n")
        self.download_file_from_s3(self.source_bucket, source_key, interim_path)
        self.logger.info(f"Uploading {source_name} from {interim_path} to {self.target_bucket}\n")
        self.s3.upload_file(interim_path, self.target_bucket,
                            f'{self.ip2location_target_prefix.format(v=ip_version)}/date={date}/{source_name}')

    def process_ip2isp(self):
        self.logger.info("Starting ip2isp process\n")
        for i in [4, 6]:
            self.process_ip2isp_by_version(i)

    def process_ip2isp_by_version(self, ip_version):
        self.logger.info(f"Starting pre-processing for ip2isp file for ip_version={ip_version}\n")
        latest_upload_date = self.get_latest_target_date_partition(self.target_bucket,
                                                                   self.ip2isp_target_prefix.format(v=ip_version))
        self.logger.info(f"Latest date in target: {latest_upload_date}\n")
        source_upload_dates = self.get_source_date_partitions(self.source_bucket,
                                                              self.ip2isp_source_prefix.format(v=ip_version))
        dates_to_process = [date for date in source_upload_dates if date > latest_upload_date]
        if len(dates_to_process) == 0:
            self.logger.info(f"Target directory is up to date for IP2ISP, ipversion={ip_version}\n")
            return
        self.logger.info(f"Dates from source bucket to process: {[date for date in dates_to_process]}\n")
        for date in dates_to_process:
            self.process_ip2isp_for_date(date, ip_version)

    def process_ip2isp_for_date(self, date, ip_version):
        self.logger.info(f"Starting pre-processing for ip2isp {date} file\n")
        tmp = tempfile.gettempdir()
        source_key = self.get_s3_key_for_date(self.source_bucket, self.ip2isp_source_prefix.format(v=ip_version), date)
        source_name = self.get_filename_from_key(source_key)
        interim_path = f'{tmp}/{source_name}'
        self.logger.info(f"Downloading {source_key} from {self.source_bucket} to {interim_path}\n")
        self.download_file_from_s3(self.source_bucket, source_key, interim_path)
        self.logger.info(f"Uploading {source_name} from {interim_path} to {self.target_bucket}\n")
        self.s3.upload_file(interim_path, self.target_bucket,
                            f'{self.ip2isp_target_prefix.format(v=ip_version)}/date={date}/{source_name}')

    def process_conn_type(self):
        self.logger.info("Starting connection type process\n")
        for i in [4, 6]:
            self.process_conn_type_by_version(i)

    def process_conn_type_by_version(self, ip_version):
        self.logger.info(f"Starting pre-processing for the connection type file for ip_version={ip_version}\n")
        latest_upload_date = self.get_latest_target_date_partition(self.target_bucket,
                                                                   self.conn_type_target_prefix.format(v=ip_version))
        path = f'{self.target_bucket}/{self.conn_type_target_prefix.format(v=ip_version)}'
        self.logger.info(f"Latest date in target {path}: {latest_upload_date}\n")
        source_upload_dates = self.get_source_date_partitions(self.source_bucket, self.conn_type_source_prefix)
        dates_to_process = [date for date in source_upload_dates if date > latest_upload_date]
        if len(dates_to_process) == 0:
            self.logger.info(f"Target directory is up to date for connection type, ipversion={ip_version}\n")
            return
        self.logger.info(f"Dates from source bucket to process: {[date for date in dates_to_process]}\n")
        for date in dates_to_process:
            self.process_conn_type_for_date(date, ip_version)

    def process_conn_type_for_date(self, date, ip_version):
        self.logger.info(f"Starting processing for connection file for {date}\n")
        tmp = tempfile.gettempdir()
        zipfile_source_key = self.get_s3_key_for_date(self.source_bucket, self.conn_type_source_prefix, date)
        zipfile_name = self.get_filename_from_key(zipfile_source_key)
        zipfile_interim_path = f'{tmp}/{zipfile_name}'
        self.download_file_from_s3(self.source_bucket, zipfile_source_key, zipfile_interim_path)
        self.logger.info("Unzipping the folder's contents to the same directory\n")
        self.unzip_folder(zipfile_interim_path, tmp)
        zipfile_name_no_ext = zipfile_name.split('.')[0]
        split_zipfile_name = zipfile_name_no_ext.split('_')
        folder_name = f'{split_zipfile_name[0]}-CSV_{split_zipfile_name[-1]}'
        fin = f'{tmp}/{folder_name}/{self.ipv_filename.format(v=ip_version)}'
        self.gzip_file(fin)
        gzip_filepath = f'{fin}.gz'
        self.upload_conn_type(self.target_bucket, gzip_filepath, date, ip_version)

    def get_latest_target_date_partition(self, bucket, prefix):
        try:
            contents = self.get_contents(bucket, prefix)
            latest_file = sorted([object for object in contents], key=lambda k: k["LastModified"])[-1]["Key"]
            latest_upload_date = re.findall(self.s3_date_partition_format, latest_file)
            return latest_upload_date[0]
        except:
            return '1970-01-01'  # force an old date

    def get_source_date_partitions(self, bucket, prefix):
        contents = self.get_contents(bucket, prefix)
        source_dates = [re.findall(self.s3_date_partition_format, object["Key"])[0] for object in contents]
        return source_dates

    def get_s3_key_for_date(self, bucket, prefix, date):
        prefix_plus_date = f'{prefix}/date={date}'
        contents = self.get_contents(bucket, prefix_plus_date)
        return contents[0]["Key"]

    def get_filename_from_key(self, key):
        return key.split('/')[-1]

    def get_contents(self, bucket, prefix):
        kwargs = {'Bucket': bucket, 'Prefix': prefix}
        contents = [page["Contents"] for page in self.paginator.paginate(**kwargs)]
        page_1 = contents[0]
        if len(contents) < 2:
            return page_1
        else:
            contents.remove(page_1)
            return [page_1.extend(page) for page in contents]

    def download_file_from_s3(self, bucket, file, directory):
        self.logger.info(f"Downloading {file} into {directory} from {bucket}\n")
        self.s3.download_file(bucket, file, directory)

    def unzip_folder(self, zip_file, target):
        self.logger.info(f"Unzipping {zip_file} to {target}\n")
        with ZipFile(zip_file, 'r') as zipObj:
            zipObj.extractall(target)

    def gzip_file(self, filename):
        self.logger.info(f"gzipping {filename}\n")
        with open(filename, "rb") as in_file:
            with gzip.open(f"{filename}.gz", "wb") as zipped_file:
                zipped_file.writelines(in_file)

    def upload_conn_type(self, bucket, filepath, upload_date, ip_version):
        conn_type_target_prefix = f'prod/private/maxmind/ip2conntype/ipversion={ip_version}/date={upload_date}'
        gz_filename = filepath.split('/')[-1]
        self.logger.info(f"uploading file {filepath} to s3://{self.target_bucket}/{conn_type_target_prefix}\n")
        self.s3.upload_file(filepath, bucket, f'{conn_type_target_prefix}/{gz_filename}')


if __name__ == "__main__":
    maxmindIngester(source_bucket='externaldata-tomba-comlinkdata-com',
                    conn_type_source_prefix='maxmind/GeoIP2-Connection-Type',
                    ip2isp_source_prefix='maxmind/GeoIP2-ISP-Blocks-IPv{v}',
                    ip2location_source_prefix='maxmind/GeoLite2-City-Records-IPv{v}',
                    ipv_filename='GeoIP2-Connection-Type-Blocks-IPv{v}.csv',
                    target_bucket='d000-comlinkdata-com',
                    conn_type_target_prefix='prod/private/maxmind/ip2conntype/ipversion={v}',
                    ip2isp_target_prefix='prod/private/maxmind/ip2isp/ipversion={v}',
                    ip2location_target_prefix='prod/private/maxmind/geolite2/ipversion={v}'
                    ).run()
